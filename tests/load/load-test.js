// CyberForce Load Testing with K6
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');

// Test configuration
export const options = {
  stages: [
    // Alpha Test: Light load
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 0 },  // Ramp down
    
    // Beta Test: Medium load
    { duration: '2m', target: 50 }, // Ramp up to 50 users
    { duration: '10m', target: 50 }, // Stay at 50 users
    { duration: '2m', target: 0 },   // Ramp down
    
    // Production Test: High load
    { duration: '5m', target: 100 }, // Ramp up to 100 users
    { duration: '15m', target: 100 }, // Stay at 100 users
    { duration: '5m', target: 200 },  // Spike to 200 users
    { duration: '5m', target: 200 },  // Stay at 200 users
    { duration: '5m', target: 0 },    // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.1'],     // Error rate under 10%
    errors: ['rate<0.1'],              // Custom error rate under 10%
  },
};

const BASE_URL = 'https://cyberforce.local';

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
];

export default function () {
  // Test landing page
  testLandingPage();
  sleep(1);
  
  // Test authentication
  testAuthentication();
  sleep(1);
  
  // Test dashboard
  testDashboard();
  sleep(1);
  
  // Test learning paths
  testLearningPaths();
  sleep(1);
  
  // Test API endpoints
  testAPIEndpoints();
  sleep(2);
}

function testLandingPage() {
  const response = http.get(`${BASE_URL}/`);
  
  const success = check(response, {
    'landing page loads': (r) => r.status === 200,
    'landing page has title': (r) => r.body.includes('CyberForce'),
    'response time < 1s': (r) => r.timings.duration < 1000,
  });
  
  errorRate.add(!success);
}

function testAuthentication() {
  const user = testUsers[Math.floor(Math.random() * testUsers.length)];
  
  // Test login page
  let response = http.get(`${BASE_URL}/login`);
  check(response, {
    'login page loads': (r) => r.status === 200,
  });
  
  // Test login API
  response = http.post(`${BASE_URL}/api/auth/login`, {
    email: user.email,
    password: user.password,
  }, {
    headers: { 'Content-Type': 'application/json' },
  });
  
  const success = check(response, {
    'login API responds': (r) => r.status === 200 || r.status === 401,
    'login response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  errorRate.add(!success);
}

function testDashboard() {
  const response = http.get(`${BASE_URL}/dashboard`);
  
  const success = check(response, {
    'dashboard loads': (r) => r.status === 200,
    'dashboard has content': (r) => r.body.includes('dashboard') || r.body.includes('Welcome'),
    'dashboard response time < 1.5s': (r) => r.timings.duration < 1500,
  });
  
  errorRate.add(!success);
}

function testLearningPaths() {
  const response = http.get(`${BASE_URL}/dashboard/learning-paths`);
  
  const success = check(response, {
    'learning paths load': (r) => r.status === 200,
    'learning paths response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  errorRate.add(!success);
}

function testAPIEndpoints() {
  // Test various API endpoints
  const endpoints = [
    '/api/learning-paths',
    '/api/challenges',
    '/api/user/progress',
    '/api/user/stats',
  ];
  
  endpoints.forEach(endpoint => {
    const response = http.get(`${BASE_URL}${endpoint}`);
    
    const success = check(response, {
      [`${endpoint} responds`]: (r) => r.status < 500,
      [`${endpoint} response time < 3s`]: (r) => r.timings.duration < 3000,
    });
    
    errorRate.add(!success);
  });
}

// Setup function - runs once before the test
export function setup() {
  console.log('🚀 Starting CyberForce Load Test');
  console.log(`Target URL: ${BASE_URL}`);
  
  // Verify the application is running
  const response = http.get(`${BASE_URL}/health`);
  if (response.status !== 200) {
    throw new Error(`Application not ready. Health check failed with status: ${response.status}`);
  }
  
  console.log('✅ Application health check passed');
  return { baseUrl: BASE_URL };
}

// Teardown function - runs once after the test
export function teardown(data) {
  console.log('🏁 Load test completed');
  console.log(`Base URL tested: ${data.baseUrl}`);
}
