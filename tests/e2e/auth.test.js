// CyberForce Authentication End-to-End Tests
import { test, expect } from '@playwright/test';

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  fullName: 'Test User'
};

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display landing page correctly', async ({ page }) => {
    // Check if landing page loads
    await expect(page).toHaveTitle(/CyberForce/);
    await expect(page.locator('h1')).toContainText('CyberForce');
    
    // Check navigation elements
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="signup-button"]')).toBeVisible();
  });

  test('should handle user registration flow', async ({ page }) => {
    // Navigate to signup
    await page.click('[data-testid="signup-button"]');
    await expect(page).toHaveURL(/.*signup/);

    // Fill registration form
    await page.fill('[data-testid="fullname-input"]', TEST_USER.fullName);
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.fill('[data-testid="confirm-password-input"]', TEST_USER.password);

    // Submit form
    await page.click('[data-testid="signup-submit"]');

    // Check for success message or email verification prompt
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText(/email verification/i);
  });

  test('should handle user login flow', async ({ page }) => {
    // Navigate to login
    await page.click('[data-testid="login-button"]');
    await expect(page).toHaveURL(/.*login/);

    // Fill login form
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);

    // Submit form
    await page.click('[data-testid="login-submit"]');

    // Check for successful login (redirect to dashboard)
    await expect(page).toHaveURL(/.*dashboard/);
    await expect(page.locator('[data-testid="user-profile"]')).toBeVisible();
  });

  test('should handle forgot password flow', async ({ page }) => {
    // Navigate to login then forgot password
    await page.click('[data-testid="login-button"]');
    await page.click('[data-testid="forgot-password-link"]');
    
    await expect(page).toHaveURL(/.*forgot-password/);

    // Fill email
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.click('[data-testid="reset-submit"]');

    // Check for success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText(/reset link sent/i);
  });

  test('should validate form inputs', async ({ page }) => {
    await page.click('[data-testid="signup-button"]');

    // Test empty form submission
    await page.click('[data-testid="signup-submit"]');
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();

    // Test invalid email
    await page.fill('[data-testid="email-input"]', 'invalid-email');
    await page.click('[data-testid="signup-submit"]');
    await expect(page.locator('[data-testid="email-error"]')).toContainText(/valid email/i);

    // Test weak password
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', '123');
    await page.click('[data-testid="signup-submit"]');
    await expect(page.locator('[data-testid="password-error"]')).toContainText(/password requirements/i);
  });

  test('should handle logout flow', async ({ page }) => {
    // Login first
    await page.click('[data-testid="login-button"]');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-submit"]');

    // Wait for dashboard
    await expect(page).toHaveURL(/.*dashboard/);

    // Logout
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');

    // Check redirect to landing page
    await expect(page).toHaveURL('/');
    await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
  });

  test('should persist authentication state', async ({ page, context }) => {
    // Login
    await page.click('[data-testid="login-button"]');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-submit"]');
    await expect(page).toHaveURL(/.*dashboard/);

    // Create new page in same context
    const newPage = await context.newPage();
    await newPage.goto('/dashboard');

    // Should still be logged in
    await expect(newPage).toHaveURL(/.*dashboard/);
    await expect(newPage.locator('[data-testid="user-profile"]')).toBeVisible();
  });
});
