// CyberForce Dashboard End-to-End Tests
import { test, expect } from '@playwright/test';

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Dashboard Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', TEST_USER.email);
    await page.fill('[data-testid="password-input"]', TEST_USER.password);
    await page.click('[data-testid="login-submit"]');
    await expect(page).toHaveURL(/.*dashboard/);
  });

  test('should display dashboard components correctly', async ({ page }) => {
    // Check main dashboard elements
    await expect(page.locator('[data-testid="welcome-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="stats-cards"]')).toBeVisible();
    await expect(page.locator('[data-testid="quick-actions"]')).toBeVisible();
    await expect(page.locator('[data-testid="learning-path"]')).toBeVisible();

    // Check navigation sidebar
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-learning-paths"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-challenges"]')).toBeVisible();
    await expect(page.locator('[data-testid="nav-progress"]')).toBeVisible();
  });

  test('should navigate to learning paths', async ({ page }) => {
    await page.click('[data-testid="nav-learning-paths"]');
    await expect(page).toHaveURL(/.*learning-paths/);
    
    // Check learning paths page elements
    await expect(page.locator('[data-testid="learning-paths-grid"]')).toBeVisible();
    await expect(page.locator('[data-testid="path-card"]').first()).toBeVisible();
  });

  test('should handle learning path enrollment', async ({ page }) => {
    await page.click('[data-testid="nav-learning-paths"]');
    
    // Find an enrollable path
    const enrollButton = page.locator('[data-testid="enroll-button"]').first();
    await enrollButton.click();

    // Check for enrollment success
    await expect(page.locator('[data-testid="continue-button"]')).toBeVisible();
    
    // Verify enrollment persists after refresh
    await page.reload();
    await expect(page.locator('[data-testid="continue-button"]')).toBeVisible();
  });

  test('should navigate to challenges', async ({ page }) => {
    await page.click('[data-testid="nav-challenges"]');
    await expect(page).toHaveURL(/.*challenges/);
    
    // Check challenges page elements
    await expect(page.locator('[data-testid="challenges-grid"]')).toBeVisible();
    await expect(page.locator('[data-testid="challenge-card"]').first()).toBeVisible();
  });

  test('should display progress tracking', async ({ page }) => {
    await page.click('[data-testid="nav-progress"]');
    await expect(page).toHaveURL(/.*progress/);
    
    // Check progress page elements
    await expect(page.locator('[data-testid="progress-overview"]')).toBeVisible();
    await expect(page.locator('[data-testid="progress-charts"]')).toBeVisible();
  });

  test('should handle assessment modal', async ({ page }) => {
    // Click assessment button
    await page.click('[data-testid="assessment-button"]');
    
    // Check modal opens
    await expect(page.locator('[data-testid="assessment-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="assessment-questions"]')).toBeVisible();
    
    // Close modal
    await page.click('[data-testid="close-modal"]');
    await expect(page.locator('[data-testid="assessment-modal"]')).not.toBeVisible();
  });

  test('should handle dark mode toggle', async ({ page }) => {
    // Toggle dark mode
    await page.click('[data-testid="dark-mode-toggle"]');
    
    // Check dark mode applied
    await expect(page.locator('html')).toHaveClass(/dark/);
    
    // Toggle back to light mode
    await page.click('[data-testid="dark-mode-toggle"]');
    await expect(page.locator('html')).not.toHaveClass(/dark/);
  });

  test('should display user profile information', async ({ page }) => {
    await page.click('[data-testid="user-menu"]');
    
    // Check profile dropdown
    await expect(page.locator('[data-testid="profile-dropdown"]')).toBeVisible();
    await expect(page.locator('[data-testid="user-email"]')).toContainText(TEST_USER.email);
    
    // Navigate to profile
    await page.click('[data-testid="profile-link"]');
    await expect(page).toHaveURL(/.*profile/);
  });

  test('should handle quick action buttons', async ({ page }) => {
    // Test Continue Learning button
    await page.click('[data-testid="continue-learning-button"]');
    await expect(page).toHaveURL(/.*learning-paths/);
    
    await page.goBack();
    
    // Test Daily Challenge button
    await page.click('[data-testid="daily-challenge-button"]');
    await expect(page).toHaveURL(/.*challenges/);
    
    await page.goBack();
    
    // Test Start Hack button
    await page.click('[data-testid="start-hack-button"]');
    await expect(page).toHaveURL(/.*start-hack/);
  });

  test('should display real-time statistics', async ({ page }) => {
    // Check stats cards have actual data
    const levelCard = page.locator('[data-testid="level-card"]');
    const modulesCard = page.locator('[data-testid="modules-card"]');
    const streakCard = page.locator('[data-testid="streak-card"]');
    
    await expect(levelCard).toBeVisible();
    await expect(modulesCard).toBeVisible();
    await expect(streakCard).toBeVisible();
    
    // Check for numeric values
    await expect(levelCard.locator('[data-testid="stat-value"]')).toContainText(/\d+/);
    await expect(modulesCard.locator('[data-testid="stat-value"]')).toContainText(/\d+/);
    await expect(streakCard.locator('[data-testid="stat-value"]')).toContainText(/\d+/);
  });

  test('should handle responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check mobile navigation
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
    await page.click('[data-testid="mobile-menu-button"]');
    await expect(page.locator('[data-testid="mobile-sidebar"]')).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('[data-testid="stats-cards"]')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
  });
});
