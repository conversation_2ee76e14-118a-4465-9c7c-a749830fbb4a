    -- =============================================
    -- ADD PUBLIC PROFILE POLICY
    -- Run this AFTER the main schema is created
    -- =============================================

    -- Create the public profile policy (now that profile_visibility column exists)
    CREATE POLICY "Public profiles are viewable by everyone" ON public.user_profiles
        FOR SELECT USING (profile_visibility = 'public');

    -- Success message
    DO $$ 
    BEGIN
        RAISE NOTICE 'Public profile policy added successfully!';
        RAISE NOTICE 'Users with profile_visibility = "public" can now be viewed by everyone';
    END $$;
