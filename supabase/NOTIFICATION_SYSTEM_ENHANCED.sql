-- =============================================
-- ENHANCED NOTIFICATION SYSTEM
-- =============================================
-- Complete notification system with rich content support
-- Supports text, images, emojis, links, and various notification types
-- =============================================

-- Drop existing table if it exists to recreate with enhanced schema
DROP TABLE IF EXISTS user_notifications CASCADE;

-- Create enhanced user_notifications table
CREATE TABLE user_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Basic notification info
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info', -- 'info', 'success', 'warning', 'error', 'challenge', 'achievement', 'system'
    priority INTEGER DEFAULT 2, -- 1=high, 2=medium, 3=low
    
    -- Rich content support
    emoji VARCHAR(10), -- Store emoji like 🎉, 🚀, ⚠️, etc.
    image_url TEXT, -- URL to notification image
    icon_name VARCHAR(50), -- Icon name for UI (FaBell, FaTrophy, etc.)
    icon_color VARCHAR(20) DEFAULT 'blue', -- Icon color
    
    -- Action support
    action_url TEXT, -- URL to navigate when clicked
    action_label VARCHAR(100), -- Button text like "View Challenge", "Start Learning"
    action_type VARCHAR(50) DEFAULT 'navigate', -- 'navigate', 'modal', 'external'
    
    -- Additional data
    metadata JSONB DEFAULT '{}', -- Store any additional data
    category VARCHAR(50), -- 'learning', 'challenge', 'achievement', 'system', 'social'
    
    -- Status tracking
    is_read BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE, -- Pin important notifications
    
    -- Scheduling
    scheduled_for TIMESTAMP WITH TIME ZONE, -- For scheduled notifications
    expires_at TIMESTAMP WITH TIME ZONE, -- Auto-expire notifications
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    dismissed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX idx_user_notifications_user_id ON user_notifications(user_id);
CREATE INDEX idx_user_notifications_type ON user_notifications(type);
CREATE INDEX idx_user_notifications_category ON user_notifications(category);
CREATE INDEX idx_user_notifications_priority ON user_notifications(priority);
CREATE INDEX idx_user_notifications_read ON user_notifications(is_read);
CREATE INDEX idx_user_notifications_dismissed ON user_notifications(is_dismissed);
CREATE INDEX idx_user_notifications_created_at ON user_notifications(created_at);
CREATE INDEX idx_user_notifications_scheduled ON user_notifications(scheduled_for);

-- Enable RLS
ALTER TABLE user_notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own notifications" ON user_notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON user_notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert notifications for any user" ON user_notifications
    FOR INSERT WITH CHECK (true); -- Allow system to create notifications

-- Create notification templates table for reusable templates
CREATE TABLE notification_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_name VARCHAR(100) UNIQUE NOT NULL,
    title_template VARCHAR(255) NOT NULL,
    message_template TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info',
    emoji VARCHAR(10),
    icon_name VARCHAR(50),
    icon_color VARCHAR(20) DEFAULT 'blue',
    category VARCHAR(50),
    action_label VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert common notification templates
INSERT INTO notification_templates (template_name, title_template, message_template, type, emoji, icon_name, icon_color, category, action_label) VALUES
('welcome', 'Welcome to CyberForce! 🎉', 'Welcome {{user_name}}! Start your cybersecurity journey with our personalized learning paths.', 'success', '🎉', 'FaRocket', 'green', 'system', 'Get Started'),
('challenge_new', 'New Challenge Available! 🚀', 'A new {{difficulty}} challenge "{{challenge_name}}" is now available. Test your skills!', 'info', '🚀', 'FaCode', 'blue', 'challenge', 'Start Challenge'),
('challenge_completed', 'Challenge Completed! 🏆', 'Congratulations! You completed "{{challenge_name}}" and earned {{points}} points!', 'success', '🏆', 'FaTrophy', 'gold', 'achievement', 'View Results'),
('learning_path_enrolled', 'Learning Path Enrolled! 📚', 'You have successfully enrolled in "{{path_name}}". Start learning now!', 'success', '📚', 'FaGraduationCap', 'green', 'learning', 'Start Learning'),
('streak_milestone', 'Learning Streak! 🔥', 'Amazing! You have maintained a {{days}} day learning streak. Keep it up!', 'success', '🔥', 'FaFire', 'orange', 'achievement', 'Continue Learning'),
('skill_level_up', 'Skill Level Up! ⭐', 'Your {{skill_name}} skill level increased to {{level}}! Great progress!', 'success', '⭐', 'FaStar', 'yellow', 'achievement', 'View Skills'),
('system_maintenance', 'System Maintenance ⚠️', 'Scheduled maintenance on {{date}}. The platform will be unavailable for {{duration}}.', 'warning', '⚠️', 'FaExclamationTriangle', 'orange', 'system', 'Learn More');

-- Function to create notification from template
CREATE OR REPLACE FUNCTION create_notification_from_template(
    template_name_param VARCHAR(100),
    user_id_param UUID,
    variables JSONB DEFAULT '{}'::JSONB,
    action_url_param TEXT DEFAULT NULL,
    scheduled_for_param TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    template_record notification_templates%ROWTYPE;
    final_title VARCHAR(255);
    final_message TEXT;
    notification_id UUID;
BEGIN
    -- Get template
    SELECT * INTO template_record FROM notification_templates WHERE template_name = template_name_param;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Template % not found', template_name_param;
    END IF;
    
    -- Replace variables in title and message
    final_title := template_record.title_template;
    final_message := template_record.message_template;
    
    -- Simple variable replacement (you can enhance this with more sophisticated templating)
    IF variables ? 'user_name' THEN
        final_title := REPLACE(final_title, '{{user_name}}', variables->>'user_name');
        final_message := REPLACE(final_message, '{{user_name}}', variables->>'user_name');
    END IF;
    
    IF variables ? 'challenge_name' THEN
        final_title := REPLACE(final_title, '{{challenge_name}}', variables->>'challenge_name');
        final_message := REPLACE(final_message, '{{challenge_name}}', variables->>'challenge_name');
    END IF;
    
    IF variables ? 'path_name' THEN
        final_title := REPLACE(final_title, '{{path_name}}', variables->>'path_name');
        final_message := REPLACE(final_message, '{{path_name}}', variables->>'path_name');
    END IF;
    
    IF variables ? 'difficulty' THEN
        final_title := REPLACE(final_title, '{{difficulty}}', variables->>'difficulty');
        final_message := REPLACE(final_message, '{{difficulty}}', variables->>'difficulty');
    END IF;
    
    IF variables ? 'points' THEN
        final_title := REPLACE(final_title, '{{points}}', variables->>'points');
        final_message := REPLACE(final_message, '{{points}}', variables->>'points');
    END IF;
    
    IF variables ? 'days' THEN
        final_title := REPLACE(final_title, '{{days}}', variables->>'days');
        final_message := REPLACE(final_message, '{{days}}', variables->>'days');
    END IF;
    
    IF variables ? 'skill_name' THEN
        final_title := REPLACE(final_title, '{{skill_name}}', variables->>'skill_name');
        final_message := REPLACE(final_message, '{{skill_name}}', variables->>'skill_name');
    END IF;
    
    IF variables ? 'level' THEN
        final_title := REPLACE(final_title, '{{level}}', variables->>'level');
        final_message := REPLACE(final_message, '{{level}}', variables->>'level');
    END IF;
    
    IF variables ? 'date' THEN
        final_title := REPLACE(final_title, '{{date}}', variables->>'date');
        final_message := REPLACE(final_message, '{{date}}', variables->>'date');
    END IF;
    
    IF variables ? 'duration' THEN
        final_title := REPLACE(final_title, '{{duration}}', variables->>'duration');
        final_message := REPLACE(final_message, '{{duration}}', variables->>'duration');
    END IF;
    
    -- Create notification
    INSERT INTO user_notifications (
        user_id, title, message, type, emoji, icon_name, icon_color, 
        category, action_url, action_label, metadata, scheduled_for
    ) VALUES (
        user_id_param, final_title, final_message, template_record.type, 
        template_record.emoji, template_record.icon_name, template_record.icon_color,
        template_record.category, action_url_param, template_record.action_label, 
        variables, scheduled_for_param
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create custom notification (for testing)
CREATE OR REPLACE FUNCTION create_test_notification(
    user_id_param UUID,
    title_param VARCHAR(255),
    message_param TEXT,
    type_param VARCHAR(50) DEFAULT 'info',
    emoji_param VARCHAR(10) DEFAULT NULL,
    action_url_param TEXT DEFAULT NULL,
    action_label_param VARCHAR(100) DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO user_notifications (
        user_id, title, message, type, emoji, action_url, action_label
    ) VALUES (
        user_id_param, title_param, message_param, type_param, 
        emoji_param, action_url_param, action_label_param
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(user_id_param UUID)
RETURNS INTEGER AS $$
DECLARE
    count_result INTEGER;
BEGIN
    SELECT COUNT(*) INTO count_result
    FROM user_notifications
    WHERE user_id = user_id_param 
    AND is_read = FALSE 
    AND is_dismissed = FALSE
    AND (expires_at IS NULL OR expires_at > NOW())
    AND (scheduled_for IS NULL OR scheduled_for <= NOW());
    
    RETURN count_result;
END;
$$ LANGUAGE plpgsql;

-- Function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(notification_id_param UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE user_notifications 
    SET is_read = TRUE, read_at = NOW()
    WHERE id = notification_id_param AND user_id = auth.uid();
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to dismiss notification
CREATE OR REPLACE FUNCTION dismiss_notification(notification_id_param UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE user_notifications 
    SET is_dismissed = TRUE, dismissed_at = NOW()
    WHERE id = notification_id_param AND user_id = auth.uid();
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Create some sample notifications for testing (replace with actual user IDs)
-- You can run these manually with real user IDs for testing

-- Example usage:
-- SELECT create_test_notification(
--     'your-user-id-here'::UUID,
--     'Test Notification 🚀',
--     'This is a test notification with emoji and action button!',
--     'info',
--     '🚀',
--     '/dashboard/challenges',
--     'View Challenges'
-- );

-- Success message
DO $$
BEGIN
    RAISE NOTICE '🎉 Enhanced Notification System created successfully!';
    RAISE NOTICE '✅ Rich content support (text, emojis, images, links)';
    RAISE NOTICE '✅ Notification templates system';
    RAISE NOTICE '✅ Priority and category support';
    RAISE NOTICE '✅ Scheduling and expiration support';
    RAISE NOTICE '✅ Helper functions for easy testing';
    RAISE NOTICE '🚀 Ready for testing with create_test_notification function!';
END $$;
