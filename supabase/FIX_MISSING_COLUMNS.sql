-- =============================================
-- FIX MISSING COLUMNS IN USER_PROFILES
-- Run this to add missing columns that are causing errors
-- =============================================

-- Add missing columns to user_profiles table
DO $$ 
BEGIN
    -- Add location column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'location') THEN
        ALTER TABLE public.user_profiles ADD COLUMN location TEXT;
        RAISE NOTICE '✅ Added location column';
    ELSE
        RAISE NOTICE '✅ location column already exists';
    END IF;
    
    -- Add country column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'country') THEN
        ALTER TABLE public.user_profiles ADD COLUMN country TEXT;
        RAISE NOTICE '✅ Added country column';
    ELSE
        RAISE NOTICE '✅ country column already exists';
    END IF;
    
    -- Add website column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'website') THEN
        ALTER TABLE public.user_profiles ADD COLUMN website TEXT;
        RAISE NOTICE '✅ Added website column';
    ELSE
        RAISE NOTICE '✅ website column already exists';
    END IF;
    
    -- Add phone_number column if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'phone_number') THEN
        ALTER TABLE public.user_profiles ADD COLUMN phone_number TEXT;
        RAISE NOTICE '✅ Added phone_number column';
    ELSE
        RAISE NOTICE '✅ phone_number column already exists';
    END IF;
    
    -- Add social media columns if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'linkedin_url') THEN
        ALTER TABLE public.user_profiles ADD COLUMN linkedin_url TEXT;
        RAISE NOTICE '✅ Added linkedin_url column';
    ELSE
        RAISE NOTICE '✅ linkedin_url column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'github_url') THEN
        ALTER TABLE public.user_profiles ADD COLUMN github_url TEXT;
        RAISE NOTICE '✅ Added github_url column';
    ELSE
        RAISE NOTICE '✅ github_url column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'twitter_url') THEN
        ALTER TABLE public.user_profiles ADD COLUMN twitter_url TEXT;
        RAISE NOTICE '✅ Added twitter_url column';
    ELSE
        RAISE NOTICE '✅ twitter_url column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'instagram_url') THEN
        ALTER TABLE public.user_profiles ADD COLUMN instagram_url TEXT;
        RAISE NOTICE '✅ Added instagram_url column';
    ELSE
        RAISE NOTICE '✅ instagram_url column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'facebook_url') THEN
        ALTER TABLE public.user_profiles ADD COLUMN facebook_url TEXT;
        RAISE NOTICE '✅ Added facebook_url column';
    ELSE
        RAISE NOTICE '✅ facebook_url column already exists';
    END IF;
    
    -- Add profile visibility and privacy columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'profile_visibility') THEN
        ALTER TABLE public.user_profiles ADD COLUMN profile_visibility TEXT DEFAULT 'public' CHECK (profile_visibility IN ('public', 'private', 'friends'));
        RAISE NOTICE '✅ Added profile_visibility column';
    ELSE
        RAISE NOTICE '✅ profile_visibility column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'show_email') THEN
        ALTER TABLE public.user_profiles ADD COLUMN show_email BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ Added show_email column';
    ELSE
        RAISE NOTICE '✅ show_email column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'show_phone') THEN
        ALTER TABLE public.user_profiles ADD COLUMN show_phone BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ Added show_phone column';
    ELSE
        RAISE NOTICE '✅ show_phone column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'show_location') THEN
        ALTER TABLE public.user_profiles ADD COLUMN show_location BOOLEAN DEFAULT TRUE;
        RAISE NOTICE '✅ Added show_location column';
    ELSE
        RAISE NOTICE '✅ show_location column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'show_social_links') THEN
        ALTER TABLE public.user_profiles ADD COLUMN show_social_links BOOLEAN DEFAULT TRUE;
        RAISE NOTICE '✅ Added show_social_links column';
    ELSE
        RAISE NOTICE '✅ show_social_links column already exists';
    END IF;
    
    -- Add notification preferences
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'email_notifications') THEN
        ALTER TABLE public.user_profiles ADD COLUMN email_notifications BOOLEAN DEFAULT TRUE;
        RAISE NOTICE '✅ Added email_notifications column';
    ELSE
        RAISE NOTICE '✅ email_notifications column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'push_notifications') THEN
        ALTER TABLE public.user_profiles ADD COLUMN push_notifications BOOLEAN DEFAULT TRUE;
        RAISE NOTICE '✅ Added push_notifications column';
    ELSE
        RAISE NOTICE '✅ push_notifications column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'marketing_emails') THEN
        ALTER TABLE public.user_profiles ADD COLUMN marketing_emails BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ Added marketing_emails column';
    ELSE
        RAISE NOTICE '✅ marketing_emails column already exists';
    END IF;
    
    -- Add theme and language preferences
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'theme_preference') THEN
        ALTER TABLE public.user_profiles ADD COLUMN theme_preference TEXT DEFAULT 'system' CHECK (theme_preference IN ('light', 'dark', 'system'));
        RAISE NOTICE '✅ Added theme_preference column';
    ELSE
        RAISE NOTICE '✅ theme_preference column already exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'preferred_language') THEN
        ALTER TABLE public.user_profiles ADD COLUMN preferred_language TEXT DEFAULT 'en';
        RAISE NOTICE '✅ Added preferred_language column';
    ELSE
        RAISE NOTICE '✅ preferred_language column already exists';
    END IF;
    
END $$;

-- Success message
DO $$ 
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== MISSING COLUMNS FIXED ===';
    RAISE NOTICE 'All required columns have been added to user_profiles table';
    RAISE NOTICE 'You can now use the profile page without column errors';
    RAISE NOTICE '';
END $$;
