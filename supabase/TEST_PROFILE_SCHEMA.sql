-- =============================================
-- TEST PROFILE SCHEMA
-- Run this to verify the schema is working correctly
-- =============================================

-- Test 1: Check if tables exist
DO $$ 
BEGIN
    -- Check user_profiles table
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        RAISE NOTICE '✅ user_profiles table exists';
    ELSE
        RAISE NOTICE '❌ user_profiles table missing';
    END IF;
    
    -- Check user_settings table
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_settings') THEN
        RAISE NOTICE '✅ user_settings table exists';
    ELSE
        RAISE NOTICE '❌ user_settings table missing';
    END IF;
    
    -- Check user_avatars table
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_avatars') THEN
        RAISE NOTICE '✅ user_avatars table exists';
    ELSE
        RAISE NOTICE '❌ user_avatars table missing';
    END IF;
    
    -- Check user_activity_log table
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_activity_log') THEN
        RAISE NOTICE '✅ user_activity_log table exists';
    ELSE
        RAISE NOTICE '❌ user_activity_log table missing';
    END IF;
END $$;

-- Test 2: Check if required columns exist in user_profiles
DO $$ 
DECLARE
    missing_columns TEXT[] := '{}';
    required_columns TEXT[] := ARRAY[
        'id', 'user_id', 'username', 'full_name', 'bio', 'avatar_url',
        'email', 'phone_number', 'location', 'country', 'website',
        'linkedin_url', 'github_url', 'twitter_url', 'instagram_url', 'facebook_url',
        'subscription_tier', 'profile_visibility', 'show_email', 'show_phone',
        'show_location', 'show_social_links', 'email_notifications', 'push_notifications',
        'marketing_emails', 'theme_preference', 'preferred_language'
    ];
    col TEXT;
BEGIN
    FOREACH col IN ARRAY required_columns
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'user_profiles' AND column_name = col
        ) THEN
            missing_columns := array_append(missing_columns, col);
        END IF;
    END LOOP;
    
    IF array_length(missing_columns, 1) IS NULL THEN
        RAISE NOTICE '✅ All required columns exist in user_profiles';
    ELSE
        RAISE NOTICE '❌ Missing columns in user_profiles: %', array_to_string(missing_columns, ', ');
    END IF;
END $$;

-- Test 3: Check RLS policies
DO $$ 
BEGIN
    -- Check if RLS is enabled
    IF EXISTS (
        SELECT 1 FROM pg_tables 
        WHERE tablename = 'user_profiles' AND rowsecurity = true
    ) THEN
        RAISE NOTICE '✅ RLS enabled on user_profiles';
    ELSE
        RAISE NOTICE '❌ RLS not enabled on user_profiles';
    END IF;
    
    -- Check if policies exist
    IF EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'user_profiles' AND policyname = 'Users can view their own profile'
    ) THEN
        RAISE NOTICE '✅ User profile view policy exists';
    ELSE
        RAISE NOTICE '❌ User profile view policy missing';
    END IF;
END $$;

-- Test 4: Check triggers
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_user_profiles_updated_at'
    ) THEN
        RAISE NOTICE '✅ Update trigger exists on user_profiles';
    ELSE
        RAISE NOTICE '❌ Update trigger missing on user_profiles';
    END IF;
END $$;

-- Test 5: Check indexes
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'user_profiles' AND indexname = 'idx_user_profiles_user_id'
    ) THEN
        RAISE NOTICE '✅ User ID index exists on user_profiles';
    ELSE
        RAISE NOTICE '❌ User ID index missing on user_profiles';
    END IF;
END $$;

-- Test 6: Test basic operations (if you have a test user)
-- Uncomment and modify the user ID below to test with a real user
/*
DO $$ 
DECLARE
    test_user_id UUID := 'YOUR_USER_ID_HERE'; -- Replace with actual user ID
    test_profile_id UUID;
BEGIN
    -- Test insert
    INSERT INTO user_profiles (
        id, user_id, username, full_name, email, subscription_tier
    ) VALUES (
        test_user_id, test_user_id, 'testuser', 'Test User', '<EMAIL>', 'free'
    ) ON CONFLICT (id) DO UPDATE SET
        full_name = EXCLUDED.full_name
    RETURNING id INTO test_profile_id;
    
    RAISE NOTICE '✅ Profile insert/update successful: %', test_profile_id;
    
    -- Test select
    IF EXISTS (SELECT 1 FROM user_profiles WHERE id = test_user_id) THEN
        RAISE NOTICE '✅ Profile select successful';
    ELSE
        RAISE NOTICE '❌ Profile select failed';
    END IF;
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE '❌ Profile operations failed: %', SQLERRM;
END $$;
*/

-- Final summary
DO $$ 
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== PROFILE SCHEMA TEST COMPLETE ===';
    RAISE NOTICE 'If you see ✅ for all tests above, your schema is ready!';
    RAISE NOTICE 'If you see ❌ for any tests, please run the PROFILE_SCHEMA_FIXED.sql file first.';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Create avatars storage bucket in Supabase Dashboard';
    RAISE NOTICE '2. Test the profile page at /dashboard/profile';
    RAISE NOTICE '3. Try uploading an avatar and editing profile information';
END $$;
