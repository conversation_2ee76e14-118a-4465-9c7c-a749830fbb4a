-- =============================================
-- USER PROGRESS TRACKING SYSTEM
-- =============================================
-- Complete database schema for tracking user learning progress,
-- assessments, recommendations, and personalized learning paths
-- =============================================

-- Create assessment_results table
CREATE TABLE IF NOT EXISTS assessment_results (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    assessment_type VARCHAR(50) DEFAULT 'initial',
    score INTEGER NOT NULL,
    level VARCHAR(20) NOT NULL, -- beginner, intermediate, advanced
    answers JSONB NOT NULL,
    recommendations TEXT[] DEFAULT '{}',
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_learning_progress table
CREATE TABLE IF NOT EXISTS user_learning_progress (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    learning_path_id UUID NOT NULL,
    module_id UUID,
    lesson_id UUID,
    progress_percentage INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, learning_path_id, module_id, lesson_id)
);

-- Create user_recommendations table
CREATE TABLE IF NOT EXISTS user_recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    recommendation_type VARCHAR(50) NOT NULL, -- 'learning_path', 'challenge', 'certification'
    item_id UUID NOT NULL,
    item_title VARCHAR(255) NOT NULL,
    reason TEXT,
    priority INTEGER DEFAULT 1, -- 1=high, 2=medium, 3=low
    is_viewed BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_activity_log table
CREATE TABLE IF NOT EXISTS user_activity_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL, -- 'login', 'module_start', 'module_complete', 'challenge_start', etc.
    activity_data JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_learning_streaks table
CREATE TABLE IF NOT EXISTS user_learning_streaks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_activity_date DATE DEFAULT CURRENT_DATE,
    streak_start_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_skill_assessments table
CREATE TABLE IF NOT EXISTS user_skill_assessments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    skill_name VARCHAR(100) NOT NULL,
    skill_level INTEGER DEFAULT 0, -- 0-100 scale
    assessment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    evidence_type VARCHAR(50), -- 'module_completion', 'challenge_success', 'manual_assessment'
    evidence_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_notifications table
CREATE TABLE IF NOT EXISTS user_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info', -- 'info', 'success', 'warning', 'error'
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    action_label VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_assessment_results_user_id ON assessment_results(user_id);
CREATE INDEX IF NOT EXISTS idx_assessment_results_type ON assessment_results(assessment_type);

CREATE INDEX IF NOT EXISTS idx_user_learning_progress_user_id ON user_learning_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_path_id ON user_learning_progress(learning_path_id);
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_last_accessed ON user_learning_progress(last_accessed);

CREATE INDEX IF NOT EXISTS idx_user_recommendations_user_id ON user_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_type ON user_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_priority ON user_recommendations(priority);

CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_type ON user_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_created_at ON user_activity_log(created_at);

CREATE INDEX IF NOT EXISTS idx_user_learning_streaks_user_id ON user_learning_streaks(user_id);

CREATE INDEX IF NOT EXISTS idx_user_skill_assessments_user_id ON user_skill_assessments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_skill_assessments_skill ON user_skill_assessments(skill_name);

CREATE INDEX IF NOT EXISTS idx_user_notifications_user_id ON user_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_read ON user_notifications(is_read);

-- Enable RLS on all tables
ALTER TABLE assessment_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_learning_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_learning_streaks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_skill_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Assessment Results
CREATE POLICY "Users can view their own assessment results" ON assessment_results
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own assessment results" ON assessment_results
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own assessment results" ON assessment_results
    FOR UPDATE USING (auth.uid() = user_id);

-- User Learning Progress
CREATE POLICY "Users can view their own learning progress" ON user_learning_progress
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own learning progress" ON user_learning_progress
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own learning progress" ON user_learning_progress
    FOR UPDATE USING (auth.uid() = user_id);

-- User Recommendations
CREATE POLICY "Users can view their own recommendations" ON user_recommendations
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own recommendations" ON user_recommendations
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own recommendations" ON user_recommendations
    FOR UPDATE USING (auth.uid() = user_id);

-- User Activity Log
CREATE POLICY "Users can view their own activity log" ON user_activity_log
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own activity log" ON user_activity_log
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- User Learning Streaks
CREATE POLICY "Users can view their own learning streaks" ON user_learning_streaks
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own learning streaks" ON user_learning_streaks
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own learning streaks" ON user_learning_streaks
    FOR UPDATE USING (auth.uid() = user_id);

-- User Skill Assessments
CREATE POLICY "Users can view their own skill assessments" ON user_skill_assessments
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own skill assessments" ON user_skill_assessments
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own skill assessments" ON user_skill_assessments
    FOR UPDATE USING (auth.uid() = user_id);

-- User Notifications
CREATE POLICY "Users can view their own notifications" ON user_notifications
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own notifications" ON user_notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Create functions for automatic updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_assessment_results_updated_at BEFORE UPDATE ON assessment_results
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_learning_progress_updated_at BEFORE UPDATE ON user_learning_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_recommendations_updated_at BEFORE UPDATE ON user_recommendations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_learning_streaks_updated_at BEFORE UPDATE ON user_learning_streaks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_skill_assessments_updated_at BEFORE UPDATE ON user_skill_assessments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update learning streak
CREATE OR REPLACE FUNCTION update_learning_streak(user_uuid UUID)
RETURNS VOID AS $$
DECLARE
    last_activity DATE;
    current_streak INTEGER;
    longest_streak INTEGER;
BEGIN
    -- Get current streak data
    SELECT last_activity_date, current_streak, longest_streak
    INTO last_activity, current_streak, longest_streak
    FROM user_learning_streaks
    WHERE user_id = user_uuid;

    -- If no record exists, create one
    IF NOT FOUND THEN
        INSERT INTO user_learning_streaks (user_id, current_streak, longest_streak, last_activity_date, streak_start_date)
        VALUES (user_uuid, 1, 1, CURRENT_DATE, CURRENT_DATE);
        RETURN;
    END IF;

    -- Check if activity is today
    IF last_activity = CURRENT_DATE THEN
        -- Already logged activity today, no change needed
        RETURN;
    END IF;

    -- Check if activity was yesterday (continuing streak)
    IF last_activity = CURRENT_DATE - INTERVAL '1 day' THEN
        -- Continue streak
        current_streak := current_streak + 1;
        longest_streak := GREATEST(longest_streak, current_streak);
    ELSE
        -- Streak broken, start new streak
        current_streak := 1;
    END IF;

    -- Update the record
    UPDATE user_learning_streaks
    SET current_streak = current_streak,
        longest_streak = longest_streak,
        last_activity_date = CURRENT_DATE,
        updated_at = NOW()
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to log user activity
CREATE OR REPLACE FUNCTION log_user_activity(
    user_uuid UUID,
    activity_type_param VARCHAR(50),
    activity_data_param JSONB DEFAULT '{}'::JSONB,
    ip_address_param INET DEFAULT NULL,
    user_agent_param TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO user_activity_log (user_id, activity_type, activity_data, ip_address, user_agent)
    VALUES (user_uuid, activity_type_param, activity_data_param, ip_address_param, user_agent_param);
    
    -- Update learning streak for learning activities
    IF activity_type_param IN ('module_start', 'module_complete', 'challenge_start', 'challenge_complete') THEN
        PERFORM update_learning_streak(user_uuid);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '🎉 User Progress Tracking System created successfully!';
    RAISE NOTICE '✅ Assessment results tracking enabled';
    RAISE NOTICE '✅ Learning progress tracking enabled';
    RAISE NOTICE '✅ Personalized recommendations system ready';
    RAISE NOTICE '✅ Activity logging system active';
    RAISE NOTICE '✅ Learning streaks tracking enabled';
    RAISE NOTICE '✅ Skill assessments tracking ready';
    RAISE NOTICE '✅ Notifications system enabled';
    RAISE NOTICE '🚀 All user progress tracking features are now available!';
END $$;
