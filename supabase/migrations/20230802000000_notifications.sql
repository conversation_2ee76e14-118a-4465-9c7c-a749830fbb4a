-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL DEFAULT 'info',
  data JSONB NOT NULL DEFAULT '{}'::JSONB,
  is_read BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for notifications table
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications"
  ON notifications
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications"
  ON notifications
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notifications"
  ON notifications
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notifications"
  ON notifications
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS notifications_user_id_idx ON notifications(user_id);
CREATE INDEX IF NOT EXISTS notifications_created_at_idx ON notifications(created_at);
CREATE INDEX IF NOT EXISTS notifications_is_read_idx ON notifications(is_read);

-- Create function to create a notification
CREATE OR REPLACE FUNCTION create_notification(
  p_user_id UUID,
  p_title TEXT,
  p_message TEXT,
  p_type TEXT DEFAULT 'info',
  p_data JSONB DEFAULT '{}'::JSONB
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_notification_id UUID;
BEGIN
  INSERT INTO notifications (
    user_id,
    title,
    message,
    type,
    data,
    is_read,
    created_at
  ) VALUES (
    p_user_id,
    p_title,
    p_message,
    p_type,
    p_data,
    FALSE,
    NOW()
  ) RETURNING id INTO v_notification_id;
  
  RETURN v_notification_id;
END;
$$;

-- Create function to mark a notification as read
CREATE OR REPLACE FUNCTION mark_notification_as_read(
  p_notification_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get the user_id of the notification
  SELECT user_id INTO v_user_id
  FROM notifications
  WHERE id = p_notification_id;
  
  -- Check if the current user is the owner of the notification
  IF v_user_id = auth.uid() THEN
    -- Update the notification
    UPDATE notifications
    SET is_read = TRUE
    WHERE id = p_notification_id;
    
    RETURN TRUE;
  ELSE
    RETURN FALSE;
  END IF;
END;
$$;

-- Create function to mark all notifications as read for a user
CREATE OR REPLACE FUNCTION mark_all_notifications_as_read()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update all notifications for the current user
  UPDATE notifications
  SET is_read = TRUE
  WHERE user_id = auth.uid() AND is_read = FALSE;
  
  RETURN TRUE;
END;
$$;

-- Create function to delete a notification
CREATE OR REPLACE FUNCTION delete_notification(
  p_notification_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get the user_id of the notification
  SELECT user_id INTO v_user_id
  FROM notifications
  WHERE id = p_notification_id;
  
  -- Check if the current user is the owner of the notification
  IF v_user_id = auth.uid() THEN
    -- Delete the notification
    DELETE FROM notifications
    WHERE id = p_notification_id;
    
    RETURN TRUE;
  ELSE
    RETURN FALSE;
  END IF;
END;
$$;

-- Create function to delete all notifications for a user
CREATE OR REPLACE FUNCTION delete_all_notifications()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Delete all notifications for the current user
  DELETE FROM notifications
  WHERE user_id = auth.uid();
  
  RETURN TRUE;
END;
$$;

-- Create function to create a security event notification
CREATE OR REPLACE FUNCTION create_security_event_notification()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create a notification for the user
  IF NEW.user_id IS NOT NULL THEN
    PERFORM create_notification(
      NEW.user_id,
      CASE 
        WHEN NEW.severity = 'critical' THEN 'Critical Security Alert'
        WHEN NEW.severity = 'high' THEN 'High Security Alert'
        WHEN NEW.severity = 'medium' THEN 'Security Warning'
        ELSE 'Security Information'
      END,
      NEW.event_data->>'message',
      CASE 
        WHEN NEW.severity = 'critical' OR NEW.severity = 'high' THEN 'alert'
        WHEN NEW.severity = 'medium' THEN 'warning'
        ELSE 'info'
      END,
      jsonb_build_object(
        'event_id', NEW.id,
        'event_type', NEW.event_type,
        'severity', NEW.severity,
        'data', NEW.event_data
      )
    );
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger for security events
CREATE TRIGGER security_event_notification_trigger
AFTER INSERT ON security_events
FOR EACH ROW
EXECUTE FUNCTION create_security_event_notification();
