-- Create security_posture table
CREATE TABLE IF NOT EXISTS security_posture (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  overall_score INTEGER NOT NULL DEFAULT 0,
  previous_score INTEGER NOT NULL DEFAULT 0,
  score_change INTEGER NOT NULL DEFAULT 0,
  strengths JSONB NOT NULL DEFAULT '[]'::JSONB,
  weaknesses JSONB NOT NULL DEFAULT '[]'::JSON<PERSON>,
  recommendations JSONB NOT NULL DEFAULT '[]'::JSON<PERSON>,
  recent_activity JSONB NOT NULL DEFAULT '[]'::JSON<PERSON>,
  categories JSONB NOT NULL DEFAULT '[]'::JSON<PERSON>,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create threat_intelligence table
CREATE TABLE IF NOT EXISTS threat_intelligence (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  global_threats JSONB NOT NULL DEFAULT '[]'::JSONB,
  vulnerabilities JSONB NOT NULL DEFAULT '[]'::JSONB,
  threat_map JSONB NOT NULL DEFAULT '{}'::JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create threat_data_cache table
CREATE TABLE IF NOT EXISTS threat_data_cache (
  id TEXT PRIMARY KEY,
  attacks JSONB NOT NULL DEFAULT '[]'::JSONB,
  statistics JSONB NOT NULL DEFAULT '{}'::JSONB,
  vulnerabilities JSONB NOT NULL DEFAULT '[]'::JSONB,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create security_events table for tracking security-related events
CREATE TABLE IF NOT EXISTS security_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  event_data JSONB NOT NULL DEFAULT '{}'::JSONB,
  severity TEXT NOT NULL DEFAULT 'info',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create security_assessments table for tracking user assessments
CREATE TABLE IF NOT EXISTS security_assessments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  assessment_type TEXT NOT NULL,
  category TEXT NOT NULL,
  score INTEGER NOT NULL DEFAULT 0,
  max_score INTEGER NOT NULL DEFAULT 100,
  answers JSONB NOT NULL DEFAULT '[]'::JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create security_recommendations table for personalized recommendations
CREATE TABLE IF NOT EXISTS security_recommendations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  type TEXT NOT NULL,
  link TEXT,
  priority INTEGER NOT NULL DEFAULT 0,
  is_completed BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for security_posture table
ALTER TABLE security_posture ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own security posture"
  ON security_posture
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own security posture"
  ON security_posture
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own security posture"
  ON security_posture
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for threat_intelligence table
ALTER TABLE threat_intelligence ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view threat intelligence"
  ON threat_intelligence
  FOR SELECT
  TO authenticated
  USING (true);

-- Create RLS policies for security_events table
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own security events"
  ON security_events
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own security events"
  ON security_events
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for security_assessments table
ALTER TABLE security_assessments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own security assessments"
  ON security_assessments
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own security assessments"
  ON security_assessments
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for security_recommendations table
ALTER TABLE security_recommendations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own security recommendations"
  ON security_recommendations
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own security recommendations"
  ON security_recommendations
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own security recommendations"
  ON security_recommendations
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create functions for updating security posture
CREATE OR REPLACE FUNCTION update_security_posture()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the updated_at timestamp
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating security posture
CREATE TRIGGER update_security_posture_trigger
BEFORE UPDATE ON security_posture
FOR EACH ROW
EXECUTE FUNCTION update_security_posture();

-- Create function for updating threat intelligence
CREATE OR REPLACE FUNCTION update_threat_intelligence()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the updated_at timestamp
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating threat intelligence
CREATE TRIGGER update_threat_intelligence_trigger
BEFORE UPDATE ON threat_intelligence
FOR EACH ROW
EXECUTE FUNCTION update_threat_intelligence();

-- Create function for updating security recommendations
CREATE OR REPLACE FUNCTION update_security_recommendations()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the updated_at timestamp
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating security recommendations
CREATE TRIGGER update_security_recommendations_trigger
BEFORE UPDATE ON security_recommendations
FOR EACH ROW
EXECUTE FUNCTION update_security_recommendations();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS security_posture_user_id_idx ON security_posture(user_id);
CREATE INDEX IF NOT EXISTS security_events_user_id_idx ON security_events(user_id);
CREATE INDEX IF NOT EXISTS security_assessments_user_id_idx ON security_assessments(user_id);
CREATE INDEX IF NOT EXISTS security_recommendations_user_id_idx ON security_recommendations(user_id);
