-- =============================================
-- SAFE INCREMENTAL SCHEMA BUILDER
-- =============================================
-- This script safely adds missing columns and tables
-- without breaking existing data or creating conflicts
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- 1. FIX PROFILE TABLES SAFELY
-- =============================================

-- First, check what profile table structure we have
DO $$
DECLARE
    profiles_exists BOOLEAN;
    user_profiles_exists BOOLEAN;
    profiles_is_view BOOLEAN;
BEGIN
    -- Check if tables exist
    SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'profiles') INTO profiles_exists;
    SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') INTO user_profiles_exists;
    SELECT EXISTS (SELECT FROM information_schema.views WHERE table_name = 'profiles') INTO profiles_is_view;
    
    RAISE NOTICE 'Profiles table exists: %', profiles_exists;
    RAISE NOTICE 'User_profiles table exists: %', user_profiles_exists;
    RAISE NOTICE 'Profiles is view: %', profiles_is_view;
    
    -- If profiles is a view, we'll work with the underlying table
    IF profiles_exists AND profiles_is_view THEN
        RAISE NOTICE 'Profiles is a view, will work with underlying table';
    END IF;
END $$;

-- Add missing columns to existing tables safely
DO $$
BEGIN
    -- Work with user_profiles table if it exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        -- Add profile_completion_percentage if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'profile_completion_percentage') THEN
            ALTER TABLE user_profiles ADD COLUMN profile_completion_percentage INTEGER DEFAULT 0;
            RAISE NOTICE 'Added profile_completion_percentage to user_profiles';
        END IF;
        
        -- Add onboarding_completed if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'onboarding_completed') THEN
            ALTER TABLE user_profiles ADD COLUMN onboarding_completed BOOLEAN DEFAULT FALSE;
            RAISE NOTICE 'Added onboarding_completed to user_profiles';
        END IF;
        
        -- Add email_verified if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'email_verified') THEN
            ALTER TABLE user_profiles ADD COLUMN email_verified BOOLEAN DEFAULT FALSE;
            RAISE NOTICE 'Added email_verified to user_profiles';
        END IF;
        
        -- Add phone_verified if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'phone_verified') THEN
            ALTER TABLE user_profiles ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
            RAISE NOTICE 'Added phone_verified to user_profiles';
        END IF;
        
        -- Add updated_at if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'updated_at') THEN
            ALTER TABLE user_profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
            RAISE NOTICE 'Added updated_at to user_profiles';
        END IF;
    END IF;
    
    -- Work with profiles table if it exists and is not a view
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'profiles') 
       AND NOT EXISTS (SELECT FROM information_schema.views WHERE table_name = 'profiles') THEN
        -- Add profile_completion_percentage if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'profile_completion_percentage') THEN
            ALTER TABLE profiles ADD COLUMN profile_completion_percentage INTEGER DEFAULT 0;
            RAISE NOTICE 'Added profile_completion_percentage to profiles';
        END IF;
        
        -- Add onboarding_completed if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'onboarding_completed') THEN
            ALTER TABLE profiles ADD COLUMN onboarding_completed BOOLEAN DEFAULT FALSE;
            RAISE NOTICE 'Added onboarding_completed to profiles';
        END IF;
        
        -- Add email_verified if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'email_verified') THEN
            ALTER TABLE profiles ADD COLUMN email_verified BOOLEAN DEFAULT FALSE;
            RAISE NOTICE 'Added email_verified to profiles';
        END IF;
        
        -- Add phone_verified if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'phone_verified') THEN
            ALTER TABLE profiles ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
            RAISE NOTICE 'Added phone_verified to profiles';
        END IF;
        
        -- Add updated_at if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'updated_at') THEN
            ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
            RAISE NOTICE 'Added updated_at to profiles';
        END IF;
    END IF;
END $$;

-- =============================================
-- 2. CREATE MISSING ESSENTIAL TABLES ONLY
-- =============================================

-- Create challenges table if it doesn't exist (referenced in code)
CREATE TABLE IF NOT EXISTS challenges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    challenge_type TEXT DEFAULT 'simulation',
    points INTEGER DEFAULT 100,
    is_active BOOLEAN DEFAULT TRUE,
    content JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create challenge_attempts table if it doesn't exist
CREATE TABLE IF NOT EXISTS challenge_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'failed')),
    score INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    submission_data JSONB DEFAULT '{}'
);

-- Create user_events table if it doesn't exist (for analytics)
CREATE TABLE IF NOT EXISTS user_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    event_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notifications table if it doesn't exist
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    notification_type TEXT DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create security_posture table if it doesn't exist
CREATE TABLE IF NOT EXISTS security_posture (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    overall_score INTEGER DEFAULT 0,
    risk_level TEXT DEFAULT 'medium',
    last_scan_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create threat_data_cache table if it doesn't exist
CREATE TABLE IF NOT EXISTS threat_data_cache (
    id TEXT PRIMARY KEY,
    data_type TEXT NOT NULL,
    content JSONB NOT NULL DEFAULT '{}',
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE
);

-- =============================================
-- 3. CREATE BASIC INDEXES FOR PERFORMANCE
-- =============================================

-- Profile indexes (create conditionally)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
        CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
    END IF;

    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'profiles')
       AND NOT EXISTS (SELECT FROM information_schema.views WHERE table_schema = 'public' AND table_name = 'profiles') THEN
        CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
    END IF;
END $$;

-- Challenge indexes
CREATE INDEX IF NOT EXISTS idx_challenge_attempts_user_id ON challenge_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_challenge_attempts_challenge_id ON challenge_attempts(challenge_id);

-- Event indexes
CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON user_events(user_id);
CREATE INDEX IF NOT EXISTS idx_user_events_type ON user_events(event_type);

-- Notification indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);

-- =============================================
-- 4. ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable RLS on user-specific tables
ALTER TABLE challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE challenge_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_posture ENABLE ROW LEVEL SECURITY;

-- Create basic policies
CREATE POLICY "Users can view their own challenge attempts" ON challenge_attempts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own events" ON user_events
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own notifications" ON notifications
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own security posture" ON security_posture
    FOR ALL USING (auth.uid() = user_id);

-- Public read for challenges
CREATE POLICY "Anyone can view challenges" ON challenges
    FOR SELECT USING (true);

-- =============================================
-- 5. CREATE UPDATE TRIGGER FUNCTION
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers to tables that have updated_at
DO $$
BEGIN
    -- Add trigger to user_profiles if it exists and has updated_at column
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') 
       AND EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'updated_at') THEN
        DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
        CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Add trigger to profiles if it exists and has updated_at column
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'profiles') 
       AND EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'updated_at')
       AND NOT EXISTS (SELECT FROM information_schema.views WHERE table_name = 'profiles') THEN
        DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
        CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Add triggers to new tables
CREATE TRIGGER update_challenges_updated_at BEFORE UPDATE ON challenges
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_security_posture_updated_at BEFORE UPDATE ON security_posture
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- COMPLETION MESSAGE
-- =============================================

DO $$
BEGIN
    RAISE NOTICE '✅ Safe Incremental Schema completed successfully!';
    RAISE NOTICE '📊 Essential tables and columns have been added safely.';
    RAISE NOTICE '🔒 Row Level Security is enabled on new tables.';
    RAISE NOTICE '⚡ Basic performance indexes have been added.';
    RAISE NOTICE '🚀 Your database is ready for avatar upload and core functionality!';
END $$;
