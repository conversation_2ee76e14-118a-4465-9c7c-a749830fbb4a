-- =============================================
-- COMPREHENSIVE SUPER ADMIN SETUP
-- =============================================
-- Create complete super admin system with CMS capabilities
-- User UUID: 5971f7c3-840f-4d2c-9931-db26d1978f5a
-- =============================================

-- 1. Create admin_roles table
CREATE TABLE IF NOT EXISTS admin_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    role VARCHAR(50) NOT NULL DEFAULT 'user', -- 'user', 'admin', 'super_admin'
    permissions JSONB DEFAULT '{}',
    granted_by UUID REFERENCES auth.users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create admin_permissions table
CREATE TABLE IF NOT EXISTS admin_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Insert comprehensive permissions
INSERT INTO admin_permissions (permission_name, description, category) VALUES
-- User Management
('users.view', 'View user profiles and data', 'user_management'),
('users.edit', 'Edit user profiles and settings', 'user_management'),
('users.delete', 'Delete user accounts', 'user_management'),
('users.ban', 'Ban/unban users', 'user_management'),
('users.subscription.change', 'Change user subscription levels', 'user_management'),
('users.progress.reset', 'Reset user progress', 'user_management'),

-- Content Management (CMS)
('content.challenges.create', 'Create new challenges', 'content_management'),
('content.challenges.edit', 'Edit existing challenges', 'content_management'),
('content.challenges.delete', 'Delete challenges', 'content_management'),
('content.learning_paths.create', 'Create learning paths', 'content_management'),
('content.learning_paths.edit', 'Edit learning paths', 'content_management'),
('content.learning_paths.delete', 'Delete learning paths', 'content_management'),
('content.modules.create', 'Create learning modules', 'content_management'),
('content.modules.edit', 'Edit learning modules', 'content_management'),
('content.modules.delete', 'Delete learning modules', 'content_management'),
('content.media.upload', 'Upload media files', 'content_management'),
('content.media.manage', 'Manage media library', 'content_management'),

-- Notification Management
('notifications.send', 'Send notifications to users', 'notification_management'),
('notifications.broadcast', 'Send broadcast notifications', 'notification_management'),
('notifications.templates.manage', 'Manage notification templates', 'notification_management'),

-- System Management
('system.analytics.view', 'View system analytics', 'system_management'),
('system.logs.view', 'View system logs', 'system_management'),
('system.settings.edit', 'Edit system settings', 'system_management'),
('system.backup.create', 'Create system backups', 'system_management'),
('system.maintenance.mode', 'Enable/disable maintenance mode', 'system_management'),

-- Admin Management
('admin.roles.assign', 'Assign admin roles to users', 'admin_management'),
('admin.permissions.manage', 'Manage admin permissions', 'admin_management'),
('admin.super_admin.access', 'Access super admin dashboard', 'admin_management')

ON CONFLICT (permission_name) DO NOTHING;

-- 4. Create role_permissions junction table
CREATE TABLE IF NOT EXISTS role_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    role VARCHAR(50) NOT NULL,
    permission_name VARCHAR(100) REFERENCES admin_permissions(permission_name),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role, permission_name)
);

-- 5. Assign ALL permissions to super_admin role
INSERT INTO role_permissions (role, permission_name)
SELECT 'super_admin', permission_name FROM admin_permissions
ON CONFLICT (role, permission_name) DO NOTHING;

-- 6. Create admin activity log
CREATE TABLE IF NOT EXISTS admin_activity_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_user_id UUID REFERENCES auth.users(id),
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create indexes
CREATE INDEX IF NOT EXISTS idx_admin_roles_user_id ON admin_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_roles_role ON admin_roles(role);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_admin_user ON admin_activity_log(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_created_at ON admin_activity_log(created_at);

-- 8. Enable RLS
ALTER TABLE admin_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_log ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies
CREATE POLICY "Super admins can view all admin roles" ON admin_roles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM admin_roles ar 
            WHERE ar.user_id = auth.uid() 
            AND ar.role = 'super_admin' 
            AND ar.is_active = true
        )
    );

CREATE POLICY "Users can view their own admin role" ON admin_roles
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Super admins can manage admin roles" ON admin_roles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_roles ar 
            WHERE ar.user_id = auth.uid() 
            AND ar.role = 'super_admin' 
            AND ar.is_active = true
        )
    );

-- 10. Create utility functions
CREATE OR REPLACE FUNCTION is_super_admin(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM admin_roles
        WHERE user_id = user_uuid 
        AND role = 'super_admin' 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION has_permission(user_uuid UUID, permission_name_param VARCHAR(100))
RETURNS BOOLEAN AS $$
DECLARE
    user_role VARCHAR(50);
BEGIN
    SELECT role INTO user_role
    FROM admin_roles
    WHERE user_id = user_uuid AND is_active = true;
    
    IF user_role IS NULL THEN
        RETURN FALSE;
    END IF;
    
    RETURN EXISTS (
        SELECT 1 FROM role_permissions
        WHERE role = user_role AND permission_name = permission_name_param
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION log_admin_activity(
    action_param VARCHAR(100),
    target_type_param VARCHAR(50) DEFAULT NULL,
    target_id_param UUID DEFAULT NULL,
    details_param JSONB DEFAULT '{}'::JSONB
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO admin_activity_log (
        admin_user_id, action, target_type, target_id, details
    ) VALUES (
        auth.uid(), action_param, target_type_param, target_id_param, details_param
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- MAKE USER 5971f7c3-840f-4d2c-9931-db26d1978f5a SUPER ADMIN
-- =============================================

-- 1. Make user super admin
INSERT INTO admin_roles (user_id, role, permissions, granted_by, is_active)
VALUES (
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    'super_admin',
    '{
        "granted_reason": "Initial super admin setup",
        "full_access": true,
        "can_manage_admins": true,
        "can_access_all_features": true,
        "cms_access": true,
        "system_control": true
    }'::JSONB,
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    true
)
ON CONFLICT (user_id) DO UPDATE SET
    role = 'super_admin',
    permissions = '{
        "granted_reason": "Initial super admin setup",
        "full_access": true,
        "can_manage_admins": true,
        "can_access_all_features": true,
        "cms_access": true,
        "system_control": true
    }'::JSONB,
    is_active = true,
    updated_at = NOW();

-- 2. Update user profile to premium (if profiles table exists)
UPDATE profiles 
SET 
    subscription_tier = 'premium',
    subscription_start_date = NOW(),
    subscription_end_date = NOW() + INTERVAL '1 year'
WHERE id = '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID;

-- 3. Create welcome notification
INSERT INTO user_notifications (
    user_id,
    title,
    message,
    type,
    emoji,
    icon_name,
    icon_color,
    category,
    priority,
    is_pinned,
    action_url,
    action_label
) VALUES (
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    'Super Admin Access Granted! 👑',
    'Congratulations! You now have complete super admin access to CyberForce. You can manage users, content, notifications, and all system features through the admin dashboard.',
    'success',
    '👑',
    'FaCrown',
    'gold',
    'system',
    1,
    true,
    '/admin',
    'Access Admin Dashboard'
);

-- 4. Log the admin creation
INSERT INTO admin_activity_log (
    admin_user_id,
    action,
    target_type,
    target_id,
    details
) VALUES (
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    'super_admin_granted',
    'user',
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    jsonb_build_object(
        'reason', 'Initial super admin setup',
        'granted_at', NOW()::text
    )
);

-- =============================================
-- CMS CONTENT MANAGEMENT TABLES
-- =============================================

-- Content types for CMS
CREATE TABLE IF NOT EXISTS cms_content_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    fields JSONB DEFAULT '[]', -- Field definitions
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default content types
INSERT INTO cms_content_types (name, slug, description, fields) VALUES
('Challenge', 'challenge', 'Cybersecurity challenges and labs', '[
    {"name": "title", "type": "text", "required": true},
    {"name": "description", "type": "textarea", "required": true},
    {"name": "difficulty", "type": "select", "options": ["Beginner", "Intermediate", "Advanced"], "required": true},
    {"name": "category", "type": "select", "options": ["Web Security", "Network Security", "Cryptography", "System Security"], "required": true},
    {"name": "points", "type": "number", "required": true},
    {"name": "time_limit", "type": "number", "required": false},
    {"name": "instructions", "type": "rich_text", "required": true},
    {"name": "solution", "type": "rich_text", "required": false},
    {"name": "hints", "type": "array", "required": false},
    {"name": "tags", "type": "array", "required": false},
    {"name": "is_premium", "type": "boolean", "required": true}
]'::JSONB),

('Learning Path', 'learning_path', 'Structured learning paths', '[
    {"name": "title", "type": "text", "required": true},
    {"name": "description", "type": "textarea", "required": true},
    {"name": "category", "type": "select", "options": ["fundamentals", "offensive", "defensive", "general"], "required": true},
    {"name": "difficulty", "type": "select", "options": ["Beginner", "Intermediate", "Advanced"], "required": true},
    {"name": "estimated_hours", "type": "number", "required": true},
    {"name": "module_count", "type": "number", "required": true},
    {"name": "prerequisites", "type": "array", "required": false},
    {"name": "learning_objectives", "type": "array", "required": true},
    {"name": "tags", "type": "array", "required": false},
    {"name": "is_premium", "type": "boolean", "required": true}
]'::JSONB),

('Learning Module', 'learning_module', 'Individual learning modules', '[
    {"name": "title", "type": "text", "required": true},
    {"name": "description", "type": "textarea", "required": true},
    {"name": "content", "type": "rich_text", "required": true},
    {"name": "learning_path_id", "type": "relation", "relation_to": "learning_path", "required": true},
    {"name": "order_index", "type": "number", "required": true},
    {"name": "estimated_minutes", "type": "number", "required": true},
    {"name": "quiz_questions", "type": "array", "required": false},
    {"name": "resources", "type": "array", "required": false}
]'::JSONB)

ON CONFLICT (slug) DO NOTHING;

-- CMS content items
CREATE TABLE IF NOT EXISTS cms_content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_type_id UUID REFERENCES cms_content_types(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    content JSONB DEFAULT '{}', -- Flexible content based on content type
    status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'published', 'archived'
    featured BOOLEAN DEFAULT FALSE,
    seo_title VARCHAR(255),
    seo_description TEXT,
    meta_data JSONB DEFAULT '{}',
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(content_type_id, slug)
);

-- Media library
CREATE TABLE IF NOT EXISTS cms_media (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size INTEGER NOT NULL,
    file_path TEXT NOT NULL,
    alt_text TEXT,
    caption TEXT,
    metadata JSONB DEFAULT '{}',
    uploaded_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content revisions for version control
CREATE TABLE IF NOT EXISTS cms_content_revisions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_id UUID REFERENCES cms_content(id) ON DELETE CASCADE,
    revision_number INTEGER NOT NULL,
    content_snapshot JSONB NOT NULL,
    change_summary TEXT,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for CMS tables
CREATE INDEX IF NOT EXISTS idx_cms_content_type_id ON cms_content(content_type_id);
CREATE INDEX IF NOT EXISTS idx_cms_content_status ON cms_content(status);
CREATE INDEX IF NOT EXISTS idx_cms_content_created_by ON cms_content(created_by);
CREATE INDEX IF NOT EXISTS idx_cms_content_published_at ON cms_content(published_at);
CREATE INDEX IF NOT EXISTS idx_cms_media_uploaded_by ON cms_media(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_cms_content_revisions_content_id ON cms_content_revisions(content_id);

-- Enable RLS for CMS tables
ALTER TABLE cms_content_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_content_revisions ENABLE ROW LEVEL SECURITY;

-- CMS RLS Policies
CREATE POLICY "Super admins can manage all CMS content" ON cms_content
    FOR ALL USING (is_super_admin());

CREATE POLICY "Published content is viewable by all" ON cms_content
    FOR SELECT USING (status = 'published');

CREATE POLICY "Super admins can manage media" ON cms_media
    FOR ALL USING (is_super_admin());

CREATE POLICY "Super admins can view content revisions" ON cms_content_revisions
    FOR SELECT USING (is_super_admin());

-- =============================================
-- VERIFICATION AND SUCCESS MESSAGE
-- =============================================

DO $$
DECLARE
    user_role VARCHAR(50);
    perm_count INTEGER;
    user_email TEXT;
BEGIN
    -- Get user info
    SELECT ar.role, u.email INTO user_role, user_email
    FROM admin_roles ar
    JOIN auth.users u ON ar.user_id = u.id
    WHERE ar.user_id = '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID;
    
    -- Count permissions
    SELECT COUNT(*) INTO perm_count
    FROM role_permissions
    WHERE role = 'super_admin';
    
    RAISE NOTICE '🎉 SUPER ADMIN SETUP COMPLETE!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '👑 User: % (%)', COALESCE(user_email, 'Email not found'), '5971f7c3-840f-4d2c-9931-db26d1978f5a';
    RAISE NOTICE '🔑 Role: %', COALESCE(user_role, 'NOT FOUND - CHECK USER ID');
    RAISE NOTICE '⚡ Permissions: % total permissions granted', perm_count;
    RAISE NOTICE '🚀 Access: /admin dashboard now available';
    RAISE NOTICE '📧 Notification: Welcome message sent';
    RAISE NOTICE '📊 Logging: Admin activity tracking enabled';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '✅ You can now login and access /admin';
    RAISE NOTICE '✅ Full CMS and user management available';
    RAISE NOTICE '✅ All system controls accessible';
END $$;
