-- =============================================
-- CREATE STORAGE BUCKET FOR AVATARS
-- Run this in Supabase SQL Editor
-- =============================================

-- Create the avatars bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'avatars',
  'avatars', 
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
) ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- =============================================
-- STORAGE POLICIES FOR AVATARS BUCKET
-- =============================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Avatar images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatar" ON storage.objects;

-- Policy 1: Public read access for avatar images
CREATE POLICY "Avatar images are publicly accessible"
ON storage.objects FOR SELECT
USING (bucket_id = 'avatars');

-- Policy 2: Users can upload their own avatars
CREATE POLICY "Users can upload their own avatar"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy 3: Users can update their own avatars
CREATE POLICY "Users can update their own avatar"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy 4: Users can delete their own avatars
CREATE POLICY "Users can delete their own avatar"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- =============================================
-- VERIFY BUCKET CREATION
-- =============================================
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'avatars') THEN
        RAISE NOTICE '✅ Avatars bucket created successfully';
        RAISE NOTICE 'Bucket ID: avatars';
        RAISE NOTICE 'Public: true';
        RAISE NOTICE 'File size limit: 5MB';
        RAISE NOTICE 'Allowed types: JPEG, PNG, GIF, WebP';
    ELSE
        RAISE NOTICE '❌ Failed to create avatars bucket';
    END IF;
END $$;

-- =============================================
-- VERIFY POLICIES
-- =============================================
DO $$ 
DECLARE
    policy_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO policy_count 
    FROM pg_policies 
    WHERE schemaname = 'storage' 
    AND tablename = 'objects' 
    AND policyname LIKE '%avatar%';
    
    IF policy_count >= 4 THEN
        RAISE NOTICE '✅ Avatar storage policies created successfully';
        RAISE NOTICE 'Policies created: %', policy_count;
    ELSE
        RAISE NOTICE '❌ Some avatar storage policies may be missing';
        RAISE NOTICE 'Policies found: %', policy_count;
    END IF;
END $$;

-- =============================================
-- HELPER FUNCTION FOR BUCKET CREATION
-- =============================================

-- Create a function that can be called from the app
CREATE OR REPLACE FUNCTION create_avatar_bucket()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Insert bucket if it doesn't exist
    INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
    VALUES (
      'avatars',
      'avatars',
      true,
      5242880, -- 5MB limit
      ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    ) ON CONFLICT (id) DO NOTHING;

    RETURN true;
EXCEPTION
    WHEN OTHERS THEN
        RETURN false;
END;
$$;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== AVATAR STORAGE SETUP COMPLETE ===';
    RAISE NOTICE 'You can now upload avatars to the /avatars bucket';
    RAISE NOTICE 'Users can only upload to their own folder (user_id)';
    RAISE NOTICE 'All avatar images are publicly accessible';
    RAISE NOTICE 'Helper function create_avatar_bucket() is available';
    RAISE NOTICE '';
END $$;
