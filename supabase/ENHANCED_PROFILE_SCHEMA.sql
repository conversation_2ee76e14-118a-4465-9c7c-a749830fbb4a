-- =============================================
-- ENHANCED PROFILE DATABASE SCHEMA
-- Complete schema for user profiles with avatar uploads,
-- settings, social links, and comprehensive user data
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- 1. ENHANCED USER PROFILES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.user_profiles (
    -- Primary identifiers
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- For compatibility
    
    -- Basic profile information
    username TEXT UNIQUE,
    full_name TEXT,
    display_name TEXT,
    bio TEXT,
    avatar_url TEXT,
    cover_image_url TEXT,
    
    -- Contact information
    email TEXT,
    phone_number TEXT,
    location TEXT,
    country TEXT,
    timezone TEXT,
    website TEXT,
    
    -- Social media links
    linkedin_url TEXT,
    github_url TEXT,
    twitter_url TEXT,
    instagram_url TEXT,
    facebook_url TEXT,
    
    -- Subscription and account status
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium')),
    subscription_status TEXT DEFAULT 'active',
    subscription_start_date TIMESTAMP WITH TIME ZONE,
    subscription_end_date TIMESTAMP WITH TIME ZONE,
    
    -- Gamification and progress
    total_xp INTEGER DEFAULT 0,
    current_level INTEGER DEFAULT 1,
    total_points INTEGER DEFAULT 0,
    total_coins INTEGER DEFAULT 100,
    coins INTEGER DEFAULT 100,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    modules_completed INTEGER DEFAULT 0,
    challenges_completed INTEGER DEFAULT 0,
    achievements_unlocked INTEGER DEFAULT 0,
    
    -- User preferences
    preferred_language TEXT DEFAULT 'en',
    theme_preference TEXT DEFAULT 'system' CHECK (theme_preference IN ('light', 'dark', 'system')),
    email_notifications BOOLEAN DEFAULT TRUE,
    push_notifications BOOLEAN DEFAULT TRUE,
    marketing_emails BOOLEAN DEFAULT FALSE,
    
    -- Privacy settings
    profile_visibility TEXT DEFAULT 'public' CHECK (profile_visibility IN ('public', 'private', 'friends')),
    show_email BOOLEAN DEFAULT FALSE,
    show_phone BOOLEAN DEFAULT FALSE,
    show_location BOOLEAN DEFAULT TRUE,
    show_social_links BOOLEAN DEFAULT TRUE,
    
    -- Account security
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    last_password_change TIMESTAMP WITH TIME ZONE,
    login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP WITH TIME ZONE,
    
    -- Admin and roles
    is_admin BOOLEAN DEFAULT FALSE,
    role TEXT DEFAULT 'user',
    permissions TEXT[] DEFAULT '{}',
    
    -- Activity tracking
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    
    -- Profile completion
    profile_completion_percentage INTEGER DEFAULT 0,
    onboarding_completed BOOLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id),
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' OR email IS NULL),
    CONSTRAINT valid_phone CHECK (phone_number ~* '^\+?[1-9]\d{1,14}$' OR phone_number IS NULL)
);

-- =============================================
-- 2. USER SETTINGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Notification settings
    email_notifications JSONB DEFAULT '{"achievements": true, "challenges": true, "updates": true, "marketing": false}'::jsonb,
    push_notifications JSONB DEFAULT '{"achievements": true, "challenges": true, "reminders": true}'::jsonb,
    
    -- Privacy settings
    privacy_settings JSONB DEFAULT '{"profile_visibility": "public", "show_progress": true, "show_achievements": true}'::jsonb,
    
    -- Learning preferences
    learning_preferences JSONB DEFAULT '{"difficulty": "intermediate", "pace": "normal", "topics": []}'::jsonb,
    
    -- UI preferences
    ui_preferences JSONB DEFAULT '{"theme": "system", "language": "en", "sidebar_collapsed": false}'::jsonb,
    
    -- Security settings
    security_settings JSONB DEFAULT '{"two_factor": false, "login_alerts": true, "session_timeout": 30}'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- =============================================
-- 3. AVATAR UPLOADS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.user_avatars (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- File information
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    file_type TEXT,
    
    -- Image metadata
    width INTEGER,
    height INTEGER,
    is_current BOOLEAN DEFAULT FALSE,
    
    -- Storage information
    storage_bucket TEXT DEFAULT 'avatars',
    storage_path TEXT,
    public_url TEXT,
    
    -- Upload tracking
    upload_status TEXT DEFAULT 'pending' CHECK (upload_status IN ('pending', 'processing', 'completed', 'failed')),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 4. USER ACTIVITY LOG
-- =============================================
CREATE TABLE IF NOT EXISTS public.user_activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Activity details
    activity_type TEXT NOT NULL,
    activity_description TEXT,
    activity_data JSONB,
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    device_info JSONB,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 5. INDEXES FOR PERFORMANCE
-- =============================================
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON public.user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription_tier ON public.user_profiles(subscription_tier);
CREATE INDEX IF NOT EXISTS idx_user_profiles_last_activity ON public.user_profiles(last_activity);

CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON public.user_settings(user_id);

CREATE INDEX IF NOT EXISTS idx_user_avatars_user_id ON public.user_avatars(user_id);
CREATE INDEX IF NOT EXISTS idx_user_avatars_is_current ON public.user_avatars(user_id, is_current) WHERE is_current = TRUE;

CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON public.user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_created_at ON public.user_activity_log(created_at);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_activity_type ON public.user_activity_log(activity_type);

-- =============================================
-- 6. ROW LEVEL SECURITY (RLS)
-- =============================================
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_avatars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_activity_log ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view their own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Public profiles can be viewed by anyone (only if profile_visibility column exists)
-- This policy will be created after the table is fully set up
-- CREATE POLICY "Public profiles are viewable by everyone" ON public.user_profiles
--     FOR SELECT USING (profile_visibility = 'public');

-- User settings policies
CREATE POLICY "Users can manage their own settings" ON public.user_settings
    FOR ALL USING (auth.uid() = user_id);

-- User avatars policies
CREATE POLICY "Users can manage their own avatars" ON public.user_avatars
    FOR ALL USING (auth.uid() = user_id);

-- User activity log policies
CREATE POLICY "Users can view their own activity" ON public.user_activity_log
    FOR SELECT USING (auth.uid() = user_id);

-- =============================================
-- 7. TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON public.user_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_avatars_updated_at BEFORE UPDATE ON public.user_avatars
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- 8. STORAGE BUCKET FOR AVATARS
-- =============================================

-- Create storage bucket for avatars (run this in Supabase dashboard or via API)
-- INSERT INTO storage.buckets (id, name, public) VALUES ('avatars', 'avatars', true);

-- Storage policies for avatars bucket
-- CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
--     FOR SELECT USING (bucket_id = 'avatars');

-- CREATE POLICY "Users can upload their own avatar" ON storage.objects
--     FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

-- CREATE POLICY "Users can update their own avatar" ON storage.objects
--     FOR UPDATE USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

-- CREATE POLICY "Users can delete their own avatar" ON storage.objects
--     FOR DELETE USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

-- =============================================
-- 9. INITIAL DATA AND FUNCTIONS
-- =============================================

-- Function to calculate profile completion percentage
CREATE OR REPLACE FUNCTION calculate_profile_completion(user_profile_id UUID)
RETURNS INTEGER AS $$
DECLARE
    completion_score INTEGER := 0;
    profile_record RECORD;
BEGIN
    SELECT * INTO profile_record FROM public.user_profiles WHERE id = user_profile_id;
    
    IF profile_record IS NULL THEN
        RETURN 0;
    END IF;
    
    -- Basic info (40 points)
    IF profile_record.full_name IS NOT NULL AND profile_record.full_name != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF profile_record.bio IS NOT NULL AND profile_record.bio != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF profile_record.avatar_url IS NOT NULL AND profile_record.avatar_url != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    IF profile_record.location IS NOT NULL AND profile_record.location != '' THEN
        completion_score := completion_score + 10;
    END IF;
    
    -- Contact info (30 points)
    IF profile_record.email IS NOT NULL AND profile_record.email != '' THEN
        completion_score := completion_score + 15;
    END IF;
    
    IF profile_record.phone_number IS NOT NULL AND profile_record.phone_number != '' THEN
        completion_score := completion_score + 15;
    END IF;
    
    -- Social links (20 points)
    IF profile_record.linkedin_url IS NOT NULL AND profile_record.linkedin_url != '' THEN
        completion_score := completion_score + 5;
    END IF;
    
    IF profile_record.github_url IS NOT NULL AND profile_record.github_url != '' THEN
        completion_score := completion_score + 5;
    END IF;
    
    IF profile_record.twitter_url IS NOT NULL AND profile_record.twitter_url != '' THEN
        completion_score := completion_score + 5;
    END IF;
    
    IF profile_record.website IS NOT NULL AND profile_record.website != '' THEN
        completion_score := completion_score + 5;
    END IF;
    
    -- Verification (10 points)
    IF profile_record.email_verified = TRUE THEN
        completion_score := completion_score + 5;
    END IF;
    
    IF profile_record.phone_verified = TRUE THEN
        completion_score := completion_score + 5;
    END IF;
    
    RETURN completion_score;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update profile completion percentage
CREATE OR REPLACE FUNCTION update_profile_completion()
RETURNS TRIGGER AS $$
BEGIN
    NEW.profile_completion_percentage := calculate_profile_completion(NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_profile_completion_trigger
    BEFORE INSERT OR UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_profile_completion();

-- =============================================
-- 10. SAMPLE DATA MIGRATION (OPTIONAL)
-- =============================================

-- Function to migrate existing user data
CREATE OR REPLACE FUNCTION migrate_existing_users()
RETURNS VOID AS $$
BEGIN
    -- Insert default settings for existing users who don't have settings
    INSERT INTO public.user_settings (user_id)
    SELECT id FROM auth.users 
    WHERE id NOT IN (SELECT user_id FROM public.user_settings WHERE user_id IS NOT NULL);
    
    -- Update profile completion for existing profiles
    UPDATE public.user_profiles 
    SET profile_completion_percentage = calculate_profile_completion(id)
    WHERE profile_completion_percentage = 0 OR profile_completion_percentage IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Run migration (uncomment to execute)
-- SELECT migrate_existing_users();

-- =============================================
-- 11. ADDITIONAL POLICIES (RUN AFTER TABLE CREATION)
-- =============================================

-- Create the public profile policy after table exists
-- Run this separately if needed:
-- CREATE POLICY "Public profiles are viewable by everyone" ON public.user_profiles
--     FOR SELECT USING (profile_visibility = 'public');

-- =============================================
-- END OF SCHEMA
-- =============================================
