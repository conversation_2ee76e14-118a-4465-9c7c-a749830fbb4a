-- =============================================
-- COMPLETE PRODUCTION SCHEMA FOR CYBERFORCE
-- =============================================
-- This creates the complete system without duplicate errors
-- Run this ONCE to set up everything
-- =============================================

-- Drop existing policies and tables if they exist to avoid conflicts
DROP POLICY IF EXISTS "Super admins can view all admin roles" ON admin_roles;
DROP POLICY IF EXISTS "Public can view published content" ON cms_content;
DROP TABLE IF EXISTS notification_templates CASCADE;

-- 1. ADMIN SYSTEM
-- =============================================

-- Admin roles table
CREATE TABLE IF NOT EXISTS admin_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    permissions JSONB DEFAULT '{}',
    granted_by UUID REFERENCES auth.users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admin permissions
CREATE TABLE IF NOT EXISTS admin_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Role permissions junction
CREATE TABLE IF NOT EXISTS role_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    role VARCHAR(50) NOT NULL,
    permission_name VARCHAR(100) REFERENCES admin_permissions(permission_name),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role, permission_name)
);

-- Admin activity log
CREATE TABLE IF NOT EXISTS admin_activity_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_user_id UUID REFERENCES auth.users(id),
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. USER PROGRESS AND ENROLLMENT SYSTEM
-- =============================================

-- User enrollments
CREATE TABLE IF NOT EXISTS user_enrollments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content_type VARCHAR(50) NOT NULL, -- 'learning_path', 'challenge'
    content_id VARCHAR(100) NOT NULL,
    enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    progress_percentage INTEGER DEFAULT 0,
    current_module_index INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'enrolled' CHECK (status IN ('enrolled', 'in_progress', 'completed', 'paused')),
    metadata JSONB DEFAULT '{}',
    UNIQUE(user_id, content_type, content_id)
);

-- User progress tracking
CREATE TABLE IF NOT EXISTS user_progress (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content_type VARCHAR(50) NOT NULL,
    content_id VARCHAR(100) NOT NULL,
    module_id VARCHAR(100),
    progress_data JSONB DEFAULT '{}',
    completed BOOLEAN DEFAULT FALSE,
    score INTEGER,
    time_spent INTEGER DEFAULT 0, -- in minutes
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User achievements
CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_type VARCHAR(50) NOT NULL,
    achievement_id VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    points INTEGER DEFAULT 0,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- User statistics
CREATE TABLE IF NOT EXISTS user_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    total_challenges_completed INTEGER DEFAULT 0,
    total_learning_paths_completed INTEGER DEFAULT 0,
    total_modules_completed INTEGER DEFAULT 0,
    total_points INTEGER DEFAULT 0,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    total_time_spent INTEGER DEFAULT 0, -- in minutes
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. CONTENT MANAGEMENT SYSTEM
-- =============================================

-- Content types
CREATE TABLE IF NOT EXISTS cms_content_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    schema_definition JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Main content table
CREATE TABLE IF NOT EXISTS cms_content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_type_id UUID REFERENCES cms_content_types(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    content JSONB NOT NULL DEFAULT '{}',
    meta_data JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    featured BOOLEAN DEFAULT FALSE,
    order_index INTEGER DEFAULT 0,
    seo_title VARCHAR(255),
    seo_description TEXT,
    seo_keywords TEXT[],
    published_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(content_type_id, slug)
);

-- Categories
CREATE TABLE IF NOT EXISTS cms_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES cms_categories(id),
    color VARCHAR(7) DEFAULT '#3B82F6',
    icon VARCHAR(50),
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Media management
CREATE TABLE IF NOT EXISTS cms_media (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path TEXT NOT NULL,
    alt_text TEXT,
    caption TEXT,
    metadata JSONB DEFAULT '{}',
    uploaded_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. NOTIFICATION SYSTEM
-- =============================================

-- Enhanced notifications
CREATE TABLE IF NOT EXISTS user_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info',
    emoji VARCHAR(10),
    icon_name VARCHAR(50),
    icon_color VARCHAR(50),
    category VARCHAR(50) DEFAULT 'general',
    priority INTEGER DEFAULT 2,
    is_read BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    action_label VARCHAR(100),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Notification templates (recreated without conflicts)
CREATE TABLE notification_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info',
    emoji VARCHAR(10),
    icon_name VARCHAR(50),
    icon_color VARCHAR(50),
    category VARCHAR(50) DEFAULT 'general',
    variables JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. LEADERBOARD SYSTEM
-- =============================================

-- Leaderboard entries
CREATE TABLE IF NOT EXISTS leaderboard_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL, -- 'overall', 'challenges', 'learning_paths', 'monthly'
    points INTEGER DEFAULT 0,
    rank_position INTEGER,
    period_start DATE,
    period_end DATE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, category, period_start, period_end)
);

-- 6. CERTIFICATES SYSTEM
-- =============================================

-- User certificates
CREATE TABLE IF NOT EXISTS user_certificates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    certificate_type VARCHAR(50) NOT NULL,
    content_id VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    issued_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    certificate_url TEXT,
    verification_code VARCHAR(100) UNIQUE,
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE
);

-- 7. SYSTEM ANALYTICS
-- =============================================

-- Daily analytics
CREATE TABLE IF NOT EXISTS daily_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL UNIQUE,
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    premium_users INTEGER DEFAULT 0,
    challenges_completed INTEGER DEFAULT 0,
    learning_paths_started INTEGER DEFAULT 0,
    total_time_spent INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User sessions
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_end TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    pages_visited INTEGER DEFAULT 0,
    actions_performed INTEGER DEFAULT 0,
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(50),
    browser VARCHAR(50),
    os VARCHAR(50),
    country VARCHAR(100),
    city VARCHAR(100)
);

-- 8. CREATE INDEXES FOR PERFORMANCE
-- =============================================

CREATE INDEX IF NOT EXISTS idx_admin_roles_user_id ON admin_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_log_admin_user ON admin_activity_log(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_user_enrollments_user_id ON user_enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_enrollments_content ON user_enrollments(content_type, content_id);
CREATE INDEX IF NOT EXISTS idx_user_progress_user_id ON user_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_statistics_user_id ON user_statistics(user_id);
CREATE INDEX IF NOT EXISTS idx_cms_content_type_id ON cms_content(content_type_id);
CREATE INDEX IF NOT EXISTS idx_cms_content_status ON cms_content(status);
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_id ON user_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_read ON user_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_leaderboard_entries_category ON leaderboard_entries(category);
CREATE INDEX IF NOT EXISTS idx_user_certificates_user_id ON user_certificates(user_id);
CREATE INDEX IF NOT EXISTS idx_daily_analytics_date ON daily_analytics(date);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);

-- 9. ENABLE ROW LEVEL SECURITY
-- =============================================

ALTER TABLE admin_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_statistics ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_content_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboard_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- 10. CREATE RLS POLICIES
-- =============================================

-- Admin policies
CREATE POLICY "Super admins can view all admin roles" ON admin_roles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM admin_roles ar
            WHERE ar.user_id = auth.uid()
            AND ar.role = 'super_admin'
            AND ar.is_active = true
        )
    );

CREATE POLICY "Users can view their own admin role" ON admin_roles
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Super admins can manage admin roles" ON admin_roles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_roles ar
            WHERE ar.user_id = auth.uid()
            AND ar.role = 'super_admin'
            AND ar.is_active = true
        )
    );

-- User data policies
CREATE POLICY "Users can view their own enrollments" ON user_enrollments
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own enrollments" ON user_enrollments
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view their own progress" ON user_progress
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own progress" ON user_progress
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view their own achievements" ON user_achievements
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can view their own statistics" ON user_statistics
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own statistics" ON user_statistics
    FOR ALL USING (user_id = auth.uid());

-- Content policies
CREATE POLICY "Public can view published content" ON cms_content
    FOR SELECT USING (status = 'published');

CREATE POLICY "Admins can manage all content" ON cms_content
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_roles ar
            WHERE ar.user_id = auth.uid()
            AND ar.is_active = true
        )
    );

-- Notification policies
CREATE POLICY "Users can view their own notifications" ON user_notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications" ON user_notifications
    FOR UPDATE USING (user_id = auth.uid());

-- Leaderboard policies
CREATE POLICY "Public can view leaderboard" ON leaderboard_entries
    FOR SELECT USING (true);

-- Certificate policies
CREATE POLICY "Users can view their own certificates" ON user_certificates
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Public can verify certificates" ON user_certificates
    FOR SELECT USING (is_active = true);

-- 11. CREATE UTILITY FUNCTIONS
-- =============================================

-- Check if user is super admin
CREATE OR REPLACE FUNCTION is_super_admin(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM admin_roles
        WHERE user_id = user_uuid
        AND role = 'super_admin'
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Check if user has permission
CREATE OR REPLACE FUNCTION has_permission(user_uuid UUID, permission_name_param VARCHAR(100))
RETURNS BOOLEAN AS $$
DECLARE
    user_role VARCHAR(50);
BEGIN
    SELECT role INTO user_role
    FROM admin_roles
    WHERE user_id = user_uuid AND is_active = true;

    IF user_role IS NULL THEN
        RETURN FALSE;
    END IF;

    RETURN EXISTS (
        SELECT 1 FROM role_permissions
        WHERE role = user_role AND permission_name = permission_name_param
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Log admin activity
CREATE OR REPLACE FUNCTION log_admin_activity(
    action_param VARCHAR(100),
    target_type_param VARCHAR(50) DEFAULT NULL,
    target_id_param UUID DEFAULT NULL,
    details_param JSONB DEFAULT '{}'::JSONB
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO admin_activity_log (
        admin_user_id, action, target_type, target_id, details
    ) VALUES (
        auth.uid(), action_param, target_type_param, target_id_param, details_param
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enroll user in content
CREATE OR REPLACE FUNCTION enroll_user_in_content(
    user_uuid UUID,
    content_type_param VARCHAR(50),
    content_id_param VARCHAR(100)
)
RETURNS UUID AS $$
DECLARE
    enrollment_id UUID;
BEGIN
    INSERT INTO user_enrollments (
        user_id, content_type, content_id, status
    ) VALUES (
        user_uuid, content_type_param, content_id_param, 'enrolled'
    )
    ON CONFLICT (user_id, content_type, content_id)
    DO UPDATE SET
        enrolled_at = NOW(),
        status = CASE
            WHEN user_enrollments.status = 'completed' THEN 'completed'
            ELSE 'enrolled'
        END
    RETURNING id INTO enrollment_id;

    -- Initialize user statistics if not exists
    INSERT INTO user_statistics (user_id)
    VALUES (user_uuid)
    ON CONFLICT (user_id) DO NOTHING;

    RETURN enrollment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update user progress
CREATE OR REPLACE FUNCTION update_user_progress(
    user_uuid UUID,
    content_type_param VARCHAR(50),
    content_id_param VARCHAR(100),
    module_id_param VARCHAR(100) DEFAULT NULL,
    progress_data_param JSONB DEFAULT '{}'::JSONB,
    completed_param BOOLEAN DEFAULT FALSE,
    score_param INTEGER DEFAULT NULL,
    time_spent_param INTEGER DEFAULT 0
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO user_progress (
        user_id, content_type, content_id, module_id,
        progress_data, completed, score, time_spent
    ) VALUES (
        user_uuid, content_type_param, content_id_param, module_id_param,
        progress_data_param, completed_param, score_param, time_spent_param
    )
    ON CONFLICT (user_id, content_type, content_id, module_id)
    DO UPDATE SET
        progress_data = progress_data_param,
        completed = completed_param,
        score = COALESCE(score_param, user_progress.score),
        time_spent = user_progress.time_spent + time_spent_param,
        last_accessed = NOW(),
        updated_at = NOW();

    -- Update enrollment progress
    UPDATE user_enrollments
    SET
        progress_percentage = CASE
            WHEN completed_param THEN 100
            ELSE LEAST(progress_percentage + 10, 90)
        END,
        status = CASE
            WHEN completed_param THEN 'completed'
            WHEN status = 'enrolled' THEN 'in_progress'
            ELSE status
        END,
        started_at = COALESCE(started_at, NOW()),
        completed_at = CASE WHEN completed_param THEN NOW() ELSE completed_at END
    WHERE user_id = user_uuid
    AND content_type = content_type_param
    AND content_id = content_id_param;

    -- Update user statistics
    UPDATE user_statistics
    SET
        total_time_spent = total_time_spent + time_spent_param,
        last_activity = NOW(),
        updated_at = NOW()
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Award achievement
CREATE OR REPLACE FUNCTION award_achievement(
    user_uuid UUID,
    achievement_type_param VARCHAR(50),
    achievement_id_param VARCHAR(100),
    title_param VARCHAR(255),
    description_param TEXT DEFAULT NULL,
    icon_param VARCHAR(50) DEFAULT 'FaTrophy',
    points_param INTEGER DEFAULT 100
)
RETURNS UUID AS $$
DECLARE
    achievement_uuid UUID;
BEGIN
    INSERT INTO user_achievements (
        user_id, achievement_type, achievement_id, title,
        description, icon, points
    ) VALUES (
        user_uuid, achievement_type_param, achievement_id_param,
        title_param, description_param, icon_param, points_param
    )
    ON CONFLICT (user_id, achievement_type, achievement_id) DO NOTHING
    RETURNING id INTO achievement_uuid;

    -- Update user statistics
    UPDATE user_statistics
    SET
        total_points = total_points + points_param,
        updated_at = NOW()
    WHERE user_id = user_uuid;

    RETURN achievement_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Get user dashboard data
CREATE OR REPLACE FUNCTION get_user_dashboard_data(user_uuid UUID)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    stats RECORD;
    enrollments JSONB;
    achievements JSONB;
    recent_progress JSONB;
BEGIN
    -- Get user statistics
    SELECT * INTO stats FROM user_statistics WHERE user_id = user_uuid;

    -- Get enrollments
    SELECT jsonb_agg(
        jsonb_build_object(
            'id', id,
            'content_type', content_type,
            'content_id', content_id,
            'progress_percentage', progress_percentage,
            'status', status,
            'enrolled_at', enrolled_at
        )
    ) INTO enrollments
    FROM user_enrollments
    WHERE user_id = user_uuid
    ORDER BY enrolled_at DESC
    LIMIT 10;

    -- Get recent achievements
    SELECT jsonb_agg(
        jsonb_build_object(
            'id', id,
            'title', title,
            'description', description,
            'icon', icon,
            'points', points,
            'earned_at', earned_at
        )
    ) INTO achievements
    FROM user_achievements
    WHERE user_id = user_uuid
    ORDER BY earned_at DESC
    LIMIT 5;

    -- Get recent progress
    SELECT jsonb_agg(
        jsonb_build_object(
            'content_type', content_type,
            'content_id', content_id,
            'module_id', module_id,
            'completed', completed,
            'score', score,
            'last_accessed', last_accessed
        )
    ) INTO recent_progress
    FROM user_progress
    WHERE user_id = user_uuid
    ORDER BY last_accessed DESC
    LIMIT 10;

    -- Build result
    result := jsonb_build_object(
        'statistics', row_to_json(stats),
        'enrollments', COALESCE(enrollments, '[]'::jsonb),
        'achievements', COALESCE(achievements, '[]'::jsonb),
        'recent_progress', COALESCE(recent_progress, '[]'::jsonb)
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. INSERT DEFAULT DATA
-- =============================================

-- Insert admin permissions
INSERT INTO admin_permissions (permission_name, description, category) VALUES
('users.view', 'View user profiles and data', 'user_management'),
('users.edit', 'Edit user profiles and settings', 'user_management'),
('users.delete', 'Delete user accounts', 'user_management'),
('users.ban', 'Ban/unban users', 'user_management'),
('users.subscription.change', 'Change user subscription levels', 'user_management'),
('content.create', 'Create new content', 'content_management'),
('content.edit', 'Edit existing content', 'content_management'),
('content.delete', 'Delete content', 'content_management'),
('content.publish', 'Publish/unpublish content', 'content_management'),
('notifications.send', 'Send notifications to users', 'notification_management'),
('notifications.broadcast', 'Send broadcast notifications', 'notification_management'),
('system.analytics.view', 'View system analytics', 'system_management'),
('system.logs.view', 'View system logs', 'system_management'),
('admin.roles.assign', 'Assign admin roles to users', 'admin_management'),
('admin.super_admin.access', 'Access super admin dashboard', 'admin_management')
ON CONFLICT (permission_name) DO NOTHING;

-- Assign ALL permissions to super_admin role
INSERT INTO role_permissions (role, permission_name)
SELECT 'super_admin', permission_name FROM admin_permissions
ON CONFLICT (role, permission_name) DO NOTHING;

-- Insert content types
INSERT INTO cms_content_types (name, slug, description) VALUES
('Challenge', 'challenge', 'Cybersecurity challenges and exercises'),
('Learning Path', 'learning_path', 'Structured learning paths with multiple modules'),
('Learning Module', 'learning_module', 'Individual learning modules within paths'),
('Blog Post', 'blog_post', 'Blog posts and articles')
ON CONFLICT (slug) DO NOTHING;

-- Insert categories
INSERT INTO cms_categories (name, slug, description, color, icon) VALUES
('Network Fundamentals', 'network-fundamentals', 'Network security fundamentals', '#3B82F6', 'FaNetworkWired'),
('Operating Systems', 'operating-systems', 'Operating system security', '#10B981', 'FaServer'),
('Blue Teaming', 'blue-teaming', 'Defensive security and blue team operations', '#2563EB', 'FaShieldAlt'),
('Red Teaming', 'red-teaming', 'Offensive security and red team operations', '#DC2626', 'FaUserSecret'),
('Web Security', 'web-security', 'Web application security', '#EF4444', 'FaGlobe'),
('Cryptography', 'cryptography', 'Cryptographic challenges and learning', '#8B5CF6', 'FaLock'),
('Forensics', 'forensics', 'Digital forensics and incident response', '#059669', 'FaSearch'),
('OSINT', 'osint', 'Open Source Intelligence gathering', '#6366F1', 'FaEye')
ON CONFLICT (slug) DO NOTHING;

-- Insert notification templates
INSERT INTO notification_templates (name, title, message, type, emoji, icon_name, category) VALUES
('welcome', 'Welcome to CyberForce! 🎉', 'Start your cybersecurity journey with our personalized learning paths.', 'success', '🎉', 'FaRocket', 'system'),
('challenge_completed', 'Challenge Completed! 🏆', 'Congratulations! You have successfully completed the {challenge_name} challenge.', 'achievement', '🏆', 'FaTrophy', 'achievement'),
('learning_path_started', 'Learning Path Started! 📚', 'You have enrolled in {learning_path_name}. Good luck on your learning journey!', 'info', '📚', 'FaGraduationCap', 'learning'),
('achievement_unlocked', 'Achievement Unlocked! ⭐', 'You have unlocked the {achievement_name} achievement!', 'achievement', '⭐', 'FaStar', 'achievement')
ON CONFLICT (name) DO NOTHING;

-- 13. MAKE USER SUPER ADMIN
-- =============================================

-- Make user 5971f7c3-840f-4d2c-9931-db26d1978f5a super admin
INSERT INTO admin_roles (user_id, role, permissions, granted_by, is_active)
VALUES (
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    'super_admin',
    jsonb_build_object(
        'granted_reason', 'Initial super admin setup',
        'full_access', true,
        'can_manage_admins', true,
        'can_access_all_features', true,
        'cms_access', true,
        'system_control', true
    ),
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    true
)
ON CONFLICT (user_id) DO UPDATE SET
    role = 'super_admin',
    permissions = jsonb_build_object(
        'granted_reason', 'Initial super admin setup',
        'full_access', true,
        'can_manage_admins', true,
        'can_access_all_features', true,
        'cms_access', true,
        'system_control', true
    ),
    is_active = true,
    updated_at = NOW();

-- Update user profile to premium
UPDATE profiles
SET
    subscription_tier = 'premium',
    subscription_start_date = NOW(),
    subscription_end_date = NOW() + INTERVAL '1 year'
WHERE id = '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID;

-- Initialize user statistics
INSERT INTO user_statistics (user_id)
VALUES ('5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID)
ON CONFLICT (user_id) DO NOTHING;

-- Create welcome notification
INSERT INTO user_notifications (
    user_id, title, message, type, emoji, icon_name, icon_color,
    category, priority, is_pinned, action_url, action_label
) VALUES (
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    'Super Admin Access Granted! 👑',
    'Congratulations! You now have complete super admin access to CyberForce. You can manage users, content, notifications, and all system features through the admin dashboard.',
    'success',
    '👑',
    'FaCrown',
    'gold',
    'system',
    1,
    true,
    '/admin',
    'Access Admin Dashboard'
)
ON CONFLICT DO NOTHING;

-- Log the admin creation
INSERT INTO admin_activity_log (
    admin_user_id, action, target_type, target_id, details
) VALUES (
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    'super_admin_granted',
    'user',
    '5971f7c3-840f-4d2c-9931-db26d1978f5a'::UUID,
    jsonb_build_object(
        'reason', 'Initial super admin setup',
        'granted_at', NOW()::text
    )
);

-- 14. SUCCESS MESSAGE
-- =============================================

DO $$
BEGIN
    RAISE NOTICE '🎉 COMPLETE PRODUCTION SCHEMA SETUP SUCCESSFUL!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '✅ Admin System: Complete with roles and permissions';
    RAISE NOTICE '✅ User Progress: Enrollment and tracking system ready';
    RAISE NOTICE '✅ CMS System: Content management with categories';
    RAISE NOTICE '✅ Notification System: Templates and delivery ready';
    RAISE NOTICE '✅ Leaderboard: Ranking and competition system';
    RAISE NOTICE '✅ Certificates: Achievement and verification system';
    RAISE NOTICE '✅ Analytics: Daily tracking and user sessions';
    RAISE NOTICE '✅ Super Admin: User 5971f7c3-840f-4d2c-9931-db26d1978f5a granted access';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '🚀 Your complete production system is ready!';
    RAISE NOTICE '📱 Access: /admin dashboard now available';
    RAISE NOTICE '📊 Features: All real functionality enabled';
END $$;
