-- =============================================
-- SESSION MANAGEMENT AND COOKIE TRACKING SCHEMA
-- Complete session management with device tracking and cookies
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- 1. USER SESSIONS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE NOT NULL,
    refresh_token TEXT,
    
    -- Device information
    device_id TEXT,
    device_name TEXT,
    device_type TEXT, -- 'desktop', 'mobile', 'tablet'
    browser_name TEXT,
    browser_version TEXT,
    operating_system TEXT,
    user_agent TEXT,
    
    -- Location information
    ip_address INET,
    country TEXT,
    city TEXT,
    timezone TEXT,
    
    -- Session status
    is_active BOOLEAN DEFAULT TRUE,
    is_primary BOOLEAN DEFAULT FALSE, -- Only one primary session per user
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Security
    login_method TEXT DEFAULT 'email', -- 'email', 'google', 'github', etc.
    two_factor_verified BOOLEAN DEFAULT FALSE,
    suspicious_activity BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_device_type CHECK (device_type IN ('desktop', 'mobile', 'tablet', 'unknown'))
);

-- =============================================
-- 2. USER COOKIES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.user_cookies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES public.user_sessions(id) ON DELETE CASCADE,
    
    -- Cookie information
    cookie_name TEXT NOT NULL,
    cookie_value TEXT NOT NULL,
    cookie_domain TEXT,
    cookie_path TEXT DEFAULT '/',
    
    -- Cookie settings
    is_secure BOOLEAN DEFAULT TRUE,
    is_http_only BOOLEAN DEFAULT TRUE,
    same_site TEXT DEFAULT 'strict', -- 'strict', 'lax', 'none'
    
    -- Cookie lifecycle
    expires_at TIMESTAMP WITH TIME ZONE,
    is_persistent BOOLEAN DEFAULT FALSE,
    is_essential BOOLEAN DEFAULT TRUE,
    
    -- Cookie categories
    category TEXT DEFAULT 'essential', -- 'essential', 'functional', 'analytics', 'marketing'
    purpose TEXT,
    
    -- User consent
    consent_given BOOLEAN DEFAULT FALSE,
    consent_date TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_same_site CHECK (same_site IN ('strict', 'lax', 'none')),
    CONSTRAINT valid_category CHECK (category IN ('essential', 'functional', 'analytics', 'marketing'))
);

-- =============================================
-- 3. LOGIN HISTORY TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.login_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES public.user_sessions(id) ON DELETE SET NULL,
    
    -- Login details
    login_type TEXT NOT NULL, -- 'login', 'logout', 'session_expired', 'forced_logout'
    login_method TEXT, -- 'email', 'google', 'github', etc.
    success BOOLEAN NOT NULL,
    failure_reason TEXT,
    
    -- Device and location
    ip_address INET,
    user_agent TEXT,
    device_fingerprint TEXT,
    country TEXT,
    city TEXT,
    
    -- Security flags
    is_suspicious BOOLEAN DEFAULT FALSE,
    risk_score INTEGER DEFAULT 0, -- 0-100 risk score
    blocked BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_login_type CHECK (login_type IN ('login', 'logout', 'session_expired', 'forced_logout')),
    CONSTRAINT valid_risk_score CHECK (risk_score >= 0 AND risk_score <= 100)
);

-- =============================================
-- 4. EMAIL VERIFICATION REQUESTS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.email_verification_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Email change details
    current_email TEXT NOT NULL,
    new_email TEXT NOT NULL,
    verification_token TEXT UNIQUE NOT NULL,
    
    -- Request status
    status TEXT DEFAULT 'pending', -- 'pending', 'verified', 'expired', 'cancelled'
    verified_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    
    -- Security
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_status CHECK (status IN ('pending', 'verified', 'expired', 'cancelled')),
    CONSTRAINT valid_emails CHECK (current_email != new_email)
);

-- =============================================
-- 5. USERNAME CHANGE REQUESTS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.username_change_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Username change details
    current_username TEXT,
    new_username TEXT NOT NULL,
    verification_token TEXT UNIQUE NOT NULL,
    
    -- Request status
    status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'expired'
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES auth.users(id), -- Admin who approved
    rejection_reason TEXT,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_username_status CHECK (status IN ('pending', 'approved', 'rejected', 'expired')),
    CONSTRAINT valid_usernames CHECK (current_username != new_username OR current_username IS NULL)
);

-- =============================================
-- 6. COOKIE CONSENT TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS public.cookie_consent (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES public.user_sessions(id) ON DELETE CASCADE,
    
    -- Consent details
    essential_cookies BOOLEAN DEFAULT TRUE, -- Always true, required for functionality
    functional_cookies BOOLEAN DEFAULT FALSE,
    analytics_cookies BOOLEAN DEFAULT FALSE,
    marketing_cookies BOOLEAN DEFAULT FALSE,
    
    -- Consent metadata
    consent_version TEXT DEFAULT '1.0',
    consent_method TEXT DEFAULT 'banner', -- 'banner', 'settings', 'implicit'
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    given_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 year'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_consent_method CHECK (consent_method IN ('banner', 'settings', 'implicit'))
);

-- =============================================
-- 7. INDEXES FOR PERFORMANCE
-- =============================================
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_token ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON public.user_sessions(user_id, is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON public.user_sessions(last_activity);

CREATE INDEX IF NOT EXISTS idx_user_cookies_user_id ON public.user_cookies(user_id);
CREATE INDEX IF NOT EXISTS idx_user_cookies_session_id ON public.user_cookies(session_id);
CREATE INDEX IF NOT EXISTS idx_user_cookies_category ON public.user_cookies(category);

CREATE INDEX IF NOT EXISTS idx_login_history_user_id ON public.login_history(user_id);
CREATE INDEX IF NOT EXISTS idx_login_history_created_at ON public.login_history(created_at);
CREATE INDEX IF NOT EXISTS idx_login_history_ip_address ON public.login_history(ip_address);

CREATE INDEX IF NOT EXISTS idx_email_verification_user_id ON public.email_verification_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_token ON public.email_verification_requests(verification_token);
CREATE INDEX IF NOT EXISTS idx_email_verification_status ON public.email_verification_requests(status);

CREATE INDEX IF NOT EXISTS idx_username_change_user_id ON public.username_change_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_username_change_token ON public.username_change_requests(verification_token);
CREATE INDEX IF NOT EXISTS idx_username_change_status ON public.username_change_requests(status);

CREATE INDEX IF NOT EXISTS idx_cookie_consent_user_id ON public.cookie_consent(user_id);
CREATE INDEX IF NOT EXISTS idx_cookie_consent_session_id ON public.cookie_consent(session_id);

-- =============================================
-- 8. ROW LEVEL SECURITY (RLS)
-- =============================================
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_cookies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.login_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_verification_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.username_change_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cookie_consent ENABLE ROW LEVEL SECURITY;

-- User sessions policies
CREATE POLICY "Users can view their own sessions" ON public.user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own sessions" ON public.user_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- User cookies policies
CREATE POLICY "Users can manage their own cookies" ON public.user_cookies
    FOR ALL USING (auth.uid() = user_id);

-- Login history policies
CREATE POLICY "Users can view their own login history" ON public.login_history
    FOR SELECT USING (auth.uid() = user_id);

-- Email verification policies
CREATE POLICY "Users can manage their own email verification" ON public.email_verification_requests
    FOR ALL USING (auth.uid() = user_id);

-- Username change policies
CREATE POLICY "Users can manage their own username changes" ON public.username_change_requests
    FOR ALL USING (auth.uid() = user_id);

-- Cookie consent policies
CREATE POLICY "Users can manage their own cookie consent" ON public.cookie_consent
    FOR ALL USING (auth.uid() = user_id);

-- =============================================
-- 9. TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_user_sessions_updated_at BEFORE UPDATE ON public.user_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_cookies_updated_at BEFORE UPDATE ON public.user_cookies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_verification_updated_at BEFORE UPDATE ON public.email_verification_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_username_change_updated_at BEFORE UPDATE ON public.username_change_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cookie_consent_updated_at BEFORE UPDATE ON public.cookie_consent
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- 10. SESSION MANAGEMENT FUNCTIONS
-- =============================================

-- Function to invalidate old sessions when new login occurs
CREATE OR REPLACE FUNCTION manage_user_sessions()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is a new active session
    IF NEW.is_active = TRUE AND (OLD IS NULL OR OLD.is_active = FALSE) THEN
        -- Set all other sessions for this user as inactive
        UPDATE public.user_sessions 
        SET is_active = FALSE, is_primary = FALSE, updated_at = NOW()
        WHERE user_id = NEW.user_id AND id != NEW.id;
        
        -- Set this session as primary
        NEW.is_primary = TRUE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER manage_user_sessions_trigger
    BEFORE INSERT OR UPDATE ON public.user_sessions
    FOR EACH ROW EXECUTE FUNCTION manage_user_sessions();

-- Success message
DO $$ 
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== SESSION MANAGEMENT SCHEMA CREATED ===';
    RAISE NOTICE 'Tables created: user_sessions, user_cookies, login_history, email_verification_requests, username_change_requests, cookie_consent';
    RAISE NOTICE 'Session management with device tracking and cookie consent is now ready!';
    RAISE NOTICE '';
END $$;
