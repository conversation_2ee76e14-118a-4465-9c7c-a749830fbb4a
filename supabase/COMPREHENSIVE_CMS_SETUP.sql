-- =============================================
-- COMPREHENSIVE CMS SETUP FOR CYBERFORCE
-- =============================================
-- This creates a complete CMS system based on your existing content structure
-- =============================================

-- 1. Create CMS Content Types
CREATE TABLE IF NOT EXISTS cms_content_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    schema_definition JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create CMS Content Table
CREATE TABLE IF NOT EXISTS cms_content (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_type_id UUID REFERENCES cms_content_types(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    content JSONB NOT NULL DEFAULT '{}',
    meta_data JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    featured BOOLEAN DEFAULT FALSE,
    order_index INTEGER DEFAULT 0,
    seo_title VARCHAR(255),
    seo_description TEXT,
    seo_keywords TEXT[],
    published_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(content_type_id, slug)
);

-- 3. Create CMS Media Table
CREATE TABLE IF NOT EXISTS cms_media (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path TEXT NOT NULL,
    alt_text TEXT,
    caption TEXT,
    metadata JSONB DEFAULT '{}',
    uploaded_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create CMS Categories
CREATE TABLE IF NOT EXISTS cms_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES cms_categories(id),
    color VARCHAR(7) DEFAULT '#3B82F6',
    icon VARCHAR(50),
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create Content-Category Junction Table
CREATE TABLE IF NOT EXISTS cms_content_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_id UUID REFERENCES cms_content(id) ON DELETE CASCADE,
    category_id UUID REFERENCES cms_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(content_id, category_id)
);

-- 6. Create CMS Tags
CREATE TABLE IF NOT EXISTS cms_tags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    slug VARCHAR(50) NOT NULL UNIQUE,
    color VARCHAR(7) DEFAULT '#6B7280',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create Content-Tags Junction Table
CREATE TABLE IF NOT EXISTS cms_content_tags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_id UUID REFERENCES cms_content(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES cms_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(content_id, tag_id)
);

-- 8. Create Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_cms_content_type_id ON cms_content(content_type_id);
CREATE INDEX IF NOT EXISTS idx_cms_content_status ON cms_content(status);
CREATE INDEX IF NOT EXISTS idx_cms_content_published_at ON cms_content(published_at);
CREATE INDEX IF NOT EXISTS idx_cms_content_created_by ON cms_content(created_by);
CREATE INDEX IF NOT EXISTS idx_cms_content_slug ON cms_content(slug);
CREATE INDEX IF NOT EXISTS idx_cms_media_uploaded_by ON cms_media(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_cms_media_mime_type ON cms_media(mime_type);
CREATE INDEX IF NOT EXISTS idx_cms_categories_parent_id ON cms_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_cms_categories_slug ON cms_categories(slug);

-- 9. Enable RLS
ALTER TABLE cms_content_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_content_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE cms_content_tags ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS Policies
-- Public read access for published content
CREATE POLICY "Public can view published content" ON cms_content
    FOR SELECT USING (status = 'published');

CREATE POLICY "Public can view content types" ON cms_content_types
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public can view categories" ON cms_categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public can view tags" ON cms_tags
    FOR SELECT USING (true);

CREATE POLICY "Public can view media" ON cms_media
    FOR SELECT USING (true);

-- Admin access for content management
CREATE POLICY "Admins can manage all content" ON cms_content
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_roles ar 
            WHERE ar.user_id = auth.uid() 
            AND ar.is_active = true
        )
    );

CREATE POLICY "Admins can manage content types" ON cms_content_types
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_roles ar 
            WHERE ar.user_id = auth.uid() 
            AND ar.is_active = true
        )
    );

CREATE POLICY "Admins can manage media" ON cms_media
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_roles ar 
            WHERE ar.user_id = auth.uid() 
            AND ar.is_active = true
        )
    );

-- 11. Insert Default Content Types
INSERT INTO cms_content_types (name, slug, description, schema_definition) VALUES
('Challenge', 'challenge', 'Cybersecurity challenges and exercises', jsonb_build_object(
    'fields', jsonb_build_array(
        jsonb_build_object('name', 'title', 'type', 'text', 'required', true),
        jsonb_build_object('name', 'description', 'type', 'textarea', 'required', true),
        jsonb_build_object('name', 'difficulty', 'type', 'select', 'options', jsonb_build_array('beginner', 'intermediate', 'advanced')),
        jsonb_build_object('name', 'category', 'type', 'text', 'required', true),
        jsonb_build_object('name', 'points', 'type', 'number', 'default', 100),
        jsonb_build_object('name', 'estimated_time', 'type', 'number', 'default', 30),
        jsonb_build_object('name', 'instructions', 'type', 'rich_text'),
        jsonb_build_object('name', 'hints', 'type', 'array'),
        jsonb_build_object('name', 'solution', 'type', 'rich_text'),
        jsonb_build_object('name', 'flag_format', 'type', 'text')
    )
)),
('Learning Path', 'learning_path', 'Structured learning paths with multiple modules', jsonb_build_object(
    'fields', jsonb_build_array(
        jsonb_build_object('name', 'title', 'type', 'text', 'required', true),
        jsonb_build_object('name', 'description', 'type', 'textarea', 'required', true),
        jsonb_build_object('name', 'short_description', 'type', 'text'),
        jsonb_build_object('name', 'difficulty', 'type', 'select', 'options', jsonb_build_array('beginner', 'intermediate', 'advanced')),
        jsonb_build_object('name', 'category', 'type', 'text', 'required', true),
        jsonb_build_object('name', 'estimated_hours', 'type', 'number', 'default', 10),
        jsonb_build_object('name', 'icon', 'type', 'text'),
        jsonb_build_object('name', 'cover_image', 'type', 'media'),
        jsonb_build_object('name', 'modules', 'type', 'array'),
        jsonb_build_object('name', 'prerequisites', 'type', 'array')
    )
)),
('Learning Module', 'learning_module', 'Individual learning modules within paths', jsonb_build_object(
    'fields', jsonb_build_array(
        jsonb_build_object('name', 'title', 'type', 'text', 'required', true),
        jsonb_build_object('name', 'description', 'type', 'textarea', 'required', true),
        jsonb_build_object('name', 'content', 'type', 'rich_text', 'required', true),
        jsonb_build_object('name', 'learning_path_id', 'type', 'text'),
        jsonb_build_object('name', 'order_index', 'type', 'number', 'default', 0),
        jsonb_build_object('name', 'estimated_time', 'type', 'number', 'default', 15),
        jsonb_build_object('name', 'objectives', 'type', 'array'),
        jsonb_build_object('name', 'key_concepts', 'type', 'array'),
        jsonb_build_object('name', 'practical_examples', 'type', 'array'),
        jsonb_build_object('name', 'quiz_questions', 'type', 'array')
    )
)),
('Blog Post', 'blog_post', 'Blog posts and articles', jsonb_build_object(
    'fields', jsonb_build_array(
        jsonb_build_object('name', 'title', 'type', 'text', 'required', true),
        jsonb_build_object('name', 'excerpt', 'type', 'textarea'),
        jsonb_build_object('name', 'content', 'type', 'rich_text', 'required', true),
        jsonb_build_object('name', 'featured_image', 'type', 'media'),
        jsonb_build_object('name', 'author', 'type', 'text'),
        jsonb_build_object('name', 'read_time', 'type', 'number', 'default', 5)
    )
))
ON CONFLICT (slug) DO NOTHING;

-- 12. Insert Default Categories
INSERT INTO cms_categories (name, slug, description, color, icon) VALUES
('Web Security', 'web-security', 'Web application security challenges and content', '#EF4444', 'FaGlobe'),
('Network Security', 'network-security', 'Network security fundamentals and challenges', '#3B82F6', 'FaNetworkWired'),
('Cryptography', 'cryptography', 'Cryptographic challenges and learning materials', '#8B5CF6', 'FaLock'),
('Forensics', 'forensics', 'Digital forensics and incident response', '#10B981', 'FaSearch'),
('Reverse Engineering', 'reverse-engineering', 'Binary analysis and reverse engineering', '#F59E0B', 'FaCogs'),
('OSINT', 'osint', 'Open Source Intelligence gathering', '#6366F1', 'FaEye'),
('Fundamentals', 'fundamentals', 'Cybersecurity fundamentals and basics', '#06B6D4', 'FaGraduationCap'),
('Red Teaming', 'red-teaming', 'Offensive security and red team operations', '#DC2626', 'FaUserSecret'),
('Blue Teaming', 'blue-teaming', 'Defensive security and blue team operations', '#2563EB', 'FaShieldAlt'),
('Cloud Security', 'cloud-security', 'Cloud platform security', '#7C3AED', 'FaCloud')
ON CONFLICT (slug) DO NOTHING;

-- 13. Insert Default Tags
INSERT INTO cms_tags (name, slug, color) VALUES
('Beginner', 'beginner', '#10B981'),
('Intermediate', 'intermediate', '#F59E0B'),
('Advanced', 'advanced', '#EF4444'),
('Hands-on', 'hands-on', '#8B5CF6'),
('Theory', 'theory', '#6B7280'),
('CTF', 'ctf', '#EC4899'),
('Real-world', 'real-world', '#059669'),
('Tools', 'tools', '#0891B2'),
('Methodology', 'methodology', '#7C2D12')
ON CONFLICT (slug) DO NOTHING;

-- 14. Create CMS Helper Functions
CREATE OR REPLACE FUNCTION get_content_by_type(content_type_slug TEXT)
RETURNS TABLE (
    id UUID,
    title VARCHAR(255),
    slug VARCHAR(255),
    content JSONB,
    status VARCHAR(20),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.title,
        c.slug,
        c.content,
        c.status,
        c.published_at,
        c.created_at
    FROM cms_content c
    JOIN cms_content_types ct ON c.content_type_id = ct.id
    WHERE ct.slug = content_type_slug
    AND c.status = 'published'
    ORDER BY c.published_at DESC NULLS LAST, c.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_featured_content()
RETURNS TABLE (
    id UUID,
    title VARCHAR(255),
    slug VARCHAR(255),
    content JSONB,
    content_type VARCHAR(100),
    published_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.title,
        c.slug,
        c.content,
        ct.name as content_type,
        c.published_at
    FROM cms_content c
    JOIN cms_content_types ct ON c.content_type_id = ct.id
    WHERE c.featured = true
    AND c.status = 'published'
    ORDER BY c.published_at DESC NULLS LAST, c.created_at DESC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 15. Success Message
DO $$
BEGIN
    RAISE NOTICE '🎉 CMS SETUP COMPLETE!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '✅ Content Types: Challenge, Learning Path, Learning Module, Blog Post';
    RAISE NOTICE '✅ Categories: 10 default categories created';
    RAISE NOTICE '✅ Tags: 9 default tags created';
    RAISE NOTICE '✅ Media Management: Ready for file uploads';
    RAISE NOTICE '✅ RLS Policies: Public read, admin write access';
    RAISE NOTICE '✅ Helper Functions: Content retrieval functions';
    RAISE NOTICE '==========================================';
    RAISE NOTICE '🚀 Your CMS is ready for content management!';
    RAISE NOTICE '📝 Access via /admin → Content Management';
END $$;
