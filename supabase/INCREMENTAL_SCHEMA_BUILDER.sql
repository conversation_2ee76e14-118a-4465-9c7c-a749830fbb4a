-- =============================================
-- CYBERFORCE INCREMENTAL SCHEMA BUILDER
-- =============================================
-- This script builds upon existing tables without dropping them
-- It only adds missing tables and columns that don't exist
-- Safe to run multiple times - will not break existing data
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =============================================
-- 1. STANDARDIZE PROFILE TABLES
-- =============================================

-- Check existing table structure and create/update accordingly
DO $$
DECLARE
    profiles_exists BOOLEAN;
    user_profiles_exists BOOLEAN;
    profiles_is_view BOOLEAN;
BEGIN
    -- Check if tables exist
    SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'profiles') INTO profiles_exists;
    SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') INTO user_profiles_exists;

    -- Check if profiles is a view
    SELECT EXISTS (SELECT FROM information_schema.views WHERE table_name = 'profiles') INTO profiles_is_view;

    -- If profiles exists as a view, drop it and work with the underlying table
    IF profiles_exists AND profiles_is_view THEN
        DROP VIEW IF EXISTS profiles;
        profiles_exists := FALSE;
    END IF;

    -- Create profiles table if it doesn't exist
    IF NOT profiles_exists THEN
        CREATE TABLE profiles (
            id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            email TEXT,
            username TEXT,
            full_name TEXT,
            avatar_url TEXT,
            bio TEXT,
            phone_number TEXT,
            location TEXT,
            country TEXT,
            website TEXT,
            linkedin_url TEXT,
            github_url TEXT,
            twitter_url TEXT,
            instagram_url TEXT,
            facebook_url TEXT,

            -- Subscription
            subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium')),
            subscription_start_date TIMESTAMP WITH TIME ZONE,
            subscription_end_date TIMESTAMP WITH TIME ZONE,

            -- Privacy settings
            profile_visibility TEXT DEFAULT 'public' CHECK (profile_visibility IN ('public', 'private', 'friends')),
            show_email BOOLEAN DEFAULT FALSE,
            show_phone BOOLEAN DEFAULT FALSE,
            show_location BOOLEAN DEFAULT TRUE,
            show_social_links BOOLEAN DEFAULT TRUE,

            -- Notification preferences
            email_notifications BOOLEAN DEFAULT TRUE,
            push_notifications BOOLEAN DEFAULT TRUE,
            marketing_emails BOOLEAN DEFAULT FALSE,

            -- UI preferences
            theme_preference TEXT DEFAULT 'system' CHECK (theme_preference IN ('light', 'dark', 'system')),
            preferred_language TEXT DEFAULT 'en',

            -- Gamification
            total_xp INTEGER DEFAULT 0,
            current_level INTEGER DEFAULT 1,
            total_points INTEGER DEFAULT 0,
            total_coins INTEGER DEFAULT 100,
            coins INTEGER DEFAULT 100,
            current_streak INTEGER DEFAULT 0,
            longest_streak INTEGER DEFAULT 0,
            modules_completed INTEGER DEFAULT 0,
            challenges_completed INTEGER DEFAULT 0,
            achievements_unlocked INTEGER DEFAULT 0,

            -- Profile completion
            profile_completion_percentage INTEGER DEFAULT 0,
            onboarding_completed BOOLEAN DEFAULT FALSE,
            email_verified BOOLEAN DEFAULT FALSE,
            phone_verified BOOLEAN DEFAULT FALSE,

            -- Admin and roles
            is_admin BOOLEAN DEFAULT FALSE,
            role TEXT DEFAULT 'user',
            permissions TEXT[] DEFAULT '{}',

            -- Security
            two_factor_enabled BOOLEAN DEFAULT FALSE,
            login_attempts INTEGER DEFAULT 0,

            -- Activity tracking
            last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            last_login TIMESTAMP WITH TIME ZONE,
            login_count INTEGER DEFAULT 0,

            -- Timestamps
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- If user_profiles exists, migrate data to profiles
        IF user_profiles_exists THEN
            INSERT INTO profiles (id, email, username, full_name, avatar_url, bio, phone_number,
                                location, country, website, linkedin_url, github_url, twitter_url,
                                instagram_url, facebook_url, subscription_tier, subscription_start_date,
                                subscription_end_date, created_at, updated_at)
            SELECT id, email, username, full_name, avatar_url, bio, phone_number,
                   location, country, website, linkedin_url, github_url, twitter_url,
                   instagram_url, facebook_url,
                   COALESCE(subscription_tier, 'free'),
                   subscription_start_date, subscription_end_date,
                   COALESCE(created_at, NOW()), COALESCE(updated_at, NOW())
            FROM user_profiles
            ON CONFLICT (id) DO NOTHING;
        END IF;
    END IF;
END $$;

-- Add missing columns to existing profiles table
DO $$
BEGIN
    -- Add profile_completion_percentage if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'profile_completion_percentage') THEN
        ALTER TABLE profiles ADD COLUMN profile_completion_percentage INTEGER DEFAULT 0;
    END IF;
    
    -- Add onboarding_completed if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'onboarding_completed') THEN
        ALTER TABLE profiles ADD COLUMN onboarding_completed BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add email_verified if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'email_verified') THEN
        ALTER TABLE profiles ADD COLUMN email_verified BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add phone_verified if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'phone_verified') THEN
        ALTER TABLE profiles ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add updated_at if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'updated_at') THEN
        ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- Do the same for user_profiles table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        -- Add missing columns to user_profiles
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'profile_completion_percentage') THEN
            ALTER TABLE user_profiles ADD COLUMN profile_completion_percentage INTEGER DEFAULT 0;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'onboarding_completed') THEN
            ALTER TABLE user_profiles ADD COLUMN onboarding_completed BOOLEAN DEFAULT FALSE;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'email_verified') THEN
            ALTER TABLE user_profiles ADD COLUMN email_verified BOOLEAN DEFAULT FALSE;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'phone_verified') THEN
            ALTER TABLE user_profiles ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'updated_at') THEN
            ALTER TABLE user_profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        END IF;
    END IF;
END $$;

-- =============================================
-- 2. CREATE MISSING CORE TABLES
-- =============================================

-- Learning Paths table
CREATE TABLE IF NOT EXISTS learning_paths (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    category TEXT,
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    estimated_duration_hours INTEGER DEFAULT 0,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    image_url TEXT,
    icon TEXT,
    color TEXT,
    order_index INTEGER DEFAULT 0,
    prerequisites TEXT[],
    learning_objectives TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Modules table
CREATE TABLE IF NOT EXISTS learning_modules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    learning_path_id UUID REFERENCES learning_paths(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    slug TEXT NOT NULL,
    description TEXT,
    content JSONB,
    module_type TEXT DEFAULT 'lesson' CHECK (module_type IN ('lesson', 'quiz', 'exercise', 'simulation')),
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    estimated_time_minutes INTEGER DEFAULT 30,
    order_index INTEGER DEFAULT 0,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    prerequisites UUID[],
    learning_objectives TEXT[],
    resources JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(learning_path_id, slug)
);

-- Learning Path Enrollments table
CREATE TABLE IF NOT EXISTS learning_path_enrollments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    learning_path_id UUID REFERENCES learning_paths(id) ON DELETE CASCADE,
    enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    current_module_id UUID REFERENCES learning_modules(id),
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, learning_path_id)
);

-- Learning Module Progress table
CREATE TABLE IF NOT EXISTS learning_module_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'skipped')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    time_spent_minutes INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    quiz_score INTEGER,
    exercise_results JSONB,
    notes TEXT,
    UNIQUE(user_id, module_id)
);

-- =============================================
-- 3. CHALLENGE SYSTEM TABLES
-- =============================================

-- Challenge Categories table
CREATE TABLE IF NOT EXISTS challenge_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT,
    color TEXT,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenges table
CREATE TABLE IF NOT EXISTS challenges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    category_id UUID REFERENCES challenge_categories(id),
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    challenge_type TEXT DEFAULT 'simulation' CHECK (challenge_type IN ('simulation', 'quiz', 'coding', 'analysis')),
    points INTEGER DEFAULT 100,
    coin_reward INTEGER DEFAULT 10,
    estimated_time_minutes INTEGER DEFAULT 30,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    content JSONB,
    solution JSONB,
    hints TEXT[],
    resources JSONB,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge Attempts table
CREATE TABLE IF NOT EXISTS challenge_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'failed', 'abandoned')),
    score INTEGER DEFAULT 0,
    max_score INTEGER DEFAULT 100,
    time_spent_minutes INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    submission_data JSONB,
    feedback TEXT,
    hints_used INTEGER DEFAULT 0
);

-- Practice Simulations table
CREATE TABLE IF NOT EXISTS practice_simulations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    simulation_type TEXT NOT NULL,
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    estimated_time_minutes INTEGER DEFAULT 30,
    points INTEGER DEFAULT 50,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    configuration JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Practice Simulation Attempts table
CREATE TABLE IF NOT EXISTS practice_simulation_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    simulation_id UUID REFERENCES practice_simulations(id) ON DELETE CASCADE,
    is_completed BOOLEAN DEFAULT FALSE,
    completion_time INTEGER,
    approach_score INTEGER DEFAULT 0,
    total_score INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    session_data JSONB
);

-- =============================================
-- 4. GAMIFICATION SYSTEM TABLES
-- =============================================

-- Achievements table
CREATE TABLE IF NOT EXISTS achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    category TEXT,
    achievement_type TEXT DEFAULT 'milestone' CHECK (achievement_type IN ('milestone', 'streak', 'completion', 'special')),
    icon TEXT,
    badge_color TEXT,
    points INTEGER DEFAULT 0,
    coin_reward INTEGER DEFAULT 0,
    rarity TEXT DEFAULT 'common' CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
    requirements JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Achievements table
CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    progress_data JSONB,
    UNIQUE(user_id, achievement_id)
);

-- Daily Challenges table
CREATE TABLE IF NOT EXISTS daily_challenges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    challenge_date DATE NOT NULL UNIQUE,
    challenge_type TEXT NOT NULL,
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    points INTEGER DEFAULT 50,
    coin_reward INTEGER DEFAULT 5,
    content JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Daily Challenges table
CREATE TABLE IF NOT EXISTS user_daily_challenges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    daily_challenge_id UUID REFERENCES daily_challenges(id) ON DELETE CASCADE,
    completed_at TIMESTAMP WITH TIME ZONE,
    score INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    submission_data JSONB,
    UNIQUE(user_id, daily_challenge_id)
);

-- User Streaks table
CREATE TABLE IF NOT EXISTS user_streaks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    streak_type TEXT NOT NULL CHECK (streak_type IN ('daily_login', 'daily_challenge', 'learning', 'challenge')),
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_activity_date DATE,
    streak_start_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, streak_type)
);

-- Leaderboard table
CREATE TABLE IF NOT EXISTS leaderboard (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    leaderboard_type TEXT DEFAULT 'global' CHECK (leaderboard_type IN ('global', 'monthly', 'weekly', 'daily')),
    score INTEGER DEFAULT 0,
    rank_position INTEGER,
    period_start DATE,
    period_end DATE,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, leaderboard_type, period_start)
);

-- =============================================
-- 5. SYSTEM AND ANALYTICS TABLES
-- =============================================

-- User Events table (Analytics)
CREATE TABLE IF NOT EXISTS user_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    event_category TEXT,
    event_data JSONB,
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    notification_type TEXT DEFAULT 'info' CHECK (notification_type IN ('info', 'success', 'warning', 'error', 'achievement')),
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    metadata JSONB,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security Posture table
CREATE TABLE IF NOT EXISTS security_posture (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    overall_score INTEGER DEFAULT 0 CHECK (overall_score >= 0 AND overall_score <= 100),
    risk_level TEXT DEFAULT 'medium' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    vulnerabilities_found INTEGER DEFAULT 0,
    recommendations TEXT[],
    last_scan_date TIMESTAMP WITH TIME ZONE,
    scan_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Threat Data Cache table
CREATE TABLE IF NOT EXISTS threat_data_cache (
    id TEXT PRIMARY KEY,
    data_type TEXT NOT NULL,
    content JSONB NOT NULL,
    source TEXT,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE
);

-- User Settings table
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    email_notifications JSONB DEFAULT '{"achievements": true, "challenges": true, "updates": true, "marketing": false}'::jsonb,
    push_notifications JSONB DEFAULT '{"achievements": true, "challenges": true, "reminders": true}'::jsonb,
    privacy_settings JSONB DEFAULT '{"profile_visibility": "public", "show_progress": true, "show_achievements": true, "show_email": false, "show_phone": false, "show_location": true, "show_social_links": true}'::jsonb,
    learning_preferences JSONB DEFAULT '{"difficulty": "intermediate", "pace": "normal", "topics": []}'::jsonb,
    ui_preferences JSONB DEFAULT '{"theme": "system", "language": "en", "sidebar_collapsed": false}'::jsonb,
    security_settings JSONB DEFAULT '{"two_factor": false, "login_alerts": true, "session_timeout": 30}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Activity Log table
CREATE TABLE IF NOT EXISTS user_activity_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL,
    activity_description TEXT,
    activity_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 6. INDEXES FOR PERFORMANCE
-- =============================================

-- Profile indexes
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);
CREATE INDEX IF NOT EXISTS idx_profiles_subscription_tier ON profiles(subscription_tier);

-- Learning system indexes
CREATE INDEX IF NOT EXISTS idx_learning_path_enrollments_user_id ON learning_path_enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_path_enrollments_path_id ON learning_path_enrollments(learning_path_id);
CREATE INDEX IF NOT EXISTS idx_learning_module_progress_user_id ON learning_module_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_module_progress_module_id ON learning_module_progress(module_id);
CREATE INDEX IF NOT EXISTS idx_learning_module_progress_status ON learning_module_progress(status);

-- Challenge system indexes
CREATE INDEX IF NOT EXISTS idx_challenge_attempts_user_id ON challenge_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_challenge_attempts_challenge_id ON challenge_attempts(challenge_id);
CREATE INDEX IF NOT EXISTS idx_challenge_attempts_status ON challenge_attempts(status);
CREATE INDEX IF NOT EXISTS idx_practice_simulation_attempts_user_id ON practice_simulation_attempts(user_id);

-- Gamification indexes
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_achievement_id ON user_achievements(achievement_id);
CREATE INDEX IF NOT EXISTS idx_user_daily_challenges_user_id ON user_daily_challenges(user_id);
CREATE INDEX IF NOT EXISTS idx_user_streaks_user_id ON user_streaks(user_id);
CREATE INDEX IF NOT EXISTS idx_leaderboard_type_rank ON leaderboard(leaderboard_type, rank_position);

-- System indexes
CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON user_events(user_id);
CREATE INDEX IF NOT EXISTS idx_user_events_type ON user_events(event_type);
CREATE INDEX IF NOT EXISTS idx_user_events_created_at ON user_events(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);

-- =============================================
-- 7. ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on all user-specific tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_path_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_module_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE challenge_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE practice_simulation_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_daily_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_streaks ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboard ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_posture ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_log ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles table
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Create policies for learning system
CREATE POLICY "Users can view their own enrollments" ON learning_path_enrollments
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own progress" ON learning_module_progress
    FOR ALL USING (auth.uid() = user_id);

-- Create policies for challenge system
CREATE POLICY "Users can view their own challenge attempts" ON challenge_attempts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own simulation attempts" ON practice_simulation_attempts
    FOR ALL USING (auth.uid() = user_id);

-- Create policies for gamification
CREATE POLICY "Users can view their own achievements" ON user_achievements
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own daily challenges" ON user_daily_challenges
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own streaks" ON user_streaks
    FOR ALL USING (auth.uid() = user_id);

-- Create policies for system tables
CREATE POLICY "Users can view their own events" ON user_events
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own notifications" ON notifications
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own security posture" ON security_posture
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own settings" ON user_settings
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own activity log" ON user_activity_log
    FOR ALL USING (auth.uid() = user_id);

-- Public read policies for reference tables
CREATE POLICY "Anyone can view learning paths" ON learning_paths
    FOR SELECT USING (true);

CREATE POLICY "Anyone can view learning modules" ON learning_modules
    FOR SELECT USING (true);

CREATE POLICY "Anyone can view challenges" ON challenges
    FOR SELECT USING (true);

CREATE POLICY "Anyone can view challenge categories" ON challenge_categories
    FOR SELECT USING (true);

CREATE POLICY "Anyone can view achievements" ON achievements
    FOR SELECT USING (true);

CREATE POLICY "Anyone can view daily challenges" ON daily_challenges
    FOR SELECT USING (true);

CREATE POLICY "Anyone can view practice simulations" ON practice_simulations
    FOR SELECT USING (true);

-- Leaderboard public read policy
CREATE POLICY "Anyone can view leaderboard" ON leaderboard
    FOR SELECT USING (true);

-- =============================================
-- 8. FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to relevant tables
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_paths_updated_at BEFORE UPDATE ON learning_paths
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_modules_updated_at BEFORE UPDATE ON learning_modules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_challenges_updated_at BEFORE UPDATE ON challenges
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_practice_simulations_updated_at BEFORE UPDATE ON practice_simulations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_security_posture_updated_at BEFORE UPDATE ON security_posture
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON user_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- SCHEMA COMPLETE
-- =============================================

-- Log completion
DO $$
BEGIN
    RAISE NOTICE '✅ CyberForce Incremental Schema Builder completed successfully!';
    RAISE NOTICE '📊 All tables, indexes, policies, and triggers have been created or updated.';
    RAISE NOTICE '🔒 Row Level Security is enabled on all user-specific tables.';
    RAISE NOTICE '⚡ Performance indexes have been added for optimal query performance.';
    RAISE NOTICE '🚀 Your database is now ready for the CyberForce application!';
END $$;
