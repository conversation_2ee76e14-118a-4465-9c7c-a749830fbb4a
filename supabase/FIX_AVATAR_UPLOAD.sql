-- =============================================
-- FIX AVATAR UPLOAD - ADD MISSING COLUMNS
-- =============================================
-- This script adds the missing profile_completion_percentage column
-- and other essential columns to the user_profiles table
-- =============================================

-- Add missing columns to user_profiles table
DO $$
BEGIN
    -- Add profile_completion_percentage if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'user_profiles' 
                   AND column_name = 'profile_completion_percentage') THEN
        ALTER TABLE user_profiles ADD COLUMN profile_completion_percentage INTEGER DEFAULT 0;
        RAISE NOTICE '✅ Added profile_completion_percentage column to user_profiles';
    ELSE
        RAISE NOTICE '✅ profile_completion_percentage column already exists in user_profiles';
    END IF;
    
    -- Add onboarding_completed if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'user_profiles' 
                   AND column_name = 'onboarding_completed') THEN
        ALTER TABLE user_profiles ADD COLUMN onboarding_completed BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ Added onboarding_completed column to user_profiles';
    ELSE
        RAISE NOTICE '✅ onboarding_completed column already exists in user_profiles';
    END IF;
    
    -- Add email_verified if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'user_profiles' 
                   AND column_name = 'email_verified') THEN
        ALTER TABLE user_profiles ADD COLUMN email_verified BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ Added email_verified column to user_profiles';
    ELSE
        RAISE NOTICE '✅ email_verified column already exists in user_profiles';
    END IF;
    
    -- Add phone_verified if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'user_profiles' 
                   AND column_name = 'phone_verified') THEN
        ALTER TABLE user_profiles ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ Added phone_verified column to user_profiles';
    ELSE
        RAISE NOTICE '✅ phone_verified column already exists in user_profiles';
    END IF;
    
    -- Add updated_at if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'user_profiles' 
                   AND column_name = 'updated_at') THEN
        ALTER TABLE user_profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        RAISE NOTICE '✅ Added updated_at column to user_profiles';
    ELSE
        RAISE NOTICE '✅ updated_at column already exists in user_profiles';
    END IF;
END $$;

-- Create or update the profiles view to include new columns
CREATE OR REPLACE VIEW profiles AS
SELECT 
    up.id,
    up.user_id,
    up.display_name,
    up.username,
    up.full_name,
    up.avatar_url,
    up.bio,
    up.email,
    up.subscription_tier,
    up.subscription_status,
    up.subscription_start_date,
    up.subscription_end_date,
    up.is_admin,
    up.role,
    up.total_points,
    up.points,
    up.total_coins,
    up.coins,
    up.current_streak,
    up.longest_streak,
    up.modules_completed,
    up.challenges_completed,
    up.achievements_earned,
    up.last_activity,
    up.learning_style,
    up.created_at,
    up.updated_at,
    -- Add new columns
    COALESCE(up.profile_completion_percentage, 0) as profile_completion_percentage,
    COALESCE(up.onboarding_completed, false) as onboarding_completed,
    COALESCE(up.email_verified, false) as email_verified,
    COALESCE(up.phone_verified, false) as phone_verified
FROM user_profiles up;

-- Create trigger to update updated_at column
CREATE OR REPLACE FUNCTION update_user_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop existing trigger if it exists and create new one
DROP TRIGGER IF EXISTS update_user_profiles_updated_at_trigger ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at_trigger 
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW 
    EXECUTE FUNCTION update_user_profiles_updated_at();

-- Create function to calculate profile completion percentage
CREATE OR REPLACE FUNCTION calculate_profile_completion(user_profile_id UUID)
RETURNS INTEGER AS $$
DECLARE
    completion_score INTEGER := 0;
    total_fields INTEGER := 10; -- Total number of profile fields we check
BEGIN
    SELECT 
        (CASE WHEN username IS NOT NULL AND username != '' THEN 1 ELSE 0 END) +
        (CASE WHEN full_name IS NOT NULL AND full_name != '' THEN 1 ELSE 0 END) +
        (CASE WHEN avatar_url IS NOT NULL AND avatar_url != '' THEN 1 ELSE 0 END) +
        (CASE WHEN bio IS NOT NULL AND bio != '' THEN 1 ELSE 0 END) +
        (CASE WHEN email IS NOT NULL AND email != '' THEN 1 ELSE 0 END) +
        (CASE WHEN subscription_tier IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN learning_style IS NOT NULL AND learning_style != '' THEN 1 ELSE 0 END) +
        (CASE WHEN email_verified = true THEN 1 ELSE 0 END) +
        (CASE WHEN onboarding_completed = true THEN 1 ELSE 0 END) +
        (CASE WHEN last_activity IS NOT NULL THEN 1 ELSE 0 END)
    INTO completion_score
    FROM user_profiles 
    WHERE id = user_profile_id;
    
    RETURN (completion_score * 100 / total_fields);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update profile completion percentage
CREATE OR REPLACE FUNCTION update_profile_completion_percentage()
RETURNS TRIGGER AS $$
BEGIN
    NEW.profile_completion_percentage = calculate_profile_completion(NEW.id);
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop existing trigger if it exists and create new one
DROP TRIGGER IF EXISTS update_profile_completion_trigger ON user_profiles;
CREATE TRIGGER update_profile_completion_trigger 
    BEFORE INSERT OR UPDATE ON user_profiles
    FOR EACH ROW 
    EXECUTE FUNCTION update_profile_completion_percentage();

-- Update existing records to calculate their completion percentage
UPDATE user_profiles 
SET profile_completion_percentage = calculate_profile_completion(id)
WHERE profile_completion_percentage IS NULL OR profile_completion_percentage = 0;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_completion ON user_profiles(profile_completion_percentage);
CREATE INDEX IF NOT EXISTS idx_user_profiles_updated_at ON user_profiles(updated_at);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email_verified ON user_profiles(email_verified);
CREATE INDEX IF NOT EXISTS idx_user_profiles_onboarding ON user_profiles(onboarding_completed);

-- Enable RLS on user_profiles if not already enabled
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create or update RLS policies
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;
CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Success message
DO $$
BEGIN
    RAISE NOTICE '🎉 Avatar upload fix completed successfully!';
    RAISE NOTICE '✅ Added missing columns to user_profiles table';
    RAISE NOTICE '✅ Updated profiles view to include new columns';
    RAISE NOTICE '✅ Created automatic profile completion calculation';
    RAISE NOTICE '✅ Added performance indexes';
    RAISE NOTICE '✅ Updated RLS policies';
    RAISE NOTICE '🚀 Avatar upload should now work without errors!';
END $$;
