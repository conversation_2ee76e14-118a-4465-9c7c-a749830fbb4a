-- =============================================
-- CHECK CURRENT DATABASE SCHEMA
-- =============================================
-- This script checks what tables and columns currently exist
-- Run this first to understand your current database structure
-- =============================================

-- Check what profile-related tables exist
DO $$
DECLARE
    rec RECORD;
BEGIN
    RAISE NOTICE '=== PROFILE TABLES CHECK ===';
    
    -- Check for profiles table
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'profiles') THEN
        IF EXISTS (SELECT FROM information_schema.views WHERE table_name = 'profiles') THEN
            RAISE NOTICE '✅ profiles table exists (VIEW)';
        ELSE
            RAISE NOTICE '✅ profiles table exists (TABLE)';
        END IF;
        
        -- List columns in profiles
        RAISE NOTICE 'Columns in profiles:';
        FOR rec IN 
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'profiles' 
            ORDER BY ordinal_position
        LOOP
            RAISE NOTICE '  - %: % (nullable: %, default: %)', rec.column_name, rec.data_type, rec.is_nullable, rec.column_default;
        END LOOP;
    ELSE
        RAISE NOTICE '❌ profiles table does NOT exist';
    END IF;
    
    -- Check for user_profiles table
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        RAISE NOTICE '✅ user_profiles table exists';
        
        -- List columns in user_profiles
        RAISE NOTICE 'Columns in user_profiles:';
        FOR rec IN 
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'user_profiles' 
            ORDER BY ordinal_position
        LOOP
            RAISE NOTICE '  - %: % (nullable: %, default: %)', rec.column_name, rec.data_type, rec.is_nullable, rec.column_default;
        END LOOP;
    ELSE
        RAISE NOTICE '❌ user_profiles table does NOT exist';
    END IF;
END $$;

-- Check what learning-related tables exist
DO $$
DECLARE
    rec RECORD;
    tbl_name TEXT;
BEGIN
    RAISE NOTICE '=== LEARNING SYSTEM TABLES CHECK ===';

    -- List of learning tables to check
    FOR tbl_name IN
        SELECT unnest(ARRAY['learning_paths', 'learning_modules', 'learning_path_enrollments',
                           'learning_module_progress', 'learning_path_metadata'])
    LOOP
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = tbl_name) THEN
            RAISE NOTICE '✅ % table exists', tbl_name;
        ELSE
            RAISE NOTICE '❌ % table does NOT exist', tbl_name;
        END IF;
    END LOOP;
END $$;

-- Check what challenge-related tables exist
DO $$
DECLARE
    tbl_name TEXT;
BEGIN
    RAISE NOTICE '=== CHALLENGE SYSTEM TABLES CHECK ===';

    -- List of challenge tables to check
    FOR tbl_name IN
        SELECT unnest(ARRAY['challenges', 'challenge_attempts', 'challenge_categories',
                           'practice_simulations', 'practice_simulation_attempts'])
    LOOP
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = tbl_name) THEN
            RAISE NOTICE '✅ % table exists', tbl_name;
        ELSE
            RAISE NOTICE '❌ % table does NOT exist', tbl_name;
        END IF;
    END LOOP;
END $$;

-- Check what system tables exist
DO $$
DECLARE
    tbl_name TEXT;
BEGIN
    RAISE NOTICE '=== SYSTEM TABLES CHECK ===';

    -- List of system tables to check
    FOR tbl_name IN
        SELECT unnest(ARRAY['user_events', 'notifications', 'security_posture',
                           'threat_data_cache', 'user_settings', 'user_activity_log'])
    LOOP
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = tbl_name) THEN
            RAISE NOTICE '✅ % table exists', tbl_name;
        ELSE
            RAISE NOTICE '❌ % table does NOT exist', tbl_name;
        END IF;
    END LOOP;
END $$;

-- Check what gamification tables exist
DO $$
DECLARE
    tbl_name TEXT;
BEGIN
    RAISE NOTICE '=== GAMIFICATION TABLES CHECK ===';

    -- List of gamification tables to check
    FOR tbl_name IN
        SELECT unnest(ARRAY['achievements', 'user_achievements', 'daily_challenges',
                           'user_daily_challenges', 'user_streaks', 'leaderboard'])
    LOOP
        IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = tbl_name) THEN
            RAISE NOTICE '✅ % table exists', tbl_name;
        ELSE
            RAISE NOTICE '❌ % table does NOT exist', tbl_name;
        END IF;
    END LOOP;
END $$;

-- List all existing tables in public schema
DO $$
DECLARE
    rec RECORD;
BEGIN
    RAISE NOTICE '=== ALL EXISTING TABLES ===';
    
    FOR rec IN 
        SELECT table_name, table_type
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
    LOOP
        RAISE NOTICE '📋 %: %', rec.table_name, rec.table_type;
    END LOOP;
END $$;

-- Check for any foreign key constraint issues
DO $$
DECLARE
    rec RECORD;
BEGIN
    RAISE NOTICE '=== FOREIGN KEY CONSTRAINTS CHECK ===';
    
    FOR rec IN 
        SELECT 
            tc.table_name, 
            tc.constraint_name, 
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_schema = 'public'
        ORDER BY tc.table_name, tc.constraint_name
    LOOP
        RAISE NOTICE '🔗 %.% -> %.%', rec.table_name, rec.column_name, rec.foreign_table_name, rec.foreign_column_name;
    END LOOP;
END $$;

RAISE NOTICE '=== SCHEMA CHECK COMPLETE ===';
RAISE NOTICE 'Review the output above to understand your current database structure.';
RAISE NOTICE 'Then run SAFE_INCREMENTAL_SCHEMA.sql to add missing components safely.';
