-- =============================================
-- NOTIFICATION TESTING GUIDE
-- =============================================
-- Complete guide for testing notifications directly from database
-- Replace 'YOUR_USER_ID' with actual user ID from auth.users table
-- =============================================

-- 1. FIRST: Check if notification schema exists
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'user_notifications';

-- If the table doesn't exist, run the NOTIFICATION_SYSTEM_ENHANCED.sql first

-- 2. Get your user ID (replace email with your actual email)
SELECT id, email FROM auth.users WHERE email = '<EMAIL>';

-- 3. BASIC NOTIFICATION TESTING
-- Replace 'YOUR_USER_ID' with the actual UUID from step 2

-- Test 1: Simple info notification
SELECT create_test_notification(
    'YOUR_USER_ID'::UUID,
    'Test Notification 📢',
    'This is a test notification to verify the system is working!',
    'info',
    '📢',
    '/dashboard',
    'Go to Dashboard'
);

-- Test 2: Success notification with emoji
SELECT create_test_notification(
    'YOUR_USER_ID'::UUID,
    'Welcome to CyberForce! 🎉',
    'Your account has been set up successfully. Start your cybersecurity journey now!',
    'success',
    '🎉',
    '/dashboard/learning-paths',
    'Start Learning'
);

-- Test 3: Challenge notification
SELECT create_test_notification(
    'YOUR_USER_ID'::UUID,
    'New Challenge Available! 🚀',
    'A new SQL Injection challenge is now available. Test your skills and earn points!',
    'challenge',
    '🚀',
    '/dashboard/challenges',
    'Start Challenge'
);

-- Test 4: Achievement notification
SELECT create_test_notification(
    'YOUR_USER_ID'::UUID,
    'Achievement Unlocked! 🏆',
    'Congratulations! You have completed 5 challenges in a row. Amazing work!',
    'achievement',
    '🏆',
    '/dashboard/achievements',
    'View Achievements'
);

-- Test 5: Warning notification
SELECT create_test_notification(
    'YOUR_USER_ID'::UUID,
    'System Maintenance ⚠️',
    'Scheduled maintenance will occur tonight from 2-4 AM. Please save your progress.',
    'warning',
    '⚠️',
    null,
    null
);

-- Test 6: Learning streak notification
SELECT create_test_notification(
    'YOUR_USER_ID'::UUID,
    'Learning Streak! 🔥',
    'Amazing! You have maintained a 7-day learning streak. Keep up the great work!',
    'success',
    '🔥',
    '/dashboard',
    'Continue Learning'
);

-- 4. TEMPLATE-BASED NOTIFICATIONS
-- These use predefined templates with variable substitution

-- Welcome notification
SELECT create_notification_from_template(
    'welcome',
    'YOUR_USER_ID'::UUID,
    '{"user_name": "John Doe"}'::JSONB,
    '/dashboard/learning-paths'
);

-- New challenge notification
SELECT create_notification_from_template(
    'challenge_new',
    'YOUR_USER_ID'::UUID,
    '{"difficulty": "Intermediate", "challenge_name": "SQL Injection Lab"}'::JSONB,
    '/dashboard/challenges/sql-injection-lab'
);

-- Challenge completed notification
SELECT create_notification_from_template(
    'challenge_completed',
    'YOUR_USER_ID'::UUID,
    '{"challenge_name": "XSS Prevention", "points": "150"}'::JSONB,
    '/dashboard/challenges/xss-prevention/results'
);

-- Learning path enrolled notification
SELECT create_notification_from_template(
    'learning_path_enrolled',
    'YOUR_USER_ID'::UUID,
    '{"path_name": "Network Security Fundamentals"}'::JSONB,
    '/dashboard/learning-paths/network-security'
);

-- Streak milestone notification
SELECT create_notification_from_template(
    'streak_milestone',
    'YOUR_USER_ID'::UUID,
    '{"days": "10"}'::JSONB,
    '/dashboard'
);

-- Skill level up notification
SELECT create_notification_from_template(
    'skill_level_up',
    'YOUR_USER_ID'::UUID,
    '{"skill_name": "Web Security", "level": "Advanced"}'::JSONB,
    '/dashboard/skills'
);

-- 5. ADVANCED NOTIFICATIONS WITH RICH CONTENT

-- Notification with image
INSERT INTO user_notifications (
    user_id, title, message, type, emoji, image_url, 
    icon_name, icon_color, action_url, action_label, category, priority
) VALUES (
    'YOUR_USER_ID'::UUID,
    'New Course Available! 📚',
    'Advanced Penetration Testing course is now live. Includes hands-on labs and real-world scenarios.',
    'info',
    '📚',
    'https://example.com/course-image.jpg',
    'FaGraduationCap',
    'blue',
    '/dashboard/courses/advanced-pentest',
    'Enroll Now',
    'learning',
    1
);

-- High priority notification
INSERT INTO user_notifications (
    user_id, title, message, type, emoji, 
    icon_name, icon_color, category, priority, is_pinned
) VALUES (
    'YOUR_USER_ID'::UUID,
    'Security Alert! 🚨',
    'Suspicious login attempt detected from unknown location. Please verify your account security.',
    'error',
    '🚨',
    'FaExclamationTriangle',
    'red',
    'security',
    1,
    true
);

-- Scheduled notification (will appear in 1 hour)
INSERT INTO user_notifications (
    user_id, title, message, type, emoji, 
    icon_name, icon_color, scheduled_for
) VALUES (
    'YOUR_USER_ID'::UUID,
    'Daily Challenge Reminder 🎯',
    'Don\'t forget to complete today\'s daily challenge! Only 2 hours left.',
    'info',
    '🎯',
    'FaBullseye',
    'orange',
    NOW() + INTERVAL '1 hour'
);

-- Expiring notification (expires in 24 hours)
INSERT INTO user_notifications (
    user_id, title, message, type, emoji, 
    expires_at, action_url, action_label
) VALUES (
    'YOUR_USER_ID'::UUID,
    'Limited Time Offer! ⏰',
    'Get 50% off premium subscription. Offer expires in 24 hours!',
    'warning',
    '⏰',
    NOW() + INTERVAL '24 hours',
    '/dashboard/subscription',
    'Upgrade Now'
);

-- 6. UTILITY QUERIES FOR TESTING

-- Check all notifications for a user
SELECT 
    id, title, message, type, emoji, is_read, is_dismissed,
    created_at, action_url, action_label
FROM user_notifications 
WHERE user_id = 'YOUR_USER_ID'::UUID 
ORDER BY created_at DESC;

-- Get unread notification count
SELECT get_unread_notification_count('YOUR_USER_ID'::UUID) as unread_count;

-- Mark a specific notification as read (replace NOTIFICATION_ID)
SELECT mark_notification_read('NOTIFICATION_ID'::UUID);

-- Dismiss a specific notification (replace NOTIFICATION_ID)
SELECT dismiss_notification('NOTIFICATION_ID'::UUID);

-- Mark all notifications as read for a user
UPDATE user_notifications 
SET is_read = true, read_at = NOW() 
WHERE user_id = 'YOUR_USER_ID'::UUID AND is_read = false;

-- Delete all notifications for a user (for testing cleanup)
DELETE FROM user_notifications WHERE user_id = 'YOUR_USER_ID'::UUID;

-- 7. BULK TESTING - Create multiple notifications at once
DO $$
DECLARE
    user_uuid UUID := 'YOUR_USER_ID'::UUID; -- Replace with actual user ID
BEGIN
    -- Create 5 different types of notifications
    PERFORM create_test_notification(
        user_uuid,
        'Challenge Completed! 🎉',
        'You successfully completed the SQL Injection challenge!',
        'success',
        '🎉',
        '/dashboard/challenges',
        'View Results'
    );
    
    PERFORM create_test_notification(
        user_uuid,
        'New Badge Earned! 🏅',
        'You earned the "SQL Master" badge for completing 10 SQL challenges.',
        'achievement',
        '🏅',
        '/dashboard/badges',
        'View Badges'
    );
    
    PERFORM create_test_notification(
        user_uuid,
        'Friend Request 👥',
        'John Doe sent you a friend request.',
        'info',
        '👥',
        '/dashboard/friends',
        'View Request'
    );
    
    PERFORM create_test_notification(
        user_uuid,
        'Course Reminder 📖',
        'You have an incomplete lesson in "Network Security Basics".',
        'warning',
        '📖',
        '/dashboard/learning-paths/network-security',
        'Continue Learning'
    );
    
    PERFORM create_test_notification(
        user_uuid,
        'System Update 🔄',
        'New features have been added to the platform. Check them out!',
        'info',
        '🔄',
        '/dashboard/updates',
        'What\'s New'
    );
    
    RAISE NOTICE 'Created 5 test notifications successfully!';
END $$;

-- 8. VERIFICATION QUERIES

-- Verify notification templates exist
SELECT template_name, title_template FROM notification_templates;

-- Check notification functions exist
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%notification%';

-- Verify RLS policies
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'user_notifications';

-- Success message
DO $$
BEGIN
    RAISE NOTICE '🎉 Notification Testing Guide Ready!';
    RAISE NOTICE '📝 Replace YOUR_USER_ID with actual user UUID';
    RAISE NOTICE '🧪 Run individual queries to test different notification types';
    RAISE NOTICE '📊 Use utility queries to verify results';
    RAISE NOTICE '🚀 Check the frontend to see notifications appear!';
END $$;
