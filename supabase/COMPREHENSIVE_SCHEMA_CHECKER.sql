-- =============================================
-- COMPREHENSIVE SCHEMA CHECKER
-- =============================================
-- This script provides detailed information about your current database
-- Copy and paste the output to help create the perfect schema
-- =============================================

-- Create a temporary function to format output nicely
CREATE OR REPLACE FUNCTION format_table_info()
RETURNS TEXT AS $$
DECLARE
    result TEXT := '';
    table_rec RECORD;
    column_rec RECORD;
    constraint_rec RECORD;
BEGIN
    result := result || E'\n=== COMPREHENSIVE DATABASE SCHEMA REPORT ===\n\n';
    
    -- List all tables in public schema
    result := result || E'📋 ALL TABLES IN PUBLIC SCHEMA:\n';
    FOR table_rec IN 
        SELECT table_name, table_type
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
    LOOP
        result := result || format('  %s (%s)', table_rec.table_name, table_rec.table_type) || E'\n';
    END LOOP;
    
    result := result || E'\n';
    
    -- Detailed information for each table
    FOR table_rec IN 
        SELECT table_name, table_type
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
    LOOP
        result := result || format(E'\n🗂️  TABLE: %s (%s)\n', table_rec.table_name, table_rec.table_type);
        result := result || E'   COLUMNS:\n';
        
        -- List columns for this table
        FOR column_rec IN 
            SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
            FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = table_rec.table_name
            ORDER BY ordinal_position
        LOOP
            result := result || format('     - %s: %s%s%s%s', 
                column_rec.column_name, 
                column_rec.data_type,
                CASE WHEN column_rec.character_maximum_length IS NOT NULL 
                     THEN '(' || column_rec.character_maximum_length || ')' 
                     ELSE '' END,
                CASE WHEN column_rec.is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END,
                CASE WHEN column_rec.column_default IS NOT NULL 
                     THEN ' DEFAULT ' || column_rec.column_default 
                     ELSE '' END
            ) || E'\n';
        END LOOP;
        
        -- List constraints for this table
        result := result || E'   CONSTRAINTS:\n';
        FOR constraint_rec IN 
            SELECT tc.constraint_name, tc.constraint_type,
                   kcu.column_name,
                   ccu.table_name AS foreign_table_name,
                   ccu.column_name AS foreign_column_name
            FROM information_schema.table_constraints AS tc 
            LEFT JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
            LEFT JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
                AND ccu.table_schema = tc.table_schema
            WHERE tc.table_schema = 'public' AND tc.table_name = table_rec.table_name
            ORDER BY tc.constraint_type, tc.constraint_name
        LOOP
            IF constraint_rec.constraint_type = 'FOREIGN KEY' THEN
                result := result || format('     - %s: %s.%s -> %s.%s', 
                    constraint_rec.constraint_type,
                    table_rec.table_name, constraint_rec.column_name,
                    constraint_rec.foreign_table_name, constraint_rec.foreign_column_name
                ) || E'\n';
            ELSE
                result := result || format('     - %s: %s (%s)', 
                    constraint_rec.constraint_type,
                    constraint_rec.constraint_name,
                    COALESCE(constraint_rec.column_name, 'multiple columns')
                ) || E'\n';
            END IF;
        END LOOP;
    END LOOP;
    
    -- Check for specific tables that the application needs
    result := result || E'\n=== APPLICATION REQUIREMENTS CHECK ===\n';
    
    -- Profile tables
    result := result || E'\n👤 PROFILE SYSTEM:\n';
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'profiles') THEN
        IF EXISTS (SELECT FROM information_schema.views WHERE table_schema = 'public' AND table_name = 'profiles') THEN
            result := result || E'  ✅ profiles (VIEW)\n';
        ELSE
            result := result || E'  ✅ profiles (TABLE)\n';
        END IF;
    ELSE
        result := result || E'  ❌ profiles (MISSING)\n';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
        result := result || E'  ✅ user_profiles\n';
    ELSE
        result := result || E'  ❌ user_profiles (MISSING)\n';
    END IF;
    
    -- Check for critical columns in profile tables
    IF EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name IN ('profiles', 'user_profiles') AND column_name = 'profile_completion_percentage') THEN
        result := result || E'  ✅ profile_completion_percentage column exists\n';
    ELSE
        result := result || E'  ❌ profile_completion_percentage column MISSING\n';
    END IF;
    
    -- Learning system
    result := result || E'\n📚 LEARNING SYSTEM:\n';
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'learning_paths') THEN
        result := result || E'  ✅ learning_paths\n';
    ELSE
        result := result || E'  ❌ learning_paths (MISSING)\n';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'learning_modules') THEN
        result := result || E'  ✅ learning_modules\n';
    ELSE
        result := result || E'  ❌ learning_modules (MISSING)\n';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'learning_path_enrollments') THEN
        result := result || E'  ✅ learning_path_enrollments\n';
    ELSE
        result := result || E'  ❌ learning_path_enrollments (MISSING)\n';
    END IF;
    
    -- Challenge system
    result := result || E'\n🎯 CHALLENGE SYSTEM:\n';
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'challenges') THEN
        result := result || E'  ✅ challenges\n';
    ELSE
        result := result || E'  ❌ challenges (MISSING)\n';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'challenge_attempts') THEN
        result := result || E'  ✅ challenge_attempts\n';
    ELSE
        result := result || E'  ❌ challenge_attempts (MISSING)\n';
    END IF;
    
    -- System tables
    result := result || E'\n⚙️ SYSTEM TABLES:\n';
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_events') THEN
        result := result || E'  ✅ user_events\n';
    ELSE
        result := result || E'  ❌ user_events (MISSING)\n';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'notifications') THEN
        result := result || E'  ✅ notifications\n';
    ELSE
        result := result || E'  ❌ notifications (MISSING)\n';
    END IF;
    
    result := result || E'\n=== END OF REPORT ===\n';
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Execute the function and display results
SELECT format_table_info();

-- Clean up
DROP FUNCTION format_table_info();
