version: '3.8'

services:
  # Ollama LLM Service
  ollama:
    image: ollama/ollama:latest
    container_name: cyberforce-ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama-data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    deploy:
      resources:
        limits:
          memory: 6G  # Adjust based on your system resources
        reservations:
          memory: 4G  # Minimum recommended for running models like Mistral 7B

  # AI Middleware Service
  ai-middleware:
    build:
      context: ./src/services/ai-middleware
      dockerfile: Dockerfile
    container_name: cyberforce-ai-middleware
    restart: unless-stopped
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - OLLAMA_API_URL=http://ollama:11434
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
    volumes:
      - ./src/services/ai-middleware:/app
      - /app/node_modules
    depends_on:
      - ollama

volumes:
  ollama-data:
