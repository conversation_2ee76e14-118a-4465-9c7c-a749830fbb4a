{"module": {"id": "io-systems-security-101", "title": "I/O Systems & Security", "description": "<PERSON><PERSON> and the Cybertron crew discover how computers talk to devices, keep them safe, and stop sneaky attacks! Learn about I/O hardware, software, and security in a fun way.", "estimated_minutes": 35, "order_index": 5, "learning_objectives": ["Explain what I/O systems are and why they matter.", "Describe how the OS manages devices and data transfer.", "Understand I/O security threats and protections."]}, "topics": [{"id": "io-basics", "title": "I/O Basics", "content_type": "storyline", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "Cybertron's Gadgets", "body": "<PERSON><PERSON> shows how computers use devices like keyboards, mice, and USB drives. The OS helps them all talk to each other!", "key_points": ["I/O devices let computers interact with the world.", "The OS uses drivers to talk to devices.", "I/O can be input (keyboard) or output (screen)."], "visualization": "Comic: <PERSON><PERSON> plugging in a USB drive and talking to a robot (device)."}]}}, {"id": "io-management-security", "title": "Managing and Securing I/O", "content_type": "theory", "estimated_minutes": 15, "order_index": 2, "content": {"sections": [{"title": "How the OS Keeps Devices Safe", "body": "<PERSON><PERSON> explains how the OS manages devices, uses interrupts, and protects against attacks like bad drivers and USB hacks.", "key_points": ["Interrupts, DMA, and buffering help manage I/O.", "Driver vulnerabilities can be dangerous.", "The OS controls who can use which device."], "visualization": "Comic: <PERSON><PERSON> blocking a suspicious USB and fixing a broken driver robot."}]}}, {"id": "io-threats-forensics", "title": "I/O Threats & Forensics", "content_type": "storyline", "estimated_minutes": 10, "order_index": 3, "content": {"sections": [{"title": "Sneaky Device Attacks and How to Find Them", "body": "<PERSON><PERSON> shows how attackers use devices to break in, and how the OS and forensics experts catch them!", "key_points": ["DMA attacks, malicious drivers, and USB hacks.", "Physical security is important for devices.", "Forensics: finding out what devices were used."], "visualization": "Comic: <PERSON><PERSON> catching a USB stick with a net and checking a device logbook."}]}}]}