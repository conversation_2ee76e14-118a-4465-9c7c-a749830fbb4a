{"module": {"id": "file-system-management-security-101", "title": "File System Management & Security", "description": "<PERSON><PERSON> and the Cybertron crew learn how computers organize files, keep them safe, and stop sneaky attacks! Discover how the OS manages files and protects them from threats.", "estimated_minutes": 45, "order_index": 4, "learning_objectives": ["Explain what a file system is and why it's important.", "Describe how the OS organizes and protects files and directories.", "Understand file permissions and access control.", "Recognize common file system security threats and protections."]}, "topics": [{"id": "file-system-basics", "title": "File System Basics", "content_type": "storyline", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "Cybertron's Library", "body": "<PERSON><PERSON> shows how computers store information in files and folders. The OS is the librarian, keeping everything in order!", "key_points": ["Files store data, programs, and more.", "The OS organizes files into directories (folders).", "File attributes: name, type, size, permissions."], "visualization": "Comic: <PERSON><PERSON> as a librarian, putting books (files) on shelves (folders)."}]}}, {"id": "file-system-organization-security", "title": "Organization & Security", "content_type": "theory", "estimated_minutes": 15, "order_index": 2, "content": {"sections": [{"title": "How the OS Protects Files", "body": "<PERSON><PERSON> explains how the OS uses permissions, access control, and monitoring to keep files safe from attackers.", "key_points": ["Permissions: who can read, write, or execute files.", "Access control: DAC, MAC, ACLs.", "File integrity monitoring and secure deletion."], "visualization": "Comic: <PERSON>ber locking a file cabinet and checking a list of who can open it."}]}}, {"id": "file-system-threats-forensics", "title": "File System Threats & Forensics", "content_type": "storyline", "estimated_minutes": 20, "order_index": 3, "content": {"sections": [{"title": "Sneaky File Attacks and How to Find Them", "body": "<PERSON><PERSON> shows how attackers try to hide or steal files, and how the OS and forensics experts catch them!", "key_points": ["Rootkits, ransomware, and data exfiltration.", "Auditing file access and secure deletion.", "Forensics: finding hidden or changed files."], "visualization": "Comic: <PERSON><PERSON> shining a flashlight on a hidden file, and locking up a suspicious folder."}]}}]}