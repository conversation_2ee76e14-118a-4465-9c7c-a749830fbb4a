{"module": {"id": "networking-security-os-perspective-101", "title": "Networking & Security (OS Perspective)", "description": "<PERSON><PERSON> and the Cybertron crew explore how the operating system connects to networks, keeps data safe, and stops cyber threats! Learn about network stacks, firewalls, and more from the OS view.", "estimated_minutes": 40, "order_index": 6, "learning_objectives": ["Explain how the OS interacts with networks and manages network connections.", "Describe OS-level firewalls and network services.", "Understand common network security threats and protections from the OS perspective."]}, "topics": [{"id": "network-stack-os", "title": "Network Stack in the OS", "content_type": "theory", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "How the OS Talks to the Internet", "body": "Cyber shows how the OS uses the network stack to send and receive data. The OS uses sockets, drivers, and interfaces to connect to the world!", "key_points": ["The OS implements the TCP/IP model.", "Sockets let programs talk over the network.", "Network drivers connect hardware to software."], "visualization": "Comic: <PERSON><PERSON> plugging a cable into a globe, with apps talking through speech bubbles (sockets)."}]}}, {"id": "firewalls-services-security", "title": "Firewalls, Services, and Security", "content_type": "theory", "estimated_minutes": 15, "order_index": 2, "content": {"sections": [{"title": "Keeping Cybertron Safe Online", "body": "<PERSON><PERSON> explains how the OS uses firewalls to block bad traffic, manages network services, and protects against attacks like ARP poisoning and DNS spoofing.", "key_points": ["Firewalls block unwanted network traffic.", "Network services can be secured or attacked.", "The OS helps prevent ARP and DNS attacks."], "visualization": "Comic: <PERSON><PERSON> building a brick wall (firewall) and patching holes in a network map."}]}}, {"id": "network-threats-forensics", "title": "Network Threats & Forensics", "content_type": "storyline", "estimated_minutes": 15, "order_index": 3, "content": {"sections": [{"title": "Catching <PERSON><PERSON>", "body": "<PERSON><PERSON> and her friends use tools like Wireshark to catch cyber villains trying to sneak into Cybertron's network!", "key_points": ["Packet sniffers help find suspicious traffic.", "The OS logs network activity for forensics.", "Good configuration keeps Cybertron safe."], "visualization": "Comic: <PERSON><PERSON> with a magnifying glass, looking at network cables and catching a sneaky villain."}]}}]}