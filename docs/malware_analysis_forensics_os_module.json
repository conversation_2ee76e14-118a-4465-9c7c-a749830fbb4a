{"module": {"id": "malware-analysis-forensics-os-101", "title": "Malware Analysis & Forensics (OS Interaction)", "description": "<PERSON><PERSON> and the Cybertron crew investigate how malware tries to trick the OS, and how forensics experts catch the bad guys! Learn about malware, analysis, and digital forensics in a fun way.", "estimated_minutes": 40, "order_index": 10, "learning_objectives": ["Explain how malware interacts with the OS (processes, memory, files).", "Describe basic malware analysis and forensics techniques.", "Understand how the OS helps catch and stop malware."]}, "topics": [{"id": "malware-types-os", "title": "Malware Types & OS Interaction", "content_type": "theory", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "Cybertron vs. Malware", "body": "<PERSON><PERSON> explains how different types of malware (viruses, worms, rootkits) try to trick the OS and what the OS does to stop them!", "key_points": ["Malware can hide in processes, memory, and files.", "The OS uses permissions and monitoring to catch malware.", "Rootkits are especially sneaky and dangerous."], "visualization": "Comic: <PERSON><PERSON> shining a flashlight on a sneaky bug hiding in a file."}]}}, {"id": "malware-analysis-forensics", "title": "Malware Analysis & Forensics", "content_type": "theory", "estimated_minutes": 15, "order_index": 2, "content": {"sections": [{"title": "How Experts Catch Malware", "body": "<PERSON><PERSON> shows how experts use tools to analyze malware, look at memory dumps, and find out what happened!", "key_points": ["Static and dynamic analysis help understand malware.", "Memory and disk forensics find hidden threats.", "Logs and artifacts help build a timeline."], "visualization": "Comic: <PERSON><PERSON> using a magnifying glass to look at a memory chip and a hard drive."}]}}, {"id": "malware-threats-prevention", "title": "Malware Threats & Prevention", "content_type": "storyline", "estimated_minutes": 15, "order_index": 3, "content": {"sections": [{"title": "Stopping the Bad Guys", "body": "<PERSON><PERSON> and her friends set up defenses to stop malware before it can do harm, using OS security features and best practices!", "key_points": ["Antivirus, updates, and backups help stop malware.", "The OS can block suspicious programs and files.", "Good habits keep Cybertron safe!"], "visualization": "Comic: <PERSON><PERSON> building a wall and setting up alarms around Cybertron."}]}}]}