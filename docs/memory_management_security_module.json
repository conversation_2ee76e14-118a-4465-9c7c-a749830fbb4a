{"module": {"id": "memory-management-security-101", "title": "Memory Management & Security", "description": "<PERSON><PERSON> and the Cybertron crew explore how computers use memory, keep it safe, and stop sneaky attacks! Learn about memory, how it's shared, and how the OS protects it.", "estimated_minutes": 45, "order_index": 3, "learning_objectives": ["Explain the difference between logical and physical memory.", "Describe how the OS allocates and protects memory.", "Understand virtual memory and why it's important.", "Recognize common memory security threats and protections."]}, "topics": [{"id": "memory-basics", "title": "Memory Basics", "content_type": "storyline", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "Cybertron's Memory Bank", "body": "<PERSON><PERSON> shows how computers remember things using memory. The OS makes sure everyone gets their own space!", "key_points": ["Memory stores data and programs.", "The OS manages who uses which part of memory.", "Logical vs. physical memory: what programs see vs. what's real."], "visualization": "Comic: <PERSON><PERSON> handing out memory cards to different apps."}]}}, {"id": "memory-allocation-protection", "title": "Memory Allocation & Protection", "content_type": "theory", "estimated_minutes": 15, "order_index": 2, "content": {"sections": [{"title": "How the OS Shares and Protects Memory", "body": "<PERSON><PERSON> explains how the OS gives memory to programs, keeps them from bumping into each other, and protects against attacks.", "key_points": ["Contiguous allocation, paging, segmentation.", "Page tables, TLB, and virtual memory.", "Protection bits: read, write, execute."], "visualization": "Comic: <PERSON><PERSON> drawing lines between memory blocks and locking some with keys."}]}}, {"id": "memory-threats-forensics", "title": "Memory Threats & Forensics", "content_type": "storyline", "estimated_minutes": 20, "order_index": 3, "content": {"sections": [{"title": "Sneaky Attacks and How to Find Them", "body": "<PERSON><PERSON> shows how attackers try to trick memory (like buffer overflows), and how the OS and forensics experts catch them!", "key_points": ["Buffer overflows, use-after-free, double-free, ROP.", "OS uses ASLR, DEP, and canaries to protect memory.", "Memory forensics helps find hidden threats."], "visualization": "Comic: <PERSON><PERSON> catching a bug sneaking into memory, and using a magnifying glass to find clues."}]}}]}