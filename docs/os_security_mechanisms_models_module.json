{"module": {"id": "os-security-mechanisms-models-101", "title": "Security Mechanisms & Models (OS-Level)", "description": "<PERSON><PERSON> and the Cybertron crew learn about passwords, permissions, logs, and security models that keep computers safe! Discover how the OS uses rules and tools to protect Cybertron.", "estimated_minutes": 40, "order_index": 9, "learning_objectives": ["Explain how authentication and authorization work in the OS.", "Describe how the OS logs and audits activity.", "Understand basic security models and cryptographic services at the OS level."]}, "topics": [{"id": "auth-authorization", "title": "Authentication & Authorization", "content_type": "theory", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "Who Can Do What in Cybertron?", "body": "<PERSON><PERSON> explains how users log in, get permissions, and how the OS checks who can do what!", "key_points": ["User accounts, groups, and permissions.", "Access control lists (ACLs) and PAM.", "Kerberos and LDAP for advanced security."], "visualization": "Comic: <PERSON><PERSON> checking a list of users and giving out keys to doors."}]}}, {"id": "auditing-logging-security-models", "title": "Auditing, Logging & Security Models", "content_type": "theory", "estimated_minutes": 15, "order_index": 2, "content": {"sections": [{"title": "Keeping Track and Setting Rules", "body": "<PERSON>ber shows how the OS keeps logs of what happens, and uses security models to keep data safe.", "key_points": ["System logs and audit trails help track activity.", "Security models: Bell<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "Trusted Computing Base (TCB) and minimizing risk."], "visualization": "Comic: <PERSON><PERSON> writing in a big logbook and drawing a diagram of security levels."}]}}, {"id": "crypto-services-os", "title": "Cryptographic Services in the OS", "content_type": "storyline", "estimated_minutes": 15, "order_index": 3, "content": {"sections": [{"title": "How the OS Uses Crypto to Stay Safe", "body": "<PERSON><PERSON> explains how the OS uses cryptography to protect data, store keys, and keep secrets safe!", "key_points": ["Kernel crypto APIs and secure key storage.", "Trusted Platform Module (TPM) and HSM.", "Why cryptography is important for OS security."], "visualization": "Comic: <PERSON><PERSON> locking secrets in a safe and using a magic key."}]}}]}