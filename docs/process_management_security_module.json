{"module": {"id": "process-management-security-101", "title": "Process Management & Security", "description": "<PERSON><PERSON> and the Cybertron crew learn how computers run programs, keep them safe, and make sure everyone gets a turn! Discover processes, threads, and how the OS protects them.", "estimated_minutes": 45, "order_index": 2, "learning_objectives": ["Explain what a process and a thread are.", "Describe how the OS manages running programs and switches between them.", "Understand how the OS keeps processes safe from each other.", "Recognize basic process security threats and protections."]}, "topics": [{"id": "processes-threads", "title": "Processes and Threads", "content_type": "storyline", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "Cybertron's Busy Workers", "body": "<PERSON>ber shows how each app is like a worker (process) in Cybertron, and sometimes they have helpers (threads). The OS keeps track of all the workers!", "key_points": ["A process is a running program.", "Threads are helpers inside a process.", "The OS uses a list (PCB) to track all processes."], "visualization": "Comic: <PERSON><PERSON> handing out job badges to workers (processes) and their helpers (threads)."}]}}, {"id": "process-management", "title": "How the OS Manages Processes", "content_type": "theory", "estimated_minutes": 10, "order_index": 2, "content": {"sections": [{"title": "Keeping Everyone Organized", "body": "<PERSON><PERSON> explains how the OS starts, stops, and switches between processes. Sometimes, a process waits, runs, or finishes. The OS makes sure no one is left out!", "key_points": ["Process states: new, ready, running, waiting, terminated.", "Context switching lets the OS switch between processes.", "The OS can create (fork) or end (terminate) processes."], "visualization": "Comic: <PERSON><PERSON> as a traffic cop, switching signs between 'run', 'wait', and 'stop'."}]}}, {"id": "ipc-scheduling-security", "title": "Communication, Scheduling, and Security", "content_type": "theory", "estimated_minutes": 15, "order_index": 3, "content": {"sections": [{"title": "How Processes Talk and Stay Safe", "body": "<PERSON><PERSON> shows how processes can talk (IPC), how the OS decides who goes next (scheduling), and how it keeps bad processes from causing trouble.", "key_points": ["IPC: pipes, message queues, shared memory, semaphores.", "Scheduling: round robin, priority, and more.", "Security: isolation, least privilege, sandboxing."], "visualization": "Comic: <PERSON><PERSON> passing messages between workers, and building walls for safety."}]}}, {"id": "process-threats", "title": "Process Security Threats", "content_type": "storyline", "estimated_minutes": 10, "order_index": 4, "content": {"sections": [{"title": "Trouble in Cybertron", "body": "Sometimes, a bad process tries to take too much or break the rules. <PERSON><PERSON> shows how the OS stops these troublemakers!", "key_points": ["Race conditions, privilege escalation, and process starvation are threats.", "The OS uses permissions and monitoring to stop attacks."], "visualization": "Comic: <PERSON><PERSON> catching a sneaky process trying to steal extra memory."}]}}]}