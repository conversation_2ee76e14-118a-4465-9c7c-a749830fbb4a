{"module": {"id": "virtualization-containerization-security-101", "title": "Virtualization & Containerization Security", "description": "<PERSON><PERSON> and the Cybertron crew explore how computers can run virtual machines and containers, and how to keep them safe! Learn about hypervisors, containers, and security tricks for these modern tools.", "estimated_minutes": 35, "order_index": 8, "learning_objectives": ["Explain what virtualization and containers are.", "Describe how the OS manages virtual machines and containers.", "Understand common security threats and protections for virtualization and containers."]}, "topics": [{"id": "virtualization-basics", "title": "Virtualization Basics", "content_type": "theory", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "Cybertron's Virtual Worlds", "body": "<PERSON><PERSON> shows how computers can run pretend computers (virtual machines) and how the OS helps manage them!", "key_points": ["Virtual machines let one computer act like many.", "Hypervisors manage virtual machines.", "Containers are like mini-computers for apps."], "visualization": "Comic: <PERSON>ber building tiny cities inside a big city, each with its own rules."}]}}, {"id": "containerization-security", "title": "Containerization & Security", "content_type": "theory", "estimated_minutes": 15, "order_index": 2, "content": {"sections": [{"title": "Keeping Containers Safe", "body": "<PERSON><PERSON> explains how containers keep apps separate, but attackers sometimes try to break out! The OS uses namespaces, cgroups, and other tricks to keep containers safe.", "key_points": ["Containers use namespaces and cgroups for isolation.", "Container escapes are a big threat.", "The OS and tools like Kubernetes help keep containers secure."], "visualization": "Comic: Cyber building walls between containers and patching holes."}]}}, {"id": "virtualization-threats-forensics", "title": "Virtualization Threats & Forensics", "content_type": "storyline", "estimated_minutes": 10, "order_index": 3, "content": {"sections": [{"title": "Catching <PERSON>ins", "body": "<PERSON><PERSON> and her friends catch attackers trying to escape from virtual machines or containers, and use forensics to find out what happened!", "key_points": ["VM escapes, container escapes, and supply chain attacks.", "Forensics: finding out what happened inside a VM or container.", "Good configuration keeps Cybertron's virtual worlds safe."], "visualization": "Comic: <PERSON><PERSON> catching a villain jumping from one tiny city to another, and checking a logbook."}]}}]}