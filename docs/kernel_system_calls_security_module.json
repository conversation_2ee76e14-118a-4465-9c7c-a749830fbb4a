{"module": {"id": "kernel-system-calls-security-101", "title": "Kernel & System Calls Security", "description": "<PERSON><PERSON> and the Cybertron crew dive deep into the heart of the OS: the kernel! Learn how the kernel works, what system calls are, and how to keep the core of the OS safe from attacks.", "estimated_minutes": 40, "order_index": 7, "learning_objectives": ["Explain what the kernel is and its main functions.", "Describe how system calls work and why they matter for security.", "Understand common kernel vulnerabilities and how to protect against them."]}, "topics": [{"id": "kernel-basics", "title": "Kernel Basics", "content_type": "theory", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "Cybertron's Core: The Kernel", "body": "<PERSON><PERSON> explains that the kernel is the heart of the OS, controlling everything! It runs in a special mode and keeps the system safe.", "key_points": ["The kernel manages hardware, memory, and processes.", "It runs in kernel mode for extra power and safety.", "User mode is for apps, kernel mode is for the OS core."], "visualization": "Comic: <PERSON><PERSON> wearing a crown, sitting in the center of Cybertron, with apps outside the castle walls."}]}}, {"id": "system-calls-security", "title": "System Calls & Security", "content_type": "theory", "estimated_minutes": 15, "order_index": 2, "content": {"sections": [{"title": "How Apps Talk to the Kernel", "body": "<PERSON><PERSON> shows how apps use system calls to ask the kernel for help, and how attackers sometimes try to trick the kernel!", "key_points": ["System calls are special requests from apps to the kernel.", "System call tables help the OS know what to do.", "Attackers may try to hook or exploit system calls."], "visualization": "Comic: <PERSON><PERSON> as a gatekeeper, letting good apps in and blocking sneaky ones."}]}}, {"id": "kernel-threats-hardening", "title": "Kernel Threats & Hardening", "content_type": "storyline", "estimated_minutes": 15, "order_index": 3, "content": {"sections": [{"title": "Defending the Core", "body": "<PERSON><PERSON> and her friends defend the kernel from rootkits and other attacks, using hardening techniques like KASLR and signed modules!", "key_points": ["Rootkits try to hide in the kernel.", "Hardening: KASLR, signed modules, disabling unused features.", "Forensics: finding hidden kernel threats."], "visualization": "Comic: <PERSON><PERSON> putting up shields and scanning the castle for hidden enemies."}]}}]}