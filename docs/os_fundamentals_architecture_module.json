{"module": {"id": "os-fundamentals-architecture-101", "title": "OS Fundamentals & Architecture", "description": "<PERSON><PERSON> and the Cybertron crew discover what makes an operating system tick! Learn about the OS's role, its main parts, and how it starts up, all through stories and comics.", "estimated_minutes": 40, "order_index": 1, "learning_objectives": ["Explain what an operating system is and its main purpose.", "Identify the main components of an OS (kernel, shell, libraries, utilities).", "Describe different types of operating systems and their structures.", "Understand the boot process and system calls."]}, "topics": [{"id": "os-what-is", "title": "What is an Operating System?", "content_type": "storyline", "estimated_minutes": 10, "order_index": 1, "content": {"sections": [{"title": "Cybertron's Brain: The OS!", "body": "In Cybertron, every computer has a brain called the Operating System. <PERSON><PERSON> explains that the OS is like the city's control center, making sure everything works together.", "key_points": ["The OS manages all hardware and software.", "It helps users and programs talk to the computer.", "Without an OS, computers can't do anything useful!"], "visualization": "Comic: Cyber at a control panel, connecting wires between apps, files, and hardware."}]}}, {"id": "os-components", "title": "Components of an OS", "content_type": "theory", "estimated_minutes": 10, "order_index": 2, "content": {"sections": [{"title": "Meet the Team: Kernel, Shell, and More!", "body": "<PERSON><PERSON> introduces the OS team: the kernel (the boss), the shell (the translator), system libraries (helpers), and utilities (tools).", "key_points": ["Kernel: Controls everything inside the computer.", "Shell: Lets users give commands.", "Libraries: Help programs work with the OS.", "Utilities: Extra tools for special jobs."], "visualization": "Comic: <PERSON><PERSON> as the boss (kernel), with friends as shell, libraries, and utilities."}]}}, {"id": "os-types-structure", "title": "Types and Structure of OS", "content_type": "theory", "estimated_minutes": 10, "order_index": 3, "content": {"sections": [{"title": "Different OS for Different Jobs", "body": "Cyber shows that some OS are for one user, some for many, some for phones, and some for big servers. The structure can be monolithic, microkernel, or layered!", "key_points": ["Single-user, multi-user, real-time, embedded, mobile OS.", "Monolithic kernel, microkernel, hybrid, layered, virtual machines."], "visualization": "Comic: Cyber switching hats for each OS type, and building blocks for OS structures."}]}}, {"id": "os-boot-system-calls", "title": "Boot Process & System Calls", "content_type": "storyline", "estimated_minutes": 10, "order_index": 4, "content": {"sections": [{"title": "How Does the OS Start?", "body": "<PERSON><PERSON> explains how the computer wakes up: BIOS/UEFI, bootloader, and then the kernel! System calls let programs ask the OS for help.", "key_points": ["Boot process: BIOS/UEFI → Bootloader → Kernel.", "System calls: Programs ask the OS to do things (like open files).", "User mode vs. kernel mode: Different privilege levels for safety."], "visualization": "Comic: <PERSON><PERSON> waking up the computer, and apps raising hands to ask the OS for help."}]}}]}