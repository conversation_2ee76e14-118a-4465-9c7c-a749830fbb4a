-- =====================================================
-- COMPREHENSIVE LEARNING CONTENT DATABASE SCHEMA
-- =====================================================
-- Complete schema for migrating all JavaScript learning content
-- to database including SVGs, interactive elements, quizzes, labs
-- =====================================================

-- Drop existing tables in correct order
DROP TABLE IF EXISTS quiz_attempts CASCADE;
DROP TABLE IF EXISTS lab_submissions CASCADE;
DROP TABLE IF EXISTS user_section_progress CASCADE;
DROP TABLE IF EXISTS user_module_progress CASCADE;
DROP TABLE IF EXISTS section_components CASCADE;
DROP TABLE IF EXISTS module_sections CASCADE;
DROP TABLE IF EXISTS learning_modules CASCADE;
DROP TABLE IF EXISTS learning_paths CASCADE;
DROP TABLE IF EXISTS content_assets CASCADE;

-- =====================================================
-- CONTENT ASSETS TABLE (for SVGs, images, videos, etc.)
-- =====================================================
CREATE TABLE content_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    asset_type VARCHAR(50) NOT NULL, -- 'svg', 'image', 'video', 'audio', 'document'
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT,
    file_content TEXT, -- For SVG content stored as text
    mime_type VARCHAR(100),
    file_size BIGINT,
    alt_text TEXT,
    description TEXT,
    tags TEXT[], -- Array of tags for categorization
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- LEARNING PATHS TABLE
-- =====================================================
CREATE TABLE learning_paths (
    id VARCHAR(100) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'fundamentals',
    difficulty VARCHAR(50) DEFAULT 'beginner', -- 'beginner', 'intermediate', 'advanced', 'expert'
    phase VARCHAR(50), -- 'foundation', 'intermediate', 'advanced', 'expert'
    estimated_hours INTEGER DEFAULT 0,
    estimated_time INTEGER DEFAULT 0, -- in minutes
    prerequisites TEXT[], -- Array of prerequisite path IDs
    skills TEXT[], -- Array of skills learned
    objectives TEXT[], -- Array of learning objectives
    modules_count INTEGER DEFAULT 0,
    total_sections INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    icon_asset_id UUID REFERENCES content_assets(id),
    banner_asset_id UUID REFERENCES content_assets(id),
    metadata JSONB, -- Additional metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- LEARNING MODULES TABLE
-- =====================================================
CREATE TABLE learning_modules (
    id VARCHAR(100) PRIMARY KEY,
    source_id VARCHAR(100), -- Original module ID from JS files (e.g., 'nf-1', 'bt-01')
    learning_path_id VARCHAR(100) REFERENCES learning_paths(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    short_description TEXT,
    category VARCHAR(100),
    phase VARCHAR(50), -- 'foundation', 'intermediate', 'advanced', 'expert'
    difficulty VARCHAR(50) DEFAULT 'beginner',
    estimated_time INTEGER DEFAULT 60, -- in minutes
    estimated_hours INTEGER DEFAULT 1,
    objectives TEXT[], -- Array of learning objectives
    prerequisites TEXT[], -- Array of prerequisite module IDs
    skills TEXT[], -- Array of skills learned
    topics TEXT[], -- Array of topics covered
    xp_reward INTEGER DEFAULT 100,
    points_reward INTEGER DEFAULT 50,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    is_premium BOOLEAN DEFAULT false,
    is_free BOOLEAN DEFAULT true,
    icon_asset_id UUID REFERENCES content_assets(id),
    banner_asset_id UUID REFERENCES content_assets(id),
    metadata JSONB, -- Additional module metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- MODULE SECTIONS TABLE
-- =====================================================
CREATE TABLE module_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    module_id VARCHAR(100) REFERENCES learning_modules(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT, -- HTML content for text sections
    content_type VARCHAR(50) DEFAULT 'text', -- 'text', 'interactive', 'quiz', 'lab', 'simulation', 'video'
    section_data JSONB, -- Rich content data (interactive elements, quiz questions, lab instructions)
    order_index INTEGER DEFAULT 0,
    estimated_time INTEGER DEFAULT 15, -- in minutes
    is_active BOOLEAN DEFAULT true,
    is_required BOOLEAN DEFAULT true,
    completion_criteria JSONB, -- Criteria for section completion
    assets UUID[], -- Array of asset IDs used in this section
    metadata JSONB, -- Additional section metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- SECTION COMPONENTS TABLE (for interactive elements)
-- =====================================================
CREATE TABLE section_components (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    section_id UUID REFERENCES module_sections(id) ON DELETE CASCADE,
    component_type VARCHAR(100) NOT NULL, -- 'svg_diagram', 'simulation', 'tool', 'exercise', 'code_block'
    component_name VARCHAR(255),
    component_data JSONB NOT NULL, -- Component configuration and content
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    assets UUID[], -- Array of asset IDs used in this component
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- USER MODULE PROGRESS TABLE
-- =====================================================
CREATE TABLE user_module_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    module_id VARCHAR(100) REFERENCES learning_modules(id) ON DELETE CASCADE,
    learning_path_id VARCHAR(100) REFERENCES learning_paths(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'not_started', -- 'not_started', 'in_progress', 'completed', 'locked'
    progress_percentage INTEGER DEFAULT 0,
    sections_completed INTEGER DEFAULT 0,
    total_sections INTEGER DEFAULT 0,
    time_spent INTEGER DEFAULT 0, -- in minutes
    xp_earned INTEGER DEFAULT 0,
    points_earned INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT false,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, module_id)
);

-- =====================================================
-- USER SECTION PROGRESS TABLE
-- =====================================================
CREATE TABLE user_section_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    section_id UUID REFERENCES module_sections(id) ON DELETE CASCADE,
    module_id VARCHAR(100) REFERENCES learning_modules(id) ON DELETE CASCADE,
    is_completed BOOLEAN DEFAULT false,
    completion_date TIMESTAMP,
    time_spent INTEGER DEFAULT 0, -- in minutes
    interaction_data JSONB, -- User interactions with section content
    notes TEXT, -- User notes for the section
    bookmarked BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, section_id)
);

-- =====================================================
-- QUIZ ATTEMPTS TABLE
-- =====================================================
CREATE TABLE quiz_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    section_id UUID REFERENCES module_sections(id) ON DELETE CASCADE,
    module_id VARCHAR(100) REFERENCES learning_modules(id) ON DELETE CASCADE,
    attempt_number INTEGER DEFAULT 1,
    questions_data JSONB NOT NULL, -- Quiz questions and structure
    answers_data JSONB, -- User answers
    score INTEGER DEFAULT 0,
    max_score INTEGER DEFAULT 100,
    percentage_score DECIMAL(5,2) DEFAULT 0.00,
    is_passed BOOLEAN DEFAULT false,
    time_taken INTEGER, -- in seconds
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- LAB SUBMISSIONS TABLE
-- =====================================================
CREATE TABLE lab_submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    section_id UUID REFERENCES module_sections(id) ON DELETE CASCADE,
    module_id VARCHAR(100) REFERENCES learning_modules(id) ON DELETE CASCADE,
    lab_data JSONB NOT NULL, -- Lab instructions and structure
    submission_data JSONB, -- User submission and results
    status VARCHAR(50) DEFAULT 'not_started', -- 'not_started', 'in_progress', 'submitted', 'graded'
    score INTEGER DEFAULT 0,
    max_score INTEGER DEFAULT 100,
    feedback TEXT,
    is_passed BOOLEAN DEFAULT false,
    time_spent INTEGER DEFAULT 0, -- in minutes
    submitted_at TIMESTAMP,
    graded_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX idx_content_assets_type ON content_assets(asset_type);
CREATE INDEX idx_content_assets_tags ON content_assets USING GIN(tags);
CREATE INDEX idx_learning_paths_category ON learning_paths(category);
CREATE INDEX idx_learning_paths_difficulty ON learning_paths(difficulty);
CREATE INDEX idx_learning_modules_path ON learning_modules(learning_path_id);
CREATE INDEX idx_learning_modules_order ON learning_modules(learning_path_id, order_index);
CREATE INDEX idx_learning_modules_source ON learning_modules(source_id);
CREATE INDEX idx_module_sections_module ON module_sections(module_id);
CREATE INDEX idx_module_sections_order ON module_sections(module_id, order_index);
CREATE INDEX idx_module_sections_type ON module_sections(content_type);
CREATE INDEX idx_section_components_section ON section_components(section_id);
CREATE INDEX idx_section_components_type ON section_components(component_type);
CREATE INDEX idx_user_module_progress_user ON user_module_progress(user_id);
CREATE INDEX idx_user_module_progress_module ON user_module_progress(module_id);
CREATE INDEX idx_user_module_progress_path ON user_module_progress(learning_path_id);
CREATE INDEX idx_user_module_progress_status ON user_module_progress(user_id, status);
CREATE INDEX idx_user_section_progress_user ON user_section_progress(user_id);
CREATE INDEX idx_user_section_progress_section ON user_section_progress(section_id);
CREATE INDEX idx_user_section_progress_module ON user_section_progress(module_id);
CREATE INDEX idx_quiz_attempts_user ON quiz_attempts(user_id);
CREATE INDEX idx_quiz_attempts_section ON quiz_attempts(section_id);
CREATE INDEX idx_lab_submissions_user ON lab_submissions(user_id);
CREATE INDEX idx_lab_submissions_section ON lab_submissions(section_id);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_content_assets_updated_at BEFORE UPDATE ON content_assets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_paths_updated_at BEFORE UPDATE ON learning_paths FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_modules_updated_at BEFORE UPDATE ON learning_modules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_module_sections_updated_at BEFORE UPDATE ON module_sections FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_section_components_updated_at BEFORE UPDATE ON section_components FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_module_progress_updated_at BEFORE UPDATE ON user_module_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_section_progress_updated_at BEFORE UPDATE ON user_section_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lab_submissions_updated_at BEFORE UPDATE ON lab_submissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================
ALTER TABLE content_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE module_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE section_components ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_module_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_section_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE quiz_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_submissions ENABLE ROW LEVEL SECURITY;

-- Public read access for learning content
CREATE POLICY "Public read access for content assets" ON content_assets FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for learning paths" ON learning_paths FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for learning modules" ON learning_modules FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for module sections" ON module_sections FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for section components" ON section_components FOR SELECT USING (is_active = true);

-- User-specific access for progress and submissions
CREATE POLICY "Users can view own module progress" ON user_module_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own module progress" ON user_module_progress FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own module progress" ON user_module_progress FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own section progress" ON user_section_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own section progress" ON user_section_progress FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own section progress" ON user_section_progress FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own quiz attempts" ON quiz_attempts FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own quiz attempts" ON quiz_attempts FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own quiz attempts" ON quiz_attempts FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own lab submissions" ON lab_submissions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own lab submissions" ON lab_submissions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own lab submissions" ON lab_submissions FOR UPDATE USING (auth.uid() = user_id);

-- Admin access for content management
CREATE POLICY "Admins can manage content assets" ON content_assets FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (profiles.role = 'super_admin' OR profiles.is_admin = true)
    )
);

CREATE POLICY "Admins can manage learning paths" ON learning_paths FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (profiles.role = 'super_admin' OR profiles.is_admin = true)
    )
);

CREATE POLICY "Admins can manage learning modules" ON learning_modules FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (profiles.role = 'super_admin' OR profiles.is_admin = true)
    )
);

CREATE POLICY "Admins can manage module sections" ON module_sections FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (profiles.role = 'super_admin' OR profiles.is_admin = true)
    )
);

CREATE POLICY "Admins can manage section components" ON section_components FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (profiles.role = 'super_admin' OR profiles.is_admin = true)
    )
);

-- Instructors can view all progress for their courses
CREATE POLICY "Instructors can view progress" ON user_module_progress FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (profiles.role IN ('super_admin', 'instructor') OR profiles.is_admin = true)
    )
);

CREATE POLICY "Instructors can view section progress" ON user_section_progress FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (profiles.role IN ('super_admin', 'instructor') OR profiles.is_admin = true)
    )
);

CREATE POLICY "Instructors can view quiz attempts" ON quiz_attempts FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (profiles.role IN ('super_admin', 'instructor') OR profiles.is_admin = true)
    )
);

CREATE POLICY "Instructors can view lab submissions" ON lab_submissions FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = auth.uid()
        AND (profiles.role IN ('super_admin', 'instructor') OR profiles.is_admin = true)
    )
);

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to get learning path with comprehensive stats
CREATE OR REPLACE FUNCTION get_learning_path_with_stats(path_id VARCHAR)
RETURNS TABLE (
    id VARCHAR,
    title VARCHAR,
    description TEXT,
    category VARCHAR,
    difficulty VARCHAR,
    phase VARCHAR,
    estimated_hours INTEGER,
    estimated_time INTEGER,
    prerequisites TEXT[],
    skills TEXT[],
    objectives TEXT[],
    modules_count BIGINT,
    total_sections BIGINT,
    completed_modules BIGINT,
    completed_sections BIGINT,
    user_progress_percentage DECIMAL,
    is_active BOOLEAN,
    is_featured BOOLEAN,
    metadata JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        lp.id,
        lp.title,
        lp.description,
        lp.category,
        lp.difficulty,
        lp.phase,
        lp.estimated_hours,
        lp.estimated_time,
        lp.prerequisites,
        lp.skills,
        lp.objectives,
        COUNT(DISTINCT lm.id) as modules_count,
        COUNT(DISTINCT ms.id) as total_sections,
        COALESCE(
            (SELECT COUNT(DISTINCT ump.module_id)
             FROM user_module_progress ump
             JOIN learning_modules lm2 ON ump.module_id = lm2.id
             WHERE lm2.learning_path_id = lp.id
             AND ump.user_id = auth.uid()
             AND ump.is_completed = true),
            0
        ) as completed_modules,
        COALESCE(
            (SELECT COUNT(DISTINCT usp.section_id)
             FROM user_section_progress usp
             JOIN module_sections ms2 ON usp.section_id = ms2.id
             JOIN learning_modules lm3 ON ms2.module_id = lm3.id
             WHERE lm3.learning_path_id = lp.id
             AND usp.user_id = auth.uid()
             AND usp.is_completed = true),
            0
        ) as completed_sections,
        COALESCE(
            (SELECT AVG(ump.progress_percentage)
             FROM user_module_progress ump
             JOIN learning_modules lm4 ON ump.module_id = lm4.id
             WHERE lm4.learning_path_id = lp.id
             AND ump.user_id = auth.uid()),
            0
        ) as user_progress_percentage,
        lp.is_active,
        lp.is_featured,
        lp.metadata
    FROM learning_paths lp
    LEFT JOIN learning_modules lm ON lp.id = lm.learning_path_id
    LEFT JOIN module_sections ms ON lm.id = ms.module_id
    WHERE lp.id = path_id
    GROUP BY lp.id, lp.title, lp.description, lp.category, lp.difficulty, lp.phase,
             lp.estimated_hours, lp.estimated_time, lp.prerequisites, lp.skills,
             lp.objectives, lp.is_active, lp.is_featured, lp.metadata;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
