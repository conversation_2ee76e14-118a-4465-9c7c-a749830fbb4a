-- =====================================================
-- OPERATING SYSTEMS CONTENT MIGRATION
-- =====================================================
-- Complete migration for all 30 operating systems modules
-- =====================================================

-- Insert Operating Systems Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'operating-systems',
    'Operating System Concepts for Cybersecurity',
    'Comprehensive learning path covering operating system security from fundamental concepts to advanced threat mitigation, designed for cybersecurity professionals.',
    'fundamentals',
    'beginner-to-advanced',
    'foundation',
    50,
    3000,
    ARRAY['Basic computer literacy', 'Understanding of networking concepts'],
    ARRAY['Operating Systems', 'Linux Security', 'Windows Security', 'System Hardening', 'Process Management', 'File Systems', 'Access Control'],
    ARRAY[
        'Understand operating system fundamentals and architecture',
        'Learn Linux and Windows security concepts and best practices',
        'Master system hardening techniques and security configurations',
        'Understand process and memory management from security perspective',
        'Learn file system security and access control mechanisms',
        'Master system monitoring and incident response techniques'
    ],
    30,
    true,
    false,
    3,
    '{"icon": "💻", "color": "#3498db", "featured_topics": ["Linux Security", "Windows Security", "System Hardening"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

-- Insert Operating Systems Modules
INSERT INTO learning_modules (
    id, source_id, learning_path_id, title, description, short_description, difficulty, 
    estimated_time, estimated_hours, objectives, topics, xp_reward, order_index, is_active, phase
) VALUES 
-- Module 1: Introduction to Operating Systems
('os-01', 'os-01', 'operating-systems', 
 'Introduction to Operating Systems',
 'Understand the fundamental concepts of operating systems and their role in cybersecurity.',
 'Understand the fundamental concepts of operating systems and their role in cybersecurity...',
 'beginner', 90, 2,
 ARRAY['Understand OS architecture', 'Learn kernel vs user space', 'Understand system calls', 'Learn security models'],
 ARRAY['OS Architecture', 'Kernel vs User Space', 'System Calls', 'Security Models', 'OS Types'],
 150, 1, true, 'foundation'),

-- Module 2: Linux Fundamentals
('os-02', 'os-02', 'operating-systems',
 'Linux Fundamentals',
 'Learn the basics of Linux operating system and command line interface.',
 'Learn the basics of Linux operating system and command line interface...',
 'beginner', 120, 2,
 ARRAY['Understand Linux distributions', 'Learn file system structure', 'Master command line basics', 'Learn package management'],
 ARRAY['Linux Distributions', 'File System', 'Command Line', 'Package Management', 'Shell Basics'],
 180, 2, true, 'foundation'),

-- Module 3: Windows Fundamentals
('os-03', 'os-03', 'operating-systems',
 'Windows Fundamentals',
 'Explore Windows operating system architecture and security features.',
 'Explore Windows operating system architecture and security features...',
 'beginner', 105, 2,
 ARRAY['Understand Windows architecture', 'Learn registry concepts', 'Understand services', 'Learn security features'],
 ARRAY['Windows Architecture', 'Registry', 'Services', 'Security Features', 'Active Directory Basics'],
 170, 3, true, 'foundation'),

-- Module 4: File Systems and Permissions
('os-04', 'os-04', 'operating-systems',
 'File Systems and Permissions',
 'Master file system concepts and permission models in different operating systems.',
 'Master file system concepts and permission models in different operating systems...',
 'beginner', 135, 2,
 ARRAY['Understand file system types', 'Learn permission models', 'Understand access control', 'Learn file attributes'],
 ARRAY['File System Types', 'Permissions', 'Access Control', 'File Attributes', 'ACLs'],
 200, 4, true, 'foundation'),

-- Module 5: Process and Memory Management
('os-05', 'os-05', 'operating-systems',
 'Process and Memory Management',
 'Understand how operating systems manage processes and memory from a security perspective.',
 'Understand how operating systems manage processes and memory from a security perspective...',
 'intermediate', 150, 3,
 ARRAY['Understand process lifecycle', 'Learn memory management', 'Understand virtual memory', 'Learn process security'],
 ARRAY['Process Lifecycle', 'Memory Management', 'Virtual Memory', 'Process Security', 'Thread Management'],
 220, 5, true, 'foundation'),

-- Module 6: Linux Command Line Mastery
('os-06', 'os-06', 'operating-systems',
 'Linux Command Line Mastery',
 'Master advanced Linux command line techniques for system administration and security.',
 'Master advanced Linux command line techniques for system administration and security...',
 'intermediate', 180, 3,
 ARRAY['Master advanced commands', 'Learn text processing', 'Understand scripting basics', 'Learn system monitoring'],
 ARRAY['Advanced Commands', 'Text Processing', 'Scripting Basics', 'System Monitoring', 'Log Analysis'],
 250, 6, true, 'foundation'),

-- Module 7: Windows PowerShell for Security
('os-07', 'os-07', 'operating-systems',
 'Windows PowerShell for Security',
 'Learn PowerShell for Windows system administration and security operations.',
 'Learn PowerShell for Windows system administration and security operations...',
 'intermediate', 165, 3,
 ARRAY['Understand PowerShell basics', 'Learn cmdlets and modules', 'Understand scripting', 'Learn security applications'],
 ARRAY['PowerShell Basics', 'Cmdlets', 'Scripting', 'Security Applications', 'Remote Management'],
 230, 7, true, 'foundation'),

-- Module 8: System Hardening Fundamentals
('os-08', 'os-08', 'operating-systems',
 'System Hardening Fundamentals',
 'Learn essential system hardening techniques for both Linux and Windows systems.',
 'Learn essential system hardening techniques for both Linux and Windows systems...',
 'intermediate', 135, 2,
 ARRAY['Understand hardening principles', 'Learn configuration management', 'Understand baseline security', 'Learn compliance frameworks'],
 ARRAY['Hardening Principles', 'Configuration Management', 'Baseline Security', 'Compliance', 'Security Benchmarks'],
 200, 8, true, 'foundation'),

-- Module 9: Linux Security Configuration
('os-09', 'os-09', 'operating-systems',
 'Linux Security Configuration',
 'Master Linux-specific security configurations and hardening techniques.',
 'Master Linux-specific security configurations and hardening techniques...',
 'intermediate', 150, 3,
 ARRAY['Learn Linux security modules', 'Understand SELinux/AppArmor', 'Learn firewall configuration', 'Understand secure boot'],
 ARRAY['Linux Security Modules', 'SELinux', 'AppArmor', 'Firewall Configuration', 'Secure Boot'],
 220, 9, true, 'intermediate'),

-- Module 10: Windows Security Configuration
('os-10', 'os-10', 'operating-systems',
 'Windows Security Configuration',
 'Master Windows-specific security configurations and Group Policy management.',
 'Master Windows-specific security configurations and Group Policy management...',
 'intermediate', 165, 3,
 ARRAY['Learn Group Policy', 'Understand Windows Defender', 'Learn BitLocker', 'Understand Windows firewall'],
 ARRAY['Group Policy', 'Windows Defender', 'BitLocker', 'Windows Firewall', 'Security Templates'],
 230, 10, true, 'intermediate'),

-- Module 11: User and Access Management
('os-11', 'os-11', 'operating-systems',
 'User and Access Management',
 'Learn comprehensive user and access management across different operating systems.',
 'Learn comprehensive user and access management across different operating systems...',
 'intermediate', 120, 2,
 ARRAY['Understand user management', 'Learn privilege escalation prevention', 'Understand role-based access', 'Learn account monitoring'],
 ARRAY['User Management', 'Privilege Escalation', 'Role-based Access', 'Account Monitoring', 'Password Policies'],
 180, 11, true, 'intermediate'),

-- Module 12: System Monitoring and Logging
('os-12', 'os-12', 'operating-systems',
 'System Monitoring and Logging',
 'Master system monitoring techniques and log management for security purposes.',
 'Master system monitoring techniques and log management for security purposes...',
 'intermediate', 135, 2,
 ARRAY['Learn system monitoring tools', 'Understand log management', 'Learn performance monitoring', 'Understand alerting'],
 ARRAY['System Monitoring', 'Log Management', 'Performance Monitoring', 'Alerting', 'SIEM Integration'],
 200, 12, true, 'intermediate'),

-- Module 13: Network Services Security
('os-13', 'os-13', 'operating-systems',
 'Network Services Security',
 'Learn to secure network services running on operating systems.',
 'Learn to secure network services running on operating systems...',
 'intermediate', 150, 3,
 ARRAY['Understand service security', 'Learn service hardening', 'Understand network security', 'Learn service monitoring'],
 ARRAY['Service Security', 'Service Hardening', 'Network Security', 'Service Monitoring', 'Port Security'],
 220, 13, true, 'intermediate'),

-- Module 14: Virtualization Security
('os-14', 'os-14', 'operating-systems',
 'Virtualization Security',
 'Understand security considerations in virtualized environments.',
 'Understand security considerations in virtualized environments...',
 'advanced', 120, 2,
 ARRAY['Understand virtualization concepts', 'Learn hypervisor security', 'Understand VM isolation', 'Learn container security'],
 ARRAY['Virtualization Concepts', 'Hypervisor Security', 'VM Isolation', 'Container Security', 'Virtual Networking'],
 180, 14, true, 'intermediate'),

-- Module 15: Incident Response on Operating Systems
('os-15', 'os-15', 'operating-systems',
 'Incident Response on Operating Systems',
 'Learn incident response techniques specific to operating system environments.',
 'Learn incident response techniques specific to operating system environments...',
 'advanced', 165, 3,
 ARRAY['Learn OS-specific IR', 'Understand evidence collection', 'Learn forensics techniques', 'Understand malware analysis'],
 ARRAY['OS-specific IR', 'Evidence Collection', 'Forensics Techniques', 'Malware Analysis', 'Timeline Analysis'],
 230, 15, true, 'intermediate'),

-- Module 16: Advanced Linux Security
('os-16', 'os-16', 'operating-systems',
 'Advanced Linux Security',
 'Master advanced Linux security concepts and enterprise-level configurations.',
 'Master advanced Linux security concepts and enterprise-level configurations...',
 'advanced', 180, 3,
 ARRAY['Learn advanced SELinux', 'Understand kernel security', 'Learn security automation', 'Understand compliance'],
 ARRAY['Advanced SELinux', 'Kernel Security', 'Security Automation', 'Compliance', 'Enterprise Security'],
 250, 16, true, 'advanced'),

-- Module 17: Advanced Windows Security
('os-17', 'os-17', 'operating-systems',
 'Advanced Windows Security',
 'Master advanced Windows security features and enterprise configurations.',
 'Master advanced Windows security features and enterprise configurations...',
 'advanced', 195, 3,
 ARRAY['Learn advanced AD security', 'Understand Windows security features', 'Learn enterprise hardening', 'Understand threat protection'],
 ARRAY['Advanced AD Security', 'Windows Security Features', 'Enterprise Hardening', 'Threat Protection', 'Security Baselines'],
 270, 17, true, 'advanced'),

-- Module 18: Malware Analysis on Operating Systems
('os-18', 'os-18', 'operating-systems',
 'Malware Analysis on Operating Systems',
 'Learn malware analysis techniques specific to different operating systems.',
 'Learn malware analysis techniques specific to different operating systems...',
 'advanced', 210, 4,
 ARRAY['Learn OS-specific malware', 'Understand analysis techniques', 'Learn sandbox analysis', 'Understand behavioral analysis'],
 ARRAY['OS-specific Malware', 'Analysis Techniques', 'Sandbox Analysis', 'Behavioral Analysis', 'Reverse Engineering'],
 300, 18, true, 'advanced'),

-- Module 19: System Recovery and Backup
('os-19', 'os-19', 'operating-systems',
 'System Recovery and Backup',
 'Master system recovery techniques and backup strategies for security resilience.',
 'Master system recovery techniques and backup strategies for security resilience...',
 'advanced', 135, 2,
 ARRAY['Learn backup strategies', 'Understand disaster recovery', 'Learn system restoration', 'Understand business continuity'],
 ARRAY['Backup Strategies', 'Disaster Recovery', 'System Restoration', 'Business Continuity', 'Data Protection'],
 200, 19, true, 'advanced'),

-- Module 20: Performance Tuning and Optimization
('os-20', 'os-20', 'operating-systems',
 'Performance Tuning and Optimization',
 'Learn performance optimization techniques while maintaining security.',
 'Learn performance optimization techniques while maintaining security...',
 'advanced', 150, 3,
 ARRAY['Learn performance analysis', 'Understand optimization techniques', 'Learn resource management', 'Understand security impact'],
 ARRAY['Performance Analysis', 'Optimization Techniques', 'Resource Management', 'Security Impact', 'Monitoring Tools'],
 220, 20, true, 'advanced'),

-- Continue with remaining modules 21-30...
-- Module 21: Cloud Operating Systems
('os-21', 'os-21', 'operating-systems',
 'Cloud Operating Systems',
 'Understand operating system concepts in cloud environments.',
 'Understand operating system concepts in cloud environments...',
 'advanced', 120, 2,
 ARRAY['Understand cloud OS concepts', 'Learn container operating systems', 'Understand serverless', 'Learn cloud security'],
 ARRAY['Cloud OS Concepts', 'Container OS', 'Serverless', 'Cloud Security', 'Microservices'],
 180, 21, true, 'advanced'),

-- Module 22: Mobile Operating Systems Security
('os-22', 'os-22', 'operating-systems',
 'Mobile Operating Systems Security',
 'Learn security concepts for mobile operating systems (iOS, Android).',
 'Learn security concepts for mobile operating systems (iOS, Android)...',
 'advanced', 135, 2,
 ARRAY['Understand mobile OS security', 'Learn iOS security', 'Learn Android security', 'Understand mobile threats'],
 ARRAY['Mobile OS Security', 'iOS Security', 'Android Security', 'Mobile Threats', 'App Security'],
 200, 22, true, 'advanced'),

-- Module 23: Embedded Systems Security
('os-23', 'os-23', 'operating-systems',
 'Embedded Systems Security',
 'Understand security challenges in embedded and IoT operating systems.',
 'Understand security challenges in embedded and IoT operating systems...',
 'advanced', 105, 2,
 ARRAY['Understand embedded OS', 'Learn IoT security', 'Understand firmware security', 'Learn device hardening'],
 ARRAY['Embedded OS', 'IoT Security', 'Firmware Security', 'Device Hardening', 'Secure Boot'],
 170, 23, true, 'advanced'),

-- Module 24: Operating System Forensics
('os-24', 'os-24', 'operating-systems',
 'Operating System Forensics',
 'Master digital forensics techniques specific to operating systems.',
 'Master digital forensics techniques specific to operating systems...',
 'advanced', 180, 3,
 ARRAY['Learn OS forensics techniques', 'Understand artifact analysis', 'Learn timeline reconstruction', 'Understand evidence preservation'],
 ARRAY['OS Forensics', 'Artifact Analysis', 'Timeline Reconstruction', 'Evidence Preservation', 'Memory Forensics'],
 250, 24, true, 'advanced'),

-- Module 25: Automation and Scripting for Security
('os-25', 'os-25', 'operating-systems',
 'Automation and Scripting for Security',
 'Learn automation and scripting techniques for security operations.',
 'Learn automation and scripting techniques for security operations...',
 'advanced', 165, 3,
 ARRAY['Learn security automation', 'Understand scripting languages', 'Learn configuration management', 'Understand orchestration'],
 ARRAY['Security Automation', 'Scripting Languages', 'Configuration Management', 'Orchestration', 'DevSecOps'],
 230, 25, true, 'advanced'),

-- Module 26: Compliance and Auditing
('os-26', 'os-26', 'operating-systems',
 'Compliance and Auditing',
 'Understand compliance requirements and auditing techniques for operating systems.',
 'Understand compliance requirements and auditing techniques for operating systems...',
 'advanced', 120, 2,
 ARRAY['Understand compliance frameworks', 'Learn auditing techniques', 'Understand documentation', 'Learn remediation'],
 ARRAY['Compliance Frameworks', 'Auditing Techniques', 'Documentation', 'Remediation', 'Risk Assessment'],
 180, 26, true, 'advanced'),

-- Module 27: Advanced Threat Protection
('os-27', 'os-27', 'operating-systems',
 'Advanced Threat Protection',
 'Learn advanced threat protection techniques for operating systems.',
 'Learn advanced threat protection techniques for operating systems...',
 'advanced', 150, 3,
 ARRAY['Learn threat protection', 'Understand behavioral analysis', 'Learn machine learning applications', 'Understand zero-day protection'],
 ARRAY['Threat Protection', 'Behavioral Analysis', 'Machine Learning', 'Zero-day Protection', 'Advanced Persistent Threats'],
 220, 27, true, 'advanced'),

-- Module 28: Secure Development on Operating Systems
('os-28', 'os-28', 'operating-systems',
 'Secure Development on Operating Systems',
 'Learn secure development practices for operating system environments.',
 'Learn secure development practices for operating system environments...',
 'advanced', 135, 2,
 ARRAY['Learn secure coding', 'Understand OS APIs', 'Learn security testing', 'Understand vulnerability assessment'],
 ARRAY['Secure Coding', 'OS APIs', 'Security Testing', 'Vulnerability Assessment', 'Code Review'],
 200, 28, true, 'advanced'),

-- Module 29: Enterprise Operating System Management
('os-29', 'os-29', 'operating-systems',
 'Enterprise Operating System Management',
 'Master enterprise-level operating system management and security.',
 'Master enterprise-level operating system management and security...',
 'advanced', 165, 3,
 ARRAY['Learn enterprise management', 'Understand scalability', 'Learn centralized management', 'Understand enterprise security'],
 ARRAY['Enterprise Management', 'Scalability', 'Centralized Management', 'Enterprise Security', 'Policy Management'],
 230, 29, true, 'advanced'),

-- Module 30: Future of Operating System Security
('os-30', 'os-30', 'operating-systems',
 'Future of Operating System Security',
 'Explore emerging trends and future directions in operating system security.',
 'Explore emerging trends and future directions in operating system security...',
 'advanced', 90, 2,
 ARRAY['Understand emerging trends', 'Learn future technologies', 'Understand quantum computing impact', 'Learn adaptive security'],
 ARRAY['Emerging Trends', 'Future Technologies', 'Quantum Computing', 'Adaptive Security', 'AI Integration'],
 150, 30, true, 'advanced')

ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    estimated_time = EXCLUDED.estimated_time,
    updated_at = CURRENT_TIMESTAMP;

-- Success message
SELECT 'Operating Systems migration completed successfully! 30 modules inserted.' as status;
