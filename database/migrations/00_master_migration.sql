-- =====================================================
-- MASTER MIGRATION SCRIPT
-- =====================================================
-- This script runs all learning content migrations in order
-- Execute this in your Supabase SQL Editor to migrate all content
-- =====================================================

-- First, ensure the comprehensive schema is in place
-- (Run comprehensive_learning_schema.sql first if not already done)

-- =====================================================
-- MIGRATION EXECUTION LOG
-- =====================================================
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    modules_migrated INTEGER DEFAULT 0
);

-- Function to log migration status
CREATE OR REPLACE FUNCTION log_migration(
    migration_name VARCHAR(255),
    migration_status VARCHAR(50),
    modules_count INTEGER DEFAULT 0,
    error_msg TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    IF migration_status = 'started' THEN
        INSERT INTO migration_log (migration_name, status, modules_migrated)
        VALUES (migration_name, migration_status, modules_count);
    ELSE
        UPDATE migration_log 
        SET status = migration_status,
            completed_at = CURRENT_TIMESTAMP,
            modules_migrated = modules_count,
            error_message = error_msg
        WHERE migration_name = migration_name AND status = 'started';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- EXECUTE ALL MIGRATIONS
-- =====================================================

-- Migration 1: Networking Fundamentals
SELECT log_migration('networking-fundamentals', 'started', 25);

-- Insert Networking Fundamentals Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'networking-fundamentals',
    'Networking Fundamentals',
    'Master the essential concepts of computer networking that form the foundation of cybersecurity. Learn about network protocols, architecture, and security principles.',
    'fundamentals',
    'beginner',
    'foundation',
    60,
    3600,
    ARRAY[]::TEXT[],
    ARRAY['Networking', 'TCP/IP', 'Network Security', 'OSI Model', 'Routing', 'Switching', 'VLANs', 'Firewalls'],
    ARRAY[
        'Understand fundamental networking concepts and terminology',
        'Master the OSI model and TCP/IP protocol suite',
        'Learn network topologies and infrastructure components',
        'Understand routing and switching concepts',
        'Master IP addressing and subnetting techniques',
        'Learn network security fundamentals and best practices',
        'Understand wireless networking and VPN technologies'
    ],
    25,
    true,
    true,
    1,
    '{"icon": "🌐", "color": "#4A5CBA", "featured_topics": ["OSI Model", "TCP/IP", "Network Security"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

SELECT log_migration('networking-fundamentals', 'completed', 25);

-- Migration 2: Blue Teaming
SELECT log_migration('blue-teaming', 'started', 50);

INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'blue-teaming',
    'Blue Teaming: Comprehensive Defensive Cybersecurity',
    'Master defensive cybersecurity operations with our comprehensive 50-module Blue Teaming curriculum. Learn threat detection, incident response, security monitoring, threat hunting, and strategic defense planning.',
    'defensive',
    'beginner-to-expert',
    'foundation',
    1800,
    108000,
    ARRAY['Basic understanding of computer networks', 'Fundamental knowledge of operating systems', 'Basic cybersecurity awareness'],
    ARRAY['Defensive Security', 'Incident Response', 'Threat Hunting', 'SIEM', 'Security Monitoring', 'Digital Forensics', 'Malware Analysis', 'Threat Intelligence'],
    ARRAY[
        'Master defensive cybersecurity fundamentals and methodologies',
        'Learn security monitoring and SIEM technologies',
        'Understand incident response procedures and best practices',
        'Develop threat hunting capabilities and techniques',
        'Master digital forensics and malware analysis',
        'Learn advanced security operations and automation'
    ],
    50,
    true,
    true,
    2,
    '{"icon": "🛡️", "color": "#88cc14", "featured_topics": ["SIEM", "Incident Response", "Threat Hunting"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

SELECT log_migration('blue-teaming', 'completed', 50);

-- Migration 3: Operating Systems
SELECT log_migration('operating-systems', 'started', 30);

INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'operating-systems',
    'Operating System Concepts for Cybersecurity',
    'Comprehensive learning path covering operating system security from fundamental concepts to advanced threat mitigation, designed for cybersecurity professionals.',
    'fundamentals',
    'beginner-to-advanced',
    'foundation',
    50,
    3000,
    ARRAY['Basic computer literacy', 'Understanding of networking concepts'],
    ARRAY['Operating Systems', 'Linux Security', 'Windows Security', 'System Hardening', 'Process Management', 'File Systems', 'Access Control'],
    ARRAY[
        'Understand operating system fundamentals and architecture',
        'Learn Linux and Windows security concepts and best practices',
        'Master system hardening techniques and security configurations',
        'Understand process and memory management from security perspective',
        'Learn file system security and access control mechanisms',
        'Master system monitoring and incident response techniques'
    ],
    30,
    true,
    false,
    3,
    '{"icon": "💻", "color": "#3498db", "featured_topics": ["Linux Security", "Windows Security", "System Hardening"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

SELECT log_migration('operating-systems', 'completed', 30);

-- Migration 4: Cloud Security
SELECT log_migration('cloud-security', 'started', 45);

INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'cloud-security',
    'Cloud Security',
    'Master cloud security across AWS, Azure, GCP, and multi-cloud environments. Learn cloud architecture security, identity management, compliance, and advanced threat protection.',
    'cloud-infrastructure',
    'beginner-to-expert',
    'intermediate',
    180,
    10800,
    ARRAY['Basic understanding of cloud computing concepts', 'Fundamental networking and security knowledge'],
    ARRAY['Cloud Security', 'AWS Security', 'Azure Security', 'GCP Security', 'Cloud Compliance', 'Container Security', 'Serverless Security', 'DevSecOps'],
    ARRAY[
        'Master cloud security fundamentals and best practices',
        'Learn AWS, Azure, and GCP security services and configurations',
        'Understand cloud compliance requirements and frameworks',
        'Master container and serverless security techniques',
        'Learn cloud incident response and forensics',
        'Understand multi-cloud security strategies'
    ],
    45,
    true,
    true,
    4,
    '{"icon": "☁️", "color": "#9b59b6", "featured_topics": ["AWS Security", "Azure Security", "Cloud Compliance"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

SELECT log_migration('cloud-security', 'completed', 45);

-- Migration 5: Threat Hunting
SELECT log_migration('threat-hunting', 'started', 24);

INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'threat-hunting',
    'Threat Hunting',
    'Master proactive threat hunting techniques, methodologies, and tools for advanced threat detection and response.',
    'defensive',
    'advanced',
    'advanced',
    120,
    7200,
    ARRAY['Advanced security monitoring knowledge', 'Understanding of threat intelligence', 'SIEM experience'],
    ARRAY['Threat Hunting', 'Behavioral Analysis', 'Anomaly Detection', 'Hunting Automation', 'Advanced Analytics'],
    ARRAY[
        'Master threat hunting methodologies',
        'Learn behavioral analysis techniques',
        'Understand anomaly detection methods',
        'Develop hunting automation skills',
        'Master advanced hunting tools'
    ],
    24,
    true,
    false,
    5,
    '{"icon": "🎯", "color": "#e67e22", "featured_topics": ["Hunting Methodology", "Behavioral Analysis", "Automation"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

SELECT log_migration('threat-hunting', 'completed', 24);

-- Migration 6: Bug Bounty
SELECT log_migration('bug-bounty', 'started', 20);

INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'bug-bounty',
    'Bug Bounty Hunting',
    'Master bug bounty hunting from reconnaissance to advanced exploitation techniques and responsible disclosure.',
    'offensive',
    'intermediate-to-advanced',
    'intermediate',
    60,
    3600,
    ARRAY['Web application security knowledge', 'Basic penetration testing skills'],
    ARRAY['Bug Bounty', 'Web Security', 'Mobile Security', 'API Testing', 'Vulnerability Research'],
    ARRAY[
        'Master bug bounty methodology',
        'Learn advanced web application testing',
        'Understand mobile security testing',
        'Master API security assessment',
        'Learn responsible disclosure practices'
    ],
    20,
    true,
    false,
    6,
    '{"icon": "🐛", "color": "#27ae60", "featured_topics": ["Web Security", "API Testing", "Mobile Security"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

SELECT log_migration('bug-bounty', 'completed', 20);

-- =====================================================
-- SAMPLE CONTENT ASSETS (SVGs)
-- =====================================================
INSERT INTO content_assets (asset_type, file_name, file_content, mime_type, alt_text, description, tags) VALUES
('svg', 'network_topology_star.svg', 
'<svg width="400" height="300" viewBox="0 0 400 300">
  <circle cx="200" cy="150" r="30" fill="#4A5CBA" stroke="#333" stroke-width="2"/>
  <text x="200" y="155" text-anchor="middle" fill="white" font-size="12">Switch</text>
  <circle cx="100" cy="80" r="20" fill="#88cc14" stroke="#333" stroke-width="2"/>
  <text x="100" y="85" text-anchor="middle" fill="black" font-size="10">PC1</text>
  <circle cx="300" cy="80" r="20" fill="#88cc14" stroke="#333" stroke-width="2"/>
  <text x="300" y="85" text-anchor="middle" fill="black" font-size="10">PC2</text>
  <circle cx="100" cy="220" r="20" fill="#88cc14" stroke="#333" stroke-width="2"/>
  <text x="100" y="225" text-anchor="middle" fill="black" font-size="10">PC3</text>
  <circle cx="300" cy="220" r="20" fill="#88cc14" stroke="#333" stroke-width="2"/>
  <text x="300" y="225" text-anchor="middle" fill="black" font-size="10">PC4</text>
  <line x1="120" y1="90" x2="180" y2="140" stroke="#667eea" stroke-width="3"/>
  <line x1="280" y1="90" x2="220" y2="140" stroke="#667eea" stroke-width="3"/>
  <line x1="120" y1="210" x2="180" y2="160" stroke="#667eea" stroke-width="3"/>
  <line x1="280" y1="210" x2="220" y2="160" stroke="#667eea" stroke-width="3"/>
</svg>',
'image/svg+xml',
'Star topology network diagram',
'Interactive star topology diagram showing central switch connected to multiple PCs',
ARRAY['networking', 'topology', 'star', 'diagram']
),

('svg', 'osi_model_layers.svg',
'<svg width="300" height="400" viewBox="0 0 300 400">
  <rect x="50" y="50" width="200" height="40" fill="#e74c3c" stroke="#333" stroke-width="2"/>
  <text x="150" y="75" text-anchor="middle" fill="white" font-weight="bold">7. Application</text>
  <rect x="50" y="90" width="200" height="40" fill="#f39c12" stroke="#333" stroke-width="2"/>
  <text x="150" y="115" text-anchor="middle" fill="white" font-weight="bold">6. Presentation</text>
  <rect x="50" y="130" width="200" height="40" fill="#f1c40f" stroke="#333" stroke-width="2"/>
  <text x="150" y="155" text-anchor="middle" fill="black" font-weight="bold">5. Session</text>
  <rect x="50" y="170" width="200" height="40" fill="#27ae60" stroke="#333" stroke-width="2"/>
  <text x="150" y="195" text-anchor="middle" fill="white" font-weight="bold">4. Transport</text>
  <rect x="50" y="210" width="200" height="40" fill="#3498db" stroke="#333" stroke-width="2"/>
  <text x="150" y="235" text-anchor="middle" fill="white" font-weight="bold">3. Network</text>
  <rect x="50" y="250" width="200" height="40" fill="#9b59b6" stroke="#333" stroke-width="2"/>
  <text x="150" y="275" text-anchor="middle" fill="white" font-weight="bold">2. Data Link</text>
  <rect x="50" y="290" width="200" height="40" fill="#34495e" stroke="#333" stroke-width="2"/>
  <text x="150" y="315" text-anchor="middle" fill="white" font-weight="bold">1. Physical</text>
</svg>',
'image/svg+xml',
'OSI Model seven layers diagram',
'Interactive OSI model diagram showing all seven layers with color coding',
ARRAY['networking', 'osi', 'model', 'layers', 'diagram']
) ON CONFLICT DO NOTHING;

-- =====================================================
-- MIGRATION SUMMARY
-- =====================================================
SELECT 
    'MIGRATION COMPLETED SUCCESSFULLY!' as status,
    COUNT(*) as total_learning_paths,
    SUM(modules_count) as total_modules
FROM learning_paths 
WHERE is_active = true;

-- Show migration log
SELECT 
    migration_name,
    status,
    modules_migrated,
    completed_at
FROM migration_log 
ORDER BY completed_at DESC;

-- =====================================================
-- NEXT STEPS
-- =====================================================
/*
NEXT STEPS:
1. ✅ Database schema created
2. ✅ Learning paths migrated
3. ✅ Sample assets added
4. 🔄 Run individual module migrations:
   - 01_networking_fundamentals_migration.sql
   - 02_blue_teaming_migration.sql  
   - 03_operating_systems_migration.sql
   - 04_cloud_security_migration.sql
5. 🔄 Use ContentMigrationDashboard for file uploads
6. 🔄 Integrate with Payload CMS
7. 🔄 Test content loading in application

ADMIN ACCESS:
- Route: /super-admin/content-migration
- Component: ContentMigrationDashboard
*/
