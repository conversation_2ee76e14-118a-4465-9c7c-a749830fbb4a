-- =====================================================
-- POPULATE NETWORKING FUNDAMENTALS CONTENT
-- =====================================================
-- This script populates the database with enhanced
-- networking fundamentals content including stories,
-- simulations, and interactive elements
-- =====================================================

-- Insert the first enhanced module: Introduction to Computer Networks
INSERT INTO learning_modules (
    id, source_id, learning_path_id, title, description, difficulty, 
    estimated_time, objectives, prerequisites, xp_reward, order_index, is_active
) VALUES (
    'module-1', 'nf-1', 'networking-fundamentals',
    'Introduction to Computer Networks',
    'Learn the fundamental concepts of computer networks and their importance in modern computing and cybersecurity.',
    'beginner',
    60,
    ARRAY[
        'Understand what a computer network is and why it''s important in today''s digital world',
        'Identify and compare different types of networks (LAN, WAN, MAN, PAN)',
        'Explain the basic components and devices that make up a network',
        'Describe different network topologies and their advantages',
        'Recognize the benefits and challenges of networking in various contexts'
    ],
    ARRAY[]::TEXT[],
    100,
    1,
    true
);

-- Insert sections for Module 1
-- Section 1: What is a Computer Network? (with story and interactive elements)
INSERT INTO module_sections (
    module_id, title, content, content_type, interactive_data, order_index, estimated_time, is_active
) VALUES (
    'module-1',
    'What is a Computer Network?',
    '<div class="learning-module-story">
        <h3>📖 Story: The Digital Neighborhood</h3>
        <p>Imagine Sarah, a cybersecurity analyst, explaining networks to her nephew Alex. "Think of a computer network like our neighborhood," she says, pointing out the window. "Each house is like a computer or device, and the streets connecting them are like network cables or Wi-Fi signals. Just like people visit each other''s houses to share things or have conversations, computers use networks to share files, send messages, and work together."</p>
        
        <p>Alex''s eyes light up. "So when I play online games with my friends, we''re all connected through this digital neighborhood?" Sarah nods. "Exactly! And just like our real neighborhood has rules - like stop signs and speed limits - computer networks have protocols that help devices communicate safely and efficiently."</p>
    </div>

    <h2>What is a Computer Network?</h2>
    <p>A computer network is a group of two or more devices that are connected to share resources and communicate with each other. Think of a network like a neighborhood where houses (devices) are connected by streets (connections) that allow people (data) to travel between them.</p>

    <div class="interactive-diagram">
        <h3>🌐 Interactive Network Diagram</h3>
        <svg width="600" height="400" viewBox="0 0 600 400">
            <!-- Router in center -->
            <rect x="275" y="175" width="50" height="50" fill="#4A5CBA" rx="5" class="network-node"/>
            <text x="300" y="205" text-anchor="middle" fill="white" font-size="12">Router</text>
            
            <!-- Laptop -->
            <rect x="150" y="100" width="60" height="40" fill="#88cc14" rx="5" class="network-node"/>
            <text x="180" y="125" text-anchor="middle" fill="black" font-size="10">Laptop</text>
            
            <!-- Desktop -->
            <rect x="400" y="100" width="60" height="40" fill="#88cc14" rx="5" class="network-node"/>
            <text x="430" y="125" text-anchor="middle" fill="black" font-size="10">Desktop</text>
            
            <!-- Connection lines with animation -->
            <line x1="210" y1="120" x2="275" y2="190" stroke="#667eea" stroke-width="3" class="data-flow-animation"/>
            <line x1="400" y1="120" x2="325" y2="190" stroke="#667eea" stroke-width="3" class="data-flow-animation"/>
        </svg>
        <p><em>Click on any device to see it light up! The animated lines show data flowing through the network.</em></p>
    </div>

    <div class="think-like-section">
        <h3>🧠 Think Like a Cybersecurity Professional</h3>
        <p><strong>Security Perspective:</strong> Every device on a network is both an asset to protect and a potential entry point for attackers. As a cybersecurity professional, you need to think about:</p>
        <ul>
            <li><strong>Asset Inventory:</strong> What devices are on your network? You can''t protect what you don''t know exists.</li>
            <li><strong>Attack Surface:</strong> Each connected device increases your attack surface - the total number of ways an attacker could potentially get in.</li>
            <li><strong>Network Segmentation:</strong> Should all devices have access to everything? (Spoiler: No!)</li>
            <li><strong>Monitoring:</strong> How do you know if something suspicious is happening on your network?</li>
        </ul>
        <p><em>"In cybersecurity, your network is only as secure as its weakest link - and that could be any connected device."</em></p>
    </div>',
    'text',
    NULL,
    1,
    20,
    true
);

-- Section 2: Network Simulation Lab (Interactive)
INSERT INTO module_sections (
    module_id, title, content, content_type, interactive_data, order_index, estimated_time, is_active
) VALUES (
    'module-1',
    'Network Simulation Lab',
    '<div class="simulation-section">
        <h3>⚡ Interactive Network Simulation</h3>
        <p>In this simulation, you''ll build a simple network step by step and see how data flows through it. This will help you understand the fundamental concepts before we dive deeper into network types and topologies.</p>
    </div>',
    'interactive',
    '{
        "type": "network_simulation",
        "title": "Build Your First Network",
        "description": "Experience how different network components work together in this interactive simulation.",
        "steps": [
            {
                "id": 1,
                "title": "Choose Your Internet Connection",
                "description": "First, select how your home connects to the internet:",
                "options": [
                    {"value": "cable", "label": "Cable Modem (Fast, widely available)"},
                    {"value": "fiber", "label": "Fiber Optic (Fastest, limited availability)"},
                    {"value": "dsl", "label": "DSL (Moderate speed, uses phone lines)"},
                    {"value": "satellite", "label": "Satellite (Available everywhere, higher latency)"}
                ]
            },
            {
                "id": 2,
                "title": "Add Your Router",
                "description": "Now add a router to create your local network",
                "diagram": {
                    "nodes": [
                        {"id": "internet", "label": "🌐 Internet", "x": 50, "y": 20},
                        {"id": "modem", "label": "📡 Modem", "x": 200, "y": 20},
                        {"id": "router", "label": "🔀 Router", "x": 350, "y": 20}
                    ],
                    "connections": [
                        {"from": "internet", "to": "modem"},
                        {"from": "modem", "to": "router"}
                    ]
                }
            },
            {
                "id": 3,
                "title": "Connect Your Devices",
                "description": "Add devices to your network",
                "devices": [
                    {"type": "laptop", "label": "💻 Laptop"},
                    {"type": "phone", "label": "📱 Smartphone"},
                    {"type": "tv", "label": "📺 Smart TV"},
                    {"type": "printer", "label": "🖨️ Printer"}
                ]
            },
            {
                "id": 4,
                "title": "Test Your Network",
                "description": "Now let''s see how data flows through your network",
                "simulations": [
                    {"action": "browse_web", "label": "Browse a Website"},
                    {"action": "print_file", "label": "Print a Document"},
                    {"action": "stream_video", "label": "Stream a Video"}
                ]
            }
        ],
        "tools": [
            {
                "name": "Network Scanner",
                "description": "Discover devices on your network",
                "preview": ["*********** - Router", "************* - Laptop", "************* - Phone"]
            },
            {
                "name": "Traffic Monitor",
                "description": "See what data is flowing through your network",
                "preview": ["HTTP: 45% (Web browsing)", "HTTPS: 35% (Secure web)", "DNS: 15% (Name resolution)"]
            },
            {
                "name": "Security Monitor",
                "description": "Watch for suspicious network activity",
                "preview": ["✅ No threats detected", "Monitoring 5 devices", "Last scan: 2 minutes ago"]
            }
        ]
    }',
    2,
    25,
    true
);

-- Section 3: Types of Networks (Enhanced with examples)
INSERT INTO module_sections (
    module_id, title, content, content_type, interactive_data, order_index, estimated_time, is_active
) VALUES (
    'module-1',
    'Types of Networks',
    '<h2>Types of Computer Networks</h2>
    <p>Networks come in different sizes and serve different purposes. They''re often classified by their geographical coverage area:</p>

    <h3>Personal Area Network (PAN)</h3>
    <p>A PAN is the smallest type of network, covering just the area around a single person (typically within a range of about 10 meters).</p>
    
    <div class="configuration-example">
        <h4>Real-World Example: Your Morning Routine</h4>
        <p>When you wake up and your smartwatch syncs with your phone via Bluetooth, you''re using a PAN. The watch, phone, and maybe your wireless earbuds all form a small personal network around you.</p>
    </div>

    <h3>Local Area Network (LAN)</h3>
    <p>A LAN connects devices within a limited area such as a home, school, office building, or campus.</p>
    
    <div class="feature-cards">
        <div class="feature-card">
            <h3>Home LAN Example</h3>
            <p>Your home Wi-Fi network connecting laptops, phones, smart TV, and gaming console through your router.</p>
        </div>
        <div class="feature-card">
            <h3>Office LAN Example</h3>
            <p>Company network connecting employee computers, printers, and servers within the office building.</p>
        </div>
    </div>

    <h3>Wide Area Network (WAN)</h3>
    <p>A WAN spans a large geographical area, often connecting multiple LANs across cities, countries, or even continents.</p>
    
    <div class="think-like-section">
        <h3>🧠 Think Like a Cybersecurity Professional</h3>
        <p><strong>Security Implications by Network Type:</strong></p>
        <ul>
            <li><strong>PAN Security:</strong> Short range but often unencrypted. Bluetooth vulnerabilities can be exploited.</li>
            <li><strong>LAN Security:</strong> You control the infrastructure, but insider threats are a concern.</li>
            <li><strong>WAN Security:</strong> Data travels through untrusted networks. Encryption is critical.</li>
        </ul>
        <p><em>"The larger the network, the more attack vectors you need to consider."</em></p>
    </div>',
    'text',
    NULL,
    3,
    15,
    true
);

-- Section 4: Knowledge Check Quiz
INSERT INTO module_sections (
    module_id, title, content, content_type, interactive_data, order_index, estimated_time, is_active
) VALUES (
    'module-1',
    'Knowledge Check',
    '<h2>Test Your Understanding</h2>
    <p>Let''s see how well you''ve grasped the fundamental concepts of computer networks.</p>',
    'quiz',
    '{
        "questions": [
            {
                "question": "What is the primary purpose of a computer network?",
                "options": [
                    "To increase the cost of computing",
                    "To share resources and enable communication between devices",
                    "To make computers run faster",
                    "To prevent unauthorized access to data"
                ],
                "correctAnswer": 1,
                "explanation": "The primary purpose of a computer network is to share resources (like files, printers, and internet connections) and enable communication between connected devices."
            },
            {
                "question": "From a cybersecurity perspective, what does each connected device represent?",
                "options": [
                    "Only an asset to protect",
                    "Only a potential entry point for attackers",
                    "Both an asset to protect and a potential entry point for attackers",
                    "Neither an asset nor a security concern"
                ],
                "correctAnswer": 2,
                "explanation": "In cybersecurity, every device on a network is both an asset that needs protection and a potential entry point that attackers could exploit to gain access to the network."
            },
            {
                "question": "Which network type would typically connect devices within a single building?",
                "options": [
                    "WAN (Wide Area Network)",
                    "MAN (Metropolitan Area Network)",
                    "LAN (Local Area Network)",
                    "PAN (Personal Area Network)"
                ],
                "correctAnswer": 2,
                "explanation": "A LAN (Local Area Network) typically connects devices within a limited area like a single building, home, or campus."
            }
        ]
    }',
    4,
    10,
    true
);

-- Update the module count for the learning path
UPDATE learning_paths 
SET modules_count = (
    SELECT COUNT(*) 
    FROM learning_modules 
    WHERE learning_path_id = 'networking-fundamentals'
) 
WHERE id = 'networking-fundamentals';

-- Success message
SELECT 'Enhanced networking fundamentals content populated successfully!' as status;
