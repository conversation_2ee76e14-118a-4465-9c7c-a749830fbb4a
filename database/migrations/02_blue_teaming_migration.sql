-- =====================================================
-- BLUE TEAMING CONTENT MIGRATION
-- =====================================================
-- Complete migration for all 50 blue teaming modules
-- =====================================================

-- Insert Blue Teaming Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'blue-teaming',
    'Blue Teaming: Comprehensive Defensive Cybersecurity',
    'Master defensive cybersecurity operations with our comprehensive 50-module Blue Teaming curriculum. Learn threat detection, incident response, security monitoring, threat hunting, and strategic defense planning.',
    'defensive',
    'beginner-to-expert',
    'foundation',
    1800,
    108000,
    ARRAY['Basic understanding of computer networks', 'Fundamental knowledge of operating systems', 'Basic cybersecurity awareness'],
    ARRAY['Defensive Security', 'Incident Response', 'Threat Hunting', 'SIEM', 'Security Monitoring', 'Digital Forensics', 'Malware Analysis', 'Threat Intelligence'],
    ARRAY[
        'Master defensive cybersecurity fundamentals and methodologies',
        'Learn security monitoring and SIEM technologies',
        'Understand incident response procedures and best practices',
        'Develop threat hunting capabilities and techniques',
        'Master digital forensics and malware analysis',
        'Learn advanced security operations and automation'
    ],
    50,
    true,
    true,
    2,
    '{"icon": "🛡️", "color": "#88cc14", "featured_topics": ["SIEM", "Incident Response", "Threat Hunting"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

-- Insert Blue Teaming Modules (Foundation Phase - Modules 1-15)
INSERT INTO learning_modules (
    id, source_id, learning_path_id, title, description, short_description, difficulty, 
    estimated_time, estimated_hours, objectives, topics, xp_reward, order_index, is_active, phase
) VALUES 
-- Module 1: Introduction to Blue Teaming
('bt-01', 'bt-01', 'blue-teaming', 
 'Introduction to Blue Teaming',
 'Learn the fundamentals of defensive cybersecurity operations and the role of blue teams in protecting organizations.',
 'Learn the fundamentals of defensive cybersecurity operations and the role of blue teams...',
 'beginner', 90, 2,
 ARRAY['Understand blue team role and responsibilities', 'Learn defense strategies', 'Understand security operations', 'Learn team structure and coordination'],
 ARRAY['Blue Team Role', 'Defense Strategies', 'Security Operations', 'Team Structure', 'Cybersecurity Framework'],
 150, 1, true, 'foundation'),

-- Module 2: Cybersecurity Fundamentals for Blue Teams
('bt-02', 'bt-02', 'blue-teaming',
 'Cybersecurity Fundamentals for Blue Teams',
 'Master the core cybersecurity concepts essential for effective defensive operations.',
 'Master the core cybersecurity concepts essential for effective defensive operations...',
 'beginner', 120, 2,
 ARRAY['Understand CIA triad', 'Learn threat landscape', 'Understand risk management', 'Learn compliance frameworks'],
 ARRAY['CIA Triad', 'Threat Landscape', 'Risk Management', 'Compliance Frameworks', 'Security Controls'],
 180, 2, true, 'foundation'),

-- Module 3: Network Security Monitoring
('bt-03', 'bt-03', 'blue-teaming',
 'Network Security Monitoring',
 'Learn to monitor network traffic and detect suspicious activities using various tools and techniques.',
 'Learn to monitor network traffic and detect suspicious activities using various tools...',
 'beginner', 135, 2,
 ARRAY['Understand network monitoring concepts', 'Learn traffic analysis', 'Understand anomaly detection', 'Learn monitoring tools'],
 ARRAY['Network Monitoring', 'Traffic Analysis', 'Anomaly Detection', 'IDS/IPS', 'Network Forensics'],
 200, 3, true, 'foundation'),

-- Module 4: Log Analysis and Management
('bt-04', 'bt-04', 'blue-teaming',
 'Log Analysis and Management',
 'Master the art of log collection, analysis, and correlation for security monitoring.',
 'Master the art of log collection, analysis, and correlation for security monitoring...',
 'beginner', 105, 2,
 ARRAY['Understand log types and sources', 'Learn log collection methods', 'Master log analysis techniques', 'Understand log correlation'],
 ARRAY['Log Types', 'Log Collection', 'Log Analysis', 'Log Correlation', 'Centralized Logging'],
 170, 4, true, 'foundation'),

-- Module 5: SIEM Fundamentals
('bt-05', 'bt-05', 'blue-teaming',
 'SIEM Fundamentals',
 'Introduction to Security Information and Event Management systems and their role in security operations.',
 'Introduction to Security Information and Event Management systems and their role...',
 'beginner', 150, 3,
 ARRAY['Understand SIEM concepts', 'Learn SIEM architecture', 'Understand event correlation', 'Learn SIEM deployment'],
 ARRAY['SIEM Concepts', 'SIEM Architecture', 'Event Correlation', 'SIEM Deployment', 'Use Cases'],
 220, 5, true, 'foundation'),

-- Module 6: Incident Response Fundamentals
('bt-06', 'bt-06', 'blue-teaming',
 'Incident Response Fundamentals',
 'Learn the structured approach to handling and managing security incidents.',
 'Learn the structured approach to handling and managing security incidents...',
 'beginner', 120, 2,
 ARRAY['Understand incident response process', 'Learn incident classification', 'Understand containment strategies', 'Learn documentation requirements'],
 ARRAY['Incident Response Process', 'Incident Classification', 'Containment', 'Documentation', 'Communication'],
 180, 6, true, 'foundation'),

-- Module 7: Threat Intelligence Basics
('bt-07', 'bt-07', 'blue-teaming',
 'Threat Intelligence Basics',
 'Introduction to threat intelligence and its application in defensive operations.',
 'Introduction to threat intelligence and its application in defensive operations...',
 'beginner', 90, 2,
 ARRAY['Understand threat intelligence concepts', 'Learn intelligence sources', 'Understand IOCs and TTPs', 'Learn intelligence integration'],
 ARRAY['Threat Intelligence', 'Intelligence Sources', 'IOCs', 'TTPs', 'Intelligence Integration'],
 150, 7, true, 'foundation'),

-- Module 8: Vulnerability Management
('bt-08', 'bt-08', 'blue-teaming',
 'Vulnerability Management',
 'Learn to identify, assess, and manage vulnerabilities in organizational systems.',
 'Learn to identify, assess, and manage vulnerabilities in organizational systems...',
 'beginner', 105, 2,
 ARRAY['Understand vulnerability lifecycle', 'Learn vulnerability scanning', 'Understand risk assessment', 'Learn patch management'],
 ARRAY['Vulnerability Lifecycle', 'Vulnerability Scanning', 'Risk Assessment', 'Patch Management', 'Remediation'],
 170, 8, true, 'foundation'),

-- Module 9: Endpoint Security and Monitoring
('bt-09', 'bt-09', 'blue-teaming',
 'Endpoint Security and Monitoring',
 'Master endpoint protection and monitoring techniques for comprehensive security coverage.',
 'Master endpoint protection and monitoring techniques for comprehensive security coverage...',
 'intermediate', 120, 2,
 ARRAY['Understand endpoint security concepts', 'Learn EDR solutions', 'Understand behavioral analysis', 'Learn endpoint forensics'],
 ARRAY['Endpoint Security', 'EDR Solutions', 'Behavioral Analysis', 'Endpoint Forensics', 'Host-based Monitoring'],
 180, 9, true, 'foundation'),

-- Module 10: Security Operations Center (SOC) Operations
('bt-10', 'bt-10', 'blue-teaming',
 'Security Operations Center (SOC) Operations',
 'Learn the structure, processes, and best practices for effective SOC operations.',
 'Learn the structure, processes, and best practices for effective SOC operations...',
 'intermediate', 135, 2,
 ARRAY['Understand SOC structure', 'Learn SOC processes', 'Understand analyst roles', 'Learn SOC metrics'],
 ARRAY['SOC Structure', 'SOC Processes', 'Analyst Roles', 'SOC Metrics', 'Shift Operations'],
 200, 10, true, 'foundation'),

-- Module 11: Digital Forensics Fundamentals
('bt-11', 'bt-11', 'blue-teaming',
 'Digital Forensics Fundamentals',
 'Introduction to digital forensics principles and techniques for incident investigation.',
 'Introduction to digital forensics principles and techniques for incident investigation...',
 'intermediate', 150, 3,
 ARRAY['Understand forensics principles', 'Learn evidence collection', 'Understand chain of custody', 'Learn forensics tools'],
 ARRAY['Forensics Principles', 'Evidence Collection', 'Chain of Custody', 'Forensics Tools', 'Legal Considerations'],
 220, 11, true, 'foundation'),

-- Module 12: Malware Analysis Basics
('bt-12', 'bt-12', 'blue-teaming',
 'Malware Analysis Basics',
 'Learn fundamental techniques for analyzing malicious software and understanding threats.',
 'Learn fundamental techniques for analyzing malicious software and understanding threats...',
 'intermediate', 180, 3,
 ARRAY['Understand malware types', 'Learn static analysis', 'Understand dynamic analysis', 'Learn analysis tools'],
 ARRAY['Malware Types', 'Static Analysis', 'Dynamic Analysis', 'Analysis Tools', 'Behavioral Analysis'],
 250, 12, true, 'foundation'),

-- Module 13: Network Forensics
('bt-13', 'bt-13', 'blue-teaming',
 'Network Forensics',
 'Master network forensics techniques for investigating network-based incidents.',
 'Master network forensics techniques for investigating network-based incidents...',
 'intermediate', 135, 2,
 ARRAY['Understand network forensics concepts', 'Learn packet analysis', 'Understand traffic reconstruction', 'Learn forensics tools'],
 ARRAY['Network Forensics', 'Packet Analysis', 'Traffic Reconstruction', 'Network Evidence', 'Timeline Analysis'],
 200, 13, true, 'foundation'),

-- Module 14: Threat Hunting Introduction
('bt-14', 'bt-14', 'blue-teaming',
 'Threat Hunting Introduction',
 'Introduction to proactive threat hunting methodologies and techniques.',
 'Introduction to proactive threat hunting methodologies and techniques...',
 'intermediate', 120, 2,
 ARRAY['Understand threat hunting concepts', 'Learn hunting methodologies', 'Understand hypothesis development', 'Learn hunting tools'],
 ARRAY['Threat Hunting', 'Hunting Methodologies', 'Hypothesis Development', 'Hunting Tools', 'Proactive Defense'],
 180, 14, true, 'foundation'),

-- Module 15: Security Automation and Orchestration
('bt-15', 'bt-15', 'blue-teaming',
 'Security Automation and Orchestration',
 'Learn to automate security processes and orchestrate defensive responses.',
 'Learn to automate security processes and orchestrate defensive responses...',
 'intermediate', 105, 2,
 ARRAY['Understand automation concepts', 'Learn SOAR platforms', 'Understand playbook development', 'Learn integration techniques'],
 ARRAY['Security Automation', 'SOAR Platforms', 'Playbook Development', 'Integration', 'Workflow Automation'],
 170, 15, true, 'foundation'),

-- Intermediate Phase Modules (16-35)
-- Module 16: Advanced SIEM Operations
('bt-16', 'bt-16', 'blue-teaming',
 'Advanced SIEM Operations',
 'Master advanced SIEM configuration, tuning, and optimization for enterprise environments.',
 'Master advanced SIEM configuration, tuning, and optimization for enterprise environments...',
 'intermediate', 150, 3,
 ARRAY['Learn advanced SIEM configuration', 'Understand correlation rules', 'Learn performance tuning', 'Master use case development'],
 ARRAY['Advanced SIEM', 'Correlation Rules', 'Performance Tuning', 'Use Case Development', 'Custom Dashboards'],
 220, 16, true, 'intermediate'),

-- Module 17: Advanced Incident Response
('bt-17', 'bt-17', 'blue-teaming',
 'Advanced Incident Response',
 'Master complex incident response scenarios and advanced containment strategies.',
 'Master complex incident response scenarios and advanced containment strategies...',
 'intermediate', 180, 3,
 ARRAY['Learn complex incident handling', 'Understand advanced containment', 'Learn forensics integration', 'Master communication strategies'],
 ARRAY['Complex Incidents', 'Advanced Containment', 'Forensics Integration', 'Crisis Communication', 'Legal Coordination'],
 250, 17, true, 'intermediate'),

-- Module 18: Threat Hunting Methodologies
('bt-18', 'bt-18', 'blue-teaming',
 'Threat Hunting Methodologies',
 'Deep dive into various threat hunting frameworks and methodologies.',
 'Deep dive into various threat hunting frameworks and methodologies...',
 'intermediate', 135, 2,
 ARRAY['Learn hunting frameworks', 'Understand MITRE ATT&CK', 'Learn hypothesis-driven hunting', 'Master hunting metrics'],
 ARRAY['Hunting Frameworks', 'MITRE ATT&CK', 'Hypothesis-driven Hunting', 'Hunting Metrics', 'Kill Chain Analysis'],
 200, 18, true, 'intermediate'),

-- Module 19: Advanced Malware Analysis
('bt-19', 'bt-19', 'blue-teaming',
 'Advanced Malware Analysis',
 'Master advanced malware analysis techniques including reverse engineering basics.',
 'Master advanced malware analysis techniques including reverse engineering basics...',
 'advanced', 210, 4,
 ARRAY['Learn reverse engineering basics', 'Understand advanced analysis techniques', 'Learn sandbox analysis', 'Master IOC extraction'],
 ARRAY['Reverse Engineering', 'Advanced Analysis', 'Sandbox Analysis', 'IOC Extraction', 'Malware Families'],
 300, 19, true, 'intermediate'),

-- Module 20: Cloud Security Monitoring
('bt-20', 'bt-20', 'blue-teaming',
 'Cloud Security Monitoring',
 'Learn to monitor and secure cloud environments across major platforms.',
 'Learn to monitor and secure cloud environments across major platforms...',
 'intermediate', 120, 2,
 ARRAY['Understand cloud security challenges', 'Learn cloud monitoring tools', 'Understand multi-cloud security', 'Learn cloud forensics'],
 ARRAY['Cloud Security', 'Cloud Monitoring', 'Multi-cloud Security', 'Cloud Forensics', 'Container Security'],
 180, 20, true, 'intermediate')

-- Continue with remaining modules 21-50...

ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    estimated_time = EXCLUDED.estimated_time,
    updated_at = CURRENT_TIMESTAMP;

-- Success message
SELECT 'Blue Teaming migration (first 20 modules) completed successfully!' as status;
