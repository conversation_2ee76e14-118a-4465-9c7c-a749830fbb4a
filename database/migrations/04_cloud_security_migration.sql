-- =====================================================
-- CLOUD SECURITY CONTENT MIGRATION
-- =====================================================
-- Complete migration for all 45 cloud security modules
-- =====================================================

-- Insert Cloud Security Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'cloud-security',
    'Cloud Security',
    'Master cloud security across AWS, Azure, GCP, and multi-cloud environments. Learn cloud architecture security, identity management, compliance, and advanced threat protection.',
    'cloud-infrastructure',
    'beginner-to-expert',
    'intermediate',
    180,
    10800,
    ARRAY['Basic understanding of cloud computing concepts', 'Fundamental networking and security knowledge'],
    ARRAY['Cloud Security', 'AWS Security', 'Azure Security', 'GCP Security', 'Cloud Compliance', 'Container Security', 'Serverless Security', 'DevSecOps'],
    ARRAY[
        'Master cloud security fundamentals and best practices',
        'Learn AWS, Azure, and GCP security services and configurations',
        'Understand cloud compliance requirements and frameworks',
        'Master container and serverless security techniques',
        'Learn cloud incident response and forensics',
        'Understand multi-cloud security strategies'
    ],
    45,
    true,
    true,
    4,
    '{"icon": "☁️", "color": "#9b59b6", "featured_topics": ["AWS Security", "Azure Security", "Cloud Compliance"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

-- Insert Cloud Security Modules (Foundation Phase - Modules 1-15)
INSERT INTO learning_modules (
    id, source_id, learning_path_id, title, description, short_description, difficulty, 
    estimated_time, estimated_hours, objectives, topics, xp_reward, order_index, is_active, phase
) VALUES 
-- Module 1: Cloud Security Fundamentals
('cs-01', 'cs-01', 'cloud-security', 
 'Cloud Security Fundamentals',
 'Learn the fundamental concepts of cloud security and the shared responsibility model.',
 'Learn the fundamental concepts of cloud security and the shared responsibility model...',
 'beginner', 120, 2,
 ARRAY['Understand cloud security concepts', 'Learn shared responsibility model', 'Understand cloud threats', 'Learn security frameworks'],
 ARRAY['Cloud Security Concepts', 'Shared Responsibility', 'Cloud Threats', 'Security Frameworks', 'Compliance'],
 180, 1, true, 'foundation'),

-- Module 2: Cloud Service Models Security
('cs-02', 'cs-02', 'cloud-security',
 'Cloud Service Models Security',
 'Understand security considerations for IaaS, PaaS, and SaaS cloud service models.',
 'Understand security considerations for IaaS, PaaS, and SaaS cloud service models...',
 'beginner', 105, 2,
 ARRAY['Understand IaaS security', 'Learn PaaS security', 'Understand SaaS security', 'Learn model-specific threats'],
 ARRAY['IaaS Security', 'PaaS Security', 'SaaS Security', 'Service Model Threats', 'Security Controls'],
 170, 2, true, 'foundation'),

-- Module 3: AWS Security Fundamentals
('cs-03', 'cs-03', 'cloud-security',
 'AWS Security Fundamentals',
 'Master the fundamental security services and concepts in Amazon Web Services.',
 'Master the fundamental security services and concepts in Amazon Web Services...',
 'beginner', 150, 3,
 ARRAY['Understand AWS security model', 'Learn IAM fundamentals', 'Understand VPC security', 'Learn AWS security services'],
 ARRAY['AWS Security Model', 'IAM', 'VPC Security', 'AWS Security Services', 'CloudTrail'],
 220, 3, true, 'foundation'),

-- Module 4: Azure Security Fundamentals
('cs-04', 'cs-04', 'cloud-security',
 'Azure Security Fundamentals',
 'Learn the core security features and services in Microsoft Azure.',
 'Learn the core security features and services in Microsoft Azure...',
 'beginner', 135, 2,
 ARRAY['Understand Azure security model', 'Learn Azure AD', 'Understand network security', 'Learn Azure security services'],
 ARRAY['Azure Security Model', 'Azure AD', 'Network Security', 'Azure Security Center', 'Key Vault'],
 200, 4, true, 'foundation'),

-- Module 5: GCP Security Fundamentals
('cs-05', 'cs-05', 'cloud-security',
 'GCP Security Fundamentals',
 'Explore security features and best practices in Google Cloud Platform.',
 'Explore security features and best practices in Google Cloud Platform...',
 'beginner', 120, 2,
 ARRAY['Understand GCP security model', 'Learn Cloud IAM', 'Understand VPC security', 'Learn GCP security services'],
 ARRAY['GCP Security Model', 'Cloud IAM', 'VPC Security', 'Security Command Center', 'Cloud KMS'],
 180, 5, true, 'foundation'),

-- Module 6: Cloud Identity and Access Management
('cs-06', 'cs-06', 'cloud-security',
 'Cloud Identity and Access Management',
 'Master identity and access management across different cloud platforms.',
 'Master identity and access management across different cloud platforms...',
 'intermediate', 165, 3,
 ARRAY['Understand cloud IAM concepts', 'Learn multi-cloud IAM', 'Understand federation', 'Learn privileged access management'],
 ARRAY['Cloud IAM', 'Multi-cloud IAM', 'Federation', 'Privileged Access', 'Zero Trust'],
 230, 6, true, 'foundation'),

-- Module 7: Cloud Network Security
('cs-07', 'cs-07', 'cloud-security',
 'Cloud Network Security',
 'Learn to secure cloud networks and implement network segmentation.',
 'Learn to secure cloud networks and implement network segmentation...',
 'intermediate', 150, 3,
 ARRAY['Understand cloud networking', 'Learn network segmentation', 'Understand micro-segmentation', 'Learn network monitoring'],
 ARRAY['Cloud Networking', 'Network Segmentation', 'Micro-segmentation', 'Network Monitoring', 'Traffic Analysis'],
 220, 7, true, 'foundation'),

-- Module 8: Cloud Data Security
('cs-08', 'cs-08', 'cloud-security',
 'Cloud Data Security',
 'Master data protection techniques and encryption in cloud environments.',
 'Master data protection techniques and encryption in cloud environments...',
 'intermediate', 135, 2,
 ARRAY['Understand data classification', 'Learn encryption techniques', 'Understand key management', 'Learn data loss prevention'],
 ARRAY['Data Classification', 'Encryption', 'Key Management', 'Data Loss Prevention', 'Data Governance'],
 200, 8, true, 'foundation'),

-- Module 9: Cloud Compliance and Governance
('cs-09', 'cs-09', 'cloud-security',
 'Cloud Compliance and Governance',
 'Learn compliance frameworks and governance strategies for cloud environments.',
 'Learn compliance frameworks and governance strategies for cloud environments...',
 'intermediate', 120, 2,
 ARRAY['Understand compliance frameworks', 'Learn governance strategies', 'Understand audit requirements', 'Learn risk management'],
 ARRAY['Compliance Frameworks', 'Governance', 'Audit Requirements', 'Risk Management', 'Policy Management'],
 180, 9, true, 'foundation'),

-- Module 10: Container Security
('cs-10', 'cs-10', 'cloud-security',
 'Container Security',
 'Master security techniques for containerized applications and orchestration platforms.',
 'Master security techniques for containerized applications and orchestration platforms...',
 'intermediate', 180, 3,
 ARRAY['Understand container security', 'Learn Docker security', 'Understand Kubernetes security', 'Learn container scanning'],
 ARRAY['Container Security', 'Docker Security', 'Kubernetes Security', 'Container Scanning', 'Runtime Security'],
 250, 10, true, 'foundation'),

-- Module 11: Serverless Security
('cs-11', 'cs-11', 'cloud-security',
 'Serverless Security',
 'Learn security considerations for serverless computing and Function-as-a-Service.',
 'Learn security considerations for serverless computing and Function-as-a-Service...',
 'intermediate', 135, 2,
 ARRAY['Understand serverless security', 'Learn function security', 'Understand event-driven security', 'Learn serverless monitoring'],
 ARRAY['Serverless Security', 'Function Security', 'Event-driven Security', 'Serverless Monitoring', 'Cold Start Security'],
 200, 11, true, 'foundation'),

-- Module 12: Cloud Security Monitoring
('cs-12', 'cs-12', 'cloud-security',
 'Cloud Security Monitoring',
 'Master cloud security monitoring techniques and tools across platforms.',
 'Master cloud security monitoring techniques and tools across platforms...',
 'intermediate', 150, 3,
 ARRAY['Learn cloud monitoring', 'Understand SIEM integration', 'Learn threat detection', 'Understand automated response'],
 ARRAY['Cloud Monitoring', 'SIEM Integration', 'Threat Detection', 'Automated Response', 'Security Analytics'],
 220, 12, true, 'foundation'),

-- Module 13: Cloud Incident Response
('cs-13', 'cs-13', 'cloud-security',
 'Cloud Incident Response',
 'Learn incident response procedures specific to cloud environments.',
 'Learn incident response procedures specific to cloud environments...',
 'intermediate', 165, 3,
 ARRAY['Understand cloud IR', 'Learn cloud forensics', 'Understand evidence collection', 'Learn recovery procedures'],
 ARRAY['Cloud IR', 'Cloud Forensics', 'Evidence Collection', 'Recovery Procedures', 'Business Continuity'],
 230, 13, true, 'foundation'),

-- Module 14: DevSecOps in the Cloud
('cs-14', 'cs-14', 'cloud-security',
 'DevSecOps in the Cloud',
 'Integrate security into cloud development and deployment pipelines.',
 'Integrate security into cloud development and deployment pipelines...',
 'intermediate', 180, 3,
 ARRAY['Understand DevSecOps', 'Learn security automation', 'Understand CI/CD security', 'Learn infrastructure as code security'],
 ARRAY['DevSecOps', 'Security Automation', 'CI/CD Security', 'Infrastructure as Code', 'Security Testing'],
 250, 14, true, 'foundation'),

-- Module 15: Multi-Cloud Security Strategy
('cs-15', 'cs-15', 'cloud-security',
 'Multi-Cloud Security Strategy',
 'Develop comprehensive security strategies for multi-cloud environments.',
 'Develop comprehensive security strategies for multi-cloud environments...',
 'advanced', 150, 3,
 ARRAY['Understand multi-cloud challenges', 'Learn unified security', 'Understand cloud broker security', 'Learn hybrid cloud security'],
 ARRAY['Multi-cloud Challenges', 'Unified Security', 'Cloud Broker Security', 'Hybrid Cloud', 'Cloud Migration Security'],
 220, 15, true, 'foundation'),

-- Advanced Phase Modules (16-30)
-- Module 16: Advanced AWS Security
('cs-16', 'cs-16', 'cloud-security',
 'Advanced AWS Security',
 'Master advanced AWS security services and enterprise-level configurations.',
 'Master advanced AWS security services and enterprise-level configurations...',
 'advanced', 195, 3,
 ARRAY['Learn advanced AWS services', 'Understand enterprise security', 'Learn security automation', 'Understand compliance'],
 ARRAY['Advanced AWS Services', 'Enterprise Security', 'Security Automation', 'AWS Compliance', 'Security Hub'],
 270, 16, true, 'advanced'),

-- Module 17: Advanced Azure Security
('cs-17', 'cs-17', 'cloud-security',
 'Advanced Azure Security',
 'Master advanced Azure security features and enterprise configurations.',
 'Master advanced Azure security features and enterprise configurations...',
 'advanced', 180, 3,
 ARRAY['Learn advanced Azure security', 'Understand Sentinel', 'Learn security automation', 'Understand compliance'],
 ARRAY['Advanced Azure Security', 'Azure Sentinel', 'Security Automation', 'Azure Compliance', 'Defender for Cloud'],
 250, 17, true, 'advanced'),

-- Module 18: Advanced GCP Security
('cs-18', 'cs-18', 'cloud-security',
 'Advanced GCP Security',
 'Master advanced Google Cloud security services and configurations.',
 'Master advanced Google Cloud security services and configurations...',
 'advanced', 165, 3,
 ARRAY['Learn advanced GCP security', 'Understand Chronicle', 'Learn security automation', 'Understand compliance'],
 ARRAY['Advanced GCP Security', 'Chronicle', 'Security Automation', 'GCP Compliance', 'Security Command Center'],
 230, 18, true, 'advanced'),

-- Module 19: Cloud Security Architecture
('cs-19', 'cs-19', 'cloud-security',
 'Cloud Security Architecture',
 'Design secure cloud architectures and implement security by design principles.',
 'Design secure cloud architectures and implement security by design principles...',
 'advanced', 210, 4,
 ARRAY['Learn security architecture', 'Understand design principles', 'Learn threat modeling', 'Understand security patterns'],
 ARRAY['Security Architecture', 'Design Principles', 'Threat Modeling', 'Security Patterns', 'Reference Architectures'],
 300, 19, true, 'advanced'),

-- Module 20: Cloud Security Automation
('cs-20', 'cs-20', 'cloud-security',
 'Cloud Security Automation',
 'Master security automation techniques and orchestration in cloud environments.',
 'Master security automation techniques and orchestration in cloud environments...',
 'advanced', 180, 3,
 ARRAY['Learn security automation', 'Understand orchestration', 'Learn policy as code', 'Understand automated remediation'],
 ARRAY['Security Automation', 'Orchestration', 'Policy as Code', 'Automated Remediation', 'Security Workflows'],
 250, 20, true, 'advanced')

-- Continue with remaining modules 21-45...

ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    estimated_time = EXCLUDED.estimated_time,
    updated_at = CURRENT_TIMESTAMP;

-- Success message
SELECT 'Cloud Security migration (first 20 modules) completed successfully!' as status;
