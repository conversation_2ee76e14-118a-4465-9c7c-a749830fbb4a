-- =====================================================
-- NETWORKING FUNDAMENTALS CONTENT MIGRATION
-- =====================================================
-- Complete migration for all 25 networking modules
-- =====================================================

-- Insert Networking Fundamentals Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'networking-fundamentals',
    'Networking Fundamentals',
    'Master the essential concepts of computer networking that form the foundation of cybersecurity. Learn about network protocols, architecture, and security principles.',
    'fundamentals',
    'beginner',
    'foundation',
    60,
    3600,
    ARRAY[]::TEXT[],
    ARRAY['Networking', 'TCP/IP', 'Network Security', 'OSI Model', 'Routing', 'Switching', 'VLANs', 'Firewalls'],
    ARRAY[
        'Understand fundamental networking concepts and terminology',
        'Master the OSI model and TCP/IP protocol suite',
        'Learn network topologies and infrastructure components',
        'Understand routing and switching concepts',
        'Master IP addressing and subnetting techniques',
        'Learn network security fundamentals and best practices',
        'Understand wireless networking and VPN technologies'
    ],
    25,
    true,
    true,
    1,
    '{"icon": "🌐", "color": "#4A5CBA", "featured_topics": ["OSI Model", "TCP/IP", "Network Security"]}'::JSONB
) ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    modules_count = EXCLUDED.modules_count,
    updated_at = CURRENT_TIMESTAMP;

-- Insert Networking Fundamentals Modules
INSERT INTO learning_modules (
    id, source_id, learning_path_id, title, description, short_description, difficulty, 
    estimated_time, estimated_hours, objectives, topics, xp_reward, order_index, is_active
) VALUES 
-- Module 1: Introduction to Computer Networks
('nf-1', 'nf-1', 'networking-fundamentals', 
 'Introduction to Computer Networks',
 'Learn the fundamental concepts of computer networks and their importance in modern computing and cybersecurity.',
 'Learn the fundamental concepts of computer networks and their importance in modern computing...',
 'beginner', 60, 1,
 ARRAY['Understand what a computer network is', 'Identify different types of networks', 'Explain basic network components', 'Describe network topologies'],
 ARRAY['Network Basics', 'Network Types', 'Network Components', 'Network Topologies'],
 100, 1, true),

-- Module 2: The OSI Model
('nf-2', 'nf-2', 'networking-fundamentals',
 'The OSI Model',
 'Understand the seven layers of the OSI model and how they provide a framework for network communication.',
 'Understand the seven layers of the OSI model and how they provide a framework...',
 'beginner', 75, 1,
 ARRAY['Understand the OSI model layers', 'Learn data encapsulation process', 'Identify protocol data units', 'Understand layer interactions'],
 ARRAY['OSI Layers', 'Data Encapsulation', 'PDUs', 'Layer Functions'],
 120, 2, true),

-- Module 3: TCP/IP Protocol Suite
('nf-3', 'nf-3', 'networking-fundamentals',
 'TCP/IP Protocol Suite',
 'Explore the TCP/IP protocol stack, the foundation of internet communications.',
 'Explore the TCP/IP protocol stack, the foundation of internet communications...',
 'beginner', 90, 2,
 ARRAY['Understand TCP/IP protocol stack', 'Learn IP addressing concepts', 'Compare TCP vs UDP', 'Analyze network protocols'],
 ARRAY['TCP/IP Stack', 'IP Addressing', 'TCP vs UDP', 'Protocol Analysis'],
 150, 3, true),

-- Module 4: IP Addressing and Subnetting
('nf-4', 'nf-4', 'networking-fundamentals',
 'IP Addressing and Subnetting',
 'Master IP addressing concepts, including IPv4, IPv6, and subnetting techniques.',
 'Master IP addressing concepts, including IPv4, IPv6, and subnetting techniques...',
 'beginner', 120, 2,
 ARRAY['Understand IPv4 addressing', 'Learn subnet masks and CIDR', 'Master subnetting calculations', 'Understand IPv6 addressing'],
 ARRAY['IPv4 Addressing', 'Subnet Masks', 'CIDR Notation', 'IPv6 Addressing'],
 180, 4, true),

-- Module 5: Network Devices and Infrastructure
('nf-5', 'nf-5', 'networking-fundamentals',
 'Network Devices and Infrastructure',
 'Learn about the hardware components that make up computer networks.',
 'Learn about the hardware components that make up computer networks...',
 'beginner', 90, 2,
 ARRAY['Identify network devices', 'Understand device functions', 'Learn about network cabling', 'Understand wireless infrastructure'],
 ARRAY['Network Interface Cards', 'Hubs and Switches', 'Routers', 'Wireless Access Points', 'Network Cabling'],
 150, 5, true),

-- Module 6: IPv6 Fundamentals
('nf-6', 'nf-6', 'networking-fundamentals',
 'IPv6 Fundamentals',
 'Understand the next generation of IP addressing and its implementation.',
 'Understand the next generation of IP addressing and its implementation...',
 'intermediate', 75, 1,
 ARRAY['Understand IPv6 address structure', 'Learn IPv6 transition mechanisms', 'Understand IPv6 security considerations'],
 ARRAY['IPv6 Address Structure', 'Transition Mechanisms', 'Security Considerations'],
 120, 6, true),

-- Module 7: Routing Fundamentals
('nf-7', 'nf-7', 'networking-fundamentals',
 'Routing Fundamentals',
 'Learn how data is routed across networks and the internet.',
 'Learn how data is routed across networks and the internet...',
 'intermediate', 105, 2,
 ARRAY['Understand routing concepts', 'Learn static vs dynamic routing', 'Understand routing protocols', 'Learn routing tables'],
 ARRAY['Routing Concepts', 'Static vs Dynamic Routing', 'Routing Protocols', 'Routing Tables'],
 170, 7, true),

-- Module 8: DNS Fundamentals
('nf-8', 'nf-8', 'networking-fundamentals',
 'DNS Fundamentals',
 'Understand the Domain Name System and its critical role in network operations.',
 'Understand the Domain Name System and its critical role in network operations...',
 'intermediate', 90, 2,
 ARRAY['Understand DNS resolution process', 'Learn DNS record types', 'Understand DNS security', 'Learn DNS troubleshooting'],
 ARRAY['DNS Resolution Process', 'DNS Records', 'DNS Security', 'DNS Troubleshooting'],
 150, 8, true),

-- Module 9: DHCP Fundamentals
('nf-9', 'nf-9', 'networking-fundamentals',
 'DHCP Fundamentals',
 'Learn how the Dynamic Host Configuration Protocol automates IP address assignment.',
 'Learn how the Dynamic Host Configuration Protocol automates IP address assignment...',
 'intermediate', 75, 1,
 ARRAY['Understand DHCP process', 'Learn DHCP options', 'Understand DHCP security', 'Learn DHCP troubleshooting'],
 ARRAY['DHCP Process', 'DHCP Options', 'DHCP Security', 'DHCP Troubleshooting'],
 120, 9, true),

-- Module 10: Switching Concepts
('nf-10', 'nf-10', 'networking-fundamentals',
 'Switching Concepts',
 'Explore how switches operate and their role in local area networks.',
 'Explore how switches operate and their role in local area networks...',
 'intermediate', 90, 2,
 ARRAY['Understand switch operation', 'Learn MAC address tables', 'Understand VLANs', 'Learn Spanning Tree Protocol'],
 ARRAY['Switch Operation', 'MAC Address Tables', 'VLANs', 'Spanning Tree Protocol'],
 150, 10, true),

-- Module 11: VLANs and Trunking
('nf-11', 'nf-11', 'networking-fundamentals',
 'VLANs and Trunking',
 'Learn how to segment networks using Virtual LANs and trunking protocols.',
 'Learn how to segment networks using Virtual LANs and trunking protocols...',
 'intermediate', 105, 2,
 ARRAY['Understand VLAN concepts', 'Learn VLAN configuration', 'Understand trunking protocols', 'Learn inter-VLAN routing'],
 ARRAY['VLAN Configuration', 'Trunking Protocols', 'Inter-VLAN Routing', 'VLAN Security'],
 170, 11, true),

-- Module 12: Network Address Translation
('nf-12', 'nf-12', 'networking-fundamentals',
 'Network Address Translation',
 'Understand how NAT extends IPv4 addressing and provides a layer of security.',
 'Understand how NAT extends IPv4 addressing and provides a layer of security...',
 'intermediate', 75, 1,
 ARRAY['Understand NAT concepts', 'Learn NAT types', 'Understand port forwarding', 'Learn NAT traversal'],
 ARRAY['NAT Types', 'Port Forwarding', 'NAT Traversal', 'NAT Security'],
 120, 12, true),

-- Module 13: Wireless Networking
('nf-13', 'nf-13', 'networking-fundamentals',
 'Wireless Networking',
 'Learn about wireless network technologies, standards, and security.',
 'Learn about wireless network technologies, standards, and security...',
 'intermediate', 120, 2,
 ARRAY['Understand Wi-Fi standards', 'Learn wireless security', 'Understand wireless troubleshooting', 'Learn wireless design'],
 ARRAY['Wi-Fi Standards', 'Wireless Security', 'Wireless Troubleshooting', 'Wireless Design'],
 180, 13, true),

-- Module 14: Network Security Fundamentals
('nf-14', 'nf-14', 'networking-fundamentals',
 'Network Security Fundamentals',
 'Explore the basic principles of securing computer networks.',
 'Explore the basic principles of securing computer networks...',
 'intermediate', 135, 2,
 ARRAY['Understand network threats', 'Learn defense in depth', 'Understand security controls', 'Learn security policies'],
 ARRAY['Network Threats', 'Defense in Depth', 'Security Controls', 'Security Policies'],
 200, 14, true),

-- Module 15: Firewalls and Access Control Lists
('nf-15', 'nf-15', 'networking-fundamentals',
 'Firewalls and Access Control Lists',
 'Learn how firewalls and ACLs protect networks by controlling traffic flow.',
 'Learn how firewalls and ACLs protect networks by controlling traffic flow...',
 'intermediate', 105, 2,
 ARRAY['Understand firewall types', 'Learn ACL configuration', 'Understand next-gen firewalls', 'Learn firewall rules'],
 ARRAY['Firewall Types', 'ACL Configuration', 'Next-Gen Firewalls', 'Firewall Rules'],
 170, 15, true),

-- Continue with remaining modules...
-- Module 16-25 would follow the same pattern

-- Module 16: Virtual Private Networks
('nf-16', 'nf-16', 'networking-fundamentals',
 'Virtual Private Networks',
 'Understand how VPNs provide secure remote access and site-to-site connectivity.',
 'Understand how VPNs provide secure remote access and site-to-site connectivity...',
 'advanced', 120, 2,
 ARRAY['Understand VPN technologies', 'Learn VPN protocols', 'Understand VPN implementation', 'Learn VPN security'],
 ARRAY['VPN Technologies', 'VPN Protocols', 'VPN Implementation', 'VPN Security'],
 180, 16, true),

-- Module 17: Network Monitoring and Management
('nf-17', 'nf-17', 'networking-fundamentals',
 'Network Monitoring and Management',
 'Learn tools and techniques for monitoring and managing network performance and security.',
 'Learn tools and techniques for monitoring and managing network performance...',
 'advanced', 105, 2,
 ARRAY['Learn monitoring tools', 'Understand SNMP', 'Learn NetFlow', 'Understand Syslog'],
 ARRAY['Monitoring Tools', 'SNMP', 'NetFlow', 'Syslog'],
 170, 17, true),

-- Module 18: Network Troubleshooting
('nf-18', 'nf-18', 'networking-fundamentals',
 'Network Troubleshooting',
 'Master the methodologies and tools for diagnosing and resolving network issues.',
 'Master the methodologies and tools for diagnosing and resolving network issues...',
 'advanced', 135, 2,
 ARRAY['Learn troubleshooting methodology', 'Understand diagnostic tools', 'Learn common issues', 'Understand problem resolution'],
 ARRAY['Troubleshooting Methodology', 'Diagnostic Tools', 'Common Issues', 'Problem Resolution'],
 200, 18, true),

-- Module 19: Packet Analysis with Wireshark
('nf-19', 'nf-19', 'networking-fundamentals',
 'Packet Analysis with Wireshark',
 'Learn to capture and analyze network traffic for troubleshooting and security analysis.',
 'Learn to capture and analyze network traffic for troubleshooting and security analysis...',
 'advanced', 150, 3,
 ARRAY['Learn packet capture', 'Understand protocol analysis', 'Learn security analysis', 'Understand traffic patterns'],
 ARRAY['Packet Capture', 'Protocol Analysis', 'Security Analysis', 'Traffic Patterns'],
 220, 19, true),

-- Module 20: Network Protocols and Services
('nf-20', 'nf-20', 'networking-fundamentals',
 'Network Protocols and Services',
 'Explore common network protocols and services and their security implications.',
 'Explore common network protocols and services and their security implications...',
 'advanced', 120, 2,
 ARRAY['Understand HTTP/HTTPS', 'Learn FTP/SFTP', 'Understand SSH', 'Learn email protocols'],
 ARRAY['HTTP/HTTPS', 'FTP/SFTP', 'SSH', 'Email Protocols'],
 180, 20, true),

-- Module 21: Network Performance and Optimization
('nf-21', 'nf-21', 'networking-fundamentals',
 'Network Performance and Optimization',
 'Learn techniques for optimizing network performance and reliability.',
 'Learn techniques for optimizing network performance and reliability...',
 'advanced', 105, 2,
 ARRAY['Learn bandwidth management', 'Understand QoS', 'Learn caching', 'Understand load balancing'],
 ARRAY['Bandwidth Management', 'QoS', 'Caching', 'Load Balancing'],
 170, 21, true),

-- Module 22: Cloud Networking
('nf-22', 'nf-22', 'networking-fundamentals',
 'Cloud Networking',
 'Understand networking concepts in cloud environments.',
 'Understand networking concepts in cloud environments...',
 'advanced', 90, 2,
 ARRAY['Understand cloud network architecture', 'Learn virtual networks', 'Understand cloud security', 'Learn hybrid connectivity'],
 ARRAY['Cloud Network Architecture', 'Virtual Networks', 'Cloud Security', 'Hybrid Connectivity'],
 150, 22, true),

-- Module 23: Software-Defined Networking
('nf-23', 'nf-23', 'networking-fundamentals',
 'Software-Defined Networking',
 'Explore the concepts and benefits of software-defined networking.',
 'Explore the concepts and benefits of software-defined networking...',
 'advanced', 105, 2,
 ARRAY['Understand SDN architecture', 'Learn SDN controllers', 'Understand SDN security', 'Learn SDN implementation'],
 ARRAY['SDN Architecture', 'SDN Controllers', 'SDN Security', 'SDN Implementation'],
 170, 23, true),

-- Module 24: Network Virtualization
('nf-24', 'nf-24', 'networking-fundamentals',
 'Network Virtualization',
 'Learn about network virtualization technologies and their implementation.',
 'Learn about network virtualization technologies and their implementation...',
 'advanced', 90, 2,
 ARRAY['Understand virtual networks', 'Learn network function virtualization', 'Understand containers', 'Learn virtualization security'],
 ARRAY['Virtual Networks', 'Network Function Virtualization', 'Containers', 'Virtualization Security'],
 150, 24, true),

-- Module 25: Network Security Assessment
('nf-25', 'nf-25', 'networking-fundamentals',
 'Network Security Assessment',
 'Learn methodologies and tools for assessing network security.',
 'Learn methodologies and tools for assessing network security...',
 'advanced', 135, 2,
 ARRAY['Learn vulnerability assessment', 'Understand penetration testing', 'Learn security auditing', 'Understand compliance'],
 ARRAY['Vulnerability Assessment', 'Penetration Testing', 'Security Auditing', 'Compliance'],
 200, 25, true)

ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    estimated_time = EXCLUDED.estimated_time,
    updated_at = CURRENT_TIMESTAMP;

-- Success message
SELECT 'Networking Fundamentals migration completed successfully! 25 modules inserted.' as status;
