-- =====================================================
-- COMPREHENSIVE LEARNING CONTENT DATABASE MIGRATION
-- =====================================================
-- This script creates all necessary tables for storing
-- learning content with rich interactive elements
-- =====================================================

-- Drop existing tables if they exist (in correct order)
DROP TABLE IF EXISTS module_progress CASCADE;
DROP TABLE IF EXISTS module_sections CASCADE;
DROP TABLE IF EXISTS learning_modules CASCADE;
DROP TABLE IF EXISTS learning_paths CASCADE;

-- =====================================================
-- LEARNING PATHS TABLE
-- =====================================================
CREATE TABLE learning_paths (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'fundamentals',
    difficulty VARCHAR(20) DEFAULT 'beginner',
    estimated_hours INTEGER DEFAULT 0,
    prerequisites TEXT[], -- Array of prerequisite path IDs
    skills TEXT[], -- Array of skills learned
    modules_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- LEARNING MODULES TABLE
-- =====================================================
CREATE TABLE learning_modules (
    id VARCHAR(50) PRIMARY KEY,
    source_id VARCHAR(50), -- Original module ID (e.g., 'nf-1')
    learning_path_id VARCHAR(50) REFERENCES learning_paths(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    difficulty VARCHAR(20) DEFAULT 'beginner',
    estimated_time INTEGER DEFAULT 60, -- in minutes
    objectives TEXT[], -- Array of learning objectives
    prerequisites TEXT[], -- Array of prerequisite module IDs
    xp_reward INTEGER DEFAULT 100,
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- MODULE SECTIONS TABLE
-- =====================================================
CREATE TABLE module_sections (
    id SERIAL PRIMARY KEY,
    module_id VARCHAR(50) REFERENCES learning_modules(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT, -- HTML content for text sections
    content_type VARCHAR(20) DEFAULT 'text', -- 'text', 'interactive', 'quiz', 'simulation'
    interactive_data JSONB, -- For interactive content, simulations, quizzes
    order_index INTEGER DEFAULT 0,
    estimated_time INTEGER DEFAULT 15, -- in minutes
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- MODULE PROGRESS TABLE
-- =====================================================
CREATE TABLE module_progress (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    module_id VARCHAR(50) REFERENCES learning_modules(id) ON DELETE CASCADE,
    section_id INTEGER REFERENCES module_sections(id) ON DELETE CASCADE,
    is_completed BOOLEAN DEFAULT false,
    completion_date TIMESTAMP,
    time_spent INTEGER DEFAULT 0, -- in minutes
    quiz_score INTEGER, -- percentage score for quiz sections
    notes TEXT, -- user notes for the section
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, section_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX idx_learning_modules_path ON learning_modules(learning_path_id);
CREATE INDEX idx_learning_modules_order ON learning_modules(learning_path_id, order_index);
CREATE INDEX idx_module_sections_module ON module_sections(module_id);
CREATE INDEX idx_module_sections_order ON module_sections(module_id, order_index);
CREATE INDEX idx_module_progress_user ON module_progress(user_id);
CREATE INDEX idx_module_progress_module ON module_progress(module_id);
CREATE INDEX idx_module_progress_completion ON module_progress(user_id, is_completed);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_learning_paths_updated_at BEFORE UPDATE ON learning_paths FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_modules_updated_at BEFORE UPDATE ON learning_modules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_module_sections_updated_at BEFORE UPDATE ON module_sections FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_module_progress_updated_at BEFORE UPDATE ON module_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE module_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE module_progress ENABLE ROW LEVEL SECURITY;

-- Public read access for learning content
CREATE POLICY "Public read access for learning paths" ON learning_paths FOR SELECT USING (true);
CREATE POLICY "Public read access for learning modules" ON learning_modules FOR SELECT USING (true);
CREATE POLICY "Public read access for module sections" ON module_sections FOR SELECT USING (true);

-- User-specific access for progress
CREATE POLICY "Users can view own progress" ON module_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own progress" ON module_progress FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own progress" ON module_progress FOR UPDATE USING (auth.uid() = user_id);

-- Admin access for content management
CREATE POLICY "Admins can manage learning paths" ON learning_paths FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND (profiles.role = 'super_admin' OR profiles.is_admin = true)
    )
);

CREATE POLICY "Admins can manage learning modules" ON learning_modules FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND (profiles.role = 'super_admin' OR profiles.is_admin = true)
    )
);

CREATE POLICY "Admins can manage module sections" ON module_sections FOR ALL USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND (profiles.role = 'super_admin' OR profiles.is_admin = true)
    )
);

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================
-- Insert Networking Fundamentals Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, estimated_hours, 
    prerequisites, skills, modules_count, is_active, is_featured
) VALUES (
    'networking-fundamentals',
    'Networking Fundamentals',
    'Master the essential concepts of computer networking that form the foundation of cybersecurity. Learn about network protocols, architecture, and security principles.',
    'fundamentals',
    'beginner',
    60,
    '{}',
    '{"Networking", "TCP/IP", "Network Security", "Network Protocols"}',
    25,
    true,
    true
);

-- =====================================================
-- FUNCTIONS FOR CONTENT MANAGEMENT
-- =====================================================
-- Function to get learning path with module count
CREATE OR REPLACE FUNCTION get_learning_path_with_stats(path_id VARCHAR)
RETURNS TABLE (
    id VARCHAR,
    title VARCHAR,
    description TEXT,
    category VARCHAR,
    difficulty VARCHAR,
    estimated_hours INTEGER,
    prerequisites TEXT[],
    skills TEXT[],
    modules_count BIGINT,
    completed_modules BIGINT,
    is_active BOOLEAN,
    is_featured BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lp.id,
        lp.title,
        lp.description,
        lp.category,
        lp.difficulty,
        lp.estimated_hours,
        lp.prerequisites,
        lp.skills,
        COUNT(lm.id) as modules_count,
        COALESCE(
            (SELECT COUNT(DISTINCT mp.module_id) 
             FROM module_progress mp 
             JOIN learning_modules lm2 ON mp.module_id = lm2.id 
             WHERE lm2.learning_path_id = lp.id 
             AND mp.user_id = auth.uid() 
             AND mp.is_completed = true), 
            0
        ) as completed_modules,
        lp.is_active,
        lp.is_featured
    FROM learning_paths lp
    LEFT JOIN learning_modules lm ON lp.id = lm.learning_path_id
    WHERE lp.id = path_id
    GROUP BY lp.id, lp.title, lp.description, lp.category, lp.difficulty, 
             lp.estimated_hours, lp.prerequisites, lp.skills, lp.is_active, lp.is_featured;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get module with progress
CREATE OR REPLACE FUNCTION get_module_with_progress(module_id VARCHAR)
RETURNS TABLE (
    id VARCHAR,
    source_id VARCHAR,
    learning_path_id VARCHAR,
    title VARCHAR,
    description TEXT,
    difficulty VARCHAR,
    estimated_time INTEGER,
    objectives TEXT[],
    prerequisites TEXT[],
    xp_reward INTEGER,
    order_index INTEGER,
    sections_count BIGINT,
    completed_sections BIGINT,
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lm.id,
        lm.source_id,
        lm.learning_path_id,
        lm.title,
        lm.description,
        lm.difficulty,
        lm.estimated_time,
        lm.objectives,
        lm.prerequisites,
        lm.xp_reward,
        lm.order_index,
        COUNT(ms.id) as sections_count,
        COALESCE(
            (SELECT COUNT(*) 
             FROM module_progress mp 
             WHERE mp.module_id = lm.id 
             AND mp.user_id = auth.uid() 
             AND mp.is_completed = true), 
            0
        ) as completed_sections,
        lm.is_active
    FROM learning_modules lm
    LEFT JOIN module_sections ms ON lm.id = ms.module_id
    WHERE lm.id = module_id
    GROUP BY lm.id, lm.source_id, lm.learning_path_id, lm.title, lm.description, 
             lm.difficulty, lm.estimated_time, lm.objectives, lm.prerequisites, 
             lm.xp_reward, lm.order_index, lm.is_active;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- COMPLETION NOTIFICATION
-- =====================================================
COMMENT ON TABLE learning_paths IS 'Stores learning path information with metadata';
COMMENT ON TABLE learning_modules IS 'Stores individual modules within learning paths';
COMMENT ON TABLE module_sections IS 'Stores sections within modules with rich content';
COMMENT ON TABLE module_progress IS 'Tracks user progress through modules and sections';

-- Migration completed successfully
SELECT 'Learning content database migration completed successfully!' as status;
