-- =====================================================
-- COMPREHENSIVE CONTENT MIGRATION SCRIPT
-- =====================================================
-- This script migrates all JavaScript learning content to database
-- including all learning paths, modules, sections, and components
-- =====================================================

-- =====================================================
-- LEARNING PATHS MIGRATION
-- =====================================================

-- Insert Networking Fundamentals Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'networking-fundamentals',
    'Networking Fundamentals',
    'Master the essential concepts of computer networking that form the foundation of cybersecurity. Learn about network protocols, architecture, and security principles.',
    'fundamentals',
    'beginner',
    'foundation',
    60,
    3600,
    ARRAY[]::TEXT[],
    ARRAY['Networking', 'TCP/IP', 'Network Security', 'Network Protocols', 'OSI Model', 'Routing', 'Switching'],
    ARRAY[
        'Understand fundamental networking concepts and terminology',
        'Master the OSI model and TCP/IP protocol suite',
        'Learn network topologies and infrastructure components',
        'Understand routing and switching concepts',
        'Master IP addressing and subnetting',
        'Learn network security fundamentals',
        'Understand wireless networking and VPNs'
    ],
    25,
    true,
    true,
    1,
    '{"icon": "🌐", "color": "#4A5CBA", "featured_topics": ["OSI Model", "TCP/IP", "Network Security"]}'::JSONB
);

-- Insert Blue Teaming Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'blue-teaming',
    'Blue Teaming: Comprehensive Defensive Cybersecurity',
    'Master defensive cybersecurity operations with our comprehensive 50-module Blue Teaming curriculum. Learn threat detection, incident response, security monitoring, threat hunting, and strategic defense planning.',
    'defensive',
    'beginner-to-expert',
    'foundation',
    1800,
    108000,
    ARRAY['Basic understanding of computer networks', 'Fundamental knowledge of operating systems', 'Basic cybersecurity awareness'],
    ARRAY['Defensive Security', 'Incident Response', 'Threat Hunting', 'SIEM', 'Security Monitoring', 'Digital Forensics'],
    ARRAY[
        'Master defensive cybersecurity fundamentals',
        'Learn security monitoring and SIEM technologies',
        'Understand incident response procedures',
        'Develop threat hunting capabilities',
        'Master digital forensics techniques',
        'Learn advanced security operations'
    ],
    50,
    true,
    true,
    2,
    '{"icon": "🛡️", "color": "#88cc14", "featured_topics": ["SIEM", "Incident Response", "Threat Hunting"]}'::JSONB
);

-- Insert Ethical Hacking Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'ethical-hacking',
    'Ethical Hacking Fundamentals',
    'Master ethical hacking and penetration testing from fundamental concepts to advanced attack techniques.',
    'offensive',
    'intermediate',
    'intermediate',
    120,
    7200,
    ARRAY['Basic networking knowledge', 'Understanding of operating systems', 'Basic cybersecurity concepts'],
    ARRAY['Penetration Testing', 'Vulnerability Assessment', 'Ethical Hacking', 'Security Testing', 'Reconnaissance'],
    ARRAY[
        'Learn ethical hacking fundamentals and methodology',
        'Master reconnaissance and information gathering',
        'Understand vulnerability assessment techniques',
        'Learn system hacking and exploitation',
        'Develop penetration testing skills'
    ],
    50,
    true,
    true,
    3,
    '{"icon": "🔓", "color": "#e74c3c", "featured_topics": ["Penetration Testing", "Vulnerability Assessment", "Reconnaissance"]}'::JSONB
);

-- Insert Red Teaming Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'red-teaming',
    'Red Teaming',
    'Master advanced offensive security techniques and red team operations.',
    'offensive',
    'advanced',
    'advanced',
    100,
    6000,
    ARRAY['Advanced penetration testing knowledge', 'Understanding of network security', 'Experience with ethical hacking'],
    ARRAY['Red Team Operations', 'Advanced Persistence', 'Lateral Movement', 'Command and Control', 'Evasion Techniques'],
    ARRAY[
        'Master red team methodology and operations',
        'Learn advanced persistence techniques',
        'Understand lateral movement and privilege escalation',
        'Develop command and control capabilities',
        'Master evasion and anti-forensics techniques'
    ],
    50,
    true,
    true,
    4,
    '{"icon": "⚔️", "color": "#c0392b", "featured_topics": ["Red Team Ops", "Lateral Movement", "C2 Infrastructure"]}'::JSONB
);

-- Insert Operating Systems Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'operating-systems',
    'Operating System Concepts for Cybersecurity',
    'Comprehensive learning path covering operating system security from fundamental concepts to advanced threat mitigation, designed for cybersecurity professionals.',
    'fundamentals',
    'beginner-to-advanced',
    'foundation',
    50,
    3000,
    ARRAY['Basic computer literacy', 'Understanding of networking concepts'],
    ARRAY['Operating Systems', 'Linux Security', 'Windows Security', 'System Hardening', 'Process Management'],
    ARRAY[
        'Understand operating system fundamentals',
        'Learn Linux and Windows security concepts',
        'Master system hardening techniques',
        'Understand process and memory management',
        'Learn file system security'
    ],
    30,
    true,
    false,
    5,
    '{"icon": "💻", "color": "#3498db", "featured_topics": ["Linux Security", "Windows Security", "System Hardening"]}'::JSONB
);

-- Insert Cloud Security Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'cloud-security',
    'Cloud Security',
    'Master cloud security across AWS, Azure, GCP, and multi-cloud environments. Learn cloud architecture security, identity management, compliance, and advanced threat protection.',
    'cloud-infrastructure',
    'beginner-to-expert',
    'intermediate',
    180,
    10800,
    ARRAY['Basic understanding of cloud computing concepts', 'Fundamental networking and security knowledge'],
    ARRAY['Cloud Security', 'AWS Security', 'Azure Security', 'GCP Security', 'Cloud Compliance', 'Container Security'],
    ARRAY[
        'Master cloud security fundamentals',
        'Learn AWS, Azure, and GCP security',
        'Understand cloud compliance requirements',
        'Master container and serverless security',
        'Learn cloud incident response'
    ],
    45,
    true,
    true,
    6,
    '{"icon": "☁️", "color": "#9b59b6", "featured_topics": ["AWS Security", "Azure Security", "Cloud Compliance"]}'::JSONB
);

-- Insert Threat Intelligence Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'threat-intelligence',
    'Threat Intelligence',
    'Master cyber threat intelligence from collection and analysis to strategic intelligence and threat hunting integration.',
    'intelligence',
    'intermediate',
    'intermediate',
    72,
    4320,
    ARRAY['Basic cybersecurity knowledge', 'Understanding of threat landscape'],
    ARRAY['Threat Intelligence', 'OSINT', 'Threat Analysis', 'IOC Management', 'Threat Hunting'],
    ARRAY[
        'Master threat intelligence fundamentals',
        'Learn OSINT collection techniques',
        'Understand threat analysis methodologies',
        'Master IOC and TTP analysis',
        'Learn threat intelligence integration'
    ],
    24,
    true,
    false,
    7,
    '{"icon": "🔍", "color": "#f39c12", "featured_topics": ["OSINT", "Threat Analysis", "IOC Management"]}'::JSONB
);

-- Insert Threat Hunting Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'threat-hunting',
    'Threat Hunting',
    'Master proactive threat hunting techniques, methodologies, and tools for advanced threat detection and response.',
    'defensive',
    'advanced',
    'advanced',
    120,
    7200,
    ARRAY['Advanced security monitoring knowledge', 'Understanding of threat intelligence', 'SIEM experience'],
    ARRAY['Threat Hunting', 'Behavioral Analysis', 'Anomaly Detection', 'Hunting Automation', 'Advanced Analytics'],
    ARRAY[
        'Master threat hunting methodologies',
        'Learn behavioral analysis techniques',
        'Understand anomaly detection methods',
        'Develop hunting automation skills',
        'Master advanced hunting tools'
    ],
    40,
    true,
    false,
    8,
    '{"icon": "🎯", "color": "#e67e22", "featured_topics": ["Hunting Methodology", "Behavioral Analysis", "Automation"]}'::JSONB
);

-- Insert Bug Bounty Learning Path
INSERT INTO learning_paths (
    id, title, description, category, difficulty, phase, estimated_hours, estimated_time,
    prerequisites, skills, objectives, modules_count, is_active, is_featured, sort_order, metadata
) VALUES (
    'bug-bounty',
    'Bug Bounty Hunting',
    'Master bug bounty hunting from reconnaissance to advanced exploitation techniques and responsible disclosure.',
    'offensive',
    'intermediate-to-advanced',
    'intermediate',
    60,
    3600,
    ARRAY['Web application security knowledge', 'Basic penetration testing skills'],
    ARRAY['Bug Bounty', 'Web Security', 'Mobile Security', 'API Testing', 'Vulnerability Research'],
    ARRAY[
        'Master bug bounty methodology',
        'Learn advanced web application testing',
        'Understand mobile security testing',
        'Master API security assessment',
        'Learn responsible disclosure practices'
    ],
    20,
    true,
    false,
    9,
    '{"icon": "🐛", "color": "#27ae60", "featured_topics": ["Web Security", "API Testing", "Mobile Security"]}'::JSONB
);

-- =====================================================
-- SAMPLE SVG ASSETS
-- =====================================================

-- Insert sample SVG assets for network diagrams
INSERT INTO content_assets (asset_type, file_name, file_content, mime_type, alt_text, description, tags) VALUES
('svg', 'network_topology_star.svg', 
'<svg width="400" height="300" viewBox="0 0 400 300">
  <circle cx="200" cy="150" r="30" fill="#4A5CBA" stroke="#333" stroke-width="2"/>
  <text x="200" y="155" text-anchor="middle" fill="white" font-size="12">Switch</text>
  <circle cx="100" cy="80" r="20" fill="#88cc14" stroke="#333" stroke-width="2"/>
  <text x="100" y="85" text-anchor="middle" fill="black" font-size="10">PC1</text>
  <circle cx="300" cy="80" r="20" fill="#88cc14" stroke="#333" stroke-width="2"/>
  <text x="300" y="85" text-anchor="middle" fill="black" font-size="10">PC2</text>
  <circle cx="100" cy="220" r="20" fill="#88cc14" stroke="#333" stroke-width="2"/>
  <text x="100" y="225" text-anchor="middle" fill="black" font-size="10">PC3</text>
  <circle cx="300" cy="220" r="20" fill="#88cc14" stroke="#333" stroke-width="2"/>
  <text x="300" y="225" text-anchor="middle" fill="black" font-size="10">PC4</text>
  <line x1="120" y1="90" x2="180" y2="140" stroke="#667eea" stroke-width="3"/>
  <line x1="280" y1="90" x2="220" y2="140" stroke="#667eea" stroke-width="3"/>
  <line x1="120" y1="210" x2="180" y2="160" stroke="#667eea" stroke-width="3"/>
  <line x1="280" y1="210" x2="220" y2="160" stroke="#667eea" stroke-width="3"/>
</svg>',
'image/svg+xml',
'Star topology network diagram',
'Interactive star topology diagram showing central switch connected to multiple PCs',
ARRAY['networking', 'topology', 'star', 'diagram']
),

('svg', 'osi_model_layers.svg',
'<svg width="300" height="400" viewBox="0 0 300 400">
  <rect x="50" y="50" width="200" height="40" fill="#e74c3c" stroke="#333" stroke-width="2"/>
  <text x="150" y="75" text-anchor="middle" fill="white" font-weight="bold">7. Application</text>
  <rect x="50" y="90" width="200" height="40" fill="#f39c12" stroke="#333" stroke-width="2"/>
  <text x="150" y="115" text-anchor="middle" fill="white" font-weight="bold">6. Presentation</text>
  <rect x="50" y="130" width="200" height="40" fill="#f1c40f" stroke="#333" stroke-width="2"/>
  <text x="150" y="155" text-anchor="middle" fill="black" font-weight="bold">5. Session</text>
  <rect x="50" y="170" width="200" height="40" fill="#27ae60" stroke="#333" stroke-width="2"/>
  <text x="150" y="195" text-anchor="middle" fill="white" font-weight="bold">4. Transport</text>
  <rect x="50" y="210" width="200" height="40" fill="#3498db" stroke="#333" stroke-width="2"/>
  <text x="150" y="235" text-anchor="middle" fill="white" font-weight="bold">3. Network</text>
  <rect x="50" y="250" width="200" height="40" fill="#9b59b6" stroke="#333" stroke-width="2"/>
  <text x="150" y="275" text-anchor="middle" fill="white" font-weight="bold">2. Data Link</text>
  <rect x="50" y="290" width="200" height="40" fill="#34495e" stroke="#333" stroke-width="2"/>
  <text x="150" y="315" text-anchor="middle" fill="white" font-weight="bold">1. Physical</text>
</svg>',
'image/svg+xml',
'OSI Model seven layers diagram',
'Interactive OSI model diagram showing all seven layers with color coding',
ARRAY['networking', 'osi', 'model', 'layers', 'diagram']
);

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
SELECT 'Comprehensive learning content schema and initial data migration completed successfully!' as status;
