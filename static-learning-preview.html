<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberForce Learning Paths - Static Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stats-bar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-around;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4A5CBA;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .learning-paths {
            display: grid;
            gap: 30px;
        }

        .learning-path {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .learning-path:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }

        .path-header {
            border-bottom: 3px solid #88cc14;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .path-title {
            font-size: 2.5rem;
            color: #4A5CBA;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .path-icon {
            font-size: 2rem;
        }

        .path-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
        }

        .path-meta {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .meta-item {
            background: #f8f9fa;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #555;
            border: 2px solid #e9ecef;
        }

        .difficulty-beginner { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .difficulty-intermediate { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .difficulty-advanced { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .module-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .module-card:hover {
            border-color: #88cc14;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(136, 204, 20, 0.2);
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #88cc14, #4A5CBA);
        }

        .module-number {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #4A5CBA;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .module-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
            padding-right: 40px;
        }

        .module-description {
            color: #666;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .module-topics {
            margin-top: 15px;
        }

        .module-topics h4 {
            font-size: 0.9rem;
            color: #4A5CBA;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .topics-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .topic-tag {
            background: #88cc14;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .path-title {
                font-size: 1.8rem;
            }
            
            .modules-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-bar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ CyberForce Learning Paths</h1>
            <p class="subtitle">Comprehensive Cybersecurity Education Platform</p>
        </div>

        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">Learning Paths</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">45</div>
                <div class="stat-label">Total Modules</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">410</div>
                <div class="stat-label">Study Hours</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">150+</div>
                <div class="stat-label">Skills Covered</div>
            </div>
        </div>

        <div class="learning-paths">
            <!-- Networking Fundamentals -->
            <div class="learning-path">
                <div class="path-header">
                    <h2 class="path-title">
                        <span class="path-icon">🌐</span>
                        Networking Fundamentals
                    </h2>
                    <p class="path-description">Master the essential concepts of computer networking that form the foundation of cybersecurity. Learn about network protocols, architecture, and security principles.</p>
                    <div class="path-meta">
                        <span class="meta-item difficulty-beginner">Beginner</span>
                        <span class="meta-item">60 hours</span>
                        <span class="meta-item">25 modules</span>
                        <span class="meta-item">fundamentals</span>
                    </div>
                </div>
                <div class="modules-grid">
                    <div class="module-card">
                        <div class="module-number">1</div>
                        <h3 class="module-title">Introduction to Computer Networks</h3>
                        <p class="module-description">Learn the fundamental concepts of computer networks and their importance in modern computing and cybersecurity.</p>
                        <div class="module-topics">
                            <h4>Topics Covered:</h4>
                            <div class="topics-list">
                                <span class="topic-tag">What is a Computer Network?</span>
                                <span class="topic-tag">Network Topologies</span>
                                <span class="topic-tag">Network Types</span>
                                <span class="topic-tag">Security Implications</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="module-card">
                        <div class="module-number">2</div>
                        <h3 class="module-title">The OSI Model</h3>
                        <p class="module-description">Understand the seven layers of the OSI model and how they provide a framework for network communication.</p>
                        <div class="module-topics">
                            <h4>Topics Covered:</h4>
                            <div class="topics-list">
                                <span class="topic-tag">OSI Model Layers</span>
                                <span class="topic-tag">Data Encapsulation</span>
                                <span class="topic-tag">Protocol Data Units</span>
                                <span class="topic-tag">Layer Interactions</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="module-card">
                        <div class="module-number">3</div>
                        <h3 class="module-title">TCP/IP Protocol Suite</h3>
                        <p class="module-description">Explore the TCP/IP protocol stack, the foundation of internet communications.</p>
                        <div class="module-topics">
                            <h4>Topics Covered:</h4>
                            <div class="topics-list">
                                <span class="topic-tag">TCP/IP Protocol Stack</span>
                                <span class="topic-tag">IP Addressing</span>
                                <span class="topic-tag">TCP vs UDP</span>
                                <span class="topic-tag">Protocol Analysis</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="module-card">
                        <div class="module-number">4</div>
                        <h3 class="module-title">IP Addressing and Subnetting</h3>
                        <p class="module-description">Master IP addressing concepts, including IPv4, IPv6, and subnetting techniques.</p>
                        <div class="module-topics">
                            <h4>Topics Covered:</h4>
                            <div class="topics-list">
                                <span class="topic-tag">IPv4 Addressing</span>
                                <span class="topic-tag">Subnet Masks</span>
                                <span class="topic-tag">CIDR Notation</span>
                                <span class="topic-tag">IPv6 Addressing</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="module-card">
                        <div class="module-number">5</div>
                        <h3 class="module-title">Network Devices and Infrastructure</h3>
                        <p class="module-description">Learn about the hardware components that make up computer networks.</p>
                        <div class="module-topics">
                            <h4>Topics Covered:</h4>
                            <div class="topics-list">
                                <span class="topic-tag">Network Interface Cards</span>
                                <span class="topic-tag">Switches & Routers</span>
                                <span class="topic-tag">Wireless Access Points</span>
                                <span class="topic-tag">Firewalls</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="module-card">
                        <div class="module-number">6</div>
                        <h3 class="module-title">IPv6 Fundamentals</h3>
                        <p class="module-description">Understand the next generation of IP addressing and its implementation.</p>
                        <div class="module-topics">
                            <h4>Topics Covered:</h4>
                            <div class="topics-list">
                                <span class="topic-tag">IPv6 Address Structure</span>
                                <span class="topic-tag">Transition Mechanisms</span>
                                <span class="topic-tag">Security Considerations</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
