// Server-side proxy for Gemini API to avoid CORS issues
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { query, language } = req.body;
    
    if (!query) {
      return res.status(400).json({ error: 'Query is required' });
    }

    // API key
    const GEMINI_API_KEY = 'AIzaSyAkEun3_2XWuSxrJ745GQajn_6gscO12cU';
    
    // Make request to Gemini API
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              role: "user",
              parts: [{ 
                text: `You are CyberForce AI, an advanced cybersecurity assistant. Answer this question about cybersecurity in ${language || 'english'}: ${query}` 
              }]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topP: 0.8,
            topK: 40,
            maxOutputTokens: 1024
          }
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Gemini API error:', errorData);
      return res.status(response.status).json({ 
        error: 'Error from Gemini API', 
        details: errorData 
      });
    }

    const data = await response.json();
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      return res.status(200).json({ 
        content: data.candidates[0].content.parts[0].text,
        category: 'general'
      });
    } else {
      return res.status(500).json({ 
        error: 'Invalid response format from Gemini API',
        details: data
      });
    }
  } catch (error) {
    console.error('Error in Gemini proxy:', error);
    return res.status(500).json({ error: 'Internal server error', message: error.message });
  }
}
