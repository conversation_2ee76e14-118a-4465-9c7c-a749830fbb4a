#!/bin/bash

# Script to start the CyberForce AI solution
# This script starts the Ollama service, pulls the required models,
# starts the AI middleware service, and configures the application

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if <PERSON><PERSON> is running
check_docker() {
  echo -e "${YELLOW}Checking if Dock<PERSON> is running...${NC}"
  if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
    exit 1
  fi
  echo -e "${GREEN}Docker is running.${NC}"
}

# Function to check if Docker Compose is installed
check_docker_compose() {
  echo -e "${YELLOW}Checking if Docker Compose is installed...${NC}"
  if ! docker-compose --version > /dev/null 2>&1; then
    echo -e "${RED}Docker Compose is not installed. Please install Docker Compose and try again.${NC}"
    exit 1
  fi
  echo -e "${GREEN}Docker Compose is installed.${NC}"
}

# Function to start the Ollama service
start_ollama() {
  echo -e "${YELLOW}Starting Ollama service...${NC}"
  docker-compose -f docker-compose.ollama.yml up -d ollama
  
  # Wait for Ollama to start
  echo -e "${YELLOW}Waiting for Ollama service to start...${NC}"
  until curl -s http://localhost:11434/api/tags > /dev/null; do
    echo -e "${YELLOW}Waiting for Ollama to start...${NC}"
    sleep 5
  done
  
  echo -e "${GREEN}Ollama service started successfully.${NC}"
}

# Function to pull the required models
pull_models() {
  echo -e "${YELLOW}Pulling required models...${NC}"
  chmod +x src/services/ai-middleware/scripts/pull-models.sh
  ./src/services/ai-middleware/scripts/pull-models.sh
  
  if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to pull models. Please check the logs and try again.${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}Models pulled successfully.${NC}"
}

# Function to start the AI middleware service
start_middleware() {
  echo -e "${YELLOW}Starting AI middleware service...${NC}"
  docker-compose -f docker-compose.ollama.yml up -d ai-middleware
  
  # Wait for middleware to start
  echo -e "${YELLOW}Waiting for AI middleware service to start...${NC}"
  until curl -s http://localhost:3006/health > /dev/null; do
    echo -e "${YELLOW}Waiting for AI middleware to start...${NC}"
    sleep 5
  done
  
  echo -e "${GREEN}AI middleware service started successfully.${NC}"
}

# Function to check if the solution is ready
check_ready() {
  echo -e "${YELLOW}Checking if the solution is ready...${NC}"
  
  # Check if Ollama is ready
  if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo -e "${RED}Ollama service is not ready. Please check the logs and try again.${NC}"
    exit 1
  fi
  
  # Check if middleware is ready
  if ! curl -s http://localhost:3006/ready > /dev/null; then
    echo -e "${RED}AI middleware service is not ready. Please check the logs and try again.${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}CyberForce AI solution is ready!${NC}"
}

# Main function
main() {
  echo -e "${GREEN}Starting CyberForce AI solution...${NC}"
  
  # Check prerequisites
  check_docker
  check_docker_compose
  
  # Start services
  start_ollama
  pull_models
  start_middleware
  
  # Check if everything is ready
  check_ready
  
  echo -e "${GREEN}CyberForce AI solution is now running.${NC}"
  echo -e "${GREEN}Ollama service: http://localhost:11434${NC}"
  echo -e "${GREEN}AI middleware service: http://localhost:3006${NC}"
  echo -e "${YELLOW}To stop the solution, run: docker-compose -f docker-compose.ollama.yml down${NC}"
}

# Execute main function
main
