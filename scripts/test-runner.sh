#!/bin/bash

# CyberForce Comprehensive Test Runner
set -e

echo "🧪 CyberForce Test Suite Runner"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL=${BASE_URL:-"https://cyberforce.local"}
TEST_ENV=${TEST_ENV:-"production"}
SKIP_BUILD=${SKIP_BUILD:-false}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    log_success "All prerequisites are installed"
}

# Build application
build_application() {
    if [ "$SKIP_BUILD" = "true" ]; then
        log_info "Skipping build (SKIP_BUILD=true)"
        return
    fi
    
    log_info "Building CyberForce application..."
    
    # Install dependencies
    npm ci
    
    # Build the application
    npm run build
    
    # Build Docker images
    docker-compose -f docker-compose.prod.yml build
    
    log_success "Application built successfully"
}

# Start services
start_services() {
    log_info "Starting CyberForce services..."
    
    # Start with production compose file
    docker-compose -f docker-compose.prod.yml up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Health check
    for i in {1..10}; do
        if curl -f -s "$BASE_URL/health" > /dev/null; then
            log_success "Services are ready"
            return
        fi
        log_info "Waiting for services... (attempt $i/10)"
        sleep 10
    done
    
    log_error "Services failed to start properly"
    docker-compose -f docker-compose.prod.yml logs
    exit 1
}

# Run unit tests
run_unit_tests() {
    log_info "Running unit tests..."
    
    if npm test; then
        log_success "Unit tests passed"
    else
        log_error "Unit tests failed"
        return 1
    fi
}

# Run integration tests
run_integration_tests() {
    log_info "Running integration tests..."
    
    # Install Playwright if not installed
    if ! command -v playwright &> /dev/null; then
        log_info "Installing Playwright..."
        npx playwright install
    fi
    
    # Run E2E tests
    if npx playwright test; then
        log_success "Integration tests passed"
    else
        log_error "Integration tests failed"
        return 1
    fi
}

# Run load tests
run_load_tests() {
    log_info "Running load tests..."
    
    # Install k6 if not installed
    if ! command -v k6 &> /dev/null; then
        log_warning "k6 is not installed. Installing..."
        
        # Install k6 based on OS
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install k6
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update
            sudo apt-get install k6
        else
            log_error "Unsupported OS for k6 installation"
            return 1
        fi
    fi
    
    # Run load tests
    if k6 run tests/load/load-test.js; then
        log_success "Load tests passed"
    else
        log_error "Load tests failed"
        return 1
    fi
}

# Test authentication flow
test_authentication() {
    log_info "Testing authentication flow..."
    
    # Test signup
    SIGNUP_RESPONSE=$(curl -s -w "%{http_code}" -X POST "$BASE_URL/api/auth/signup" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"TestPassword123!","fullName":"Test User"}')
    
    if [[ "${SIGNUP_RESPONSE: -3}" =~ ^(200|201|409)$ ]]; then
        log_success "Signup endpoint working"
    else
        log_error "Signup endpoint failed (${SIGNUP_RESPONSE: -3})"
    fi
    
    # Test login
    LOGIN_RESPONSE=$(curl -s -w "%{http_code}" -X POST "$BASE_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"TestPassword123!"}')
    
    if [[ "${LOGIN_RESPONSE: -3}" =~ ^(200|401)$ ]]; then
        log_success "Login endpoint working"
    else
        log_error "Login endpoint failed (${LOGIN_RESPONSE: -3})"
    fi
}

# Test API endpoints
test_api_endpoints() {
    log_info "Testing API endpoints..."
    
    local endpoints=(
        "/api/learning-paths"
        "/api/challenges"
        "/api/user/progress"
        "/health"
    )
    
    for endpoint in "${endpoints[@]}"; do
        RESPONSE=$(curl -s -w "%{http_code}" "$BASE_URL$endpoint")
        HTTP_CODE="${RESPONSE: -3}"
        
        if [[ "$HTTP_CODE" =~ ^(200|401|403)$ ]]; then
            log_success "Endpoint $endpoint working ($HTTP_CODE)"
        else
            log_error "Endpoint $endpoint failed ($HTTP_CODE)"
        fi
    done
}

# Test SSL configuration
test_ssl() {
    log_info "Testing SSL configuration..."
    
    if curl -k -s "$BASE_URL" > /dev/null; then
        log_success "SSL endpoint accessible"
    else
        log_error "SSL endpoint failed"
    fi
    
    # Test SSL certificate
    SSL_INFO=$(echo | openssl s_client -connect cyberforce.local:443 -servername cyberforce.local 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        log_success "SSL certificate valid"
        echo "$SSL_INFO"
    else
        log_warning "SSL certificate check failed (self-signed certificate)"
    fi
}

# Generate test report
generate_report() {
    log_info "Generating test report..."
    
    REPORT_FILE="test-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > "$REPORT_FILE" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>CyberForce Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #88cc14; color: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
    </style>
</head>
<body>
    <div class="header">
        <h1>CyberForce Test Report</h1>
        <p>Generated on: $(date)</p>
        <p>Environment: $TEST_ENV</p>
        <p>Base URL: $BASE_URL</p>
    </div>
    
    <div class="section success">
        <h2>✅ Test Summary</h2>
        <p>All critical tests have been executed. Check individual sections for details.</p>
    </div>
    
    <div class="section">
        <h2>🔧 System Information</h2>
        <p><strong>Docker Version:</strong> $(docker --version)</p>
        <p><strong>Node.js Version:</strong> $(node --version)</p>
        <p><strong>npm Version:</strong> $(npm --version)</p>
    </div>
    
    <div class="section">
        <h2>🐳 Docker Services</h2>
        <pre>$(docker-compose -f docker-compose.prod.yml ps)</pre>
    </div>
    
    <div class="section">
        <h2>📊 Service Logs</h2>
        <p>Check Docker logs for detailed service information:</p>
        <code>docker-compose -f docker-compose.prod.yml logs</code>
    </div>
</body>
</html>
EOF
    
    log_success "Test report generated: $REPORT_FILE"
}

# Cleanup
cleanup() {
    log_info "Cleaning up..."
    
    # Stop services
    docker-compose -f docker-compose.prod.yml down
    
    # Remove test data
    docker volume prune -f
    
    log_success "Cleanup completed"
}

# Main execution
main() {
    echo "Starting test execution with the following configuration:"
    echo "- Base URL: $BASE_URL"
    echo "- Environment: $TEST_ENV"
    echo "- Skip Build: $SKIP_BUILD"
    echo ""
    
    # Run test phases
    check_prerequisites
    build_application
    start_services
    
    # Run tests
    test_authentication
    test_api_endpoints
    test_ssl
    
    # Optional comprehensive tests
    if [ "$1" = "--full" ]; then
        run_unit_tests
        run_integration_tests
        run_load_tests
    fi
    
    generate_report
    
    log_success "All tests completed successfully! 🎉"
}

# Handle script termination
trap cleanup EXIT

# Run main function
main "$@"
