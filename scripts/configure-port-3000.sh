#!/bin/bash

# Configure CyberForce to run on port 3000
# This allows direct access via dev.cyberforce.om:3000

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER_USER="ubuntu"
SERVER_HOST="*************"
SERVER_PATH="/var/www/cyberforce"
DOMAIN="dev.cyberforce.om"
SSH_KEY="/Users/<USER>/Downloads/cyf-dev-app-key.pem"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_header "Configure CyberForce for Port 3000"
print_status "Target: $SERVER_USER@$SERVER_HOST:$SERVER_PATH"
print_status "Domain: $DOMAIN:3000"

# Set correct permissions for SSH key
chmod 400 "$SSH_KEY" 2>/dev/null || true

# Execute configuration on server
print_status "Configuring application to run on port 3000..."
ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << 'EOF'
set -e

# Colors for remote output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[REMOTE]${NC} $1"; }
print_success() { echo -e "${GREEN}[REMOTE]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[REMOTE]${NC} $1"; }

print_status "Setting up application to run on port 3000..."

# Navigate to application directory
cd /var/www/cyberforce

# Install serve globally to serve the built application
print_status "Installing serve package globally..."
sudo npm install -g serve

# Create a systemd service to run the application on port 3000
print_status "Creating systemd service for CyberForce..."
sudo tee /etc/systemd/system/cyberforce.service > /dev/null << 'SERVICEEOF'
[Unit]
Description=CyberForce Application
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/var/www/cyberforce
ExecStart=/usr/bin/serve -s dist -l 3000
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
SERVICEEOF

# Reload systemd and enable the service
print_status "Enabling and starting CyberForce service..."
sudo systemctl daemon-reload
sudo systemctl enable cyberforce
sudo systemctl start cyberforce

# Update nginx configuration to proxy to port 3000
print_status "Updating nginx configuration for port 3000..."
sudo tee /etc/nginx/sites-available/dev.cyberforce.om > /dev/null << 'NGINXEOF'
server {
    listen 80;
    server_name dev.cyberforce.om;

    # Redirect to port 3000
    location / {
        return 301 http://dev.cyberforce.om:3000$request_uri;
    }
}

server {
    listen 3000;
    server_name dev.cyberforce.om;
    
    # Proxy to the application running on localhost:3000
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Health check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
NGINXEOF

# Test nginx configuration
print_status "Testing nginx configuration..."
sudo nginx -t

# Restart nginx
print_status "Restarting nginx..."
sudo systemctl restart nginx

# Open port 3000 in firewall (if ufw is enabled)
print_status "Opening port 3000 in firewall..."
sudo ufw allow 3000 2>/dev/null || print_warning "UFW not enabled or already configured"

# Check service status
print_status "Checking service status..."
sleep 5

SERVICE_STATUS=$(sudo systemctl is-active cyberforce)
NGINX_STATUS=$(sudo systemctl is-active nginx)

print_status "Service Status:"
echo "- CyberForce Service: $SERVICE_STATUS"
echo "- Nginx Service: $NGINX_STATUS"

# Test the application
print_status "Testing application on port 3000..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ | grep -q "200"; then
    print_success "Application is responding on port 3000!"
else
    print_warning "Application test failed, checking logs..."
    sudo systemctl status cyberforce --no-pager -l
fi

# Show logs if there are any issues
if [ "$SERVICE_STATUS" != "active" ]; then
    print_warning "Service not active, showing logs:"
    sudo journalctl -u cyberforce --no-pager -l --since "5 minutes ago"
fi

print_success "Configuration completed!"
print_status "Application should be available at:"
echo "- Direct: http://dev.cyberforce.om:3000"
echo "- Health: http://dev.cyberforce.om:3000/health"
echo "- Redirect from: http://dev.cyberforce.om (redirects to :3000)"

# Show service management commands
print_status "Service management commands:"
echo "- Check status: sudo systemctl status cyberforce"
echo "- View logs: sudo journalctl -u cyberforce -f"
echo "- Restart: sudo systemctl restart cyberforce"
echo "- Stop: sudo systemctl stop cyberforce"

EOF

print_header "Configuration Summary"
print_success "✅ Application configured to run on port 3000"
print_success "✅ Systemd service created and started"
print_success "✅ Nginx configured for port 3000 access"
print_success "✅ Firewall port 3000 opened"

echo -e "\n${GREEN}🎉 CyberForce is now running on port 3000!${NC}"
echo -e "${BLUE}Access your application at:${NC}"
echo "- Main URL: http://dev.cyberforce.om:3000"
echo "- Health Check: http://dev.cyberforce.om:3000/health"
echo "- Direct IP: http://*************:3000"

echo -e "\n${BLUE}Benefits of port 3000:${NC}"
echo "- Direct domain access without DNS changes"
echo "- No conflicts with other web services"
echo "- Easy to remember and share"
echo "- Standard development port"

print_status "Configuration completed at $(date)"
