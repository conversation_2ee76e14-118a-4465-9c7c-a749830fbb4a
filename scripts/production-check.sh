#!/bin/bash

# CyberForce Production Readiness Check Script
# This script validates that all components are working correctly

set -e

echo "🚀 CyberForce Production Readiness Check"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        ERRORS=$((ERRORS + 1))
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Initialize error counter
ERRORS=0

echo ""
echo "🔍 Checking System Requirements..."
echo "--------------------------------"

# Check Node.js version
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)
    if [ $MAJOR_VERSION -ge 18 ]; then
        print_status 0 "Node.js version: $NODE_VERSION"
    else
        print_status 1 "Node.js version too old: $NODE_VERSION (requires 18+)"
    fi
else
    print_status 1 "Node.js not found"
fi

# Check npm
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_status 0 "npm version: $NPM_VERSION"
else
    print_status 1 "npm not found"
fi

# Check Docker
if command -v docker &> /dev/null; then
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    print_status 0 "Docker version: $DOCKER_VERSION"
else
    print_status 1 "Docker not found"
fi

echo ""
echo "📁 Checking Project Structure..."
echo "-------------------------------"

# Check if we're in the right directory
if [ -f "package.json" ]; then
    print_status 0 "package.json found"
else
    print_status 1 "package.json not found - are you in the project root?"
    exit 1
fi

# Check key files
FILES=(
    "src/App.jsx"
    "src/components/dashboard/EnhancedDashboard.jsx"
    "src/database/progress_schema.sql"
    ".env.example"
    "src/index.css"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status 0 "$file exists"
    else
        print_status 1 "$file missing"
    fi
done

echo ""
echo "🔧 Checking Environment Configuration..."
echo "--------------------------------------"

# Check .env file
if [ -f ".env" ]; then
    print_status 0 ".env file exists"
    
    # Check required environment variables
    ENV_VARS=(
        "REACT_APP_SUPABASE_URL"
        "REACT_APP_SUPABASE_ANON_KEY"
    )
    
    for var in "${ENV_VARS[@]}"; do
        if grep -q "^$var=" .env; then
            VALUE=$(grep "^$var=" .env | cut -d'=' -f2)
            if [ "$VALUE" != "your-supabase-url" ] && [ "$VALUE" != "your-supabase-anon-key" ]; then
                print_status 0 "$var configured"
            else
                print_status 1 "$var not configured (still has default value)"
            fi
        else
            print_status 1 "$var not found in .env"
        fi
    done
else
    print_status 1 ".env file not found"
    print_info "Copy .env.example to .env and configure your values"
fi

echo ""
echo "📦 Checking Dependencies..."
echo "-------------------------"

# Check if node_modules exists
if [ -d "node_modules" ]; then
    print_status 0 "node_modules directory exists"
else
    print_status 1 "node_modules not found - run 'npm install'"
fi

# Check package-lock.json
if [ -f "package-lock.json" ]; then
    print_status 0 "package-lock.json exists"
else
    print_warning "package-lock.json not found - consider running 'npm install'"
fi

echo ""
echo "🐳 Checking Docker Services..."
echo "-----------------------------"

# Check if Docker is running
if docker info &> /dev/null; then
    print_status 0 "Docker daemon is running"
    
    # Check for Ollama container
    if docker ps | grep -q "cyberforce-ollama"; then
        print_status 0 "Ollama container is running"
        
        # Check Ollama API
        if curl -s http://localhost:11434/api/tags &> /dev/null; then
            print_status 0 "Ollama API is responding"
            
            # Check available models
            MODELS=$(curl -s http://localhost:11434/api/tags | jq -r '.models[].name' 2>/dev/null || echo "")
            if [ -n "$MODELS" ]; then
                print_status 0 "AI models available: $(echo $MODELS | tr '\n' ' ')"
            else
                print_status 1 "No AI models found - run 'docker exec cyberforce-ollama ollama pull llama2'"
            fi
        else
            print_status 1 "Ollama API not responding"
        fi
    else
        print_status 1 "Ollama container not running"
        print_info "Start with: docker run -d --name cyberforce-ollama -p 11434:11434 ollama/ollama"
    fi
else
    print_status 1 "Docker daemon not running"
fi

echo ""
echo "🏗️ Checking Build Process..."
echo "---------------------------"

# Check if build directory exists
if [ -d "dist" ] || [ -d "build" ]; then
    print_status 0 "Build directory exists"
else
    print_warning "Build directory not found - run 'npm run build'"
fi

# Try to run build (if npm is available)
if command -v npm &> /dev/null; then
    print_info "Testing build process..."
    if npm run build &> /dev/null; then
        print_status 0 "Build process successful"
    else
        print_status 1 "Build process failed"
    fi
fi

echo ""
echo "🧪 Running Basic Tests..."
echo "-----------------------"

# Check if test script exists
if npm run test --dry-run &> /dev/null; then
    print_info "Running tests..."
    if npm test -- --watchAll=false &> /dev/null; then
        print_status 0 "Tests passed"
    else
        print_status 1 "Tests failed"
    fi
else
    print_warning "No test script found"
fi

echo ""
echo "🔒 Security Checks..."
echo "-------------------"

# Check for security vulnerabilities
if command -v npm &> /dev/null; then
    print_info "Checking for security vulnerabilities..."
    if npm audit --audit-level=high &> /dev/null; then
        print_status 0 "No high-severity vulnerabilities found"
    else
        print_status 1 "Security vulnerabilities found - run 'npm audit fix'"
    fi
fi

# Check for sensitive files
SENSITIVE_FILES=(
    ".env"
    "*.key"
    "*.pem"
    "*.p12"
)

for pattern in "${SENSITIVE_FILES[@]}"; do
    if ls $pattern &> /dev/null; then
        print_warning "Sensitive files found: $pattern - ensure they're in .gitignore"
    fi
done

echo ""
echo "📊 Performance Checks..."
echo "----------------------"

# Check bundle size (if build exists)
if [ -d "dist" ]; then
    BUNDLE_SIZE=$(du -sh dist 2>/dev/null | cut -f1)
    print_info "Build size: $BUNDLE_SIZE"
    
    # Check for large files
    LARGE_FILES=$(find dist -size +1M -type f 2>/dev/null || echo "")
    if [ -n "$LARGE_FILES" ]; then
        print_warning "Large files found in build:"
        echo "$LARGE_FILES"
    fi
fi

echo ""
echo "🌐 Network Connectivity..."
echo "------------------------"

# Check internet connectivity
if ping -c 1 google.com &> /dev/null; then
    print_status 0 "Internet connectivity available"
else
    print_status 1 "No internet connectivity"
fi

# Check Supabase connectivity (if configured)
if [ -f ".env" ] && grep -q "REACT_APP_SUPABASE_URL" .env; then
    SUPABASE_URL=$(grep "REACT_APP_SUPABASE_URL" .env | cut -d'=' -f2)
    if [ "$SUPABASE_URL" != "your-supabase-url" ]; then
        if curl -s "$SUPABASE_URL/rest/v1/" &> /dev/null; then
            print_status 0 "Supabase connectivity verified"
        else
            print_status 1 "Cannot connect to Supabase"
        fi
    fi
fi

echo ""
echo "📋 Summary"
echo "=========="

if [ $ERRORS -eq 0 ]; then
    echo -e "${GREEN}🎉 All checks passed! Your CyberForce platform is production-ready.${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Deploy to your production server"
    echo "2. Configure SSL certificates"
    echo "3. Set up monitoring and backups"
    echo "4. Run final end-to-end tests"
else
    echo -e "${RED}❌ $ERRORS issues found. Please fix them before deploying to production.${NC}"
    echo ""
    echo "Common fixes:"
    echo "• Configure environment variables in .env"
    echo "• Install dependencies with 'npm install'"
    echo "• Start Ollama container for AI features"
    echo "• Fix any security vulnerabilities"
fi

echo ""
echo "📚 Documentation:"
echo "• Production Deployment: ./PRODUCTION_DEPLOYMENT.md"
echo "• Ollama Setup: ./docker/ollama-setup.md"
echo "• Progress Tracking: ./PROGRESS_TRACKING_SETUP.md"

exit $ERRORS
