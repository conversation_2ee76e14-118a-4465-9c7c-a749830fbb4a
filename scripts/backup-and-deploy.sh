#!/bin/bash

# CyberForce Backup and Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
BACKUP_DIR="./backup"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="cyberforce_backup_$TIMESTAMP"
DEPLOYMENT_ENV=${DEPLOYMENT_ENV:-"production"}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directory
create_backup_structure() {
    log_info "Creating backup structure..."
    
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME"/{source,database,config,ssl,logs}
    
    log_success "Backup structure created"
}

# Backup source code
backup_source_code() {
    log_info "Backing up source code..."
    
    # Create source code archive
    tar -czf "$BACKUP_DIR/$BACKUP_NAME/source/cyberforce-source.tar.gz" \
        --exclude=node_modules \
        --exclude=dist \
        --exclude=.git \
        --exclude=backup \
        --exclude=logs \
        .
    
    # Copy important files
    cp package.json "$BACKUP_DIR/$BACKUP_NAME/source/"
    cp package-lock.json "$BACKUP_DIR/$BACKUP_NAME/source/" 2>/dev/null || true
    cp .env.production "$BACKUP_DIR/$BACKUP_NAME/source/" 2>/dev/null || true
    
    log_success "Source code backed up"
}

# Backup database
backup_database() {
    log_info "Backing up database..."
    
    # Backup PostgreSQL if running
    if docker ps | grep -q cyberforce-postgres; then
        docker exec cyberforce-postgres pg_dump -U cyberforce cyberforce > \
            "$BACKUP_DIR/$BACKUP_NAME/database/postgres_backup.sql"
        log_success "PostgreSQL backup completed"
    else
        log_warning "PostgreSQL container not running, skipping database backup"
    fi
    
    # Backup Redis if running
    if docker ps | grep -q cyberforce-redis; then
        docker exec cyberforce-redis redis-cli BGSAVE
        sleep 5
        docker cp cyberforce-redis:/data/dump.rdb "$BACKUP_DIR/$BACKUP_NAME/database/"
        log_success "Redis backup completed"
    else
        log_warning "Redis container not running, skipping Redis backup"
    fi
}

# Backup configuration
backup_configuration() {
    log_info "Backing up configuration..."
    
    # Copy Docker configurations
    cp docker-compose.prod.yml "$BACKUP_DIR/$BACKUP_NAME/config/" 2>/dev/null || true
    cp Dockerfile "$BACKUP_DIR/$BACKUP_NAME/config/" 2>/dev/null || true
    cp nginx.conf "$BACKUP_DIR/$BACKUP_NAME/config/" 2>/dev/null || true
    cp docker-entrypoint.sh "$BACKUP_DIR/$BACKUP_NAME/config/" 2>/dev/null || true
    
    # Copy monitoring configurations
    cp -r monitoring "$BACKUP_DIR/$BACKUP_NAME/config/" 2>/dev/null || true
    
    log_success "Configuration backed up"
}

# Backup SSL certificates
backup_ssl() {
    log_info "Backing up SSL certificates..."
    
    if [ -d "./ssl" ]; then
        cp -r ssl "$BACKUP_DIR/$BACKUP_NAME/"
        log_success "SSL certificates backed up"
    else
        log_warning "SSL directory not found, skipping SSL backup"
    fi
}

# Backup logs
backup_logs() {
    log_info "Backing up logs..."
    
    if [ -d "./logs" ]; then
        cp -r logs "$BACKUP_DIR/$BACKUP_NAME/"
        log_success "Logs backed up"
    else
        log_warning "Logs directory not found, skipping logs backup"
    fi
}

# Create deployment package
create_deployment_package() {
    log_info "Creating deployment package..."
    
    # Create deployment archive
    cd "$BACKUP_DIR"
    tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    
    # Create deployment script
    cat > "${BACKUP_NAME}_deploy.sh" << 'EOF'
#!/bin/bash

# CyberForce Deployment Script
set -e

BACKUP_NAME=$(basename "$0" _deploy.sh)
DEPLOY_DIR="/opt/cyberforce"

echo "🚀 Deploying CyberForce from backup: $BACKUP_NAME"

# Create deployment directory
sudo mkdir -p "$DEPLOY_DIR"
cd "$DEPLOY_DIR"

# Extract backup
sudo tar -xzf "/tmp/${BACKUP_NAME}.tar.gz"
cd "$BACKUP_NAME"

# Extract source code
cd source
sudo tar -xzf cyberforce-source.tar.gz
cd ..

# Set up environment
sudo cp source/.env.production .env

# Install dependencies and build
sudo docker-compose -f config/docker-compose.prod.yml build

# Start services
sudo docker-compose -f config/docker-compose.prod.yml up -d

echo "✅ CyberForce deployed successfully!"
echo "🌐 Access the application at: https://your-domain.com"
EOF
    
    chmod +x "${BACKUP_NAME}_deploy.sh"
    
    cd ..
    
    log_success "Deployment package created: $BACKUP_DIR/${BACKUP_NAME}.tar.gz"
}

# Generate backup manifest
generate_manifest() {
    log_info "Generating backup manifest..."
    
    cat > "$BACKUP_DIR/$BACKUP_NAME/MANIFEST.md" << EOF
# CyberForce Backup Manifest

**Backup Created:** $(date)
**Backup Name:** $BACKUP_NAME
**Environment:** $DEPLOYMENT_ENV

## Contents

### Source Code
- Complete application source code (excluding node_modules, dist, .git)
- package.json and package-lock.json
- Environment configuration

### Database
- PostgreSQL database dump
- Redis data dump

### Configuration
- Docker Compose files
- Nginx configuration
- Docker entrypoint script
- Monitoring configurations

### SSL Certificates
- SSL certificate files
- Private keys

### Logs
- Application logs
- Nginx logs
- System logs

## Deployment Instructions

1. Copy the backup archive to your target server
2. Extract the archive: \`tar -xzf ${BACKUP_NAME}.tar.gz\`
3. Run the deployment script: \`./${BACKUP_NAME}_deploy.sh\`
4. Configure your domain and SSL certificates
5. Update environment variables as needed

## System Requirements

- Docker 20.10+
- Docker Compose 2.0+
- 4GB RAM minimum
- 20GB disk space minimum
- Ubuntu 20.04+ or similar Linux distribution

## Security Notes

- Change all default passwords before deployment
- Update SSL certificates with your domain certificates
- Review and update environment variables
- Configure firewall rules appropriately

## Support

For support and documentation, visit: https://github.com/your-org/cyberforce
EOF
    
    log_success "Backup manifest generated"
}

# Test backup integrity
test_backup_integrity() {
    log_info "Testing backup integrity..."
    
    # Test archive integrity
    if tar -tzf "$BACKUP_DIR/${BACKUP_NAME}.tar.gz" > /dev/null; then
        log_success "Backup archive integrity verified"
    else
        log_error "Backup archive is corrupted"
        return 1
    fi
    
    # Test source code archive
    if tar -tzf "$BACKUP_DIR/$BACKUP_NAME/source/cyberforce-source.tar.gz" > /dev/null; then
        log_success "Source code archive integrity verified"
    else
        log_error "Source code archive is corrupted"
        return 1
    fi
}

# Upload to cloud storage (optional)
upload_to_cloud() {
    if [ -n "$AWS_S3_BUCKET" ]; then
        log_info "Uploading backup to AWS S3..."
        
        if command -v aws &> /dev/null; then
            aws s3 cp "$BACKUP_DIR/${BACKUP_NAME}.tar.gz" "s3://$AWS_S3_BUCKET/backups/"
            aws s3 cp "$BACKUP_DIR/${BACKUP_NAME}_deploy.sh" "s3://$AWS_S3_BUCKET/backups/"
            log_success "Backup uploaded to S3"
        else
            log_warning "AWS CLI not installed, skipping S3 upload"
        fi
    fi
}

# Cleanup old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups..."
    
    # Keep only last 5 backups
    cd "$BACKUP_DIR"
    ls -t cyberforce_backup_*.tar.gz | tail -n +6 | xargs rm -f 2>/dev/null || true
    ls -t cyberforce_backup_*_deploy.sh | tail -n +6 | xargs rm -f 2>/dev/null || true
    
    # Remove backup directories older than 7 days
    find . -name "cyberforce_backup_*" -type d -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    
    log_success "Old backups cleaned up"
}

# Main backup function
create_backup() {
    echo "🔄 Starting CyberForce backup process..."
    echo "Backup name: $BACKUP_NAME"
    echo ""
    
    create_backup_structure
    backup_source_code
    backup_database
    backup_configuration
    backup_ssl
    backup_logs
    generate_manifest
    create_deployment_package
    test_backup_integrity
    upload_to_cloud
    cleanup_old_backups
    
    echo ""
    log_success "🎉 Backup completed successfully!"
    echo "📦 Backup location: $BACKUP_DIR/${BACKUP_NAME}.tar.gz"
    echo "🚀 Deployment script: $BACKUP_DIR/${BACKUP_NAME}_deploy.sh"
    echo ""
    echo "To deploy on a new server:"
    echo "1. Copy both files to the target server"
    echo "2. Run: chmod +x ${BACKUP_NAME}_deploy.sh"
    echo "3. Run: ./${BACKUP_NAME}_deploy.sh"
}

# Show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  backup              Create a complete backup"
    echo "  deploy <backup>     Deploy from a backup file"
    echo "  list                List available backups"
    echo "  cleanup             Clean up old backups"
    echo "  --help              Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  DEPLOYMENT_ENV      Deployment environment (default: production)"
    echo "  AWS_S3_BUCKET       S3 bucket for backup storage (optional)"
}

# List available backups
list_backups() {
    echo "📋 Available backups:"
    echo ""
    
    if [ -d "$BACKUP_DIR" ]; then
        ls -la "$BACKUP_DIR"/cyberforce_backup_*.tar.gz 2>/dev/null || echo "No backups found"
    else
        echo "Backup directory not found"
    fi
}

# Main execution
case "${1:-backup}" in
    "backup")
        create_backup
        ;;
    "list")
        list_backups
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    "--help"|"help")
        show_usage
        ;;
    *)
        echo "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
