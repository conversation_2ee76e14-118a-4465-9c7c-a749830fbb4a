# Database Migration Guide

This guide explains how to migrate data from the old Supabase instance to the new one.

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Access to both Supabase instances

## Configuration

The migration script uses the following Supabase instances:

### Source (Old) Supabase Instance
- URL: `https://acralqosjpbrbnhpduko.supabase.co`
- Anon Key: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFjcmFscW9zanBicmJuaHBkdWtvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA0MjY0NzcsImV4cCI6MjA1NjAwMjQ3N30.1ijQwuDX60O5E0bHskmUGOaYNsOUYeqex56DpTPT650`

### Target (New) Supabase Instance
- URL: `https://yselfyopwwtslluyreoo.supabase.co`
- Anon Key: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzZWxmeW9wd3d0c2xsdXlyZW9vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDAxNTU4ODYsImV4cCI6MjA1NTczMTg4Nn0.uw3o_iNlD3Ytm5WI8pO-AyEEfPAwLIwbG1_15oTDpTc`

## Migration Steps

### 1. Set Up the Database Schema

Before migrating data, you need to set up the database schema in the new Supabase instance:

1. Log in to the Supabase dashboard for the new instance
2. Go to the SQL Editor
3. Copy the contents of `scripts/schema.sql` and run it in the SQL Editor

This will create all the necessary tables, indexes, and relationships in the new database.

### 2. Run the Migration Script

The migration script will transfer all data from the old Supabase instance to the new one:

```bash
# Using npm
npm run migrate-db

# Using yarn
yarn migrate-db
```

The script will:
- Migrate data from all tables in the correct order (respecting foreign key constraints)
- Migrate storage buckets and files
- Log all operations to both the console and a log file in the `migration_logs` directory

### 3. Verify the Migration

After the migration is complete, verify that all data has been transferred correctly:

1. Log in to the Supabase dashboard for the new instance
2. Go to the Table Editor and check that all tables have data
3. Go to the Storage section and check that all buckets and files have been transferred
4. Run the application with the new Supabase configuration and test all functionality

### 4. Update the Application Configuration

The application has already been updated to use the new Supabase instance. The changes include:

1. Updated `.env` file with the new Supabase URL and key
2. Updated `src/lib/supabase.js` to use the new Supabase URL and key

### 5. Troubleshooting

If you encounter any issues during the migration:

1. Check the migration logs in the `migration_logs` directory
2. If a specific table fails to migrate, you can modify the migration script to start from that table
3. For storage issues, you may need to manually transfer files

## Tables Migrated

The migration script transfers data from the following tables:

1. `users`
2. `user_profiles`
3. `user_coins`
4. `learning_paths`
5. `learning_modules`
6. `module_categories`
7. `module_sections`
8. `module_content`
9. `module_progress`
10. `challenges`
11. `challenge_categories`
12. `challenge_content`
13. `challenge_submissions`
14. `leaderboard`
15. `products`
16. `product_variants`
17. `orders`
18. `order_items`
19. `threat_data_cache`
20. `ai_responses`
21. `security_notifications`

## Storage Buckets Migrated

The migration script also transfers all storage buckets and their contents.

## Post-Migration Tasks

After the migration is complete, you may want to:

1. Set up authentication providers in the new Supabase instance
2. Configure storage bucket permissions
3. Set up any additional Supabase features (Edge Functions, Realtime, etc.)
4. Update any API keys or service accounts that use the old Supabase instance

## Rollback Plan

If you need to roll back to the old Supabase instance:

1. Update the `.env` file with the old Supabase URL and key
2. Update `src/lib/supabase.js` to use the old Supabase URL and key
3. Restart the application

## Support

If you encounter any issues during the migration, please contact the development team for assistance.
