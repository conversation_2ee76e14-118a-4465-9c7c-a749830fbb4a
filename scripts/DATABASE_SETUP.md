# Supabase Database Setup Guide

This guide explains how to set up the database for the CyberForce application using Supabase.

## Option 1: Using the Supabase SQL Editor (Recommended)

The easiest way to set up the database is to use the Supabase SQL Editor:

1. Log in to the Supabase dashboard at https://yselfyopwwtslluyreoo.supabase.co/dashboard/
2. Go to the SQL Editor
3. Copy the contents of `scripts/direct-setup.sql` and paste it into the SQL Editor
4. Click "Run" to execute the SQL script

This will create all the necessary tables, indexes, and relationships in the database, and insert some sample data.

## Option 2: Using the Setup Script

Alternatively, you can use the setup script to set up the database:

```bash
# Using npm
npm run setup-db

# Using yarn
yarn setup-db
```

This script will:
1. Create all the necessary tables, indexes, and relationships in the database
2. Insert sample data into the database

## Database Schema

The database schema includes the following tables:

1. `users` - User accounts
2. `user_profiles` - User profile information
3. `user_coins` - User coin balances
4. `learning_paths` - Learning paths
5. `learning_modules` - Learning modules
6. `module_categories` - Module categories
7. `module_sections` - Module sections
8. `module_content` - Module content
9. `module_progress` - User progress in modules
10. `challenges` - Challenges
11. `challenge_categories` - Challenge categories
12. `challenge_content` - Challenge content
13. `challenge_submissions` - User challenge submissions
14. `leaderboard` - Leaderboard rankings
15. `products` - Products
16. `product_variants` - Product variants
17. `orders` - Orders
18. `order_items` - Order items
19. `threat_data_cache` - Cached threat data
20. `ai_responses` - AI responses
21. `security_notifications` - Security notifications

## Sample Data

The setup process includes inserting sample data into the database:

- Learning paths for Network Fundamentals, Operating System Security, Ethical Hacking, Red Team Operations, and Blue Team Defense
- Challenges for Network Reconnaissance, Web Application Vulnerabilities, Password Cracking, and Network Traffic Analysis
- Challenge categories for Web Security, Network Security, Cryptography, and Forensics

## Troubleshooting

If you encounter any issues during the setup process:

1. Check that you have the correct Supabase URL and API key in the `.env` file
2. Make sure you have the necessary permissions to create tables in the Supabase database
3. If using the setup script, check that you have Node.js installed and all dependencies are installed

If you continue to experience issues, try running the SQL script directly in the Supabase SQL Editor (Option 1).

## Verifying the Setup

To verify that the database has been set up correctly:

1. Go to the Supabase dashboard
2. Navigate to the Table Editor
3. You should see all the tables listed in the sidebar
4. Click on a table to view its contents and verify that sample data has been inserted

## Next Steps

After setting up the database, you can:

1. Start the application with `npm run dev` or `yarn dev`
2. Create a user account in the application
3. Explore the learning paths and challenges

## Support

If you encounter any issues during the setup process, please contact the development team for assistance.
