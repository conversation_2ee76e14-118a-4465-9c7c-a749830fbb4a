/**
 * Database Migration Script
 * 
 * This script migrates data from the old Supabase instance to the new one.
 * It handles all tables and relationships, preserving data integrity.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Source (old) Supabase instance
const SOURCE_SUPABASE_URL = 'https://acralqosjpbrbnhpduko.supabase.co';
const SOURCE_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFjcmFscW9zanBicmJuaHBkdWtvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA0MjY0NzcsImV4cCI6MjA1NjAwMjQ3N30.1ijQwuDX60O5E0bHskmUGOaYNsOUYeqex56DpTPT650';

// Target (new) Supabase instance
const TARGET_SUPABASE_URL = 'https://yselfyopwwtslluyreoo.supabase.co';
const TARGET_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzZWxmeW9wd3d0c2xsdXlyZW9vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDAxNTU4ODYsImV4cCI6MjA1NTczMTg4Nn0.uw3o_iNlD3Ytm5WI8pO-AyEEfPAwLIwbG1_15oTDpTc';

// Initialize Supabase clients
const sourceSupabase = createClient(SOURCE_SUPABASE_URL, SOURCE_SUPABASE_KEY);
const targetSupabase = createClient(TARGET_SUPABASE_URL, TARGET_SUPABASE_KEY);

// Tables to migrate in order (respecting foreign key constraints)
const TABLES_TO_MIGRATE = [
  'users',
  'user_profiles',
  'user_coins',
  'learning_paths',
  'learning_modules',
  'module_categories',
  'module_sections',
  'module_content',
  'module_progress',
  'challenges',
  'challenge_categories',
  'challenge_content',
  'challenge_submissions',
  'leaderboard',
  'products',
  'product_variants',
  'orders',
  'order_items',
  'threat_data_cache',
  'ai_responses',
  'security_notifications'
];

// Create a log directory if it doesn't exist
const LOG_DIR = path.join(process.cwd(), 'migration_logs');
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR);
}

// Log file for the migration
const LOG_FILE = path.join(LOG_DIR, `migration_${new Date().toISOString().replace(/:/g, '-')}.log`);

/**
 * Log a message to both console and log file
 * @param {string} message - Message to log
 * @param {boolean} isError - Whether this is an error message
 */
function log(message, isError = false) {
  const timestamp = new Date().toISOString();
  const formattedMessage = `[${timestamp}] ${message}`;
  
  if (isError) {
    console.error(formattedMessage);
  } else {
    console.log(formattedMessage);
  }
  
  fs.appendFileSync(LOG_FILE, formattedMessage + '\n');
}

/**
 * Migrate data from one table
 * @param {string} tableName - Name of the table to migrate
 */
async function migrateTable(tableName) {
  log(`Starting migration of table: ${tableName}`);
  
  try {
    // Get all data from source table
    const { data: sourceData, error: sourceError } = await sourceSupabase
      .from(tableName)
      .select('*');
    
    if (sourceError) {
      throw new Error(`Error fetching data from source table ${tableName}: ${sourceError.message}`);
    }
    
    if (!sourceData || sourceData.length === 0) {
      log(`No data found in source table ${tableName}, skipping...`);
      return;
    }
    
    log(`Found ${sourceData.length} rows in source table ${tableName}`);
    
    // Insert data into target table
    const { data: insertData, error: insertError } = await targetSupabase
      .from(tableName)
      .upsert(sourceData, { onConflict: 'id' });
    
    if (insertError) {
      throw new Error(`Error inserting data into target table ${tableName}: ${insertError.message}`);
    }
    
    log(`Successfully migrated ${sourceData.length} rows to target table ${tableName}`);
  } catch (error) {
    log(`Migration failed for table ${tableName}: ${error.message}`, true);
    throw error;
  }
}

/**
 * Migrate storage buckets and files
 */
async function migrateStorage() {
  log('Starting migration of storage buckets and files');
  
  try {
    // Get list of buckets from source
    const { data: sourceBuckets, error: sourceBucketsError } = await sourceSupabase
      .storage
      .listBuckets();
    
    if (sourceBucketsError) {
      throw new Error(`Error fetching buckets from source: ${sourceBucketsError.message}`);
    }
    
    log(`Found ${sourceBuckets.length} buckets in source`);
    
    // Process each bucket
    for (const bucket of sourceBuckets) {
      log(`Processing bucket: ${bucket.name}`);
      
      // Create bucket in target if it doesn't exist
      try {
        const { data: createBucketData, error: createBucketError } = await targetSupabase
          .storage
          .createBucket(bucket.name, { public: bucket.public });
        
        if (createBucketError && !createBucketError.message.includes('already exists')) {
          throw new Error(`Error creating bucket ${bucket.name} in target: ${createBucketError.message}`);
        }
      } catch (error) {
        log(`Note: Bucket ${bucket.name} may already exist in target: ${error.message}`);
      }
      
      // List files in source bucket
      const { data: sourceFiles, error: sourceFilesError } = await sourceSupabase
        .storage
        .from(bucket.name)
        .list();
      
      if (sourceFilesError) {
        throw new Error(`Error listing files in source bucket ${bucket.name}: ${sourceFilesError.message}`);
      }
      
      log(`Found ${sourceFiles.length} files in bucket ${bucket.name}`);
      
      // Process each file
      for (const file of sourceFiles) {
        if (file.id) {  // Skip folders
          log(`Migrating file: ${file.name}`);
          
          // Download file from source
          const { data: fileData, error: downloadError } = await sourceSupabase
            .storage
            .from(bucket.name)
            .download(file.name);
          
          if (downloadError) {
            log(`Error downloading file ${file.name} from source bucket ${bucket.name}: ${downloadError.message}`, true);
            continue;
          }
          
          // Upload file to target
          const { data: uploadData, error: uploadError } = await targetSupabase
            .storage
            .from(bucket.name)
            .upload(file.name, fileData, {
              contentType: file.metadata?.mimetype || 'application/octet-stream',
              upsert: true
            });
          
          if (uploadError) {
            log(`Error uploading file ${file.name} to target bucket ${bucket.name}: ${uploadError.message}`, true);
            continue;
          }
          
          log(`Successfully migrated file ${file.name}`);
        }
      }
    }
    
    log('Storage migration completed successfully');
  } catch (error) {
    log(`Storage migration failed: ${error.message}`, true);
    throw error;
  }
}

/**
 * Main migration function
 */
async function migrateDatabase() {
  log('Starting database migration');
  
  try {
    // Migrate tables
    for (const table of TABLES_TO_MIGRATE) {
      await migrateTable(table);
    }
    
    // Migrate storage
    await migrateStorage();
    
    log('Database migration completed successfully');
  } catch (error) {
    log(`Database migration failed: ${error.message}`, true);
    process.exit(1);
  }
}

// Run the migration
migrateDatabase();
