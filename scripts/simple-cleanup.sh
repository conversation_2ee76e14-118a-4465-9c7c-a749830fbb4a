#!/bin/bash

# Simple Server Cleanup Script for Ubuntu user
# This script cleans up old files with proper permissions

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER_USER="ubuntu"
SERVER_HOST="*************"
SERVER_PATH="/var/www/cyberforce"
DOMAIN="dev.cyberforce.om"
SSH_KEY="/Users/<USER>/Downloads/cyf-dev-app-key.pem"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_header "Simple Server Cleanup"
print_status "Target: $SERVER_USER@$SERVER_HOST:$SERVER_PATH"
print_status "Domain: $DOMAIN"

# Set correct permissions for SSH key
chmod 400 "$SSH_KEY" 2>/dev/null || true

# Execute cleanup on server
print_status "Executing cleanup on server..."
ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << 'EOF'
set -e

# Colors for remote output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[REMOTE]${NC} $1"; }
print_success() { echo -e "${GREEN}[REMOTE]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[REMOTE]${NC} $1"; }

print_status "Starting simple server cleanup..."

# Remove old application directories
print_status "Removing old application files..."
sudo rm -rf /var/www/cyberforce* 2>/dev/null || true
sudo rm -rf /var/www/html/* 2>/dev/null || true

# Remove old nginx configurations
print_status "Cleaning nginx configurations..."
sudo rm -f /etc/nginx/sites-available/dev.cyberforce.om 2>/dev/null || true
sudo rm -f /etc/nginx/sites-enabled/dev.cyberforce.om 2>/dev/null || true

# Clean temporary files
print_status "Cleaning temporary files..."
sudo rm -rf /tmp/cyberforce-* 2>/dev/null || true
sudo rm -rf /tmp/nginx-* 2>/dev/null || true

# Clean logs
print_status "Cleaning logs..."
sudo rm -rf /var/log/nginx/*cyberforce* 2>/dev/null || true

# Stop any running Docker containers
print_status "Stopping Docker containers..."
sudo docker stop $(sudo docker ps -q --filter "name=cyberforce") 2>/dev/null || true
sudo docker rm $(sudo docker ps -aq --filter "name=cyberforce") 2>/dev/null || true

# Clean npm cache
print_status "Cleaning npm cache..."
npm cache clean --force 2>/dev/null || true

# Update system packages
print_status "Updating system packages..."
sudo apt-get update -qq
sudo apt-get upgrade -y -qq
sudo apt-get autoremove -y -qq
sudo apt-get autoclean -qq

# Create fresh application directory
print_status "Creating fresh application directory..."
sudo mkdir -p /var/www/cyberforce
sudo chown -R ubuntu:ubuntu /var/www/cyberforce
sudo chmod -R 755 /var/www/cyberforce

# Ensure nginx is running
print_status "Ensuring nginx is running..."
sudo systemctl enable nginx
sudo systemctl start nginx || sudo systemctl restart nginx

print_success "Simple cleanup completed successfully!"
print_status "Server is ready for deployment"

# Show current status
print_status "Current status:"
echo "- Nginx status: $(sudo systemctl is-active nginx)"
echo "- Application directory: $(ls -la /var/www/ | grep cyberforce || echo 'Created and ready')"
echo "- Available disk space: $(df -h / | tail -1 | awk '{print $4}')"
echo "- Available memory: $(free -h | grep '^Mem:' | awk '{print $7}')"

EOF

print_header "Cleanup Summary"
print_success "✅ Old application files removed"
print_success "✅ Nginx configurations cleaned"
print_success "✅ Temporary files cleaned"
print_success "✅ System packages updated"
print_success "✅ Fresh application directory created"
print_success "✅ Nginx service restarted"

echo -e "\n${GREEN}🧹 Server cleanup completed successfully!${NC}"
echo -e "${BLUE}Server is now ready for fresh deployment.${NC}"
echo -e "${BLUE}Next step: Run the deployment script${NC}"
echo "  ./scripts/deploy-to-server.sh"

print_status "Cleanup completed at $(date)"
