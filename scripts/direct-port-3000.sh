#!/bin/bash

# Direct port 3000 setup - bypass nginx and run directly
# This will make the application directly accessible on port 3000

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER_USER="ubuntu"
SERVER_HOST="*************"
SSH_KEY="/Users/<USER>/Downloads/cyf-dev-app-key.pem"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_header "Direct Port 3000 Setup"

# Set correct permissions for SSH key
chmod 400 "$SSH_KEY" 2>/dev/null || true

# Execute direct setup on server
print_status "Setting up direct port 3000 access..."
ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << 'EOF'
set -e

# Colors for remote output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[REMOTE]${NC} $1"; }
print_success() { echo -e "${GREEN}[REMOTE]${NC} $1"; }

print_status "Setting up direct port 3000 access..."

# Stop the current service
print_status "Stopping current service..."
sudo systemctl stop cyberforce

# Remove nginx proxy configuration and use simple direct access
print_status "Updating nginx for direct access..."
sudo tee /etc/nginx/sites-available/dev.cyberforce.om > /dev/null << 'NGINXEOF'
server {
    listen 80;
    server_name dev.cyberforce.om;
    
    # Simple redirect to port 3000
    location / {
        return 301 http://dev.cyberforce.om:3000$request_uri;
    }
}
NGINXEOF

# Test and reload nginx
sudo nginx -t
sudo systemctl reload nginx

# Create a simple service that uses Python's built-in server on port 3000
print_status "Creating simple HTTP server service..."
sudo tee /etc/systemd/system/cyberforce.service > /dev/null << 'SERVICEEOF'
[Unit]
Description=CyberForce Application
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/var/www/cyberforce/dist
ExecStart=/usr/bin/python3 -m http.server 3000
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
SERVICEEOF

# Reload systemd and start the service
print_status "Starting simple HTTP server on port 3000..."
sudo systemctl daemon-reload
sudo systemctl start cyberforce

# Wait for the service to start
sleep 3

# Check service status
SERVICE_STATUS=$(sudo systemctl is-active cyberforce)
print_status "Service Status: $SERVICE_STATUS"

# Test the application
print_status "Testing application on port 3000..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ | grep -q "200"; then
    print_success "✅ Application is responding on port 3000!"
else
    print_status "Waiting and testing again..."
    sleep 5
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ | grep -q "200"; then
        print_success "✅ Application is now responding on port 3000!"
    else
        print_status "Checking service logs..."
        sudo journalctl -u cyberforce --no-pager -l --since "2 minutes ago"
    fi
fi

# Verify port is listening
print_status "Verifying port 3000 is listening..."
if ss -tlnp | grep :3000; then
    print_success "✅ Port 3000 is listening!"
else
    print_status "Port 3000 not found"
fi

# Test external access
print_status "Testing external access..."
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://*************:3000/ || echo "failed")
if [ "$RESPONSE" = "200" ]; then
    print_success "✅ External access working!"
else
    print_status "External response: $RESPONSE"
fi

# Show service status
print_status "Service status:"
sudo systemctl status cyberforce --no-pager -l | head -10

print_success "Direct port 3000 setup completed!"
print_status "Application should be available at:"
echo "- http://dev.cyberforce.om:3000"
echo "- http://*************:3000"

# Test if the index.html is being served
print_status "Testing index.html content..."
if curl -s http://localhost:3000/ | grep -q "CyberForce"; then
    print_success "✅ CyberForce content is being served!"
else
    print_status "Content test inconclusive"
fi

EOF

print_header "Direct Setup Summary"
print_success "✅ Simple HTTP server configured on port 3000"
print_success "✅ Nginx configured for redirect"

echo -e "\n${GREEN}🎉 CyberForce is now directly accessible on port 3000!${NC}"
echo -e "${BLUE}Access your application at:${NC}"
echo "- http://dev.cyberforce.om:3000"
echo "- http://*************:3000"

echo -e "\n${BLUE}This setup uses Python's built-in HTTP server which is:${NC}"
echo "- Simple and reliable"
echo "- Perfect for serving static files"
echo "- No complex configuration needed"

print_status "Direct setup completed at $(date)"
