/**
 * Supabase Setup Script
 *
 * This script sets up the Supabase database by creating tables and inserting sample data.
 * It uses the Supabase JavaScript client to interact with the database.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://yselfyopwwtslluyreoo.supabase.co';
const SUPABASE_KEY = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlzZWxmeW9wd3d0c2xsdXlyZW9vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDAxNTU4ODYsImV4cCI6MjA1NTczMTg4Nn0.uw3o_iNlD3Ytm5WI8pO-AyEEfPAwLIwbG1_15oTDpTc';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

/**
 * Create database tables
 */
async function createTables() {
  console.log('Creating database tables...');

  try {
    // Create learning_paths table
    const { error: pathsError } = await supabase
      .from('learning_paths')
      .insert([
        {
          title: 'Network Fundamentals',
          description: 'Learn the basics of computer networking, including protocols, architecture, and troubleshooting.',
          image_url: '/images/learning-paths/network-fundamentals.svg',
          difficulty: 'beginner',
          estimated_hours: 20,
          is_premium: false,
          is_featured: true,
          display_order: 1
        },
        {
          title: 'Operating System Security',
          description: 'Master the security features of Windows and Linux operating systems.',
          image_url: '/images/learning-paths/os-security.svg',
          difficulty: 'intermediate',
          estimated_hours: 25,
          is_premium: true,
          is_featured: true,
          display_order: 2
        },
        {
          title: 'Ethical Hacking',
          description: 'Learn ethical hacking techniques and methodologies based on the CEH curriculum.',
          image_url: '/images/learning-paths/ethical-hacking.svg',
          difficulty: 'advanced',
          estimated_hours: 40,
          is_premium: true,
          is_featured: false,
          display_order: 3
        }
      ]);

    if (pathsError) {
      // If the error is because the table doesn't exist, we need to create it first
      if (pathsError.message.includes('relation "learning_paths" does not exist')) {
        console.log('Creating learning_paths table...');

        // Create the table using REST API
        const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': SUPABASE_KEY,
            'Authorization': `Bearer ${SUPABASE_KEY}`
          },
          body: JSON.stringify({
            name: 'learning_paths',
            schema: 'public',
            columns: [
              {
                name: 'id',
                type: 'uuid',
                primaryKey: true,
                defaultValue: { type: 'function', value: 'uuid_generate_v4()' }
              },
              { name: 'title', type: 'text', nullable: false },
              { name: 'description', type: 'text' },
              { name: 'image_url', type: 'text' },
              { name: 'difficulty', type: 'text', defaultValue: { type: 'string', value: 'beginner' } },
              { name: 'estimated_hours', type: 'integer' },
              { name: 'is_premium', type: 'boolean', defaultValue: { type: 'boolean', value: false } },
              { name: 'is_featured', type: 'boolean', defaultValue: { type: 'boolean', value: false } },
              { name: 'display_order', type: 'integer' },
              { name: 'created_at', type: 'timestamptz', defaultValue: { type: 'function', value: 'now()' } },
              { name: 'updated_at', type: 'timestamptz', defaultValue: { type: 'function', value: 'now()' } }
            ]
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to create learning_paths table: ${await response.text()}`);
        }
      } else {
        console.error('Error creating learning paths:', pathsError.message);
      }
    } else {
      console.log('Successfully created learning paths');
    }

    // Create challenges table
    const { error: challengesError } = await supabase
      .from('challenges')
      .insert([
        {
          title: 'Network Reconnaissance',
          description: 'Use network reconnaissance techniques to map a target network.',
          difficulty: 'beginner',
          points: 100,
          coin_cost: 0,
          is_free: true,
          is_preview: true,
          is_open: true,
          image_url: '/images/challenges/network-recon.svg'
        },
        {
          title: 'Web Application Vulnerabilities',
          description: 'Identify and exploit common web application vulnerabilities.',
          difficulty: 'intermediate',
          points: 200,
          coin_cost: 50,
          is_free: false,
          is_preview: false,
          is_open: true,
          image_url: '/images/challenges/web-app.svg'
        }
      ]);

    if (challengesError) {
      // If the error is because the table doesn't exist, we need to create it first
      if (challengesError.message.includes('relation "challenges" does not exist')) {
        console.log('Creating challenges table...');

        // Create the table using REST API
        const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': SUPABASE_KEY,
            'Authorization': `Bearer ${SUPABASE_KEY}`
          },
          body: JSON.stringify({
            name: 'challenges',
            schema: 'public',
            columns: [
              {
                name: 'id',
                type: 'uuid',
                primaryKey: true,
                defaultValue: { type: 'function', value: 'uuid_generate_v4()' }
              },
              { name: 'title', type: 'text', nullable: false },
              { name: 'description', type: 'text' },
              { name: 'difficulty', type: 'text', defaultValue: { type: 'string', value: 'beginner' } },
              { name: 'points', type: 'integer', defaultValue: { type: 'integer', value: 0 } },
              { name: 'coin_cost', type: 'integer', defaultValue: { type: 'integer', value: 0 } },
              { name: 'is_free', type: 'boolean', defaultValue: { type: 'boolean', value: false } },
              { name: 'is_preview', type: 'boolean', defaultValue: { type: 'boolean', value: false } },
              { name: 'is_open', type: 'boolean', defaultValue: { type: 'boolean', value: true } },
              { name: 'image_url', type: 'text' },
              { name: 'created_at', type: 'timestamptz', defaultValue: { type: 'function', value: 'now()' } },
              { name: 'updated_at', type: 'timestamptz', defaultValue: { type: 'function', value: 'now()' } }
            ]
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to create challenges table: ${await response.text()}`);
        }
      } else {
        console.error('Error creating challenges:', challengesError.message);
      }
    } else {
      console.log('Successfully created challenges');
    }

    console.log('Database tables created successfully');
  } catch (error) {
    console.error('Error creating tables:', error.message);
    throw error;
  }
}

/**
 * Set up the Supabase database
 */
async function setupSupabase() {
  console.log('Setting up Supabase database...');

  try {
    // Create tables and insert sample data
    await createTables();

    console.log('Supabase database setup completed successfully');
    console.log('\nIMPORTANT: For a complete database setup, please copy and paste the contents of scripts/direct-setup.sql into the Supabase SQL Editor and run it.');
  } catch (error) {
    console.error(`Supabase database setup failed: ${error.message}`);
    console.log('\nPlease use the manual setup method instead:');
    console.log('1. Log in to the Supabase dashboard at https://yselfyopwwtslluyreoo.supabase.co/dashboard/');
    console.log('2. Go to the SQL Editor');
    console.log('3. Copy the contents of scripts/direct-setup.sql and paste it into the SQL Editor');
    console.log('4. Click "Run" to execute the SQL script');
    process.exit(1);
  }
}

// Run the setup
setupSupabase();
