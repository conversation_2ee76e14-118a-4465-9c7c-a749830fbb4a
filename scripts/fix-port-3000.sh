#!/bin/bash

# Fix CyberForce to properly run on port 3000
# The serve command needs the correct port parameter

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER_USER="ubuntu"
SERVER_HOST="*************"
SSH_KEY="/Users/<USER>/Downloads/cyf-dev-app-key.pem"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_header "Fix CyberForce Port 3000 Configuration"

# Set correct permissions for SSH key
chmod 400 "$SSH_KEY" 2>/dev/null || true

# Execute fix on server
print_status "Fixing port 3000 configuration..."
ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << 'EOF'
set -e

# Colors for remote output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[REMOTE]${NC} $1"; }
print_success() { echo -e "${GREEN}[REMOTE]${NC} $1"; }

print_status "Fixing CyberForce service to run on port 3000..."

# Stop the current service
print_status "Stopping current service..."
sudo systemctl stop cyberforce

# Update the systemd service with correct port parameter
print_status "Updating systemd service configuration..."
sudo tee /etc/systemd/system/cyberforce.service > /dev/null << 'SERVICEEOF'
[Unit]
Description=CyberForce Application
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/var/www/cyberforce
ExecStart=/usr/bin/serve -s dist -p 3000
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
SERVICEEOF

# Reload systemd and restart the service
print_status "Reloading systemd and starting service..."
sudo systemctl daemon-reload
sudo systemctl start cyberforce

# Wait a moment for the service to start
sleep 3

# Check service status
SERVICE_STATUS=$(sudo systemctl is-active cyberforce)
print_status "Service Status: $SERVICE_STATUS"

if [ "$SERVICE_STATUS" = "active" ]; then
    print_success "Service is running!"
    
    # Test the application
    print_status "Testing application on port 3000..."
    sleep 2
    
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ | grep -q "200"; then
        print_success "Application is responding on port 3000!"
    else
        print_status "Testing with different approach..."
        # Sometimes it takes a moment to start
        sleep 5
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ | grep -q "200"; then
            print_success "Application is now responding on port 3000!"
        else
            print_status "Checking service logs..."
            sudo journalctl -u cyberforce --no-pager -l --since "2 minutes ago"
        fi
    fi
else
    print_status "Service not active, checking logs..."
    sudo journalctl -u cyberforce --no-pager -l --since "2 minutes ago"
fi

# Show current status
print_status "Current service status:"
sudo systemctl status cyberforce --no-pager -l

# Test port accessibility
print_status "Testing port 3000 accessibility..."
if netstat -tlnp | grep :3000; then
    print_success "Port 3000 is listening!"
else
    print_status "Port 3000 not found in netstat, checking with ss..."
    ss -tlnp | grep :3000 || print_status "Port 3000 not yet bound"
fi

print_success "Fix completed!"
print_status "Application should be available at:"
echo "- http://dev.cyberforce.om:3000"
echo "- http://*************:3000"

EOF

print_header "Fix Summary"
print_success "✅ Service configuration updated with correct port parameter"
print_success "✅ Service restarted"

echo -e "\n${GREEN}🔧 Port 3000 configuration fixed!${NC}"
echo -e "${BLUE}Your CyberForce application should now be accessible at:${NC}"
echo "- http://dev.cyberforce.om:3000"
echo "- http://*************:3000"

print_status "Fix completed at $(date)"
