-- Insert sample data into the CyberForce database

-- Sample learning paths
INSERT INTO learning_paths (id, title, description, image_url, difficulty, estimated_hours, is_premium, is_featured, display_order)
VALUES
  (uuid_generate_v4(), 'Network Fundamentals', 'Learn the basics of computer networking, including protocols, architecture, and troubleshooting.', '/images/learning-paths/network-fundamentals.svg', 'beginner', 20, false, true, 1),
  (uuid_generate_v4(), 'Operating System Security', 'Master the security features of Windows and Linux operating systems.', '/images/learning-paths/os-security.svg', 'intermediate', 25, true, true, 2),
  (uuid_generate_v4(), 'Ethical Hacking', 'Learn ethical hacking techniques and methodologies based on the CEH curriculum.', '/images/learning-paths/ethical-hacking.svg', 'advanced', 40, true, false, 3),
  (uuid_generate_v4(), 'Red Team Operations', 'Advanced offensive security techniques for red team operations.', '/images/learning-paths/red-team.svg', 'expert', 50, true, false, 4),
  (uuid_generate_v4(), 'Blue Team Defense', 'Learn defensive security techniques including SIEM, malware analysis, and incident response.', '/images/learning-paths/blue-team.svg', 'advanced', 45, true, true, 5);

-- Get the IDs of the learning paths
DO $$
DECLARE
  network_path_id UUID;
  os_path_id UUID;
  ethical_hacking_path_id UUID;
  red_team_path_id UUID;
  blue_team_path_id UUID;
BEGIN
  SELECT id INTO network_path_id FROM learning_paths WHERE title = 'Network Fundamentals';
  SELECT id INTO os_path_id FROM learning_paths WHERE title = 'Operating System Security';
  SELECT id INTO ethical_hacking_path_id FROM learning_paths WHERE title = 'Ethical Hacking';
  SELECT id INTO red_team_path_id FROM learning_paths WHERE title = 'Red Team Operations';
  SELECT id INTO blue_team_path_id FROM learning_paths WHERE title = 'Blue Team Defense';

  -- Sample learning modules for Network Fundamentals
  INSERT INTO learning_modules (id, path_id, title, description, image_url, difficulty, estimated_minutes, is_free, is_preview, display_order)
  VALUES
    (uuid_generate_v4(), network_path_id, 'Introduction to Networking', 'Learn the basics of computer networking and the OSI model.', '/images/modules/intro-networking.svg', 'beginner', 60, true, true, 1),
    (uuid_generate_v4(), network_path_id, 'TCP/IP Protocol Suite', 'Understand the TCP/IP protocol suite and how it relates to the OSI model.', '/images/modules/tcp-ip.svg', 'beginner', 90, true, false, 2),
    (uuid_generate_v4(), network_path_id, 'Network Devices', 'Learn about routers, switches, firewalls, and other network devices.', '/images/modules/network-devices.svg', 'beginner', 120, false, false, 3),
    (uuid_generate_v4(), network_path_id, 'IP Addressing and Subnetting', 'Master IP addressing, subnetting, and CIDR notation.', '/images/modules/ip-addressing.svg', 'intermediate', 150, false, false, 4),
    (uuid_generate_v4(), network_path_id, 'Routing Fundamentals', 'Learn about routing protocols and how data is routed across networks.', '/images/modules/routing.svg', 'intermediate', 120, false, false, 5);

  -- Sample learning modules for Operating System Security
  INSERT INTO learning_modules (id, path_id, title, description, image_url, difficulty, estimated_minutes, is_free, is_preview, display_order)
  VALUES
    (uuid_generate_v4(), os_path_id, 'Windows Security Fundamentals', 'Learn the basics of Windows security features and configurations.', '/images/modules/windows-security.svg', 'intermediate', 120, true, true, 1),
    (uuid_generate_v4(), os_path_id, 'Linux Security Fundamentals', 'Learn the basics of Linux security features and configurations.', '/images/modules/linux-security.svg', 'intermediate', 120, true, false, 2),
    (uuid_generate_v4(), os_path_id, 'User Account Management', 'Learn how to manage user accounts and permissions securely.', '/images/modules/user-accounts.svg', 'intermediate', 90, false, false, 3),
    (uuid_generate_v4(), os_path_id, 'File System Security', 'Understand file system permissions and access controls.', '/images/modules/file-system.svg', 'intermediate', 90, false, false, 4),
    (uuid_generate_v4(), os_path_id, 'Hardening Operating Systems', 'Learn how to harden Windows and Linux operating systems against attacks.', '/images/modules/hardening.svg', 'advanced', 150, false, false, 5);

  -- Sample challenges
  INSERT INTO challenges (id, title, description, difficulty, points, coin_cost, is_free, is_preview, is_open, image_url)
  VALUES
    (uuid_generate_v4(), 'Network Reconnaissance', 'Use network reconnaissance techniques to map a target network.', 'beginner', 100, 0, true, true, true, '/images/challenges/network-recon.svg'),
    (uuid_generate_v4(), 'Web Application Vulnerabilities', 'Identify and exploit common web application vulnerabilities.', 'intermediate', 200, 50, false, false, true, '/images/challenges/web-app.svg'),
    (uuid_generate_v4(), 'Password Cracking', 'Use password cracking techniques to gain access to a system.', 'intermediate', 200, 50, false, false, true, '/images/challenges/password-crack.svg'),
    (uuid_generate_v4(), 'Network Traffic Analysis', 'Analyze network traffic to identify suspicious activities.', 'advanced', 300, 100, false, false, true, '/images/challenges/traffic-analysis.svg');

  -- Sample challenge categories
  INSERT INTO challenge_categories (id, name, description, icon, color)
  VALUES
    (uuid_generate_v4(), 'Web Security', 'Challenges related to web application security.', 'FaGlobe', '#3498db'),
    (uuid_generate_v4(), 'Network Security', 'Challenges related to network security.', 'FaNetworkWired', '#2ecc71'),
    (uuid_generate_v4(), 'Cryptography', 'Challenges related to cryptography and encryption.', 'FaLock', '#9b59b6'),
    (uuid_generate_v4(), 'Forensics', 'Challenges related to digital forensics.', 'FaSearch', '#e74c3c');

  -- Sample products
  INSERT INTO products (id, name, description, image_url, category, is_digital, is_active)
  VALUES
    (uuid_generate_v4(), 'Premium Subscription', 'Access to all premium learning paths and challenges.', '/images/products/premium.svg', 'subscription', true, true),
    (uuid_generate_v4(), 'Business Subscription', 'Access to all premium content plus team management features.', '/images/products/business.svg', 'subscription', true, true),
    (uuid_generate_v4(), 'Coin Pack - Small', 'Get 500 coins to spend on challenges and other content.', '/images/products/coins-small.svg', 'coins', true, true),
    (uuid_generate_v4(), 'Coin Pack - Medium', 'Get 1000 coins to spend on challenges and other content.', '/images/products/coins-medium.svg', 'coins', true, true),
    (uuid_generate_v4(), 'Coin Pack - Large', 'Get 2000 coins to spend on challenges and other content.', '/images/products/coins-large.svg', 'coins', true, true);

  -- Get the IDs of the products
  DECLARE
    premium_id UUID;
    business_id UUID;
    coins_small_id UUID;
    coins_medium_id UUID;
    coins_large_id UUID;
  BEGIN
    SELECT id INTO premium_id FROM products WHERE name = 'Premium Subscription';
    SELECT id INTO business_id FROM products WHERE name = 'Business Subscription';
    SELECT id INTO coins_small_id FROM products WHERE name = 'Coin Pack - Small';
    SELECT id INTO coins_medium_id FROM products WHERE name = 'Coin Pack - Medium';
    SELECT id INTO coins_large_id FROM products WHERE name = 'Coin Pack - Large';

    -- Sample product variants
    INSERT INTO product_variants (id, product_id, name, description, price, currency, is_active)
    VALUES
      (uuid_generate_v4(), premium_id, 'Monthly', 'Monthly subscription to Premium tier.', 9.99, 'USD', true),
      (uuid_generate_v4(), premium_id, 'Annual', 'Annual subscription to Premium tier (save 20%).', 95.88, 'USD', true),
      (uuid_generate_v4(), business_id, 'Monthly', 'Monthly subscription to Business tier.', 19.99, 'USD', true),
      (uuid_generate_v4(), business_id, 'Annual', 'Annual subscription to Business tier (save 20%).', 191.88, 'USD', true),
      (uuid_generate_v4(), coins_small_id, 'Standard', '500 coins package.', 4.99, 'USD', true),
      (uuid_generate_v4(), coins_medium_id, 'Standard', '1000 coins package.', 9.99, 'USD', true),
      (uuid_generate_v4(), coins_large_id, 'Standard', '2000 coins package.', 19.99, 'USD', true);
  END;
END $$;
