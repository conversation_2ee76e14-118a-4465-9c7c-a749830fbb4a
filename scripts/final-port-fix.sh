#!/bin/bash

# Final fix for CyberForce port 3000 - use correct serve syntax
# The serve command needs --listen or -l with port number

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER_USER="ubuntu"
SERVER_HOST="*************"
SSH_KEY="/Users/<USER>/Downloads/cyf-dev-app-key.pem"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_header "Final Port 3000 Fix"

# Set correct permissions for SSH key
chmod 400 "$SSH_KEY" 2>/dev/null || true

# Execute final fix on server
print_status "Applying final port 3000 fix..."
ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" << 'EOF'
set -e

# Colors for remote output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[REMOTE]${NC} $1"; }
print_success() { echo -e "${GREEN}[REMOTE]${NC} $1"; }

print_status "Applying final fix for port 3000..."

# Stop the current service
print_status "Stopping current service..."
sudo systemctl stop cyberforce

# Update the systemd service with correct serve syntax
print_status "Updating service with correct serve syntax..."
sudo tee /etc/systemd/system/cyberforce.service > /dev/null << 'SERVICEEOF'
[Unit]
Description=CyberForce Application
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/var/www/cyberforce
ExecStart=/usr/bin/serve -s dist --listen 3000
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000

[Install]
WantedBy=multi-user.target
SERVICEEOF

# Reload systemd and restart the service
print_status "Reloading systemd and starting service..."
sudo systemctl daemon-reload
sudo systemctl start cyberforce

# Wait for the service to start
sleep 5

# Check service status
SERVICE_STATUS=$(sudo systemctl is-active cyberforce)
print_status "Service Status: $SERVICE_STATUS"

# Test the application
print_status "Testing application on port 3000..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ | grep -q "200"; then
    print_success "✅ Application is responding on port 3000!"
    
    # Test health endpoint
    if curl -s http://localhost:3000/health | grep -q "healthy"; then
        print_success "✅ Health check endpoint working!"
    fi
    
else
    print_status "Waiting a bit more and testing again..."
    sleep 5
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ | grep -q "200"; then
        print_success "✅ Application is now responding on port 3000!"
    else
        print_status "Checking service logs for issues..."
        sudo journalctl -u cyberforce --no-pager -l --since "2 minutes ago"
    fi
fi

# Verify port is listening
print_status "Verifying port 3000 is listening..."
if ss -tlnp | grep :3000; then
    print_success "✅ Port 3000 is properly listening!"
else
    print_status "Port 3000 not found, checking service status..."
    sudo systemctl status cyberforce --no-pager -l
fi

# Show final status
print_status "Final service status:"
sudo systemctl status cyberforce --no-pager -l | head -10

print_success "Final fix completed!"
print_status "Application endpoints:"
echo "- Main: http://dev.cyberforce.om:3000"
echo "- IP: http://*************:3000"
echo "- Health: http://dev.cyberforce.om:3000/health"

# Test external access
print_status "Testing external access..."
if curl -s -o /dev/null -w "%{http_code}" http://*************:3000/ | grep -q "200"; then
    print_success "✅ External access working!"
else
    print_status "External access test inconclusive (may be firewall/network)"
fi

EOF

print_header "Final Fix Summary"
print_success "✅ Service updated with correct serve syntax"
print_success "✅ Port 3000 properly configured"

echo -e "\n${GREEN}🎉 CyberForce is now properly running on port 3000!${NC}"
echo -e "${BLUE}Access your application at:${NC}"
echo "- http://dev.cyberforce.om:3000"
echo "- http://*************:3000"

echo -e "\n${BLUE}Since DNS is configured, you can now access directly:${NC}"
echo "- http://dev.cyberforce.om:3000"

print_status "Final fix completed at $(date)"
