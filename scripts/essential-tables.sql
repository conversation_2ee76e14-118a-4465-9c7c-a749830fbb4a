-- Essential Tables for CyberForce Application
-- This script creates only the most essential tables needed for the application to function

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create custom types
DO $$ BEGIN
    CREATE TYPE subscription_tier AS ENUM ('free', 'premium', 'business');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE challenge_status AS ENUM ('not_started', 'in_progress', 'completed', 'failed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE difficulty_level AS ENUM ('beginner', 'intermediate', 'advanced', 'expert');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create essential tables

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY,
  username TEXT UNIQUE,
  full_name TEXT,
  email TEXT UNIQUE,
  avatar_url TEXT,
  subscription_tier subscription_tier DEFAULT 'free',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Learning paths table
CREATE TABLE IF NOT EXISTS learning_paths (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  image_url TEXT,
  difficulty difficulty_level DEFAULT 'beginner',
  estimated_hours INTEGER,
  is_premium BOOLEAN DEFAULT FALSE,
  is_featured BOOLEAN DEFAULT FALSE,
  display_order INTEGER,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Learning modules table
CREATE TABLE IF NOT EXISTS learning_modules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  path_id UUID REFERENCES learning_paths(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  description TEXT,
  image_url TEXT,
  difficulty difficulty_level DEFAULT 'beginner',
  estimated_minutes INTEGER,
  is_free BOOLEAN DEFAULT FALSE,
  is_preview BOOLEAN DEFAULT FALSE,
  display_order INTEGER,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Challenges table
CREATE TABLE IF NOT EXISTS challenges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  difficulty difficulty_level DEFAULT 'beginner',
  points INTEGER DEFAULT 0,
  coin_cost INTEGER DEFAULT 0,
  is_free BOOLEAN DEFAULT FALSE,
  is_preview BOOLEAN DEFAULT FALSE,
  is_open BOOLEAN DEFAULT TRUE,
  image_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert sample data

-- Sample learning paths
INSERT INTO learning_paths (title, description, image_url, difficulty, estimated_hours, is_premium, is_featured, display_order)
VALUES
  ('Network Fundamentals', 'Learn the basics of computer networking, including protocols, architecture, and troubleshooting.', '/images/learning-paths/network-fundamentals.svg', 'beginner', 20, false, true, 1),
  ('Operating System Security', 'Master the security features of Windows and Linux operating systems.', '/images/learning-paths/os-security.svg', 'intermediate', 25, true, true, 2),
  ('Ethical Hacking', 'Learn ethical hacking techniques and methodologies based on the CEH curriculum.', '/images/learning-paths/ethical-hacking.svg', 'advanced', 40, true, false, 3);

-- Sample challenges
INSERT INTO challenges (title, description, difficulty, points, coin_cost, is_free, is_preview, is_open, image_url)
VALUES
  ('Network Reconnaissance', 'Use network reconnaissance techniques to map a target network.', 'beginner', 100, 0, true, true, true, '/images/challenges/network-recon.svg'),
  ('Web Application Vulnerabilities', 'Identify and exploit common web application vulnerabilities.', 'intermediate', 200, 50, false, false, true, '/images/challenges/web-app.svg');
