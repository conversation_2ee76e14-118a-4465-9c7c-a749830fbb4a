# Supabase Database Setup Guide

This guide provides step-by-step instructions for setting up the Supabase database for the CyberForce application.

## Method 1: Using the Supabase SQL Editor (Recommended)

The most reliable way to set up the database is to use the Supabase SQL Editor:

1. Log in to the Supabase dashboard at https://yselfyopwwtslluyreoo.supabase.co/dashboard/
2. Go to the SQL Editor (click on "SQL Editor" in the left sidebar)
3. Click on "New query" to create a new SQL query
4. Open the file `scripts/direct-setup.sql` in your code editor
5. Copy the **entire contents** of the file (but not the filename itself)
6. Paste the SQL code into the Supabase SQL Editor
7. Click "Run" to execute the SQL script

This will create all the necessary tables, indexes, and relationships in the database, and insert some sample data.

## Method 2: Using the Setup Script

Alternatively, you can use the setup script to set up the database:

```bash
# Using npm
npm run setup-db

# Using yarn
yarn setup-db
```

This script will:
1. Create basic tables in the database
2. Insert sample data into the tables

**Note**: This method may not create all the tables and relationships needed for the application to function properly. It's recommended to use Method 1 for a complete setup.

## Verifying the Setup

To verify that the database has been set up correctly:

1. Go to the Supabase dashboard
2. Navigate to the Table Editor (click on "Table Editor" in the left sidebar)
3. You should see tables like `learning_paths`, `challenges`, etc. in the sidebar
4. Click on a table to view its contents and verify that sample data has been inserted

## Troubleshooting

If you encounter any issues during the setup process:

### SQL Syntax Error

If you see an error like `syntax error at or near "scripts"`, it means you've pasted the filename instead of the file contents. Make sure to copy only the contents of the SQL file, not the filename.

### Table Already Exists

If you see an error like `relation "learning_paths" already exists`, it means the table has already been created. You can either:
- Drop the existing tables first (be careful, this will delete all data)
- Ignore the error and continue with the setup

### Connection Issues

If you're having trouble connecting to the Supabase database:
1. Check that the Supabase URL and API key in the `.env` file are correct
2. Make sure you have internet connectivity
3. Try accessing the Supabase dashboard in your browser to verify that the service is up

## Manual Table Creation

If you're still having issues, you can create the tables manually through the Supabase dashboard:

1. Go to the Table Editor
2. Click "Create a new table"
3. Enter the table name (e.g., `learning_paths`)
4. Add columns with appropriate types (refer to the SQL script for column definitions)
5. Click "Save" to create the table
6. Repeat for other tables

## Support

If you continue to experience issues with the database setup, please contact the development team for assistance.
