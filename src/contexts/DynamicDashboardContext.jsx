import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { dynamicAuth } from '../lib/dynamicAuth';

const DynamicDashboardContext = createContext();

const initialState = {
  user: null,
  profile: null,
  stats: {
    level: 'Beginner',
    points: 0,
    learningModules: 0,
    dailyChallenges: 0,
    experiencePoints: 0
  },
  enrollments: [],
  progress: [],
  achievements: [],
  challenges: [],
  loading: true,
  error: null,
  isAuthenticated: false
};

function dashboardReducer(state, action) {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_USER':
      return { ...state, user: action.payload, isAuthenticated: !!action.payload };
    case 'SET_PROFILE':
      return { ...state, profile: action.payload };
    case 'SET_STATS':
      return { ...state, stats: { ...state.stats, ...action.payload } };
    case 'SET_ENROLLMENTS':
      return { ...state, enrollments: action.payload };
    case 'SET_PROGRESS':
      return { ...state, progress: action.payload };
    case 'SET_ACHIEVEMENTS':
      return { ...state, achievements: action.payload };
    case 'SET_CHALLENGES':
      return { ...state, challenges: action.payload };
    case 'UPDATE_PROGRESS':
      return {
        ...state,
        progress: state.progress.map(p =>
          p.learning_path === action.payload.learning_path && p.module_id === action.payload.module_id
            ? { ...p, ...action.payload }
            : p
        )
      };
    case 'ADD_ACHIEVEMENT':
      return {
        ...state,
        achievements: [action.payload, ...state.achievements]
      };
    case 'RESET':
      return { ...initialState, loading: false };
    default:
      return state;
  }
}

export const DynamicDashboardProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Calculate stats from real data
  const calculateStats = (progress, achievements, enrollments) => {
    const completedModules = progress.filter(p => p.completed).length;
    const totalPoints = achievements.reduce((sum, achievement) => {
      return sum + (achievement.achievement_data?.points || 0);
    }, 0);
    
    const experiencePoints = progress.reduce((sum, p) => {
      return sum + (p.progress_percentage || 0);
    }, 0);

    // Determine level based on completed modules
    let level = 'Beginner';
    if (completedModules >= 50) level = 'Expert';
    else if (completedModules >= 25) level = 'Advanced';
    else if (completedModules >= 10) level = 'Intermediate';

    return {
      level,
      points: totalPoints,
      learningModules: completedModules,
      dailyChallenges: achievements.filter(a => a.achievement_type === 'daily_challenge').length,
      experiencePoints: Math.floor(experiencePoints / 10) // Convert to reasonable scale
    };
  };

  // Load user data
  const loadUserData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const { user, profile } = await dynamicAuth.getCurrentUser();
      
      if (!user) {
        dispatch({ type: 'RESET' });
        return;
      }

      dispatch({ type: 'SET_USER', payload: user });
      dispatch({ type: 'SET_PROFILE', payload: profile });

      // Load user progress, enrollments, and achievements
      const [progress, enrollments, achievements] = await Promise.all([
        dynamicAuth.getUserProgress(user.id),
        dynamicAuth.getUserEnrollments(user.id),
        dynamicAuth.getUserAchievements(user.id)
      ]);

      dispatch({ type: 'SET_PROGRESS', payload: progress });
      dispatch({ type: 'SET_ENROLLMENTS', payload: enrollments });
      dispatch({ type: 'SET_ACHIEVEMENTS', payload: achievements });

      // Calculate and set stats
      const stats = calculateStats(progress, achievements, enrollments);
      dispatch({ type: 'SET_STATS', payload: stats });

      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      console.error('Error loading user data:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  // Enroll in learning path
  const enrollInPath = async (learningPath) => {
    if (!state.user) return;

    try {
      await dynamicAuth.enrollInLearningPath(state.user.id, learningPath);
      
      // Reload enrollments
      const enrollments = await dynamicAuth.getUserEnrollments(state.user.id);
      dispatch({ type: 'SET_ENROLLMENTS', payload: enrollments });
    } catch (error) {
      console.error('Error enrolling in learning path:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  // Update module progress
  const updateProgress = async (learningPath, moduleId, progressData) => {
    if (!state.user) return;

    try {
      const updatedProgress = await dynamicAuth.updateModuleProgress(
        state.user.id,
        learningPath,
        moduleId,
        progressData
      );

      dispatch({ type: 'UPDATE_PROGRESS', payload: updatedProgress });

      // If module completed, add achievement
      if (progressData.completed) {
        const achievement = await dynamicAuth.addAchievement(
          state.user.id,
          'module_completed',
          {
            learning_path: learningPath,
            module_id: moduleId,
            points: 10
          }
        );
        dispatch({ type: 'ADD_ACHIEVEMENT', payload: achievement });
      }

      // Recalculate stats
      const progress = await dynamicAuth.getUserProgress(state.user.id);
      const achievements = await dynamicAuth.getUserAchievements(state.user.id);
      const stats = calculateStats(progress, achievements, state.enrollments);
      dispatch({ type: 'SET_STATS', payload: stats });
    } catch (error) {
      console.error('Error updating progress:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  // Add achievement
  const addAchievement = async (achievementType, achievementData = {}) => {
    if (!state.user) return;

    try {
      const achievement = await dynamicAuth.addAchievement(
        state.user.id,
        achievementType,
        achievementData
      );
      dispatch({ type: 'ADD_ACHIEVEMENT', payload: achievement });

      // Recalculate stats
      const achievements = await dynamicAuth.getUserAchievements(state.user.id);
      const stats = calculateStats(state.progress, achievements, state.enrollments);
      dispatch({ type: 'SET_STATS', payload: stats });
    } catch (error) {
      console.error('Error adding achievement:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  // Update profile
  const updateProfile = async (updates) => {
    if (!state.user) return;

    try {
      const updatedProfile = await dynamicAuth.updateUserProfile(state.user.id, updates);
      dispatch({ type: 'SET_PROFILE', payload: updatedProfile });
    } catch (error) {
      console.error('Error updating profile:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  };

  // Load data on mount and when user changes
  useEffect(() => {
    loadUserData();
  }, []);

  const value = {
    ...state,
    loadUserData,
    enrollInPath,
    updateProgress,
    addAchievement,
    updateProfile,
    refreshData: loadUserData
  };

  return (
    <DynamicDashboardContext.Provider value={value}>
      {children}
    </DynamicDashboardContext.Provider>
  );
};

export const useDynamicDashboard = () => {
  const context = useContext(DynamicDashboardContext);
  if (!context) {
    throw new Error('useDynamicDashboard must be used within a DynamicDashboardProvider');
  }
  return context;
};

export default DynamicDashboardContext;
