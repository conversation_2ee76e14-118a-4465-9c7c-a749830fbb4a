-- CyberForce Progress Tracking Database Schema
-- This file contains all the tables needed for user progress tracking

-- User Profiles Table (Enhanced)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE,
    full_name VARCHA<PERSON>(100),
    avatar_url TEXT,
    bio TEXT,
    total_coins INTEGER DEFAULT 0,
    total_points INTEGER DEFAULT 0,
    modules_completed INTEGER DEFAULT 0,
    challenges_completed INTEGER DEFAULT 0,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    subscription_tier VARCHAR(20) DEFAULT 'free', -- 'free', 'premium', 'business'
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Paths Table
CREATE TABLE IF NOT EXISTS learning_paths (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    difficulty_level VARCHAR(20) DEFAULT 'beginner', -- 'beginner', 'intermediate', 'advanced'
    estimated_duration INTEGER, -- in hours
    total_modules INTEGER DEFAULT 0,
    is_premium BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    icon_name VARCHAR(50),
    color_scheme VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning Modules Table
CREATE TABLE IF NOT EXISTS learning_modules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    learning_path_id UUID REFERENCES learning_paths(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content JSONB, -- Structured content data
    difficulty_level VARCHAR(20) DEFAULT 'beginner',
    estimated_duration INTEGER, -- in minutes
    points_reward INTEGER DEFAULT 50,
    is_premium BOOLEAN DEFAULT false,
    is_free BOOLEAN DEFAULT true,
    is_preview BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    prerequisites JSONB, -- Array of module IDs that must be completed first
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Module Sections Table
CREATE TABLE IF NOT EXISTS module_sections (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    content JSONB, -- Section content
    section_type VARCHAR(50) DEFAULT 'lesson', -- 'lesson', 'quiz', 'lab', 'assessment'
    display_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT true,
    points_reward INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Module Progress Table
CREATE TABLE IF NOT EXISTS user_module_progress (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    module_id UUID REFERENCES learning_modules(id) ON DELETE CASCADE,
    current_section_id UUID REFERENCES module_sections(id),
    progress_percentage INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT false,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    time_spent INTEGER DEFAULT 0, -- in minutes
    UNIQUE(user_id, module_id)
);

-- Challenges Table
CREATE TABLE IF NOT EXISTS challenges (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content JSONB, -- Challenge content and instructions
    difficulty_level VARCHAR(20) DEFAULT 'beginner',
    category VARCHAR(100),
    points_reward INTEGER DEFAULT 100,
    time_limit INTEGER, -- in minutes
    is_premium BOOLEAN DEFAULT false,
    is_free BOOLEAN DEFAULT true,
    is_preview BOOLEAN DEFAULT false,
    is_daily BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Challenge Progress Table
CREATE TABLE IF NOT EXISTS user_challenge_progress (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    challenge_id UUID REFERENCES challenges(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'started', -- 'started', 'in_progress', 'completed', 'failed'
    points_earned INTEGER DEFAULT 0,
    attempts INTEGER DEFAULT 1,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_attempt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    time_spent INTEGER DEFAULT 0, -- in minutes
    UNIQUE(user_id, challenge_id)
);

-- Daily Challenges Table
CREATE TABLE IF NOT EXISTS user_daily_challenges (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    challenge_date DATE NOT NULL,
    challenge_type VARCHAR(50) DEFAULT 'general', -- 'general', 'network', 'security', etc.
    completed BOOLEAN DEFAULT false,
    points_earned INTEGER DEFAULT 0,
    completed_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, challenge_date)
);

-- User Achievements Table
CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_type VARCHAR(100) NOT NULL,
    achievement_name VARCHAR(200) NOT NULL,
    description TEXT,
    points_earned INTEGER DEFAULT 0,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB -- Additional achievement data
);

-- Certificates Table
CREATE TABLE IF NOT EXISTS certificates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    learning_path_id UUID REFERENCES learning_paths(id),
    certificate_name VARCHAR(200) NOT NULL,
    certificate_type VARCHAR(50) DEFAULT 'completion', -- 'completion', 'mastery', 'specialization'
    issued_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    certificate_url TEXT,
    verification_code VARCHAR(100) UNIQUE,
    is_verified BOOLEAN DEFAULT true
);

-- User Enrollments Table
CREATE TABLE IF NOT EXISTS user_enrollments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    learning_path_id UUID REFERENCES learning_paths(id) ON DELETE CASCADE,
    enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    progress_percentage INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, learning_path_id)
);

-- AI Chat History Table
CREATE TABLE IF NOT EXISTS chat_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL, -- 'user', 'assistant', 'system'
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text', -- 'text', 'welcome', 'error', 'system'
    metadata JSONB, -- Additional message data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Chat Sessions Table
CREATE TABLE IF NOT EXISTS chat_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200),
    context JSONB, -- Session context and preferences
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    message_count INTEGER DEFAULT 0
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_module_progress_user_id ON user_module_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_module_progress_module_id ON user_module_progress(module_id);
CREATE INDEX IF NOT EXISTS idx_user_challenge_progress_user_id ON user_challenge_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_daily_challenges_user_id ON user_daily_challenges(user_id);
CREATE INDEX IF NOT EXISTS idx_user_daily_challenges_date ON user_daily_challenges(challenge_date);
CREATE INDEX IF NOT EXISTS idx_learning_modules_path_id ON learning_modules(learning_path_id);
CREATE INDEX IF NOT EXISTS idx_module_sections_module_id ON module_sections(module_id);
CREATE INDEX IF NOT EXISTS idx_user_enrollments_user_id ON user_enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_history_user_id ON chat_history(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_history_session_id ON chat_history(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id);

-- Row Level Security (RLS) Policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_module_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_challenge_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_daily_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_enrollments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_profiles
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for user_module_progress
CREATE POLICY "Users can view own module progress" ON user_module_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own module progress" ON user_module_progress FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own module progress" ON user_module_progress FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for user_challenge_progress
CREATE POLICY "Users can view own challenge progress" ON user_challenge_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own challenge progress" ON user_challenge_progress FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own challenge progress" ON user_challenge_progress FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for user_daily_challenges
CREATE POLICY "Users can view own daily challenges" ON user_daily_challenges FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own daily challenges" ON user_daily_challenges FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own daily challenges" ON user_daily_challenges FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for user_achievements
CREATE POLICY "Users can view own achievements" ON user_achievements FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own achievements" ON user_achievements FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for certificates
CREATE POLICY "Users can view own certificates" ON certificates FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own certificates" ON certificates FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for user_enrollments
CREATE POLICY "Users can view own enrollments" ON user_enrollments FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own enrollments" ON user_enrollments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own enrollments" ON user_enrollments FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Enable RLS for chat tables
ALTER TABLE chat_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for chat_history
CREATE POLICY "Users can view own chat history" ON chat_history FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own chat messages" ON chat_history FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own chat messages" ON chat_history FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for chat_sessions
CREATE POLICY "Users can view own chat sessions" ON chat_sessions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own chat sessions" ON chat_sessions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own chat sessions" ON chat_sessions FOR UPDATE USING (auth.uid() = user_id);

-- Public read access for learning content
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE module_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE challenges ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public can view learning paths" ON learning_paths FOR SELECT USING (true);
CREATE POLICY "Public can view learning modules" ON learning_modules FOR SELECT USING (true);
CREATE POLICY "Public can view module sections" ON module_sections FOR SELECT USING (true);
CREATE POLICY "Public can view challenges" ON challenges FOR SELECT USING (true);
