// Content Loader Service - Loads content exactly as structured in JS files
class ContentLoaderService {
  
  // Load Network Fundamentals content with full structure
  static async loadNetworkFundamentals() {
    try {
      const { networkFundamentalsModules, networkFundamentalsPath } = await import('../data/content/network-fundamentals/index.js');
      
      if (!networkFundamentalsModules || Object.keys(networkFundamentalsModules).length === 0) {
        throw new Error('No Network Fundamentals modules found');
      }

      console.log(`📚 ContentLoader: Found ${Object.keys(networkFundamentalsModules).length} Network Fundamentals modules`);

      // Create learning path object
      const learningPath = {
        id: 'networking-fundamentals',
        title: networkFundamentalsPath.title,
        description: networkFundamentalsPath.description,
        category: networkFundamentalsPath.category,
        difficulty: networkFundamentalsPath.difficulty,
        estimated_hours: networkFundamentalsPath.estimatedHours,
        prerequisites: networkFundamentalsPath.prerequisites || [],
        skills: ['Networking', 'TCP/IP', 'Network Security', 'Network Protocols'],
        modules_count: Object.keys(networkFundamentalsModules).length,
        is_active: true,
        is_featured: true
      };

      // Convert modules from object to array with full content structure
      const modules = Object.entries(networkFundamentalsModules).map(([moduleId, moduleContent], index) => {
        const displayId = moduleId.startsWith('nf-') ? `module-${moduleId.replace('nf-', '')}` : moduleId;

        return {
          id: displayId,
          sourceId: moduleId,
          learning_path_id: 'networking-fundamentals',
          title: moduleContent.title,
          description: moduleContent.description,
          difficulty: moduleContent.difficulty || 'Beginner',
          estimatedTime: moduleContent.estimatedTime || 60,
          objectives: moduleContent.objectives || [],
          sections: moduleContent.sections || [],
          prerequisites: moduleContent.prerequisites || [],
          xpReward: moduleContent.xpReward || 100,
          order_index: parseInt(moduleId.replace('nf-', '')) || index + 1,
          is_active: true,
          // Full content structure preserved
          content: moduleContent
        };
      }).sort((a, b) => a.order_index - b.order_index);

      return { learningPath, modules };
    } catch (error) {
      console.error('Error loading Network Fundamentals content:', error);
      throw error;
    }
  }

  // Load Operating Systems content with full structure
  static async loadOperatingSystems() {
    try {
      const { osLearningPath } = await import('../data/content/os/index.js');

      console.log(`📚 ContentLoader: Found ${osLearningPath.modules.length} OS modules`);

      const learningPath = {
        id: 'operating-systems',
        title: 'Operating Systems for Cybersecurity',
        description: 'Master the essential concepts of Linux and Windows operating systems from a cybersecurity perspective.',
        category: 'fundamentals',
        difficulty: 'beginner',
        estimated_hours: 50,
        modules_count: osLearningPath.modules.length,
        is_active: true,
        is_featured: true,
        prerequisites: [],
        skills: ['Linux', 'Windows', 'Command Line', 'System Administration']
      };

      const modules = osLearningPath.modules.map((module, index) => ({
        ...module,
        learning_path_id: 'operating-systems',
        order_index: index + 1,
        is_active: true,
        // Preserve full content structure
        content: module
      }));

      return { learningPath, modules };
    } catch (error) {
      console.error('Error loading Operating Systems content:', error);
      throw error;
    }
  }

  // Load Blue Teaming content with full structure
  static async loadBlueTeaming() {
    try {
      const blueTeamingContent = await import('../data/content/blue-teaming/index.js');

      let blueTeamingModules = [];
      if (blueTeamingContent.default && Array.isArray(blueTeamingContent.default)) {
        blueTeamingModules = blueTeamingContent.default;
      } else if (blueTeamingContent.blueTeamingModules && Array.isArray(blueTeamingContent.blueTeamingModules)) {
        blueTeamingModules = blueTeamingContent.blueTeamingModules;
      } else {
        const exportedArrays = Object.values(blueTeamingContent).filter(val => Array.isArray(val));
        if (exportedArrays.length > 0) {
          blueTeamingModules = exportedArrays[0];
        }
      }

      console.log(`📚 ContentLoader: Found ${blueTeamingModules.length} Blue Teaming modules`);

      const learningPath = {
        id: 'blue-teaming',
        title: 'Blue Teaming',
        description: 'Master defensive security operations, incident response, and threat hunting.',
        category: 'defensive',
        difficulty: 'intermediate',
        estimated_hours: Math.ceil(blueTeamingModules.length * 1.5),
        prerequisites: ['networking-fundamentals'],
        skills: ['Blue Teaming', 'Incident Response', 'Threat Hunting', 'Security Operations'],
        modules_count: blueTeamingModules.length,
        is_active: true,
        is_featured: true
      };

      const modules = blueTeamingModules.map((module, index) => ({
        ...module,
        learning_path_id: 'blue-teaming',
        order_index: index + 1,
        is_active: true,
        // Preserve full content structure
        content: module
      }));

      return { learningPath, modules };
    } catch (error) {
      console.error('Error loading Blue Teaming content:', error);
      throw error;
    }
  }

  // Load Red Teaming content with full structure
  static async loadRedTeaming() {
    try {
      const redTeamingContent = await import('../data/content/red-teaming/index.js');

      let redTeamingModules = [];
      if (redTeamingContent.default && Array.isArray(redTeamingContent.default)) {
        redTeamingModules = redTeamingContent.default;
      } else if (redTeamingContent.redTeamingModules && Array.isArray(redTeamingContent.redTeamingModules)) {
        redTeamingModules = redTeamingContent.redTeamingModules;
      } else {
        const exportedArrays = Object.values(redTeamingContent).filter(val => Array.isArray(val));
        if (exportedArrays.length > 0) {
          redTeamingModules = exportedArrays[0];
        }
      }

      console.log(`📚 ContentLoader: Found ${redTeamingModules.length} Red Teaming modules`);

      const learningPath = {
        id: 'red-teaming',
        title: 'Red Teaming',
        description: 'Master advanced offensive security techniques and red team operations.',
        category: 'offensive',
        difficulty: 'advanced',
        estimated_hours: Math.ceil(redTeamingModules.length * 2),
        prerequisites: ['ethical-hacking'],
        skills: ['Red Teaming', 'Advanced Penetration Testing', 'Social Engineering', 'Post-Exploitation'],
        modules_count: redTeamingModules.length,
        is_active: true,
        is_featured: true
      };

      const modules = redTeamingModules.map((module, index) => ({
        ...module,
        learning_path_id: 'red-teaming',
        order_index: index + 1,
        is_active: true,
        // Preserve full content structure
        content: module
      }));

      return { learningPath, modules };
    } catch (error) {
      console.error('Error loading Red Teaming content:', error);
      throw error;
    }
  }

  // Load Ethical Hacking content with full structure
  static async loadEthicalHacking() {
    try {
      const { getAllEthicalHackingModules } = await import('../data/content/ethical-hacking/index.js');
      const ethicalHackingModules = getAllEthicalHackingModules();

      console.log(`📚 ContentLoader: Found ${ethicalHackingModules.length} Ethical Hacking modules`);

      const learningPath = {
        id: 'ethical-hacking',
        title: 'Ethical Hacking Fundamentals',
        description: 'Master ethical hacking and penetration testing from fundamental concepts to advanced attack techniques, designed for cybersecurity professionals pursuing CEH, OSCP, and professional penetration testing careers.',
        category: 'offensive',
        difficulty: 'intermediate',
        estimated_hours: 120,
        modules_count: ethicalHackingModules.length,
        is_active: true,
        is_featured: true,
        prerequisites: ['networking-fundamentals'],
        skills: ['Ethical Hacking', 'Penetration Testing', 'Vulnerability Assessment', 'Security Testing']
      };

      const modules = ethicalHackingModules.map((module, index) => ({
        ...module,
        learning_path_id: 'ethical-hacking',
        order_index: index + 1,
        is_active: true,
        // Preserve full content structure
        content: module
      }));

      return { learningPath, modules };
    } catch (error) {
      console.error('Error loading Ethical Hacking content:', error);
      throw error;
    }
  }

  // Main loader function that routes to appropriate content loader
  static async loadLearningPathContent(pathId) {
    switch (pathId) {
      case 'networking-fundamentals':
        return await this.loadNetworkFundamentals();
      case 'operating-systems':
      case 'os-fundamentals':
        return await this.loadOperatingSystems();
      case 'blue-teaming':
        return await this.loadBlueTeaming();
      case 'red-teaming':
        return await this.loadRedTeaming();
      case 'ethical-hacking':
        return await this.loadEthicalHacking();
      default:
        throw new Error(`No content loader available for path: ${pathId}`);
    }
  }

  // Get module content by ID (preserves full structure)
  static async getModuleContent(pathId, moduleId) {
    try {
      const { modules } = await this.loadLearningPathContent(pathId);
      const module = modules.find(m => m.id === moduleId || m.sourceId === moduleId);
      
      if (!module) {
        throw new Error(`Module ${moduleId} not found in path ${pathId}`);
      }

      return module;
    } catch (error) {
      console.error('Error loading module content:', error);
      throw error;
    }
  }
}

export default ContentLoaderService;
