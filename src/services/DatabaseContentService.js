/**
 * Database Content Service
 * 
 * Handles loading learning content from the database with fallback to local files
 * Provides comprehensive content management for the learning platform
 */

import { supabase } from '../config/supabaseClient';
import ContentLoaderService from './ContentLoaderService';

class DatabaseContentService {
  
  /**
   * Get all learning paths with statistics
   */
  static async getLearningPaths() {
    try {
      console.log('🔍 DatabaseContentService: Fetching learning paths from database...');
      
      const { data: paths, error } = await supabase
        .from('learning_paths')
        .select(`
          id,
          title,
          description,
          category,
          difficulty,
          estimated_hours,
          prerequisites,
          skills,
          modules_count,
          is_active,
          is_featured,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Database error:', error);
        throw error;
      }

      if (!paths || paths.length === 0) {
        console.log('📁 No paths in database, falling back to local content...');
        return await this.getFallbackLearningPaths();
      }

      console.log(`✅ DatabaseContentService: Found ${paths.length} learning paths in database`);
      return paths;

    } catch (error) {
      console.error('Error fetching learning paths from database:', error);
      console.log('📁 Falling back to local content...');
      return await this.getFallbackLearningPaths();
    }
  }

  /**
   * Get learning path by ID with modules
   */
  static async getLearningPath(pathId) {
    try {
      console.log(`🔍 DatabaseContentService: Fetching learning path ${pathId} from database...`);
      
      // Get learning path
      const { data: path, error: pathError } = await supabase
        .from('learning_paths')
        .select('*')
        .eq('id', pathId)
        .eq('is_active', true)
        .single();

      if (pathError || !path) {
        console.log(`📁 Path ${pathId} not found in database, falling back to local content...`);
        return await ContentLoaderService.loadLearningPathContent(pathId);
      }

      // Get modules for this path
      const { data: modules, error: modulesError } = await supabase
        .from('learning_modules')
        .select('*')
        .eq('learning_path_id', pathId)
        .eq('is_active', true)
        .order('order_index', { ascending: true });

      if (modulesError) {
        console.error('Error fetching modules:', modulesError);
        throw modulesError;
      }

      console.log(`✅ DatabaseContentService: Found path with ${modules?.length || 0} modules`);
      
      return {
        learningPath: path,
        modules: modules || []
      };

    } catch (error) {
      console.error(`Error fetching learning path ${pathId} from database:`, error);
      console.log('📁 Falling back to local content...');
      return await ContentLoaderService.loadLearningPathContent(pathId);
    }
  }

  /**
   * Get module content with sections
   */
  static async getModuleContent(pathId, moduleId) {
    try {
      console.log(`🔍 DatabaseContentService: Fetching module ${moduleId} from database...`);
      
      // Get module
      const { data: module, error: moduleError } = await supabase
        .from('learning_modules')
        .select('*')
        .eq('id', moduleId)
        .eq('learning_path_id', pathId)
        .eq('is_active', true)
        .single();

      if (moduleError || !module) {
        console.log(`📁 Module ${moduleId} not found in database, falling back to local content...`);
        return await ContentLoaderService.getModuleContent(pathId, moduleId);
      }

      // Get sections for this module
      const { data: sections, error: sectionsError } = await supabase
        .from('module_sections')
        .select('*')
        .eq('module_id', moduleId)
        .eq('is_active', true)
        .order('order_index', { ascending: true });

      if (sectionsError) {
        console.error('Error fetching sections:', sectionsError);
        throw sectionsError;
      }

      // Transform sections to match expected format
      const transformedSections = sections?.map(section => ({
        title: section.title,
        content: section.content_type === 'interactive' || section.content_type === 'quiz' 
          ? section.interactive_data 
          : section.content,
        type: section.content_type,
        estimatedTime: section.estimated_time,
        id: section.id
      })) || [];

      const moduleWithSections = {
        ...module,
        sections: transformedSections,
        estimatedTime: module.estimated_time,
        xpReward: module.xp_reward
      };

      console.log(`✅ DatabaseContentService: Found module with ${transformedSections.length} sections`);
      return moduleWithSections;

    } catch (error) {
      console.error(`Error fetching module ${moduleId} from database:`, error);
      console.log('📁 Falling back to local content...');
      return await ContentLoaderService.getModuleContent(pathId, moduleId);
    }
  }

  /**
   * Save user progress for a section
   */
  static async saveProgress(moduleId, sectionId, progressData) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('module_progress')
        .upsert({
          user_id: user.id,
          module_id: moduleId,
          section_id: sectionId,
          is_completed: progressData.isCompleted || false,
          completion_date: progressData.isCompleted ? new Date().toISOString() : null,
          time_spent: progressData.timeSpent || 0,
          quiz_score: progressData.quizScore || null,
          notes: progressData.notes || null
        }, {
          onConflict: 'user_id,section_id'
        });

      if (error) {
        console.error('Error saving progress:', error);
        throw error;
      }

      console.log('✅ Progress saved successfully');
      return data;

    } catch (error) {
      console.error('Error saving progress:', error);
      throw error;
    }
  }

  /**
   * Get user progress for a module
   */
  static async getModuleProgress(moduleId) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return [];
      }

      const { data, error } = await supabase
        .from('module_progress')
        .select('*')
        .eq('user_id', user.id)
        .eq('module_id', moduleId);

      if (error) {
        console.error('Error fetching progress:', error);
        return [];
      }

      return data || [];

    } catch (error) {
      console.error('Error fetching progress:', error);
      return [];
    }
  }

  /**
   * Fallback to local content when database is not available
   */
  static async getFallbackLearningPaths() {
    try {
      const paths = [];
      
      // Load each learning path from local files
      const pathIds = ['networking-fundamentals', 'operating-systems', 'ethical-hacking', 'blue-teaming', 'red-teaming'];
      
      for (const pathId of pathIds) {
        try {
          const { learningPath } = await ContentLoaderService.loadLearningPathContent(pathId);
          if (learningPath) {
            paths.push(learningPath);
          }
        } catch (error) {
          console.warn(`Could not load ${pathId}:`, error.message);
        }
      }

      console.log(`📁 Loaded ${paths.length} learning paths from local files`);
      return paths;

    } catch (error) {
      console.error('Error loading fallback content:', error);
      return [];
    }
  }

  /**
   * Migrate local content to database
   */
  static async migrateContentToDatabase() {
    try {
      console.log('🔄 Starting content migration to database...');
      
      const pathIds = ['networking-fundamentals'];
      
      for (const pathId of pathIds) {
        console.log(`📦 Migrating ${pathId}...`);
        
        const { learningPath, modules } = await ContentLoaderService.loadLearningPathContent(pathId);
        
        // Insert learning path
        const { error: pathError } = await supabase
          .from('learning_paths')
          .upsert({
            id: learningPath.id,
            title: learningPath.title,
            description: learningPath.description,
            category: learningPath.category,
            difficulty: learningPath.difficulty,
            estimated_hours: learningPath.estimated_hours,
            prerequisites: learningPath.prerequisites || [],
            skills: learningPath.skills || [],
            modules_count: modules.length,
            is_active: true,
            is_featured: learningPath.is_featured || false
          });

        if (pathError) {
          console.error(`Error inserting path ${pathId}:`, pathError);
          continue;
        }

        // Insert modules and sections
        for (const module of modules) {
          const { error: moduleError } = await supabase
            .from('learning_modules')
            .upsert({
              id: module.id,
              source_id: module.sourceId,
              learning_path_id: pathId,
              title: module.title,
              description: module.description,
              difficulty: module.difficulty,
              estimated_time: module.estimatedTime,
              objectives: module.objectives || [],
              prerequisites: module.prerequisites || [],
              xp_reward: module.xpReward,
              order_index: module.order_index,
              is_active: true
            });

          if (moduleError) {
            console.error(`Error inserting module ${module.id}:`, moduleError);
            continue;
          }

          // Insert sections
          if (module.sections) {
            for (let i = 0; i < module.sections.length; i++) {
              const section = module.sections[i];
              
              const { error: sectionError } = await supabase
                .from('module_sections')
                .insert({
                  module_id: module.id,
                  title: section.title,
                  content: typeof section.content === 'string' ? section.content : null,
                  content_type: section.type || 'text',
                  interactive_data: typeof section.content === 'object' ? section.content : null,
                  order_index: i + 1,
                  estimated_time: section.estimatedTime || 15,
                  is_active: true
                });

              if (sectionError) {
                console.error(`Error inserting section for module ${module.id}:`, sectionError);
              }
            }
          }
        }
        
        console.log(`✅ Successfully migrated ${pathId}`);
      }
      
      console.log('🎉 Content migration completed successfully!');
      return true;

    } catch (error) {
      console.error('Error during content migration:', error);
      throw error;
    }
  }
}

export default DatabaseContentService;
