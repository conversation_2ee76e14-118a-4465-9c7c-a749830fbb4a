import { supabase } from '../lib/supabase';
import toast from 'react-hot-toast';

/**
 * Session Management Service
 * 
 * Handles user sessions, device tracking, and cookie management
 */
class SessionManagementService {
  constructor() {
    this.currentSession = null;
    this.deviceInfo = this.getDeviceInfo();
  }

  /**
   * Get device information
   */
  getDeviceInfo() {
    const userAgent = navigator.userAgent;
    const platform = navigator.platform;
    
    // Detect device type
    let deviceType = 'desktop';
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      deviceType = /iPad/.test(userAgent) ? 'tablet' : 'mobile';
    }

    // Detect browser
    let browserName = 'Unknown';
    let browserVersion = 'Unknown';
    
    if (userAgent.includes('Chrome')) {
      browserName = 'Chrome';
      browserVersion = userAgent.match(/Chrome\/([0-9.]+)/)?.[1] || 'Unknown';
    } else if (userAgent.includes('Firefox')) {
      browserName = 'Firefox';
      browserVersion = userAgent.match(/Firefox\/([0-9.]+)/)?.[1] || 'Unknown';
    } else if (userAgent.includes('Safari')) {
      browserName = 'Safari';
      browserVersion = userAgent.match(/Version\/([0-9.]+)/)?.[1] || 'Unknown';
    } else if (userAgent.includes('Edge')) {
      browserName = 'Edge';
      browserVersion = userAgent.match(/Edge\/([0-9.]+)/)?.[1] || 'Unknown';
    }

    return {
      deviceType,
      browserName,
      browserVersion,
      operatingSystem: platform,
      userAgent,
      deviceName: `${browserName} on ${platform}`,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }

  /**
   * Get user's IP address and location
   */
  async getLocationInfo() {
    try {
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      return {
        ip: data.ip,
        country: data.country_name,
        city: data.city,
        timezone: data.timezone
      };
    } catch (error) {
      console.error('Error getting location info:', error);
      return {
        ip: 'Unknown',
        country: 'Unknown',
        city: 'Unknown',
        timezone: this.deviceInfo.timezone
      };
    }
  }

  /**
   * Create a new session
   */
  async createSession(userId, loginMethod = 'email') {
    try {
      const locationInfo = await this.getLocationInfo();
      const sessionToken = this.generateSessionToken();
      
      const sessionData = {
        user_id: userId,
        session_token: sessionToken,
        device_id: this.generateDeviceId(),
        device_name: this.deviceInfo.deviceName,
        device_type: this.deviceInfo.deviceType,
        browser_name: this.deviceInfo.browserName,
        browser_version: this.deviceInfo.browserVersion,
        operating_system: this.deviceInfo.operatingSystem,
        user_agent: this.deviceInfo.userAgent,
        ip_address: locationInfo.ip,
        country: locationInfo.country,
        city: locationInfo.city,
        timezone: locationInfo.timezone,
        login_method: loginMethod,
        is_active: true,
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      };

      const { data, error } = await supabase
        .from('user_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) throw error;

      this.currentSession = data;
      
      // Log the login
      await this.logActivity(userId, 'login', loginMethod, true);
      
      // Set session cookie
      this.setSessionCookie(sessionToken);
      
      return { success: true, session: data };

    } catch (error) {
      console.error('Error creating session:', error);
      await this.logActivity(userId, 'login', loginMethod, false, error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update session activity
   */
  async updateSessionActivity(sessionId) {
    try {
      await supabase
        .from('user_sessions')
        .update({ 
          last_activity: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId);

    } catch (error) {
      console.error('Error updating session activity:', error);
    }
  }

  /**
   * Invalidate session
   */
  async invalidateSession(sessionId, userId, reason = 'logout') {
    try {
      await supabase
        .from('user_sessions')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId);

      // Log the logout
      await this.logActivity(userId, reason, null, true);
      
      // Clear session cookie
      this.clearSessionCookie();
      
      this.currentSession = null;
      
      return { success: true };

    } catch (error) {
      console.error('Error invalidating session:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get active sessions for user
   */
  async getUserSessions(userId) {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('last_activity', { ascending: false });

      if (error) throw error;

      return { success: true, sessions: data };

    } catch (error) {
      console.error('Error getting user sessions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Log user activity
   */
  async logActivity(userId, activityType, loginMethod, success, failureReason = null) {
    try {
      const locationInfo = await this.getLocationInfo();
      
      const logData = {
        user_id: userId,
        session_id: this.currentSession?.id || null,
        login_type: activityType,
        login_method: loginMethod,
        success,
        failure_reason: failureReason,
        ip_address: locationInfo.ip,
        user_agent: this.deviceInfo.userAgent,
        device_fingerprint: this.generateDeviceFingerprint(),
        country: locationInfo.country,
        city: locationInfo.city,
        is_suspicious: false, // TODO: Implement suspicious activity detection
        risk_score: 0 // TODO: Implement risk scoring
      };

      await supabase
        .from('login_history')
        .insert(logData);

    } catch (error) {
      console.error('Error logging activity:', error);
    }
  }

  /**
   * Generate session token
   */
  generateSessionToken() {
    return 'sess_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  /**
   * Generate device ID
   */
  generateDeviceId() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);
    
    const fingerprint = canvas.toDataURL() + 
                       navigator.userAgent + 
                       navigator.language + 
                       screen.width + 'x' + screen.height;
    
    return 'dev_' + btoa(fingerprint).substr(0, 16);
  }

  /**
   * Generate device fingerprint
   */
  generateDeviceFingerprint() {
    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack
    };
    
    return btoa(JSON.stringify(fingerprint));
  }

  /**
   * Set session cookie
   */
  setSessionCookie(sessionToken) {
    const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
    document.cookie = `session_token=${sessionToken}; expires=${expires.toUTCString()}; path=/; secure; samesite=strict`;
  }

  /**
   * Get session cookie
   */
  getSessionCookie() {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'session_token') {
        return value;
      }
    }
    return null;
  }

  /**
   * Clear session cookie
   */
  clearSessionCookie() {
    document.cookie = 'session_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
  }

  /**
   * Check if session is valid
   */
  async validateSession(sessionToken) {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('session_token', sessionToken)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        return { valid: false };
      }

      // Check if session is expired
      if (new Date(data.expires_at) < new Date()) {
        await this.invalidateSession(data.id, data.user_id, 'session_expired');
        return { valid: false, reason: 'expired' };
      }

      // Update last activity
      await this.updateSessionActivity(data.id);
      
      this.currentSession = data;
      return { valid: true, session: data };

    } catch (error) {
      console.error('Error validating session:', error);
      return { valid: false, error: error.message };
    }
  }

  /**
   * Force logout from all devices
   */
  async logoutAllDevices(userId) {
    try {
      await supabase
        .from('user_sessions')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      // Log the forced logout
      await this.logActivity(userId, 'forced_logout', null, true);
      
      this.clearSessionCookie();
      this.currentSession = null;
      
      return { success: true };

    } catch (error) {
      console.error('Error logging out all devices:', error);
      return { success: false, error: error.message };
    }
  }
}

// Export singleton instance
export const sessionManager = new SessionManagementService();
export default sessionManager;
