import { supabase } from '../lib/supabase';

class ProgressTrackingService {
  // Save assessment results
  static async saveAssessmentResults(userId, results) {
    try {
      const { data, error } = await supabase
        .from('assessment_results')
        .insert({
          user_id: userId,
          assessment_type: 'initial',
          score: results.score,
          level: results.level,
          answers: results.answers,
          recommendations: results.recommendations
        });

      if (error) throw error;

      // Generate personalized recommendations
      await this.generateRecommendations(userId, results);

      return { data, error: null };
    } catch (error) {
      console.error('Error saving assessment results:', error);
      return { data: null, error: error.message };
    }
  }

  // Generate personalized recommendations based on assessment
  static async generateRecommendations(userId, assessmentResults) {
    try {
      const recommendations = [];

      // Generate recommendations based on assessment results
      const { level, answers } = assessmentResults;
      
      // Beginner recommendations
      if (level === 'beginner') {
        recommendations.push({
          user_id: userId,
          recommendation_type: 'learning_path',
          item_id: 'network-fundamentals',
          item_title: 'Network Fundamentals',
          reason: 'Perfect starting point for cybersecurity beginners',
          priority: 1
        });
      }

      // Interest-based recommendations
      if (answers.interests?.includes('ethical_hacking')) {
        recommendations.push({
          user_id: userId,
          recommendation_type: 'learning_path',
          item_id: 'ethical-hacking-fundamentals',
          item_title: 'Ethical Hacking Fundamentals',
          reason: 'Based on your interest in ethical hacking',
          priority: 1
        });
      }

      if (answers.interests?.includes('network_security')) {
        recommendations.push({
          user_id: userId,
          recommendation_type: 'learning_path',
          item_id: 'network-security',
          item_title: 'Network Security',
          reason: 'Matches your network security interests',
          priority: 2
        });
      }

      // Save recommendations to database
      if (recommendations.length > 0) {
        const { error } = await supabase
          .from('user_recommendations')
          .insert(recommendations);

        if (error) throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error generating recommendations:', error);
      return { success: false, error: error.message };
    }
  }

  // Track learning progress
  static async trackLearningProgress(userId, pathId, moduleId = null, lessonId = null, progressData = {}) {
    try {
      const { data, error } = await supabase
        .from('user_learning_progress')
        .upsert({
          user_id: userId,
          learning_path_id: pathId,
          module_id: moduleId,
          lesson_id: lessonId,
          progress_percentage: progressData.progressPercentage || 0,
          time_spent_minutes: progressData.timeSpentMinutes || 0,
          last_accessed: new Date().toISOString(),
          completed_at: progressData.completed ? new Date().toISOString() : null
        }, {
          onConflict: 'user_id,learning_path_id,module_id,lesson_id'
        });

      if (error) throw error;

      // Log activity
      await this.logActivity(userId, 'learning_progress_update', {
        learning_path_id: pathId,
        module_id: moduleId,
        lesson_id: lessonId,
        progress_percentage: progressData.progressPercentage
      });

      return { data, error: null };
    } catch (error) {
      console.error('Error tracking learning progress:', error);
      return { data: null, error: error.message };
    }
  }

  // Log user activity
  static async logActivity(userId, activityType, activityData = {}, ipAddress = null, userAgent = null) {
    try {
      const { data, error } = await supabase
        .rpc('log_user_activity', {
          user_uuid: userId,
          activity_type_param: activityType,
          activity_data_param: activityData,
          ip_address_param: ipAddress,
          user_agent_param: userAgent
        });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error logging activity:', error);
      return { data: null, error: error.message };
    }
  }

  // Get user recommendations
  static async getUserRecommendations(userId) {
    try {
      const { data, error } = await supabase
        .from('user_recommendations')
        .select('*')
        .eq('user_id', userId)
        .eq('is_dismissed', false)
        .order('priority', { ascending: true })
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching recommendations:', error);
      return { data: null, error: error.message };
    }
  }

  // Get user learning streak
  static async getUserStreak(userId) {
    try {
      const { data, error } = await supabase
        .from('user_learning_streaks')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user streak:', error);
      return { data: null, error: error.message };
    }
  }

  // Get user notifications (enhanced)
  static async getUserNotifications(userId, includeRead = true) {
    try {
      let query = supabase
        .from('user_notifications')
        .select('*')
        .eq('user_id', userId)
        .eq('is_dismissed', false);

      // Filter by read status if specified
      if (!includeRead) {
        query = query.eq('is_read', false);
      }

      // Filter out expired notifications
      query = query.or('expires_at.is.null,expires_at.gt.' + new Date().toISOString());

      // Filter out scheduled notifications that aren't due yet
      query = query.or('scheduled_for.is.null,scheduled_for.lte.' + new Date().toISOString());

      const { data, error } = await query
        .order('is_pinned', { ascending: false })
        .order('priority', { ascending: true })
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return { data: null, error: error.message };
    }
  }

  // Get unread notification count
  static async getUnreadNotificationCount(userId) {
    try {
      const { data, error } = await supabase
        .rpc('get_unread_notification_count', {
          user_id_param: userId
        });

      if (error) throw error;
      return { count: data || 0, error: null };
    } catch (error) {
      console.error('Error fetching unread count:', error);
      return { count: 0, error: error.message };
    }
  }

  // Mark notification as read
  static async markNotificationAsRead(notificationId) {
    try {
      const { data, error } = await supabase
        .rpc('mark_notification_read', {
          notification_id_param: notificationId
        });

      if (error) throw error;
      return { success: data, error: null };
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return { success: false, error: error.message };
    }
  }

  // Dismiss notification
  static async dismissNotification(notificationId) {
    try {
      const { data, error } = await supabase
        .rpc('dismiss_notification', {
          notification_id_param: notificationId
        });

      if (error) throw error;
      return { success: data, error: null };
    } catch (error) {
      console.error('Error dismissing notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Create notification (enhanced)
  static async createNotification(userId, title, message, options = {}) {
    try {
      const {
        type = 'info',
        emoji = null,
        imageUrl = null,
        iconName = null,
        iconColor = 'blue',
        actionUrl = null,
        actionLabel = null,
        actionType = 'navigate',
        category = 'system',
        priority = 2,
        metadata = {},
        scheduledFor = null,
        expiresAt = null,
        isPinned = false
      } = options;

      const { data, error } = await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          title,
          message,
          type,
          emoji,
          image_url: imageUrl,
          icon_name: iconName,
          icon_color: iconColor,
          action_url: actionUrl,
          action_label: actionLabel,
          action_type: actionType,
          category,
          priority,
          metadata,
          scheduled_for: scheduledFor,
          expires_at: expiresAt,
          is_pinned: isPinned
        });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error creating notification:', error);
      return { data: null, error: error.message };
    }
  }

  // Create notification from template
  static async createNotificationFromTemplate(userId, templateName, variables = {}, actionUrl = null, scheduledFor = null) {
    try {
      const { data, error } = await supabase
        .rpc('create_notification_from_template', {
          template_name_param: templateName,
          user_id_param: userId,
          variables: variables,
          action_url_param: actionUrl,
          scheduled_for_param: scheduledFor
        });

      if (error) throw error;
      return { notificationId: data, error: null };
    } catch (error) {
      console.error('Error creating notification from template:', error);
      return { notificationId: null, error: error.message };
    }
  }

  // Create test notification (for testing purposes)
  static async createTestNotification(userId, title, message, type = 'info', emoji = null, actionUrl = null, actionLabel = null) {
    try {
      const { data, error } = await supabase
        .rpc('create_test_notification', {
          user_id_param: userId,
          title_param: title,
          message_param: message,
          type_param: type,
          emoji_param: emoji,
          action_url_param: actionUrl,
          action_label_param: actionLabel
        });

      if (error) throw error;
      return { notificationId: data, error: null };
    } catch (error) {
      console.error('Error creating test notification:', error);
      return { notificationId: null, error: error.message };
    }
  }

  // Get user skill assessments
  static async getUserSkillAssessments(userId) {
    try {
      const { data, error } = await supabase
        .from('user_skill_assessments')
        .select('*')
        .eq('user_id', userId)
        .order('assessment_date', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching skill assessments:', error);
      return { data: null, error: error.message };
    }
  }

  // Update skill level
  static async updateSkillLevel(userId, skillName, skillLevel, evidenceType = null, evidenceId = null) {
    try {
      const { data, error } = await supabase
        .from('user_skill_assessments')
        .upsert({
          user_id: userId,
          skill_name: skillName,
          skill_level: skillLevel,
          evidence_type: evidenceType,
          evidence_id: evidenceId,
          assessment_date: new Date().toISOString()
        });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error updating skill level:', error);
      return { data: null, error: error.message };
    }
  }

  // Get comprehensive user progress
  static async getUserProgress(userId) {
    try {
      const [
        assessmentResults,
        learningProgress,
        recommendations,
        streak,
        notifications,
        skillAssessments
      ] = await Promise.all([
        this.getAssessmentResults(userId),
        this.getLearningProgress(userId),
        this.getUserRecommendations(userId),
        this.getUserStreak(userId),
        this.getUserNotifications(userId),
        this.getUserSkillAssessments(userId)
      ]);

      return {
        assessment: assessmentResults.data,
        progress: learningProgress.data,
        recommendations: recommendations.data,
        streak: streak.data,
        notifications: notifications.data,
        skills: skillAssessments.data,
        error: null
      };
    } catch (error) {
      console.error('Error fetching user progress:', error);
      return { error: error.message };
    }
  }

  // Get assessment results
  static async getAssessmentResults(userId) {
    try {
      const { data, error } = await supabase
        .from('assessment_results')
        .select('*')
        .eq('user_id', userId)
        .order('completed_at', { ascending: false })
        .limit(1);

      if (error) throw error;
      return { data: data?.[0] || null, error: null };
    } catch (error) {
      console.error('Error fetching assessment results:', error);
      return { data: null, error: error.message };
    }
  }

  // Get learning progress
  static async getLearningProgress(userId) {
    try {
      const { data, error } = await supabase
        .from('user_learning_progress')
        .select('*')
        .eq('user_id', userId)
        .order('last_accessed', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching learning progress:', error);
      return { data: null, error: error.message };
    }
  }
}

export default ProgressTrackingService;
