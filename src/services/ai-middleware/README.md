# CyberForce AI Middleware Service

This service provides a middleware layer between the CyberForce application and the Ollama LLM service. It handles request processing, caching, rate limiting, and database interactions.

## Features

- Integration with Ollama LLM service
- Support for multiple LLM models (Mistral 7B, Phi-2)
- Response caching for improved performance
- Rate limiting to prevent abuse
- Feedback collection and storage
- Conversation history tracking
- Supabase database integration

## Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for local development)
- Supabase account and project

## Setup Instructions

### 1. Environment Configuration

Copy the example environment file and update it with your Supabase credentials:

```bash
cp .env.example .env
```

Edit the `.env` file to include your Supabase URL and API key.

### 2. Start the Services

Use Docker Compose to start the Ollama and AI middleware services:

```bash
docker-compose -f docker-compose.ollama.yml up -d
```

### 3. Pull the Required Models

After the Ollama service is running, pull the required models:

```bash
./scripts/pull-models.sh
```

This will download the Mistral 7B and Phi-2 models, which may take some time depending on your internet connection.

### 4. Set Up the Database

Apply the database migrations to your Supabase project:

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy the contents of `migrations/ai_tables.sql`
4. Run the SQL script to create the necessary tables and policies

## API Endpoints

### Chat Endpoint

```
POST /api/ai/chat
```

Request body:
```json
{
  "query": "What is XSS?",
  "language": "english",
  "userId": "optional-user-id",
  "model": "mistral"
}
```

Response:
```json
{
  "content": "XSS (Cross-Site Scripting) is a type of security vulnerability...",
  "category": "security",
  "language": "english",
  "cached": false,
  "source": "ollama"
}
```

### Feedback Endpoint

```
POST /api/ai/feedback
```

Request body:
```json
{
  "query": "What is XSS?",
  "response": {
    "content": "XSS (Cross-Site Scripting) is a type of security vulnerability..."
  },
  "rating": 1,
  "comment": "Great explanation!",
  "userId": "optional-user-id"
}
```

Response:
```json
{
  "success": true
}
```

### Models Endpoint

```
GET /api/ai/models
```

Response:
```json
{
  "models": [
    {
      "id": "mistral",
      "name": "mistral",
      "description": "Ollama model: mistral",
      "size": 4100000000,
      "modified_at": "2023-12-01T12:00:00Z"
    },
    {
      "id": "phi2",
      "name": "phi2",
      "description": "Ollama model: phi2",
      "size": 2800000000,
      "modified_at": "2023-12-01T12:00:00Z"
    }
  ]
}
```

## Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  CyberForce App │────▶│  AI Middleware  │────▶│  Ollama Service │
│                 │     │                 │     │                 │
└─────────────────┘     └────────┬────────┘     └─────────────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │
                        │    Supabase     │
                        │    Database     │
                        │                 │
                        └─────────────────┘
```

## Development

### Local Development

To run the service locally without Docker:

```bash
npm install
npm run dev
```

### Testing

```bash
npm test
```

## Cloud Deployment

For cloud deployment, you'll need to:

1. Build and push the Docker images to a container registry
2. Deploy the containers to your cloud provider (Azure, AWS, GCP, etc.)
3. Configure environment variables for the cloud environment
4. Set up networking and security rules

Detailed cloud deployment instructions are available in the `docs/cloud-deployment.md` file.

## Troubleshooting

### Common Issues

1. **Ollama service not responding**
   - Check if the Ollama container is running: `docker ps`
   - Check Ollama logs: `docker logs cyberforce-ollama`
   - Ensure the models are properly downloaded

2. **Database connection issues**
   - Verify your Supabase credentials in the `.env` file
   - Check if the tables exist in your Supabase project
   - Ensure RLS policies are properly configured

3. **Memory issues**
   - Adjust the memory limits in `docker-compose.ollama.yml` based on your system resources
   - Consider using a smaller model if you're experiencing memory constraints

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
