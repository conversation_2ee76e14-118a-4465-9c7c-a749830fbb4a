#!/bin/bash

# Script to pull required Ollama models
# This script should be run after the Ollama container is up and running

# Set the Ollama API URL
OLLAMA_API_URL=${OLLAMA_API_URL:-"http://localhost:11434"}

# Function to check if <PERSON>lla<PERSON> is running
check_ollama() {
  echo "Checking if Olla<PERSON> is running at $OLLAMA_API_URL..."
  if curl -s "$OLLAMA_API_URL/api/tags" > /dev/null; then
    echo "Ollama is running."
    return 0
  else
    echo "Ollama is not running or not accessible at $OLLAMA_API_URL."
    return 1
  fi
}

# Function to pull a model
pull_model() {
  local model=$1
  echo "Pulling model: $model..."
  curl -X POST "$OLLAMA_API_URL/api/pull" -d "{\"name\":\"$model\"}"
  echo "Model $model pulled successfully."
}

# Main function
main() {
  # Check if Ollama is running
  if ! check_ollama; then
    echo "Please make sure <PERSON><PERSON><PERSON> is running before executing this script."
    exit 1
  fi

  # Pull models
  pull_model "mistral"
  pull_model "phi2"
  
  echo "All models pulled successfully."
}

# Execute main function
main
