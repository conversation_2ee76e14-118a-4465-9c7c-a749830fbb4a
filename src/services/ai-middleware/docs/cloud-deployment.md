# Cloud Deployment Guide for CyberForce AI

This guide provides instructions for deploying the CyberForce AI solution to Azure cloud.

## Prerequisites

- Azure account with active subscription
- Azure CLI installed and configured
- Docker installed locally
- Docker Hub account or Azure Container Registry access

## Deployment Steps

### 1. Create Azure Container Registry (ACR)

```bash
# Create a resource group
az group create --name cyberforce-ai-rg --location eastus

# Create a container registry
az acr create --resource-group cyberforce-ai-rg --name cyberforceregistry --sku Basic

# Log in to the registry
az acr login --name cyberforceregistry
```

### 2. Build and Push Docker Images

```bash
# Build the AI middleware image
docker build -t cyberforceregistry.azurecr.io/cyberforce-ai-middleware:latest ./src/services/ai-middleware

# Push the image to ACR
docker push cyberforceregistry.azurecr.io/cyberforce-ai-middleware:latest
```

### 3. Create Azure Container Instances

#### 3.1 Create Ollama Container Instance

```bash
# Create a file storage share for Ollama data
az storage account create \
  --name cyberforceaistorage \
  --resource-group cyberforce-ai-rg \
  --location eastus \
  --sku Standard_LRS

# Create a file share
az storage share create \
  --name ollama-data \
  --account-name cyberforceaistorage

# Get storage key
STORAGE_KEY=$(az storage account keys list \
  --account-name cyberforceaistorage \
  --resource-group cyberforce-ai-rg \
  --query "[0].value" -o tsv)

# Create Ollama container instance
az container create \
  --resource-group cyberforce-ai-rg \
  --name cyberforce-ollama \
  --image ollama/ollama:latest \
  --dns-name-label cyberforce-ollama \
  --ports 11434 \
  --cpu 4 \
  --memory 8 \
  --environment-variables OLLAMA_HOST=0.0.0.0 \
  --azure-file-volume-account-name cyberforceaistorage \
  --azure-file-volume-account-key $STORAGE_KEY \
  --azure-file-volume-share-name ollama-data \
  --azure-file-volume-mount-path /root/.ollama
```

#### 3.2 Create AI Middleware Container Instance

```bash
# Create environment variables file
cat > env.txt << EOF
OLLAMA_API_URL=http://cyberforce-ollama.eastus.azurecontainer.io:11434
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
NODE_ENV=production
PORT=3006
EOF

# Create AI middleware container instance
az container create \
  --resource-group cyberforce-ai-rg \
  --name cyberforce-ai-middleware \
  --image cyberforceregistry.azurecr.io/cyberforce-ai-middleware:latest \
  --dns-name-label cyberforce-ai-middleware \
  --ports 3006 \
  --cpu 2 \
  --memory 4 \
  --environment-variables-file env.txt \
  --registry-username cyberforceregistry \
  --registry-password $(az acr credential show -n cyberforceregistry --query "passwords[0].value" -o tsv)
```

### 4. Pull LLM Models

After the Ollama container is running, you need to pull the required models:

```bash
# Get the Ollama container IP
OLLAMA_IP=$(az container show \
  --resource-group cyberforce-ai-rg \
  --name cyberforce-ollama \
  --query ipAddress.fqdn -o tsv)

# Pull the Mistral model
curl -X POST "http://$OLLAMA_IP:11434/api/pull" -d '{"name":"mistral"}'

# Pull the Phi-2 model
curl -X POST "http://$OLLAMA_IP:11434/api/pull" -d '{"name":"phi2"}'
```

### 5. Configure Azure Application Gateway (Optional)

For production deployments, you should set up an Application Gateway to handle SSL termination and routing:

```bash
# Create a public IP for the gateway
az network public-ip create \
  --resource-group cyberforce-ai-rg \
  --name cyberforce-gateway-ip \
  --allocation-method Static \
  --sku Standard

# Create the Application Gateway
az network application-gateway create \
  --resource-group cyberforce-ai-rg \
  --name cyberforce-gateway \
  --sku Standard_v2 \
  --public-ip-address cyberforce-gateway-ip \
  --frontend-port 443 \
  --http-settings-port 3006 \
  --http-settings-protocol Http \
  --servers cyberforce-ai-middleware.eastus.azurecontainer.io
```

### 6. Update Application Configuration

Update your CyberForce application to use the new AI middleware endpoint:

```javascript
// In your ollamaChat.js file
const API_URL = 'https://cyberforce-gateway.eastus.cloudapp.azure.com/api/ai';
// or if not using Application Gateway
const API_URL = 'http://cyberforce-ai-middleware.eastus.azurecontainer.io:3006/api/ai';
```

## Monitoring and Maintenance

### Monitoring Container Health

```bash
# Check Ollama container logs
az container logs --resource-group cyberforce-ai-rg --name cyberforce-ollama

# Check AI middleware container logs
az container logs --resource-group cyberforce-ai-rg --name cyberforce-ai-middleware
```

### Updating the Deployment

To update the AI middleware:

1. Build a new Docker image with your changes
2. Push the new image to ACR
3. Restart the container instance:

```bash
az container restart --resource-group cyberforce-ai-rg --name cyberforce-ai-middleware
```

### Scaling Considerations

For higher load scenarios, consider:

1. Increasing the CPU and memory allocation for the containers
2. Moving to Azure Kubernetes Service (AKS) for better scaling capabilities
3. Using Azure Cache for Redis to improve caching performance

## Cost Optimization

To optimize costs:

1. Use Azure B-series VMs for development environments
2. Set up auto-shutdown for non-production resources
3. Monitor resource usage and adjust allocations as needed
4. Consider using Azure Spot instances for non-critical workloads

## Security Considerations

1. Use Azure Key Vault to store sensitive information like API keys
2. Enable Azure Defender for Containers
3. Implement network security groups to restrict access
4. Set up Azure Monitor to detect unusual activity
5. Regularly update container images with security patches

## Troubleshooting

### Common Issues

1. **Container fails to start**
   - Check resource allocation (CPU/memory)
   - Verify environment variables
   - Check container logs for errors

2. **Model download failures**
   - Ensure sufficient storage space
   - Check network connectivity
   - Try downloading a smaller model first

3. **Performance issues**
   - Monitor CPU and memory usage
   - Consider using a smaller model
   - Optimize caching strategies

## Backup and Disaster Recovery

1. Regularly backup the Ollama data volume
2. Set up geo-redundant storage for critical data
3. Document the deployment process for quick recovery
4. Consider multi-region deployment for high availability
