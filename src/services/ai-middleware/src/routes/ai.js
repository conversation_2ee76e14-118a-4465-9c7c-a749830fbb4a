/**
 * AI Routes
 * 
 * This module handles all AI-related routes for the middleware service.
 */

const express = require('express');
const router = express.Router();
const { createClient } = require('@supabase/supabase-js');
const NodeCache = require('node-cache');
const axios = require('axios');
const winston = require('winston');

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Ollama API client
const ollama = axios.create({
  baseURL: process.env.OLLAMA_API_URL || 'http://localhost:11434',
  timeout: 30000, // 30 seconds timeout
});

// Initialize in-memory cache
const cache = new NodeCache({
  stdTTL: 3600, // 1 hour default TTL
  checkperiod: 600, // Check for expired keys every 10 minutes
  useClones: false
});

/**
 * Generate a chat response using Ollama
 * @route POST /api/ai/chat
 */
router.post('/chat', async (req, res) => {
  try {
    const { query, language = 'english', userId = null, model = 'mistral' } = req.body;
    
    if (!query || typeof query !== 'string') {
      return res.status(400).json({ error: 'Invalid query parameter' });
    }
    
    logger.info(`Generating response for query: "${query.substring(0, 50)}..." using model: ${model}`);
    
    // Check cache first
    const cacheKey = `${model}:${query.toLowerCase().trim()}`;
    const cachedResponse = cache.get(cacheKey);
    
    if (cachedResponse) {
      logger.info('Using cached response');
      return res.json({ ...cachedResponse, cached: true });
    }
    
    // Try to get response from database first
    try {
      const { data: dbResponse, error } = await supabase
        .from('ai_responses')
        .select('*')
        .textSearch('keyword', query.toLowerCase())
        .limit(1)
        .single();
      
      if (!error && dbResponse) {
        logger.info('Using database response');
        const response = {
          content: dbResponse.content,
          category: dbResponse.category || 'general',
          language,
          cached: false,
          source: 'database'
        };
        
        // Cache the response
        cache.set(cacheKey, response);
        
        return res.json(response);
      }
    } catch (dbError) {
      logger.info('No matching response in database');
    }
    
    // Generate response using Ollama
    try {
      // Create system prompt for cybersecurity context
      const systemPrompt = `You are CyberForce AI, an advanced cybersecurity assistant. 
You specialize in cybersecurity topics including but not limited to:
- Network security
- Application security
- Cloud security
- Threat intelligence
- Incident response
- Security operations
- Penetration testing
- Compliance and regulations

Respond in ${language} language.
Keep your responses educational, accurate, and helpful.
Format your responses with markdown for better readability.
Include code examples when relevant.
Be concise but thorough in your explanations.`;

      // Call Ollama API
      const ollamaResponse = await ollama.post('/api/chat', {
        model: model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: query }
        ],
        options: {
          temperature: 0.7,
          top_p: 0.9
        }
      });
      
      // Extract response content
      const content = ollamaResponse.data.message.content;
      
      // Determine category based on content
      let category = 'general';
      if (content.toLowerCase().includes('code') || content.toLowerCase().includes('script')) {
        category = 'code';
      } else if (content.toLowerCase().includes('attack') || content.toLowerCase().includes('threat')) {
        category = 'threat';
      } else if (content.toLowerCase().includes('compliance') || content.toLowerCase().includes('regulation')) {
        category = 'compliance';
      }
      
      // Create response object
      const response = {
        content,
        category,
        language,
        cached: false,
        source: 'ollama'
      };
      
      // Cache the response
      cache.set(cacheKey, response);
      
      // Save to database if user is authenticated
      if (userId) {
        try {
          await supabase.from('ai_responses').insert({
            keyword: query.toLowerCase(),
            content,
            category,
            language,
            user_id: userId
          });
        } catch (saveError) {
          logger.error('Error saving response to database:', saveError);
        }
      }
      
      return res.json(response);
    } catch (ollamaError) {
      logger.error('Error calling Ollama API:', ollamaError);
      throw ollamaError;
    }
  } catch (error) {
    logger.error('Error in /chat endpoint:', error);
    res.status(500).json({
      error: 'Failed to generate response',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
  }
});

/**
 * Submit feedback for an AI response
 * @route POST /api/ai/feedback
 */
router.post('/feedback', async (req, res) => {
  try {
    const { query, response, rating, comment = null, userId = null } = req.body;
    
    if (!query || !response || rating === undefined) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }
    
    if (![1, 0, -1].includes(rating)) {
      return res.status(400).json({ error: 'Invalid rating value' });
    }
    
    logger.info(`Received feedback for query: "${query.substring(0, 50)}...", rating: ${rating}`);
    
    // Save feedback to database
    if (userId) {
      try {
        await supabase.from('ai_feedback').insert({
          user_id: userId,
          query,
          response: response.content,
          rating,
          comment,
          created_at: new Date().toISOString()
        });
      } catch (dbError) {
        logger.error('Error saving feedback to database:', dbError);
        // Continue even if saving fails
      }
    }
    
    // If positive feedback (rating = 1), prioritize this response in the cache
    if (rating === 1) {
      // Update TTL for this response in cache to be longer
      const cacheKey = `mistral:${query.toLowerCase().trim()}`;
      if (cache.has(cacheKey)) {
        cache.ttl(cacheKey, 24 * 3600); // Extend TTL to 24 hours
      }
    }
    
    return res.json({ success: true });
  } catch (error) {
    logger.error('Error in /feedback endpoint:', error);
    res.status(500).json({
      error: 'Failed to process feedback',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
  }
});

/**
 * Get available models
 * @route GET /api/ai/models
 */
router.get('/models', async (req, res) => {
  try {
    // Get models from Ollama
    const response = await ollama.get('/api/tags');
    
    // Format the response
    const models = response.data.models.map(model => ({
      id: model.name,
      name: model.name,
      description: `Ollama model: ${model.name}`,
      size: model.size,
      modified_at: model.modified_at
    }));
    
    return res.json({ models });
  } catch (error) {
    logger.error('Error fetching models:', error);
    res.status(500).json({
      error: 'Failed to fetch models',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
  }
});

module.exports = router;
