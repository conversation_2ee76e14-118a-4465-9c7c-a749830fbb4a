-- Migration file for AI-related tables in Supabase

-- Table for storing AI responses
CREATE TABLE IF NOT EXISTS ai_responses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  keyword TEXT NOT NULL,
  content TEXT NOT NULL,
  category TEXT DEFAULT 'general',
  language TEXT DEFAULT 'english',
  domain TEXT DEFAULT 'CYBERSECURITY',
  source TEXT DEFAULT 'ollama',
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add full-text search index on keyword column
CREATE INDEX IF NOT EXISTS ai_responses_keyword_idx ON ai_responses USING GIN (to_tsvector('english', keyword));

-- Table for storing user feedback on AI responses
CREATE TABLE IF NOT EXISTS ai_feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  query TEXT NOT NULL,
  response TEXT NOT NULL,
  rating INTEGER NOT NULL CHECK (rating IN (-1, 0, 1)),
  comment TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing conversation history
CREATE TABLE IF NOT EXISTS ai_conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  title TEXT DEFAULT 'New Conversation',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing individual messages in conversations
CREATE TABLE IF NOT EXISTS ai_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID REFERENCES ai_conversations(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for ai_responses
CREATE TRIGGER update_ai_responses_updated_at
BEFORE UPDATE ON ai_responses
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Trigger for ai_conversations
CREATE TRIGGER update_ai_conversations_updated_at
BEFORE UPDATE ON ai_conversations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- RLS Policies for ai_responses
ALTER TABLE ai_responses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow users to view their own responses"
ON ai_responses
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Allow users to insert their own responses"
ON ai_responses
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own responses"
ON ai_responses
FOR UPDATE
USING (auth.uid() = user_id);

-- RLS Policies for ai_feedback
ALTER TABLE ai_feedback ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow users to view their own feedback"
ON ai_feedback
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Allow users to insert their own feedback"
ON ai_feedback
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- RLS Policies for ai_conversations
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow users to view their own conversations"
ON ai_conversations
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Allow users to insert their own conversations"
ON ai_conversations
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own conversations"
ON ai_conversations
FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own conversations"
ON ai_conversations
FOR DELETE
USING (auth.uid() = user_id);

-- RLS Policies for ai_messages
ALTER TABLE ai_messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow users to view messages in their conversations"
ON ai_messages
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM ai_conversations
    WHERE ai_conversations.id = ai_messages.conversation_id
    AND ai_conversations.user_id = auth.uid()
  )
);

CREATE POLICY "Allow users to insert messages in their conversations"
ON ai_messages
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM ai_conversations
    WHERE ai_conversations.id = ai_messages.conversation_id
    AND ai_conversations.user_id = auth.uid()
  )
);

-- Create a view for analytics
CREATE OR REPLACE VIEW ai_analytics AS
SELECT
  DATE_TRUNC('day', created_at) AS day,
  COUNT(*) AS total_queries,
  COUNT(DISTINCT user_id) AS unique_users,
  category,
  AVG(
    CASE 
      WHEN EXISTS (
        SELECT 1 FROM ai_feedback 
        WHERE ai_feedback.query = ai_responses.keyword
        AND ai_feedback.rating = 1
      ) THEN 1
      WHEN EXISTS (
        SELECT 1 FROM ai_feedback 
        WHERE ai_feedback.query = ai_responses.keyword
        AND ai_feedback.rating = -1
      ) THEN 0
      ELSE 0.5
    END
  ) AS avg_satisfaction
FROM ai_responses
GROUP BY DATE_TRUNC('day', created_at), category
ORDER BY day DESC, category;
