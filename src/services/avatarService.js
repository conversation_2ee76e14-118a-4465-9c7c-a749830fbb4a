import { supabase } from '../lib/supabase';
import toast from 'react-hot-toast';

/**
 * Avatar Service
 * 
 * Handles avatar upload, management, and storage operations
 */
class AvatarService {
  constructor() {
    this.bucketName = 'avatars';
    this.maxFileSize = 5 * 1024 * 1024; // 5MB
    this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  }

  /**
   * Validate image file
   */
  validateFile(file) {
    const errors = [];

    if (!file) {
      errors.push('No file selected');
      return errors;
    }

    // Check file type
    if (!this.allowedTypes.includes(file.type)) {
      errors.push('Invalid file type. Please select a JPEG, PNG, GIF, or WebP image.');
    }

    // Check file size
    if (file.size > this.maxFileSize) {
      errors.push(`File size must be less than ${this.maxFileSize / (1024 * 1024)}MB`);
    }

    return errors;
  }

  /**
   * Generate unique filename
   */
  generateFileName(userId, originalName) {
    const timestamp = Date.now();
    const extension = originalName.split('.').pop().toLowerCase();
    return `${userId}/${timestamp}.${extension}`;
  }

  /**
   * Create image preview
   */
  createPreview(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(e);
      reader.readAsDataURL(file);
    });
  }

  /**
   * Upload avatar to Supabase Storage
   */
  async uploadAvatar(userId, file, onProgress = null) {
    try {
      // Validate file
      const validationErrors = this.validateFile(file);
      if (validationErrors.length > 0) {
        throw new Error(validationErrors.join(', '));
      }

      // Ensure bucket exists
      await this.ensureBucketExists();

      // Generate filename
      const fileName = this.generateFileName(userId, file.name);

      // Upload file
      const { data, error } = await supabase.storage
        .from(this.bucketName)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true, // Allow overwriting
          onUploadProgress: onProgress
        });

      if (error) {
        console.error('Storage upload error:', error);
        throw new Error(`Upload failed: ${error.message}`);
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(this.bucketName)
        .getPublicUrl(fileName);

      return {
        success: true,
        data: {
          fileName,
          filePath: data.path,
          publicUrl,
          fileSize: file.size,
          fileType: file.type
        }
      };

    } catch (error) {
      console.error('Avatar upload error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update user profile with new avatar
   */
  async updateUserAvatar(userId, avatarData) {
    try {
      // Update user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({
          avatar_url: avatarData.publicUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (profileError) throw profileError;

      // Log avatar upload
      const { error: logError } = await supabase
        .from('user_avatars')
        .insert({
          user_id: userId,
          file_name: avatarData.fileName,
          file_path: avatarData.filePath,
          file_size: avatarData.fileSize,
          file_type: avatarData.fileType,
          public_url: avatarData.publicUrl,
          is_current: true,
          upload_status: 'completed'
        });

      if (logError) console.warn('Failed to log avatar upload:', logError);

      // Set previous avatars as not current
      await supabase
        .from('user_avatars')
        .update({ is_current: false })
        .eq('user_id', userId)
        .neq('public_url', avatarData.publicUrl);

      return { success: true };

    } catch (error) {
      console.error('Error updating user avatar:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete avatar from storage
   */
  async deleteAvatar(filePath) {
    try {
      const { error } = await supabase.storage
        .from(this.bucketName)
        .remove([filePath]);

      if (error) throw error;

      return { success: true };

    } catch (error) {
      console.error('Error deleting avatar:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user's avatar history
   */
  async getUserAvatars(userId) {
    try {
      const { data, error } = await supabase
        .from('user_avatars')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return {
        success: true,
        data: data || []
      };

    } catch (error) {
      console.error('Error fetching user avatars:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Complete avatar upload process
   */
  async completeAvatarUpload(userId, file, onProgress = null) {
    try {
      // Show loading toast
      const loadingToast = toast.loading('Uploading avatar...');

      // Upload file
      const uploadResult = await this.uploadAvatar(userId, file, onProgress);
      
      if (!uploadResult.success) {
        toast.dismiss(loadingToast);
        toast.error(uploadResult.error);
        return uploadResult;
      }

      // Update user profile
      const updateResult = await this.updateUserAvatar(userId, uploadResult.data);
      
      toast.dismiss(loadingToast);
      
      if (updateResult.success) {
        toast.success('Avatar updated successfully!');
        return {
          success: true,
          data: uploadResult.data
        };
      } else {
        toast.error('Failed to update profile');
        return updateResult;
      }

    } catch (error) {
      console.error('Complete avatar upload error:', error);
      toast.error('Failed to upload avatar');
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Ensure bucket exists
   */
  async ensureBucketExists() {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();

      if (listError) {
        console.warn('Could not list buckets:', listError);
        return; // Continue anyway, bucket might exist
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.bucketName);

      if (!bucketExists) {
        console.log('Avatar bucket does not exist, attempting to create...');

        // Try to create bucket via SQL (more reliable)
        const { error: createError } = await supabase.rpc('create_avatar_bucket');

        if (createError) {
          console.warn('Could not create bucket via RPC:', createError);
          // Try direct creation as fallback
          const { error: directError } = await supabase.storage.createBucket(this.bucketName, {
            public: true,
            allowedMimeTypes: this.allowedTypes,
            fileSizeLimit: this.maxFileSize
          });

          if (directError) {
            console.warn('Direct bucket creation failed:', directError);
          }
        }
      }

    } catch (error) {
      console.warn('Error ensuring bucket exists:', error);
      // Don't throw error, let upload attempt continue
    }
  }

  /**
   * Initialize storage bucket (run once)
   */
  async initializeBucket() {
    try {
      await this.ensureBucketExists();
      return { success: true };

    } catch (error) {
      console.error('Error initializing avatar bucket:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
export const avatarService = new AvatarService();
export default avatarService;
