/**
 * SecurityInsightsService.js
 * 
 * This service handles fetching, processing, and real-time updates for security insights data.
 * It integrates with Supabase for data storage and real-time updates.
 */

import { supabase, safeQuery } from '../lib/supabase';
import RealThreatDataService from './RealThreatDataService';

// Cache for security insights data
const securityInsightsCache = {
  securityPosture: null,
  threatIntelligence: null,
  lastUpdated: null,
  subscription: null
};

/**
 * Initialize the security insights service
 * @returns {Promise} Promise that resolves when initialization is complete
 */
const initializeSecurityInsights = async () => {
  console.log('Initializing Security Insights Service...');
  
  try {
    // Initialize the real threat data service
    await RealThreatDataService.initialize();
    
    // Set up real-time subscription for security posture updates
    setupRealtimeSubscriptions();
    
    // Load initial data
    await fetchSecurityInsights();
    
    console.log('Security Insights Service initialized successfully');
    return true;
  } catch (error) {
    console.error('Failed to initialize Security Insights Service:', error);
    return false;
  }
};

/**
 * Set up real-time subscriptions for security insights data
 */
const setupRealtimeSubscriptions = () => {
  // Unsubscribe from any existing subscription
  if (securityInsightsCache.subscription) {
    securityInsightsCache.subscription.unsubscribe();
  }
  
  // Subscribe to security_posture table changes
  securityInsightsCache.subscription = supabase
    .channel('security-insights-changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'security_posture' }, 
      (payload) => {
        console.log('Security posture update received:', payload);
        // Update the cache with the new data
        if (payload.new) {
          securityInsightsCache.securityPosture = payload.new;
          securityInsightsCache.lastUpdated = new Date().toISOString();
        }
      }
    )
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'threat_intelligence' },
      (payload) => {
        console.log('Threat intelligence update received:', payload);
        // Update the cache with the new data
        if (payload.new) {
          securityInsightsCache.threatIntelligence = payload.new;
          securityInsightsCache.lastUpdated = new Date().toISOString();
        }
      }
    )
    .subscribe();
    
  console.log('Real-time subscriptions set up for security insights data');
};

/**
 * Fetch security insights data from the database
 * @returns {Promise} Promise that resolves with the security insights data
 */
const fetchSecurityInsights = async () => {
  try {
    console.log('Fetching security insights data...');
    
    // Get the current user session
    const { data: { session } } = await supabase.auth.getSession();
    const userId = session?.user?.id;
    
    // If no user is logged in or we're in demo mode, use demo data
    if (!userId || userId === 'demo' || userId.includes('demo') || userId.includes('goutham-user-id')) {
      console.log('Demo mode detected, using demo security insights data');
      return getDemoSecurityInsights();
    }
    
    // Fetch security posture data
    const { data: securityPosture, error: securityPostureError } = await supabase
      .from('security_posture')
      .select('*')
      .eq('user_id', userId || 'demo')
      .single();
      
    if (securityPostureError && securityPostureError.code !== 'PGRST116') {
      console.error('Error fetching security posture:', securityPostureError);
      throw securityPostureError;
    }
    
    // Fetch threat intelligence data
    const { data: threatIntelligence, error: threatIntelligenceError } = await supabase
      .from('threat_intelligence')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();
      
    if (threatIntelligenceError && threatIntelligenceError.code !== 'PGRST116') {
      console.error('Error fetching threat intelligence:', threatIntelligenceError);
      throw threatIntelligenceError;
    }
    
    // If no data exists, create initial data
    if (!securityPosture || !threatIntelligence) {
      console.log('No security insights data found, creating initial data...');
      return createInitialSecurityInsights(userId);
    }
    
    // Update the cache
    securityInsightsCache.securityPosture = securityPosture;
    securityInsightsCache.threatIntelligence = threatIntelligence;
    securityInsightsCache.lastUpdated = new Date().toISOString();
    
    console.log('Security insights data fetched successfully');
    
    return {
      securityPosture,
      threatIntelligence,
      lastUpdated: securityInsightsCache.lastUpdated
    };
  } catch (error) {
    console.error('Error fetching security insights:', error);
    
    // If there's an error, use cached data if available
    if (securityInsightsCache.securityPosture && securityInsightsCache.threatIntelligence) {
      console.log('Using cached security insights data');
      return {
        securityPosture: securityInsightsCache.securityPosture,
        threatIntelligence: securityInsightsCache.threatIntelligence,
        lastUpdated: securityInsightsCache.lastUpdated,
        fromCache: true
      };
    }
    
    // If no cached data, use demo data
    console.log('No cached data available, using demo data');
    return getDemoSecurityInsights();
  }
};

/**
 * Create initial security insights data for a new user
 * @param {string} userId - The user ID
 * @returns {Promise} Promise that resolves with the created security insights data
 */
const createInitialSecurityInsights = async (userId) => {
  try {
    console.log('Creating initial security insights data...');
    
    // Get threat data from the real threat data service
    const threatData = await RealThreatDataService.fetchThreatData();
    
    // Create initial security posture data
    const initialSecurityPosture = {
      user_id: userId || 'demo',
      overall_score: 65,
      previous_score: 60,
      score_change: 5,
      strengths: [
        { name: 'Web Security', score: 75, description: 'Good understanding of web vulnerabilities' },
        { name: 'Network Security', score: 70, description: 'Solid knowledge of network protocols' },
        { name: 'Security Awareness', score: 80, description: 'Strong security awareness practices' }
      ],
      weaknesses: [
        { name: 'Cloud Security', score: 45, description: 'Limited knowledge of cloud security concepts' },
        { name: 'Mobile Security', score: 55, description: 'Room for improvement in mobile security' },
        { name: 'IoT Security', score: 40, description: 'Limited exposure to IoT security principles' }
      ],
      recommendations: [
        { id: 'cloud-security', title: 'Cloud Security Fundamentals', type: 'module', link: '/dashboard/learning-paths/cloud-security' },
        { id: 'mobile-security', title: 'Mobile Security Assessment', type: 'assessment', link: '/dashboard/assessments/mobile-security' },
        { id: 'iot-security', title: 'IoT Security Workshop', type: 'event', link: '/dashboard/events/iot-security-workshop' }
      ],
      recent_activity: [
        { type: 'assessment', title: 'Completed Web Security Assessment', date: '2 days ago', score: 75 },
        { type: 'module', title: 'Completed Network Security Fundamentals', date: '5 days ago', score: 70 },
        { type: 'challenge', title: 'Solved XSS Challenge', date: '1 week ago', score: 80 }
      ],
      categories: [
        { name: 'Web Security', score: 75, trend: 'up' },
        { name: 'Network Security', score: 70, trend: 'up' },
        { name: 'Cloud Security', score: 45, trend: 'down' },
        { name: 'Mobile Security', score: 55, trend: 'neutral' },
        { name: 'IoT Security', score: 40, trend: 'down' },
        { name: 'Security Awareness', score: 80, trend: 'up' }
      ],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Create initial threat intelligence data
    const initialThreatIntelligence = {
      global_threats: threatData.attacks.slice(0, 5).map((attack, index) => ({
        id: attack.id || `threat-${index + 1}`,
        name: attack.details.name || attack.type.replace('_', ' ').charAt(0).toUpperCase() + attack.type.replace('_', ' ').slice(1),
        severity: attack.severity,
        region: attack.details.countryCode || 'Global',
        trend: ['increasing', 'stable', 'decreasing'][Math.floor(Math.random() * 3)]
      })),
      vulnerabilities: threatData.vulnerabilities.slice(0, 5).map((vuln, index) => {
        const cve = vuln.cve;
        return {
          id: cve.id || `vuln-${index + 1}`,
          name: cve.id || `CVE-${new Date().getFullYear()}-${Math.floor(Math.random() * 10000)}`,
          severity: (() => {
            const score = cve.metrics?.cvssMetricV31?.[0]?.cvssData?.baseScore;
            if (!score) return 'medium';
            if (score >= 9.0) return 'critical';
            if (score >= 7.0) return 'high';
            if (score >= 4.0) return 'medium';
            return 'low';
          })(),
          affected: cve.descriptions?.[0]?.value?.split(' ')[0] + ' Systems' || 'Multiple Systems',
          status: Math.random() > 0.5 ? 'active' : 'patched'
        };
      }),
      threat_map: {
        regions: [
          { id: 'na', name: 'North America', threatLevel: 75 },
          { id: 'sa', name: 'South America', threatLevel: 60 },
          { id: 'eu', name: 'Europe', threatLevel: 70 },
          { id: 'as', name: 'Asia', threatLevel: 80 },
          { id: 'af', name: 'Africa', threatLevel: 65 },
          { id: 'oc', name: 'Oceania', threatLevel: 55 }
        ]
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Insert the data into the database
    const { data: securityPosture, error: securityPostureError } = await supabase
      .from('security_posture')
      .insert(initialSecurityPosture)
      .select()
      .single();
      
    if (securityPostureError) {
      console.error('Error creating security posture:', securityPostureError);
      throw securityPostureError;
    }
    
    const { data: threatIntelligence, error: threatIntelligenceError } = await supabase
      .from('threat_intelligence')
      .insert(initialThreatIntelligence)
      .select()
      .single();
      
    if (threatIntelligenceError) {
      console.error('Error creating threat intelligence:', threatIntelligenceError);
      throw threatIntelligenceError;
    }
    
    // Update the cache
    securityInsightsCache.securityPosture = securityPosture;
    securityInsightsCache.threatIntelligence = threatIntelligence;
    securityInsightsCache.lastUpdated = new Date().toISOString();
    
    console.log('Initial security insights data created successfully');
    
    return {
      securityPosture,
      threatIntelligence,
      lastUpdated: securityInsightsCache.lastUpdated
    };
  } catch (error) {
    console.error('Error creating initial security insights:', error);
    
    // If there's an error, use demo data
    console.log('Using demo security insights data');
    return getDemoSecurityInsights();
  }
};

/**
 * Get demo security insights data
 * @returns {Object} Demo security insights data
 */
const getDemoSecurityInsights = () => {
  // Create demo security posture data
  const demoSecurityPosture = {
    user_id: 'demo',
    overall_score: 72,
    previous_score: 65,
    score_change: 7,
    strengths: [
      { name: 'Web Security', score: 85, description: 'Strong understanding of web vulnerabilities and protections' },
      { name: 'Network Security', score: 78, description: 'Good knowledge of network security principles' },
      { name: 'Security Awareness', score: 90, description: 'Excellent security awareness practices' }
    ],
    weaknesses: [
      { name: 'Cloud Security', score: 45, description: 'Limited knowledge of cloud security concepts' },
      { name: 'Mobile Security', score: 55, description: 'Room for improvement in mobile security understanding' },
      { name: 'IoT Security', score: 40, description: 'Limited exposure to IoT security principles' }
    ],
    recommendations: [
      { id: 'cloud-security', title: 'Cloud Security Fundamentals', type: 'module', link: '/dashboard/learning-paths/cloud-security' },
      { id: 'mobile-security', title: 'Mobile Security Assessment', type: 'assessment', link: '/dashboard/assessments/mobile-security' },
      { id: 'iot-security', title: 'IoT Security Workshop', type: 'event', link: '/dashboard/events/iot-security-workshop' }
    ],
    recent_activity: [
      { type: 'assessment', title: 'Completed Web Security Assessment', date: '2 days ago', score: 85 },
      { type: 'module', title: 'Completed Network Security Fundamentals', date: '5 days ago', score: 78 },
      { type: 'challenge', title: 'Solved XSS Challenge', date: '1 week ago', score: 90 }
    ],
    categories: [
      { name: 'Web Security', score: 85, trend: 'up' },
      { name: 'Network Security', score: 78, trend: 'up' },
      { name: 'Cloud Security', score: 45, trend: 'down' },
      { name: 'Mobile Security', score: 55, trend: 'neutral' },
      { name: 'IoT Security', score: 40, trend: 'down' },
      { name: 'Security Awareness', score: 90, trend: 'up' }
    ],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  // Create demo threat intelligence data
  const demoThreatIntelligence = {
    global_threats: [
      { id: 'threat-1', name: 'Ransomware Campaign', severity: 'high', region: 'Global', trend: 'increasing' },
      { id: 'threat-2', name: 'DDoS Attacks', severity: 'medium', region: 'Asia', trend: 'stable' },
      { id: 'threat-3', name: 'Phishing Campaign', severity: 'high', region: 'North America', trend: 'increasing' },
      { id: 'threat-4', name: 'Supply Chain Attacks', severity: 'critical', region: 'Global', trend: 'increasing' },
      { id: 'threat-5', name: 'Zero-day Exploits', severity: 'critical', region: 'Europe', trend: 'stable' }
    ],
    vulnerabilities: [
      { id: 'vuln-1', name: 'CVE-2023-1234', severity: 'critical', affected: 'Windows Systems', status: 'active' },
      { id: 'vuln-2', name: 'CVE-2023-5678', severity: 'high', affected: 'Apache Servers', status: 'patched' },
      { id: 'vuln-3', name: 'CVE-2023-9012', severity: 'medium', affected: 'Chrome Browser', status: 'active' },
      { id: 'vuln-4', name: 'CVE-2023-3456', severity: 'critical', affected: 'Linux Kernel', status: 'active' },
      { id: 'vuln-5', name: 'CVE-2023-7890', severity: 'high', affected: 'iOS Devices', status: 'patched' }
    ],
    threat_map: {
      regions: [
        { id: 'na', name: 'North America', threatLevel: 75 },
        { id: 'sa', name: 'South America', threatLevel: 60 },
        { id: 'eu', name: 'Europe', threatLevel: 70 },
        { id: 'as', name: 'Asia', threatLevel: 80 },
        { id: 'af', name: 'Africa', threatLevel: 65 },
        { id: 'oc', name: 'Oceania', threatLevel: 55 }
      ]
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  return {
    securityPosture: demoSecurityPosture,
    threatIntelligence: demoThreatIntelligence,
    lastUpdated: new Date().toISOString(),
    isDemo: true
  };
};

// Export the service
export default {
  initialize: initializeSecurityInsights,
  fetchSecurityInsights,
  getSecurityPosture: () => securityInsightsCache.securityPosture,
  getThreatIntelligence: () => securityInsightsCache.threatIntelligence,
  getLastUpdated: () => securityInsightsCache.lastUpdated
};
