/**
 * AI Recommendation Service
 * 
 * This service provides AI-driven recommendations for learning paths,
 * challenges, and personalized assessments.
 */

import { supabase } from '../lib/supabase';

/**
 * Get personalized learning path recommendations based on user profile and assessment
 * 
 * @param {Object} userProfile - User profile data
 * @param {Object} assessmentResults - Results from skill assessment
 * @returns {Promise<Array>} - Recommended learning paths
 */
export const getPersonalizedLearningPaths = async (userProfile, assessmentResults) => {
  try {
    // First check localStorage for cached recommendations
    const cachedRecommendations = localStorage.getItem(`learningPathRecommendations_${userProfile?.id}`);
    if (cachedRecommendations) {
      const { recommendations, timestamp } = JSON.parse(cachedRecommendations);
      // Use cached recommendations if less than 24 hours old
      if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
        return recommendations;
      }
    }
    
    // If no valid cached recommendations, generate new ones
    
    // Get all learning paths
    const { data: learningPaths, error } = await supabase
      .from('learning_paths')
      .select('*')
      .eq('is_active', true);
      
    if (error) throw error;
    
    // If no assessment results, return default recommendations
    if (!assessmentResults) {
      // Sort by beginner-friendliness and popularity
      const defaultRecommendations = learningPaths
        .sort((a, b) => {
          // Prioritize beginner paths
          if (a.difficulty === 'beginner' && b.difficulty !== 'beginner') return -1;
          if (a.difficulty !== 'beginner' && b.difficulty === 'beginner') return 1;
          
          // Then by popularity
          return b.enrollment_count - a.enrollment_count;
        })
        .slice(0, 3);
      
      // Cache recommendations
      localStorage.setItem(`learningPathRecommendations_${userProfile?.id}`, JSON.stringify({
        recommendations: defaultRecommendations,
        timestamp: Date.now()
      }));
      
      return defaultRecommendations;
    }
    
    // With assessment results, provide personalized recommendations
    
    // Calculate path scores based on assessment results
    const scoredPaths = learningPaths.map(path => {
      let score = 0;
      
      // Match user skill levels with path requirements
      Object.entries(assessmentResults.skills).forEach(([skill, level]) => {
        // If path focuses on a skill the user is weak in, increase score
        if (level < 3 && path.skills_covered?.includes(skill)) {
          score += (3 - level) * 10;
        }
        
        // If path requires skills the user is strong in, slightly increase score
        if (level >= 3 && path.prerequisites?.includes(skill)) {
          score += 5;
        }
      });
      
      // Adjust score based on difficulty match
      const userLevel = assessmentResults.overallLevel || 'beginner';
      if (path.difficulty === userLevel) {
        score += 20;
      } else if (
        (userLevel === 'beginner' && path.difficulty === 'intermediate') ||
        (userLevel === 'intermediate' && ['beginner', 'advanced'].includes(path.difficulty)) ||
        (userLevel === 'advanced' && path.difficulty === 'intermediate')
      ) {
        score += 10;
      }
      
      return { ...path, score };
    });
    
    // Sort by score and return top recommendations
    const personalizedRecommendations = scoredPaths
      .sort((a, b) => b.score - a.score)
      .slice(0, 3);
    
    // Cache recommendations
    localStorage.setItem(`learningPathRecommendations_${userProfile?.id}`, JSON.stringify({
      recommendations: personalizedRecommendations,
      timestamp: Date.now()
    }));
    
    return personalizedRecommendations;
  } catch (error) {
    console.error('Error getting personalized learning paths:', error);
    return [];
  }
};

/**
 * Get skill assessment questions based on user's experience level
 * 
 * @param {string} experienceLevel - User's self-reported experience level
 * @returns {Promise<Array>} - Assessment questions
 */
export const getSkillAssessmentQuestions = async (experienceLevel = 'beginner') => {
  try {
    // Define question categories based on cybersecurity domains
    const categories = [
      'networking',
      'operating_systems',
      'web_security',
      'cryptography',
      'security_fundamentals'
    ];
    
    // Get questions from each category appropriate for the experience level
    const questions = [];
    
    for (const category of categories) {
      // In a real implementation, this would fetch from a database
      // For now, we'll use mock questions stored in localStorage
      
      const mockQuestions = getMockQuestionsForCategory(category, experienceLevel);
      questions.push(...mockQuestions);
    }
    
    // Return a subset of questions to keep assessment reasonable length
    return questions.slice(0, 10);
  } catch (error) {
    console.error('Error getting assessment questions:', error);
    return [];
  }
};

/**
 * Evaluate assessment results and provide skill levels
 * 
 * @param {Array} answers - User's answers to assessment questions
 * @returns {Object} - Skill levels and recommendations
 */
export const evaluateAssessment = (answers) => {
  try {
    // Calculate skill levels based on answers
    const skills = {
      networking: 0,
      operating_systems: 0,
      web_security: 0,
      cryptography: 0,
      security_fundamentals: 0
    };
    
    let totalScore = 0;
    let questionCount = 0;
    
    // Process each answer
    answers.forEach(answer => {
      if (answer.isCorrect) {
        // Increment the specific skill score
        skills[answer.category] += 1;
        totalScore += 1;
      }
      
      // Track questions per category
      if (answer.category in skills) {
        questionCount += 1;
      }
    });
    
    // Normalize skill scores to 0-5 scale
    Object.keys(skills).forEach(skill => {
      const questionsInCategory = answers.filter(a => a.category === skill).length;
      if (questionsInCategory > 0) {
        skills[skill] = Math.round((skills[skill] / questionsInCategory) * 5);
      }
    });
    
    // Calculate overall level
    const overallScore = totalScore / questionCount;
    let overallLevel = 'beginner';
    
    if (overallScore >= 0.8) {
      overallLevel = 'advanced';
    } else if (overallScore >= 0.5) {
      overallLevel = 'intermediate';
    }
    
    // Generate recommendations based on skill levels
    const weakestSkills = Object.entries(skills)
      .sort(([, a], [, b]) => a - b)
      .slice(0, 2)
      .map(([skill]) => skill);
    
    return {
      skills,
      overallLevel,
      weakestSkills,
      overallScore: Math.round(overallScore * 100)
    };
  } catch (error) {
    console.error('Error evaluating assessment:', error);
    return {
      skills: {},
      overallLevel: 'beginner',
      weakestSkills: [],
      overallScore: 0
    };
  }
};

/**
 * Get mock questions for a specific category and experience level
 * 
 * @param {string} category - Question category
 * @param {string} experienceLevel - User experience level
 * @returns {Array} - Mock questions
 */
const getMockQuestionsForCategory = (category, experienceLevel) => {
  // In a real implementation, these would come from a database
  const mockQuestions = {
    networking: {
      beginner: [
        {
          id: 'net_b_1',
          question: 'What does IP stand for in IP address?',
          options: [
            'Internet Protocol',
            'Internet Provider',
            'Identity Protection',
            'Internal Processing'
          ],
          correctAnswer: 'Internet Protocol',
          category: 'networking'
        },
        {
          id: 'net_b_2',
          question: 'Which of the following is a private IP address?',
          options: [
            '*******',
            '***********',
            '*************',
            '*************'
          ],
          correctAnswer: '***********',
          category: 'networking'
        }
      ],
      intermediate: [
        {
          id: 'net_i_1',
          question: 'Which protocol operates at the Transport layer of the OSI model?',
          options: [
            'HTTP',
            'IP',
            'TCP',
            'Ethernet'
          ],
          correctAnswer: 'TCP',
          category: 'networking'
        }
      ]
    },
    // Add more categories as needed
  };
  
  // Return questions for the specified category and level, or empty array if not found
  return (mockQuestions[category]?.[experienceLevel] || []);
};

export default {
  getPersonalizedLearningPaths,
  getSkillAssessmentQuestions,
  evaluateAssessment
};
