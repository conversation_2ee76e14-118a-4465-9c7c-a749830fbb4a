import { supabase } from '../lib/supabase';
import toast from 'react-hot-toast';

// Enhanced Enrollment Service for Learning Paths and Challenges
export class EnrollmentService {
  // Enroll user in content (learning path or challenge)
  static async enrollUser(userId, contentType, contentId, contentTitle) {
    try {
      // Check if already enrolled
      const { data: existingEnrollment } = await supabase
        .from('user_enrollments')
        .select('*')
        .eq('user_id', userId)
        .eq('content_type', contentType)
        .eq('content_id', contentId)
        .single();

      if (existingEnrollment) {
        if (existingEnrollment.status === 'completed') {
          toast.info('You have already completed this content!');
          return { success: false, message: 'Already completed' };
        } else {
          toast.info('You are already enrolled in this content!');
          return { success: false, message: 'Already enrolled' };
        }
      }

      // Create enrollment using the database function
      const { data, error } = await supabase
        .rpc('enroll_user_in_content', {
          user_uuid: userId,
          content_type_param: contentType,
          content_id_param: contentId
        });

      if (error) {
        // Fallback: direct insertion if function doesn't exist
        const { data: enrollment, error: insertError } = await supabase
          .from('user_enrollments')
          .insert({
            user_id: userId,
            content_type: contentType,
            content_id: contentId,
            status: 'enrolled',
            progress_percentage: 0
          })
          .select()
          .single();

        if (insertError) throw insertError;

        // Initialize user statistics if not exists
        await supabase
          .from('user_statistics')
          .insert({ user_id: userId })
          .on('conflict', 'user_id')
          .ignore();
      }

      // Create notification for successful enrollment
      await this.createEnrollmentNotification(userId, contentType, contentTitle);

      toast.success(`Successfully enrolled in ${contentTitle}!`);
      return { success: true, enrollmentId: data };

    } catch (error) {
      console.error('Error enrolling user:', error);
      toast.error('Failed to enroll. Please try again.');
      return { success: false, error: error.message };
    }
  }

  // Legacy method for backward compatibility
  static async enrollInLearningPath(userId, learningPathId) {
    return this.enrollUser(userId, 'learning_path', learningPathId, 'Learning Path');
  }

  // Get user's enrollments
  static async getUserEnrollments(userId) {
    try {
      const { data, error } = await supabase
        .from('user_enrollments')
        .select('*')
        .eq('user_id', userId)
        .order('enrolled_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting user enrollments:', error);
      return [];
    }
  }

  // Get enrollment status for specific content
  static async getEnrollmentStatus(userId, contentType, contentId) {
    try {
      const { data, error } = await supabase
        .from('user_enrollments')
        .select('*')
        .eq('user_id', userId)
        .eq('content_type', contentType)
        .eq('content_id', contentId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data || null;
    } catch (error) {
      console.error('Error checking enrollment status:', error);
      return null;
    }
  }

  // Update progress
  static async updateProgress(userId, contentType, contentId, moduleId = null, progressData = {}, completed = false, score = null, timeSpent = 0) {
    try {
      const { error } = await supabase
        .rpc('update_user_progress', {
          user_uuid: userId,
          content_type_param: contentType,
          content_id_param: contentId,
          module_id_param: moduleId,
          progress_data_param: progressData,
          completed_param: completed,
          score_param: score,
          time_spent_param: timeSpent
        });

      if (error) {
        // Fallback: direct insertion if function doesn't exist
        await supabase
          .from('user_progress')
          .upsert({
            user_id: userId,
            content_type: contentType,
            content_id: contentId,
            module_id: moduleId,
            progress_data: progressData,
            completed: completed,
            score: score,
            time_spent: timeSpent,
            last_accessed: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        // Update enrollment progress
        const progressPercentage = completed ? 100 : Math.min(90, 10);
        await supabase
          .from('user_enrollments')
          .update({
            progress_percentage: progressPercentage,
            status: completed ? 'completed' : 'in_progress',
            started_at: new Date().toISOString(),
            completed_at: completed ? new Date().toISOString() : null
          })
          .eq('user_id', userId)
          .eq('content_type', contentType)
          .eq('content_id', contentId);
      }

      // If completed, award achievement
      if (completed) {
        await this.awardCompletionAchievement(userId, contentType, contentId);
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating progress:', error);
      return { success: false, error: error.message };
    }
  }

  // Update enrollment progress
  static async updateEnrollmentProgress(userId, learningPathId, progressPercentage, completed = false) {
    try {
      const updates = {
        progress_percentage: progressPercentage,
        completed,
        updated_at: new Date().toISOString()
      };

      if (completed) {
        updates.completed_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from('user_enrollments')
        .update(updates)
        .eq('user_id', userId)
        .eq('learning_path_id', learningPathId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating enrollment progress:', error);
      throw error;
    }
  }

  // Calculate learning path progress based on completed modules
  static async calculateLearningPathProgress(userId, learningPathId) {
    try {
      // Get all modules in the learning path
      const { data: pathModules, error: modulesError } = await supabase
        .from('learning_modules')
        .select('id')
        .eq('learning_path_id', learningPathId)
        .order('display_order', { ascending: true });

      if (modulesError) throw modulesError;

      if (!pathModules || pathModules.length === 0) {
        return { progressPercentage: 0, completedModules: 0, totalModules: 0 };
      }

      // Get user's progress for these modules
      const moduleIds = pathModules.map(m => m.id);
      const { data: userProgress, error: progressError } = await supabase
        .from('user_module_progress')
        .select('module_id, completed')
        .eq('user_id', userId)
        .in('module_id', moduleIds);

      if (progressError) throw progressError;

      const completedModules = userProgress ? userProgress.filter(p => p.completed).length : 0;
      const totalModules = pathModules.length;
      const progressPercentage = totalModules > 0 ? Math.round((completedModules / totalModules) * 100) : 0;

      return {
        progressPercentage,
        completedModules,
        totalModules,
        isCompleted: progressPercentage === 100
      };
    } catch (error) {
      console.error('Error calculating learning path progress:', error);
      return { progressPercentage: 0, completedModules: 0, totalModules: 0 };
    }
  }

  // Get next module in learning path
  static async getNextModule(userId, learningPathId) {
    try {
      // Get all modules in the learning path
      const { data: pathModules, error: modulesError } = await supabase
        .from('learning_modules')
        .select('id, title, display_order')
        .eq('learning_path_id', learningPathId)
        .order('display_order', { ascending: true });

      if (modulesError) throw modulesError;

      if (!pathModules || pathModules.length === 0) {
        return null;
      }

      // Get user's progress for these modules
      const moduleIds = pathModules.map(m => m.id);
      const { data: userProgress, error: progressError } = await supabase
        .from('user_module_progress')
        .select('module_id, completed')
        .eq('user_id', userId)
        .in('module_id', moduleIds);

      if (progressError) throw progressError;

      // Find the first incomplete module
      const completedModuleIds = userProgress ? userProgress.filter(p => p.completed).map(p => p.module_id) : [];
      
      for (const module of pathModules) {
        if (!completedModuleIds.includes(module.id)) {
          return module;
        }
      }

      // All modules completed
      return null;
    } catch (error) {
      console.error('Error getting next module:', error);
      return null;
    }
  }

  // Check if user is enrolled in a learning path
  static async isUserEnrolled(userId, learningPathId) {
    try {
      const { data, error } = await supabase
        .from('user_enrollments')
        .select('id')
        .eq('user_id', userId)
        .eq('learning_path_id', learningPathId)
        .single();

      return !error && data;
    } catch (error) {
      return false;
    }
  }

  // Get enrollment statistics
  static async getEnrollmentStats(userId) {
    try {
      const { data, error } = await supabase
        .from('user_enrollments')
        .select('progress_percentage, completed')
        .eq('user_id', userId);

      if (error) throw error;

      const totalEnrollments = data.length;
      const completedPaths = data.filter(e => e.completed).length;
      const inProgressPaths = data.filter(e => !e.completed && e.progress_percentage > 0).length;
      const averageProgress = totalEnrollments > 0 
        ? Math.round(data.reduce((sum, e) => sum + e.progress_percentage, 0) / totalEnrollments)
        : 0;

      return {
        totalEnrollments,
        completedPaths,
        inProgressPaths,
        averageProgress
      };
    } catch (error) {
      console.error('Error getting enrollment stats:', error);
      return {
        totalEnrollments: 0,
        completedPaths: 0,
        inProgressPaths: 0,
        averageProgress: 0
      };
    }
  }

  // Unenroll from learning path
  static async unenrollFromLearningPath(userId, learningPathId) {
    try {
      const { error } = await supabase
        .from('user_enrollments')
        .delete()
        .eq('user_id', userId)
        .eq('learning_path_id', learningPathId);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error unenrolling from learning path:', error);
      throw error;
    }
  }

  // Get recommended learning paths based on user progress and preferences
  static async getRecommendedPaths(userId, limit = 5) {
    try {
      // Get user's current enrollments
      const enrollments = await this.getUserEnrollments(userId);
      const enrolledPathIds = enrollments.map(e => e.learning_path_id);

      // Get user's completed modules to understand their interests
      const { data: userProgress, error: progressError } = await supabase
        .from('user_module_progress')
        .select(`
          completed,
          learning_modules!inner (
            learning_path_id,
            learning_paths!inner (
              category,
              difficulty_level
            )
          )
        `)
        .eq('user_id', userId)
        .eq('completed', true);

      if (progressError) throw progressError;

      // Analyze user preferences
      const categories = {};
      const difficulties = {};

      if (userProgress) {
        userProgress.forEach(progress => {
          const category = progress.learning_modules.learning_paths.category;
          const difficulty = progress.learning_modules.learning_paths.difficulty_level;
          
          categories[category] = (categories[category] || 0) + 1;
          difficulties[difficulty] = (difficulties[difficulty] || 0) + 1;
        });
      }

      // Get recommended paths
      let query = supabase
        .from('learning_paths')
        .select('*')
        .limit(limit);

      // Exclude already enrolled paths
      if (enrolledPathIds.length > 0) {
        query = query.not('id', 'in', `(${enrolledPathIds.join(',')})`);
      }

      const { data: recommendedPaths, error: pathsError } = await query;

      if (pathsError) throw pathsError;

      // Score and sort recommendations
      const scoredPaths = recommendedPaths.map(path => {
        let score = 0;
        
        // Boost score based on user's preferred categories
        if (categories[path.category]) {
          score += categories[path.category] * 10;
        }
        
        // Boost score based on user's preferred difficulties
        if (difficulties[path.difficulty_level]) {
          score += difficulties[path.difficulty_level] * 5;
        }
        
        // Add some randomness to avoid always showing the same paths
        score += Math.random() * 10;
        
        return { ...path, recommendationScore: score };
      });

      return scoredPaths.sort((a, b) => b.recommendationScore - a.recommendationScore);
    } catch (error) {
      console.error('Error getting recommended paths:', error);
      return [];
    }
  }

  // Award completion achievement
  static async awardCompletionAchievement(userId, contentType, contentId) {
    try {
      const achievementTitle = contentType === 'learning_path'
        ? 'Learning Path Completed!'
        : 'Challenge Completed!';

      const achievementDescription = contentType === 'learning_path'
        ? `Completed the ${contentId} learning path`
        : `Completed the ${contentId} challenge`;

      const { error } = await supabase
        .rpc('award_achievement', {
          user_uuid: userId,
          achievement_type_param: 'completion',
          achievement_id_param: `${contentType}_${contentId}`,
          title_param: achievementTitle,
          description_param: achievementDescription,
          icon_param: contentType === 'learning_path' ? 'FaGraduationCap' : 'FaTrophy',
          points_param: contentType === 'learning_path' ? 200 : 100
        });

      if (error) {
        // Fallback: direct insertion
        await supabase
          .from('user_achievements')
          .insert({
            user_id: userId,
            achievement_type: 'completion',
            achievement_id: `${contentType}_${contentId}`,
            title: achievementTitle,
            description: achievementDescription,
            icon: contentType === 'learning_path' ? 'FaGraduationCap' : 'FaTrophy',
            points: contentType === 'learning_path' ? 200 : 100
          });
      }

      // Create achievement notification
      await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          title: achievementTitle,
          message: achievementDescription,
          type: 'achievement',
          emoji: contentType === 'learning_path' ? '🎓' : '🏆',
          icon_name: contentType === 'learning_path' ? 'FaGraduationCap' : 'FaTrophy',
          icon_color: 'gold',
          category: 'achievement',
          priority: 1
        });

    } catch (error) {
      console.error('Error awarding achievement:', error);
    }
  }

  // Create enrollment notification
  static async createEnrollmentNotification(userId, contentType, contentTitle) {
    try {
      const title = contentType === 'learning_path'
        ? 'Learning Path Started! 📚'
        : 'Challenge Started! 🚀';

      const message = `You have enrolled in ${contentTitle}. Good luck on your learning journey!`;

      await supabase
        .from('user_notifications')
        .insert({
          user_id: userId,
          title,
          message,
          type: 'info',
          emoji: contentType === 'learning_path' ? '📚' : '🚀',
          icon_name: contentType === 'learning_path' ? 'FaGraduationCap' : 'FaCode',
          icon_color: 'blue',
          category: 'learning',
          priority: 2
        });

    } catch (error) {
      console.error('Error creating enrollment notification:', error);
    }
  }

  // Get user dashboard data
  static async getUserDashboardData(userId) {
    try {
      const { data, error } = await supabase
        .rpc('get_user_dashboard_data', { user_uuid: userId });

      if (error) throw error;
      return data || {
        statistics: {},
        enrollments: [],
        achievements: [],
        recent_progress: []
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Fallback: get data manually
      return await this.getFallbackDashboardData(userId);
    }
  }

  // Fallback dashboard data if function doesn't exist
  static async getFallbackDashboardData(userId) {
    try {
      const [enrollments, achievements, progress, statistics] = await Promise.all([
        this.getUserEnrollments(userId),
        this.getUserAchievements(userId),
        this.getRecentProgress(userId),
        this.getUserStatistics(userId)
      ]);

      return {
        statistics,
        enrollments: enrollments.slice(0, 10),
        achievements: achievements.slice(0, 5),
        recent_progress: progress.slice(0, 10)
      };
    } catch (error) {
      console.error('Error fetching fallback dashboard data:', error);
      return {
        statistics: {},
        enrollments: [],
        achievements: [],
        recent_progress: []
      };
    }
  }

  // Get user achievements
  static async getUserAchievements(userId) {
    try {
      const { data, error } = await supabase
        .from('user_achievements')
        .select('*')
        .eq('user_id', userId)
        .order('earned_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching achievements:', error);
      return [];
    }
  }

  // Get recent progress
  static async getRecentProgress(userId) {
    try {
      const { data, error } = await supabase
        .from('user_progress')
        .select('*')
        .eq('user_id', userId)
        .order('last_accessed', { ascending: false })
        .limit(10);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching recent progress:', error);
      return [];
    }
  }

  // Get user statistics
  static async getUserStatistics(userId) {
    try {
      const { data, error } = await supabase
        .from('user_statistics')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data || {
        total_challenges_completed: 0,
        total_learning_paths_completed: 0,
        total_modules_completed: 0,
        total_points: 0,
        current_streak: 0,
        longest_streak: 0,
        total_time_spent: 0
      };
    } catch (error) {
      console.error('Error fetching statistics:', error);
      return {
        total_challenges_completed: 0,
        total_learning_paths_completed: 0,
        total_modules_completed: 0,
        total_points: 0,
        current_streak: 0,
        longest_streak: 0,
        total_time_spent: 0
      };
    }
  }

  // Check if user can access premium content
  static async canAccessPremiumContent(userId) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('subscription_tier')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data?.subscription_tier === 'premium';
    } catch (error) {
      console.error('Error checking premium access:', error);
      return false;
    }
  }
}

export default EnrollmentService;
