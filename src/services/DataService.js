import { supabase } from '../lib/supabase';

/**
 * DataService
 *
 * A service for handling data operations related to challenges, user progress,
 * and other application data.
 */

/**
 * Get a challenge by its slug or ID
 * @param {string} slugOrId - The challenge slug or ID
 * @returns {Promise<Object>} - The challenge data
 */
export const getChallengeBySlug = async (slugOrId) => {
  try {
    // Check if we have mock data for development
    if (import.meta.env.DEV) {
      // Import mock challenges data
      const mockChallenges = [
        {
          id: 'web-1',
          slug: 'sql-injection-basics',
          title: 'SQL Injection Basics',
          description: 'Learn the fundamentals of SQL injection by exploiting a vulnerable login form. This challenge introduces you to one of the most common web application vulnerabilities.',
          category: { name: 'Web Security' },
          difficulty: { name: 'Begin<PERSON>' },
          type: { name: 'Exploitation' },
          points: 100,
          coin_reward: 10,
          estimated_time: 20,
          content: {
            description: 'SQL injection is a code injection technique that exploits a security vulnerability occurring in the database layer of an application.',
            scenario: 'You are a security researcher tasked with testing a company\'s login page for vulnerabilities. Your goal is to bypass the authentication mechanism using SQL injection techniques.',
            objectives: [
              'Understand how SQL injection works',
              'Identify vulnerable input fields',
              'Exploit SQL injection to bypass authentication',
              'Extract database information'
            ],
            flag_format: 'flag{sql_injection_master_2023}',
            hints: [
              { hint: 'Try using single quotes in the input fields to test for SQL injection vulnerabilities.', coin_cost: 5 },
              { hint: 'The classic OR 1=1 technique might be useful here.', coin_cost: 10 }
            ],
            challenge_data: {
              'Target URL': 'https://example.com/login',
              'Database Type': 'MySQL'
            }
          }
        },
        {
          id: 'network-1',
          slug: 'packet-analysis',
          title: 'Network Traffic Analysis',
          description: 'Analyze network traffic to identify suspicious activities and potential security threats.',
          category: { name: 'Network Security' },
          difficulty: { name: 'Intermediate' },
          type: { name: 'Analysis' },
          points: 150,
          coin_reward: 15,
          estimated_time: 30,
          content: {
            description: 'Network traffic analysis is the process of recording, reviewing and analyzing network traffic for the purpose of performance, security and/or general network operations and management.',
            scenario: 'You are a security analyst at a large corporation. The network team has detected unusual traffic patterns and has provided you with a packet capture file to analyze.',
            objectives: [
              'Analyze network traffic patterns',
              'Identify suspicious connections',
              'Detect potential data exfiltration',
              'Determine the source and destination of malicious traffic'
            ],
            flag_format: 'flag{network_traffic_analyst_pro}',
            hints: [
              { hint: 'Look for unusual DNS queries that might indicate command and control traffic.', coin_cost: 5 },
              { hint: 'Check for large data transfers to external IP addresses.', coin_cost: 10 }
            ],
            challenge_data: {
              'Capture File Size': '24.5 MB',
              'Time Period': '15 minutes',
              'Protocol Distribution': 'TCP (65%), UDP (30%), ICMP (5%)'
            }
          }
        },
        {
          id: 'crypto-1',
          slug: 'cryptographic-attacks',
          title: 'Password Cracking Challenge',
          description: 'Crack various password hashes using different techniques and tools.',
          category: { name: 'Cryptography' },
          difficulty: { name: 'Intermediate' },
          type: { name: 'Exploitation' },
          points: 200,
          coin_reward: 20,
          estimated_time: 40,
          content: {
            description: 'Password cracking is the process of recovering passwords from data that has been stored in or transmitted by a computer system.',
            scenario: 'You have obtained a file containing password hashes from a security breach. Your task is to crack these hashes to reveal the original passwords.',
            objectives: [
              'Identify different types of password hashes',
              'Use appropriate techniques to crack each hash type',
              'Understand the weaknesses in different hashing algorithms',
              'Successfully recover all passwords'
            ],
            flag_format: 'flag{password_master_cracker}',
            hints: [
              { hint: 'The first hash is MD5, which is vulnerable to rainbow table attacks.', coin_cost: 5 },
              { hint: 'For bcrypt hashes, a dictionary attack with common passwords might be more efficient than brute force.', coin_cost: 10 }
            ],
            challenge_data: {
              'Hash Types': 'MD5, SHA-1, bcrypt',
              'Number of Hashes': '3'
            }
          }
        },
        {
          id: 'forensics-1',
          slug: 'memory-forensics',
          title: 'Binary Analysis Challenge',
          description: 'Analyze binary files to identify vulnerabilities and extract hidden information.',
          category: { name: 'Digital Forensics' },
          difficulty: { name: 'Advanced' },
          type: { name: 'Analysis' },
          points: 250,
          coin_reward: 25,
          estimated_time: 50,
          content: {
            description: 'Binary analysis is the process of analyzing compiled binary files to understand their behavior, identify vulnerabilities, and extract information.',
            scenario: 'You are a security researcher analyzing a suspicious binary file. Your task is to identify potential vulnerabilities in the code that could be exploited by attackers.',
            objectives: [
              'Perform static analysis of the binary file',
              'Identify buffer overflow vulnerabilities',
              'Detect format string vulnerabilities',
              'Find use-after-free memory corruption issues'
            ],
            flag_format: 'flag{binary_analysis_master}',
            hints: [
              { hint: 'Look for memory operations without proper bounds checking.', coin_cost: 5 },
              { hint: 'Check for printf-like functions with user-controlled format strings.', coin_cost: 10 }
            ],
            challenge_data: {
              'File Type': 'Windows PE Executable',
              'Architecture': 'x86-64',
              'Size': '24.5 KB'
            }
          }
        }
      ];

      // Find the challenge by ID or slug
      const mockChallenge = mockChallenges.find(
        challenge => challenge.id === slugOrId || challenge.slug === slugOrId
      );

      if (mockChallenge) {
        return mockChallenge;
      }
    }

    // First try to get by slug
    let { data, error } = await supabase
      .from('challenges')
      .select(`
        *,
        category:challenge_categories(*),
        difficulty:challenge_difficulty_levels(*),
        content:challenge_content(*)
      `)
      .eq('slug', slugOrId)
      .single();

    // If not found by slug, try by ID
    if (error && error.code === 'PGRST116') {
      ({ data, error } = await supabase
        .from('challenges')
        .select(`
          *,
          category:challenge_categories(*),
          difficulty:challenge_difficulty_levels(*),
          content:challenge_content(*)
        `)
        .eq('id', slugOrId)
        .single());
    }

    if (error) {
      console.error('Error fetching challenge:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getChallengeBySlug:', error);
    return null;
  }
};

/**
 * Get user progress data
 * @returns {Promise<Object>} - User progress data
 */
export const getUserProgress = async () => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) {
      return { challenges: {}, modules: {}, pathProgress: {} };
    }

    // Get challenge progress
    const { data: challengeProgress, error: challengeError } = await supabase
      .from('challenge_attempts')
      .select(`
        challenge_id,
        is_correct,
        points_earned,
        time_spent,
        attempted_at
      `)
      .eq('user_id', session.user.id);

    if (challengeError) {
      console.error('Error fetching challenge progress:', challengeError);
    }

    // Get challenge completions
    const { data: challengeCompletions, error: completionsError } = await supabase
      .from('challenge_completions')
      .select(`
        challenge_id,
        points_earned,
        completed_at
      `)
      .eq('user_id', session.user.id);

    if (completionsError) {
      console.error('Error fetching challenge completions:', completionsError);
    }

    // Process challenge data
    const challenges = {};

    // Process attempts
    if (challengeProgress) {
      challengeProgress.forEach(attempt => {
        if (!challenges[attempt.challenge_id]) {
          challenges[attempt.challenge_id] = {
            attempts: [],
            isCompleted: false
          };
        }

        challenges[attempt.challenge_id].attempts.push({
          isCorrect: attempt.is_correct,
          pointsEarned: attempt.points_earned,
          timeSpent: attempt.time_spent,
          attemptedAt: attempt.attempted_at
        });
      });
    }

    // Process completions
    if (challengeCompletions) {
      challengeCompletions.forEach(completion => {
        if (!challenges[completion.challenge_id]) {
          challenges[completion.challenge_id] = {
            attempts: [],
            isCompleted: true,
            completedAt: completion.completed_at,
            pointsEarned: completion.points_earned
          };
        } else {
          challenges[completion.challenge_id].isCompleted = true;
          challenges[completion.challenge_id].completedAt = completion.completed_at;
          challenges[completion.challenge_id].pointsEarned = completion.points_earned;
        }
      });
    }

    // Get module progress (simplified for now)
    const { data: moduleProgress, error: moduleError } = await supabase
      .from('module_progress')
      .select(`
        module_id,
        status,
        progress_percentage,
        last_accessed
      `)
      .eq('user_id', session.user.id);

    if (moduleError) {
      console.error('Error fetching module progress:', moduleError);
    }

    // Process module data
    const modules = {};

    if (moduleProgress) {
      moduleProgress.forEach(progress => {
        modules[progress.module_id] = {
          status: progress.status,
          progressPercentage: progress.progress_percentage,
          lastAccessed: progress.last_accessed
        };
      });
    }

    // Get learning path progress
    const { data: pathProgress, error: pathError } = await supabase
      .from('learning_path_progress')
      .select(`
        path_id,
        status,
        progress_percentage,
        last_accessed
      `)
      .eq('user_id', session.user.id);

    if (pathError) {
      console.error('Error fetching path progress:', pathError);
    }

    // Process path data
    const paths = {};

    if (pathProgress) {
      pathProgress.forEach(progress => {
        paths[progress.path_id] = {
          status: progress.status,
          progressPercentage: progress.progress_percentage,
          lastAccessed: progress.last_accessed
        };
      });
    }

    return {
      challenges,
      modules,
      pathProgress: paths
    };
  } catch (error) {
    console.error('Error in getUserProgress:', error);

    // Return empty data structure if there's an error
    return {
      challenges: {},
      modules: {},
      pathProgress: {}
    };
  }
};

/**
 * Submit a challenge solution
 * @param {string} challengeId - The challenge ID
 * @param {string} submission - The user's submission
 * @returns {Promise<Object>} - The result of the submission
 */
export const submitChallengeSolution = async (challengeId, submission) => {
  try {
    const { data: { session } } = await supabase.auth.getSession();

    // For development, allow submissions without authentication
    if (!session?.user && !import.meta.env.DEV) {
      throw new Error('Not authenticated');
    }

    // Get the challenge to check the correct solution
    const challenge = await getChallengeBySlug(challengeId);
    if (!challenge) {
      throw new Error('Challenge not found');
    }

    // In development mode, accept any submission that contains "flag{"
    let isCorrect = false;
    if (import.meta.env.DEV) {
      isCorrect = submission.trim().toLowerCase().includes('flag{');
      console.log('Development mode: Accepting flag submission:', submission);
    } else {
      // Check if the submission is correct
      isCorrect = challenge.content?.flag_format &&
                  submission.trim().toLowerCase() === challenge.content.flag_format.toLowerCase();
    }

    // Calculate time spent (if there's a previous attempt)
    let timeSpent = 60; // Default to 60 seconds

    if (import.meta.env.DEV) {
      // In development mode, use a random time between 30 seconds and 5 minutes
      timeSpent = Math.floor(Math.random() * 270) + 30;
      console.log('Development mode: Using simulated completion time:', timeSpent, 'seconds');
    } else {
      const { data: previousAttempts } = await supabase
        .from('challenge_attempts')
        .select('attempted_at')
        .eq('user_id', session.user.id)
        .eq('challenge_id', challengeId)
        .order('attempted_at', { ascending: true })
        .limit(1);

      const startTime = previousAttempts && previousAttempts.length > 0
        ? new Date(previousAttempts[0].attempted_at).getTime()
        : Date.now() - 60000; // Default to 1 minute if no previous attempts

      timeSpent = Math.floor((Date.now() - startTime) / 1000); // Time in seconds
    }

    // Record the attempt
    let attemptError = null;

    if (!import.meta.env.DEV) {
      // Only record in database in production
      const { data: attemptData, error } = await supabase
        .from('challenge_attempts')
        .insert({
          user_id: session.user.id,
          challenge_id: challengeId,
          submission,
          is_correct: isCorrect,
          points_earned: isCorrect ? challenge.points : 0,
          time_spent: timeSpent,
          attempted_at: new Date().toISOString()
        })
        .select()
        .single();

      attemptError = error;
    } else {
      // In development mode, store in localStorage
      const attempts = JSON.parse(localStorage.getItem('challengeAttempts') || '{}');
      const challengeAttempts = attempts[challengeId] || [];

      challengeAttempts.push({
        submission,
        isCorrect,
        pointsEarned: isCorrect ? challenge.points : 0,
        timeSpent,
        attemptedAt: new Date().toISOString()
      });

      attempts[challengeId] = challengeAttempts;
      localStorage.setItem('challengeAttempts', JSON.stringify(attempts));
      console.log('Development mode: Stored attempt in localStorage');
    }

    if (attemptError) {
      throw attemptError;
    }

    // If correct, record the completion if not already completed
    if (isCorrect) {
      if (import.meta.env.DEV) {
        // In development mode, store completion in localStorage
        const completions = JSON.parse(localStorage.getItem('challengeCompletions') || '{}');
        const now = new Date().toISOString();

        // Update challenge enrollments to mark as completed
        const enrollments = JSON.parse(localStorage.getItem('challengeEnrollments') || '{}');
        if (enrollments[challengeId]) {
          enrollments[challengeId].isCompleted = true;
          enrollments[challengeId].completedAt = now;
          enrollments[challengeId].pointsEarned = challenge.points;
          localStorage.setItem('challengeEnrollments', JSON.stringify(enrollments));
        }

        // Store completion data
        completions[challengeId] = {
          pointsEarned: challenge.points,
          completionTime: timeSpent,
          completedAt: now
        };

        localStorage.setItem('challengeCompletions', JSON.stringify(completions));
        console.log('Development mode: Stored completion in localStorage');

        // Simulate leaderboard update
        const mockLeaderboardEntry = {
          userId: 'dev-user',
          username: 'Developer',
          avatarUrl: null,
          totalScore: challenge.points,
          completionTime: timeSpent,
          approachScore: Math.floor(Math.random() * 30) + 70,
          createdAt: now
        };

        const leaderboard = JSON.parse(localStorage.getItem('leaderboard') || '[]');
        leaderboard.push(mockLeaderboardEntry);
        localStorage.setItem('leaderboard', JSON.stringify(leaderboard));
      } else {
        // In production, use the database
        const { data: existingCompletion } = await supabase
          .from('challenge_completions')
          .select('id')
          .eq('user_id', session.user.id)
          .eq('challenge_id', challengeId)
          .single();

        if (!existingCompletion) {
          const { error: completionError } = await supabase
            .from('challenge_completions')
            .insert({
              user_id: session.user.id,
              challenge_id: challengeId,
              points_earned: challenge.points,
              completion_time: timeSpent,
              completed_at: new Date().toISOString()
            });

          if (completionError) {
            console.error('Error recording completion:', completionError);
          }

          // Update leaderboard
          await updateLeaderboard(session.user.id, challengeId, challenge.points, timeSpent);
        }
      }
    }

    return {
      success: isCorrect,
      points: isCorrect ? challenge.points : 0,
      timeSpent,
      message: isCorrect ? 'Congratulations! You solved the challenge!' : 'Incorrect solution. Try again!'
    };
  } catch (error) {
    console.error('Error in submitChallengeSolution:', error);
    return {
      success: false,
      message: 'An error occurred while submitting your solution. Please try again.'
    };
  }
};

/**
 * Update the leaderboard with a new entry
 * @param {string} userId - The user ID
 * @param {string} challengeId - The challenge ID
 * @param {number} score - The score earned
 * @param {number} completionTime - The time taken to complete the challenge
 */
const updateLeaderboard = async (userId, challengeId, score, completionTime) => {
  try {
    // Get user info for the leaderboard
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('username, avatar_url')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error fetching user data for leaderboard:', userError);
      return;
    }

    // Insert leaderboard entry
    const { error: leaderboardError } = await supabase
      .from('leaderboards')
      .insert({
        user_id: userId,
        challenge_id: challengeId,
        total_score: score,
        completion_time: completionTime,
        approach_score: Math.floor(Math.random() * 30) + 70, // Random score between 70-100 for now
        created_at: new Date().toISOString()
      });

    if (leaderboardError) {
      console.error('Error updating leaderboard:', leaderboardError);
    }
  } catch (error) {
    console.error('Error in updateLeaderboard:', error);
  }
};

export default {
  getChallengeBySlug,
  getUserProgress,
  submitChallengeSolution
};
