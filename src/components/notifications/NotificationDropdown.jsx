import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  FaBell,
  FaTimes,
  FaCheck,
  FaExternalLinkAlt,
  FaTrash,
  FaPin,
  FaEye,
  FaCode,
  FaTrophy,
  FaGraduationCap,
  FaFire,
  FaStar,
  FaExclamationTriangle,
  FaRocket,
  FaShieldAlt
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import ProgressTrackingService from '../../services/ProgressTrackingService';

const NotificationDropdown = ({ isOpen, onClose, user }) => {
  const { darkMode } = useGlobalTheme();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // 'all', 'unread', 'read'

  useEffect(() => {
    if (isOpen && user) {
      fetchNotifications();
    }
  }, [isOpen, user, filter]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const includeRead = filter !== 'unread';
      const { data } = await ProgressTrackingService.getUserNotifications(user.id, includeRead);
      
      let filteredData = data || [];
      if (filter === 'read') {
        filteredData = filteredData.filter(n => n.is_read);
      } else if (filter === 'unread') {
        filteredData = filteredData.filter(n => !n.is_read);
      }
      
      setNotifications(filteredData);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationClick = async (notification) => {
    // Mark as read if not already read
    if (!notification.is_read) {
      await ProgressTrackingService.markNotificationAsRead(notification.id);
      setNotifications(prev => 
        prev.map(n => n.id === notification.id ? { ...n, is_read: true } : n)
      );
    }

    // Handle action
    if (notification.action_url) {
      if (notification.action_type === 'external') {
        window.open(notification.action_url, '_blank');
      } else {
        navigate(notification.action_url);
        onClose();
      }
    }
  };

  const handleMarkAsRead = async (notificationId, event) => {
    event.stopPropagation();
    await ProgressTrackingService.markNotificationAsRead(notificationId);
    setNotifications(prev => 
      prev.map(n => n.id === notificationId ? { ...n, is_read: true } : n)
    );
  };

  const handleDismiss = async (notificationId, event) => {
    event.stopPropagation();
    await ProgressTrackingService.dismissNotification(notificationId);
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const getIcon = (notification) => {
    const iconMap = {
      FaBell: FaBell,
      FaCode: FaCode,
      FaTrophy: FaTrophy,
      FaGraduationCap: FaGraduationCap,
      FaFire: FaFire,
      FaStar: FaStar,
      FaExclamationTriangle: FaExclamationTriangle,
      FaRocket: FaRocket,
      FaShieldAlt: FaShieldAlt
    };

    const IconComponent = iconMap[notification.icon_name] || FaBell;
    return <IconComponent className={`text-${notification.icon_color || 'blue'}-500`} />;
  };

  const getTypeColor = (type) => {
    const colorMap = {
      success: 'green',
      warning: 'yellow',
      error: 'red',
      info: 'blue',
      challenge: 'purple',
      achievement: 'yellow'
    };
    return colorMap[type] || 'blue';
  };

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50" onClick={onClose}>
      <div className="absolute top-16 right-4 w-96 max-w-[calc(100vw-2rem)]">
        <motion.div
          initial={{ opacity: 0, y: -10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -10, scale: 0.95 }}
          className={`${
            darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          } border rounded-xl shadow-2xl overflow-hidden`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FaBell className="text-blue-500" />
                Notifications
              </h3>
              <button
                onClick={onClose}
                className={`p-1 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
              >
                <FaTimes />
              </button>
            </div>

            {/* Filter tabs */}
            <div className="flex gap-1 mt-3">
              {['all', 'unread', 'read'].map((filterType) => (
                <button
                  key={filterType}
                  onClick={() => setFilter(filterType)}
                  className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                    filter === filterType
                      ? 'bg-blue-500 text-white'
                      : darkMode
                        ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                  }`}
                >
                  {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {/* Notifications list */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin text-blue-500 text-2xl mb-2">
                  <FaBell />
                </div>
                <p className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Loading notifications...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-8 text-center">
                <FaBell className={`text-4xl mb-2 ${darkMode ? 'text-gray-600' : 'text-gray-400'}`} />
                <p className={`font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {filter === 'unread' ? 'No unread notifications' : 'No notifications'}
                </p>
                <p className={`text-sm ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                  You're all caught up!
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                <AnimatePresence>
                  {notifications.map((notification) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      className={`p-4 cursor-pointer transition-colors relative ${
                        !notification.is_read
                          ? darkMode
                            ? 'bg-blue-900/20 hover:bg-blue-900/30'
                            : 'bg-blue-50 hover:bg-blue-100'
                          : darkMode
                            ? 'hover:bg-gray-700'
                            : 'hover:bg-gray-50'
                      }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      {/* Unread indicator */}
                      {!notification.is_read && (
                        <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}

                      <div className="flex gap-3 ml-4">
                        {/* Icon or Emoji */}
                        <div className="flex-shrink-0 mt-1">
                          {notification.emoji ? (
                            <span className="text-xl">{notification.emoji}</span>
                          ) : (
                            <div className={`w-8 h-8 rounded-lg bg-${getTypeColor(notification.type)}-100 flex items-center justify-center`}>
                              {getIcon(notification)}
                            </div>
                          )}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <h4 className={`font-medium text-sm ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                              {notification.title}
                            </h4>
                            <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} ml-2`}>
                              {formatTimeAgo(notification.created_at)}
                            </span>
                          </div>
                          
                          <p className={`text-sm mt-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            {notification.message}
                          </p>

                          {/* Action button */}
                          {notification.action_label && (
                            <div className="mt-2">
                              <span className={`inline-flex items-center gap-1 text-xs px-2 py-1 rounded-lg bg-${getTypeColor(notification.type)}-100 text-${getTypeColor(notification.type)}-700`}>
                                {notification.action_label}
                                {notification.action_type === 'external' && <FaExternalLinkAlt />}
                              </span>
                            </div>
                          )}

                          {/* Action buttons */}
                          <div className="flex gap-2 mt-2">
                            {!notification.is_read && (
                              <button
                                onClick={(e) => handleMarkAsRead(notification.id, e)}
                                className={`text-xs px-2 py-1 rounded-lg ${darkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-600'} flex items-center gap-1`}
                              >
                                <FaCheck className="text-xs" />
                                Mark Read
                              </button>
                            )}
                            <button
                              onClick={(e) => handleDismiss(notification.id, e)}
                              className={`text-xs px-2 py-1 rounded-lg ${darkMode ? 'bg-red-900/30 hover:bg-red-900/50 text-red-400' : 'bg-red-100 hover:bg-red-200 text-red-600'} flex items-center gap-1`}
                            >
                              <FaTimes className="text-xs" />
                              Dismiss
                            </button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className={`p-3 border-t ${darkMode ? 'border-gray-700 bg-gray-750' : 'border-gray-200 bg-gray-50'}`}>
              <button
                onClick={() => {
                  // Mark all as read
                  notifications.forEach(n => {
                    if (!n.is_read) {
                      ProgressTrackingService.markNotificationAsRead(n.id);
                    }
                  });
                  setNotifications(prev => prev.map(n => ({ ...n, is_read: true })));
                }}
                className={`w-full text-sm py-2 rounded-lg ${darkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-600'} transition-colors`}
              >
                Mark All as Read
              </button>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default NotificationDropdown;
