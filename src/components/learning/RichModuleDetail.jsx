import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaArrowLeft, FaBook, FaClock, FaCheck, FaPlay, FaCode, FaQuestionCircle,
  FaGraduationCap, FaTrophy, FaChevronDown, FaChevronRight, FaChevronLeft, FaLightbulb,
  FaCheckCircle, FaExclamationTriangle, FaInfoCircle
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import EmptyState from '../common/EmptyState';
import DatabaseContentService from '../../services/DatabaseContentService';

/**
 * RichModuleDetail Component
 * 
 * Displays module content exactly as structured in JS files with full rich content
 */
const RichModuleDetail = () => {
  const { pathId, moduleId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { darkMode } = useGlobalTheme();

  const [module, setModule] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeSectionIndex, setActiveSectionIndex] = useState(0);
  const [completedSections, setCompletedSections] = useState(new Set());

  // Fetch module content using DatabaseContentService with fallback
  useEffect(() => {
    const fetchModuleContent = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log(`🔍 RichModuleDetail: Loading module ${moduleId} from path ${pathId}`);

        const moduleContent = await DatabaseContentService.getModuleContent(pathId, moduleId);

        if (!moduleContent) {
          throw new Error('Module not found');
        }

        console.log(`✅ RichModuleDetail: Successfully loaded module:`, moduleContent.title);
        setModule(moduleContent);

        // Load user progress if authenticated
        if (user) {
          const progress = await DatabaseContentService.getModuleProgress(moduleId);
          const completedSectionIds = new Set(
            progress.filter(p => p.is_completed).map(p => p.section_id)
          );
          setCompletedSections(completedSectionIds);
        }

      } catch (err) {
        console.error('Error loading module content:', err);
        setError(err.message || 'Failed to load module content');
      } finally {
        setLoading(false);
      }
    };

    if (pathId && moduleId) {
      fetchModuleContent();
    }
  }, [pathId, moduleId, user]);

  // Interactive functionality - ALWAYS called (no conditional hooks)
  useEffect(() => {
    // Only run if we have an active section and module
    if (!module || !module.sections || !module.sections[activeSectionIndex]) return;

    const activeSection = module.sections[activeSectionIndex];

    // Add interactive SVG animations
    const animateSVGElements = () => {
      const dataPackets = document.querySelectorAll('.data-packet');
      dataPackets.forEach(packet => {
        packet.style.animation = 'pulse 2s infinite';
      });
    };

    // Add click handlers for interactive elements
    const addInteractiveHandlers = () => {
      // Network simulation handlers
      const networkNodes = document.querySelectorAll('.network-node');
      networkNodes.forEach(node => {
        node.addEventListener('click', () => {
          node.style.background = '#88cc14';
          setTimeout(() => {
            node.style.background = '#f0f8ff';
          }, 1000);
        });
      });

      // Practice tool handlers
      const practiceTools = document.querySelectorAll('.practice-tool');
      practiceTools.forEach(tool => {
        tool.addEventListener('click', () => {
          const toolName = tool.querySelector('h4')?.textContent;
          if (toolName) {
            console.log(`Opening ${toolName}...`);
          }
        });
      });

      // Exercise control handlers
      const exerciseButtons = document.querySelectorAll('.exercise-controls button');
      exerciseButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          const action = e.target.textContent;
          console.log(`Executing: ${action}`);

          // Simulate exercise interaction
          if (action.includes('Start')) {
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
              if (index === 0) step.style.display = 'block';
              else step.style.display = 'none';
            });
          }
        });
      });

      // Step button handlers
      const stepButtons = document.querySelectorAll('.step-button');
      stepButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          const currentStep = e.target.closest('.step');
          const stepNumber = parseInt(currentStep?.dataset?.step || '0');
          const nextStep = document.querySelector(`[data-step="${stepNumber + 1}"]`);

          if (nextStep && currentStep) {
            currentStep.style.display = 'none';
            nextStep.style.display = 'block';
          }
        });
      });
    };

    // Initialize interactive elements after content loads
    const timer = setTimeout(() => {
      animateSVGElements();
      addInteractiveHandlers();
    }, 500);

    return () => clearTimeout(timer);
  }, [activeSectionIndex, module]); // Fixed dependencies

  // Handle section completion
  const handleSectionComplete = async (sectionIndex) => {
    try {
      const section = module.sections[sectionIndex];
      if (!section || !user) return;

      // Update local state immediately for better UX
      setCompletedSections(prev => new Set([...prev, sectionIndex]));

      // Save progress to database
      await DatabaseContentService.saveProgress(moduleId, section.id || sectionIndex, {
        isCompleted: true,
        timeSpent: section.estimatedTime || 15,
        completionDate: new Date().toISOString()
      });

      console.log(`✅ Section "${section.title}" marked as complete`);
    } catch (error) {
      console.error('Error saving section completion:', error);
      // Revert local state if database save failed
      setCompletedSections(prev => {
        const newSet = new Set(prev);
        newSet.delete(sectionIndex);
        return newSet;
      });
    }
  };

  // Enhance content with interactive elements
  const enhanceContentWithInteractivity = (content) => {
    if (!content) return content;

    let enhancedContent = content;

    // Add enhanced styling to Think Like sections
    enhancedContent = enhancedContent.replace(
      /<h3>Think Like.*?<\/h3>/gi,
      (match) => `<div class="think-like-section">${match}</div>`
    );

    // Add enhanced styling to simulation sections
    enhancedContent = enhancedContent.replace(
      /<h3>.*?Simulation.*?<\/h3>/gi,
      (match) => `<div class="simulation-section">${match}</div>`
    );

    // Add enhanced styling to tools sections
    enhancedContent = enhancedContent.replace(
      /<h3>.*?Tools.*?<\/h3>/gi,
      (match) => `<div class="tools-section">${match}</div>`
    );

    // Enhance SVG containers
    enhancedContent = enhancedContent.replace(
      /<div class="interactive-diagram">/gi,
      '<div class="interactive-diagram enhanced-svg-container">'
    );

    // Add network animation classes to SVG elements
    enhancedContent = enhancedContent.replace(
      /<rect.*?Network.*?>/gi,
      (match) => match.replace('>', ' class="network-animation">')
    );

    // Add data flow animation to connection lines
    enhancedContent = enhancedContent.replace(
      /<line.*?stroke.*?>/gi,
      (match) => match.replace('>', ' class="data-flow-animation" stroke-dasharray="10,5">')
    );

    return enhancedContent;
  };

  // Open interactive tools
  const openTool = (toolType) => {
    switch (toolType) {
      case 'subnet-calculator':
        alert('🧮 Subnet Calculator\n\nEnter IP: ***********\nSubnet Mask: /24\n\nResult:\n• Network: ***********\n• Broadcast: *************\n• Hosts: 254\n• First Host: ***********\n• Last Host: *************\n\n(Full calculator coming soon!)');
        break;
      case 'packet-analyzer':
        alert('📊 Packet Analyzer\n\nAnalyzing sample HTTP packet:\n\n[Ethernet Header]\nSource MAC: aa:bb:cc:dd:ee:ff\nDest MAC: 11:22:33:44:55:66\n\n[IP Header]\nSource IP: ***********00\nDest IP: *************\n\n[TCP Header]\nSource Port: 54321\nDest Port: 80\n\n[HTTP Data]\nGET / HTTP/1.1\nHost: example.com\n\n(Full analyzer coming soon!)');
        break;
      case 'network-tester':
        alert('🔧 Network Tester\n\nTesting connectivity...\n\n✅ ping *********** - 2ms (Gateway)\n✅ ping ******* - 15ms (DNS)\n✅ ping google.com - 18ms (Internet)\n\nNetwork Status: HEALTHY\nBandwidth: 100 Mbps\nLatency: Low\n\n(Full tester coming soon!)');
        break;
      case 'config-generator':
        alert('⚙️ Config Generator\n\nGenerated Cisco Switch Config:\n\n! Basic Configuration\nhostname ClassroomSwitch\n!\ninterface GigabitEthernet0/1\n description Teacher PC\n switchport mode access\n switchport access vlan 10\n no shutdown\n!\ninterface GigabitEthernet0/2\n description Student PC\n switchport mode access\n switchport access vlan 20\n no shutdown\n!\n(Full generator coming soon!)');
        break;
      default:
        alert('Tool coming soon!');
    }
  };

  // Handle back navigation
  const handleBack = () => {
    navigate(`/dashboard/learning-paths/${pathId}`);
  };

  // Get section icon based on type
  const getSectionIcon = (type) => {
    switch (type) {
      case 'text':
        return <FaBook className="text-blue-400" />;
      case 'interactive':
        return <FaCode className="text-green-400" />;
      case 'quiz':
        return <FaQuestionCircle className="text-yellow-400" />;
      case 'simulation':
        return <FaPlay className="text-purple-400" />;
      default:
        return <FaBook className="text-blue-400" />;
    }
  };

  // Render quiz content
  const renderQuizContent = (quizData) => {
    if (!quizData.questions) return null;

    return (
      <div className="space-y-6">
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-yellow-900/20 border-yellow-500/30' : 'bg-yellow-50 border-yellow-200'} border`}>
          <div className="flex items-center gap-2 mb-2">
            <FaLightbulb className="text-yellow-500" />
            <h4 className="font-semibold">Knowledge Check</h4>
          </div>
          <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Test your understanding with these questions.
          </p>
        </div>

        {quizData.questions.map((question, index) => (
          <div key={index} className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <h5 className={`font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Question {index + 1}: {question.question}
            </h5>
            
            <div className="space-y-2 mb-4">
              {question.options.map((option, optionIndex) => (
                <div key={optionIndex} className={`p-3 rounded border ${
                  optionIndex === question.correctAnswer 
                    ? darkMode ? 'bg-green-900/30 border-green-500/50' : 'bg-green-50 border-green-200'
                    : darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                }`}>
                  <div className="flex items-center gap-2">
                    {optionIndex === question.correctAnswer && <FaCheck className="text-green-500" />}
                    <span className={optionIndex === question.correctAnswer ? 'font-medium' : ''}>{option}</span>
                  </div>
                </div>
              ))}
            </div>

            {question.explanation && (
              <div className={`p-3 rounded ${darkMode ? 'bg-blue-900/20' : 'bg-blue-50'}`}>
                <div className="flex items-start gap-2">
                  <FaInfoCircle className="text-blue-500 mt-1 flex-shrink-0" />
                  <div>
                    <h6 className="font-medium mb-1">Explanation:</h6>
                    <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      {question.explanation}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Render interactive content with rich elements
  const renderInteractiveContent = (interactiveData) => {
    // Handle both scenarios and rich HTML content
    const hasScenarios = interactiveData.scenarios && interactiveData.scenarios.length > 0;
    const hasDescription = interactiveData.description;
    const hasCodeExample = interactiveData.codeExample;

    return (
      <div className="interactive-content-container">
        <style jsx>{`
          .interactive-content-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
            color: white;
          }
          .simulation-container {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
          }
          .simulation-controls {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
          }
          .simulation-button {
            background: #88cc14;
            color: black;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
          }
          .simulation-button:hover {
            background: #7ab811;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
          }
          .simulation-display {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            color: black;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
          }
          .network-diagram {
            width: 100%;
            height: 100%;
            position: relative;
          }
          .network-node {
            position: absolute;
            width: 80px;
            height: 60px;
            background: #f0f8ff;
            border: 2px solid #0066cc;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
          }
          .network-node:hover {
            background: #e6f3ff;
            transform: scale(1.05);
          }
          .practice-tools {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin: 20px 0;
          }
          .practice-tool {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
          }
          .practice-tool:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
          }
          .practice-tool h4 {
            color: #88cc14 !important;
            margin-bottom: 8px;
          }
          .tool-preview {
            margin-top: 12px;
            padding: 8px;
            background: rgba(0,0,0,0.1);
            border-radius: 4px;
            font-size: 12px;
          }
          .mini-calculator {
            text-align: center;
          }
          .calc-display {
            background: #000;
            color: #0f0;
            padding: 4px;
            border-radius: 2px;
            font-family: monospace;
            margin-bottom: 4px;
          }
          .calc-result {
            color: #88cc14;
            font-weight: bold;
          }
          .packet-preview {
            display: flex;
            flex-direction: column;
            gap: 2px;
          }
          .packet-layer {
            background: linear-gradient(90deg, #667eea, #764ba2);
            color: white;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 10px;
            text-align: center;
          }
          .ping-preview {
            font-family: monospace;
            font-size: 10px;
          }
          .ping-result {
            color: #00cc00;
            margin-bottom: 2px;
          }
          .config-preview {
            font-family: monospace;
            font-size: 10px;
            color: #333;
          }
          .config-line {
            margin-bottom: 2px;
          }
          .code-simulator {
            background: #1a1a1a;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
          }
          .terminal-output {
            color: #00ff00;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
          }
        `}</style>

        <h3 className="text-2xl font-bold text-primary mb-4">🚀 Interactive Learning Experience</h3>

        {/* Description content */}
        {hasDescription && (
          <div
            className="prose max-w-none prose-invert mb-6"
            dangerouslySetInnerHTML={{ __html: interactiveData.description }}
          />
        )}

        {/* Network Simulation */}
        <div className="simulation-container">
          <h4 className="text-xl font-semibold mb-4">🌐 Network Simulation</h4>
          <div className="simulation-controls">
            <button className="simulation-button">
              Start Network Demo
            </button>
            <button className="simulation-button">
              Show Data Flow
            </button>
            <button className="simulation-button">
              Reset
            </button>
          </div>
          <div className="simulation-display">
            <div className="network-diagram">
              {/* Computer A */}
              <div className="network-node" style={{top: '20px', left: '50px'}}>
                💻<br/>Computer A
              </div>
              {/* Switch */}
              <div className="network-node" style={{top: '120px', left: '200px', width: '100px'}}>
                🔀<br/>Switch
              </div>
              {/* Computer B */}
              <div className="network-node" style={{top: '20px', right: '50px'}}>
                💻<br/>Computer B
              </div>
              {/* Server */}
              <div className="network-node" style={{top: '220px', left: '200px', width: '100px'}}>
                🖥️<br/>Server
              </div>
            </div>
          </div>
        </div>

        {/* Practice Tools */}
        <div className="practice-tools">
          <div className="practice-tool" onClick={() => openTool('subnet-calculator')}>
            <h4>🧮 Subnet Calculator</h4>
            <p>Calculate network subnets and IP ranges</p>
            <div className="tool-preview">
              <div className="mini-calculator">
                <div className="calc-display">***********/24</div>
                <div className="calc-result">256 hosts</div>
              </div>
            </div>
          </div>
          <div className="practice-tool" onClick={() => openTool('packet-analyzer')}>
            <h4>📊 Packet Analyzer</h4>
            <p>Analyze network packet structures</p>
            <div className="tool-preview">
              <div className="packet-preview">
                <div className="packet-layer">Ethernet</div>
                <div className="packet-layer">IP</div>
                <div className="packet-layer">TCP</div>
                <div className="packet-layer">HTTP</div>
              </div>
            </div>
          </div>
          <div className="practice-tool" onClick={() => openTool('network-tester')}>
            <h4>🔧 Network Tester</h4>
            <p>Test network connectivity and performance</p>
            <div className="tool-preview">
              <div className="ping-preview">
                <div className="ping-result">✅ *********** - 5ms</div>
                <div className="ping-result">✅ google.com - 15ms</div>
              </div>
            </div>
          </div>
          <div className="practice-tool" onClick={() => openTool('config-generator')}>
            <h4>⚙️ Config Generator</h4>
            <p>Generate network device configurations</p>
            <div className="tool-preview">
              <div className="config-preview">
                <div className="config-line">interface GigabitEthernet0/1</div>
                <div className="config-line">ip address *********** *************</div>
              </div>
            </div>
          </div>
        </div>

        {/* Code Example with Interactive Terminal */}
        {hasCodeExample && (
          <div className="code-simulator">
            <h4 className="text-lg font-semibold mb-4 text-primary">💻 Interactive Terminal</h4>
            <div className="terminal-output">
              Welcome to Network Command Simulator
              Type 'help' for available commands
            </div>
            <pre className="text-green-400 text-sm overflow-x-auto mt-4">
              <code>{interactiveData.codeExample}</code>
            </pre>
          </div>
        )}

        {/* Scenarios (if available) */}
        {hasScenarios && (
          <div className="space-y-4 mt-6">
            <h4 className="text-xl font-semibold text-primary">📋 Practice Scenarios</h4>
            {interactiveData.scenarios.map((scenario, index) => (
              <div key={index} className="bg-white/10 rounded-lg p-4">
                <h5 className="font-semibold mb-2 text-primary">Scenario {index + 1}</h5>
                <p className="mb-3">{scenario.description}</p>
                <div className="bg-black/20 rounded p-3">
                  <p><strong>Solution:</strong> {scenario.solution}</p>
                  <p><strong>Explanation:</strong> {scenario.explanation}</p>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="text-center mt-6">
          <button className="simulation-button text-lg px-8 py-3">
            🎯 Complete Interactive Challenge
          </button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="animate-spin text-primary text-4xl">
            <FaBook />
          </div>
        </div>
      </div>
    );
  }

  if (error || !module) {
    return (
      <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
        <EmptyState
          icon={<FaBook className="text-5xl" />}
          title="Module Not Found"
          message={error || "The module you're looking for doesn't exist or has been removed."}
          action={
            <button
              className="bg-[#88cc14] hover:bg-[#7ab811] text-black px-4 py-2 rounded-lg font-medium transition-colors"
              onClick={handleBack}
            >
              <FaArrowLeft className="inline-block mr-2" />
              Back to Learning Path
            </button>
          }
        />
      </div>
    );
  }

  const activeSection = module.sections?.[activeSectionIndex];

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Back button */}
      <button
        onClick={handleBack}
        className={`mb-6 flex items-center gap-2 ${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-800'} transition-colors`}
      >
        <FaArrowLeft />
        Back to Learning Path
      </button>

      {/* Module Header */}
      <div className={`p-6 rounded-lg mb-8 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}>
        <h1 className={`text-3xl font-bold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          {module.title}
        </h1>
        <p className={`text-lg mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {module.description}
        </p>

        {/* Module Meta */}
        <div className="flex flex-wrap items-center gap-4 mb-6">
          <span className={`px-3 py-1 rounded-full text-sm ${
            module.difficulty === 'Beginner' ? 'bg-green-500/20 text-green-400 border-green-500/30' :
            module.difficulty === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' :
            'bg-red-500/20 text-red-400 border-red-500/30'
          } border`}>
            {module.difficulty}
          </span>
          <span className={`flex items-center gap-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <FaClock />
            {module.estimatedTime} minutes
          </span>
          <span className={`flex items-center gap-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <FaBook />
            {module.sections?.length || 0} sections
          </span>
          {module.xpReward && (
            <span className={`flex items-center gap-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              <FaTrophy />
              {module.xpReward} XP
            </span>
          )}
        </div>

        {/* Learning Objectives */}
        {module.objectives && module.objectives.length > 0 && (
          <div>
            <h3 className={`text-lg font-semibold mb-3 flex items-center gap-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              <FaGraduationCap className="text-[#88cc14]" />
              Learning Objectives
            </h3>
            <ul className="space-y-2">
              {module.objectives.map((objective, index) => (
                <li key={index} className={`flex items-start gap-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  <FaCheck className="text-[#88cc14] mt-1 flex-shrink-0" />
                  {objective}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Module Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sections Sidebar */}
        <div className="lg:col-span-1">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border sticky top-4`}>
            <h3 className={`font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Sections
            </h3>
            <div className="space-y-2">
              {module.sections?.map((section, index) => (
                <button
                  key={index}
                  onClick={() => setActiveSectionIndex(index)}
                  className={`w-full text-left p-3 rounded-lg transition-colors ${
                    index === activeSectionIndex
                      ? 'bg-[#88cc14] text-black'
                      : darkMode
                        ? 'hover:bg-gray-700 text-gray-300'
                        : 'hover:bg-gray-100 text-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    {getSectionIcon(section.type)}
                    <div className="flex-1">
                      <div className="font-medium text-sm">{section.title}</div>
                      <div className={`text-xs ${index === activeSectionIndex ? 'text-black/70' : darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {section.type || 'content'}
                      </div>
                    </div>
                    {completedSections.has(index) && (
                      <FaCheckCircle className="text-green-500" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Section Content */}
        <div className="lg:col-span-3">
          {activeSection ? (
            <motion.div
              key={activeSectionIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}
            >
              <h2 className={`text-2xl font-bold mb-6 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {activeSection.title}
              </h2>

              {/* Text Content with Rich Elements */}
              {activeSection.type === 'text' && typeof activeSection.content === 'string' && (
                <div className="rich-content-container">
                  <style jsx>{`
                    .learning-module-story {
                      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                      border-radius: 12px;
                      padding: 24px;
                      margin: 24px 0;
                      color: white;
                      position: relative;
                      overflow: hidden;
                    }
                    .learning-module-story::before {
                      content: '📖';
                      position: absolute;
                      top: 16px;
                      right: 16px;
                      font-size: 24px;
                      opacity: 0.7;
                    }
                    .learning-module-story h3 {
                      color: #88cc14 !important;
                      margin-bottom: 16px;
                      font-size: 1.25rem;
                    }
                    .interactive-diagram {
                      background: ${darkMode ? '#1a1f35' : '#f8fafc'};
                      border: 2px solid ${darkMode ? '#374151' : '#e2e8f0'};
                      border-radius: 12px;
                      padding: 20px;
                      margin: 24px 0;
                      text-align: center;
                    }
                    .interactive-diagram svg {
                      max-width: 100%;
                      height: auto;
                      background: white;
                      border-radius: 8px;
                    }
                    .feature-cards {
                      display: grid;
                      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                      gap: 20px;
                      margin: 24px 0;
                    }
                    .feature-card {
                      background: ${darkMode ? '#374151' : '#f1f5f9'};
                      border: 1px solid ${darkMode ? '#4b5563' : '#cbd5e1'};
                      border-radius: 12px;
                      padding: 20px;
                      transition: transform 0.2s, box-shadow 0.2s;
                    }
                    .feature-card:hover {
                      transform: translateY(-4px);
                      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                    }
                    .feature-card h3 {
                      color: #88cc14 !important;
                      margin-bottom: 12px;
                    }
                    .comparison-table {
                      width: 100%;
                      border-collapse: collapse;
                      margin: 24px 0;
                      background: ${darkMode ? '#374151' : 'white'};
                      border-radius: 8px;
                      overflow: hidden;
                      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    }
                    .comparison-table th {
                      background: #88cc14;
                      color: black;
                      padding: 16px;
                      text-align: left;
                      font-weight: 600;
                    }
                    .comparison-table td {
                      padding: 16px;
                      border-bottom: 1px solid ${darkMode ? '#4b5563' : '#e2e8f0'};
                    }
                    .comparison-table tr:hover {
                      background: ${darkMode ? '#4b5563' : '#f8fafc'};
                    }
                    .configuration-example {
                      background: ${darkMode ? '#1f2937' : '#f8fafc'};
                      border: 1px solid ${darkMode ? '#374151' : '#e2e8f0'};
                      border-radius: 12px;
                      padding: 24px;
                      margin: 24px 0;
                    }
                    .code-block {
                      background: #1a1a1a;
                      color: #00ff00;
                      padding: 20px;
                      border-radius: 8px;
                      font-family: 'Courier New', monospace;
                      font-size: 14px;
                      line-height: 1.5;
                      overflow-x: auto;
                      margin: 16px 0;
                    }
                    .interactive-exercise {
                      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                      border-radius: 12px;
                      padding: 24px;
                      margin: 24px 0;
                      color: white;
                    }
                    .exercise-container {
                      background: rgba(255,255,255,0.1);
                      border-radius: 8px;
                      padding: 20px;
                      margin-top: 16px;
                    }
                    .exercise-controls button {
                      background: #88cc14;
                      color: black;
                      border: none;
                      padding: 12px 24px;
                      border-radius: 6px;
                      font-weight: 600;
                      margin-right: 12px;
                      cursor: pointer;
                      transition: background 0.2s;
                    }
                    .exercise-controls button:hover {
                      background: #7ab811;
                    }
                    .step {
                      background: rgba(255,255,255,0.1);
                      border-radius: 8px;
                      padding: 16px;
                      margin: 16px 0;
                    }
                    .step h4 {
                      color: #88cc14 !important;
                      margin-bottom: 12px;
                    }
                    .step select, .step-button {
                      background: white;
                      color: black;
                      border: 1px solid #ccc;
                      padding: 8px 12px;
                      border-radius: 4px;
                      margin: 8px 8px 8px 0;
                    }
                    .step-button {
                      background: #88cc14;
                      color: black;
                      border: none;
                      font-weight: 600;
                      cursor: pointer;
                    }
                    .quiz-container {
                      background: ${darkMode ? '#374151' : '#f8fafc'};
                      border-radius: 12px;
                      padding: 24px;
                      margin: 24px 0;
                    }
                    .quiz-question {
                      margin-bottom: 24px;
                    }
                    .quiz-options {
                      margin: 16px 0;
                    }
                    .quiz-options label {
                      display: block;
                      padding: 12px;
                      margin: 8px 0;
                      background: ${darkMode ? '#4b5563' : 'white'};
                      border-radius: 8px;
                      cursor: pointer;
                      transition: background 0.2s;
                    }
                    .quiz-options label:hover {
                      background: ${darkMode ? '#6b7280' : '#f1f5f9'};
                    }
                    .think-like-section {
                      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                      border-radius: 12px;
                      padding: 24px;
                      margin: 24px 0;
                      color: white;
                      position: relative;
                      overflow: hidden;
                    }
                    .think-like-section::before {
                      content: '🧠';
                      position: absolute;
                      top: 16px;
                      right: 16px;
                      font-size: 24px;
                      opacity: 0.7;
                    }
                    .think-like-section h3 {
                      color: #ffd700 !important;
                      margin-bottom: 16px;
                      font-size: 1.25rem;
                    }
                    .simulation-section {
                      background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
                      border-radius: 12px;
                      padding: 24px;
                      margin: 24px 0;
                      color: white;
                      position: relative;
                    }
                    .simulation-section::before {
                      content: '⚡';
                      position: absolute;
                      top: 16px;
                      right: 16px;
                      font-size: 24px;
                      opacity: 0.7;
                    }
                    .simulation-section h3 {
                      color: #88cc14 !important;
                      margin-bottom: 16px;
                      font-size: 1.25rem;
                    }
                    .tools-section {
                      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
                      border-radius: 12px;
                      padding: 24px;
                      margin: 24px 0;
                      color: #333;
                      position: relative;
                    }
                    .tools-section::before {
                      content: '🔧';
                      position: absolute;
                      top: 16px;
                      right: 16px;
                      font-size: 24px;
                      opacity: 0.7;
                    }
                    .tools-section h3 {
                      color: #2c3e50 !important;
                      margin-bottom: 16px;
                      font-size: 1.25rem;
                    }
                    .enhanced-svg-container {
                      background: white;
                      border-radius: 12px;
                      padding: 20px;
                      margin: 20px 0;
                      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                      position: relative;
                      overflow: hidden;
                    }
                    .enhanced-svg-container::before {
                      content: '';
                      position: absolute;
                      top: 0;
                      left: 0;
                      right: 0;
                      height: 4px;
                      background: linear-gradient(90deg, #88cc14, #667eea, #764ba2);
                    }
                    .network-animation {
                      animation: networkPulse 3s ease-in-out infinite;
                    }
                    @keyframes networkPulse {
                      0%, 100% { opacity: 1; transform: scale(1); }
                      50% { opacity: 0.8; transform: scale(1.05); }
                    }
                    .data-flow-animation {
                      animation: dataFlow 4s linear infinite;
                    }
                    @keyframes dataFlow {
                      0% { stroke-dashoffset: 100; }
                      100% { stroke-dashoffset: 0; }
                    }
                  `}</style>
                  <div
                    className={`prose max-w-none ${darkMode ? 'prose-invert' : ''} prose-lg prose-headings:text-primary prose-p:text-gray-300 prose-li:text-gray-300 prose-strong:text-white`}
                    dangerouslySetInnerHTML={{ __html: enhanceContentWithInteractivity(activeSection.content) }}
                  />
                </div>
              )}

              {/* Quiz Content */}
              {activeSection.type === 'quiz' && activeSection.content && (
                renderQuizContent(activeSection.content)
              )}

              {/* Interactive Content */}
              {activeSection.type === 'interactive' && activeSection.content && (
                renderInteractiveContent(activeSection.content)
              )}

              {/* Section Navigation */}
              <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-700">
                <button
                  onClick={() => setActiveSectionIndex(Math.max(0, activeSectionIndex - 1))}
                  disabled={activeSectionIndex === 0}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                    activeSectionIndex === 0
                      ? 'opacity-50 cursor-not-allowed'
                      : 'bg-gray-700 hover:bg-gray-600 text-white'
                  }`}
                >
                  <FaChevronLeft />
                  Previous
                </button>

                <button
                  onClick={() => handleSectionComplete(activeSectionIndex)}
                  className="bg-[#88cc14] hover:bg-[#7ab811] text-black px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Mark Complete
                </button>

                <button
                  onClick={() => setActiveSectionIndex(Math.min(module.sections.length - 1, activeSectionIndex + 1))}
                  disabled={activeSectionIndex === module.sections.length - 1}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                    activeSectionIndex === module.sections.length - 1
                      ? 'opacity-50 cursor-not-allowed'
                      : 'bg-[#88cc14] hover:bg-[#7ab811] text-black'
                  }`}
                >
                  Next
                  <FaChevronRight />
                </button>
              </div>
            </motion.div>
          ) : (
            <EmptyState
              icon={<FaBook className="text-5xl" />}
              title="No Section Selected"
              message="Select a section from the sidebar to view its content."
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default RichModuleDetail;
