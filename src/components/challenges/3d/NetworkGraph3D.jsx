import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { FaServer, FaDesktop, FaNetworkWired, FaGlobe, FaLock, FaUnlock } from 'react-icons/fa';

/**
 * 3D Network Graph Component
 * 
 * A 3D visualization of network nodes and connections for network security challenges
 */
const NetworkGraph3D = ({ 
  nodes = [], 
  connections = [],
  height = '400px',
  onNodeClick,
  highlightedNodes = [],
  highlightedConnections = []
}) => {
  const { darkMode } = useGlobalTheme();
  const containerRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const rendererRef = useRef(null);
  const labelRendererRef = useRef(null);
  const controlsRef = useRef(null);
  const nodeObjectsRef = useRef({});
  const connectionObjectsRef = useRef([]);
  const [isAnimating, setIsAnimating] = useState(true);
  const [hoveredNode, setHoveredNode] = useState(null);

  // Initialize 3D scene
  useEffect(() => {
    if (!containerRef.current) return;

    // Create scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(darkMode ? 0x111827 : 0xf3f4f6);
    sceneRef.current = scene;

    // Create camera
    const camera = new THREE.PerspectiveCamera(
      60,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.z = 15;
    cameraRef.current = camera;

    // Create WebGL renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Create CSS2D renderer for labels
    const labelRenderer = new CSS2DRenderer();
    labelRenderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    labelRenderer.domElement.style.position = 'absolute';
    labelRenderer.domElement.style.top = '0';
    labelRenderer.domElement.style.pointerEvents = 'none';
    containerRef.current.appendChild(labelRenderer.domElement);
    labelRendererRef.current = labelRenderer;

    // Add controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.5;
    controls.enableZoom = true;
    controls.enablePan = true;
    controlsRef.current = controls;

    // Add lights
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    // Add raycaster for interaction
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();

    const onMouseMove = (event) => {
      // Calculate mouse position in normalized device coordinates
      const rect = containerRef.current.getBoundingClientRect();
      mouse.x = ((event.clientX - rect.left) / containerRef.current.clientWidth) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / containerRef.current.clientHeight) * 2 + 1;

      // Update the picking ray with the camera and mouse position
      raycaster.setFromCamera(mouse, camera);

      // Calculate objects intersecting the picking ray
      const nodeValues = Object.values(nodeObjectsRef.current);
      const intersects = raycaster.intersectObjects(nodeValues.map(n => n.mesh));

      if (intersects.length > 0) {
        const nodeEntry = Object.entries(nodeObjectsRef.current).find(
          ([_, nodeObj]) => nodeObj.mesh === intersects[0].object
        );
        
        if (nodeEntry) {
          const [nodeId, _] = nodeEntry;
          setHoveredNode(nodeId);
          containerRef.current.style.cursor = 'pointer';
        }
      } else {
        setHoveredNode(null);
        containerRef.current.style.cursor = 'default';
      }
    };

    const onClick = (event) => {
      if (hoveredNode && onNodeClick) {
        onNodeClick(hoveredNode);
      }
    };

    containerRef.current.addEventListener('mousemove', onMouseMove);
    containerRef.current.addEventListener('click', onClick);

    // Animation loop
    const animate = () => {
      if (!isAnimating) return;
      requestAnimationFrame(animate);
      controls.update();
      
      // Pulse effect for highlighted nodes
      if (highlightedNodes.length > 0) {
        const time = Date.now() * 0.001;
        highlightedNodes.forEach(nodeId => {
          const nodeObj = nodeObjectsRef.current[nodeId];
          if (nodeObj && nodeObj.mesh) {
            const scale = 1 + 0.1 * Math.sin(time * 3);
            nodeObj.mesh.scale.set(scale, scale, scale);
            
            // Also pulse the glow
            if (nodeObj.glow) {
              const glowScale = 1.2 + 0.2 * Math.sin(time * 3);
              nodeObj.glow.scale.set(glowScale, glowScale, glowScale);
              nodeObj.glow.material.opacity = 0.5 + 0.2 * Math.sin(time * 3);
            }
          }
        });
      }
      
      // Animate data flow on connections
      connectionObjectsRef.current.forEach(conn => {
        if (conn.particles) {
          conn.particles.forEach(particle => {
            particle.position.lerp(conn.end.clone(), 0.02);
            
            // Reset particle when it reaches the end
            if (particle.position.distanceTo(conn.end) < 0.3) {
              particle.position.copy(conn.start);
            }
          });
        }
      });
      
      renderer.render(scene, camera);
      labelRenderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return;
      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
      labelRenderer.setSize(width, height);
    };
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (containerRef.current) {
        if (renderer.domElement) {
          containerRef.current.removeChild(renderer.domElement);
        }
        if (labelRenderer.domElement) {
          containerRef.current.removeChild(labelRenderer.domElement);
        }
      }
      window.removeEventListener('resize', handleResize);
      containerRef.current.removeEventListener('mousemove', onMouseMove);
      containerRef.current.removeEventListener('click', onClick);
      isAnimating && cancelAnimationFrame(animate);
    };
  }, [darkMode, isAnimating, onNodeClick, hoveredNode]);

  // Create or update nodes
  useEffect(() => {
    if (!sceneRef.current) return;
    
    // Clear existing nodes
    Object.values(nodeObjectsRef.current).forEach(nodeObj => {
      if (nodeObj.mesh) sceneRef.current.remove(nodeObj.mesh);
      if (nodeObj.label) sceneRef.current.remove(nodeObj.label);
      if (nodeObj.glow) sceneRef.current.remove(nodeObj.glow);
    });
    nodeObjectsRef.current = {};
    
    // Create new nodes
    nodes.forEach(node => {
      // Create node mesh
      const geometry = new THREE.SphereGeometry(node.size || 0.5, 32, 32);
      const material = new THREE.MeshPhongMaterial({
        color: node.color || 0x3b82f6,
        shininess: 30,
        emissive: node.color || 0x3b82f6,
        emissiveIntensity: 0.2
      });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.set(node.x || 0, node.y || 0, node.z || 0);
      sceneRef.current.add(mesh);
      
      // Create glow effect for highlighted nodes
      let glow = null;
      if (highlightedNodes.includes(node.id)) {
        const glowGeometry = new THREE.SphereGeometry((node.size || 0.5) * 1.2, 32, 32);
        const glowMaterial = new THREE.MeshBasicMaterial({
          color: 0xff9900,
          transparent: true,
          opacity: 0.5
        });
        glow = new THREE.Mesh(glowGeometry, glowMaterial);
        glow.position.copy(mesh.position);
        sceneRef.current.add(glow);
      }
      
      // Create label
      const labelDiv = document.createElement('div');
      labelDiv.className = 'text-xs font-bold';
      labelDiv.textContent = node.label || node.id;
      labelDiv.style.color = darkMode ? '#ffffff' : '#000000';
      labelDiv.style.padding = '2px 5px';
      labelDiv.style.backgroundColor = darkMode ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)';
      labelDiv.style.borderRadius = '4px';
      
      const label = new CSS2DObject(labelDiv);
      label.position.set(0, (node.size || 0.5) + 0.3, 0);
      mesh.add(label);
      
      // Store references
      nodeObjectsRef.current[node.id] = { mesh, label, glow };
    });
  }, [nodes, darkMode, highlightedNodes]);

  // Create or update connections
  useEffect(() => {
    if (!sceneRef.current) return;
    
    // Clear existing connections
    connectionObjectsRef.current.forEach(conn => {
      if (conn.line) sceneRef.current.remove(conn.line);
      if (conn.particles) {
        conn.particles.forEach(particle => {
          sceneRef.current.remove(particle);
        });
      }
    });
    connectionObjectsRef.current = [];
    
    // Create new connections
    connections.forEach(connection => {
      const startNode = nodeObjectsRef.current[connection.source];
      const endNode = nodeObjectsRef.current[connection.target];
      
      if (startNode && endNode) {
        const start = startNode.mesh.position;
        const end = endNode.mesh.position;
        
        // Create line
        const points = [start, end];
        const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
        const lineMaterial = new THREE.LineBasicMaterial({ 
          color: connection.color || 0x888888,
          linewidth: connection.width || 1,
          opacity: highlightedConnections.includes(connection.id) ? 1 : 0.6,
          transparent: true
        });
        const line = new THREE.Line(lineGeometry, lineMaterial);
        sceneRef.current.add(line);
        
        // Create data flow particles
        const particles = [];
        if (connection.hasTraffic) {
          const particleCount = connection.trafficVolume || 3;
          const particleGeometry = new THREE.SphereGeometry(0.1, 8, 8);
          const particleMaterial = new THREE.MeshBasicMaterial({
            color: connection.trafficColor || 0x00ff00,
            transparent: true,
            opacity: 0.8
          });
          
          for (let i = 0; i < particleCount; i++) {
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            // Position particle along the line
            const t = i / particleCount;
            particle.position.lerpVectors(start, end, t);
            sceneRef.current.add(particle);
            particles.push(particle);
          }
        }
        
        // Store references
        connectionObjectsRef.current.push({ 
          id: connection.id,
          line, 
          particles,
          start,
          end
        });
      }
    });
  }, [connections, darkMode, highlightedConnections]);

  return (
    <div 
      ref={containerRef} 
      className={`relative overflow-hidden rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-300'}`}
      style={{ height }}
    >
      {/* Controls */}
      <div className="absolute top-2 right-2 z-10">
        <button
          onClick={() => setIsAnimating(!isAnimating)}
          className={`p-2 rounded-full ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-200 hover:bg-gray-300'}`}
          title={isAnimating ? "Pause animation" : "Resume animation"}
        >
          {isAnimating ? "⏸️" : "▶️"}
        </button>
      </div>
      
      {/* Node info tooltip */}
      {hoveredNode && (
        <div 
          className={`absolute bottom-2 left-2 p-3 rounded-lg z-10 ${
            darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          } shadow-lg border ${darkMode ? 'border-gray-700' : 'border-gray-300'}`}
        >
          <div className="font-bold">{
            nodes.find(n => n.id === hoveredNode)?.label || hoveredNode
          }</div>
          <div className="text-sm text-gray-500">
            {nodes.find(n => n.id === hoveredNode)?.type || 'Node'}
          </div>
          {nodes.find(n => n.id === hoveredNode)?.details && (
            <div className="mt-1 text-xs">
              {nodes.find(n => n.id === hoveredNode)?.details}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NetworkGraph3D;
