import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';

/**
 * 3D Binary Visualizer Component
 * 
 * A 3D visualization of binary data for reverse engineering and binary analysis challenges
 */
const BinaryVisualizer3D = ({ 
  binaryData = [], 
  height = '400px',
  highlightedSections = [],
  onSectionClick,
  colorMapping = {
    '00': 0x000000,  // Black for null bytes
    'FF': 0xff0000,  // Red for 0xFF bytes
    'text': 0x00ff00, // Green for text
    'code': 0x0000ff, // Blue for code
    'data': 0xffff00, // Yellow for data
    'header': 0xff00ff, // Magenta for headers
    'default': 0x888888 // Gray for other bytes
  }
}) => {
  const { darkMode } = useGlobalTheme();
  const containerRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const rendererRef = useRef(null);
  const controlsRef = useRef(null);
  const cubesRef = useRef([]);
  const [isAnimating, setIsAnimating] = useState(true);
  const [hoveredSection, setHoveredSection] = useState(null);
  const [viewMode, setViewMode] = useState('3d'); // '3d', 'heatmap', 'entropy'

  // Initialize 3D scene
  useEffect(() => {
    if (!containerRef.current) return;

    // Create scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(darkMode ? 0x111827 : 0xf3f4f6);
    sceneRef.current = scene;

    // Create camera
    const camera = new THREE.PerspectiveCamera(
      60,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.z = 50;
    cameraRef.current = camera;

    // Create renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Add controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.5;
    controls.enableZoom = true;
    controls.enablePan = true;
    controlsRef.current = controls;

    // Add lights
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    // Add raycaster for interaction
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();

    const onMouseMove = (event) => {
      // Calculate mouse position in normalized device coordinates
      const rect = containerRef.current.getBoundingClientRect();
      mouse.x = ((event.clientX - rect.left) / containerRef.current.clientWidth) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / containerRef.current.clientHeight) * 2 + 1;

      // Update the picking ray with the camera and mouse position
      raycaster.setFromCamera(mouse, camera);

      // Calculate objects intersecting the picking ray
      const intersects = raycaster.intersectObjects(cubesRef.current);

      if (intersects.length > 0) {
        const cube = intersects[0].object;
        setHoveredSection(cube.userData);
        containerRef.current.style.cursor = 'pointer';
      } else {
        setHoveredSection(null);
        containerRef.current.style.cursor = 'default';
      }
    };

    const onClick = (event) => {
      if (hoveredSection && onSectionClick) {
        onSectionClick(hoveredSection);
      }
    };

    containerRef.current.addEventListener('mousemove', onMouseMove);
    containerRef.current.addEventListener('click', onClick);

    // Animation loop
    const animate = () => {
      if (!isAnimating) return;
      requestAnimationFrame(animate);
      controls.update();
      
      // Pulse effect for highlighted sections
      if (highlightedSections.length > 0) {
        const time = Date.now() * 0.001;
        cubesRef.current.forEach(cube => {
          if (highlightedSections.includes(cube.userData.offset)) {
            const scale = 1 + 0.1 * Math.sin(time * 3);
            cube.scale.set(scale, scale, scale);
            
            // Also pulse the emissive intensity
            if (cube.material) {
              cube.material.emissiveIntensity = 0.5 + 0.3 * Math.sin(time * 3);
            }
          }
        });
      }
      
      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return;
      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement);
      }
      window.removeEventListener('resize', handleResize);
      containerRef.current.removeEventListener('mousemove', onMouseMove);
      containerRef.current.removeEventListener('click', onClick);
      isAnimating && cancelAnimationFrame(animate);
    };
  }, [darkMode, isAnimating, onSectionClick, hoveredSection]);

  // Create or update binary visualization
  useEffect(() => {
    if (!sceneRef.current || binaryData.length === 0) return;
    
    // Clear existing cubes
    cubesRef.current.forEach(cube => {
      sceneRef.current.remove(cube);
    });
    cubesRef.current = [];
    
    // Calculate grid dimensions
    const bytesPerRow = Math.ceil(Math.sqrt(binaryData.length));
    const rows = Math.ceil(binaryData.length / bytesPerRow);
    
    // Create cubes based on view mode
    if (viewMode === '3d') {
      // 3D cube visualization
      binaryData.forEach((byte, index) => {
        const x = (index % bytesPerRow) - bytesPerRow / 2;
        const z = Math.floor(index / bytesPerRow) - rows / 2;
        
        // Determine byte type and color
        let color = colorMapping.default;
        let height = 1;
        
        if (byte.type && colorMapping[byte.type]) {
          color = colorMapping[byte.type];
        } else if (byte.value === '00') {
          color = colorMapping['00'];
          height = 0.2; // Make null bytes shorter
        } else if (byte.value === 'FF') {
          color = colorMapping['FF'];
          height = 1.5; // Make 0xFF bytes taller
        }
        
        // Create cube
        const geometry = new THREE.BoxGeometry(0.8, height, 0.8);
        const material = new THREE.MeshPhongMaterial({
          color: color,
          shininess: 30,
          emissive: color,
          emissiveIntensity: highlightedSections.includes(byte.offset) ? 0.5 : 0.1
        });
        const cube = new THREE.Mesh(geometry, material);
        
        // Position cube
        cube.position.set(x, height / 2, z);
        
        // Store metadata
        cube.userData = {
          offset: byte.offset,
          value: byte.value,
          type: byte.type,
          description: byte.description
        };
        
        sceneRef.current.add(cube);
        cubesRef.current.push(cube);
      });
    } else if (viewMode === 'heatmap') {
      // 2D heatmap visualization
      const planeGeometry = new THREE.PlaneGeometry(bytesPerRow, rows);
      const positions = planeGeometry.attributes.position.array;
      
      // Create color data for heatmap
      const colors = new Float32Array(positions.length);
      
      for (let i = 0; i < binaryData.length; i++) {
        const byte = binaryData[i];
        const x = i % bytesPerRow;
        const y = Math.floor(i / bytesPerRow);
        
        // Convert hex value to intensity (0-1)
        const value = parseInt(byte.value, 16) / 255;
        
        // Set color based on value
        const color = new THREE.Color();
        if (byte.type && colorMapping[byte.type]) {
          color.setHex(colorMapping[byte.type]);
        } else {
          color.setHSL(value * 0.7, 1, 0.5); // Use HSL for smooth color gradient
        }
        
        // Set vertex colors
        const vertexIndex = (y * bytesPerRow + x) * 4;
        for (let j = 0; j < 4; j++) {
          colors[vertexIndex + j * 3] = color.r;
          colors[vertexIndex + j * 3 + 1] = color.g;
          colors[vertexIndex + j * 3 + 2] = color.b;
        }
      }
      
      // Create material with vertex colors
      const material = new THREE.MeshBasicMaterial({
        vertexColors: true,
        side: THREE.DoubleSide
      });
      
      const plane = new THREE.Mesh(planeGeometry, material);
      plane.rotation.x = -Math.PI / 2;
      
      sceneRef.current.add(plane);
      cubesRef.current.push(plane);
    } else if (viewMode === 'entropy') {
      // Entropy visualization (3D terrain)
      const terrainGeometry = new THREE.PlaneGeometry(bytesPerRow, rows, bytesPerRow - 1, rows - 1);
      const positions = terrainGeometry.attributes.position.array;
      
      // Calculate entropy for each position
      for (let i = 0; i < binaryData.length; i++) {
        const byte = binaryData[i];
        const x = i % bytesPerRow;
        const y = Math.floor(i / bytesPerRow);
        
        // Calculate local entropy (randomness)
        let entropy = 0;
        const windowSize = 8;
        const start = Math.max(0, i - windowSize / 2);
        const end = Math.min(binaryData.length - 1, i + windowSize / 2);
        
        // Count unique values in window
        const uniqueValues = new Set();
        for (let j = start; j <= end; j++) {
          if (j < binaryData.length) {
            uniqueValues.add(binaryData[j].value);
          }
        }
        
        // Normalize entropy
        entropy = uniqueValues.size / windowSize;
        
        // Set height based on entropy
        const vertexIndex = (y * (bytesPerRow) + x);
        positions[vertexIndex * 3 + 2] = entropy * 5; // Scale height
      }
      
      // Update geometry
      terrainGeometry.computeVertexNormals();
      
      // Create material
      const material = new THREE.MeshPhongMaterial({
        color: 0x3b82f6,
        shininess: 30,
        wireframe: false,
        flatShading: true
      });
      
      const terrain = new THREE.Mesh(terrainGeometry, material);
      terrain.rotation.x = -Math.PI / 2;
      
      sceneRef.current.add(terrain);
      cubesRef.current.push(terrain);
    }
    
  }, [binaryData, viewMode, darkMode, highlightedSections, colorMapping]);

  return (
    <div className="relative">
      <div 
        ref={containerRef} 
        className={`relative overflow-hidden rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-300'}`}
        style={{ height }}
      >
        {/* Controls */}
        <div className="absolute top-2 right-2 z-10 flex space-x-2">
          <select
            value={viewMode}
            onChange={(e) => setViewMode(e.target.value)}
            className={`text-sm rounded-md ${
              darkMode 
                ? 'bg-gray-800 border-gray-700 text-white' 
                : 'bg-gray-50 border-gray-300 text-gray-900'
            } border p-1`}
          >
            <option value="3d">3D View</option>
            <option value="heatmap">Heatmap</option>
            <option value="entropy">Entropy</option>
          </select>
          
          <button
            onClick={() => setIsAnimating(!isAnimating)}
            className={`p-2 rounded-full ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-200 hover:bg-gray-300'}`}
            title={isAnimating ? "Pause animation" : "Resume animation"}
          >
            {isAnimating ? "⏸️" : "▶️"}
          </button>
        </div>
      </div>
      
      {/* Byte info tooltip */}
      {hoveredSection && (
        <div 
          className={`absolute bottom-2 left-2 p-3 rounded-lg z-10 ${
            darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          } shadow-lg border ${darkMode ? 'border-gray-700' : 'border-gray-300'}`}
        >
          <div className="font-mono">
            <span className="font-bold">Offset:</span> 0x{hoveredSection.offset.toString(16).padStart(8, '0')}
          </div>
          <div className="font-mono">
            <span className="font-bold">Value:</span> 0x{hoveredSection.value}
          </div>
          {hoveredSection.type && (
            <div className="font-mono">
              <span className="font-bold">Type:</span> {hoveredSection.type}
            </div>
          )}
          {hoveredSection.description && (
            <div className="mt-1 text-xs">
              {hoveredSection.description}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BinaryVisualizer3D;
