import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { FaTerminal, FaKeyboard, FaArrowRight } from 'react-icons/fa';

/**
 * 3D Terminal Component
 * 
 * A realistic 3D terminal interface for challenge simulators
 */
const Terminal3D = ({ 
  initialCommands = [], 
  onCommandSubmit,
  readOnly = false,
  terminalType = 'bash',
  backgroundColor = '#000',
  textColor = '#33ff33',
  prompt = '$ ',
  welcomeMessage = 'Welcome to CyberForce Terminal',
  height = '400px'
}) => {
  const { darkMode } = useGlobalTheme();
  const containerRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const rendererRef = useRef(null);
  const controlsRef = useRef(null);
  const terminalRef = useRef(null);
  const [command, setCommand] = useState('');
  const [history, setHistory] = useState(initialCommands);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isAnimating, setIsAnimating] = useState(true);
  const [is3DMode, setIs3DMode] = useState(true);

  // Initialize 3D scene
  useEffect(() => {
    if (!containerRef.current || !is3DMode) return;

    // Create scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0a0a0a);
    sceneRef.current = scene;

    // Create camera
    const camera = new THREE.PerspectiveCamera(
      75,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.z = 5;
    cameraRef.current = camera;

    // Create renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Add controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.5;
    controls.enableZoom = true;
    controls.enablePan = false;
    controls.autoRotate = true;
    controls.autoRotateSpeed = 0.5;
    controlsRef.current = controls;

    // Create terminal screen
    const screenGeometry = new THREE.BoxGeometry(6, 3.5, 0.1);
    const screenMaterial = new THREE.MeshPhongMaterial({
      color: 0x111111,
      specular: 0x111111,
      shininess: 30
    });
    const screen = new THREE.Mesh(screenGeometry, screenMaterial);
    scene.add(screen);

    // Create terminal frame
    const frameGeometry = new THREE.BoxGeometry(6.2, 3.7, 0.2);
    const frameMaterial = new THREE.MeshPhongMaterial({
      color: 0x333333,
      specular: 0x222222,
      shininess: 20
    });
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);
    frame.position.z = -0.1;
    scene.add(frame);

    // Create terminal base
    const baseGeometry = new THREE.BoxGeometry(4, 0.3, 2);
    const baseMaterial = new THREE.MeshPhongMaterial({
      color: 0x222222,
      specular: 0x111111,
      shininess: 10
    });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.y = -2;
    base.position.z = 0.5;
    scene.add(base);

    // Add lights
    const ambientLight = new THREE.AmbientLight(0x404040, 1);
    scene.add(ambientLight);

    const keyLight = new THREE.DirectionalLight(0xffffff, 0.7);
    keyLight.position.set(1, 1, 2);
    scene.add(keyLight);

    const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
    fillLight.position.set(-1, 0.5, 1);
    scene.add(fillLight);

    const backLight = new THREE.DirectionalLight(0x0088ff, 0.2);
    backLight.position.set(0, -1, -1);
    scene.add(backLight);

    // Create HTML terminal content
    const terminalContent = document.createElement('div');
    terminalContent.style.position = 'absolute';
    terminalContent.style.top = '50%';
    terminalContent.style.left = '50%';
    terminalContent.style.transform = 'translate(-50%, -50%)';
    terminalContent.style.width = '80%';
    terminalContent.style.height = '70%';
    terminalContent.style.overflow = 'hidden';
    terminalContent.style.color = textColor;
    terminalContent.style.fontFamily = 'monospace';
    terminalContent.style.fontSize = '12px';
    terminalContent.style.whiteSpace = 'pre-wrap';
    terminalContent.style.textAlign = 'left';
    terminalContent.style.padding = '10px';
    terminalContent.style.boxSizing = 'border-box';
    terminalContent.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    terminalContent.style.border = '1px solid #333';
    terminalContent.style.borderRadius = '5px';
    terminalContent.style.pointerEvents = 'none';
    containerRef.current.appendChild(terminalContent);
    terminalRef.current = terminalContent;

    // Animation loop
    const animate = () => {
      if (!isAnimating) return;
      requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return;
      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement);
      }
      if (containerRef.current && terminalContent) {
        containerRef.current.removeChild(terminalContent);
      }
      window.removeEventListener('resize', handleResize);
      isAnimating && cancelAnimationFrame(animate);
    };
  }, [is3DMode, isAnimating, textColor]);

  // Update terminal content
  useEffect(() => {
    if (!terminalRef.current) return;
    
    const content = [
      `<span style="color: #33ccff">${welcomeMessage}</span>`,
      `<span style="color: #888888">Type 'help' for available commands</span>`,
      '',
      ...history.map(item => {
        if (item.type === 'command') {
          return `<span style="color: #33ccff">${prompt}</span> <span style="color: #ffffff">${item.text}</span>`;
        } else if (item.type === 'error') {
          return `<span style="color: #ff3333">${item.text}</span>`;
        } else if (item.type === 'success') {
          return `<span style="color: #33ff33">${item.text}</span>`;
        } else if (item.type === 'info') {
          return `<span style="color: #ffcc33">${item.text}</span>`;
        } else {
          return `<span style="color: #cccccc">${item.text}</span>`;
        }
      }),
      `<span style="color: #33ccff">${prompt}</span> <span style="color: #ffffff">${command}</span><span class="cursor" style="animation: blink 1s infinite; color: #ffffff">▋</span>`
    ].join('<br>');
    
    terminalRef.current.innerHTML = content;
  }, [history, command, prompt, welcomeMessage]);

  // Handle command submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!command.trim()) return;
    
    const newHistory = [...history, { type: 'command', text: command }];
    setHistory(newHistory);
    
    if (onCommandSubmit) {
      const result = onCommandSubmit(command);
      if (result) {
        setHistory([...newHistory, ...result]);
      }
    }
    
    setCommand('');
    setHistoryIndex(-1);
  };

  // Handle key navigation through command history
  const handleKeyDown = (e) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      const commandHistory = history.filter(item => item.type === 'command');
      if (historyIndex < commandHistory.length - 1) {
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        setCommand(commandHistory[commandHistory.length - 1 - newIndex].text);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setCommand(history.filter(item => item.type === 'command')[history.filter(item => item.type === 'command').length - 1 - newIndex].text);
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setCommand('');
      }
    }
  };

  return (
    <div className="relative">
      <div 
        ref={containerRef} 
        className={`relative overflow-hidden rounded-lg border ${darkMode ? 'border-gray-700' : 'border-gray-300'}`}
        style={{ height }}
      >
        {!is3DMode && (
          <div 
            className={`w-full h-full overflow-auto p-4 font-mono text-sm ${darkMode ? 'bg-black text-green-400' : 'bg-gray-900 text-green-400'}`}
          >
            {welcomeMessage && <div className="text-blue-400 mb-2">{welcomeMessage}</div>}
            {history.map((item, index) => (
              <div 
                key={index} 
                className={`mb-1 ${
                  item.type === 'error' ? 'text-red-400' :
                  item.type === 'success' ? 'text-green-400' :
                  item.type === 'info' ? 'text-yellow-400' :
                  item.type === 'command' ? 'text-white' : 'text-gray-400'
                }`}
              >
                {item.type === 'command' && <span className="text-blue-400">{prompt}</span>}
                {item.text}
              </div>
            ))}
            {!readOnly && (
              <form onSubmit={handleSubmit} className="flex items-center mt-2">
                <span className="text-blue-400 mr-1">{prompt}</span>
                <input
                  type="text"
                  value={command}
                  onChange={(e) => setCommand(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="flex-1 bg-transparent border-none outline-none text-white"
                  autoFocus
                />
              </form>
            )}
          </div>
        )}
      </div>
      
      <div className="absolute top-2 right-2 flex space-x-2">
        <button
          onClick={() => setIs3DMode(!is3DMode)}
          className={`p-2 rounded-full ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-200 hover:bg-gray-300'}`}
          title={is3DMode ? "Switch to 2D mode" : "Switch to 3D mode"}
        >
          {is3DMode ? <FaTerminal /> : <FaKeyboard />}
        </button>
        <button
          onClick={() => setIsAnimating(!isAnimating)}
          className={`p-2 rounded-full ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-200 hover:bg-gray-300'}`}
          title={isAnimating ? "Pause animation" : "Resume animation"}
        >
          {isAnimating ? "⏸️" : "▶️"}
        </button>
      </div>
    </div>
  );
};

export default Terminal3D;
