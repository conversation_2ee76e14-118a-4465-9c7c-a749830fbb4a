import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaTrophy, FaMedal, FaAward, FaClock, FaChartLine, FaFilter, FaSearch, FaUserCircle } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';

/**
 * ChallengeLeaderboard Component
 *
 * A comprehensive leaderboard for challenges with multiple ranking criteria,
 * filtering options, and user profile integration.
 */
const ChallengeLeaderboard = ({ challengeId }) => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const [leaderboardData, setLeaderboardData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [rankingCriteria, setRankingCriteria] = useState('total_score');
  const [timeFrame, setTimeFrame] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [userRank, setUserRank] = useState(null);

  // Ranking criteria options
  const rankingOptions = [
    { id: 'total_score', name: 'Total Score', description: 'Overall performance score' },
    { id: 'completion_time', name: 'Completion Time', description: 'Fastest completion times' },
    { id: 'approach_score', name: 'Approach Score', description: 'Quality of solution approach' },
    { id: 'attempts', name: 'Fewest Attempts', description: 'Solved with minimal attempts' }
  ];

  // Time frame options
  const timeFrameOptions = [
    { id: 'all', name: 'All Time' },
    { id: 'week', name: 'This Week' },
    { id: 'month', name: 'This Month' }
  ];

  // Fetch leaderboard data from database
  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        setIsLoading(true);

        // Fetch from Supabase
        let query = supabase
          .from('leaderboards')
          .select(`
            *,
            user:user_id (
              id,
              username,
              avatar_url
            )
          `);

        // Add challenge filter if provided
        if (challengeId) {
          query = query.eq('challenge_id', challengeId);
        }

        // Add time frame filter
        if (timeFrame === 'week') {
          query = query.gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());
        } else if (timeFrame === 'month') {
          query = query.gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());
        }

        // Add sorting based on ranking criteria
        if (rankingCriteria === 'completion_time' || rankingCriteria === 'attempts') {
          query = query.order(rankingCriteria, { ascending: true }); // Lower is better
        } else {
          query = query.order(rankingCriteria, { ascending: false }); // Higher is better
        }

        const { data, error } = await query;

        if (error) throw error;

        if (data && data.length > 0) {
          // Add rank to each entry
          const rankedData = data.map((entry, index) => ({
            ...entry,
            rank: index + 1
          }));

          setLeaderboardData(rankedData);

          // Find user's rank
          if (user) {
            const userEntry = rankedData.find(entry => entry.user_id === user.id);
            setUserRank(userEntry || null);
          }
        } else {
          // If no data in database, use empty array
          setLeaderboardData([]);
        }
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
        setError(error.message);

        // Fallback to empty array
        setLeaderboardData([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLeaderboard();
  }, [challengeId, rankingCriteria, timeFrame, user]);

  // Format time (seconds to MM:SS)
  const formatTime = (seconds) => {
    if (!seconds) return 'N/A';

    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;

    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Filter leaderboard data based on search query
  const filteredData = leaderboardData.filter(entry => {
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();
    return (
      entry.user?.username?.toLowerCase().includes(searchLower) ||
      entry.user?.email?.toLowerCase().includes(searchLower)
    );
  });

  // Get trophy icon based on rank
  const getTrophyIcon = (rank) => {
    switch (rank) {
      case 1:
        return <FaTrophy className="text-yellow-400" />;
      case 2:
        return <FaMedal className="text-gray-400" />;
      case 3:
        return <FaMedal className="text-amber-600" />;
      default:
        return <FaAward className="text-blue-400" />;
    }
  };

  return (
    <div className={`rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} p-6`}>
      <h2 className="text-xl font-bold mb-4">Challenge Leaderboard</h2>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6">
        {/* Ranking Criteria */}
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium mb-2">Ranking Criteria</label>
          <select
            value={rankingCriteria}
            onChange={(e) => setRankingCriteria(e.target.value)}
            className={`w-full p-2 rounded-lg ${
              darkMode
                ? 'bg-gray-700 text-white border-gray-600'
                : 'bg-white text-gray-900 border-gray-300'
            } border`}
          >
            {rankingOptions.map(option => (
              <option key={option.id} value={option.id}>{option.name}</option>
            ))}
          </select>
        </div>

        {/* Time Frame */}
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium mb-2">Time Frame</label>
          <select
            value={timeFrame}
            onChange={(e) => setTimeFrame(e.target.value)}
            className={`w-full p-2 rounded-lg ${
              darkMode
                ? 'bg-gray-700 text-white border-gray-600'
                : 'bg-white text-gray-900 border-gray-300'
            } border`}
          >
            {timeFrameOptions.map(option => (
              <option key={option.id} value={option.id}>{option.name}</option>
            ))}
          </select>
        </div>

        {/* Search */}
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium mb-2">Search Users</label>
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search by username..."
              className={`w-full p-2 pl-10 rounded-lg ${
                darkMode
                  ? 'bg-gray-700 text-white border-gray-600'
                  : 'bg-white text-gray-900 border-gray-300'
              } border`}
            />
            <FaSearch className="absolute left-3 top-3 text-gray-400" />
          </div>
        </div>
      </div>

      {/* User's Rank */}
      {userRank && (
        <div className={`mb-6 p-4 rounded-lg ${darkMode ? 'bg-blue-900/20' : 'bg-blue-50'} border ${darkMode ? 'border-blue-800' : 'border-blue-200'}`}>
          <h3 className="font-medium mb-2">Your Ranking</h3>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mr-3">
                {getTrophyIcon(userRank.rank)}
              </div>
              <div>
                <p className="font-bold">Rank #{userRank.rank}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {rankingCriteria === 'total_score' && `Score: ${userRank.total_score}`}
                  {rankingCriteria === 'completion_time' && `Time: ${formatTime(userRank.completion_time)}`}
                  {rankingCriteria === 'approach_score' && `Approach: ${userRank.approach_score}/100`}
                  {rankingCriteria === 'attempts' && `Attempts: ${userRank.attempts || 'N/A'}`}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Leaderboard Table */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-500 mb-2">Error loading leaderboard</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      ) : filteredData.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`${darkMode ? 'border-gray-700' : 'border-gray-200'} border-b`}>
                <th className="px-4 py-2 text-left">Rank</th>
                <th className="px-4 py-2 text-left">User</th>
                <th className="px-4 py-2 text-right">
                  {rankingCriteria === 'total_score' && 'Score'}
                  {rankingCriteria === 'completion_time' && 'Time'}
                  {rankingCriteria === 'approach_score' && 'Approach'}
                  {rankingCriteria === 'attempts' && 'Attempts'}
                </th>
                <th className="px-4 py-2 text-right">Completed</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.map((entry) => (
                <motion.tr
                  key={entry.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className={`${
                    entry.user_id === user?.id
                      ? darkMode ? 'bg-blue-900/20' : 'bg-blue-50'
                      : ''
                  } ${darkMode ? 'border-gray-700' : 'border-gray-200'} border-b`}
                >
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center mr-2">
                        {getTrophyIcon(entry.rank)}
                      </div>
                      <span className="font-bold">{entry.rank}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center mr-2">
                        {entry.user?.avatar_url ? (
                          <img
                            src={entry.user.avatar_url}
                            alt={entry.user.username}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <FaUserCircle className="text-gray-400" />
                        )}
                      </div>
                      <span>{entry.user?.username || 'Anonymous'}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-right font-medium">
                    {rankingCriteria === 'total_score' && entry.total_score}
                    {rankingCriteria === 'completion_time' && formatTime(entry.completion_time)}
                    {rankingCriteria === 'approach_score' && `${entry.approach_score}/100`}
                    {rankingCriteria === 'attempts' && (entry.attempts || 'N/A')}
                  </td>
                  <td className="px-4 py-3 text-right text-gray-500 dark:text-gray-400">
                    {new Date(entry.created_at).toLocaleDateString()}
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">No leaderboard entries found</p>
        </div>
      )}
    </div>
  );
};

export default ChallengeLeaderboard;
