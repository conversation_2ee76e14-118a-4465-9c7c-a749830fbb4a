import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>ock, <PERSON>aU<PERSON>lock, FaLightbulb, FaCheck, FaInfoCircle, FaExclamationTriangle, FaDatabase, FaKey, FaServer } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import Terminal3D from '../3d/Terminal3D';
import BinaryVisualizer3D from '../3d/BinaryVisualizer3D';
import { supabase } from '../../../lib/supabase';

const PasswordCrackingSimulator = ({ onComplete, challengeId }) => {
  const { darkMode } = useGlobalTheme();
  const [currentHash, setCurrentHash] = useState(0);
  const [password, setPassword] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [message, setMessage] = useState(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [hintLevel, setHintLevel] = useState(0);
  const [startTime] = useState(new Date());
  const [crackedPasswords, setCrackedPasswords] = useState([]);
  const [activeTab, setActiveTab] = useState('terminal'); // 'terminal', 'visualizer', 'info'
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'info', text: 'Password Cracking Challenge initialized...' },
    { type: 'info', text: 'Target: Password Hash Database' },
    { type: 'info', text: 'Objective: Crack all three password hashes' },
    { type: 'system', text: 'Loading hash #1: 5f4dcc3b5aa765d61d8327deb882cf99 (MD5)' }
  ]);

  // Binary data for visualization
  const [binaryData, setBinaryData] = useState([]);

  // Generate binary data for visualization based on current hash
  useEffect(() => {
    const generateBinaryData = () => {
      const currentHashData = hashes[currentHash];
      const hashBytes = [];

      // Convert hash to binary representation for visualization
      if (currentHashData.type === 'MD5' || currentHashData.type === 'SHA-1') {
        // For MD5 and SHA-1, convert hex to binary
        for (let i = 0; i < currentHashData.hash.length; i += 2) {
          const byte = currentHashData.hash.substring(i, i + 2);
          hashBytes.push({
            offset: i / 2,
            value: byte,
            type: byte === '00' ? 'null' :
                  byte === 'ff' || byte === 'FF' ? 'special' :
                  parseInt(byte, 16) > 127 ? 'high' : 'normal',
            description: `Byte at offset 0x${(i/2).toString(16).padStart(2, '0')}: 0x${byte}`
          });
        }
      } else if (currentHashData.type === 'bcrypt') {
        // For bcrypt, use the ASCII values of the hash
        for (let i = 0; i < currentHashData.hash.length; i++) {
          const charCode = currentHashData.hash.charCodeAt(i).toString(16).padStart(2, '0');
          hashBytes.push({
            offset: i,
            value: charCode,
            type: charCode === '24' ? 'special' : // $ character
                  charCode === '79' ? 'special' : // y character
                  charCode === '31' ? 'special' : // 1 character
                  charCode === '30' ? 'special' : // 0 character
                  'normal',
            description: `Character '${currentHashData.hash[i]}' at offset 0x${i.toString(16).padStart(2, '0')}: 0x${charCode}`
          });
        }
      }

      setBinaryData(hashBytes);
    };

    generateBinaryData();
  }, [currentHash]);

  // Track attempt in database
  const trackAttempt = async (isCorrect) => {
    if (!challengeId) return;

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) return;

      const { error } = await supabase
        .from('challenge_attempts')
        .insert({
          challenge_id: challengeId,
          user_id: session.user.id,
          is_correct: isCorrect,
          attempt_number: attempts,
          time_spent: Math.floor((new Date() - startTime) / 1000),
          attempted_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error tracking attempt:', error);
      }
    } catch (err) {
      console.error('Error in trackAttempt:', err);
    }
  };

  const hashes = [
    {
      hash: '5f4dcc3b5aa765d61d8327deb882cf99',
      type: 'MD5',
      password: 'password',
      hint1: 'This is one of the most common passwords used.',
      hint2: 'It\'s literally the word "password".',
      description: 'MD5 is a widely used hash function that produces a 128-bit hash value. It\'s no longer considered secure for password storage due to its vulnerability to collision attacks and the speed at which it can be brute-forced.',
      visualization: {
        colorMapping: {
          'null': 0x000000,
          'special': 0xff0000,
          'high': 0x0000ff,
          'normal': 0x00ff00
        }
      }
    },
    {
      hash: '5baa61e4c9b93f3f0682250b6cf8331b7ee68fd8',
      type: 'SHA-1',
      password: 'password1',
      hint1: 'This password is similar to the first one, but with a number added.',
      hint2: 'Try adding a "1" to the end of the first password.',
      description: 'SHA-1 is a cryptographic hash function that produces a 160-bit hash value. Like MD5, it\'s no longer considered secure for password storage due to demonstrated collision attacks.',
      visualization: {
        colorMapping: {
          'null': 0x000000,
          'special': 0xff0000,
          'high': 0x0000ff,
          'normal': 0x00ff00
        }
      }
    },
    {
      hash: '$2y$10$F9hXnLl7IVhMJ.Zyj0jGAO38BXCxV/N4NLnLVVEx7hHQmWkr0qcp.',
      type: 'bcrypt',
      password: 'security42',
      hint1: 'This is a common word related to this field of study, followed by a two-digit number.',
      hint2: 'The word is "security" and the number is the "Answer to the Ultimate Question of Life, the Universe, and Everything" in The Hitchhiker\'s Guide to the Galaxy.',
      description: 'bcrypt is a password hashing function designed to be slow and computationally expensive, making it resistant to brute-force attacks. The "$2y$10$" prefix indicates the algorithm version and cost factor.',
      visualization: {
        colorMapping: {
          'null': 0x000000,
          'special': 0xff00ff,
          'high': 0x0000ff,
          'normal': 0x00ff00
        }
      }
    }
  ];

  const addToConsole = (type, text) => {
    setConsoleOutput(prev => [...prev, { type, text }]);
    // Auto-scroll console to bottom
    setTimeout(() => {
      const consoleElement = document.getElementById('password-console');
      if (consoleElement) {
        consoleElement.scrollTop = consoleElement.scrollHeight;
      }
    }, 100);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setAttempts(prev => prev + 1);

    // Simulate cracking attempt
    addToConsole('system', `Attempting to crack ${hashes[currentHash].type} hash with password: ${password}`);

    setTimeout(() => {
      const isCorrect = password.toLowerCase() === hashes[currentHash].password.toLowerCase();

      // Track attempt in database
      trackAttempt(isCorrect);

      if (isCorrect) {
        // Correct password
        addToConsole('success', `Hash cracked! Password: ${password}`);

        // Add to cracked passwords
        setCrackedPasswords(prev => [...prev, password]);

        // Highlight the cracked hash in the binary visualizer
        const updatedBinaryData = binaryData.map(byte => ({
          ...byte,
          type: byte.type === 'normal' ? 'cracked' : byte.type
        }));
        setBinaryData(updatedBinaryData);

        if (currentHash < hashes.length - 1) {
          // Move to next hash
          setTimeout(() => {
            setCurrentHash(prev => prev + 1);
            setPassword('');
            addToConsole('system', `Loading hash #${currentHash + 2}: ${hashes[currentHash + 1].hash} (${hashes[currentHash + 1].type})`);
            setMessage({ type: 'success', text: `Great job! You've cracked hash #${currentHash + 1}. Now try the next one.` });
          }, 1500);
        } else {
          // All hashes cracked
          setIsSuccess(true);
          setMessage({ type: 'success', text: 'Congratulations! You\'ve cracked all the password hashes!' });

          // Calculate time spent
          const endTime = new Date();
          const timeSpent = Math.floor((endTime - startTime) / 1000); // in seconds
          const minutes = Math.floor(timeSpent / 60);
          const seconds = timeSpent % 60;
          const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

          // Record completion in database
          try {
            const recordCompletion = async () => {
              if (!challengeId) return;

              const { data: { session } } = await supabase.auth.getSession();
              if (!session?.user) return;

              // Check if already completed
              const { data: existingCompletion } = await supabase
                .from('challenge_completions')
                .select('id')
                .eq('challenge_id', challengeId)
                .eq('user_id', session.user.id)
                .single();

              if (!existingCompletion) {
                // Record completion
                await supabase
                  .from('challenge_completions')
                  .insert({
                    challenge_id: challengeId,
                    user_id: session.user.id,
                    score: 100,
                    time_spent: timeSpent,
                    attempt_count: attempts,
                    completed_at: new Date().toISOString()
                  });
              }
            };

            recordCompletion();
          } catch (err) {
            console.error('Error recording completion:', err);
          }

          // Call the onComplete callback with challenge results
          if (onComplete) {
            onComplete({
              success: true,
              flag: `flag{${crackedPasswords.join('_')}_master_cracker}`,
              attempts: attempts + 1,
              timeSpent: formattedTime,
            });
          }
        }
      } else {
        // Incorrect password
        addToConsole('error', 'Incorrect password. Try again.');
        setMessage({ type: 'error', text: 'Incorrect password. Try again.' });
      }
    }, 800); // Simulate processing delay
  };

  const getNextHint = () => {
    setHintLevel(prev => prev + 1);
    setShowHint(true);

    // Add hint request to console
    addToConsole('system', 'Hint requested...');

    if (hintLevel % 2 === 0) {
      // First hint for current hash
      addToConsole('hint', hashes[currentHash].hint1);
    } else {
      // Second hint for current hash
      addToConsole('hint', hashes[currentHash].hint2);
    }
  };

  // Auto-scroll console on new messages
  useEffect(() => {
    const consoleElement = document.getElementById('password-console');
    if (consoleElement) {
      consoleElement.scrollTop = consoleElement.scrollHeight;
    }
  }, [consoleOutput]);

  // Handle terminal commands
  const handleTerminalCommand = (command) => {
    const cmd = command.trim().toLowerCase();
    const results = [];

    if (cmd === 'help') {
      results.push({ type: 'info', text: 'Available commands:' });
      results.push({ type: 'info', text: '  help - Show this help message' });
      results.push({ type: 'info', text: '  info - Show information about the current hash' });
      results.push({ type: 'info', text: '  crack <password> - Attempt to crack the current hash with the given password' });
      results.push({ type: 'info', text: '  hint - Get a hint for the current hash' });
      results.push({ type: 'info', text: '  status - Show current progress' });
    } else if (cmd === 'info') {
      results.push({ type: 'info', text: `Hash type: ${hashes[currentHash].type}` });
      results.push({ type: 'info', text: `Hash value: ${hashes[currentHash].hash}` });
      results.push({ type: 'info', text: hashes[currentHash].description });
    } else if (cmd.startsWith('crack ')) {
      const pass = cmd.substring(6);
      results.push({ type: 'system', text: `Attempting to crack ${hashes[currentHash].type} hash with password: ${pass}` });

      if (pass.toLowerCase() === hashes[currentHash].password.toLowerCase()) {
        results.push({ type: 'success', text: `Hash cracked! Password: ${pass}` });

        // Set the password and submit
        setPassword(pass);
        setTimeout(() => {
          handleSubmit({ preventDefault: () => {} });
        }, 500);
      } else {
        results.push({ type: 'error', text: 'Incorrect password. Try again.' });
      }
    } else if (cmd === 'hint') {
      getNextHint();
      results.push({ type: 'system', text: 'Hint requested...' });
      results.push({ type: 'hint', text: hintLevel % 2 === 0 ? hashes[currentHash].hint1 : hashes[currentHash].hint2 });
    } else if (cmd === 'status') {
      results.push({ type: 'info', text: `Current progress: ${Math.round((crackedPasswords.length / hashes.length) * 100)}%` });
      results.push({ type: 'info', text: `Hashes cracked: ${crackedPasswords.length}/${hashes.length}` });
      results.push({ type: 'info', text: `Current hash: #${currentHash + 1} (${hashes[currentHash].type})` });
      results.push({ type: 'info', text: `Attempts: ${attempts}` });
    } else {
      results.push({ type: 'error', text: `Unknown command: ${cmd}. Type 'help' for available commands.` });
    }

    return results;
  };

  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
      {/* Challenge Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex justify-between items-center`}>
        <h2 className="text-xl font-bold flex items-center">
          {isSuccess ? <FaUnlock className="text-green-500 mr-2" /> : <FaLock className="text-red-500 mr-2" />}
          Password Cracking Challenge
        </h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={getNextHint}
            className={`px-3 py-1 rounded-lg flex items-center ${
              darkMode
                ? 'bg-gray-800 hover:bg-gray-700 text-gray-300'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
            }`}
          >
            <FaLightbulb className="mr-1 text-yellow-500" /> Get Hint
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className={`flex border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
        <button
          onClick={() => setActiveTab('terminal')}
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === 'terminal'
              ? darkMode
                ? 'bg-gray-800 text-white border-b-2 border-blue-500'
                : 'bg-white text-blue-600 border-b-2 border-blue-500'
              : darkMode
                ? 'text-gray-400 hover:text-gray-200'
                : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <FaTerminal className="mr-2" /> Terminal
        </button>
        <button
          onClick={() => setActiveTab('visualizer')}
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === 'visualizer'
              ? darkMode
                ? 'bg-gray-800 text-white border-b-2 border-blue-500'
                : 'bg-white text-blue-600 border-b-2 border-blue-500'
              : darkMode
                ? 'text-gray-400 hover:text-gray-200'
                : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <FaDatabase className="mr-2" /> Hash Visualizer
        </button>
        <button
          onClick={() => setActiveTab('info')}
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === 'info'
              ? darkMode
                ? 'bg-gray-800 text-white border-b-2 border-blue-500'
                : 'bg-white text-blue-600 border-b-2 border-blue-500'
              : darkMode
                ? 'text-gray-400 hover:text-gray-200'
                : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <FaInfoCircle className="mr-2" /> Info
        </button>
      </div>

      {/* Challenge Content */}
      <div className="p-6">
        {/* Status Message */}
        {message && (
          <div className={`mb-4 p-3 rounded-lg ${
            message.type === 'success'
              ? darkMode ? 'bg-green-900/20 text-green-300 border border-green-900/30' : 'bg-green-100 text-green-800 border border-green-200'
              : message.type === 'warning'
                ? darkMode ? 'bg-yellow-900/20 text-yellow-300 border border-yellow-900/30' : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                : darkMode ? 'bg-red-900/20 text-red-300 border border-red-900/30' : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-start">
              {message.type === 'success' ? (
                <FaCheck className="mt-1 mr-2" />
              ) : message.type === 'warning' ? (
                <FaExclamationTriangle className="mt-1 mr-2" />
              ) : (
                <FaInfoCircle className="mt-1 mr-2" />
              )}
              <div>{message.text}</div>
            </div>
          </div>
        )}

        {/* Tab Content */}
        <div className="mb-6">
          {/* Terminal Tab */}
          {activeTab === 'terminal' && (
            <div>
              <div className="mb-6">
                <Terminal3D
                  initialCommands={consoleOutput}
                  onCommandSubmit={handleTerminalCommand}
                  readOnly={isSuccess}
                  terminalType="bash"
                  prompt="hacker@cyberforce:~$ "
                  welcomeMessage={`Password Cracking Challenge - Hash ${currentHash + 1}/${hashes.length} (${hashes[currentHash].type})`}
                  height="350px"
                />
              </div>

              <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0F172A] border-gray-800' : 'bg-white border-gray-300'} border`}>
                <div className="text-center mb-4">
                  <h3 className="text-xl font-bold">Password Hash Cracker</h3>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Hash {currentHash + 1} of {hashes.length}: {hashes[currentHash].type}
                  </p>
                </div>

                <div className={`p-3 mb-4 rounded ${darkMode ? 'bg-black' : 'bg-gray-900'} font-mono text-green-400 overflow-x-auto`}>
                  {hashes[currentHash].hash}
                </div>

                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <label className={`block mb-2 text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Password Guess
                    </label>
                    <input
                      type="text"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className={`w-full p-2.5 rounded-lg ${
                        darkMode
                          ? 'bg-gray-800 border-gray-700 text-white'
                          : 'bg-gray-50 border-gray-300 text-gray-900'
                      } border`}
                      placeholder="Enter password guess"
                      disabled={isSuccess}
                    />
                  </div>

                  <button
                    type="submit"
                    className={`w-full py-2.5 px-5 rounded-lg ${
                      isSuccess
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                    disabled={isSuccess}
                  >
                    {isSuccess ? 'All Hashes Cracked!' : 'Crack Hash'}
                  </button>
                </form>

                {/* Progress Indicator */}
                <div className="mt-4">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Progress</span>
                    <span>{Math.round((crackedPasswords.length / hashes.length) * 100)}%</span>
                  </div>
                  <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
                    <div
                      className="h-2 rounded-full bg-green-500"
                      style={{ width: `${(crackedPasswords.length / hashes.length) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Visualizer Tab */}
          {activeTab === 'visualizer' && (
            <div>
              <div className="mb-6">
                <BinaryVisualizer3D
                  binaryData={binaryData}
                  height="350px"
                  highlightedSections={[]}
                  colorMapping={hashes[currentHash].visualization.colorMapping}
                />
              </div>

              <div className={`p-4 rounded-lg ${darkMode ? 'bg-[#0F172A] border-gray-800' : 'bg-white border-gray-300'} border`}>
                <h3 className="text-xl font-bold mb-2">Hash Structure Analysis</h3>
                <p className={`mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Visualizing the binary structure of the {hashes[currentHash].type} hash. Each cube represents a byte in the hash.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Hash Information</h4>
                    <ul className={`list-disc pl-5 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <li>Type: {hashes[currentHash].type}</li>
                      <li>Length: {hashes[currentHash].hash.length} characters</li>
                      <li>
                        Byte Count: {
                          hashes[currentHash].type === 'MD5' || hashes[currentHash].type === 'SHA-1'
                            ? Math.floor(hashes[currentHash].hash.length / 2)
                            : hashes[currentHash].hash.length
                        }
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Color Legend</h4>
                    <ul className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <li className="flex items-center mb-1">
                        <div className="w-4 h-4 bg-black mr-2"></div>
                        <span>Null bytes (0x00)</span>
                      </li>
                      <li className="flex items-center mb-1">
                        <div className="w-4 h-4 bg-red-600 mr-2"></div>
                        <span>Special bytes (0xFF)</span>
                      </li>
                      <li className="flex items-center mb-1">
                        <div className="w-4 h-4 bg-blue-600 mr-2"></div>
                        <span>High value bytes (&gt; 127)</span>
                      </li>
                      <li className="flex items-center">
                        <div className="w-4 h-4 bg-green-600 mr-2"></div>
                        <span>Normal bytes</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Info Tab */}
          {activeTab === 'info' && (
            <div>
              {/* Challenge Description */}
              <div className={`mb-6 p-4 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
                <h3 className="font-bold mb-2">Challenge Scenario:</h3>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-4`}>
                  You have obtained a file containing password hashes from a security breach. Your task is to crack these hashes to reveal the original passwords.
                </p>
                <div className="flex items-center">
                  <FaInfoCircle className={`mr-2 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
                  <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>
                    Password cracking is the process of recovering passwords from data that has been stored in or transmitted in a hashed form.
                  </p>
                </div>
              </div>

              {/* Hash Information */}
              <div className={`mb-6 p-4 rounded-lg ${darkMode ? 'bg-[#0F172A] border-gray-800' : 'bg-white border-gray-300'} border`}>
                <h3 className="text-xl font-bold mb-4">Hash Information</h3>

                <div className="mb-4">
                  <h4 className="font-medium mb-2">Current Hash ({hashes[currentHash].type})</h4>
                  <div className={`p-3 rounded ${darkMode ? 'bg-black' : 'bg-gray-900'} font-mono text-green-400 overflow-x-auto mb-2`}>
                    {hashes[currentHash].hash}
                  </div>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {hashes[currentHash].description}
                  </p>
                </div>

                <div className="mb-4">
                  <h4 className="font-medium mb-2">Password Cracking Techniques</h4>
                  <ul className={`list-disc pl-5 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    <li><strong>Dictionary Attack</strong>: Tries common words and passwords from a predefined list</li>
                    <li><strong>Brute Force</strong>: Tries every possible combination of characters</li>
                    <li><strong>Rainbow Tables</strong>: Uses precomputed tables to reverse cryptographic hash functions</li>
                    <li><strong>Rule-based Attack</strong>: Applies common password creation patterns to dictionary words</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Progress</h4>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Hashes Cracked</span>
                    <span>{crackedPasswords.length}/{hashes.length}</span>
                  </div>
                  <div className={`h-2 w-full rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-200'}`}>
                    <div
                      className="h-2 rounded-full bg-green-500"
                      style={{ width: `${(crackedPasswords.length / hashes.length) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Hints Section */}
        {showHint && (
          <div className={`mt-4 p-4 rounded-lg ${
            darkMode ? 'bg-yellow-900/20 border-yellow-900/30' : 'bg-yellow-50 border-yellow-200'
          } border`}>
            <div className="flex items-start">
              <FaLightbulb className={`mt-1 mr-2 ${darkMode ? 'text-yellow-400' : 'text-yellow-600'}`} />
              <div>
                <h4 className={`font-medium ${darkMode ? 'text-yellow-300' : 'text-yellow-800'}`}>Hint {hintLevel}:</h4>
                <p className={darkMode ? 'text-yellow-200' : 'text-yellow-700'}>
                  {hintLevel % 2 === 1 ? hashes[currentHash].hint1 : hashes[currentHash].hint2}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Cracked Passwords */}
        {crackedPasswords.length > 0 && (
          <div className="mt-4">
            <h3 className={`text-lg font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Cracked Passwords:</h3>
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
              <ul className="space-y-2">
                {crackedPasswords.map((pass, index) => (
                  <li key={index} className="flex items-center">
                    <FaUnlock className="text-green-500 mr-2" />
                    <span className={`font-mono ${darkMode ? 'text-green-400' : 'text-green-600'}`}>{pass}</span>
                    {index < currentHash && (
                      <span className="ml-2 text-xs bg-green-500 text-white px-2 py-0.5 rounded">Cracked</span>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Success Message */}
        {isSuccess && (
          <div className={`mt-6 p-4 rounded-lg ${
            darkMode ? 'bg-green-900/20 border-green-900/30' : 'bg-green-100 border-green-200'
          } border`}>
            <div className="text-center">
              <h3 className={`text-xl font-bold mb-2 ${darkMode ? 'text-green-300' : 'text-green-800'}`}>
                Challenge Completed!
              </h3>
              <p className={`mb-3 ${darkMode ? 'text-green-200' : 'text-green-700'}`}>
                Congratulations! You've successfully cracked all the password hashes.
              </p>
              <div className={`p-3 rounded ${darkMode ? 'bg-black' : 'bg-gray-900'} font-mono text-green-400 mb-3`}>
                Flag: flag{'{' + crackedPasswords.join('_') + '_master_cracker}'}
              </div>

              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <div className="font-medium mb-1">Time Spent</div>
                  <div className="text-2xl">
                    {Math.floor(((new Date() - startTime) / 1000) / 60)}m {Math.floor(((new Date() - startTime) / 1000) % 60)}s
                  </div>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <div className="font-medium mb-1">Attempts</div>
                  <div className="text-2xl">{attempts}</div>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <div className="font-medium mb-1">Score</div>
                  <div className="text-2xl">100 points</div>
                </div>
              </div>

              <h4 className={`font-medium mt-4 mb-2 ${darkMode ? 'text-green-300' : 'text-green-800'}`}>What you learned:</h4>
              <ul className={`list-disc pl-5 text-left ${darkMode ? 'text-green-200' : 'text-green-700'}`}>
                <li>How to identify different types of password hashes</li>
                <li>Techniques for cracking password hashes of varying complexity</li>
                <li>The importance of strong, unique passwords</li>
                <li>Why modern hashing algorithms like bcrypt are more secure than older ones like MD5</li>
              </ul>

              <button
                onClick={() => {
                  // Reset the challenge
                  setCurrentHash(0);
                  setPassword('');
                  setAttempts(0);
                  setMessage(null);
                  setIsSuccess(false);
                  setShowHint(false);
                  setHintLevel(0);
                  setCrackedPasswords([]);
                  setConsoleOutput([
                    { type: 'info', text: 'Password Cracking Challenge restarted...' },
                    { type: 'info', text: 'Target: Password Hash Database' },
                    { type: 'info', text: 'Objective: Crack all three password hashes' },
                    { type: 'system', text: 'Loading hash #1: 5f4dcc3b5aa765d61d8327deb882cf99 (MD5)' }
                  ]);
                }}
                className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
              >
                Reset Challenge
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PasswordCrackingSimulator;
