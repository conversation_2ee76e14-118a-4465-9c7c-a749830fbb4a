/**
 * Comprehensive Content Manager
 * 
 * Advanced admin interface for managing all learning content
 * including modules, sections, SVGs, quizzes, and interactive elements
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaBook, FaDatabase, FaUpload, FaDownload, FaSync, FaPlus, FaEdit, 
  FaTrash, FaEye, FaCode, FaImage, FaPlay, FaQuestionCircle,
  FaCheckCircle, FaExclamationTriangle, FaInfoCircle, FaCog
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import ComprehensiveContentMigrationService from '../../services/ComprehensiveContentMigrationService';
import { supabase } from '../../config/supabaseClient';

const ComprehensiveContentManager = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [migrationStatus, setMigrationStatus] = useState(null);
  const [contentStats, setContentStats] = useState({
    totalPaths: 0,
    totalModules: 0,
    totalSections: 0,
    totalAssets: 0,
    totalQuizzes: 0,
    totalLabs: 0
  });
  const [learningPaths, setLearningPaths] = useState([]);
  const [selectedPath, setSelectedPath] = useState(null);
  const [modules, setModules] = useState([]);
  const [selectedModule, setSelectedModule] = useState(null);
  const [sections, setSections] = useState([]);

  // Load dashboard data
  useEffect(() => {
    loadContentStats();
    loadLearningPaths();
  }, []);

  const loadContentStats = async () => {
    try {
      const [pathsResult, modulesResult, sectionsResult, assetsResult] = await Promise.all([
        supabase.from('learning_paths').select('*', { count: 'exact', head: true }),
        supabase.from('learning_modules').select('*', { count: 'exact', head: true }),
        supabase.from('module_sections').select('*', { count: 'exact', head: true }),
        supabase.from('content_assets').select('*', { count: 'exact', head: true })
      ]);

      const quizzesResult = await supabase
        .from('module_sections')
        .select('*', { count: 'exact', head: true })
        .eq('content_type', 'quiz');

      const labsResult = await supabase
        .from('module_sections')
        .select('*', { count: 'exact', head: true })
        .eq('content_type', 'lab');

      setContentStats({
        totalPaths: pathsResult.count || 0,
        totalModules: modulesResult.count || 0,
        totalSections: sectionsResult.count || 0,
        totalAssets: assetsResult.count || 0,
        totalQuizzes: quizzesResult.count || 0,
        totalLabs: labsResult.count || 0
      });
    } catch (error) {
      console.error('Error loading content stats:', error);
    }
  };

  const loadLearningPaths = async () => {
    try {
      const { data, error } = await supabase
        .from('learning_paths')
        .select('*')
        .order('sort_order', { ascending: true });

      if (error) throw error;
      setLearningPaths(data || []);
    } catch (error) {
      console.error('Error loading learning paths:', error);
    }
  };

  const loadModules = async (pathId) => {
    try {
      const { data, error } = await supabase
        .from('learning_modules')
        .select('*')
        .eq('learning_path_id', pathId)
        .order('order_index', { ascending: true });

      if (error) throw error;
      setModules(data || []);
    } catch (error) {
      console.error('Error loading modules:', error);
    }
  };

  const loadSections = async (moduleId) => {
    try {
      const { data, error } = await supabase
        .from('module_sections')
        .select('*')
        .eq('module_id', moduleId)
        .order('order_index', { ascending: true });

      if (error) throw error;
      setSections(data || []);
    } catch (error) {
      console.error('Error loading sections:', error);
    }
  };

  const handleMigrateAllContent = async () => {
    try {
      setLoading(true);
      setMigrationStatus({ type: 'info', message: 'Starting comprehensive content migration...' });
      
      await ComprehensiveContentMigrationService.migrateAllContent();
      
      setMigrationStatus({ type: 'success', message: 'All content migrated successfully!' });
      await loadContentStats();
      await loadLearningPaths();
      
    } catch (error) {
      console.error('Migration error:', error);
      setMigrationStatus({ type: 'error', message: `Migration failed: ${error.message}` });
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FaDatabase },
    { id: 'paths', label: 'Learning Paths', icon: FaBook },
    { id: 'modules', label: 'Modules', icon: FaCode },
    { id: 'sections', label: 'Sections', icon: FaEdit },
    { id: 'assets', label: 'Assets', icon: FaImage },
    { id: 'migration', label: 'Migration Tools', icon: FaSync }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaBook className="text-2xl text-blue-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Learning Paths</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {contentStats.totalPaths}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaCode className="text-2xl text-green-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Modules</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {contentStats.totalModules}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaEdit className="text-2xl text-purple-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Sections</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {contentStats.totalSections}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaImage className="text-2xl text-yellow-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Assets (SVGs, Images)</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {contentStats.totalAssets}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaQuestionCircle className="text-2xl text-red-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Quizzes</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {contentStats.totalQuizzes}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaPlay className="text-2xl text-indigo-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Labs & Simulations</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {contentStats.totalLabs}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Migration Status */}
      {migrationStatus && (
        <div className={`p-4 rounded-lg border ${
          migrationStatus.type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
          migrationStatus.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
          'bg-blue-50 border-blue-200 text-blue-800'
        }`}>
          <div className="flex items-center">
            {migrationStatus.type === 'success' && <FaCheckCircle className="mr-2" />}
            {migrationStatus.type === 'error' && <FaExclamationTriangle className="mr-2" />}
            {migrationStatus.type === 'info' && <FaInfoCircle className="mr-2" />}
            {migrationStatus.message}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
        <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Content Management Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={handleMigrateAllContent}
            disabled={loading}
            className="flex items-center justify-center p-4 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            <FaSync className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            Migrate All Content
          </button>
          
          <button
            onClick={() => setActiveTab('paths')}
            className="flex items-center justify-center p-4 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
          >
            <FaPlus className="mr-2" />
            Manage Paths
          </button>
          
          <button
            onClick={() => setActiveTab('modules')}
            className="flex items-center justify-center p-4 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors"
          >
            <FaCode className="mr-2" />
            Manage Modules
          </button>
          
          <button
            onClick={() => setActiveTab('assets')}
            className="flex items-center justify-center p-4 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors"
          >
            <FaImage className="mr-2" />
            Manage Assets
          </button>
        </div>
      </div>
    </div>
  );

  const renderLearningPaths = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Learning Paths Management
        </h3>
        <button className="flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
          <FaPlus className="mr-2" />
          Add New Path
        </button>
      </div>

      <div className="grid gap-4">
        {learningPaths.map((path) => (
          <div key={path.id} className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center mb-2">
                  <span className="text-2xl mr-3">{path.metadata?.icon || '📚'}</span>
                  <h4 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {path.title}
                  </h4>
                </div>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                  {path.description}
                </p>
                <div className="flex items-center space-x-4">
                  <span className={`text-xs px-2 py-1 rounded ${
                    path.difficulty === 'beginner' ? 'bg-green-100 text-green-800' :
                    path.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {path.difficulty}
                  </span>
                  <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {path.modules_count} modules
                  </span>
                  <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {path.estimated_hours} hours
                  </span>
                  <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {path.category}
                  </span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button 
                  onClick={() => {
                    setSelectedPath(path);
                    loadModules(path.id);
                    setActiveTab('modules');
                  }}
                  className="p-2 text-blue-500 hover:bg-blue-50 rounded"
                >
                  <FaEye />
                </button>
                <button className="p-2 text-green-500 hover:bg-green-50 rounded">
                  <FaEdit />
                </button>
                <button className="p-2 text-red-500 hover:bg-red-50 rounded">
                  <FaTrash />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Comprehensive Content Manager
          </h1>
          <p className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-2`}>
            Advanced management for all learning content, modules, sections, and assets
          </p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-blue-500 text-white'
                  : darkMode
                    ? 'text-gray-400 hover:text-white hover:bg-gray-800'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'paths' && renderLearningPaths()}
            {activeTab === 'modules' && <div>Modules Management (Coming Soon)</div>}
            {activeTab === 'sections' && <div>Sections Management (Coming Soon)</div>}
            {activeTab === 'assets' && <div>Assets Management (Coming Soon)</div>}
            {activeTab === 'migration' && <div>Migration Tools (Coming Soon)</div>}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ComprehensiveContentManager;
