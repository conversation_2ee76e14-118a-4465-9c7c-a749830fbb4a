import React, { useState, useEffect } from 'react';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';
import {
  FaUsers, FaTrophy, FaBook, FaBell, FaChartLine, FaUserShield,
  FaCog, FaPlus, FaEdit, FaTrash, FaBan, FaUnlock, FaEnvelope,
  FaEye, FaUserTimes, FaUserCheck, FaCalendarAlt, FaCrown,
  FaDatabase, FaFileAlt, FaImage, FaCode, FaShieldAlt,
  FaNetworkWired, FaServer, FaLock, FaGraduationCap,
  FaHistory, FaUpload, FaDownload, FaSync, FaExclamationTriangle, FaCheck
} from 'react-icons/fa';
import NotificationTester from './NotificationTester';
import UserManagement from './UserManagement';
import ContentManagement from './ContentManagement';
import SystemAnalytics from './SystemAnalytics';
import MediaLibrary from './MediaLibrary';
import ActivityLogs from './ActivityLogs';

const ComprehensiveSuperAdminDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Check if user is super admin
  useEffect(() => {
    checkSuperAdminAccess();
  }, [user]);

  const checkSuperAdminAccess = async () => {
    if (!user) {
      setIsAuthorized(false);
      setLoading(false);
      return;
    }

    try {
      // First check if the super admin function exists
      const { data, error } = await supabase
        .rpc('is_super_admin', { user_uuid: user.id });

      if (error) {
        // If function doesn't exist, check if user is in admin_roles table
        console.log('Super admin function not found, checking admin_roles table...');
        const { data: adminRole, error: roleError } = await supabase
          .from('admin_roles')
          .select('role, is_active')
          .eq('user_id', user.id)
          .eq('role', 'super_admin')
          .eq('is_active', true)
          .single();

        if (roleError) {
          // If admin_roles table doesn't exist, allow access for your specific UUID
          console.log('Admin roles table not found, checking hardcoded UUID...');
          if (user.id === '5971f7c3-840f-4d2c-9931-db26d1978f5a') {
            setIsAuthorized(true);
            loadDashboardData();
            return;
          } else {
            setIsAuthorized(false);
            return;
          }
        }

        setIsAuthorized(adminRole && adminRole.role === 'super_admin');
      } else {
        setIsAuthorized(data === true);
      }

      if (data === true || (user.id === '5971f7c3-840f-4d2c-9931-db26d1978f5a')) {
        loadDashboardData();
      }
    } catch (error) {
      console.error('Error checking super admin access:', error);
      // Fallback: allow access for your specific UUID
      if (user.id === '5971f7c3-840f-4d2c-9931-db26d1978f5a') {
        setIsAuthorized(true);
        loadDashboardData();
      } else {
        setIsAuthorized(false);
      }
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load statistics with error handling for missing tables
      const results = await Promise.allSettled([
        supabase.from('profiles').select('id', { count: 'exact' }),
        supabase.from('challenges').select('id', { count: 'exact' }),
        supabase.from('learning_paths').select('id', { count: 'exact' }),
        supabase.from('user_notifications').select('id', { count: 'exact' }),
        supabase.from('cms_media').select('id', { count: 'exact' }),
        supabase.from('admin_activity_log').select('id', { count: 'exact' })
      ]);

      // Extract counts with fallbacks
      const [
        usersCount,
        challengesCount,
        learningPathsCount,
        notificationsCount,
        mediaCount,
        activityCount
      ] = results.map(result =>
        result.status === 'fulfilled' ? result.value : { count: 0 }
      );

      // Get recent activity with error handling
      let recentActivity = [];
      try {
        const { data } = await supabase
          .from('admin_activity_log')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(10);
        recentActivity = data || [];
      } catch (error) {
        console.log('Admin activity log table not available yet');
      }

      // Get user statistics with error handling
      let userStats = [];
      try {
        const { data } = await supabase
          .from('profiles')
          .select('subscription_tier')
          .neq('subscription_tier', null);
        userStats = data || [];
      } catch (error) {
        console.log('Profiles table not available yet');
      }

      const premiumUsers = userStats.filter(u => u.subscription_tier === 'premium').length || 0;
      const freeUsers = userStats.filter(u => u.subscription_tier === 'free').length || 0;

      setStats({
        totalUsers: usersCount.count || 0,
        premiumUsers,
        freeUsers,
        totalChallenges: challengesCount.count || 0,
        totalLearningPaths: learningPathsCount.count || 0,
        totalNotifications: notificationsCount.count || 0,
        totalMedia: mediaCount.count || 0,
        totalActivity: activityCount.count || 0,
        recentActivity: recentActivity
      });

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Set default stats if everything fails
      setStats({
        totalUsers: 0,
        premiumUsers: 0,
        freeUsers: 0,
        totalChallenges: 0,
        totalLearningPaths: 0,
        totalNotifications: 0,
        totalMedia: 0,
        totalActivity: 0,
        recentActivity: []
      });
    } finally {
      setLoading(false);
    }
  };

  // Dashboard tabs with comprehensive features
  const tabs = [
    { id: 'overview', label: 'Overview', icon: FaChartLine, description: 'System overview and statistics' },
    { id: 'users', label: 'User Management', icon: FaUsers, description: 'Manage users and subscriptions' },
    { id: 'content', label: 'Content Management', icon: FaBook, description: 'CMS for challenges and learning paths' },
    { id: 'media', label: 'Media Library', icon: FaImage, description: 'Upload and manage media files' },
    { id: 'notifications', label: 'Notifications', icon: FaBell, description: 'Send and manage notifications' },
    { id: 'analytics', label: 'Analytics', icon: FaChartLine, description: 'System analytics and reports' },
    { id: 'logs', label: 'Activity Logs', icon: FaHistory, description: 'View system and admin activity' },
    { id: 'settings', label: 'System Settings', icon: FaCog, description: 'Configure system settings' }
  ];

  const StatCard = ({ title, value, icon: Icon, color = 'blue', change, description }) => (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-xl border p-6 hover:shadow-lg transition-all duration-300`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {title}
          </p>
          <p className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mt-2`}>
            {value?.toLocaleString() || 0}
          </p>
          {description && (
            <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'} mt-1`}>
              {description}
            </p>
          )}
          {change && (
            <p className={`text-sm mt-2 ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change > 0 ? '+' : ''}{change}% from last month
            </p>
          )}
        </div>
        <div className={`p-4 rounded-xl bg-${color}-100 dark:bg-${color}-900/30`}>
          <Icon className={`h-8 w-8 text-${color}-600 dark:text-${color}-400`} />
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={`text-lg ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Loading Super Admin Dashboard...
          </p>
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-[#0F1419]' : 'bg-gray-50'} flex items-center justify-center`}>
        <div className="text-center">
          <FaExclamationTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
            Access Denied
          </h1>
          <p className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
            You don't have super admin privileges to access this dashboard.
          </p>
          <p className={`text-sm ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
            Contact your system administrator if you believe this is an error.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0F1419]' : 'bg-gray-50'}`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-b shadow-sm`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-[#88cc14] to-green-400 flex items-center justify-center mr-4">
                <FaUserShield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Super Admin Dashboard
                </h1>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Complete system management and CMS control
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Last updated: {new Date().toLocaleTimeString()}
              </span>
              <button
                onClick={loadDashboardData}
                className="bg-[#88cc14] text-white px-4 py-2 rounded-lg hover:bg-[#7ab512] transition-colors flex items-center gap-2"
              >
                <FaSync className="h-4 w-4" />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border-b`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-[#88cc14] text-[#88cc14]'
                      : `border-transparent ${darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'}`
                  }`}
                  title={tab.description}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatCard 
                title="Total Users" 
                value={stats.totalUsers} 
                icon={FaUsers} 
                color="blue"
                description="Registered users"
              />
              <StatCard 
                title="Premium Users" 
                value={stats.premiumUsers} 
                icon={FaCrown} 
                color="yellow"
                description="Active subscriptions"
              />
              <StatCard 
                title="Challenges" 
                value={stats.totalChallenges} 
                icon={FaTrophy} 
                color="orange"
                description="Available challenges"
              />
              <StatCard 
                title="Learning Paths" 
                value={stats.totalLearningPaths} 
                icon={FaGraduationCap} 
                color="green"
                description="Learning paths created"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StatCard 
                title="Notifications" 
                value={stats.totalNotifications} 
                icon={FaBell} 
                color="purple"
                description="Total notifications sent"
              />
              <StatCard 
                title="Media Files" 
                value={stats.totalMedia} 
                icon={FaImage} 
                color="pink"
                description="Uploaded media files"
              />
              <StatCard 
                title="Admin Actions" 
                value={stats.totalActivity} 
                icon={FaHistory} 
                color="indigo"
                description="Logged admin activities"
              />
              <StatCard 
                title="System Health" 
                value="100%" 
                icon={FaShieldAlt} 
                color="green"
                description="All systems operational"
              />
            </div>

            {/* Recent Activity */}
            <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-xl border p-6`}>
              <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                Recent Admin Activity
              </h3>
              <div className="space-y-3">
                {stats.recentActivity?.slice(0, 5).map((activity, index) => (
                  <div key={index} className={`flex items-center justify-between p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                    <div className="flex items-center gap-3">
                      <FaHistory className="text-blue-500" />
                      <div>
                        <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {activity.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </p>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {activity.target_type && `Target: ${activity.target_type}`}
                        </p>
                      </div>
                    </div>
                    <span className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                      {new Date(activity.created_at).toLocaleString()}
                    </span>
                  </div>
                )) || (
                  <p className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    No recent activity
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'users' && <UserManagement />}
        {activeTab === 'content' && <ContentManagement />}
        {activeTab === 'media' && <MediaLibrary />}
        {activeTab === 'notifications' && <NotificationTester />}
        {activeTab === 'analytics' && <SystemAnalytics />}
        {activeTab === 'logs' && <ActivityLogs />}
        
        {activeTab === 'settings' && (
          <div className="space-y-6">
            <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-xl p-6`}>
              <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                Super Admin Setup Status
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 rounded-lg bg-green-100 dark:bg-green-900/30">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                      <FaCheck className="text-white text-sm" />
                    </div>
                    <div>
                      <p className="font-medium text-green-800 dark:text-green-200">Super Admin Access</p>
                      <p className="text-sm text-green-600 dark:text-green-400">You have super admin privileges</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-yellow-100 dark:bg-yellow-900/30">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-yellow-500 flex items-center justify-center">
                      <FaExclamationTriangle className="text-white text-sm" />
                    </div>
                    <div>
                      <p className="font-medium text-yellow-800 dark:text-yellow-200">Database Schema Setup</p>
                      <p className="text-sm text-yellow-600 dark:text-yellow-400">Run the SQL schemas for full functionality</p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/30">
                  <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Setup Instructions:</h4>
                  <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>1. Run <code className="bg-blue-200 dark:bg-blue-800 px-1 rounded">FIXED_SUPER_ADMIN_SETUP.sql</code></li>
                    <li>2. Run <code className="bg-blue-200 dark:bg-blue-800 px-1 rounded">NOTIFICATION_SYSTEM_ENHANCED.sql</code></li>
                    <li>3. Run <code className="bg-blue-200 dark:bg-blue-800 px-1 rounded">USER_PROGRESS_TRACKING.sql</code></li>
                    <li>4. Refresh this dashboard to see full functionality</li>
                  </ol>
                </div>
              </div>
            </div>

            <div className="text-center py-12">
              <FaCog className={`h-16 w-16 ${darkMode ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} />
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                System Settings
              </h3>
              <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Advanced system configuration will be available after database setup.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComprehensiveSuperAdminDashboard;
