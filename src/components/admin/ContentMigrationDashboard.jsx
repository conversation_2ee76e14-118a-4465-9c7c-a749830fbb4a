/**
 * Content Migration Dashboard
 * 
 * Comprehensive dashboard for migrating JavaScript content files to database
 * Includes file upload, parsing, and CMS integration capabilities
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaUpload, FaDatabase, FaFileCode, FaSync, FaCheckCircle, FaExclamationTriangle,
  FaInfoCircle, FaDownload, FaEye, FaEdit, FaTrash, FaCog, FaPlay, FaPause
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { supabase } from '../../config/supabaseClient';
import PayloadCMSIntegrationService from '../../services/PayloadCMSIntegrationService';

const ContentMigrationDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('upload');
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [migrationStatus, setMigrationStatus] = useState(null);
  const [migrationProgress, setMigrationProgress] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [migrationResults, setMigrationResults] = useState([]);
  const [availableMigrations, setAvailableMigrations] = useState([
    { id: 'networking', name: 'Networking Fundamentals', modules: 25, status: 'ready' },
    { id: 'blue-teaming', name: 'Blue Teaming', modules: 50, status: 'ready' },
    { id: 'operating-systems', name: 'Operating Systems', modules: 30, status: 'ready' },
    { id: 'cloud-security', name: 'Cloud Security', modules: 45, status: 'ready' },
    { id: 'threat-hunting', name: 'Threat Hunting', modules: 24, status: 'ready' },
    { id: 'bug-bounty', name: 'Bug Bounty', modules: 20, status: 'ready' }
  ]);

  const tabs = [
    { id: 'upload', label: 'File Upload', icon: FaUpload },
    { id: 'migrate', label: 'SQL Migration', icon: FaDatabase },
    { id: 'cms', label: 'CMS Integration', icon: FaCog },
    { id: 'results', label: 'Results', icon: FaCheckCircle }
  ];

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    const jsFiles = files.filter(file => file.name.endsWith('.js'));
    
    setUploadedFiles(prev => [...prev, ...jsFiles.map(file => ({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      size: file.size,
      status: 'uploaded',
      parsed: false,
      migrated: false
    }))]);

    setMigrationStatus({
      type: 'success',
      message: `${jsFiles.length} JavaScript files uploaded successfully!`
    });
  };

  const parseUploadedFiles = async () => {
    setIsProcessing(true);
    setMigrationProgress(0);

    try {
      for (let i = 0; i < uploadedFiles.length; i++) {
        const fileData = uploadedFiles[i];
        
        // Simulate file parsing
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Update file status
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileData.id ? { ...f, status: 'parsed', parsed: true } : f
        ));
        
        setMigrationProgress(((i + 1) / uploadedFiles.length) * 100);
      }

      setMigrationStatus({
        type: 'success',
        message: 'All files parsed successfully!'
      });
    } catch (error) {
      setMigrationStatus({
        type: 'error',
        message: `Error parsing files: ${error.message}`
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const runSQLMigration = async (migrationId) => {
    setIsProcessing(true);
    setMigrationStatus({
      type: 'info',
      message: `Running ${migrationId} migration...`
    });

    try {
      // Simulate SQL migration execution
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Update migration status
      setAvailableMigrations(prev => prev.map(m => 
        m.id === migrationId ? { ...m, status: 'completed' } : m
      ));

      setMigrationResults(prev => [...prev, {
        id: Date.now(),
        migration: migrationId,
        status: 'success',
        timestamp: new Date().toISOString(),
        modules: availableMigrations.find(m => m.id === migrationId)?.modules || 0
      }]);

      setMigrationStatus({
        type: 'success',
        message: `${migrationId} migration completed successfully!`
      });
    } catch (error) {
      setMigrationStatus({
        type: 'error',
        message: `Migration failed: ${error.message}`
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const syncToCMS = async () => {
    setIsProcessing(true);
    setMigrationStatus({
      type: 'info',
      message: 'Syncing content to Payload CMS...'
    });

    try {
      await PayloadCMSIntegrationService.syncToPayloadCMS();
      
      setMigrationStatus({
        type: 'success',
        message: 'Content synced to Payload CMS successfully!'
      });
    } catch (error) {
      setMigrationStatus({
        type: 'error',
        message: `CMS sync failed: ${error.message}`
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const renderUploadTab = () => (
    <div className="space-y-6">
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
        <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Upload JavaScript Content Files
        </h3>
        
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <FaUpload className="mx-auto text-4xl text-gray-400 mb-4" />
          <p className={`text-lg mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Drag and drop JavaScript files here, or click to browse
          </p>
          <input
            type="file"
            multiple
            accept=".js"
            onChange={handleFileUpload}
            className="hidden"
            id="file-upload"
          />
          <label
            htmlFor="file-upload"
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg cursor-pointer transition-colors"
          >
            Choose Files
          </label>
        </div>

        {uploadedFiles.length > 0 && (
          <div className="mt-6">
            <div className="flex justify-between items-center mb-4">
              <h4 className={`text-md font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Uploaded Files ({uploadedFiles.length})
              </h4>
              <button
                onClick={parseUploadedFiles}
                disabled={isProcessing}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isProcessing ? <FaSync className="animate-spin" /> : 'Parse Files'}
              </button>
            </div>

            <div className="space-y-2">
              {uploadedFiles.map((file) => (
                <div key={file.id} className={`p-3 rounded border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <FaFileCode className="text-blue-500 mr-3" />
                      <div>
                        <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {file.name}
                        </p>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {(file.size / 1024).toFixed(2)} KB
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {file.parsed && <FaCheckCircle className="text-green-500" />}
                      <span className={`text-xs px-2 py-1 rounded ${
                        file.status === 'uploaded' ? 'bg-blue-100 text-blue-800' :
                        file.status === 'parsed' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {file.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {isProcessing && (
              <div className="mt-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Processing files...</span>
                  <span>{Math.round(migrationProgress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${migrationProgress}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  const renderMigrateTab = () => (
    <div className="space-y-6">
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
        <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          SQL Migration Scripts
        </h3>
        <p className={`text-sm mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Run pre-built SQL migration scripts to populate the database with learning content.
        </p>

        <div className="grid gap-4">
          {availableMigrations.map((migration) => (
            <div key={migration.id} className={`p-4 rounded border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
              <div className="flex justify-between items-center">
                <div>
                  <h4 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {migration.name}
                  </h4>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {migration.modules} modules • database/migrations/{migration.id}_migration.sql
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`text-xs px-2 py-1 rounded ${
                    migration.status === 'ready' ? 'bg-blue-100 text-blue-800' :
                    migration.status === 'completed' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {migration.status}
                  </span>
                  <button
                    onClick={() => runSQLMigration(migration.id)}
                    disabled={isProcessing || migration.status === 'completed'}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors disabled:opacity-50"
                  >
                    {migration.status === 'completed' ? 'Completed' : 'Run Migration'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center">
            <FaInfoCircle className="text-yellow-600 mr-2" />
            <p className="text-yellow-800 text-sm">
              <strong>Instructions:</strong> Copy the SQL content from the migration files in the database/migrations/ folder and execute them in your Supabase SQL Editor.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCMSTab = () => (
    <div className="space-y-6">
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
        <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Payload CMS Integration
        </h3>
        <p className={`text-sm mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Sync content between Supabase database and Payload CMS for advanced content management.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className={`p-4 rounded border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
            <h4 className={`font-semibold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Sync to CMS
            </h4>
            <p className={`text-sm mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Export content from Supabase to Payload CMS for advanced editing capabilities.
            </p>
            <button
              onClick={syncToCMS}
              disabled={isProcessing}
              className="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors disabled:opacity-50"
            >
              {isProcessing ? <FaSync className="animate-spin inline mr-2" /> : <FaUpload className="inline mr-2" />}
              Sync to CMS
            </button>
          </div>

          <div className={`p-4 rounded border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
            <h4 className={`font-semibold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Import from CMS
            </h4>
            <p className={`text-sm mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Import edited content from Payload CMS back to Supabase database.
            </p>
            <button
              onClick={() => PayloadCMSIntegrationService.importFromPayloadCMS()}
              disabled={isProcessing}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors disabled:opacity-50"
            >
              <FaDownload className="inline mr-2" />
              Import from CMS
            </button>
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center">
            <FaInfoCircle className="text-blue-600 mr-2" />
            <div className="text-blue-800 text-sm">
              <p><strong>CMS Location:</strong> us-cyberforce-cms-main folder in Downloads</p>
              <p><strong>Access:</strong> Run the CMS separately and use the admin interface for advanced content editing</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderResultsTab = () => (
    <div className="space-y-6">
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
        <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Migration Results
        </h3>

        {migrationResults.length === 0 ? (
          <div className="text-center py-8">
            <FaInfoCircle className="mx-auto text-4xl text-gray-400 mb-4" />
            <p className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              No migrations completed yet
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {migrationResults.map((result) => (
              <div key={result.id} className={`p-4 rounded border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {result.migration}
                    </h4>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {result.modules} modules migrated • {new Date(result.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <FaCheckCircle className="text-green-500 text-xl" />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Content Migration Dashboard
          </h1>
          <p className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-2`}>
            Migrate JavaScript content files to database and integrate with CMS
          </p>
        </div>

        {/* Status Alert */}
        {migrationStatus && (
          <div className={`mb-6 p-4 rounded-lg border ${
            migrationStatus.type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
            migrationStatus.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
            'bg-blue-50 border-blue-200 text-blue-800'
          }`}>
            <div className="flex items-center">
              {migrationStatus.type === 'success' && <FaCheckCircle className="mr-2" />}
              {migrationStatus.type === 'error' && <FaExclamationTriangle className="mr-2" />}
              {migrationStatus.type === 'info' && <FaInfoCircle className="mr-2" />}
              {migrationStatus.message}
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="flex space-x-1 mb-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-500 text-white'
                  : darkMode
                    ? 'text-gray-400 hover:text-white hover:bg-gray-800'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'upload' && renderUploadTab()}
            {activeTab === 'migrate' && renderMigrateTab()}
            {activeTab === 'cms' && renderCMSTab()}
            {activeTab === 'results' && renderResultsTab()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ContentMigrationDashboard;
