import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  FaBell,
  FaCode,
  FaTrophy,
  FaGraduationCap,
  FaFire,
  FaStar,
  FaExclamationTriangle,
  FaRocket,
  FaShieldAlt,
  FaPlus,
  FaCheck,
  FaTimes
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import ProgressTrackingService from '../../services/ProgressTrackingService';
import toast from 'react-hot-toast';

const NotificationTester = () => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: 'info',
    emoji: '',
    iconName: 'FaBell',
    iconColor: 'blue',
    actionUrl: '',
    actionLabel: '',
    category: 'system',
    priority: 2
  });

  const notificationTypes = [
    { value: 'info', label: 'Info', color: 'blue' },
    { value: 'success', label: 'Success', color: 'green' },
    { value: 'warning', label: 'Warning', color: 'yellow' },
    { value: 'error', label: 'Error', color: 'red' },
    { value: 'challenge', label: 'Challenge', color: 'purple' },
    { value: 'achievement', label: 'Achievement', color: 'yellow' }
  ];

  const iconOptions = [
    { value: 'FaBell', label: 'Bell', icon: FaBell },
    { value: 'FaCode', label: 'Code', icon: FaCode },
    { value: 'FaTrophy', label: 'Trophy', icon: FaTrophy },
    { value: 'FaGraduationCap', label: 'Graduation', icon: FaGraduationCap },
    { value: 'FaFire', label: 'Fire', icon: FaFire },
    { value: 'FaStar', label: 'Star', icon: FaStar },
    { value: 'FaExclamationTriangle', label: 'Warning', icon: FaExclamationTriangle },
    { value: 'FaRocket', label: 'Rocket', icon: FaRocket },
    { value: 'FaShieldAlt', label: 'Shield', icon: FaShieldAlt }
  ];

  const emojiOptions = ['🎉', '🚀', '🏆', '⭐', '🔥', '📚', '⚠️', '💡', '🎯', '🛡️'];

  const quickTemplates = [
    {
      name: 'Welcome Message',
      data: {
        title: 'Welcome to CyberForce! 🎉',
        message: 'Start your cybersecurity journey with our personalized learning paths.',
        type: 'success',
        emoji: '🎉',
        iconName: 'FaRocket',
        iconColor: 'green',
        actionUrl: '/dashboard/learning-paths',
        actionLabel: 'Start Learning',
        category: 'system'
      }
    },
    {
      name: 'New Challenge',
      data: {
        title: 'New Challenge Available! 🚀',
        message: 'A new SQL Injection challenge is now available. Test your skills!',
        type: 'challenge',
        emoji: '🚀',
        iconName: 'FaCode',
        iconColor: 'purple',
        actionUrl: '/dashboard/challenges',
        actionLabel: 'Start Challenge',
        category: 'challenge'
      }
    },
    {
      name: 'Achievement Unlocked',
      data: {
        title: 'Achievement Unlocked! 🏆',
        message: 'You have completed 5 challenges in a row. Amazing work!',
        type: 'achievement',
        emoji: '🏆',
        iconName: 'FaTrophy',
        iconColor: 'yellow',
        actionUrl: '/dashboard/achievements',
        actionLabel: 'View Achievements',
        category: 'achievement'
      }
    },
    {
      name: 'Learning Streak',
      data: {
        title: 'Learning Streak! 🔥',
        message: 'You have maintained a 7-day learning streak. Keep it up!',
        type: 'success',
        emoji: '🔥',
        iconName: 'FaFire',
        iconColor: 'orange',
        actionUrl: '/dashboard',
        actionLabel: 'Continue Learning',
        category: 'achievement'
      }
    }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleTemplateSelect = (template) => {
    setFormData(template.data);
  };

  const handleCreateNotification = async () => {
    if (!user) {
      toast.error('Please log in to create notifications');
      return;
    }

    if (!formData.title || !formData.message) {
      toast.error('Title and message are required');
      return;
    }

    setIsCreating(true);
    try {
      const options = {
        type: formData.type,
        emoji: formData.emoji || null,
        iconName: formData.iconName,
        iconColor: formData.iconColor,
        actionUrl: formData.actionUrl || null,
        actionLabel: formData.actionLabel || null,
        category: formData.category,
        priority: parseInt(formData.priority)
      };

      await ProgressTrackingService.createNotification(
        user.id,
        formData.title,
        formData.message,
        options
      );

      toast.success('Notification created successfully! 🎉');
      
      // Reset form
      setFormData({
        title: '',
        message: '',
        type: 'info',
        emoji: '',
        iconName: 'FaBell',
        iconColor: 'blue',
        actionUrl: '',
        actionLabel: '',
        category: 'system',
        priority: 2
      });
    } catch (error) {
      console.error('Error creating notification:', error);
      toast.error('Failed to create notification');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-xl p-6 shadow-lg`}>
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 rounded-xl bg-blue-500/20 flex items-center justify-center">
            <FaBell className="text-blue-500" />
          </div>
          <div>
            <h2 className="text-xl font-bold">Notification Tester</h2>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Create and test notifications for the current user
            </p>
          </div>
        </div>

        {/* Quick Templates */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Quick Templates</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {quickTemplates.map((template, index) => (
              <button
                key={index}
                onClick={() => handleTemplateSelect(template)}
                className={`p-3 rounded-lg border text-left transition-colors ${
                  darkMode
                    ? 'border-gray-600 hover:border-blue-500 bg-gray-700 hover:bg-gray-600'
                    : 'border-gray-200 hover:border-blue-500 bg-gray-50 hover:bg-blue-50'
                }`}
              >
                <div className="font-medium text-sm">{template.name}</div>
                <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {template.data.title}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Form */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Title */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-2">Title *</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Enter notification title..."
              className={`w-full p-3 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
          </div>

          {/* Message */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-2">Message *</label>
            <textarea
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              placeholder="Enter notification message..."
              rows={3}
              className={`w-full p-3 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
          </div>

          {/* Type */}
          <div>
            <label className="block text-sm font-medium mb-2">Type</label>
            <select
              value={formData.type}
              onChange={(e) => handleInputChange('type', e.target.value)}
              className={`w-full p-3 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              {notificationTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Priority */}
          <div>
            <label className="block text-sm font-medium mb-2">Priority</label>
            <select
              value={formData.priority}
              onChange={(e) => handleInputChange('priority', e.target.value)}
              className={`w-full p-3 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              <option value={1}>High</option>
              <option value={2}>Medium</option>
              <option value={3}>Low</option>
            </select>
          </div>

          {/* Emoji */}
          <div>
            <label className="block text-sm font-medium mb-2">Emoji</label>
            <div className="flex gap-2 flex-wrap">
              {emojiOptions.map(emoji => (
                <button
                  key={emoji}
                  onClick={() => handleInputChange('emoji', emoji)}
                  className={`w-10 h-10 rounded-lg border text-lg ${
                    formData.emoji === emoji
                      ? 'border-blue-500 bg-blue-500/20'
                      : darkMode
                        ? 'border-gray-600 hover:border-gray-500'
                        : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  {emoji}
                </button>
              ))}
            </div>
          </div>

          {/* Icon */}
          <div>
            <label className="block text-sm font-medium mb-2">Icon</label>
            <select
              value={formData.iconName}
              onChange={(e) => handleInputChange('iconName', e.target.value)}
              className={`w-full p-3 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              {iconOptions.map(icon => (
                <option key={icon.value} value={icon.value}>
                  {icon.label}
                </option>
              ))}
            </select>
          </div>

          {/* Action URL */}
          <div>
            <label className="block text-sm font-medium mb-2">Action URL</label>
            <input
              type="text"
              value={formData.actionUrl}
              onChange={(e) => handleInputChange('actionUrl', e.target.value)}
              placeholder="/dashboard/challenges"
              className={`w-full p-3 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
          </div>

          {/* Action Label */}
          <div>
            <label className="block text-sm font-medium mb-2">Action Label</label>
            <input
              type="text"
              value={formData.actionLabel}
              onChange={(e) => handleInputChange('actionLabel', e.target.value)}
              placeholder="View Challenges"
              className={`w-full p-3 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
          </div>
        </div>

        {/* Create Button */}
        <div className="mt-6">
          <button
            onClick={handleCreateNotification}
            disabled={isCreating || !formData.title || !formData.message}
            className={`w-full md:w-auto px-6 py-3 rounded-lg font-semibold transition-colors ${
              isCreating || !formData.title || !formData.message
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            } flex items-center justify-center gap-2`}
          >
            {isCreating ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                Creating...
              </>
            ) : (
              <>
                <FaPlus />
                Create Notification
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default NotificationTester;
