import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaUpload, FaImage, FaVideo, FaFile, FaTrash, FaDownload,
  FaSearch, FaFilter, FaEye, FaCopy, FaEdit
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

const MediaLibrary = () => {
  const { darkMode } = useGlobalTheme();
  const [media, setMedia] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [showUploadModal, setShowUploadModal] = useState(false);

  useEffect(() => {
    loadMedia();
  }, []);

  const loadMedia = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('cms_media')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setMedia(data || []);
    } catch (error) {
      console.error('Error loading media:', error);
      toast.error('Failed to load media');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (files) => {
    if (!files || files.length === 0) return;

    setUploading(true);
    try {
      for (const file of files) {
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
        const filePath = `media/${fileName}`;

        // Upload file to Supabase Storage
        const { error: uploadError } = await supabase.storage
          .from('media')
          .upload(filePath, file);

        if (uploadError) throw uploadError;

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('media')
          .getPublicUrl(filePath);

        // Save media record to database
        const { error: dbError } = await supabase
          .from('cms_media')
          .insert({
            filename: fileName,
            original_filename: file.name,
            mime_type: file.type,
            file_size: file.size,
            file_path: publicUrl,
            uploaded_by: (await supabase.auth.getUser()).data.user.id
          });

        if (dbError) throw dbError;
      }

      toast.success(`${files.length} file(s) uploaded successfully!`);
      loadMedia();
      setShowUploadModal(false);

      // Log admin activity
      await supabase.rpc('log_admin_activity', {
        action_param: 'media_uploaded',
        target_type_param: 'media',
        details_param: { count: files.length }
      });

    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Failed to upload files');
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteMedia = async (mediaItem) => {
    if (!confirm(`Are you sure you want to delete "${mediaItem.original_filename}"?`)) return;

    try {
      // Delete from storage
      const filePath = mediaItem.file_path.split('/').pop();
      await supabase.storage
        .from('media')
        .remove([`media/${filePath}`]);

      // Delete from database
      const { error } = await supabase
        .from('cms_media')
        .delete()
        .eq('id', mediaItem.id);

      if (error) throw error;

      toast.success('Media deleted successfully!');
      loadMedia();

      // Log admin activity
      await supabase.rpc('log_admin_activity', {
        action_param: 'media_deleted',
        target_type_param: 'media',
        target_id_param: mediaItem.id,
        details_param: { filename: mediaItem.original_filename }
      });

    } catch (error) {
      console.error('Error deleting media:', error);
      toast.error('Failed to delete media');
    }
  };

  const copyToClipboard = (url) => {
    navigator.clipboard.writeText(url);
    toast.success('URL copied to clipboard!');
  };

  const getFileIcon = (mimeType) => {
    if (mimeType.startsWith('image/')) return FaImage;
    if (mimeType.startsWith('video/')) return FaVideo;
    return FaFile;
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredMedia = media.filter(item => {
    const matchesSearch = item.original_filename.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterType === 'all' || item.mime_type.startsWith(filterType);
    return matchesSearch && matchesFilter;
  });

  const UploadModal = () => (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`w-full max-w-md ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-6`}
      >
        <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Upload Media
        </h3>
        
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <FaUpload className={`mx-auto h-12 w-12 ${darkMode ? 'text-gray-400' : 'text-gray-400'} mb-4`} />
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
            Drag and drop files here, or click to select
          </p>
          <input
            type="file"
            multiple
            accept="image/*,video/*,.pdf,.doc,.docx"
            onChange={(e) => handleFileUpload(Array.from(e.target.files))}
            className="hidden"
            id="file-upload"
          />
          <label
            htmlFor="file-upload"
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg cursor-pointer inline-block"
          >
            Select Files
          </label>
        </div>

        <div className="flex gap-3 mt-6">
          <button
            onClick={() => setShowUploadModal(false)}
            className={`flex-1 py-2 px-4 rounded-lg ${
              darkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-white'
                : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
            }`}
          >
            Cancel
          </button>
        </div>
      </motion.div>
    </div>
  );

  const MediaModal = ({ media: mediaItem, onClose }) => (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`w-full max-w-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl overflow-hidden`}
      >
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {mediaItem.original_filename}
            </h2>
            <button
              onClick={onClose}
              className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
            >
              ×
            </button>
          </div>
        </div>

        <div className="p-6">
          {mediaItem.mime_type.startsWith('image/') && (
            <img
              src={mediaItem.file_path}
              alt={mediaItem.original_filename}
              className="w-full h-64 object-cover rounded-lg mb-4"
            />
          )}

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>File Size:</span>
              <p className={darkMode ? 'text-gray-300' : 'text-gray-600'}>
                {formatFileSize(mediaItem.file_size)}
              </p>
            </div>
            <div>
              <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Type:</span>
              <p className={darkMode ? 'text-gray-300' : 'text-gray-600'}>
                {mediaItem.mime_type}
              </p>
            </div>
            <div>
              <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Uploaded:</span>
              <p className={darkMode ? 'text-gray-300' : 'text-gray-600'}>
                {new Date(mediaItem.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="mt-4">
            <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>URL:</span>
            <div className="flex gap-2 mt-1">
              <input
                type="text"
                value={mediaItem.file_path}
                readOnly
                className={`flex-1 p-2 rounded border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-gray-100 border-gray-300 text-gray-900'
                }`}
              />
              <button
                onClick={() => copyToClipboard(mediaItem.file_path)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded"
              >
                <FaCopy />
              </button>
            </div>
          </div>

          <div className="flex gap-3 mt-6">
            <button
              onClick={() => window.open(mediaItem.file_path, '_blank')}
              className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2"
            >
              <FaDownload />
              Download
            </button>
            <button
              onClick={() => handleDeleteMedia(mediaItem)}
              className="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg"
            >
              <FaTrash />
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Media Library
          </h2>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Upload and manage media files for your content
          </p>
        </div>
        <button
          onClick={() => setShowUploadModal(true)}
          className="bg-[#88cc14] hover:bg-[#7ab512] text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
        >
          <FaUpload />
          Upload Media
        </button>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="flex-1 relative">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search media..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className={`px-4 py-2 rounded-lg border ${
            darkMode
              ? 'bg-gray-700 border-gray-600 text-white'
              : 'bg-white border-gray-300 text-gray-900'
          } focus:outline-none focus:ring-2 focus:ring-blue-500`}
        >
          <option value="all">All Types</option>
          <option value="image">Images</option>
          <option value="video">Videos</option>
          <option value="application">Documents</option>
        </select>
      </div>

      {/* Media Grid */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Loading media...</p>
        </div>
      ) : filteredMedia.length === 0 ? (
        <div className="text-center py-12">
          <FaImage className={`h-16 w-16 ${darkMode ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} />
          <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            No media found
          </h3>
          <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {searchQuery ? 'Try adjusting your search criteria.' : 'Upload your first media file to get started.'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredMedia.map((item) => {
            const FileIcon = getFileIcon(item.mime_type);
            return (
              <div
                key={item.id}
                className={`${
                  darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
                } border rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer`}
                onClick={() => setSelectedMedia(item)}
              >
                <div className="aspect-square bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  {item.mime_type.startsWith('image/') ? (
                    <img
                      src={item.file_path}
                      alt={item.original_filename}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <FileIcon className="h-12 w-12 text-gray-400" />
                  )}
                </div>
                <div className="p-4">
                  <h3 className={`font-medium text-sm ${darkMode ? 'text-white' : 'text-gray-900'} truncate`}>
                    {item.original_filename}
                  </h3>
                  <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
                    {formatFileSize(item.file_size)}
                  </p>
                  <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'} mt-1`}>
                    {new Date(item.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && <UploadModal />}

      {/* Media Detail Modal */}
      {selectedMedia && (
        <MediaModal
          media={selectedMedia}
          onClose={() => setSelectedMedia(null)}
        />
      )}

      {/* Upload Progress */}
      {uploading && (
        <div className="fixed bottom-4 right-4 bg-blue-500 text-white p-4 rounded-lg shadow-lg">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            <span>Uploading files...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaLibrary;
