/**
 * Content Management Dashboard
 * 
 * Comprehensive super admin dashboard for managing learning content,
 * user progress, and system analytics
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaBook, FaUsers, FaChartLine, FaDatabase, FaCog, FaUpload,
  FaDownload, FaSync, FaPlus, FaEdit, FaTrash, FaEye,
  FaCheckCircle, FaExclamationTriangle, FaInfoCircle
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import DatabaseContentService from '../../services/DatabaseContentService';

const ContentManagementDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    totalPaths: 0,
    totalModules: 0,
    totalSections: 0,
    totalUsers: 0,
    activeUsers: 0,
    completionRate: 0
  });
  const [learningPaths, setLearningPaths] = useState([]);
  const [migrationStatus, setMigrationStatus] = useState(null);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load learning paths
      const paths = await DatabaseContentService.getLearningPaths();
      setLearningPaths(paths);
      
      // Calculate stats
      setStats({
        totalPaths: paths.length,
        totalModules: paths.reduce((sum, path) => sum + (path.modules_count || 0), 0),
        totalSections: 0, // Will be calculated from database
        totalUsers: 0, // Will be fetched from profiles table
        activeUsers: 0,
        completionRate: 0
      });
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMigrateContent = async () => {
    try {
      setLoading(true);
      setMigrationStatus({ type: 'info', message: 'Starting content migration...' });
      
      await DatabaseContentService.migrateContentToDatabase();
      
      setMigrationStatus({ type: 'success', message: 'Content migration completed successfully!' });
      await loadDashboardData(); // Refresh data
      
    } catch (error) {
      console.error('Migration error:', error);
      setMigrationStatus({ type: 'error', message: `Migration failed: ${error.message}` });
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FaChartLine },
    { id: 'content', label: 'Content Management', icon: FaBook },
    { id: 'users', label: 'User Management', icon: FaUsers },
    { id: 'analytics', label: 'Analytics', icon: FaChartLine },
    { id: 'system', label: 'System Tools', icon: FaCog }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaBook className="text-2xl text-blue-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Learning Paths</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {stats.totalPaths}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaBook className="text-2xl text-green-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Modules</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {stats.totalModules}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaUsers className="text-2xl text-purple-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Users</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {stats.totalUsers}
              </p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
          <div className="flex items-center">
            <FaChartLine className="text-2xl text-yellow-500 mr-4" />
            <div>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Completion Rate</p>
              <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {stats.completionRate}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Migration Status */}
      {migrationStatus && (
        <div className={`p-4 rounded-lg border ${
          migrationStatus.type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
          migrationStatus.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
          'bg-blue-50 border-blue-200 text-blue-800'
        }`}>
          <div className="flex items-center">
            {migrationStatus.type === 'success' && <FaCheckCircle className="mr-2" />}
            {migrationStatus.type === 'error' && <FaExclamationTriangle className="mr-2" />}
            {migrationStatus.type === 'info' && <FaInfoCircle className="mr-2" />}
            {migrationStatus.message}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
        <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={handleMigrateContent}
            disabled={loading}
            className="flex items-center justify-center p-4 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            <FaSync className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            Migrate Content to Database
          </button>
          
          <button
            onClick={() => setActiveTab('content')}
            className="flex items-center justify-center p-4 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
          >
            <FaPlus className="mr-2" />
            Create New Content
          </button>
          
          <button
            onClick={() => setActiveTab('analytics')}
            className="flex items-center justify-center p-4 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors"
          >
            <FaChartLine className="mr-2" />
            View Analytics
          </button>
        </div>
      </div>
    </div>
  );

  const renderContentManagement = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          Learning Paths
        </h3>
        <button className="flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
          <FaPlus className="mr-2" />
          Add New Path
        </button>
      </div>

      <div className="grid gap-4">
        {learningPaths.map((path) => (
          <div key={path.id} className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h4 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {path.title}
                </h4>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
                  {path.description}
                </p>
                <div className="flex items-center mt-2 space-x-4">
                  <span className={`text-xs px-2 py-1 rounded ${
                    path.difficulty === 'beginner' ? 'bg-green-100 text-green-800' :
                    path.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {path.difficulty}
                  </span>
                  <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {path.modules_count} modules
                  </span>
                  <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {path.estimated_hours} hours
                  </span>
                </div>
              </div>
              <div className="flex space-x-2">
                <button className="p-2 text-blue-500 hover:bg-blue-50 rounded">
                  <FaEye />
                </button>
                <button className="p-2 text-green-500 hover:bg-green-50 rounded">
                  <FaEdit />
                </button>
                <button className="p-2 text-red-500 hover:bg-red-50 rounded">
                  <FaTrash />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className={`min-h-screen p-6 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Content Management Dashboard
          </h1>
          <p className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-2`}>
            Manage learning content, users, and system analytics
          </p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-500 text-white'
                  : darkMode
                    ? 'text-gray-400 hover:text-white hover:bg-gray-800'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'content' && renderContentManagement()}
            {activeTab === 'users' && <div>User Management (Coming Soon)</div>}
            {activeTab === 'analytics' && <div>Analytics (Coming Soon)</div>}
            {activeTab === 'system' && <div>System Tools (Coming Soon)</div>}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ContentManagementDashboard;
