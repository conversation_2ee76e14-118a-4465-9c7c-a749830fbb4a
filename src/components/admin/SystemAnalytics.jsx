import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaChartLine, FaUsers, FaTrophy, FaEye, FaDownload,
  FaCalendarAlt, FaArrowUp, FaArrowDown, FaEquals
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { supabase } from '../../lib/supabase';

const SystemAnalytics = () => {
  const { darkMode } = useGlobalTheme();
  const [analytics, setAnalytics] = useState({});
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');

  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeRange) {
        case '24h':
          startDate.setHours(startDate.getHours() - 24);
          break;
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          break;
      }

      // Get user analytics
      const { data: userStats } = await supabase
        .from('profiles')
        .select('created_at, subscription_tier')
        .gte('created_at', startDate.toISOString());

      // Get activity analytics
      const { data: activityStats } = await supabase
        .from('user_activity_log')
        .select('created_at, activity_type')
        .gte('created_at', startDate.toISOString());

      // Get notification analytics
      const { data: notificationStats } = await supabase
        .from('user_notifications')
        .select('created_at, type, is_read')
        .gte('created_at', startDate.toISOString());

      // Process analytics data
      const processedAnalytics = {
        userGrowth: userStats?.length || 0,
        premiumUsers: userStats?.filter(u => u.subscription_tier === 'premium').length || 0,
        totalActivity: activityStats?.length || 0,
        notificationsSent: notificationStats?.length || 0,
        notificationReadRate: notificationStats?.length > 0 
          ? (notificationStats.filter(n => n.is_read).length / notificationStats.length * 100).toFixed(1)
          : 0,
        dailyActiveUsers: calculateDailyActiveUsers(activityStats),
        topActivities: calculateTopActivities(activityStats),
        userGrowthTrend: calculateGrowthTrend(userStats)
      };

      setAnalytics(processedAnalytics);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateDailyActiveUsers = (activities) => {
    if (!activities) return [];
    
    const dailyUsers = {};
    activities.forEach(activity => {
      const date = new Date(activity.created_at).toDateString();
      if (!dailyUsers[date]) {
        dailyUsers[date] = new Set();
      }
      dailyUsers[date].add(activity.user_id);
    });

    return Object.entries(dailyUsers).map(([date, users]) => ({
      date,
      count: users.size
    }));
  };

  const calculateTopActivities = (activities) => {
    if (!activities) return [];
    
    const activityCounts = {};
    activities.forEach(activity => {
      activityCounts[activity.activity_type] = (activityCounts[activity.activity_type] || 0) + 1;
    });

    return Object.entries(activityCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([type, count]) => ({ type, count }));
  };

  const calculateGrowthTrend = (users) => {
    if (!users || users.length === 0) return 0;
    
    const now = new Date();
    const halfwayPoint = new Date(now.getTime() - (now.getTime() - new Date(users[0]?.created_at).getTime()) / 2);
    
    const firstHalf = users.filter(u => new Date(u.created_at) < halfwayPoint).length;
    const secondHalf = users.filter(u => new Date(u.created_at) >= halfwayPoint).length;
    
    if (firstHalf === 0) return secondHalf > 0 ? 100 : 0;
    return ((secondHalf - firstHalf) / firstHalf * 100).toFixed(1);
  };

  const MetricCard = ({ title, value, icon: Icon, color = 'blue', trend, subtitle }) => (
    <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-xl p-6 hover:shadow-lg transition-all duration-300`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {title}
          </p>
          <p className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mt-2`}>
            {value}
          </p>
          {subtitle && (
            <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'} mt-1`}>
              {subtitle}
            </p>
          )}
          {trend !== undefined && (
            <div className="flex items-center mt-2">
              {trend > 0 ? (
                <FaArrowUp className="text-green-500 mr-1" />
              ) : trend < 0 ? (
                <FaArrowDown className="text-red-500 mr-1" />
              ) : (
                <FaEquals className="text-gray-500 mr-1" />
              )}
              <span className={`text-sm ${
                trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-gray-500'
              }`}>
                {Math.abs(trend)}%
              </span>
            </div>
          )}
        </div>
        <div className={`p-4 rounded-xl bg-${color}-100 dark:bg-${color}-900/30`}>
          <Icon className={`h-8 w-8 text-${color}-600 dark:text-${color}-400`} />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            System Analytics
          </h2>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Monitor platform performance and user engagement
          </p>
        </div>
        <div className="flex gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className={`px-4 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <FaDownload />
            Export Report
          </button>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Loading analytics...</p>
        </div>
      ) : (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="New Users"
              value={analytics.userGrowth}
              icon={FaUsers}
              color="blue"
              trend={analytics.userGrowthTrend}
              subtitle={`in ${timeRange}`}
            />
            <MetricCard
              title="Premium Users"
              value={analytics.premiumUsers}
              icon={FaTrophy}
              color="yellow"
              subtitle="active subscriptions"
            />
            <MetricCard
              title="Total Activity"
              value={analytics.totalActivity}
              icon={FaChartLine}
              color="green"
              subtitle="user actions"
            />
            <MetricCard
              title="Notifications"
              value={analytics.notificationsSent}
              icon={FaEye}
              color="purple"
              subtitle={`${analytics.notificationReadRate}% read rate`}
            />
          </div>

          {/* Charts and Detailed Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Daily Active Users */}
            <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-xl p-6`}>
              <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                Daily Active Users
              </h3>
              <div className="space-y-3">
                {analytics.dailyActiveUsers?.slice(0, 7).map((day, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {new Date(day.date).toLocaleDateString()}
                    </span>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 rounded-full bg-blue-500`} style={{ width: `${Math.max(day.count * 10, 20)}px` }}></div>
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {day.count}
                      </span>
                    </div>
                  </div>
                )) || (
                  <p className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    No activity data available
                  </p>
                )}
              </div>
            </div>

            {/* Top Activities */}
            <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-xl p-6`}>
              <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                Top Activities
              </h3>
              <div className="space-y-3">
                {analytics.topActivities?.map((activity, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {activity.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 rounded-full bg-green-500`} style={{ width: `${Math.max(activity.count * 5, 20)}px` }}></div>
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {activity.count}
                      </span>
                    </div>
                  </div>
                )) || (
                  <p className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    No activity data available
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* System Health */}
          <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-xl p-6`}>
            <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
              System Health
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-green-100 flex items-center justify-center">
                  <div className="w-8 h-8 rounded-full bg-green-500"></div>
                </div>
                <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Database</p>
                <p className="text-sm text-green-600">Operational</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-green-100 flex items-center justify-center">
                  <div className="w-8 h-8 rounded-full bg-green-500"></div>
                </div>
                <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>API</p>
                <p className="text-sm text-green-600">Operational</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-green-100 flex items-center justify-center">
                  <div className="w-8 h-8 rounded-full bg-green-500"></div>
                </div>
                <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Storage</p>
                <p className="text-sm text-green-600">Operational</p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SystemAnalytics;
