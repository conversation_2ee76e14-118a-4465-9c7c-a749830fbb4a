import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaPlus, FaEdit, FaTrash, FaEye, FaSave, FaTimes, FaCode,
  FaGraduationCap, FaTrophy, FaBook, FaSearch, FaFilter,
  FaUpload, FaDownload, FaClone, FaHistory, FaCheck, FaTag,
  FaFolder, FaImage, FaFileAlt, FaCopy, FaExternalLinkAlt
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

// Import existing content for migration
import { learningPaths } from '../../data/learning-paths-structure';
import { networkFundamentalsModules } from '../../data/content/network-fundamentals';
import challengesData from '../../data/challenges.json';

const ContentManagement = () => {
  const { darkMode } = useGlobalTheme();
  const [activeContentType, setActiveContentType] = useState('challenge');
  const [content, setContent] = useState([]);
  const [contentTypes, setContentTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingContent, setEditingContent] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    loadContentTypes();
    loadContent();
  }, [activeContentType]);

  const loadContentTypes = async () => {
    try {
      // Try to load from CMS database first
      const { data: cmsTypes, error } = await supabase
        .from('cms_content_types')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (!error && cmsTypes) {
        setContentTypes(cmsTypes);
        return;
      }

      // Fallback to static content types
      console.log('CMS tables not available, using static types');
      const staticContentTypes = [
        { id: '1', name: 'Challenge', slug: 'challenge', is_active: true },
        { id: '2', name: 'Learning Path', slug: 'learning_path', is_active: true },
        { id: '3', name: 'Learning Module', slug: 'learning_module', is_active: true },
        { id: '4', name: 'Blog Post', slug: 'blog_post', is_active: true }
      ];

      setContentTypes(staticContentTypes);
    } catch (error) {
      console.error('Error loading content types:', error);
      toast.error('Failed to load content types');
    }
  };

  const loadContent = async () => {
    try {
      setLoading(true);

      // Try to load from CMS database first
      const { data: contentTypeData } = await supabase
        .from('cms_content_types')
        .select('id')
        .eq('slug', activeContentType)
        .single();

      if (contentTypeData) {
        // Load from CMS database
        let query = supabase
          .from('cms_content')
          .select(`
            *,
            cms_content_types(name, slug),
            created_by_profile:profiles!cms_content_created_by_fkey(full_name, email)
          `)
          .eq('content_type_id', contentTypeData.id)
          .order('created_at', { ascending: false });

        if (statusFilter !== 'all') {
          query = query.eq('status', statusFilter);
        }

        const { data: cmsContent, error } = await query;

        if (!error && cmsContent) {
          setContent(cmsContent);
          return;
        }
      }

      // Fallback: Load from existing data sources
      let data = [];

      if (activeContentType === 'challenge') {
        // Load from challenges.json or database
        try {
          const { data: challenges, error } = await supabase
            .from('challenges')
            .select('*')
            .order('created_at', { ascending: false });

          if (!error && challenges) {
            data = challenges.map(challenge => ({
              id: challenge.id,
              title: challenge.title,
              slug: challenge.slug || challenge.id,
              content: {
                description: challenge.description,
                difficulty: challenge.difficulty_id || challenge.difficulty,
                category: challenge.category,
                points: challenge.points,
                instructions: challenge.instructions
              },
              status: 'published',
              created_at: challenge.created_at || new Date().toISOString(),
              cms_content_types: { name: 'Challenge', slug: 'challenge' }
            }));
          } else {
            // Use static challenges data
            data = challengesData.map(challenge => ({
              id: challenge.id,
              title: challenge.title,
              slug: challenge.slug,
              content: {
                description: challenge.description,
                difficulty: challenge.difficulty?.name || 'beginner',
                category: challenge.category?.name || 'general',
                points: challenge.points || 100
              },
              status: 'published',
              created_at: new Date().toISOString(),
              cms_content_types: { name: 'Challenge', slug: 'challenge' }
            }));
          }
        } catch (error) {
          console.log('Using static challenges data');
          data = challengesData.slice(0, 10).map(challenge => ({
            id: challenge.id,
            title: challenge.title,
            slug: challenge.slug,
            content: {
              description: challenge.description,
              difficulty: challenge.difficulty?.name || 'beginner',
              category: challenge.category?.name || 'general'
            },
            status: 'published',
            created_at: new Date().toISOString(),
            cms_content_types: { name: 'Challenge', slug: 'challenge' }
          }));
        }
      } else if (activeContentType === 'learning_path') {
        // Load learning paths from static data
        data = learningPaths.map(path => ({
          id: path.id,
          title: path.title,
          slug: path.id,
          content: {
            description: path.description,
            difficulty: path.difficulty,
            category: path.category,
            estimatedHours: path.estimatedHours,
            modules: path.modules
          },
          status: 'published',
          created_at: new Date().toISOString(),
          cms_content_types: { name: 'Learning Path', slug: 'learning_path' }
        }));
      } else if (activeContentType === 'learning_module') {
        // Load network fundamentals modules as example
        if (networkFundamentalsModules) {
          data = Object.entries(networkFundamentalsModules).map(([key, module]) => ({
            id: key,
            title: module.title,
            slug: key,
            content: {
              description: module.description || 'Learning module content',
              content: module.content,
              objectives: module.objectives,
              keyPoints: module.keyPoints
            },
            status: 'published',
            created_at: new Date().toISOString(),
            cms_content_types: { name: 'Learning Module', slug: 'learning_module' }
          }));
        }
      }

      setContent(data);
    } catch (error) {
      console.error('Error loading content:', error);
      toast.error('Failed to load content');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateContent = async (contentData) => {
    try {
      // Get content type ID
      const { data: contentTypeData } = await supabase
        .from('cms_content_types')
        .select('id')
        .eq('slug', activeContentType)
        .single();

      if (!contentTypeData) {
        toast.error('Content type not found. Please run CMS setup first.');
        return;
      }

      // Create content in CMS
      const { data, error } = await supabase
        .from('cms_content')
        .insert({
          content_type_id: contentTypeData.id,
          title: contentData.title,
          slug: contentData.title.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
          content: contentData,
          status: 'draft',
          created_by: (await supabase.auth.getUser()).data.user.id
        })
        .select()
        .single();

      if (error) throw error;

      toast.success('Content created successfully!');
      setShowCreateModal(false);
      loadContent();

      // Log admin activity
      try {
        await supabase.rpc('log_admin_activity', {
          action_param: 'content_created',
          target_type_param: activeContentType,
          target_id_param: data.id,
          details_param: { title: contentData.title }
        });
      } catch (logError) {
        console.log('Activity logging not available yet');
      }

    } catch (error) {
      console.error('Error creating content:', error);
      toast.error('Failed to create content');
    }
  };

  const handleDeleteContent = async (id, title) => {
    if (!confirm(`Are you sure you want to delete "${title}"?`)) return;

    try {
      let error = null;
      
      if (activeContentType === 'challenge') {
        const result = await supabase
          .from('challenges')
          .delete()
          .eq('id', id);
        error = result.error;
      } else if (activeContentType === 'learning_path') {
        const result = await supabase
          .from('learning_paths')
          .delete()
          .eq('id', id);
        error = result.error;
      }

      if (error) throw error;

      toast.success('Content deleted successfully!');
      loadContent();

      // Log admin activity if function exists
      try {
        await supabase.rpc('log_admin_activity', {
          action_param: 'content_deleted',
          target_type_param: activeContentType,
          target_id_param: id,
          details_param: { title }
        });
      } catch (logError) {
        console.log('Activity logging not available yet');
      }

    } catch (error) {
      console.error('Error deleting content:', error);
      toast.error('Failed to delete content');
    }
  };

  const handlePublishContent = async (id, title) => {
    try {
      const { error } = await supabase
        .from('cms_content')
        .update({
          status: 'published',
          published_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;

      toast.success('Content published successfully!');
      loadContent();

      // Log admin activity
      try {
        await supabase.rpc('log_admin_activity', {
          action_param: 'content_published',
          target_type_param: activeContentType,
          target_id_param: id,
          details_param: { title }
        });
      } catch (logError) {
        console.log('Activity logging not available yet');
      }

    } catch (error) {
      console.error('Error publishing content:', error);
      toast.error('Failed to publish content');
    }
  };

  const migrateExistingContent = async () => {
    if (!confirm('This will migrate your existing content to the CMS. Continue?')) return;

    try {
      setLoading(true);
      let migratedCount = 0;

      // Migrate challenges
      if (challengesData && challengesData.length > 0) {
        const { data: challengeType } = await supabase
          .from('cms_content_types')
          .select('id')
          .eq('slug', 'challenge')
          .single();

        if (challengeType) {
          for (const challenge of challengesData.slice(0, 5)) { // Migrate first 5 as example
            const { error } = await supabase
              .from('cms_content')
              .insert({
                content_type_id: challengeType.id,
                title: challenge.title,
                slug: challenge.slug,
                content: {
                  description: challenge.description,
                  difficulty: challenge.difficulty?.name || 'beginner',
                  category: challenge.category?.name || 'general',
                  points: challenge.points || 100,
                  instructions: challenge.instructions,
                  hints: challenge.hints || [],
                  flag_format: challenge.flag_format
                },
                status: 'published',
                published_at: new Date().toISOString(),
                created_by: (await supabase.auth.getUser()).data.user.id
              })
              .select()
              .single();

            if (!error) migratedCount++;
          }
        }
      }

      // Migrate learning paths
      if (learningPaths && learningPaths.length > 0) {
        const { data: pathType } = await supabase
          .from('cms_content_types')
          .select('id')
          .eq('slug', 'learning_path')
          .single();

        if (pathType) {
          for (const path of learningPaths) {
            const { error } = await supabase
              .from('cms_content')
              .insert({
                content_type_id: pathType.id,
                title: path.title,
                slug: path.id,
                content: {
                  description: path.description,
                  difficulty: path.difficulty,
                  category: path.category,
                  estimatedHours: path.estimatedHours,
                  modules: path.modules || [],
                  icon: path.icon?.name || 'FaBook'
                },
                status: 'published',
                published_at: new Date().toISOString(),
                created_by: (await supabase.auth.getUser()).data.user.id
              })
              .select()
              .single();

            if (!error) migratedCount++;
          }
        }
      }

      toast.success(`Successfully migrated ${migratedCount} content items!`);
      loadContent();

    } catch (error) {
      console.error('Error migrating content:', error);
      toast.error('Failed to migrate content');
    } finally {
      setLoading(false);
    }
  };

  const getContentTypeIcon = (type) => {
    const icons = {
      challenge: FaTrophy,
      learning_path: FaGraduationCap,
      learning_module: FaBook
    };
    return icons[type] || FaCode;
  };

  const getStatusColor = (status) => {
    const colors = {
      draft: 'yellow',
      published: 'green',
      archived: 'gray'
    };
    return colors[status] || 'gray';
  };

  const filteredContent = content.filter(item =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.content?.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Content Management System
          </h2>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Create, edit, and manage all your cybersecurity content
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={migrateExistingContent}
            className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
          >
            <FaUpload />
            Migrate Content
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-[#88cc14] hover:bg-[#7ab512] text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
          >
            <FaPlus />
            Create Content
          </button>
        </div>
      </div>

      {/* Content Type Tabs */}
      <div className="flex gap-2 overflow-x-auto">
        {contentTypes.map((type) => {
          const Icon = getContentTypeIcon(type.slug);
          return (
            <button
              key={type.id}
              onClick={() => setActiveContentType(type.slug)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium whitespace-nowrap transition-colors ${
                activeContentType === type.slug
                  ? 'bg-[#88cc14] text-white'
                  : darkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
              }`}
            >
              <Icon className="w-4 h-4" />
              {type.name}
            </button>
          );
        })}
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="flex-1 relative">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search content..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className={`px-4 py-2 rounded-lg border ${
            darkMode
              ? 'bg-gray-700 border-gray-600 text-white'
              : 'bg-white border-gray-300 text-gray-900'
          } focus:outline-none focus:ring-2 focus:ring-blue-500`}
        >
          <option value="all">All Status</option>
          <option value="draft">Draft</option>
          <option value="published">Published</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      {/* Content List */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Loading content...</p>
        </div>
      ) : filteredContent.length === 0 ? (
        <div className="text-center py-12">
          <FaCode className={`h-16 w-16 ${darkMode ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} />
          <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            No content found
          </h3>
          <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {searchQuery ? 'Try adjusting your search criteria.' : 'Create your first content item to get started.'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredContent.map((item) => (
            <div
              key={item.id}
              className={`${
                darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
              } border rounded-xl p-6 hover:shadow-lg transition-all duration-300`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className={`font-bold text-lg ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                    {item.title}
                  </h3>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                    {item.content?.description || 'No description available'}
                  </p>
                  <div className="flex items-center gap-2 mb-3">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium bg-${getStatusColor(item.status)}-100 text-${getStatusColor(item.status)}-800`}
                    >
                      {item.status}
                    </span>
                    {item.content?.difficulty && (
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {item.content.difficulty}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'} mb-4`}>
                Created: {new Date(item.created_at).toLocaleDateString()}
              </div>

              <div className="flex gap-2">
                <button
                  onClick={() => setEditingContent(item)}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-1"
                >
                  <FaEdit className="w-3 h-3" />
                  Edit
                </button>
                {item.status === 'draft' && (
                  <button
                    onClick={() => handlePublishContent(item.id, item.title)}
                    className="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-1"
                  >
                    <FaCheck className="w-3 h-3" />
                    Publish
                  </button>
                )}
                <button
                  onClick={() => handleDeleteContent(item.id, item.title)}
                  className="bg-red-500 hover:bg-red-600 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors"
                >
                  <FaTrash className="w-3 h-3" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Modal */}
      {showCreateModal && <ContentCreateModal />}

      {/* Edit Modal */}
      {editingContent && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={`w-full max-w-md ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl p-6`}
          >
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Edit Content
            </h3>
            <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Advanced content editing will be available after running the CMS database schema. For now, you can delete content or edit directly in the database.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setEditingContent(null)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg"
              >
                Close
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );

  // Content Creation Modal Component
  const ContentCreateModal = () => {
    const [formData, setFormData] = useState({
      title: '',
      description: '',
      difficulty: 'beginner',
      category: '',
      content: '',
      points: 100,
      estimatedTime: 30
    });

    const handleSubmit = (e) => {
      e.preventDefault();
      if (!formData.title.trim()) {
        toast.error('Title is required');
        return;
      }
      handleCreateContent(formData);
    };

    const getFieldsForContentType = () => {
      switch (activeContentType) {
        case 'challenge':
          return (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Difficulty
                  </label>
                  <select
                    value={formData.difficulty}
                    onChange={(e) => setFormData({...formData, difficulty: e.target.value})}
                    className={`w-full p-2 rounded border ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                  </select>
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Points
                  </label>
                  <input
                    type="number"
                    value={formData.points}
                    onChange={(e) => setFormData({...formData, points: parseInt(e.target.value)})}
                    className={`w-full p-2 rounded border ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>
              </div>
            </>
          );
        case 'learning_path':
          return (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Difficulty
                  </label>
                  <select
                    value={formData.difficulty}
                    onChange={(e) => setFormData({...formData, difficulty: e.target.value})}
                    className={`w-full p-2 rounded border ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                  </select>
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Estimated Hours
                  </label>
                  <input
                    type="number"
                    value={formData.estimatedTime}
                    onChange={(e) => setFormData({...formData, estimatedTime: parseInt(e.target.value)})}
                    className={`w-full p-2 rounded border ${
                      darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>
              </div>
            </>
          );
        default:
          return null;
      }
    };

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className={`w-full max-w-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl max-h-[90vh] overflow-y-auto`}
        >
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Create New {contentTypes.find(t => t.slug === activeContentType)?.name || 'Content'}
              </h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
              >
                <FaTimes />
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                className={`w-full p-2 rounded border ${
                  darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
                placeholder="Enter content title"
                required
              />
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
                className={`w-full p-2 rounded border ${
                  darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
                placeholder="Enter content description"
                required
              />
            </div>

            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Category
              </label>
              <input
                type="text"
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
                className={`w-full p-2 rounded border ${
                  darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
                placeholder="e.g., Web Security, Network Security"
              />
            </div>

            {getFieldsForContentType()}

            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Content
              </label>
              <textarea
                value={formData.content}
                onChange={(e) => setFormData({...formData, content: e.target.value})}
                rows={6}
                className={`w-full p-2 rounded border ${
                  darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'
                }`}
                placeholder="Enter the main content..."
              />
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={() => setShowCreateModal(false)}
                className={`flex-1 py-2 px-4 rounded-lg ${
                  darkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                }`}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 bg-[#88cc14] hover:bg-[#7ab512] text-white py-2 px-4 rounded-lg font-medium"
              >
                Create Content
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    );
  };
};

export default ContentManagement;
