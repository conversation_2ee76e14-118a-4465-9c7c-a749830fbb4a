import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>a<PERSON><PERSON><PERSON>, FaSearch, FaFilter, FaDownload, FaEye,
  FaUser, FaCode, FaTrash, FaEdit, FaBan, FaUnlock
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { supabase } from '../../lib/supabase';

const ActivityLogs = () => {
  const { darkMode } = useGlobalTheme();
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [actionFilter, setActionFilter] = useState('all');
  const [dateRange, setDateRange] = useState('7d');
  const [selectedLog, setSelectedLog] = useState(null);

  useEffect(() => {
    loadLogs();
  }, [actionFilter, dateRange]);

  const loadLogs = async () => {
    try {
      setLoading(true);
      
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (dateRange) {
        case '24h':
          startDate.setHours(startDate.getHours() - 24);
          break;
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          break;
      }

      let query = supabase
        .from('admin_activity_log')
        .select(`
          *,
          admin_user:profiles!admin_activity_log_admin_user_id_fkey(full_name, email)
        `)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (actionFilter !== 'all') {
        query = query.eq('action', actionFilter);
      }

      const { data, error } = await query;

      if (error) throw error;
      setLogs(data || []);
    } catch (error) {
      console.error('Error loading activity logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActionIcon = (action) => {
    const iconMap = {
      user_created: FaUser,
      user_updated: FaEdit,
      user_deleted: FaTrash,
      user_banned: FaBan,
      user_unbanned: FaUnlock,
      content_created: FaCode,
      content_updated: FaEdit,
      content_deleted: FaTrash,
      content_published: FaEye,
      subscription_changed: FaUser,
      notification_sent: FaEye,
      media_uploaded: FaCode,
      media_deleted: FaTrash
    };
    return iconMap[action] || FaHistory;
  };

  const getActionColor = (action) => {
    if (action.includes('created') || action.includes('uploaded')) return 'text-green-600';
    if (action.includes('updated') || action.includes('published')) return 'text-blue-600';
    if (action.includes('deleted') || action.includes('banned')) return 'text-red-600';
    if (action.includes('unbanned')) return 'text-green-600';
    return 'text-gray-600';
  };

  const formatAction = (action) => {
    return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const filteredLogs = logs.filter(log =>
    log.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.admin_user?.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.details?.title?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const LogDetailModal = ({ log, onClose }) => (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`w-full max-w-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl shadow-2xl overflow-hidden`}
      >
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Activity Log Details
            </h2>
            <button
              onClick={onClose}
              className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
            >
              ×
            </button>
          </div>
        </div>

        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Action
              </label>
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {formatAction(log.action)}
              </p>
            </div>
            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Admin User
              </label>
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {log.admin_user?.full_name || log.admin_user?.email || 'Unknown'}
              </p>
            </div>
            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Target Type
              </label>
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {log.target_type || 'N/A'}
              </p>
            </div>
            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Target ID
              </label>
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'} font-mono`}>
                {log.target_id || 'N/A'}
              </p>
            </div>
            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Timestamp
              </label>
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {new Date(log.created_at).toLocaleString()}
              </p>
            </div>
            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                IP Address
              </label>
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'} font-mono`}>
                {log.ip_address || 'N/A'}
              </p>
            </div>
          </div>

          {log.details && Object.keys(log.details).length > 0 && (
            <div>
              <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Details
              </label>
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <pre className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-600'} whitespace-pre-wrap`}>
                  {JSON.stringify(log.details, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {log.user_agent && (
            <div>
              <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                User Agent
              </label>
              <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} break-all`}>
                {log.user_agent}
              </p>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Activity Logs
          </h2>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Monitor all admin activities and system events
          </p>
        </div>
        <div className="flex gap-3">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className={`px-4 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            <FaDownload />
            Export Logs
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="flex-1 relative">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search logs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>
        <select
          value={actionFilter}
          onChange={(e) => setActionFilter(e.target.value)}
          className={`px-4 py-2 rounded-lg border ${
            darkMode
              ? 'bg-gray-700 border-gray-600 text-white'
              : 'bg-white border-gray-300 text-gray-900'
          } focus:outline-none focus:ring-2 focus:ring-blue-500`}
        >
          <option value="all">All Actions</option>
          <option value="user_created">User Created</option>
          <option value="user_updated">User Updated</option>
          <option value="user_deleted">User Deleted</option>
          <option value="content_created">Content Created</option>
          <option value="content_updated">Content Updated</option>
          <option value="content_deleted">Content Deleted</option>
          <option value="subscription_changed">Subscription Changed</option>
        </select>
      </div>

      {/* Activity Logs */}
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={darkMode ? 'text-gray-400' : 'text-gray-600'}>Loading activity logs...</p>
        </div>
      ) : filteredLogs.length === 0 ? (
        <div className="text-center py-12">
          <FaHistory className={`h-16 w-16 ${darkMode ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} />
          <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            No activity logs found
          </h3>
          <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {searchQuery ? 'Try adjusting your search criteria.' : 'No admin activities recorded in the selected time range.'}
          </p>
        </div>
      ) : (
        <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-xl overflow-hidden`}>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredLogs.map((log) => {
              const ActionIcon = getActionIcon(log.action);
              return (
                <div
                  key={log.id}
                  className={`p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors`}
                  onClick={() => setSelectedLog(log)}
                >
                  <div className="flex items-start gap-4">
                    <div className={`p-2 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                      <ActionIcon className={`h-5 w-5 ${getActionColor(log.action)}`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                          {formatAction(log.action)}
                        </h3>
                        <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {new Date(log.created_at).toLocaleString()}
                        </span>
                      </div>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
                        By {log.admin_user?.full_name || log.admin_user?.email || 'Unknown Admin'}
                        {log.target_type && ` • Target: ${log.target_type}`}
                      </p>
                      {log.details?.title && (
                        <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'} mt-1`}>
                          {log.details.title}
                        </p>
                      )}
                    </div>
                    <FaEye className={`h-4 w-4 ${darkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Log Detail Modal */}
      {selectedLog && (
        <LogDetailModal
          log={selectedLog}
          onClose={() => setSelectedLog(null)}
        />
      )}
    </div>
  );
};

export default ActivityLogs;
