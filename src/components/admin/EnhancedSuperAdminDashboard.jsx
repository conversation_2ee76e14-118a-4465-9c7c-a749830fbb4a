import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import {
  FaUsers, FaTrophy, FaBook, FaBell, FaChartLine, FaUserShield,
  FaCog, FaPlus, FaEdit, FaTrash, FaBan, FaUnlock, FaEnvelope,
  FaEye, FaUserTimes, FaUserCheck, FaCalendarAlt, FaCrown,
  FaDatabase, FaFileAlt, FaImage, FaCode, FaShieldAlt,
  FaNetworkWired, FaServer, FaLock, FaGraduationCap,
  FaHistory, FaUpload, FaDownload, FaSync, FaExclamationTriangle, 
  FaCheck, FaBars, FaTimes, FaHome, FaCertificate, FaMedal,
  FaChartBar, FaChartPie, FaArrowUp, FaArrowDown, FaEquals,
  FaUserGraduate, FaGamepad, FaAward, FaClipboardList
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

// Import admin components
import NotificationTester from './NotificationTester';
import UserManagement from './UserManagement';
import ContentManagement from './ContentManagement';
import SystemAnalytics from './SystemAnalytics';
import MediaLibrary from './MediaLibrary';
import ActivityLogs from './ActivityLogs';

const EnhancedSuperAdminDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [realTimeStats, setRealTimeStats] = useState({
    onlineUsers: 0,
    todaySignups: 0,
    activeEnrollments: 0,
    completionRate: 0
  });

  useEffect(() => {
    checkSuperAdminAccess();
  }, [user]);

  useEffect(() => {
    if (isAuthorized) {
      loadDashboardData();
      loadRealTimeStats();
      // Set up real-time updates
      const interval = setInterval(loadRealTimeStats, 30000); // Update every 30 seconds
      return () => clearInterval(interval);
    }
  }, [isAuthorized]);

  const checkSuperAdminAccess = async () => {
    if (!user) {
      setIsAuthorized(false);
      setLoading(false);
      return;
    }

    try {
      // Check if user is super admin
      const { data, error } = await supabase
        .rpc('is_super_admin', { user_uuid: user.id });

      if (error) {
        // Fallback: check admin_roles table directly
        const { data: adminRole, error: roleError } = await supabase
          .from('admin_roles')
          .select('role, is_active')
          .eq('user_id', user.id)
          .eq('role', 'super_admin')
          .eq('is_active', true)
          .single();

        if (roleError) {
          // Final fallback: hardcoded UUID
          if (user.id === '5971f7c3-840f-4d2c-9931-db26d1978f5a') {
            setIsAuthorized(true);
            return;
          } else {
            setIsAuthorized(false);
            return;
          }
        }

        setIsAuthorized(adminRole && adminRole.role === 'super_admin');
      } else {
        setIsAuthorized(data === true);
      }
      
    } catch (error) {
      console.error('Error checking super admin access:', error);
      // Fallback for your specific UUID
      if (user.id === '5971f7c3-840f-4d2c-9931-db26d1978f5a') {
        setIsAuthorized(true);
      } else {
        setIsAuthorized(false);
      }
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load comprehensive statistics with error handling
      const results = await Promise.allSettled([
        supabase.from('profiles').select('id, subscription_tier, created_at', { count: 'exact' }),
        supabase.from('challenges').select('id', { count: 'exact' }),
        supabase.from('learning_paths').select('id', { count: 'exact' }),
        supabase.from('user_notifications').select('id', { count: 'exact' }),
        supabase.from('cms_media').select('id', { count: 'exact' }),
        supabase.from('admin_activity_log').select('id', { count: 'exact' }),
        supabase.from('user_enrollments').select('id, status', { count: 'exact' }),
        supabase.from('user_achievements').select('id', { count: 'exact' }),
        supabase.from('user_certificates').select('id', { count: 'exact' })
      ]);

      // Extract counts with fallbacks
      const [
        usersResult,
        challengesResult,
        learningPathsResult,
        notificationsResult,
        mediaResult,
        activityResult,
        enrollmentsResult,
        achievementsResult,
        certificatesResult
      ] = results.map(result => 
        result.status === 'fulfilled' ? result.value : { count: 0, data: [] }
      );

      // Get user statistics with subscription breakdown
      let userStats = { total: 0, premium: 0, free: 0, newToday: 0 };
      if (usersResult.data) {
        const today = new Date().toDateString();
        userStats = {
          total: usersResult.count || 0,
          premium: usersResult.data.filter(u => u.subscription_tier === 'premium').length,
          free: usersResult.data.filter(u => u.subscription_tier === 'free' || !u.subscription_tier).length,
          newToday: usersResult.data.filter(u => new Date(u.created_at).toDateString() === today).length
        };
      }

      // Get enrollment statistics
      let enrollmentStats = { total: 0, active: 0, completed: 0 };
      if (enrollmentsResult.data) {
        enrollmentStats = {
          total: enrollmentsResult.count || 0,
          active: enrollmentsResult.data.filter(e => e.status === 'in_progress').length,
          completed: enrollmentsResult.data.filter(e => e.status === 'completed').length
        };
      }

      setStats({
        users: userStats,
        totalChallenges: challengesResult.count || 0,
        totalLearningPaths: learningPathsResult.count || 0,
        totalNotifications: notificationsResult.count || 0,
        totalMedia: mediaResult.count || 0,
        totalActivity: activityResult.count || 0,
        enrollments: enrollmentStats,
        totalAchievements: achievementsResult.count || 0,
        totalCertificates: certificatesResult.count || 0
      });

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Set default stats if everything fails
      setStats({
        users: { total: 0, premium: 0, free: 0, newToday: 0 },
        totalChallenges: 0,
        totalLearningPaths: 0,
        totalNotifications: 0,
        totalMedia: 0,
        totalActivity: 0,
        enrollments: { total: 0, active: 0, completed: 0 },
        totalAchievements: 0,
        totalCertificates: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const loadRealTimeStats = async () => {
    try {
      // Get online users (users active in last 5 minutes)
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();
      const { data: onlineUsers } = await supabase
        .from('user_sessions')
        .select('user_id')
        .gte('session_start', fiveMinutesAgo)
        .is('session_end', null);

      // Get today's signups
      const today = new Date().toISOString().split('T')[0];
      const { data: todaySignups } = await supabase
        .from('profiles')
        .select('id')
        .gte('created_at', today);

      // Get active enrollments
      const { data: activeEnrollments } = await supabase
        .from('user_enrollments')
        .select('id')
        .eq('status', 'in_progress');

      // Calculate completion rate
      const { data: allEnrollments } = await supabase
        .from('user_enrollments')
        .select('status');

      const completionRate = allEnrollments?.length > 0 
        ? (allEnrollments.filter(e => e.status === 'completed').length / allEnrollments.length * 100).toFixed(1)
        : 0;

      setRealTimeStats({
        onlineUsers: onlineUsers?.length || 0,
        todaySignups: todaySignups?.length || 0,
        activeEnrollments: activeEnrollments?.length || 0,
        completionRate: parseFloat(completionRate)
      });

    } catch (error) {
      console.error('Error loading real-time stats:', error);
    }
  };

  const exportData = async (dataType) => {
    try {
      let data = [];
      let filename = '';

      switch (dataType) {
        case 'users':
          const { data: users } = await supabase
            .from('profiles')
            .select('*');
          data = users || [];
          filename = 'users_export.csv';
          break;
        case 'enrollments':
          const { data: enrollments } = await supabase
            .from('user_enrollments')
            .select('*');
          data = enrollments || [];
          filename = 'enrollments_export.csv';
          break;
        case 'analytics':
          const { data: analytics } = await supabase
            .from('daily_analytics')
            .select('*')
            .order('date', { ascending: false })
            .limit(30);
          data = analytics || [];
          filename = 'analytics_export.csv';
          break;
        default:
          toast.error('Invalid export type');
          return;
      }

      if (data.length === 0) {
        toast.error('No data to export');
        return;
      }

      // Convert to CSV
      const headers = Object.keys(data[0]);
      const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => 
          JSON.stringify(row[header] || '')
        ).join(','))
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      a.click();
      window.URL.revokeObjectURL(url);

      toast.success(`${dataType} data exported successfully!`);

      // Log admin activity
      try {
        await supabase.rpc('log_admin_activity', {
          action_param: 'data_exported',
          target_type_param: dataType,
          details_param: { filename, record_count: data.length }
        });
      } catch (logError) {
        console.log('Activity logging not available yet');
      }

    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Failed to export data');
    }
  };

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: FaHome },
    { id: 'users', label: 'User Management', icon: FaUsers },
    { id: 'content', label: 'Content Management', icon: FaBook },
    { id: 'notifications', label: 'Notifications', icon: FaBell },
    { id: 'media', label: 'Media Library', icon: FaImage },
    { id: 'analytics', label: 'Analytics', icon: FaChartLine },
    { id: 'leaderboard', label: 'Leaderboard', icon: FaTrophy },
    { id: 'certificates', label: 'Certificates', icon: FaCertificate },
    { id: 'activity', label: 'Activity Logs', icon: FaHistory },
    { id: 'settings', label: 'Settings', icon: FaCog }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#88cc14] mx-auto mb-4"></div>
          <p className={`text-lg ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Loading Super Admin Dashboard...
          </p>
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <FaUserShield className={`h-16 w-16 ${darkMode ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} />
          <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
            Access Denied
          </h2>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            You don't have permission to access the Super Admin Dashboard.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-50'} flex`}>
      {/* Sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            transition={{ duration: 0.3 }}
            className={`fixed inset-y-0 left-0 z-50 w-64 ${
              darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            } border-r shadow-lg lg:relative lg:translate-x-0`}
          >
            {/* Sidebar Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <FaCrown className="text-[#88cc14] text-2xl" />
                <h1 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Super Admin
                </h1>
              </div>
              <button
                onClick={() => setSidebarOpen(false)}
                className={`lg:hidden p-2 rounded-lg ${
                  darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                <FaTimes />
              </button>
            </div>

            {/* Sidebar Navigation */}
            <nav className="p-4 space-y-2">
              {sidebarItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors ${
                      activeTab === item.id
                        ? 'bg-[#88cc14] text-white'
                        : darkMode
                          ? 'text-gray-300 hover:bg-gray-700'
                          : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {item.label}
                  </button>
                );
              })}
            </nav>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Top Header */}
        <header className={`${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } border-b px-6 py-4 flex items-center justify-between`}>
          <div className="flex items-center gap-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className={`p-2 rounded-lg ${
                darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
              }`}
            >
              <FaBars />
            </button>
            <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {sidebarItems.find(item => item.id === activeTab)?.label || 'Dashboard'}
            </h2>
          </div>

          <div className="flex items-center gap-4">
            {/* Real-time indicators */}
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className={darkMode ? 'text-gray-300' : 'text-gray-600'}>
                  {realTimeStats.onlineUsers} online
                </span>
              </div>
              <div className={`px-2 py-1 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <span className={darkMode ? 'text-gray-300' : 'text-gray-600'}>
                  {realTimeStats.todaySignups} signups today
                </span>
              </div>
            </div>

            {/* Export dropdown */}
            <div className="relative">
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    exportData(e.target.value);
                    e.target.value = '';
                  }
                }}
                className={`px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="">Export Data</option>
                <option value="users">Users CSV</option>
                <option value="enrollments">Enrollments CSV</option>
                <option value="analytics">Analytics CSV</option>
              </select>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 p-6 overflow-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              {activeTab === 'overview' && <OverviewDashboard stats={stats} realTimeStats={realTimeStats} />}
              {activeTab === 'users' && <UserManagement />}
              {activeTab === 'content' && <ContentManagement />}
              {activeTab === 'notifications' && <NotificationTester />}
              {activeTab === 'media' && <MediaLibrary />}
              {activeTab === 'analytics' && <SystemAnalytics />}
              {activeTab === 'activity' && <ActivityLogs />}
              {activeTab === 'leaderboard' && <LeaderboardManagement />}
              {activeTab === 'certificates' && <CertificateManagement />}
              {activeTab === 'settings' && <SystemSettings />}
            </motion.div>
          </AnimatePresence>
        </main>
      </div>
    </div>
  );
};

// Overview Dashboard Component
const OverviewDashboard = ({ stats, realTimeStats }) => {
  const { darkMode } = useGlobalTheme();

  const MetricCard = ({ title, value, icon: Icon, color = 'blue', trend, subtitle, onClick }) => (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      } border rounded-xl p-6 hover:shadow-lg transition-all duration-300 cursor-pointer`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {title}
          </p>
          <p className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mt-2`}>
            {value}
          </p>
          {subtitle && (
            <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'} mt-1`}>
              {subtitle}
            </p>
          )}
          {trend !== undefined && (
            <div className="flex items-center mt-2">
              {trend > 0 ? (
                <FaArrowUp className="text-green-500 mr-1" />
              ) : trend < 0 ? (
                <FaArrowDown className="text-red-500 mr-1" />
              ) : (
                <FaEquals className="text-gray-500 mr-1" />
              )}
              <span className={`text-sm ${
                trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-gray-500'
              }`}>
                {Math.abs(trend)}%
              </span>
            </div>
          )}
        </div>
        <div className={`p-4 rounded-xl bg-${color}-100 dark:bg-${color}-900/30`}>
          <Icon className={`h-8 w-8 text-${color}-600 dark:text-${color}-400`} />
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Real-time Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Online Users"
          value={realTimeStats.onlineUsers}
          icon={FaUsers}
          color="green"
          subtitle="Currently active"
        />
        <MetricCard
          title="Today's Signups"
          value={realTimeStats.todaySignups}
          icon={FaUserCheck}
          color="blue"
          subtitle="New registrations"
        />
        <MetricCard
          title="Active Enrollments"
          value={realTimeStats.activeEnrollments}
          icon={FaGraduationCap}
          color="purple"
          subtitle="In progress"
        />
        <MetricCard
          title="Completion Rate"
          value={`${realTimeStats.completionRate}%`}
          icon={FaTrophy}
          color="yellow"
          subtitle="Overall success rate"
        />
      </div>

      {/* Main Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <MetricCard
          title="Total Users"
          value={stats.users?.total || 0}
          icon={FaUsers}
          color="blue"
          subtitle={`${stats.users?.premium || 0} premium, ${stats.users?.free || 0} free`}
        />
        <MetricCard
          title="Challenges"
          value={stats.totalChallenges}
          icon={FaCode}
          color="red"
          subtitle="Available challenges"
        />
        <MetricCard
          title="Learning Paths"
          value={stats.totalLearningPaths}
          icon={FaGraduationCap}
          color="green"
          subtitle="Structured courses"
        />
        <MetricCard
          title="Total Enrollments"
          value={stats.enrollments?.total || 0}
          icon={FaClipboardList}
          color="purple"
          subtitle={`${stats.enrollments?.completed || 0} completed`}
        />
        <MetricCard
          title="Achievements"
          value={stats.totalAchievements}
          icon={FaAward}
          color="yellow"
          subtitle="User achievements"
        />
        <MetricCard
          title="Certificates"
          value={stats.totalCertificates}
          icon={FaCertificate}
          color="indigo"
          subtitle="Issued certificates"
        />
      </div>

      {/* Quick Actions */}
      <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-xl p-6`}>
        <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="flex items-center gap-3 p-4 rounded-lg bg-blue-500 hover:bg-blue-600 text-white transition-colors">
            <FaPlus />
            <span>Create Content</span>
          </button>
          <button className="flex items-center gap-3 p-4 rounded-lg bg-green-500 hover:bg-green-600 text-white transition-colors">
            <FaBell />
            <span>Send Notification</span>
          </button>
          <button className="flex items-center gap-3 p-4 rounded-lg bg-purple-500 hover:bg-purple-600 text-white transition-colors">
            <FaUpload />
            <span>Upload Media</span>
          </button>
          <button className="flex items-center gap-3 p-4 rounded-lg bg-orange-500 hover:bg-orange-600 text-white transition-colors">
            <FaDownload />
            <span>Export Data</span>
          </button>
        </div>
      </div>

      {/* System Health */}
      <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-xl p-6`}>
        <h3 className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
          System Health
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-green-100 flex items-center justify-center">
              <FaDatabase className="w-8 h-8 text-green-600" />
            </div>
            <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Database</p>
            <p className="text-sm text-green-600">Operational</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-green-100 flex items-center justify-center">
              <FaServer className="w-8 h-8 text-green-600" />
            </div>
            <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>API</p>
            <p className="text-sm text-green-600">Operational</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-green-100 flex items-center justify-center">
              <FaShieldAlt className="w-8 h-8 text-green-600" />
            </div>
            <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Security</p>
            <p className="text-sm text-green-600">Operational</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Placeholder components for other sections
const LeaderboardManagement = () => {
  const { darkMode } = useGlobalTheme();
  return (
    <div className="text-center py-12">
      <FaTrophy className={`h-16 w-16 ${darkMode ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} />
      <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
        Leaderboard Management
      </h3>
      <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        Manage rankings, competitions, and user achievements.
      </p>
    </div>
  );
};

const CertificateManagement = () => {
  const { darkMode } = useGlobalTheme();
  return (
    <div className="text-center py-12">
      <FaCertificate className={`h-16 w-16 ${darkMode ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} />
      <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
        Certificate Management
      </h3>
      <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        Issue, manage, and verify user certificates.
      </p>
    </div>
  );
};

const SystemSettings = () => {
  const { darkMode } = useGlobalTheme();
  return (
    <div className="text-center py-12">
      <FaCog className={`h-16 w-16 ${darkMode ? 'text-gray-600' : 'text-gray-400'} mx-auto mb-4`} />
      <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
        System Settings
      </h3>
      <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        Configure system settings, permissions, and preferences.
      </p>
    </div>
  );
};

export default EnhancedSuperAdminDashboard;
