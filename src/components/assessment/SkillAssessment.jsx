import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaArrowRight, FaCheck, FaTimes, FaSpinner, FaLightbulb, FaGraduationCap, FaChartLine } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { getSkillAssessmentQuestions, evaluateAssessment, getPersonalizedLearningPaths } from '../../services/AIRecommendationService';

/**
 * SkillAssessment Component
 *
 * A comprehensive skill assessment tool that evaluates user knowledge
 * and provides personalized learning path recommendations.
 */
const SkillAssessment = ({ onComplete }) => {
  const { darkMode } = useGlobalTheme();
  const { user, profile } = useAuth();
  const [step, setStep] = useState('intro'); // intro, experience, assessment, results
  const [experienceLevel, setExperienceLevel] = useState('');
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [recommendations, setRecommendations] = useState([]);

  // Load questions based on experience level
  useEffect(() => {
    if (step === 'assessment' && experienceLevel) {
      const loadQuestions = async () => {
        setLoading(true);
        try {
          const assessmentQuestions = await getSkillAssessmentQuestions(experienceLevel);
          setQuestions(assessmentQuestions);
        } catch (error) {
          console.error('Error loading assessment questions:', error);
        } finally {
          setLoading(false);
        }
      };

      loadQuestions();
    }
  }, [step, experienceLevel]);

  // Handle experience level selection
  const handleExperienceSelect = (level) => {
    setExperienceLevel(level);
    setStep('assessment');
  };

  // Handle answer selection
  const handleAnswerSelect = (questionId, selectedAnswer) => {
    const currentQuestion = questions[currentQuestionIndex];
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer;

    // Record answer
    setAnswers([
      ...answers,
      {
        questionId,
        selectedAnswer,
        isCorrect,
        category: currentQuestion.category
      }
    ]);

    // Move to next question or results
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      // All questions answered, evaluate results
      evaluateResults();
    }
  };

  // Evaluate assessment results
  const evaluateResults = async () => {
    setLoading(true);
    try {
      // Evaluate answers
      const assessmentResults = evaluateAssessment(answers);
      setResults(assessmentResults);

      // Get personalized recommendations
      const pathRecommendations = await getPersonalizedLearningPaths(profile, assessmentResults);
      setRecommendations(pathRecommendations);

      // Save results to localStorage for persistence
      localStorage.setItem(`assessmentResults_${user?.id || 'anonymous'}`, JSON.stringify({
        results: assessmentResults,
        timestamp: Date.now()
      }));

      // Move to results step
      setStep('results');
    } catch (error) {
      console.error('Error evaluating assessment:', error);
    } finally {
      setLoading(false);
    }
  };

  // Skip assessment
  const handleSkipAssessment = () => {
    setStep('results');
    setResults({
      skills: {
        networking: 0,
        operating_systems: 0,
        web_security: 0,
        cryptography: 0,
        security_fundamentals: 0
      },
      overallLevel: 'beginner',
      weakestSkills: ['networking', 'security_fundamentals'],
      overallScore: 0
    });

    // Get default recommendations
    getPersonalizedLearningPaths(profile).then(recs => {
      setRecommendations(recs);
    });
  };

  // Complete assessment and pass results to parent
  const handleComplete = () => {
    if (onComplete) {
      onComplete({
        results,
        recommendations,
        experienceLevel
      });
    }
  };

  // Render introduction step
  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg max-w-2xl mx-auto`}
    >
      <div className="text-center mb-6">
        <FaGraduationCap className={`text-4xl mx-auto mb-4 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
        <h2 className={`text-2xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Personalized Learning Assessment</h2>
        <p className={`${darkMode ? 'text-gray-300' : 'text-gray-900'}`}>
          Take this quick assessment to help us personalize your learning experience.
          We'll recommend the best learning paths based on your current knowledge and skills.
        </p>
      </div>

      <div className="space-y-4 mb-6">
        <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} p-4 rounded-lg`}>
          <h3 className={`font-medium mb-2 flex items-center ${darkMode ? 'text-white' : 'text-gray-800'}`}>
            <FaLightbulb className="mr-2 text-yellow-500" /> Why take this assessment?
          </h3>
          <ul className={`list-disc pl-5 space-y-1 ${darkMode ? 'text-gray-300' : 'text-gray-900'}`}>
            <li>Get personalized learning recommendations</li>
            <li>Identify your strengths and areas for improvement</li>
            <li>Save time by focusing on what you need to learn</li>
            <li>Track your progress over time</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={handleSkipAssessment}
          className={`px-4 py-2 rounded-lg ${
            darkMode
              ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
          }`}
        >
          Skip Assessment
        </button>
        <button
          onClick={() => setStep('experience')}
          className={`px-4 py-2 rounded-lg flex items-center ${
            darkMode
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
        >
          Start Assessment <FaArrowRight className="ml-2" />
        </button>
      </div>
    </motion.div>
  );

  // Render experience level selection step
  const renderExperienceSelection = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg max-w-2xl mx-auto`}
    >
      <h2 className={`text-2xl font-bold mb-4 text-center ${darkMode ? 'text-white' : 'text-gray-800'}`}>What's your cybersecurity experience level?</h2>
      <p className={`text-center mb-6 ${darkMode ? 'text-gray-300' : 'text-gray-900'}`}>
        This helps us tailor the assessment questions to your level.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <button
          onClick={() => handleExperienceSelect('beginner')}
          className={`p-4 rounded-lg border-2 transition-all ${
            darkMode
              ? 'hover:bg-gray-700 border-gray-700 hover:border-blue-500'
              : 'hover:bg-gray-100 border-gray-200 hover:border-blue-500'
          }`}
        >
          <h3 className={`font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Beginner</h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-900'}`}>
            New to cybersecurity or just starting to learn the basics.
          </p>
        </button>

        <button
          onClick={() => handleExperienceSelect('intermediate')}
          className={`p-4 rounded-lg border-2 transition-all ${
            darkMode
              ? 'hover:bg-gray-700 border-gray-700 hover:border-blue-500'
              : 'hover:bg-gray-100 border-gray-200 hover:border-blue-500'
          }`}
        >
          <h3 className={`font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Intermediate</h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-900'}`}>
            Familiar with core concepts and some practical experience.
          </p>
        </button>

        <button
          onClick={() => handleExperienceSelect('advanced')}
          className={`p-4 rounded-lg border-2 transition-all ${
            darkMode
              ? 'hover:bg-gray-700 border-gray-700 hover:border-blue-500'
              : 'hover:bg-gray-100 border-gray-200 hover:border-blue-500'
          }`}
        >
          <h3 className={`font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Advanced</h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-900'}`}>
            Experienced professional with deep knowledge in multiple areas.
          </p>
        </button>
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => setStep('intro')}
          className={`px-4 py-2 rounded-lg ${
            darkMode
              ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
          }`}
        >
          Back
        </button>
        <button
          onClick={handleSkipAssessment}
          className={`px-4 py-2 rounded-lg ${
            darkMode
              ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
          }`}
        >
          Skip Assessment
        </button>
      </div>
    </motion.div>
  );

  // Render assessment questions
  const renderAssessment = () => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <FaSpinner className="animate-spin text-4xl mb-4" />
          <p>Loading assessment questions...</p>
        </div>
      );
    }

    if (questions.length === 0) {
      return (
        <div className="text-center py-12">
          <p className="mb-4">No questions available for this assessment.</p>
          <button
            onClick={() => setStep('experience')}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
          >
            Back
          </button>
        </div>
      );
    }

    const currentQuestion = questions[currentQuestionIndex];

    return (
      <motion.div
        key={currentQuestionIndex}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg max-w-2xl mx-auto`}
      >
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-900'}`}>
              Question {currentQuestionIndex + 1} of {questions.length}
            </span>
            <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-900'}`}>
              Category: {currentQuestion.category.replace('_', ' ')}
            </span>
          </div>
          <h3 className={`text-xl font-medium mb-4 ${darkMode ? 'text-white' : 'text-gray-800'}`}>{currentQuestion.question}</h3>
          <div className="space-y-3">
            {currentQuestion.options.map((option) => (
              <button
                key={option}
                onClick={() => handleAnswerSelect(currentQuestion.id, option)}
                className={`w-full text-left p-3 rounded-lg border transition-colors ${
                  darkMode
                    ? 'border-gray-700 hover:bg-gray-700 text-gray-200'
                    : 'border-gray-200 hover:bg-gray-100 text-gray-800'
                }`}
              >
                {option}
              </button>
            ))}
          </div>
        </div>

        <div className="flex justify-between">
          <button
            onClick={handleSkipAssessment}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
            }`}
          >
            Skip Assessment
          </button>
          <div className="flex items-center">
            <span className={`text-sm mr-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {currentQuestionIndex + 1} / {questions.length}
            </span>
          </div>
        </div>
      </motion.div>
    );
  };

  // Render assessment results
  const renderResults = () => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <FaSpinner className="animate-spin text-4xl mb-4" />
          <p>Analyzing your results...</p>
        </div>
      );
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 shadow-lg max-w-2xl mx-auto`}
      >
        <div className="text-center mb-6">
          <FaChartLine className={`text-4xl mx-auto mb-4 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
          <h2 className={`text-2xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Your Assessment Results</h2>
          <p className={`${darkMode ? 'text-gray-300' : 'text-gray-900'}`}>
            Based on your answers, we've created a personalized learning plan for you.
          </p>
        </div>

        {results && (
          <div className="mb-6">
            <div className={`p-4 rounded-lg mb-4 ${
              darkMode ? 'bg-blue-900/20 border border-blue-800' : 'bg-blue-50 border border-blue-200'
            }`}>
              <h3 className={`font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Overall Skill Level: {results.overallLevel}</h3>
              <div className="w-full bg-gray-300 dark:bg-gray-700 h-4 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-500"
                  style={{ width: `${results.overallScore}%` }}
                ></div>
              </div>
              <p className="text-sm mt-1 text-right">{results.overallScore}%</p>
            </div>

            <h3 className={`font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Skill Breakdown</h3>
            <div className="space-y-3">
              {Object.entries(results.skills).map(([skill, level]) => (
                <div key={skill} className="space-y-1">
                  <div className="flex justify-between">
                    <span className={`capitalize ${darkMode ? 'text-gray-300' : 'text-gray-900'}`}>{skill.replace('_', ' ')}</span>
                    <span className={darkMode ? 'text-gray-300' : 'text-gray-900'}>{level}/5</span>
                  </div>
                  <div className="w-full bg-gray-300 dark:bg-gray-700 h-2 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${level < 2 ? 'bg-red-500' : level < 4 ? 'bg-yellow-500' : 'bg-green-500'}`}
                      style={{ width: `${(level / 5) * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="mb-6">
          <h3 className={`font-medium mb-3 ${darkMode ? 'text-white' : 'text-gray-800'}`}>Recommended Learning Paths</h3>
          {recommendations.length > 0 ? (
            <div className="space-y-3">
              {recommendations.map((path) => (
                <div
                  key={path.id}
                  className={`p-3 rounded-lg border ${
                    darkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-100'
                  } transition-colors cursor-pointer`}
                >
                  <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>{path.title}</h4>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-900'}`}>
                    {path.description}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-900'}`}>
              No specific recommendations available. Try exploring our beginner learning paths.
            </p>
          )}
        </div>

        <div className="flex justify-between">
          <button
            onClick={() => setStep('intro')}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
            }`}
          >
            Restart Assessment
          </button>
          <button
            onClick={handleComplete}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
          >
            Continue to Dashboard
          </button>
        </div>
      </motion.div>
    );
  };

  // Render the appropriate step
  const renderStep = () => {
    switch (step) {
      case 'intro':
        return renderIntro();
      case 'experience':
        return renderExperienceSelection();
      case 'assessment':
        return renderAssessment();
      case 'results':
        return renderResults();
      default:
        return renderIntro();
    }
  };

  return (
    <div className="py-8">
      {renderStep()}
    </div>
  );
};

export default SkillAssessment;
