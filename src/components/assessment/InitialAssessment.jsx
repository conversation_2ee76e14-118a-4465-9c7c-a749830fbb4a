import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaGraduationCap, 
  FaCode, 
  FaShieldAlt, 
  FaServer, 
  FaNetworkWired,
  FaArrowRight,
  FaArrowLeft,
  FaCheck,
  FaTimes
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

const InitialAssessment = ({ isOpen, onClose, onComplete }) => {
  const { darkMode } = useGlobalTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const assessmentQuestions = [
    {
      id: 'experience',
      title: 'What is your cybersecurity experience level?',
      type: 'single',
      options: [
        { value: 'beginner', label: 'Complete Beginner', description: 'New to cybersecurity', points: 1 },
        { value: 'some_knowledge', label: 'Some Knowledge', description: 'Basic understanding', points: 2 },
        { value: 'intermediate', label: 'Intermediate', description: '1-3 years experience', points: 3 },
        { value: 'advanced', label: 'Advanced', description: '3+ years experience', points: 4 }
      ]
    },
    {
      id: 'interests',
      title: 'Which areas interest you most?',
      type: 'multiple',
      options: [
        { value: 'ethical_hacking', label: 'Ethical Hacking', icon: FaCode, points: 3 },
        { value: 'network_security', label: 'Network Security', icon: FaNetworkWired, points: 3 },
        { value: 'web_security', label: 'Web Application Security', icon: FaShieldAlt, points: 3 },
        { value: 'system_admin', label: 'System Administration', icon: FaServer, points: 2 },
        { value: 'compliance', label: 'Compliance & Governance', icon: FaGraduationCap, points: 2 }
      ]
    },
    {
      id: 'goals',
      title: 'What are your primary learning goals?',
      type: 'single',
      options: [
        { value: 'career_change', label: 'Career Change', description: 'Switch to cybersecurity', points: 4 },
        { value: 'skill_upgrade', label: 'Skill Upgrade', description: 'Enhance current skills', points: 3 },
        { value: 'certification', label: 'Certification Prep', description: 'Prepare for certifications', points: 3 },
        { value: 'personal_interest', label: 'Personal Interest', description: 'Learn for fun', points: 2 }
      ]
    },
    {
      id: 'time_commitment',
      title: 'How much time can you dedicate to learning per week?',
      type: 'single',
      options: [
        { value: '1-3', label: '1-3 hours', points: 1 },
        { value: '4-7', label: '4-7 hours', points: 2 },
        { value: '8-15', label: '8-15 hours', points: 3 },
        { value: '15+', label: '15+ hours', points: 4 }
      ]
    },
    {
      id: 'learning_style',
      title: 'How do you prefer to learn?',
      type: 'multiple',
      options: [
        { value: 'hands_on', label: 'Hands-on Labs', points: 3 },
        { value: 'video', label: 'Video Tutorials', points: 2 },
        { value: 'reading', label: 'Reading Materials', points: 2 },
        { value: 'interactive', label: 'Interactive Challenges', points: 3 },
        { value: 'mentorship', label: 'Mentorship/Guidance', points: 2 }
      ]
    }
  ];

  const handleAnswer = (questionId, value, isMultiple = false) => {
    if (isMultiple) {
      setAnswers(prev => ({
        ...prev,
        [questionId]: prev[questionId] 
          ? prev[questionId].includes(value)
            ? prev[questionId].filter(v => v !== value)
            : [...prev[questionId], value]
          : [value]
      }));
    } else {
      setAnswers(prev => ({
        ...prev,
        [questionId]: value
      }));
    }
  };

  const calculateResults = () => {
    let totalScore = 0;
    let recommendations = [];

    // Calculate total score
    assessmentQuestions.forEach(question => {
      const answer = answers[question.id];
      if (answer) {
        if (Array.isArray(answer)) {
          answer.forEach(value => {
            const option = question.options.find(opt => opt.value === value);
            if (option) totalScore += option.points;
          });
        } else {
          const option = question.options.find(opt => opt.value === answer);
          if (option) totalScore += option.points;
        }
      }
    });

    // Generate recommendations based on answers
    const experience = answers.experience;
    const interests = answers.interests || [];
    const goals = answers.goals;

    // Recommend learning paths based on experience and interests
    if (experience === 'beginner') {
      recommendations.push('network-fundamentals');
      if (interests.includes('ethical_hacking')) {
        recommendations.push('ethical-hacking-fundamentals');
      }
    } else if (experience === 'intermediate' || experience === 'advanced') {
      if (interests.includes('ethical_hacking')) {
        recommendations.push('advanced-penetration-testing');
      }
      if (interests.includes('network_security')) {
        recommendations.push('network-security-advanced');
      }
    }

    // Default recommendation if none match
    if (recommendations.length === 0) {
      recommendations.push('network-fundamentals');
    }

    return {
      score: totalScore,
      level: totalScore < 8 ? 'beginner' : totalScore < 15 ? 'intermediate' : 'advanced',
      recommendations,
      answers
    };
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      const results = calculateResults();
      
      // Save results to localStorage
      localStorage.setItem('assessmentResults', JSON.stringify(results));
      localStorage.setItem('assessmentCompleted', 'true');
      
      // Call the completion callback
      onComplete(results);
      
    } catch (error) {
      console.error('Error submitting assessment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentQuestion = assessmentQuestions[currentStep];
  const isLastStep = currentStep === assessmentQuestions.length - 1;
  const canProceed = answers[currentQuestion?.id];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className={`w-full max-w-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-2xl shadow-2xl overflow-hidden`}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Cybersecurity Assessment</h2>
              <p className="text-blue-100">Help us personalize your learning journey</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <FaTimes />
            </button>
          </div>
          
          {/* Progress bar */}
          <div className="mt-4">
            <div className="flex justify-between text-sm text-blue-100 mb-2">
              <span>Question {currentStep + 1} of {assessmentQuestions.length}</span>
              <span>{Math.round(((currentStep + 1) / assessmentQuestions.length) * 100)}%</span>
            </div>
            <div className="w-full bg-blue-800 rounded-full h-2">
              <div 
                className="bg-white h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / assessmentQuestions.length) * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* Question Content */}
        <div className="p-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className={`text-xl font-bold mb-6 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {currentQuestion.title}
              </h3>

              <div className="space-y-3">
                {currentQuestion.options.map((option) => {
                  const isSelected = currentQuestion.type === 'multiple'
                    ? answers[currentQuestion.id]?.includes(option.value)
                    : answers[currentQuestion.id] === option.value;

                  return (
                    <button
                      key={option.value}
                      onClick={() => handleAnswer(currentQuestion.id, option.value, currentQuestion.type === 'multiple')}
                      className={`w-full p-4 rounded-lg border-2 text-left transition-all duration-200 ${
                        isSelected
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                          : darkMode
                            ? 'border-gray-600 hover:border-gray-500 bg-gray-700'
                            : 'border-gray-200 hover:border-gray-300 bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {option.icon && <option.icon className="text-blue-500" />}
                          <div>
                            <div className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                              {option.label}
                            </div>
                            {option.description && (
                              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                {option.description}
                              </div>
                            )}
                          </div>
                        </div>
                        {isSelected && <FaCheck className="text-blue-500" />}
                      </div>
                    </button>
                  );
                })}
              </div>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className={`p-6 border-t ${darkMode ? 'border-gray-700 bg-gray-750' : 'border-gray-200 bg-gray-50'}`}>
          <div className="flex justify-between">
            <button
              onClick={() => setCurrentStep(prev => Math.max(0, prev - 1))}
              disabled={currentStep === 0}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                currentStep === 0
                  ? 'opacity-50 cursor-not-allowed'
                  : darkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
            >
              <FaArrowLeft />
              Previous
            </button>

            {isLastStep ? (
              <button
                onClick={handleSubmit}
                disabled={!canProceed || isSubmitting}
                className={`flex items-center gap-2 px-6 py-2 rounded-lg font-semibold transition-colors ${
                  !canProceed || isSubmitting
                    ? 'opacity-50 cursor-not-allowed bg-gray-400'
                    : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                }`}
              >
                {isSubmitting ? 'Submitting...' : 'Complete Assessment'}
                <FaCheck />
              </button>
            ) : (
              <button
                onClick={() => setCurrentStep(prev => prev + 1)}
                disabled={!canProceed}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg font-semibold transition-colors ${
                  !canProceed
                    ? 'opacity-50 cursor-not-allowed bg-gray-400'
                    : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                }`}
              >
                Next
                <FaArrowRight />
              </button>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default InitialAssessment;
