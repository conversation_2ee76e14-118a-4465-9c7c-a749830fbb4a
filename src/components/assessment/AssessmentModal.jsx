import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import SkillAssessment from './SkillAssessment';

/**
 * AssessmentModal Component
 *
 * A modal that displays the skill assessment component.
 * Can be triggered on first login or manually by the user.
 */
const AssessmentModal = ({ isOpen, onClose, onComplete }) => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const [showAssessment, setShowAssessment] = useState(false);

  // Show assessment after modal animation completes
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        setShowAssessment(true);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setShowAssessment(false);
    }
  }, [isOpen]);

  // Handle assessment completion
  const handleAssessmentComplete = (results) => {
    // Save assessment completion status
    localStorage.setItem(`assessmentCompleted_${user?.id || 'anonymous'}`, 'true');

    // Call parent completion handler
    if (onComplete) {
      onComplete(results);
    }

    // Close modal
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className={`relative w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-lg shadow-xl ${
              darkMode ? 'bg-gray-900' : 'bg-white'
            }`}
          >
            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 z-10 p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              aria-label="Close"
            >
              <FaTimes className={darkMode ? 'text-white' : 'text-gray-800'} />
            </button>

            {/* Modal content */}
            <div className="p-6">
              {showAssessment ? (
                <SkillAssessment onComplete={handleAssessmentComplete} />
              ) : (
                <div className="flex justify-center items-center h-64">
                  <div className={`w-8 h-8 border-4 ${darkMode ? 'border-blue-500' : 'border-blue-600'} border-t-transparent rounded-full animate-spin`}></div>
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AssessmentModal;
