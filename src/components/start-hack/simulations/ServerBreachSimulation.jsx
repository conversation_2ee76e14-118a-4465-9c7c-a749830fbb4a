import React, { useState, useEffect, useRef } from 'react';
import { FaServer, FaLock, FaUnlock, FaTerminal, FaCheck, FaInfoCircle, FaExclamationTriangle, FaTrophy, FaArrowLeft, FaStopwatch } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const ServerBreachSimulation = ({ simulation, onComplete, onBack }) => {
  const { darkMode } = useGlobalTheme();
  const [command, setCommand] = useState('');
  const [message, setMessage] = useState(null);
  const [attempts, setAttempts] = useState(0);
  const [isSuccess, setIsSuccess] = useState(false);
  const [startTime] = useState(new Date());
  const [elapsedTime, setElapsedTime] = useState(0);
  const [objectives, setObjectives] = useState([
    { id: 1, description: 'Run a port scan on the target server', completed: false },
    { id: 2, description: 'Attempt SSH connection', completed: false },
    { id: 3, description: 'Brute force SSH password', completed: false },
    { id: 4, description: 'Access the server and extract sensitive data', completed: false }
  ]);
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'system', text: 'Server Breach Simulation initialized...' },
    { type: 'system', text: 'Target: *************' },
    { type: 'system', text: 'Type "help" for available commands.' }
  ]);
  const [simulationData, setSimulationData] = useState(null);
  
  const timerRef = useRef(null);
  
  // Start the timer
  useEffect(() => {
    timerRef.current = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now - startTime) / 1000); // in seconds
      setElapsedTime(elapsed);
    }, 1000);
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [startTime]);
  
  // Fetch simulation data
  useEffect(() => {
    const fetchSimulationData = async () => {
      if (!simulation?.id) return;
      
      try {
        const { data, error } = await supabase
          .from('start_hack_simulations')
          .select('*')
          .eq('id', simulation.id)
          .single();
        
        if (error) throw error;
        
        if (data) {
          setSimulationData(data);
          
          // If objectives are defined in the database, use them
          if (data.objectives) {
            setObjectives(data.objectives.map(obj => ({ ...obj, completed: false })));
          }
        }
      } catch (error) {
        console.error('Error fetching simulation data:', error);
      }
    };
    
    fetchSimulationData();
  }, [simulation]);
  
  const addToConsole = (type, text) => {
    setConsoleOutput(prev => [...prev, { type, text }]);
    // Auto-scroll console to bottom
    setTimeout(() => {
      const consoleElement = document.getElementById('server-breach-console');
      if (consoleElement) {
        consoleElement.scrollTop = consoleElement.scrollHeight;
      }
    }, 100);
  };
  
  const handleCommand = (e) => {
    e.preventDefault();
    
    if (!command.trim()) return;
    
    // Add command to console
    addToConsole('command', `$ ${command}`);
    
    // Process command
    processCommand(command);
    
    // Clear command input
    setCommand('');
  };
  
  const processCommand = (cmd) => {
    const lowerCmd = cmd.toLowerCase().trim();
    
    // Increment attempts
    setAttempts(prev => prev + 1);
    
    // Process different commands
    if (lowerCmd === 'help') {
      addToConsole('system', 'Available commands:');
      addToConsole('system', '  help - Show this help message');
      addToConsole('system', '  nmap <target> - Scan a target for open ports');
      addToConsole('system', '  ssh <user>@<host> - Attempt SSH connection');
      addToConsole('system', '  hydra <options> - Brute force passwords');
      addToConsole('system', '  cat <file> - Display file contents');
      addToConsole('system', '  ls - List files in current directory');
      addToConsole('system', '  clear - Clear the console');
    } else if (lowerCmd.startsWith('nmap')) {
      const target = lowerCmd.split(' ')[1] || '*************';
      
      addToConsole('system', `Running port scan on ${target}...`);
      
      // Simulate scanning delay
      setTimeout(() => {
        addToConsole('success', 'Scan complete. Results:');
        addToConsole('data', 'Starting Nmap scan...');
        addToConsole('data', 'Port 22/tcp open ssh OpenSSH 7.6p1');
        addToConsole('data', 'Port 80/tcp open http Apache 2.4.29');
        addToConsole('data', 'Port 443/tcp open https Apache 2.4.29');
        
        // Mark first objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 1 ? { ...obj, completed: true } : obj
        ));
      }, 2000);
    } else if (lowerCmd.startsWith('ssh')) {
      const parts = lowerCmd.split(' ');
      const target = parts[1] || 'admin@*************';
      
      // Check if first objective is completed
      if (!objectives.find(obj => obj.id === 1)?.completed) {
        addToConsole('error', 'You need to scan the server first to identify open ports.');
        return;
      }
      
      addToConsole('system', `Attempting SSH connection to ${target}...`);
      
      // Simulate connection delay
      setTimeout(() => {
        addToConsole('data', 'SSH connection attempt...');
        addToConsole('error', 'Permission denied (publickey,password).');
        addToConsole('info', '[!] Note: Server allows password authentication despite config');
        addToConsole('info', '[!] Default credentials might work');
        
        // Mark second objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 2 ? { ...obj, completed: true } : obj
        ));
      }, 1500);
    } else if (lowerCmd.startsWith('hydra')) {
      // Check if second objective is completed
      if (!objectives.find(obj => obj.id === 2)?.completed) {
        addToConsole('error', 'You need to attempt an SSH connection first.');
        return;
      }
      
      if (!lowerCmd.includes('ssh://*************')) {
        addToConsole('error', 'Invalid hydra command. Try: hydra -l admin -P /usr/share/wordlists/common.txt ssh://*************');
        return;
      }
      
      addToConsole('system', 'Running password brute force attack...');
      
      // Simulate brute force delay
      setTimeout(() => {
        addToConsole('data', 'Hydra v9.1 (c) 2020 by van Hauser/THC & David Maciejak');
        addToConsole('data', '[DATA] max 16 tasks per 1 server');
        addToConsole('success', '[22][ssh] host: *************   login: admin   password: admin123');
        addToConsole('data', '[STATUS] attack finished for ************* (valid pair found)');
        
        // Mark third objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 3 ? { ...obj, completed: true } : obj
        ));
      }, 3000);
    } else if (lowerCmd === 'ls') {
      // Check if third objective is completed
      if (!objectives.find(obj => obj.id === 3)?.completed) {
        addToConsole('error', 'You need to gain access to the server first.');
        return;
      }
      
      addToConsole('system', 'Listing files in current directory...');
      
      // Simulate command execution
      setTimeout(() => {
        addToConsole('data', 'config.yaml  logs/  backup/  sensitive_data.txt  users.db');
      }, 500);
    } else if (lowerCmd.startsWith('cat')) {
      // Check if third objective is completed
      if (!objectives.find(obj => obj.id === 3)?.completed) {
        addToConsole('error', 'You need to gain access to the server first.');
        return;
      }
      
      const file = lowerCmd.split(' ')[1];
      
      if (!file) {
        addToConsole('error', 'Please specify a file to read.');
        return;
      }
      
      addToConsole('system', `Reading file: ${file}`);
      
      // Simulate command execution
      setTimeout(() => {
        if (file === 'sensitive_data.txt') {
          addToConsole('data', '=== CONFIDENTIAL INFORMATION ===');
          addToConsole('data', 'Server access credentials:');
          addToConsole('data', 'Database: admin/Sup3rS3cr3t!');
          addToConsole('data', 'API Key: 7f4e6s5d4a3s2d1f2g3h4j5k6l7');
          addToConsole('data', 'Secret Flag: FLAG{s3rv3r_br34ch_c0mpl3t3d}');
          
          // Mark fourth objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 4 ? { ...obj, completed: true } : obj
          ));
          
          // Check if all objectives are completed
          const allCompleted = objectives.every(obj => obj.id === 4 || obj.completed);
          
          if (allCompleted) {
            // Stop the timer
            clearInterval(timerRef.current);
            
            // Mark simulation as completed
            setIsSuccess(true);
            
            // Show success message
            setMessage({ type: 'success', text: 'Simulation completed successfully!' });
            
            // Call the onComplete callback
            if (onComplete) {
              onComplete({
                success: true,
                timeSpent: elapsedTime,
                attempts,
                score: calculateScore(elapsedTime, attempts)
              });
            }
          }
        } else if (file === 'config.yaml') {
          addToConsole('data', 'server:');
          addToConsole('data', '  host: *************');
          addToConsole('data', '  port: 22');
          addToConsole('data', '  ssh_enabled: true');
          addToConsole('data', '  password_auth: true  # Security misconfiguration!');
        } else if (file === 'users.db') {
          addToConsole('data', 'admin:7f4e6s5d4a3s2d1f2g3h4j5k6l7:1000:1000');
          addToConsole('data', 'user1:5f4dcc3b5aa765d61d8327deb882cf99:1001:1001');
          addToConsole('data', 'user2:d8578edf8458ce06fbc5bb76a58c5ca4:1002:1002');
        } else {
          addToConsole('error', `File not found: ${file}`);
        }
      }, 500);
    } else if (lowerCmd === 'clear') {
      setConsoleOutput([
        { type: 'system', text: 'Console cleared.' },
        { type: 'system', text: 'Type "help" for available commands.' }
      ]);
    } else {
      addToConsole('error', `Unknown command: ${cmd}`);
      addToConsole('system', 'Type "help" for available commands.');
    }
  };
  
  // Calculate score based on time and attempts
  const calculateScore = (time, attempts) => {
    // Base score: 100 points
    let score = 100;
    
    // Deduct points for time (1 point per 30 seconds)
    score -= Math.floor(time / 30);
    
    // Deduct points for excessive attempts (1 point per attempt over 10)
    if (attempts > 10) {
      score -= (attempts - 10);
    }
    
    // Ensure score doesn't go below 0
    return Math.max(0, score);
  };
  
  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
      {/* Simulation Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex justify-between items-center`}>
        <h2 className="text-xl font-bold flex items-center">
          <FaServer className="text-blue-500 mr-2" />
          Server Breach Simulation
        </h2>
        <div className="flex items-center space-x-2">
          <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
            <FaStopwatch className="mr-1 text-blue-500" />
            <span className="text-sm">{formatTime(elapsedTime)}</span>
          </div>
        </div>
      </div>
      
      {/* Simulation Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Objectives */}
          <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
            <h3 className="text-xl font-bold mb-4">Objectives</h3>
            
            {message && (
              <div className={`p-3 rounded-lg mb-4 ${
                message.type === 'success' 
                  ? 'bg-green-100 text-green-800 border-green-200' 
                  : message.type === 'warning'
                    ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                    : 'bg-red-100 text-red-800 border-red-200'
              } border`}>
                <div className="flex items-center">
                  {message.type === 'success' ? (
                    <FaCheck className="mr-2" />
                  ) : message.type === 'warning' ? (
                    <FaExclamationTriangle className="mr-2" />
                  ) : (
                    <FaInfoCircle className="mr-2" />
                  )}
                  <span>{message.text}</span>
                </div>
              </div>
            )}
            
            <ul className="space-y-4">
              {objectives.map((objective) => (
                <li key={objective.id} className="flex items-start">
                  <div className={`mt-0.5 mr-2 flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                    objective.completed 
                      ? 'bg-green-500 text-white' 
                      : darkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-200 text-gray-500'
                  }`}>
                    {objective.completed ? (
                      <FaCheck className="text-xs" />
                    ) : (
                      <span className="text-xs">{objective.id}</span>
                    )}
                  </div>
                  <span className={objective.completed ? 'line-through opacity-70' : ''}>
                    {objective.description}
                  </span>
                </li>
              ))}
            </ul>
            
            {isSuccess && (
              <div className="mt-6 p-4 bg-green-100 text-green-800 border border-green-200 rounded-lg">
                <div className="flex items-center mb-2">
                  <FaTrophy className="mr-2 text-yellow-500" />
                  <span className="font-bold">Simulation Completed!</span>
                </div>
                <p>You successfully breached the server and extracted sensitive data.</p>
                <div className="mt-2 grid grid-cols-2 gap-2">
                  <div>
                    <span className="text-sm font-bold">Time:</span>
                    <span className="ml-2">{formatTime(elapsedTime)}</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Attempts:</span>
                    <span className="ml-2">{attempts}</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Score:</span>
                    <span className="ml-2">{calculateScore(elapsedTime, attempts)}/100</span>
                  </div>
                </div>
                <button
                  onClick={onBack}
                  className="mt-4 w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center justify-center"
                >
                  <FaArrowLeft className="mr-2" />
                  Back to Simulations
                </button>
              </div>
            )}
          </div>
          
          {/* Middle and Right Columns - Terminal and Info */}
          <div className="lg:col-span-2">
            <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <FaTerminal className="mr-2 text-green-500" />
                Terminal
              </h3>
              
              <div 
                id="server-breach-console"
                className={`h-80 overflow-y-auto p-4 rounded-lg font-mono text-sm ${
                  darkMode 
                    ? 'bg-gray-900 text-gray-300' 
                    : 'bg-gray-800 text-gray-200'
                }`}
              >
                {consoleOutput.map((output, index) => (
                  <div key={index} className="mb-1">
                    <span className={
                      output.type === 'system' 
                        ? 'text-blue-400' 
                        : output.type === 'command' 
                          ? 'text-green-400'
                          : output.type === 'error'
                            ? 'text-red-400'
                            : output.type === 'success'
                              ? 'text-green-400'
                              : output.type === 'info'
                                ? 'text-yellow-400'
                                : output.type === 'data'
                                  ? 'text-purple-400'
                                  : 'text-gray-400'
                    }>
                      {output.text}
                    </span>
                  </div>
                ))}
              </div>
              
              <form onSubmit={handleCommand} className="mt-4 flex">
                <div className={`px-3 py-2 ${
                  darkMode 
                    ? 'bg-gray-900 text-green-400' 
                    : 'bg-gray-800 text-green-400'
                } rounded-l-lg`}>
                  $
                </div>
                <input
                  type="text"
                  value={command}
                  onChange={(e) => setCommand(e.target.value)}
                  className={`flex-grow p-2 ${
                    darkMode 
                      ? 'bg-gray-900 border-gray-700 text-gray-300' 
                      : 'bg-gray-800 border-gray-700 text-gray-200'
                  } border-y border-r rounded-r-lg font-mono`}
                  placeholder="Type a command..."
                  disabled={isSuccess}
                />
              </form>
              
              <div className="mt-4">
                <h4 className={`font-bold mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Simulation Description:</h4>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  In this simulation, you'll attempt to breach a vulnerable server by scanning for open ports,
                  attempting SSH connections, and using brute force techniques to gain access. Once inside,
                  you'll need to extract sensitive information from the server.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServerBreachSimulation;
