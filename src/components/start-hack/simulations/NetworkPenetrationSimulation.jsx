import React, { useState, useEffect, useRef } from 'react';
import { FaNetworkWired, FaLock, FaUnlock, FaTerminal, FaCheck, FaInfoCircle, FaExclamationTriangle, FaTrophy, FaArrowLeft, FaStopwatch } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const NetworkPenetrationSimulation = ({ simulation, onComplete, onBack }) => {
  const { darkMode } = useGlobalTheme();
  const [command, setCommand] = useState('');
  const [message, setMessage] = useState(null);
  const [attempts, setAttempts] = useState(0);
  const [isSuccess, setIsSuccess] = useState(false);
  const [startTime] = useState(new Date());
  const [elapsedTime, setElapsedTime] = useState(0);
  const [objectives, setObjectives] = useState([
    { id: 1, description: 'Perform network reconnaissance', completed: false },
    { id: 2, description: 'Identify vulnerable services', completed: false },
    { id: 3, description: 'Exploit vulnerabilities to gain access', completed: false },
    { id: 4, description: 'Escalate privileges', completed: false },
    { id: 5, description: 'Extract sensitive data', completed: false }
  ]);
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'system', text: 'Network Penetration Simulation initialized...' },
    { type: 'system', text: 'Target Network: ***********/24' },
    { type: 'system', text: 'Type "help" for available commands.' }
  ]);
  const [simulationData, setSimulationData] = useState(null);
  const [networkMap, setNetworkMap] = useState(null);
  
  const timerRef = useRef(null);
  
  // Start the timer
  useEffect(() => {
    timerRef.current = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now - startTime) / 1000); // in seconds
      setElapsedTime(elapsed);
    }, 1000);
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [startTime]);
  
  // Fetch simulation data
  useEffect(() => {
    const fetchSimulationData = async () => {
      if (!simulation?.id) return;
      
      try {
        const { data, error } = await supabase
          .from('start_hack_simulations')
          .select('*')
          .eq('id', simulation.id)
          .single();
        
        if (error) throw error;
        
        if (data) {
          setSimulationData(data);
          
          // If objectives are defined in the database, use them
          if (data.objectives) {
            setObjectives(data.objectives.map(obj => ({ ...obj, completed: false })));
          }
        }
      } catch (error) {
        console.error('Error fetching simulation data:', error);
      }
    };
    
    fetchSimulationData();
  }, [simulation]);
  
  const addToConsole = (type, text) => {
    setConsoleOutput(prev => [...prev, { type, text }]);
    // Auto-scroll console to bottom
    setTimeout(() => {
      const consoleElement = document.getElementById('network-pentest-console');
      if (consoleElement) {
        consoleElement.scrollTop = consoleElement.scrollHeight;
      }
    }, 100);
  };
  
  const handleCommand = (e) => {
    e.preventDefault();
    
    if (!command.trim()) return;
    
    // Add command to console
    addToConsole('command', `$ ${command}`);
    
    // Process command
    processCommand(command);
    
    // Clear command input
    setCommand('');
  };
  
  const processCommand = (cmd) => {
    const lowerCmd = cmd.toLowerCase().trim();
    
    // Increment attempts
    setAttempts(prev => prev + 1);
    
    // Process different commands
    if (lowerCmd === 'help') {
      addToConsole('system', 'Available commands:');
      addToConsole('system', '  help - Show this help message');
      addToConsole('system', '  nmap <target> - Scan a target for open ports and services');
      addToConsole('system', '  enum <target> <service> - Enumerate a specific service on a target');
      addToConsole('system', '  exploit <target> <service> - Exploit a vulnerable service');
      addToConsole('system', '  privesc - Attempt to escalate privileges');
      addToConsole('system', '  extract - Extract data from the compromised system');
      addToConsole('system', '  clear - Clear the console');
    } else if (lowerCmd.startsWith('nmap')) {
      const target = lowerCmd.split(' ')[1] || '***********/24';
      
      addToConsole('system', `Running network scan on ${target}...`);
      
      // Simulate scanning delay
      setTimeout(() => {
        if (target === '***********/24' || target === 'network') {
          addToConsole('success', 'Network scan complete. Hosts discovered:');
          addToConsole('data', '*********** - Router/Gateway');
          addToConsole('data', '***********0 - Web Server');
          addToConsole('data', '************ - Database Server');
          addToConsole('data', '************ - File Server');
          
          // Update network map
          setNetworkMap({
            hosts: [
              { ip: '***********', type: 'router', services: ['ssh:22', 'http:80'] },
              { ip: '***********0', type: 'webserver', services: ['http:80', 'https:443', 'ssh:22'] },
              { ip: '************', type: 'database', services: ['mysql:3306', 'ssh:22'] },
              { ip: '************', type: 'fileserver', services: ['ftp:21', 'smb:445', 'ssh:22'] }
            ]
          });
          
          // Mark first objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 1 ? { ...obj, completed: true } : obj
          ));
        } else if (target === '***********') {
          addToConsole('success', 'Host scan complete for ***********:');
          addToConsole('data', 'Port 22: SSH - OpenSSH 7.9');
          addToConsole('data', 'Port 80: HTTP - Router Admin Interface');
        } else if (target === '***********0') {
          addToConsole('success', 'Host scan complete for ***********0:');
          addToConsole('data', 'Port 22: SSH - OpenSSH 7.4');
          addToConsole('data', 'Port 80: HTTP - Apache 2.4.6');
          addToConsole('data', 'Port 443: HTTPS - Apache 2.4.6');
        } else if (target === '************') {
          addToConsole('success', 'Host scan complete for ************:');
          addToConsole('data', 'Port 22: SSH - OpenSSH 7.4');
          addToConsole('data', 'Port 3306: MySQL 5.7.30');
        } else if (target === '************') {
          addToConsole('success', 'Host scan complete for ************:');
          addToConsole('data', 'Port 21: FTP - vsftpd 3.0.2');
          addToConsole('data', 'Port 22: SSH - OpenSSH 7.4');
          addToConsole('data', 'Port 445: SMB - Samba 4.9.1');
        } else {
          addToConsole('error', `No hosts found at ${target}`);
        }
      }, 2000);
    } else if (lowerCmd.startsWith('enum')) {
      // Check if first objective is completed
      if (!objectives.find(obj => obj.id === 1)?.completed) {
        addToConsole('error', 'You need to scan the network first.');
        return;
      }
      
      const parts = lowerCmd.split(' ');
      const target = parts[1];
      const service = parts[2];
      
      if (!target || !service) {
        addToConsole('error', 'Please specify a target and service to enumerate.');
        addToConsole('system', 'Usage: enum <target> <service>');
        addToConsole('system', 'Example: enum ************ ftp');
        return;
      }
      
      addToConsole('system', `Enumerating ${service} service on ${target}...`);
      
      // Simulate enumeration delay
      setTimeout(() => {
        if (target === '************' && service === 'ftp') {
          addToConsole('success', 'FTP enumeration complete:');
          addToConsole('data', 'Anonymous FTP access enabled');
          addToConsole('data', 'Writable directories found');
          addToConsole('data', 'Outdated vsftpd 3.0.2 with known vulnerabilities');
          
          // Mark second objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 2 ? { ...obj, completed: true } : obj
          ));
        } else if (target === '************' && service === 'smb') {
          addToConsole('success', 'SMB enumeration complete:');
          addToConsole('data', 'Samba 4.9.1 with misconfigured shares');
          addToConsole('data', 'Readable shares without authentication');
          addToConsole('data', 'Potential for SMB relay attacks');
          
          // Mark second objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 2 ? { ...obj, completed: true } : obj
          ));
        } else if (target === '************' && service === 'mysql') {
          addToConsole('success', 'MySQL enumeration complete:');
          addToConsole('data', 'MySQL 5.7.30 with weak configuration');
          addToConsole('data', 'Anonymous access enabled');
          addToConsole('data', 'Default credentials may be in use');
          
          // Mark second objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 2 ? { ...obj, completed: true } : obj
          ));
        } else {
          addToConsole('error', `Could not enumerate ${service} on ${target}`);
        }
      }, 1500);
    } else if (lowerCmd.startsWith('exploit')) {
      // Check if second objective is completed
      if (!objectives.find(obj => obj.id === 2)?.completed) {
        addToConsole('error', 'You need to enumerate services first.');
        return;
      }
      
      const parts = lowerCmd.split(' ');
      const target = parts[1];
      const service = parts[2];
      
      if (!target || !service) {
        addToConsole('error', 'Please specify a target and service to exploit.');
        addToConsole('system', 'Usage: exploit <target> <service>');
        addToConsole('system', 'Example: exploit ************ ftp');
        return;
      }
      
      addToConsole('system', `Exploiting ${service} service on ${target}...`);
      
      // Simulate exploitation delay
      setTimeout(() => {
        if (target === '************' && service === 'ftp') {
          addToConsole('success', 'FTP exploitation successful!');
          addToConsole('data', 'Logged in with anonymous access');
          addToConsole('data', 'Uploaded reverse shell payload');
          addToConsole('data', 'Established connection to target');
          addToConsole('data', 'Shell access gained as ftp user');
          
          // Mark third objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 3 ? { ...obj, completed: true } : obj
          ));
        } else if (target === '************' && service === 'smb') {
          addToConsole('success', 'SMB exploitation successful!');
          addToConsole('data', 'Accessed shares without authentication');
          addToConsole('data', 'Executed payload through writable share');
          addToConsole('data', 'Shell access gained as smb user');
          
          // Mark third objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 3 ? { ...obj, completed: true } : obj
          ));
        } else if (target === '************' && service === 'mysql') {
          addToConsole('success', 'MySQL exploitation successful!');
          addToConsole('data', 'Connected with default credentials (root:password)');
          addToConsole('data', 'Executed system commands through MySQL UDF');
          addToConsole('data', 'Shell access gained as mysql user');
          
          // Mark third objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 3 ? { ...obj, completed: true } : obj
          ));
        } else {
          addToConsole('error', `Could not exploit ${service} on ${target}`);
        }
      }, 2500);
    } else if (lowerCmd === 'privesc') {
      // Check if third objective is completed
      if (!objectives.find(obj => obj.id === 3)?.completed) {
        addToConsole('error', 'You need to exploit a service first.');
        return;
      }
      
      addToConsole('system', 'Attempting privilege escalation...');
      
      // Simulate privilege escalation delay
      setTimeout(() => {
        addToConsole('success', 'Privilege escalation successful!');
        addToConsole('data', 'Exploited kernel vulnerability CVE-2021-4034');
        addToConsole('data', 'Gained root access to the system');
        addToConsole('data', 'uid=0(root) gid=0(root) groups=0(root)');
        
        // Mark fourth objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 4 ? { ...obj, completed: true } : obj
        ));
      }, 2000);
    } else if (lowerCmd === 'extract') {
      // Check if fourth objective is completed
      if (!objectives.find(obj => obj.id === 4)?.completed) {
        addToConsole('error', 'You need to escalate privileges first.');
        return;
      }
      
      addToConsole('system', 'Extracting sensitive data from the system...');
      
      // Simulate data extraction delay
      setTimeout(() => {
        addToConsole('success', 'Data extraction successful!');
        addToConsole('data', '=== USER CREDENTIALS ===');
        addToConsole('data', 'admin:7f4e6s5d4a3s2d1f2g3h4j5k6l7');
        addToConsole('data', 'root:r00tP4ssw0rd!');
        addToConsole('data', '=== NETWORK CONFIGURATION ===');
        addToConsole('data', 'Internal network: 10.0.0.0/8');
        addToConsole('data', 'VPN endpoints: 172.16.0.1, 172.16.0.2');
        addToConsole('data', '=== SECRET FLAG ===');
        addToConsole('data', 'FLAG{n3tw0rk_p3n3tr4t10n_c0mpl3t3}');
        
        // Mark fifth objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 5 ? { ...obj, completed: true } : obj
        ));
        
        // Check if all objectives are completed
        const allCompleted = objectives.every(obj => obj.id === 5 || obj.completed);
        
        if (allCompleted) {
          // Stop the timer
          clearInterval(timerRef.current);
          
          // Mark simulation as completed
          setIsSuccess(true);
          
          // Show success message
          setMessage({ type: 'success', text: 'Simulation completed successfully!' });
          
          // Call the onComplete callback
          if (onComplete) {
            onComplete({
              success: true,
              timeSpent: elapsedTime,
              attempts,
              score: calculateScore(elapsedTime, attempts)
            });
          }
        }
      }, 2500);
    } else if (lowerCmd === 'clear') {
      setConsoleOutput([
        { type: 'system', text: 'Console cleared.' },
        { type: 'system', text: 'Type "help" for available commands.' }
      ]);
    } else {
      addToConsole('error', `Unknown command: ${cmd}`);
      addToConsole('system', 'Type "help" for available commands.');
    }
  };
  
  // Calculate score based on time and attempts
  const calculateScore = (time, attempts) => {
    // Base score: 100 points
    let score = 100;
    
    // Deduct points for time (1 point per 30 seconds)
    score -= Math.floor(time / 30);
    
    // Deduct points for excessive attempts (1 point per attempt over 15)
    if (attempts > 15) {
      score -= (attempts - 15);
    }
    
    // Ensure score doesn't go below 0
    return Math.max(0, score);
  };
  
  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
      {/* Simulation Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex justify-between items-center`}>
        <h2 className="text-xl font-bold flex items-center">
          <FaNetworkWired className="text-blue-500 mr-2" />
          Network Penetration Simulation
        </h2>
        <div className="flex items-center space-x-2">
          <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
            <FaStopwatch className="mr-1 text-blue-500" />
            <span className="text-sm">{formatTime(elapsedTime)}</span>
          </div>
        </div>
      </div>
      
      {/* Simulation Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Objectives */}
          <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
            <h3 className="text-xl font-bold mb-4">Objectives</h3>
            
            {message && (
              <div className={`p-3 rounded-lg mb-4 ${
                message.type === 'success' 
                  ? 'bg-green-100 text-green-800 border-green-200' 
                  : message.type === 'warning'
                    ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                    : 'bg-red-100 text-red-800 border-red-200'
              } border`}>
                <div className="flex items-center">
                  {message.type === 'success' ? (
                    <FaCheck className="mr-2" />
                  ) : message.type === 'warning' ? (
                    <FaExclamationTriangle className="mr-2" />
                  ) : (
                    <FaInfoCircle className="mr-2" />
                  )}
                  <span>{message.text}</span>
                </div>
              </div>
            )}
            
            <ul className="space-y-4">
              {objectives.map((objective) => (
                <li key={objective.id} className="flex items-start">
                  <div className={`mt-0.5 mr-2 flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                    objective.completed 
                      ? 'bg-green-500 text-white' 
                      : darkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-200 text-gray-500'
                  }`}>
                    {objective.completed ? (
                      <FaCheck className="text-xs" />
                    ) : (
                      <span className="text-xs">{objective.id}</span>
                    )}
                  </div>
                  <span className={objective.completed ? 'line-through opacity-70' : ''}>
                    {objective.description}
                  </span>
                </li>
              ))}
            </ul>
            
            {isSuccess && (
              <div className="mt-6 p-4 bg-green-100 text-green-800 border border-green-200 rounded-lg">
                <div className="flex items-center mb-2">
                  <FaTrophy className="mr-2 text-yellow-500" />
                  <span className="font-bold">Simulation Completed!</span>
                </div>
                <p>You successfully penetrated the network and extracted sensitive data.</p>
                <div className="mt-2 grid grid-cols-2 gap-2">
                  <div>
                    <span className="text-sm font-bold">Time:</span>
                    <span className="ml-2">{formatTime(elapsedTime)}</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Attempts:</span>
                    <span className="ml-2">{attempts}</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Score:</span>
                    <span className="ml-2">{calculateScore(elapsedTime, attempts)}/100</span>
                  </div>
                </div>
                <button
                  onClick={onBack}
                  className="mt-4 w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center justify-center"
                >
                  <FaArrowLeft className="mr-2" />
                  Back to Simulations
                </button>
              </div>
            )}
          </div>
          
          {/* Middle and Right Columns - Terminal and Info */}
          <div className="lg:col-span-2">
            <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <FaTerminal className="mr-2 text-green-500" />
                Terminal
              </h3>
              
              <div 
                id="network-pentest-console"
                className={`h-80 overflow-y-auto p-4 rounded-lg font-mono text-sm ${
                  darkMode 
                    ? 'bg-gray-900 text-gray-300' 
                    : 'bg-gray-800 text-gray-200'
                }`}
              >
                {consoleOutput.map((output, index) => (
                  <div key={index} className="mb-1">
                    <span className={
                      output.type === 'system' 
                        ? 'text-blue-400' 
                        : output.type === 'command' 
                          ? 'text-green-400'
                          : output.type === 'error'
                            ? 'text-red-400'
                            : output.type === 'success'
                              ? 'text-green-400'
                              : output.type === 'info'
                                ? 'text-yellow-400'
                                : output.type === 'data'
                                  ? 'text-purple-400'
                                  : 'text-gray-400'
                    }>
                      {output.text}
                    </span>
                  </div>
                ))}
              </div>
              
              <form onSubmit={handleCommand} className="mt-4 flex">
                <div className={`px-3 py-2 ${
                  darkMode 
                    ? 'bg-gray-900 text-green-400' 
                    : 'bg-gray-800 text-green-400'
                } rounded-l-lg`}>
                  $
                </div>
                <input
                  type="text"
                  value={command}
                  onChange={(e) => setCommand(e.target.value)}
                  className={`flex-grow p-2 ${
                    darkMode 
                      ? 'bg-gray-900 border-gray-700 text-gray-300' 
                      : 'bg-gray-800 border-gray-700 text-gray-200'
                  } border-y border-r rounded-r-lg font-mono`}
                  placeholder="Type a command..."
                  disabled={isSuccess}
                />
              </form>
              
              <div className="mt-4">
                <h4 className={`font-bold mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Simulation Description:</h4>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  In this simulation, you'll perform a network penetration test on a corporate network.
                  You'll scan for hosts, enumerate services, exploit vulnerabilities, escalate privileges,
                  and extract sensitive data from the compromised systems.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkPenetrationSimulation;
