import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaLock, FaUnlock, FaLightbulb, FaCheck, FaInfoCircle, FaExclamationTriangle, FaTrophy, FaArrowLeft, FaSearch, FaFilter, FaStar, FaClock, FaCode, FaNetworkWired, FaDatabase, FaServer, FaShieldAlt, FaUserSecret, FaHtml5, FaKey, FaTerminal, FaUserCog } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { supabase } from '../../lib/supabase';
import SQLInjectionSimulator from './simulators/SQLInjectionSimulator';
import DDoSSimulator from './simulators/DDoSSimulator';
import XSSSimulator from './simulators/XSSSimulator';
import PasswordCrackingSimulator from './simulators/PasswordCrackingSimulator';
import NetworkScanningSimulator from './simulators/NetworkScanningSimulator';
import SocialEngineeringSimulator from './simulators/SocialEngineeringSimulator';
import SubscriptionRequired from '../SubscriptionRequired';

// Import placeholders for new simulator components
// These will be implemented later
const ProcessScriptingSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  return (
    <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
      <h3 className="text-xl font-bold mb-4">Process Scripting Simulator</h3>
      <p className="mb-6">This simulation is currently under development. Check back soon!</p>
    </div>
  );
};

const CommandInjectionSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  return (
    <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
      <h3 className="text-xl font-bold mb-4">Command Injection Simulator</h3>
      <p className="mb-6">This simulation is currently under development. Check back soon!</p>
    </div>
  );
};

const PrivilegeEscalationSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  return (
    <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
      <h3 className="text-xl font-bold mb-4">Privilege Escalation Simulator</h3>
      <p className="mb-6">This simulation is currently under development. Check back soon!</p>
    </div>
  );
};

const StartHackHub = () => {
  const { darkMode } = useGlobalTheme();
  const { subscriptionLevel } = useSubscription();
  const navigate = useNavigate();
  const { simulationId } = useParams();
  const [simulations, setSimulations] = useState([]);
  const [userSimulations, setUserSimulations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSimulation, setActiveSimulation] = useState(null);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [difficultyLevels, setDifficultyLevels] = useState([]);
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [user, setUser] = useState(null);
  const [authChecked, setAuthChecked] = useState(false);

  // Get current user from auth context
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);
      } catch (error) {
        console.error('Error getting current user:', error);
      } finally {
        setAuthChecked(true);
      }
    };

    getCurrentUser();
  }, []);

  // Fetch simulations from the database
  useEffect(() => {
    const fetchSimulations = async () => {
      try {
        setLoading(true);

        // Set default categories if database fetch fails
        const defaultCategories = [
          { id: 'web-security', name: 'Web Security' },
          { id: 'network-security', name: 'Network Security' },
          { id: 'cryptography', name: 'Cryptography' },
          { id: 'human-security', name: 'Human Security' }
        ];

        // Set default difficulty levels if database fetch fails
        const defaultDifficulties = [
          { id: 'beginner', name: 'Beginner' },
          { id: 'intermediate', name: 'Intermediate' },
          { id: 'advanced', name: 'Advanced' },
          { id: 'expert', name: 'Expert' }
        ];

        try {
          // Fetch categories
          const { data: categoriesData, error: categoriesError } = await supabase
            .from('practice_simulation_categories')
            .select('*')
            .order('display_order');

          if (!categoriesError && categoriesData?.length > 0) {
            setCategories(categoriesData);
          } else {
            setCategories(defaultCategories);
          }

          // Fetch difficulty levels
          const { data: difficultyData, error: difficultyError } = await supabase
            .from('practice_simulation_difficulty_levels')
            .select('*')
            .order('display_order');

          if (!difficultyError && difficultyData?.length > 0) {
            setDifficultyLevels(difficultyData);
          } else {
            setDifficultyLevels(defaultDifficulties);
          }

          // Fetch simulations
          const { data: simulationsData, error: simulationsError } = await supabase
            .from('practice_simulations')
            .select(`
              *,
              category:category_id(id, name),
              difficulty:difficulty_id(id, name)
            `)
            .eq('is_active', true)
            .order('created_at');

          // Always use static simulations for now to ensure content is visible
          setSimulations(STATIC_SIMULATIONS);

          // Fetch user's simulation progress
          const { data: userData } = await supabase.auth.getUser();

          if (userData?.user?.id) {
            const { data: userSimulationsData, error: userSimulationsError } = await supabase
              .from('practice_simulation_attempts')
              .select('*')
              .eq('user_id', userData.user.id);

            if (!userSimulationsError) {
              setUserSimulations(userSimulationsData || []);
            }
          }
        } catch (dbError) {
          console.error('Database error:', dbError);
          // Fall back to static data
          setCategories(defaultCategories);
          setDifficultyLevels(defaultDifficulties);
          setSimulations(STATIC_SIMULATIONS);
        }

        // If simulationId is provided, set it as the active simulation
        if (simulationId) {
          const simulation = STATIC_SIMULATIONS.find(sim => sim.id === simulationId || sim.slug === simulationId);
          if (simulation) {
            setActiveSimulation(simulation);
          }
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching simulations:', error);
        // Ensure simulations are still set even if there's an error
        setSimulations(STATIC_SIMULATIONS);
        setLoading(false);
      }
    };

    fetchSimulations();
  }, [simulationId]);

  // Filter simulations based on search query, category, and difficulty
  const filteredSimulations = simulations.filter(simulation => {
    // Ensure simulation has required properties
    if (!simulation || !simulation.title || !simulation.description) {
      return false;
    }

    const matchesSearch = simulation.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         simulation.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory = selectedCategory === 'all' ||
                           (simulation.category && simulation.category.id === selectedCategory);

    const matchesDifficulty = selectedDifficulty === 'all' ||
                             (simulation.difficulty && simulation.difficulty.id === selectedDifficulty);

    const matchesFilter = filter === 'all' ||
                         (filter === 'completed' && userSimulations.some(us => us.simulation_id === simulation.id && us.is_completed)) ||
                         (filter === 'in-progress' && userSimulations.some(us => us.simulation_id === simulation.id && !us.is_completed)) ||
                         (filter === 'not-started' && !userSimulations.some(us => us.simulation_id === simulation.id));

    return matchesSearch && matchesCategory && matchesDifficulty && matchesFilter;
  });

  // Handle simulation completion
  const handleSimulationComplete = async (result) => {
    console.log('Simulation completed:', result);

    // Show completion message or update UI
    // This would typically update the user's progress in the database

    // Refresh the list of user simulations
    if (activeSimulation) {
      const { data: user } = await supabase.auth.getUser();

      if (user && user.id) {
        const { data: userSimulationsData } = await supabase
          .from('practice_simulation_attempts')
          .select('*')
          .eq('user_id', user.id);

        setUserSimulations(userSimulationsData || []);
      }
    }
  };

  // Render the appropriate simulator component based on the simulation type
  const renderSimulator = (simulation) => {
    switch (simulation.simulation_type) {
      case 'sql_injection':
        return <SQLInjectionSimulator onComplete={handleSimulationComplete} simulationId={simulation.id} />;
      case 'ddos':
        return <DDoSSimulator onComplete={handleSimulationComplete} simulationId={simulation.id} />;
      case 'xss':
        return <XSSSimulator onComplete={handleSimulationComplete} simulationId={simulation.id} />;
      case 'password_cracking':
        return <PasswordCrackingSimulator onComplete={handleSimulationComplete} simulationId={simulation.id} />;
      case 'network_scanning':
        return <NetworkScanningSimulator onComplete={handleSimulationComplete} simulationId={simulation.id} />;
      case 'social_engineering':
        return <SocialEngineeringSimulator onComplete={handleSimulationComplete} simulationId={simulation.id} />;
      case 'process_scripting':
        return <ProcessScriptingSimulator onComplete={handleSimulationComplete} simulationId={simulation.id} />;
      case 'command_injection':
        return <CommandInjectionSimulator onComplete={handleSimulationComplete} simulationId={simulation.id} />;
      case 'privilege_escalation':
        return <PrivilegeEscalationSimulator onComplete={handleSimulationComplete} simulationId={simulation.id} />;
      default:
        // For any simulation type that doesn't have a dedicated component yet
        return (
          <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
            <h3 className="text-xl font-bold mb-4">Coming Soon: {simulation.title}</h3>
            <p className="mb-6">This simulation is currently under development and will be available soon.</p>
            <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-6">
              <p className="font-bold">What you'll learn in this simulation:</p>
              <ul className="list-disc ml-5 mt-2">
                <li>Understanding {simulation.title.toLowerCase()} concepts and techniques</li>
                <li>Identifying common vulnerabilities and attack vectors</li>
                <li>Practical hands-on exercises with guided instructions</li>
                <li>Remediation strategies and best practices</li>
              </ul>
            </div>
            <button
              onClick={() => {
                setActiveSimulation(null);
                navigate('/dashboard/start-hack');
              }}
              className={`px-4 py-2 rounded-lg ${darkMode ? 'bg-blue-600' : 'bg-blue-500'} text-white hover:bg-blue-700`}
            >
              Back to Simulations
            </button>
          </div>
        );
    }
  };

  // All simulations are available since we're on a business premium account
  const isPremiumLocked = false;
  const isBusinessLocked = false;

  // If a simulation is active, show the simulator
  if (activeSimulation) {
    return (
      <div className="container mx-auto px-4 py-8">
        <button
          onClick={() => {
            setActiveSimulation(null);
            navigate('/dashboard/start-hack');
          }}
          className={`mb-4 flex items-center ${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-gray-900'}`}
        >
          <FaArrowLeft className="mr-2" /> Back to Simulations
        </button>

        <h1 className="text-2xl font-bold mb-6">{activeSimulation.title}</h1>

        {isPremiumLocked ? (
          <SubscriptionRequired
            feature="premium simulations"
            message="This simulation requires a premium subscription."
          />
        ) : isBusinessLocked ? (
          <SubscriptionRequired
            feature="business simulations"
            message="This simulation requires a business subscription."
            requiredTier="business"
          />
        ) : (
          renderSimulator(activeSimulation)
        )}
      </div>
    );
  }

  // Otherwise, show the list of available simulations
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-2">Start Hack</h1>
      <p className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        Practice cybersecurity techniques in realistic simulations
      </p>

      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
          </div>
          <input
            type="text"
            placeholder="Search simulations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`pl-10 pr-4 py-2 w-full rounded-lg ${
              darkMode
                ? 'bg-gray-800 border-gray-700 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border`}
          />
        </div>

        <div className="flex flex-wrap gap-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-gray-800 border-gray-700 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border`}
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>

          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-gray-800 border-gray-700 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border`}
          >
            <option value="all">All Difficulties</option>
            {difficultyLevels.map(difficulty => (
              <option key={difficulty.id} value={difficulty.id}>{difficulty.name}</option>
            ))}
          </select>

          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-gray-800 border-gray-700 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border`}
          >
            <option value="all">All Simulations</option>
            <option value="completed">Completed</option>
            <option value="in-progress">In Progress</option>
            <option value="not-started">Not Started</option>
          </select>
        </div>
      </div>

      {/* Simulations Grid */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredSimulations.length === 0 ? (
        <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          <FaInfoCircle className="mx-auto text-4xl mb-4" />
          <p className="text-xl">No simulations found matching your filters.</p>
          <p className="mt-2">Try adjusting your search criteria or filters.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSimulations.map(simulation => {
            const userSimulation = userSimulations.find(us => us.simulation_id === simulation.id);
            const isCompleted = userSimulation?.is_completed;
            const isInProgress = userSimulation && !isCompleted;
            const isPremium = simulation.is_premium;
            const isBusiness = simulation.is_business;

            return (
              <motion.div
                key={simulation.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`rounded-lg overflow-hidden border ${
                  darkMode
                    ? 'bg-[#1A1F35] border-gray-800'
                    : 'bg-white border-gray-200'
                }`}
              >
                {/* Simulation Card Header */}
                <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                  <div className="flex justify-between items-start">
                    <h3 className="text-lg font-bold">{simulation.title}</h3>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <span className={`px-2 py-0.5 rounded ${
                      simulation.difficulty?.name === 'Beginner' ? 'bg-green-100 text-green-800' :
                      simulation.difficulty?.name === 'Intermediate' ? 'bg-blue-100 text-blue-800' :
                      simulation.difficulty?.name === 'Advanced' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {simulation.difficulty?.name || 'Unknown'}
                    </span>
                    <span className={`ml-2 px-2 py-0.5 rounded ${
                      darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {simulation.category?.name || 'Uncategorized'}
                    </span>
                    {simulation.estimated_time && (
                      <span className={`ml-2 flex items-center ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        <FaClock className="mr-1" /> {simulation.estimated_time} min
                      </span>
                    )}
                  </div>
                </div>

                {/* Simulation Card Body */}
                <div className="p-4">
                  <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {simulation.description}
                  </p>

                  {/* Simulation Icon */}
                  <div className="flex justify-center mb-4">
                    {simulation.simulation_type === 'sql_injection' && (
                      <FaDatabase className="text-5xl text-blue-500" />
                    )}
                    {simulation.simulation_type === 'ddos' && (
                      <FaNetworkWired className="text-5xl text-red-500" />
                    )}
                    {simulation.simulation_type === 'xss' && (
                      <FaCode className="text-5xl text-green-500" />
                    )}
                    {simulation.simulation_type === 'password_cracking' && (
                      <FaLock className="text-5xl text-yellow-500" />
                    )}
                    {simulation.simulation_type === 'network_scanning' && (
                      <FaServer className="text-5xl text-purple-500" />
                    )}
                    {simulation.simulation_type === 'social_engineering' && (
                      <FaUserSecret className="text-5xl text-orange-500" />
                    )}
                    {simulation.simulation_type === 'process_scripting' && (
                      <FaTerminal className="text-5xl text-cyan-500" />
                    )}
                    {simulation.simulation_type === 'command_injection' && (
                      <FaCode className="text-5xl text-pink-500" />
                    )}
                    {simulation.simulation_type === 'privilege_escalation' && (
                      <FaUserCog className="text-5xl text-indigo-500" />
                    )}
                    {!['sql_injection', 'ddos', 'xss', 'password_cracking', 'network_scanning', 'social_engineering', 'process_scripting', 'command_injection', 'privilege_escalation'].includes(simulation.simulation_type) && (
                      <FaShieldAlt className="text-5xl text-gray-500" />
                    )}
                  </div>

                  {/* Status and Start Button */}
                  <div className="flex items-center justify-between">
                    <div>
                      {isCompleted && (
                        <span className="flex items-center text-green-500">
                          <FaCheck className="mr-1" /> Completed
                        </span>
                      )}
                      {isInProgress && (
                        <span className="flex items-center text-blue-500">
                          <FaInfoCircle className="mr-1" /> In Progress
                        </span>
                      )}
                    </div>

                    <button
                      onClick={() => {
                        setActiveSimulation(simulation);
                        navigate(`/dashboard/start-hack/${simulation.slug || simulation.id}`);
                      }}
                      className={`px-4 py-2 rounded-lg ${
                        darkMode ? 'bg-blue-600' : 'bg-blue-500'
                      } text-white hover:bg-blue-700`}
                    >
                      {isCompleted ? (
                        "Practice Again"
                      ) : isInProgress ? (
                        "Continue"
                      ) : (
                        "Start"
                      )}
                    </button>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      )}
    </div>
  );
};

// Static simulations data for initial development
const STATIC_SIMULATIONS = [
  {
    id: 'sql-injection-basics',
    slug: 'sql-injection-basics',
    title: 'SQL Injection Basics',
    description: 'Learn the fundamentals of SQL injection by exploiting a vulnerable login form.',
    simulation_type: 'sql_injection',
    category: { id: 'web-security', name: 'Web Security' },
    difficulty: { id: 'beginner', name: 'Beginner' },
    estimated_time: 20,
    is_premium: false,
    is_business: false
  },
  {
    id: 'ddos-attack-simulation',
    slug: 'ddos-attack-simulation',
    title: 'DDoS Attack Simulation',
    description: 'Learn how DDoS attacks work by simulating an attack on a vulnerable server.',
    simulation_type: 'ddos',
    category: { id: 'network-security', name: 'Network Security' },
    difficulty: { id: 'intermediate', name: 'Intermediate' },
    estimated_time: 30,
    is_premium: false,
    is_business: false
  },
  {
    id: 'xss-vulnerability-basics',
    slug: 'xss-vulnerability-basics',
    title: 'XSS Vulnerability Basics',
    description: 'Learn how Cross-Site Scripting (XSS) vulnerabilities work by exploiting a vulnerable comment section.',
    simulation_type: 'xss',
    category: { id: 'web-security', name: 'Web Security' },
    difficulty: { id: 'beginner', name: 'Beginner' },
    estimated_time: 25,
    is_premium: false,
    is_business: false
  },
  {
    id: 'password-cracking-simulation',
    slug: 'password-cracking-simulation',
    title: 'Password Cracking Simulation',
    description: 'Learn how to crack passwords using various techniques including dictionary attacks and brute force methods.',
    simulation_type: 'password_cracking',
    category: { id: 'cryptography', name: 'Cryptography' },
    difficulty: { id: 'beginner', name: 'Beginner' },
    estimated_time: 30,
    is_premium: false,
    is_business: false
  },
  {
    id: 'network-scanning-basics',
    slug: 'network-scanning-basics',
    title: 'Network Scanning Basics',
    description: 'Learn how to perform network reconnaissance and identify vulnerable services on a network.',
    simulation_type: 'network_scanning',
    category: { id: 'network-security', name: 'Network Security' },
    difficulty: { id: 'intermediate', name: 'Intermediate' },
    estimated_time: 35,
    is_premium: false,
    is_business: false
  },
  {
    id: 'social-engineering-simulation',
    slug: 'social-engineering-simulation',
    title: 'Social Engineering Simulation',
    description: 'Learn how attackers use social engineering techniques to manipulate users into revealing sensitive information.',
    simulation_type: 'social_engineering',
    category: { id: 'human-security', name: 'Human Security' },
    difficulty: { id: 'beginner', name: 'Beginner' },
    estimated_time: 25,
    is_premium: false,
    is_business: false
  },
  {
    id: 'advanced-sql-injection',
    slug: 'advanced-sql-injection',
    title: 'Advanced SQL Injection',
    description: 'Master advanced SQL injection techniques including blind SQL injection and time-based attacks.',
    simulation_type: 'sql_injection',
    category: { id: 'web-security', name: 'Web Security' },
    difficulty: { id: 'advanced', name: 'Advanced' },
    estimated_time: 45,
    is_premium: true,
    is_business: false
  },
  {
    id: 'enterprise-ddos-protection',
    slug: 'enterprise-ddos-protection',
    title: 'Enterprise DDoS Protection',
    description: 'Learn how to implement and test enterprise-grade DDoS protection measures.',
    simulation_type: 'ddos',
    category: { id: 'network-security', name: 'Network Security' },
    difficulty: { id: 'expert', name: 'Expert' },
    estimated_time: 60,
    is_premium: false,
    is_business: true
  },
  {
    id: 'advanced-xss-attacks',
    slug: 'advanced-xss-attacks',
    title: 'Advanced XSS Attacks',
    description: 'Master advanced Cross-Site Scripting techniques including DOM-based XSS and filter evasion methods.',
    simulation_type: 'xss',
    category: { id: 'web-security', name: 'Web Security' },
    difficulty: { id: 'advanced', name: 'Advanced' },
    estimated_time: 40,
    is_premium: true,
    is_business: false
  },
  {
    id: 'wifi-hacking-simulation',
    slug: 'wifi-hacking-simulation',
    title: 'WiFi Hacking Simulation',
    description: 'Learn how to identify and exploit vulnerabilities in wireless networks using common tools and techniques.',
    simulation_type: 'network_scanning',
    category: { id: 'network-security', name: 'Network Security' },
    difficulty: { id: 'intermediate', name: 'Intermediate' },
    estimated_time: 45,
    is_premium: false,
    is_business: false
  },
  {
    id: 'phishing-campaign-simulation',
    slug: 'phishing-campaign-simulation',
    title: 'Phishing Campaign Simulation',
    description: 'Learn how to identify and create phishing campaigns, and understand the psychology behind successful attacks.',
    simulation_type: 'social_engineering',
    category: { id: 'human-security', name: 'Human Security' },
    difficulty: { id: 'intermediate', name: 'Intermediate' },
    estimated_time: 35,
    is_premium: false,
    is_business: false
  },
  {
    id: 'hash-cracking-advanced',
    slug: 'hash-cracking-advanced',
    title: 'Advanced Hash Cracking',
    description: 'Master advanced password hash cracking techniques using specialized tools and hardware acceleration.',
    simulation_type: 'password_cracking',
    category: { id: 'cryptography', name: 'Cryptography' },
    difficulty: { id: 'advanced', name: 'Advanced' },
    estimated_time: 50,
    is_premium: false,
    is_business: false
  },
  {
    id: 'process-scripting-attack',
    slug: 'process-scripting-attack',
    title: 'Process Scripting Vulnerabilities',
    description: 'Learn how to identify and exploit process scripting vulnerabilities in various platforms and systems.',
    simulation_type: 'process_scripting',
    category: { id: 'system-security', name: 'System Security' },
    difficulty: { id: 'intermediate', name: 'Intermediate' },
    estimated_time: 40,
    is_premium: false,
    is_business: false
  },
  {
    id: 'command-injection-basics',
    slug: 'command-injection-basics',
    title: 'Command Injection Basics',
    description: 'Learn how to identify and exploit command injection vulnerabilities in web applications and system interfaces.',
    simulation_type: 'command_injection',
    category: { id: 'web-security', name: 'Web Security' },
    difficulty: { id: 'beginner', name: 'Beginner' },
    estimated_time: 30,
    is_premium: false,
    is_business: false
  },
  {
    id: 'privilege-escalation-linux',
    slug: 'privilege-escalation-linux',
    title: 'Linux Privilege Escalation',
    description: 'Learn techniques to escalate privileges on Linux systems by exploiting common misconfigurations and vulnerabilities.',
    simulation_type: 'privilege_escalation',
    category: { id: 'system-security', name: 'System Security' },
    difficulty: { id: 'intermediate', name: 'Intermediate' },
    estimated_time: 45,
    is_premium: false,
    is_business: false
  }
];

export default StartHackHub;
