import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import StartHackHub from './StartHackHub';
import ChallengerHub from '../challenger/ChallengerHub';
import { FaRocket, FaTrophy, FaChevronDown, FaChevronUp } from 'react-icons/fa';

/**
 * StartHackDashboard Component
 * 
 * A wrapper component for the StartHackHub that ensures it stays within the dashboard
 * and provides navigation between Start Hack and Challenger sections.
 */
const StartHackDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const { simulationId } = useParams();
  const [showChallenger, setShowChallenger] = useState(false);
  const navigate = useNavigate();

  // Toggle the challenger section
  const toggleChallenger = () => {
    setShowChallenger(!showChallenger);
  };

  // Navigate to Challenger
  const goToChallenger = () => {
    navigate('/dashboard/challenger');
  };

  return (
    <div className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>
      <div className="px-4 py-4">
        {/* Content */}
        <div className="mb-8">
          <StartHackHub simulationId={simulationId} />
        </div>

        {/* Challenger Section Toggle */}
        <div className={`mt-12 p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border`}>
          <div className="flex justify-between items-center cursor-pointer" onClick={toggleChallenger}>
            <div>
              <h2 className="text-2xl font-bold flex items-center">
                <FaTrophy className="text-yellow-500 mr-2" />
                Ready for a Challenge?
              </h2>
              <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Test your skills in competitive cybersecurity challenges
              </p>
            </div>
            <div>
              {showChallenger ? (
                <FaChevronUp className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'}`} />
              ) : (
                <FaChevronDown className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'}`} />
              )}
            </div>
          </div>

          {showChallenger && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              transition={{ duration: 0.3 }}
              className="mt-6"
            >
              <p className="mb-4">
                The Challenger section offers competitive cybersecurity challenges where you can test your skills against other users.
                Complete challenges, earn points, and climb the leaderboards to prove your expertise.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <h3 className="font-bold mb-2">Competitive Challenges</h3>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Solve real-world cybersecurity challenges with time tracking and scoring
                  </p>
                </div>
                <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <h3 className="font-bold mb-2">Global Leaderboards</h3>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Compete against other users and see how you rank globally
                  </p>
                </div>
              </div>
              <button
                onClick={goToChallenger}
                className={`px-4 py-2 rounded-lg ${
                  darkMode
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
              >
                Go to Challenger
              </button>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StartHackDashboard;
