import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaNetworkWired, FaServer, FaInfoCircle, FaExclamationTriangle } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const DDoSSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  const [step, setStep] = useState(0);
  const [attackRate, setAttackRate] = useState(50);
  const [attackType, setAttackType] = useState('syn');
  const [targetServer, setTargetServer] = useState('web-server');
  const [isAttacking, setIsAttacking] = useState(false);
  const [progress, setProgress] = useState(0);
  const [serverStatus, setServerStatus] = useState('online');
  const [logs, setLogs] = useState([]);
  const [success, setSuccess] = useState(false);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [timer, setTimer] = useState(null);
  const [userProgress, setUserProgress] = useState(null);
  
  const logsEndRef = useRef(null);

  // Fetch user progress
  useEffect(() => {
    const fetchUserProgress = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          const { data, error } = await supabase
            .from('practice_simulation_attempts')
            .select('*')
            .eq('user_id', user.id)
            .eq('simulation_id', simulationId)
            .single();
          
          if (error && error.code !== 'PGSQL_ERROR') {
            console.error('Error fetching user progress:', error);
          }
          
          setUserProgress(data || null);
        }
      } catch (error) {
        console.error('Error fetching user progress:', error);
      }
    };
    
    fetchUserProgress();
  }, [simulationId]);

  // Start timer when component mounts
  useEffect(() => {
    if (!startTime) {
      setStartTime(Date.now());
      const interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
      setTimer(interval);
    }
    
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [startTime, timer]);

  // Scroll to bottom of logs
  useEffect(() => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Simulate DDoS attack
  const startAttack = () => {
    if (isAttacking) return;
    
    setIsAttacking(true);
    setServerStatus('degraded');
    setLogs([...logs, `[${formatTime(elapsedTime)}] Starting ${attackType.toUpperCase()} flood attack on ${targetServer}...`]);
    
    const attackInterval = setInterval(() => {
      setProgress((prev) => {
        const increment = (attackRate / 100) * (Math.random() * 5 + 5);
        const newProgress = prev + increment;
        
        if (newProgress >= 100) {
          clearInterval(attackInterval);
          setIsAttacking(false);
          setServerStatus('offline');
          setSuccess(true);
          setLogs((prevLogs) => [...prevLogs, `[${formatTime(elapsedTime)}] Attack successful! Target server is down.`]);
          
          // Stop timer
          if (timer) {
            clearInterval(timer);
          }
          
          // Calculate score
          const timeScore = Math.max(0, 100 - Math.floor(elapsedTime / 5));
          const efficiencyScore = Math.max(0, attackRate);
          const totalScore = Math.floor((timeScore + efficiencyScore) / 2);
          
          // Call onComplete with results
          onComplete({
            success: true,
            timeSpent: elapsedTime,
            attackType,
            targetServer,
            score: totalScore
          });
          
          // Save progress to database
          saveProgress(true);
          
          return 100;
        }
        
        // Add log entries at certain progress points
        if (Math.floor(prev / 10) < Math.floor(newProgress / 10)) {
          setLogs((prevLogs) => [
            ...prevLogs,
            `[${formatTime(elapsedTime)}] Server load at ${Math.floor(newProgress)}%. ${
              newProgress > 50 ? 'Target showing signs of strain.' : 'Target still responsive.'
            }`
          ]);
        }
        
        return newProgress;
      });
    }, 1000);
    
    return () => clearInterval(attackInterval);
  };

  // Save progress to database
  const saveProgress = async (isCompleted) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { error } = await supabase
          .from('practice_simulation_attempts')
          .upsert({
            user_id: user.id,
            simulation_id: simulationId,
            is_completed: isCompleted,
            completion_time: elapsedTime,
            approach_score: attackRate,
            total_score: Math.max(0, 100 - Math.floor(elapsedTime / 10) + attackRate / 2),
            started_at: new Date(startTime).toISOString(),
            completed_at: isCompleted ? new Date().toISOString() : null
          });
        
        if (error) {
          console.error('Error saving progress:', error);
        }
      }
    } catch (error) {
      console.error('Error saving progress:', error);
    }
  };

  // Steps for the DDoS simulation
  const steps = [
    {
      title: 'Introduction to DDoS Attacks',
      content: (
        <div>
          <p className="mb-4">
            A Distributed Denial of Service (DDoS) attack is a malicious attempt to disrupt the normal traffic of a targeted server, service, or network
            by overwhelming it with a flood of internet traffic.
          </p>
          <div className="flex items-center p-4 mb-4 rounded-lg bg-blue-500/10 border border-blue-500/30">
            <FaInfoCircle className="text-blue-500 mr-2 flex-shrink-0" />
            <p className="text-sm">
              This is a safe environment to understand how DDoS attacks work. The skills you learn here should only be used ethically,
              such as in penetration testing with proper authorization or to better understand how to defend against such attacks.
            </p>
          </div>
          <h3 className="text-lg font-bold mb-2">Objectives:</h3>
          <ul className="list-disc pl-6 mb-4">
            <li>Understand how DDoS attacks work</li>
            <li>Learn about different types of DDoS attacks</li>
            <li>Successfully simulate a DDoS attack in a controlled environment</li>
          </ul>
          <button
            onClick={() => setStep(1)}
            className={`px-4 py-2 rounded-lg ${
              darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
            } text-white`}
          >
            Start Simulation
          </button>
        </div>
      )
    },
    {
      title: 'DDoS Attack Simulator',
      content: (
        <div>
          <p className="mb-4">
            In this simulation, you'll configure and launch a simulated DDoS attack against a target server.
            Your goal is to overwhelm the server's resources and cause it to become unresponsive.
          </p>
          <div className="flex items-center p-4 mb-4 rounded-lg bg-yellow-500/10 border border-yellow-500/30">
            <FaExclamationTriangle className="text-yellow-500 mr-2 flex-shrink-0" />
            <p className="text-sm">
              Remember: This is a simulation. In real-world scenarios, unauthorized DDoS attacks are illegal and can result in severe penalties.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className={`p-4 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <h3 className="text-lg font-bold mb-4">Attack Configuration</h3>
              
              <div className="mb-4">
                <label className="block mb-2">Target Server:</label>
                <select
                  value={targetServer}
                  onChange={(e) => setTargetServer(e.target.value)}
                  className={`w-full px-4 py-2 rounded-lg ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'
                  } border`}
                  disabled={isAttacking}
                >
                  <option value="web-server">Web Server</option>
                  <option value="database-server">Database Server</option>
                  <option value="auth-server">Authentication Server</option>
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block mb-2">Attack Type:</label>
                <select
                  value={attackType}
                  onChange={(e) => setAttackType(e.target.value)}
                  className={`w-full px-4 py-2 rounded-lg ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'
                  } border`}
                  disabled={isAttacking}
                >
                  <option value="syn">SYN Flood</option>
                  <option value="udp">UDP Flood</option>
                  <option value="http">HTTP Flood</option>
                </select>
              </div>
              
              <div className="mb-4">
                <label className="block mb-2">Attack Rate: {attackRate}%</label>
                <input
                  type="range"
                  min="10"
                  max="100"
                  value={attackRate}
                  onChange={(e) => setAttackRate(parseInt(e.target.value))}
                  className="w-full"
                  disabled={isAttacking}
                />
                <div className="flex justify-between text-xs">
                  <span>Low</span>
                  <span>Medium</span>
                  <span>High</span>
                </div>
              </div>
              
              <button
                onClick={startAttack}
                disabled={isAttacking || success}
                className={`w-full px-4 py-2 rounded-lg ${
                  isAttacking || success
                    ? `${darkMode ? 'bg-gray-700' : 'bg-gray-300'} cursor-not-allowed`
                    : `${darkMode ? 'bg-red-600 hover:bg-red-700' : 'bg-red-500 hover:bg-red-600'} text-white`
                }`}
              >
                {isAttacking ? 'Attack in Progress...' : success ? 'Attack Completed' : 'Launch Attack'}
              </button>
            </div>
            
            <div className={`p-4 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <h3 className="text-lg font-bold mb-4">Target Status</h3>
              
              <div className="mb-4 flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  serverStatus === 'online' ? 'bg-green-500' :
                  serverStatus === 'degraded' ? 'bg-yellow-500' :
                  'bg-red-500'
                }`}></div>
                <span className="capitalize">{serverStatus}</span>
              </div>
              
              <div className="mb-4">
                <label className="block mb-2">Server Load:</label>
                <div className="w-full bg-gray-200 rounded-full h-4 dark:bg-gray-700">
                  <div
                    className={`h-4 rounded-full ${
                      progress < 50 ? 'bg-green-500' :
                      progress < 80 ? 'bg-yellow-500' :
                      'bg-red-500'
                    }`}
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <div className="text-right mt-1">{Math.floor(progress)}%</div>
              </div>
              
              <div className="mb-4">
                <label className="block mb-2">Attack Logs:</label>
                <div className={`h-40 overflow-y-auto p-2 rounded-lg font-mono text-xs ${
                  darkMode ? 'bg-gray-900 text-gray-300' : 'bg-gray-100 text-gray-800'
                }`}>
                  {logs.length === 0 ? (
                    <p className="text-gray-500">No logs yet. Start the attack to see logs.</p>
                  ) : (
                    logs.map((log, index) => (
                      <div key={index} className="mb-1">{log}</div>
                    ))
                  )}
                  <div ref={logsEndRef} />
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-between items-center">
            <button
              onClick={() => setStep(0)}
              className={`px-4 py-2 rounded-lg ${
                darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
              }`}
              disabled={isAttacking}
            >
              Back
            </button>
            {success && (
              <button
                onClick={() => setStep(2)}
                className={`px-4 py-2 rounded-lg ${
                  darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
                } text-white`}
              >
                Continue
              </button>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Explanation',
      content: (
        <div>
          <h3 className="text-lg font-bold mb-4">How DDoS Attacks Work</h3>
          <p className="mb-4">
            Congratulations! You've successfully simulated a DDoS attack. Let's understand what happened:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-bold mb-2">Attack Types:</h4>
              <ul className="list-disc pl-6 mb-4">
                <li><strong>SYN Flood:</strong> Exploits TCP handshake by sending many SYN packets without completing the handshake</li>
                <li><strong>UDP Flood:</strong> Sends a large number of UDP packets to random ports on a target server</li>
                <li><strong>HTTP Flood:</strong> Overwhelms a web server with seemingly legitimate HTTP GET or POST requests</li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-2">Impact:</h4>
              <ul className="list-disc pl-6 mb-4">
                <li>Exhausts server resources (CPU, memory, bandwidth)</li>
                <li>Prevents legitimate users from accessing services</li>
                <li>Can cause financial losses due to downtime</li>
                <li>May be used as a smokescreen for other attacks</li>
              </ul>
            </div>
          </div>
          
          <h3 className="text-lg font-bold mb-4">DDoS Defense Techniques</h3>
          <ul className="list-disc pl-6 mb-4">
            <li>Traffic filtering and rate limiting</li>
            <li>Using Content Delivery Networks (CDNs)</li>
            <li>Implementing load balancers</li>
            <li>Deploying DDoS protection services</li>
            <li>Configuring firewalls and intrusion prevention systems</li>
          </ul>
          
          <div className="flex items-center p-4 mb-4 rounded-lg bg-green-500/10 border border-green-500/30">
            <FaCheck className="text-green-500 mr-2 flex-shrink-0" />
            <div>
              <p className="font-bold">Simulation Complete!</p>
              <p>Time: {formatTime(elapsedTime)} | Attack Type: {attackType.toUpperCase()} | Target: {targetServer}</p>
            </div>
          </div>
          
          <button
            onClick={() => window.location.reload()}
            className={`px-4 py-2 rounded-lg ${
              darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
            } text-white`}
          >
            Restart Simulation
          </button>
        </div>
      )
    }
  ];

  return (
    <div className={`rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'}`}>
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold flex items-center">
            <FaNetworkWired className="text-red-500 mr-2" />
            {steps[step].title}
          </h2>
          <div className="flex items-center">
            <div className={`px-3 py-1 rounded-lg text-sm ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              Time: {formatTime(elapsedTime)}
            </div>
          </div>
        </div>
      </div>
      <div className="p-6">
        <motion.div
          key={step}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {steps[step].content}
        </motion.div>
      </div>
    </div>
  );
};

export default DDoSSimulator;
