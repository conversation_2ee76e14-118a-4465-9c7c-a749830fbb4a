import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaTerminal, FaInfoCircle, FaExclamationTriangle, FaLightbulb, FaCode, FaServer } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const ProcessScriptingSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  const [step, setStep] = useState(0);
  const [commandInput, setCommandInput] = useState('');
  const [output, setOutput] = useState('');
  const [success, setSuccess] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [hintLevel, setHintLevel] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [user, setUser] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);

  // Process scripting scenario data
  const scenario = {
    title: "Process Scripting Vulnerabilities",
    description: "Learn how to identify and exploit process scripting vulnerabilities in various systems.",
    hints: [
      "Try looking for commands that might reveal system information",
      "Process injection often involves manipulating environment variables or command parameters",
      "Look for ways to escape the restricted environment"
    ],
    vulnerabilities: [
      {
        name: "Command Injection",
        description: "Occurs when an application passes unsafe user-supplied data to a system shell",
        impact: "Allows attackers to execute arbitrary commands on the host operating system",
        remediation: "Use proper input validation, avoid shell commands when possible, and implement allowlists"
      },
      {
        name: "Path Traversal",
        description: "Allows attackers to access files outside the intended directory",
        impact: "Can lead to sensitive information disclosure or system compromise",
        remediation: "Validate and sanitize user input, use proper access controls"
      },
      {
        name: "Insecure Deserialization",
        description: "Processing untrusted data through deserialization mechanisms",
        impact: "Can lead to remote code execution, denial of service, or privilege escalation",
        remediation: "Implement integrity checks, use secure deserialization libraries"
      }
    ]
  };

  // Get current user
  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
    };
    getUser();
  }, []);

  // Start timer when simulation begins
  useEffect(() => {
    if (step > 0 && !startTime) {
      setStartTime(Date.now());
      setIsTimerRunning(true);
    }
  }, [step, startTime]);

  // Timer logic
  useEffect(() => {
    let interval;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, startTime]);

  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle command submission
  const handleCommandSubmit = (e) => {
    e.preventDefault();
    
    // Process the command
    processCommand(commandInput);
    
    // Clear the input
    setCommandInput('');
  };

  // Process the command
  const processCommand = (command) => {
    setAttempts(attempts + 1);
    
    // Simulate command processing
    let response = '';
    
    if (step === 1) {
      // Step 1: Identify vulnerable process
      if (command.toLowerCase().includes('ps') || command.toLowerCase().includes('list') || command.toLowerCase().includes('processes')) {
        response = `PID   USER     CMD
1     root     /sbin/init
234   www      /usr/sbin/httpd
456   admin    /usr/bin/vulnerable_script.py
789   user     /bin/bash
876   system   /usr/sbin/cron`;
        
        if (command.toLowerCase().includes('vulnerable') || command.toLowerCase().includes('456')) {
          setSuccess(true);
          setStep(2);
        }
      } else {
        response = "Command not recognized or insufficient. Try listing running processes.";
      }
    } else if (step === 2) {
      // Step 2: Exploit the vulnerability
      if (command.toLowerCase().includes('inject') || 
          command.toLowerCase().includes('exploit') || 
          command.toLowerCase().includes('vulnerable_script.py') || 
          command.toLowerCase().includes('--payload')) {
        response = "Attempting to exploit vulnerable_script.py...";
        
        if (command.toLowerCase().includes('--payload') && command.toLowerCase().includes('bypass')) {
          response += "\nExploit successful! You've gained access to the system.";
          setSuccess(true);
          setIsTimerRunning(false);
          setIsCompleted(true);
          setShowCongratulations(true);
          
          // Record completion
          if (user) {
            recordCompletion();
          }
          
          // Call onComplete callback
          if (onComplete) {
            onComplete({
              success: true,
              timeTaken: elapsedTime,
              attempts
            });
          }
        } else {
          response += "\nExploit failed. Try using a different payload or approach.";
        }
      } else {
        response = "Command not recognized. Try exploiting the vulnerable process.";
      }
    }
    
    setOutput(response);
  };

  // Record completion in database
  const recordCompletion = async () => {
    try {
      const { data, error } = await supabase
        .from('practice_simulation_attempts')
        .upsert({
          user_id: user.id,
          simulation_id: simulationId || 'process-scripting-attack',
          is_completed: true,
          completion_time: elapsedTime,
          approach_score: Math.max(100 - (attempts * 5), 50),
          total_score: Math.max(100 - (attempts * 5), 50),
          started_at: new Date(startTime).toISOString(),
          completed_at: new Date().toISOString()
        }, { onConflict: 'user_id, simulation_id' });

      if (error) {
        console.error('Error recording completion:', error);
      }
    } catch (error) {
      console.error('Error recording completion:', error);
    }
  };

  // Show next hint
  const showNextHint = () => {
    setShowHint(true);
    setHintLevel(prev => Math.min(prev + 1, scenario.hints.length - 1));
  };

  // Render the appropriate step
  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <div className="space-y-6">
            <p className="mb-4">
              Process scripting vulnerabilities occur when attackers can manipulate or inject code into running processes.
              These vulnerabilities can lead to privilege escalation, data theft, or complete system compromise.
            </p>
            <div className="flex items-center p-4 mb-4 rounded-lg bg-blue-500/10 border border-blue-500/30">
              <FaInfoCircle className="text-blue-500 mr-2 flex-shrink-0" />
              <p className="text-sm">
                This is a safe environment to practice identifying and exploiting process scripting vulnerabilities.
                The skills you learn here should only be used ethically, such as in penetration testing with proper authorization.
              </p>
            </div>
            <h3 className="text-lg font-bold mb-2">What You'll Learn:</h3>
            <ul className="list-disc pl-6 mb-4">
              <li>How to identify vulnerable processes</li>
              <li>Common process scripting vulnerabilities</li>
              <li>Exploitation techniques and their impacts</li>
              <li>Remediation strategies to protect systems</li>
            </ul>
            <button
              onClick={() => setStep(1)}
              className={`px-4 py-2 rounded-lg ${
                darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
              } text-white`}
            >
              Start Simulation
            </button>
          </div>
        );
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-bold">Step 1: Identify Vulnerable Processes</h2>
            <p className="mb-4">
              Your first task is to identify potentially vulnerable processes running on the target system.
              Use terminal commands to list running processes and look for anything suspicious.
            </p>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <div className="flex items-center mb-2">
                <FaTerminal className="mr-2" /> 
                <span className="font-bold">Terminal</span>
              </div>
              <div className="font-mono text-sm">
                <p>$ Connected to target system</p>
                <p>$ Enter commands to explore the system</p>
                {output && (
                  <div className="mt-2 p-2 rounded bg-black/20">
                    <pre>{output}</pre>
                  </div>
                )}
              </div>
            </div>
            <form onSubmit={handleCommandSubmit} className="mt-4">
              <div className="flex">
                <span className={`px-2 py-2 rounded-l-lg ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>$</span>
                <input
                  type="text"
                  value={commandInput}
                  onChange={(e) => setCommandInput(e.target.value)}
                  placeholder="Enter command"
                  className={`flex-grow p-2 ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border-y border-r rounded-r-lg`}
                />
                <button
                  type="submit"
                  className={`ml-2 px-4 py-2 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                >
                  Execute
                </button>
              </div>
            </form>
            {attempts > 0 && !success && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Try a different command to list processes.
              </div>
            )}
            {attempts >= 2 && !success && (
              <button
                onClick={showNextHint}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaLightbulb className="mr-2" /> {scenario.hints[hintLevel]}
              </div>
            )}
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-100'} text-green-500 flex items-center`}>
              <FaCheck className="mr-2" /> You've identified a vulnerable process: vulnerable_script.py (PID 456)
            </div>
            <h2 className="text-xl font-bold">Step 2: Exploit the Vulnerability</h2>
            <p className="mb-4">
              Now that you've identified the vulnerable process, try to exploit it to gain access to the system.
              The script appears to be vulnerable to command injection.
            </p>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <div className="flex items-center mb-2">
                <FaTerminal className="mr-2" /> 
                <span className="font-bold">Terminal</span>
              </div>
              <div className="font-mono text-sm">
                <p>$ Target: vulnerable_script.py (PID 456)</p>
                <p>$ The script accepts a --payload parameter</p>
                {output && (
                  <div className="mt-2 p-2 rounded bg-black/20">
                    <pre>{output}</pre>
                  </div>
                )}
              </div>
            </div>
            <form onSubmit={handleCommandSubmit} className="mt-4">
              <div className="flex">
                <span className={`px-2 py-2 rounded-l-lg ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>$</span>
                <input
                  type="text"
                  value={commandInput}
                  onChange={(e) => setCommandInput(e.target.value)}
                  placeholder="Enter command"
                  className={`flex-grow p-2 ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border-y border-r rounded-r-lg`}
                />
                <button
                  type="submit"
                  className={`ml-2 px-4 py-2 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                >
                  Execute
                </button>
              </div>
            </form>
            {attempts > 0 && !success && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Try a different approach to exploit the vulnerability.
              </div>
            )}
            {attempts >= 2 && !success && (
              <button
                onClick={showNextHint}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaLightbulb className="mr-2" /> Try using: exploit vulnerable_script.py --payload bypass
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  // Render congratulations screen
  const renderCongratulations = () => {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} text-center`}
      >
        <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center">
          <FaTerminal className="text-4xl text-green-500" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Exploitation Successful!</h2>
        <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          You've successfully identified and exploited a process scripting vulnerability.
        </p>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Time Taken</p>
            <p className="text-xl font-bold">{formatTime(elapsedTime)}</p>
          </div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Attempts</p>
            <p className="text-xl font-bold">{attempts}</p>
          </div>
        </div>
        
        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <h3 className="font-bold text-lg mb-2">Vulnerability Details</h3>
          <div className="space-y-4">
            {scenario.vulnerabilities.map((vuln, index) => (
              <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                <h4 className="font-bold text-blue-500">{vuln.name}</h4>
                <p className="mt-1"><span className="font-bold">Description:</span> {vuln.description}</p>
                <p className="mt-1"><span className="font-bold">Impact:</span> {vuln.impact}</p>
                <p className="mt-1"><span className="font-bold">Remediation:</span> {vuln.remediation}</p>
              </div>
            ))}
          </div>
        </div>
        
        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'}`}>
          <h3 className="font-bold text-lg mb-2">What You've Learned</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>How to identify vulnerable processes in a system</li>
            <li>Techniques for exploiting process scripting vulnerabilities</li>
            <li>The impact of successful exploitation</li>
            <li>Best practices for securing systems against these attacks</li>
          </ul>
        </div>
        
        <button
          onClick={() => window.location.href = '/dashboard/start-hack'}
          className={`px-6 py-3 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
        >
          Return to Simulations
        </button>
      </motion.div>
    );
  };

  return (
    <div className={`max-w-4xl mx-auto ${darkMode ? 'text-white' : 'text-gray-900'}`}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">{scenario.title}</h1>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{scenario.description}</p>
      </div>

      {/* Progress and Timer */}
      {step > 0 && (
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full ${step >= 1 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
              1
            </div>
            <div className={`h-1 w-16 mx-2 ${step >= 2 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
            <div className={`w-8 h-8 rounded-full ${step >= 2 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
              2
            </div>
            <div className={`h-1 w-16 mx-2 ${step >= 3 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
            <div className={`w-8 h-8 rounded-full ${step >= 3 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
              3
            </div>
          </div>
          <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
            <FaClock className="mr-2" />
            <span>{formatTime(elapsedTime)}</span>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}>
        {showCongratulations ? renderCongratulations() : renderStep()}
      </div>
    </div>
  );
};

export default ProcessScriptingSimulator;
