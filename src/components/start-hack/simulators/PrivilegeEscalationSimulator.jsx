import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaTerminal, FaInfoCircle, FaExclamationTriangle, FaLightbulb, FaUserCog, FaServer, FaClock } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const PrivilegeEscalationSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  const [step, setStep] = useState(0);
  const [commandInput, setCommandInput] = useState('');
  const [output, setOutput] = useState('');
  const [success, setSuccess] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [hintLevel, setHintLevel] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [user, setUser] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);

  // Privilege escalation scenario data
  const scenario = {
    title: "Linux Privilege Escalation",
    description: "Learn how to escalate privileges on Linux systems by exploiting common misconfigurations.",
    hints: [
      "Try checking for SUID binaries using the 'find' command",
      "Look for misconfigured sudo permissions with 'sudo -l'",
      "Check for writable files in /etc/ directory"
    ],
    vulnerabilities: [
      {
        name: "SUID Binaries Exploitation",
        description: "Exploiting executables with the SUID bit set to gain elevated privileges",
        impact: "Allows attackers to execute commands with the permissions of the file owner (often root)",
        remediation: "Regularly audit SUID binaries, remove unnecessary SUID permissions, update vulnerable applications"
      },
      {
        name: "Sudo Misconfiguration",
        description: "Exploiting overly permissive sudo rules that allow users to run certain commands as root",
        impact: "Allows attackers to execute privileged commands and potentially gain full root access",
        remediation: "Follow the principle of least privilege, regularly audit sudo configurations"
      },
      {
        name: "Writable Configuration Files",
        description: "Exploiting system configuration files that are writable by non-privileged users",
        impact: "Can lead to system compromise by modifying critical configurations",
        remediation: "Ensure proper file permissions, regularly audit file permissions on critical files"
      }
    ]
  };

  // Get current user
  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
    };
    getUser();
  }, []);

  // Start timer when simulation begins
  useEffect(() => {
    if (step > 0 && !startTime) {
      setStartTime(Date.now());
      setIsTimerRunning(true);
    }
  }, [step, startTime]);

  // Timer logic
  useEffect(() => {
    let interval;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, startTime]);

  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle command submission
  const handleCommandSubmit = (e) => {
    e.preventDefault();
    
    // Process the command
    processCommand(commandInput);
    
    // Clear the input
    setCommandInput('');
  };

  // Process the command
  const processCommand = (command) => {
    setAttempts(attempts + 1);
    
    // Simulate command processing
    let response = '';
    
    if (step === 1) {
      // Step 1: Reconnaissance
      if (command.toLowerCase().includes('find') && command.toLowerCase().includes('suid')) {
        response = `Searching for SUID binaries...\n\n/usr/bin/passwd\n/usr/bin/sudo\n/usr/bin/newgrp\n/usr/bin/chsh\n/usr/bin/gpasswd\n/usr/bin/chfn\n/usr/bin/mount\n/usr/bin/su\n/usr/bin/umount\n/usr/bin/custom_backup\n/usr/bin/pkexec`;
        
        if (command.toLowerCase().includes('custom_backup')) {
          setSuccess(true);
          setStep(2);
        }
      } else if (command.toLowerCase().includes('sudo') && command.toLowerCase().includes('-l')) {
        response = `User regular_user may run the following commands on target-system:\n    (ALL) NOPASSWD: /usr/bin/custom_backup`;
        setSuccess(true);
        setStep(2);
      } else if (command.toLowerCase().includes('ls') && command.toLowerCase().includes('-la')) {
        response = `total 32\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Aug 15 10:30 .\ndrwxr-xr-x 6 <USER> <GROUP> 4096 Aug 15 10:28 ..\n-rwsr-xr-x 1 root root 8744 Aug 15 10:29 custom_backup\n-rw-r--r-- 1 <USER> <GROUP>  220 Aug 15 10:28 .bash_logout\n-rw-r--r-- 1 <USER> <GROUP> 3771 Aug 15 10:28 .bashrc\n-rw-r--r-- 1 <USER> <GROUP>  807 Aug 15 10:28 .profile`;
        setSuccess(true);
        setStep(2);
      } else {
        response = `${command}: command executed with limited privileges`;
      }
    } else if (step === 2) {
      // Step 2: Exploitation
      if (command.toLowerCase().includes('custom_backup') && 
          (command.toLowerCase().includes('--help') || command.toLowerCase().includes('-h'))) {
        response = `custom_backup - Backup utility\n\nUsage: custom_backup [options] [path]\n\nOptions:\n  --help, -h     Show this help message\n  --debug, -d    Run in debug mode\n  --target, -t   Specify target directory\n\nExample: custom_backup -d /home/<USER>
      } else if (command.toLowerCase().includes('custom_backup') && command.toLowerCase().includes('-d')) {
        response = `Running custom_backup in debug mode...\n\nDEBUG: Current user: regular_user\nDEBUG: Effective user: root\nDEBUG: Loading configuration from /etc/custom_backup.conf\nDEBUG: Starting backup process\nDEBUG: Spawning shell for debugging purposes\n\n# whoami\nroot\n\n# id\nuid=0(root) gid=0(root) groups=0(root)`;
        setSuccess(true);
        setIsTimerRunning(false);
        setIsCompleted(true);
        setShowCongratulations(true);
        
        // Record completion
        if (user) {
          recordCompletion();
        }
        
        // Call onComplete callback
        if (onComplete) {
          onComplete({
            success: true,
            timeTaken: elapsedTime,
            attempts
          });
        }
      } else if (command.toLowerCase().includes('sudo') && command.toLowerCase().includes('custom_backup')) {
        response = `Running custom_backup with sudo...\n\nBackup completed successfully.\n\n# whoami\nroot\n\n# id\nuid=0(root) gid=0(root) groups=0(root)`;
        setSuccess(true);
        setIsTimerRunning(false);
        setIsCompleted(true);
        setShowCongratulations(true);
        
        // Record completion
        if (user) {
          recordCompletion();
        }
        
        // Call onComplete callback
        if (onComplete) {
          onComplete({
            success: true,
            timeTaken: elapsedTime,
            attempts
          });
        }
      } else {
        response = `${command}: command executed with limited privileges`;
      }
    }
    
    setOutput(response);
  };

  // Record completion in database
  const recordCompletion = async () => {
    try {
      const { data, error } = await supabase
        .from('practice_simulation_attempts')
        .upsert({
          user_id: user.id,
          simulation_id: simulationId || 'privilege-escalation-linux',
          is_completed: true,
          completion_time: elapsedTime,
          approach_score: Math.max(100 - (attempts * 5), 50),
          total_score: Math.max(100 - (attempts * 5), 50),
          started_at: new Date(startTime).toISOString(),
          completed_at: new Date().toISOString()
        }, { onConflict: 'user_id, simulation_id' });

      if (error) {
        console.error('Error recording completion:', error);
      }
    } catch (error) {
      console.error('Error recording completion:', error);
    }
  };

  // Show next hint
  const showNextHint = () => {
    setShowHint(true);
    setHintLevel(prev => Math.min(prev + 1, scenario.hints.length - 1));
  };

  // Render the appropriate step
  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <div className="space-y-6">
            <p className="mb-4">
              Privilege escalation is the process of exploiting vulnerabilities or misconfigurations to gain elevated access
              to resources that are normally protected from an application or user. In Linux systems, this often means
              gaining root access from a standard user account.
            </p>
            <div className="flex items-center p-4 mb-4 rounded-lg bg-blue-500/10 border border-blue-500/30">
              <FaInfoCircle className="text-blue-500 mr-2 flex-shrink-0" />
              <p className="text-sm">
                This is a safe environment to practice privilege escalation techniques.
                The skills you learn here should only be used ethically, such as in penetration testing with proper authorization.
              </p>
            </div>
            <h3 className="text-lg font-bold mb-2">What You'll Learn:</h3>
            <ul className="list-disc pl-6 mb-4">
              <li>Common Linux privilege escalation vectors</li>
              <li>How to identify vulnerable configurations</li>
              <li>Techniques for exploiting SUID binaries and sudo misconfigurations</li>
              <li>Best practices for securing Linux systems</li>
            </ul>
            <button
              onClick={() => setStep(1)}
              className={`px-4 py-2 rounded-lg ${
                darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
              } text-white`}
            >
              Start Simulation
            </button>
          </div>
        );
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-bold">Step 1: Reconnaissance</h2>
            <p className="mb-4">
              You have access to a Linux system as a regular user. Your goal is to escalate privileges to root.
              Start by exploring the system to identify potential privilege escalation vectors.
            </p>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <div className="flex items-center mb-2">
                <FaTerminal className="mr-2" /> 
                <span className="font-bold">Terminal (regular_user@target-system)</span>
              </div>
              <div className="font-mono text-sm">
                <p>$ id</p>
                <p>uid=1000(regular_user) gid=1000(regular_user) groups=1000(regular_user)</p>
                <p>$ pwd</p>
                <p>/home/<USER>/p>
                <p>$ Enter commands to explore the system</p>
                {output && (
                  <div className="mt-2 p-2 rounded bg-black/20">
                    <pre className="whitespace-pre-wrap">{output}</pre>
                  </div>
                )}
              </div>
            </div>
            <form onSubmit={handleCommandSubmit} className="mt-4">
              <div className="flex">
                <span className={`px-2 py-2 rounded-l-lg ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>$</span>
                <input
                  type="text"
                  value={commandInput}
                  onChange={(e) => setCommandInput(e.target.value)}
                  placeholder="Enter command"
                  className={`flex-grow p-2 ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border-y border-r rounded-r-lg`}
                />
                <button
                  type="submit"
                  className={`ml-2 px-4 py-2 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                >
                  Execute
                </button>
              </div>
            </form>
            {attempts > 0 && !success && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Try a different command to identify privilege escalation vectors.
              </div>
            )}
            {attempts >= 2 && !success && (
              <button
                onClick={showNextHint}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaLightbulb className="mr-2" /> {scenario.hints[hintLevel]}
              </div>
            )}
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-100'} text-green-500 flex items-center`}>
              <FaCheck className="mr-2" /> You've identified a potential privilege escalation vector: custom_backup
            </div>
            <h2 className="text-xl font-bold">Step 2: Exploitation</h2>
            <p className="mb-4">
              Now that you've identified a potential privilege escalation vector, try to exploit it to gain root access.
              The custom_backup binary has the SUID bit set and/or can be run with sudo.
            </p>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <div className="flex items-center mb-2">
                <FaTerminal className="mr-2" /> 
                <span className="font-bold">Terminal (regular_user@target-system)</span>
              </div>
              <div className="font-mono text-sm">
                <p>$ id</p>
                <p>uid=1000(regular_user) gid=1000(regular_user) groups=1000(regular_user)</p>
                <p>$ Enter commands to exploit the vulnerability</p>
                {output && (
                  <div className="mt-2 p-2 rounded bg-black/20">
                    <pre className="whitespace-pre-wrap">{output}</pre>
                  </div>
                )}
              </div>
            </div>
            <form onSubmit={handleCommandSubmit} className="mt-4">
              <div className="flex">
                <span className={`px-2 py-2 rounded-l-lg ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>$</span>
                <input
                  type="text"
                  value={commandInput}
                  onChange={(e) => setCommandInput(e.target.value)}
                  placeholder="Enter command"
                  className={`flex-grow p-2 ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border-y border-r rounded-r-lg`}
                />
                <button
                  type="submit"
                  className={`ml-2 px-4 py-2 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                >
                  Execute
                </button>
              </div>
            </form>
            {attempts > 0 && !success && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Try a different approach to exploit the vulnerability.
              </div>
            )}
            {attempts >= 2 && !success && (
              <button
                onClick={showNextHint}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaLightbulb className="mr-2" /> Try running: custom_backup --help to see available options
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  // Render congratulations screen
  const renderCongratulations = () => {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} text-center`}
      >
        <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center">
          <FaUserCog className="text-4xl text-green-500" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Privilege Escalation Successful!</h2>
        <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          You've successfully escalated privileges from a regular user to root.
        </p>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Time Taken</p>
            <p className="text-xl font-bold">{formatTime(elapsedTime)}</p>
          </div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Attempts</p>
            <p className="text-xl font-bold">{attempts}</p>
          </div>
        </div>
        
        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <h3 className="font-bold text-lg mb-2">Vulnerability Details</h3>
          <div className="space-y-4">
            {scenario.vulnerabilities.map((vuln, index) => (
              <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                <h4 className="font-bold text-blue-500">{vuln.name}</h4>
                <p className="mt-1"><span className="font-bold">Description:</span> {vuln.description}</p>
                <p className="mt-1"><span className="font-bold">Impact:</span> {vuln.impact}</p>
                <p className="mt-1"><span className="font-bold">Remediation:</span> {vuln.remediation}</p>
              </div>
            ))}
          </div>
        </div>
        
        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'}`}>
          <h3 className="font-bold text-lg mb-2">What You've Learned</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>How to identify potential privilege escalation vectors in Linux systems</li>
            <li>Techniques for exploiting SUID binaries and sudo misconfigurations</li>
            <li>The importance of proper system hardening and security configurations</li>
            <li>Best practices for preventing privilege escalation vulnerabilities</li>
          </ul>
        </div>
        
        <button
          onClick={() => window.location.href = '/dashboard/start-hack'}
          className={`px-6 py-3 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
        >
          Return to Simulations
        </button>
      </motion.div>
    );
  };

  return (
    <div className={`max-w-4xl mx-auto ${darkMode ? 'text-white' : 'text-gray-900'}`}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">{scenario.title}</h1>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{scenario.description}</p>
      </div>

      {/* Progress and Timer */}
      {step > 0 && (
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full ${step >= 1 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
              1
            </div>
            <div className={`h-1 w-16 mx-2 ${step >= 2 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
            <div className={`w-8 h-8 rounded-full ${step >= 2 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
              2
            </div>
            <div className={`h-1 w-16 mx-2 ${step >= 3 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
            <div className={`w-8 h-8 rounded-full ${step >= 3 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
              3
            </div>
          </div>
          <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
            <FaClock className="mr-2" />
            <span>{formatTime(elapsedTime)}</span>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}>
        {showCongratulations ? renderCongratulations() : renderStep()}
      </div>
    </div>
  );
};

export default PrivilegeEscalationSimulator;
