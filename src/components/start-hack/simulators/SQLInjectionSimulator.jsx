import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaLock, FaUnlock, FaDatabase, FaCode, FaInfoCircle, FaExclamationTriangle } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const SQLInjectionSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  const [step, setStep] = useState(0);
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [success, setSuccess] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [timer, setTimer] = useState(null);
  const [userProgress, setUserProgress] = useState(null);

  // Fetch user progress
  useEffect(() => {
    const fetchUserProgress = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          const { data, error } = await supabase
            .from('practice_simulation_attempts')
            .select('*')
            .eq('user_id', user.id)
            .eq('simulation_id', simulationId)
            .single();
          
          if (error && error.code !== 'PGSQL_ERROR') {
            console.error('Error fetching user progress:', error);
          }
          
          setUserProgress(data || null);
        }
      } catch (error) {
        console.error('Error fetching user progress:', error);
      }
    };
    
    fetchUserProgress();
  }, [simulationId]);

  // Start timer when component mounts
  useEffect(() => {
    if (!startTime) {
      setStartTime(Date.now());
      const interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
      setTimer(interval);
    }
    
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [startTime, timer]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Steps for the SQL injection simulation
  const steps = [
    {
      title: 'Introduction to SQL Injection',
      content: (
        <div>
          <p className="mb-4">
            SQL Injection is a code injection technique that exploits vulnerabilities in applications that interact with databases.
            In this simulation, you'll learn how to identify and exploit a basic SQL injection vulnerability in a login form.
          </p>
          <div className="flex items-center p-4 mb-4 rounded-lg bg-blue-500/10 border border-blue-500/30">
            <FaInfoCircle className="text-blue-500 mr-2 flex-shrink-0" />
            <p className="text-sm">
              This is a safe environment to practice SQL injection techniques. The skills you learn here should only be used ethically,
              such as in penetration testing with proper authorization.
            </p>
          </div>
          <h3 className="text-lg font-bold mb-2">Objectives:</h3>
          <ul className="list-disc pl-6 mb-4">
            <li>Understand how SQL injection works</li>
            <li>Learn to identify vulnerable input fields</li>
            <li>Successfully bypass a login form using SQL injection</li>
          </ul>
          <button
            onClick={() => setStep(1)}
            className={`px-4 py-2 rounded-lg ${
              darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
            } text-white`}
          >
            Start Simulation
          </button>
        </div>
      )
    },
    {
      title: 'The Login Form',
      content: (
        <div>
          <p className="mb-4">
            You're presented with a login form for a website. Your goal is to bypass the authentication without knowing the password.
          </p>
          <p className="mb-4">
            When a user submits this form, the application might execute an SQL query like this:
          </p>
          <div className={`p-4 rounded-lg font-mono text-sm mb-4 ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
            <code>SELECT * FROM users WHERE username = 'input_username' AND password = 'input_password';</code>
          </div>
          <p className="mb-4">
            If the query returns a row, the login is successful. If not, it fails.
          </p>
          <div className="flex items-center p-4 mb-4 rounded-lg bg-yellow-500/10 border border-yellow-500/30">
            <FaExclamationTriangle className="text-yellow-500 mr-2 flex-shrink-0" />
            <p className="text-sm">
              Think about how you could manipulate this query to always return a result, regardless of the password.
            </p>
          </div>
          <div className={`p-6 rounded-lg border mb-4 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <h3 className="text-lg font-bold mb-4 text-center">Login Form</h3>
            <div className="mb-4">
              <label className="block mb-2">Username:</label>
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                className={`w-full px-4 py-2 rounded-lg ${
                  darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'
                } border`}
                placeholder="Enter username"
              />
            </div>
            <div className="mb-4">
              <label className="block mb-2">Password:</label>
              <input
                type="password"
                className={`w-full px-4 py-2 rounded-lg ${
                  darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'
                } border`}
                placeholder="Enter password"
                value="********"
                readOnly
              />
            </div>
            <button
              onClick={() => {
                setAttempts(attempts + 1);
                
                // Check if the input contains SQL injection
                if (input.includes("'") && (input.includes("--") || input.includes("OR") || input.includes("="))) {
                  setOutput("Login successful! You've bypassed the authentication.");
                  setSuccess(true);
                  
                  // Stop timer
                  if (timer) {
                    clearInterval(timer);
                  }
                  
                  // Calculate score based on attempts and time
                  const timeScore = Math.max(0, 100 - Math.floor(elapsedTime / 5));
                  const attemptScore = Math.max(0, 100 - (attempts * 10));
                  const totalScore = Math.floor((timeScore + attemptScore) / 2);
                  
                  // Call onComplete with results
                  onComplete({
                    success: true,
                    timeSpent: elapsedTime,
                    attempts,
                    score: totalScore
                  });
                  
                  // Save progress to database
                  saveProgress(true);
                } else {
                  setOutput("Login failed. Invalid username or password.");
                }
              }}
              className={`w-full px-4 py-2 rounded-lg ${
                darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
              } text-white`}
            >
              Login
            </button>
          </div>
          {output && (
            <div className={`p-4 rounded-lg mb-4 ${
              success
                ? 'bg-green-500/10 border border-green-500/30 text-green-500'
                : 'bg-red-500/10 border border-red-500/30 text-red-500'
            }`}>
              <p>{output}</p>
            </div>
          )}
          <div className="flex justify-between items-center">
            <button
              onClick={() => setStep(0)}
              className={`px-4 py-2 rounded-lg ${
                darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
              }`}
            >
              Back
            </button>
            {success && (
              <button
                onClick={() => setStep(2)}
                className={`px-4 py-2 rounded-lg ${
                  darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
                } text-white`}
              >
                Continue
              </button>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Explanation',
      content: (
        <div>
          <h3 className="text-lg font-bold mb-4">How SQL Injection Works</h3>
          <p className="mb-4">
            Congratulations! You've successfully exploited an SQL injection vulnerability. Let's understand what happened:
          </p>
          <div className={`p-4 rounded-lg font-mono text-sm mb-4 ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
            <p>Original query:</p>
            <code>SELECT * FROM users WHERE username = 'input_username' AND password = 'input_password';</code>
            <p className="mt-2">With your injection:</p>
            <code>SELECT * FROM users WHERE username = '{input}' AND password = 'anything';</code>
          </div>
          <p className="mb-4">
            By injecting SQL code into the username field, you manipulated the query to always return true, regardless of the password.
          </p>
          <h3 className="text-lg font-bold mb-4">Prevention Techniques</h3>
          <ul className="list-disc pl-6 mb-4">
            <li>Use parameterized queries or prepared statements</li>
            <li>Implement input validation and sanitization</li>
            <li>Apply the principle of least privilege for database accounts</li>
            <li>Use ORM (Object-Relational Mapping) libraries</li>
          </ul>
          <div className="flex items-center p-4 mb-4 rounded-lg bg-green-500/10 border border-green-500/30">
            <FaCheck className="text-green-500 mr-2 flex-shrink-0" />
            <div>
              <p className="font-bold">Simulation Complete!</p>
              <p>Time: {formatTime(elapsedTime)} | Attempts: {attempts}</p>
            </div>
          </div>
          <button
            onClick={() => window.location.reload()}
            className={`px-4 py-2 rounded-lg ${
              darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
            } text-white`}
          >
            Restart Simulation
          </button>
        </div>
      )
    }
  ];

  // Save progress to database
  const saveProgress = async (isCompleted) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { error } = await supabase
          .from('practice_simulation_attempts')
          .upsert({
            user_id: user.id,
            simulation_id: simulationId,
            is_completed: isCompleted,
            completion_time: elapsedTime,
            approach_score: Math.max(0, 100 - (attempts * 10)),
            total_score: Math.max(0, 100 - Math.floor(elapsedTime / 10) - (attempts * 5)),
            started_at: new Date(startTime).toISOString(),
            completed_at: isCompleted ? new Date().toISOString() : null
          });
        
        if (error) {
          console.error('Error saving progress:', error);
        }
      }
    } catch (error) {
      console.error('Error saving progress:', error);
    }
  };

  return (
    <div className={`rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'}`}>
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold flex items-center">
            <FaDatabase className="text-blue-500 mr-2" />
            {steps[step].title}
          </h2>
          <div className="flex items-center">
            <div className={`px-3 py-1 rounded-lg text-sm ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              Time: {formatTime(elapsedTime)}
            </div>
          </div>
        </div>
      </div>
      <div className="p-6">
        <motion.div
          key={step}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {steps[step].content}
        </motion.div>
      </div>
    </div>
  );
};

export default SQLInjectionSimulator;
