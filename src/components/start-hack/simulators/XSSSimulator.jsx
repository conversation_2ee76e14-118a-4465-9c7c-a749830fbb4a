import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaCode, FaInfoCircle, FaExclamationTriangle, FaGlobe, FaHtml5 } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const XSSSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  const [step, setStep] = useState(0);
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [success, setSuccess] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [timer, setTimer] = useState(null);
  const [userProgress, setUserProgress] = useState(null);
  const [previewContent, setPreviewContent] = useState('');

  const previewRef = useRef(null);

  // Fetch user progress
  useEffect(() => {
    const fetchUserProgress = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();

        if (user) {
          const { data, error } = await supabase
            .from('practice_simulation_attempts')
            .select('*')
            .eq('user_id', user.id)
            .eq('simulation_id', simulationId)
            .single();

          if (error && error.code !== 'PGSQL_ERROR') {
            console.error('Error fetching user progress:', error);
          }

          setUserProgress(data || null);
        }
      } catch (error) {
        console.error('Error fetching user progress:', error);
      }
    };

    fetchUserProgress();
  }, [simulationId]);

  // Start timer when component mounts
  useEffect(() => {
    if (!startTime) {
      setStartTime(Date.now());
      const interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
      setTimer(interval);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [startTime, timer]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Update preview content when input changes
  useEffect(() => {
    if (step === 1) {
      const baseContent = `
        <div class="comment-section">
          <h3>User Comments</h3>
          <div class="comment">
            <strong>Admin:</strong> Welcome to our website!
          </div>
          <div class="comment">
            <strong>User1:</strong> Great content, thanks for sharing!
          </div>
          <div class="comment">
            <strong>You:</strong> ${input}
          </div>
        </div>
      `;
      setPreviewContent(baseContent);

      // If in preview mode, update the iframe content
      if (previewRef.current) {
        try {
          const doc = previewRef.current.contentDocument || previewRef.current.contentWindow?.document;
          if (doc) {
            doc.open();
            doc.write(`
              <html>
                <head>
                  <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    .comment-section { border: 1px solid #ccc; padding: 15px; border-radius: 5px; }
                    .comment { margin-bottom: 10px; padding: 8px; background-color: #f5f5f5; border-radius: 3px; }
                  </style>
                </head>
                <body>
                  ${baseContent}
                </body>
              </html>
            `);
            doc.close();
          }
        } catch (error) {
          console.error('Error updating iframe content:', error);
        }
      }
    }
  }, [input, step]);

  // Save progress to database
  const saveProgress = async (isCompleted) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { error } = await supabase
          .from('practice_simulation_attempts')
          .upsert({
            user_id: user.id,
            simulation_id: simulationId,
            is_completed: isCompleted,
            completion_time: elapsedTime,
            approach_score: Math.max(0, 100 - (attempts * 10)),
            total_score: Math.max(0, 100 - Math.floor(elapsedTime / 10) - (attempts * 5)),
            started_at: new Date(startTime).toISOString(),
            completed_at: isCompleted ? new Date().toISOString() : null
          });

        if (error) {
          console.error('Error saving progress:', error);
        }
      }
    } catch (error) {
      console.error('Error saving progress:', error);
    }
  };

  // Steps for the XSS simulation
  const steps = [
    {
      title: 'Introduction to Cross-Site Scripting (XSS)',
      content: (
        <div>
          <p className="mb-4">
            Cross-Site Scripting (XSS) is a type of security vulnerability that allows attackers to inject malicious client-side scripts into web pages viewed by other users.
            In this simulation, you'll learn how to identify and exploit a basic XSS vulnerability in a comment section.
          </p>
          <div className="flex items-center p-4 mb-4 rounded-lg bg-blue-500/10 border border-blue-500/30">
            <FaInfoCircle className="text-blue-500 mr-2 flex-shrink-0" />
            <p className="text-sm">
              This is a safe environment to practice XSS techniques. The skills you learn here should only be used ethically,
              such as in penetration testing with proper authorization.
            </p>
          </div>
          <h3 className="text-lg font-bold mb-2">Objectives:</h3>
          <ul className="list-disc pl-6 mb-4">
            <li>Understand how XSS vulnerabilities work</li>
            <li>Learn to identify vulnerable input fields</li>
            <li>Successfully exploit an XSS vulnerability</li>
          </ul>
          <button
            onClick={() => setStep(1)}
            className={`px-4 py-2 rounded-lg ${
              darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
            } text-white`}
          >
            Start Simulation
          </button>
        </div>
      )
    },
    {
      title: 'The Comment Section',
      content: (
        <div>
          <p className="mb-4">
            You're presented with a comment section on a website. Your goal is to inject JavaScript code that will execute when the page loads.
          </p>
          <p className="mb-4">
            The website doesn't properly sanitize user input, making it vulnerable to XSS attacks.
          </p>
          <div className="flex items-center p-4 mb-4 rounded-lg bg-yellow-500/10 border border-yellow-500/30">
            <FaExclamationTriangle className="text-yellow-500 mr-2 flex-shrink-0" />
            <p className="text-sm">
              Try entering HTML tags or JavaScript code in the comment field to see if they execute.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <h3 className="text-lg font-bold mb-4 text-center">Comment Form</h3>
              <div className="mb-4">
                <label className="block mb-2">Your Comment:</label>
                <textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  className={`w-full px-4 py-2 rounded-lg ${
                    darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'
                  } border`}
                  placeholder="Enter your comment here..."
                  rows={4}
                />
              </div>
              <button
                onClick={() => {
                  setAttempts(attempts + 1);

                  // Check if the input contains XSS payload
                  if ((input.includes('<script>') && input.includes('</script>')) ||
                      input.includes('javascript:') ||
                      (input.includes('onerror=') || input.includes('onclick=')) ||
                      input.includes('alert(')) {
                    setOutput("XSS vulnerability successfully exploited!");
                    setSuccess(true);

                    // Stop timer
                    if (timer) {
                      clearInterval(timer);
                    }

                    // Calculate score based on attempts and time
                    const timeScore = Math.max(0, 100 - Math.floor(elapsedTime / 5));
                    const attemptScore = Math.max(0, 100 - (attempts * 10));
                    const totalScore = Math.floor((timeScore + attemptScore) / 2);

                    // Call onComplete with results
                    onComplete({
                      success: true,
                      timeSpent: elapsedTime,
                      attempts,
                      score: totalScore
                    });

                    // Save progress to database
                    saveProgress(true);
                  } else {
                    setOutput("Comment posted, but no XSS vulnerability detected.");
                  }
                }}
                className={`w-full px-4 py-2 rounded-lg ${
                  darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
                } text-white`}
              >
                Post Comment
              </button>
            </div>

            <div className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <h3 className="text-lg font-bold mb-4 text-center">Preview</h3>
              <div className={`h-48 overflow-auto rounded-lg border ${
                darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
              }`}>
                <iframe
                  ref={previewRef}
                  title="Comment Preview"
                  className="w-full h-full"
                  sandbox="allow-scripts"
                  srcDoc={`
                    <html>
                      <head>
                        <style>
                          body { font-family: Arial, sans-serif; padding: 20px; }
                          .comment-section { border: 1px solid #ccc; padding: 15px; border-radius: 5px; }
                          .comment { margin-bottom: 10px; padding: 8px; background-color: #f5f5f5; border-radius: 3px; }
                        </style>
                      </head>
                      <body>
                        <div class="comment-section">
                          <h3>User Comments</h3>
                          <div class="comment">
                            <strong>Admin:</strong> Welcome to our website!
                          </div>
                          <div class="comment">
                            <strong>User1:</strong> Great content, thanks for sharing!
                          </div>
                          <div class="comment">
                            <strong>You:</strong> ${input}
                          </div>
                        </div>
                      </body>
                    </html>
                  `}
                />
              </div>
            </div>
          </div>

          {output && (
            <div className={`p-4 rounded-lg mb-4 ${
              success
                ? 'bg-green-500/10 border border-green-500/30 text-green-500'
                : 'bg-red-500/10 border border-red-500/30 text-red-500'
            }`}>
              <p>{output}</p>
            </div>
          )}

          <div className="flex justify-between items-center">
            <button
              onClick={() => setStep(0)}
              className={`px-4 py-2 rounded-lg ${
                darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
              }`}
            >
              Back
            </button>
            {success && (
              <button
                onClick={() => setStep(2)}
                className={`px-4 py-2 rounded-lg ${
                  darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
                } text-white`}
              >
                Continue
              </button>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Explanation',
      content: (
        <div>
          <h3 className="text-lg font-bold mb-4">How XSS Vulnerabilities Work</h3>
          <p className="mb-4">
            Congratulations! You've successfully exploited an XSS vulnerability. Let's understand what happened:
          </p>
          <div className={`p-4 rounded-lg font-mono text-sm mb-4 ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
            <p>You injected JavaScript code that executed in the browser:</p>
            <code>{input}</code>
          </div>
          <p className="mb-4">
            When a website doesn't properly sanitize user input, attackers can inject malicious scripts that execute when other users view the page.
            This can lead to cookie theft, session hijacking, or other attacks.
          </p>
          <h3 className="text-lg font-bold mb-4">Types of XSS Attacks</h3>
          <ul className="list-disc pl-6 mb-4">
            <li><strong>Reflected XSS:</strong> The malicious script is reflected off a web server, such as in search results or error messages.</li>
            <li><strong>Stored XSS:</strong> The malicious script is stored on the target server, such as in a database, message forum, or comment field.</li>
            <li><strong>DOM-based XSS:</strong> The vulnerability exists in client-side code rather than server-side code.</li>
          </ul>
          <h3 className="text-lg font-bold mb-4">Prevention Techniques</h3>
          <ul className="list-disc pl-6 mb-4">
            <li>Input validation and sanitization</li>
            <li>Output encoding</li>
            <li>Content Security Policy (CSP)</li>
            <li>Use of modern frameworks that automatically escape output</li>
            <li>X-XSS-Protection header</li>
          </ul>
          <div className="flex items-center p-4 mb-4 rounded-lg bg-green-500/10 border border-green-500/30">
            <FaCheck className="text-green-500 mr-2 flex-shrink-0" />
            <div>
              <p className="font-bold">Simulation Complete!</p>
              <p>Time: {formatTime(elapsedTime)} | Attempts: {attempts}</p>
            </div>
          </div>
          <button
            onClick={() => window.location.reload()}
            className={`px-4 py-2 rounded-lg ${
              darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
            } text-white`}
          >
            Restart Simulation
          </button>
        </div>
      )
    }
  ];

  return (
    <div className={`rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'}`}>
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold flex items-center">
            <FaHtml5 className="text-orange-500 mr-2" />
            {steps[step].title}
          </h2>
          <div className="flex items-center">
            <div className={`px-3 py-1 rounded-lg text-sm ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              Time: {formatTime(elapsedTime)}
            </div>
          </div>
        </div>
      </div>
      <div className="p-6">
        <motion.div
          key={step}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {steps[step].content}
        </motion.div>
      </div>
    </div>
  );
};

export default XSSSimulator;
