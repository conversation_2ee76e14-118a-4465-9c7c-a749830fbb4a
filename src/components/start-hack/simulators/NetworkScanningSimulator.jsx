import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaNetworkWired, FaServer, FaCheck, FaTimes, FaInfoCircle, FaTerminal, FaSearch, FaLock, FaUnlock, FaExclamationTriangle, FaClock } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { useXP } from '../../../contexts/XPContext';
import { supabase } from '../../../lib/supabase';

const NetworkScanningSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  const { awardXP } = useXP();
  const [step, setStep] = useState(0);
  const [selectedScanType, setSelectedScanType] = useState(null);
  const [targetInput, setTargetInput] = useState('');
  const [portInput, setPortInput] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [scanResults, setScanResults] = useState([]);
  const [vulnerableServices, setVulnerableServices] = useState([]);
  const [user, setUser] = useState(null);

  // Network scanning scenario data
  const scenario = {
    title: "Network Scanning Simulation",
    description: "In this simulation, you'll learn how to perform network reconnaissance and identify vulnerable services on a network.",
    targetNetwork: "***********/24",
    vulnerableHost: "************",
    vulnerablePorts: [22, 80, 443, 3306],
    vulnerableServices: [
      { port: 22, service: "SSH", version: "OpenSSH 7.2p2", vulnerability: "CVE-2016-6210: User enumeration vulnerability" },
      { port: 80, service: "HTTP", version: "Apache 2.4.18", vulnerability: "CVE-2016-8743: HTTP request smuggling vulnerability" },
      { port: 443, service: "HTTPS", version: "Apache 2.4.18", vulnerability: "CVE-2016-8743: HTTP request smuggling vulnerability" },
      { port: 3306, service: "MySQL", version: "5.7.12", vulnerability: "CVE-2016-6662: Remote code execution vulnerability" }
    ],
    hints: [
      "Try scanning the entire subnet to find hosts",
      "Look for common ports like 22 (SSH), 80 (HTTP), 443 (HTTPS), and database ports",
      "Check for outdated service versions that might have known vulnerabilities"
    ],
    scanTypes: [
      {
        id: "ping",
        name: "Ping Sweep",
        description: "Discovers active hosts on the network using ICMP echo requests",
        effectiveness: "Basic host discovery",
        speed: "Fast",
        icon: <FaSearch className="text-blue-500" />
      },
      {
        id: "port",
        name: "Port Scan",
        description: "Scans for open ports on a specific host or network",
        effectiveness: "Service discovery",
        speed: "Medium",
        icon: <FaServer className="text-green-500" />
      },
      {
        id: "vulnerability",
        name: "Vulnerability Scan",
        description: "Identifies potential vulnerabilities in discovered services",
        effectiveness: "Vulnerability detection",
        speed: "Slow",
        icon: <FaLock className="text-red-500" />
      }
    ]
  };

  // Get current user
  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
    };
    getUser();
  }, []);

  // Start timer when simulation begins
  useEffect(() => {
    if (step > 0 && !startTime) {
      setStartTime(Date.now());
      setIsTimerRunning(true);
    }
  }, [step, startTime]);

  // Timer logic
  useEffect(() => {
    let interval;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, startTime]);

  // Handle scan type selection
  const handleScanTypeSelect = (scanType) => {
    setSelectedScanType(scanType);
    if (scanType.id === "ping") {
      // Simulate ping sweep results
      setTimeout(() => {
        setScanResults([
          { host: "***********", status: "up", role: "Gateway" },
          { host: "************", status: "up", role: "Workstation" },
          { host: "************", status: "up", role: "Workstation" },
          { host: "************", status: "up", role: "Server" },
          { host: "************0", status: "up", role: "Workstation" },
          { host: "*************", status: "up", role: "Printer" }
        ]);
        setStep(1);
      }, 1500);
    }
  };

  // Handle target host submission
  const handleTargetSubmit = (e) => {
    e.preventDefault();
    if (targetInput === scenario.vulnerableHost) {
      setIsCorrect(true);
      // Simulate port scan results
      setTimeout(() => {
        setScanResults([
          { port: 22, state: "open", service: "SSH" },
          { port: 80, state: "open", service: "HTTP" },
          { port: 443, state: "open", service: "HTTPS" },
          { port: 3306, state: "open", service: "MySQL" }
        ]);
        setStep(2);
      }, 1500);
    } else {
      setIsCorrect(false);
      setAttempts(attempts + 1);
    }
  };

  // Handle vulnerable port submission
  const handlePortSubmit = (e) => {
    e.preventDefault();
    const ports = portInput.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p));
    const allCorrect = scenario.vulnerablePorts.every(p => ports.includes(p));
    const allValid = ports.every(p => scenario.vulnerablePorts.includes(p));

    if (allCorrect && allValid) {
      setIsCorrect(true);
      // Simulate vulnerability scan results
      setTimeout(() => {
        setVulnerableServices(scenario.vulnerableServices);
        setIsTimerRunning(false);
        setIsCompleted(true);
        setShowCongratulations(true);

        // Record completion in database
        if (user) {
          recordCompletion();
        }

        // Award XP if available
        if (typeof awardXP === 'function') {
          awardXP('simulation_completed', {
            simulationId,
            timeTaken: elapsedTime,
            attempts
          });
        }

        // Call onComplete callback
        if (onComplete) {
          onComplete({
            success: true,
            timeTaken: elapsedTime,
            attempts
          });
        }
      }, 1500);
    } else {
      setIsCorrect(false);
      setAttempts(attempts + 1);
    }
  };

  // Record completion in database
  const recordCompletion = async () => {
    try {
      const { data, error } = await supabase
        .from('practice_simulation_attempts')
        .upsert({
          user_id: user.id,
          simulation_id: simulationId || 'network-scanning-basics',
          is_completed: true,
          completion_time: elapsedTime,
          approach_score: Math.max(100 - (attempts * 5), 50),
          total_score: Math.max(100 - (attempts * 5), 50),
          started_at: new Date(startTime).toISOString(),
          completed_at: new Date().toISOString()
        }, { onConflict: 'user_id, simulation_id' });

      if (error) {
        console.error('Error recording completion:', error);
      }
    } catch (error) {
      console.error('Error recording completion:', error);
    }
  };

  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Render the appropriate step
  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-bold">Step 1: Choose a Network Scanning Technique</h2>
            <p>Select a technique to begin reconnaissance on the target network ({scenario.targetNetwork}):</p>
            <div className="grid grid-cols-1 gap-4 mt-4">
              {scenario.scanTypes.map((scanType) => (
                <button
                  key={scanType.id}
                  onClick={() => handleScanTypeSelect(scanType)}
                  className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-white hover:bg-gray-100'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} text-left flex items-start`}
                >
                  <div className="mr-4 mt-1">{scanType.icon}</div>
                  <div>
                    <h3 className="font-bold">{scanType.name}</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{scanType.description}</p>
                    <div className="flex mt-2 text-xs">
                      <span className={`mr-3 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Purpose: {scanType.effectiveness}</span>
                      <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Speed: {scanType.speed}</span>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        );
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-bold">Step 2: Analyze Ping Sweep Results</h2>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <div className="flex items-center mb-2">
                <FaTerminal className="mr-2" />
                <span className="font-bold">Terminal</span>
              </div>
              <div className="font-mono text-sm">
                <p>$ Running ping sweep on {scenario.targetNetwork}...</p>
                <p>$ Discovered hosts:</p>
                {scanResults.map((result, index) => (
                  <p key={index}>$ {result.host} is {result.status} ({result.role})</p>
                ))}
              </div>
            </div>
            <p>Based on the ping sweep results, which host would you like to investigate further?</p>
            <form onSubmit={handleTargetSubmit} className="mt-4">
              <div className="flex">
                <input
                  type="text"
                  value={targetInput}
                  onChange={(e) => setTargetInput(e.target.value)}
                  placeholder="Enter IP address (e.g., 192.168.1.x)"
                  className={`flex-grow p-2 rounded-l-lg ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                />
                <button
                  type="submit"
                  className={`px-4 py-2 rounded-r-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                >
                  Scan
                </button>
              </div>
            </form>
            {attempts > 0 && !isCorrect && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Try looking for a server that might have valuable services.
              </div>
            )}
            {attempts >= 2 && !isCorrect && (
              <button
                onClick={() => setShowHint(true)}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaInfoCircle className="mr-2" /> {scenario.hints[0]}
              </div>
            )}
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-100'} text-green-500 flex items-center`}>
              <FaCheck className="mr-2" /> Port scan completed on {scenario.vulnerableHost}
            </div>
            <h2 className="text-xl font-bold">Step 3: Identify Vulnerable Ports</h2>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <div className="flex items-center mb-2">
                <FaTerminal className="mr-2" />
                <span className="font-bold">Terminal</span>
              </div>
              <div className="font-mono text-sm">
                <p>$ Running port scan on {scenario.vulnerableHost}...</p>
                <p>$ Discovered open ports:</p>
                {scanResults.map((result, index) => (
                  <p key={index}>$ Port {result.port}/{result.state}: {result.service}</p>
                ))}
              </div>
            </div>
            <p>Based on the port scan results, which ports would you investigate for vulnerabilities? (Enter comma-separated list)</p>
            <form onSubmit={handlePortSubmit} className="mt-4">
              <div className="flex">
                <input
                  type="text"
                  value={portInput}
                  onChange={(e) => setPortInput(e.target.value)}
                  placeholder="Enter ports (e.g., 22,80,443)"
                  className={`flex-grow p-2 rounded-l-lg ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                />
                <button
                  type="submit"
                  className={`px-4 py-2 rounded-r-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                >
                  Scan
                </button>
              </div>
            </form>
            {attempts > 0 && !isCorrect && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Make sure to include all potentially vulnerable ports.
              </div>
            )}
            {attempts >= 2 && !isCorrect && (
              <button
                onClick={() => setShowHint(true)}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaInfoCircle className="mr-2" /> {scenario.hints[1]}
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  // Render congratulations screen
  const renderCongratulations = () => {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} text-center`}
      >
        <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center">
          <FaUnlock className="text-4xl text-green-500" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Network Scan Completed Successfully!</h2>
        <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          You've successfully identified vulnerable services on the target network.
        </p>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Time Taken</p>
            <p className="text-xl font-bold">{formatTime(elapsedTime)}</p>
          </div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Attempts</p>
            <p className="text-xl font-bold">{attempts + 1}</p>
          </div>
        </div>

        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <h3 className="font-bold text-lg mb-2">Vulnerability Scan Results</h3>
          <div className="space-y-3">
            {vulnerableServices.map((service, index) => (
              <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500`}>
                <div className="flex items-center">
                  <FaExclamationTriangle className="mr-2" />
                  <span className="font-bold">{service.service} (Port {service.port})</span>
                </div>
                <p className="mt-1">Version: {service.version}</p>
                <p className="mt-1">Vulnerability: {service.vulnerability}</p>
              </div>
            ))}
          </div>
        </div>

        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'}`}>
          <h3 className="font-bold text-lg mb-2">What You've Learned</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>How to perform network reconnaissance using ping sweeps</li>
            <li>How to identify open ports on target systems</li>
            <li>How to detect potentially vulnerable services</li>
            <li>The importance of keeping services updated to prevent exploitation</li>
          </ul>
        </div>
        <button
          onClick={() => window.location.href = '/dashboard/start-hack'}
          className={`px-6 py-3 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
        >
          Return to Simulations
        </button>
      </motion.div>
    );
  };

  return (
    <div className={`max-w-4xl mx-auto ${darkMode ? 'text-white' : 'text-gray-900'}`}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">{scenario.title}</h1>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{scenario.description}</p>
      </div>

      {/* Progress and Timer */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
            {step + 1}
          </div>
          <div className={`h-1 w-16 mx-2 ${step >= 1 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
          <div className={`w-8 h-8 rounded-full ${step >= 1 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
            2
          </div>
          <div className={`h-1 w-16 mx-2 ${step >= 2 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
          <div className={`w-8 h-8 rounded-full ${step >= 2 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
            3
          </div>
        </div>
        <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
          <FaClock className="mr-2" />
          <span>{formatTime(elapsedTime)}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}>
        {showCongratulations ? renderCongratulations() : renderStep()}
      </div>
    </div>
  );
};

export default NetworkScanningSimulator;
