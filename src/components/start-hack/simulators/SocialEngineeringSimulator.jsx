import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaUserSecret, FaEnvelope, FaPhone, FaIdCard, FaCheck, FaTimes, FaInfoCircle, FaExclamationTriangle, FaLock, FaUnlock, FaClock } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { useXP } from '../../../contexts/XPContext';
import { supabase } from '../../../lib/supabase';

const SocialEngineeringSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  const { awardXP } = useXP();
  const [step, setStep] = useState(0);
  const [selectedTechnique, setSelectedTechnique] = useState(null);
  const [selectedTargets, setSelectedTargets] = useState([]);
  const [selectedInfo, setSelectedInfo] = useState([]);
  const [isCorrect, setIsCorrect] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [user, setUser] = useState(null);

  // Social engineering scenario data
  const scenario = {
    title: "Social Engineering Simulation",
    description: "In this simulation, you'll learn how attackers use social engineering techniques to manipulate users into revealing sensitive information.",
    targetCompany: "TechCorp Industries",
    targetDepartment: "Finance Department",
    goal: "Gain access to financial records",
    hints: [
      "Focus on employees with access to sensitive information",
      "Look for information that could help bypass security measures",
      "Consider which technique would be most effective for the target"
    ],
    techniques: [
      {
        id: "phishing",
        name: "Phishing",
        description: "Create deceptive emails that appear to be from legitimate sources to trick users into revealing information",
        effectiveness: "High for mass targeting",
        difficulty: "Medium",
        icon: <FaEnvelope className="text-blue-500" />
      },
      {
        id: "pretexting",
        name: "Pretexting",
        description: "Create a fabricated scenario to engage a victim and gain their trust to extract information",
        effectiveness: "High for specific targets",
        difficulty: "High",
        icon: <FaPhone className="text-green-500" />
      },
      {
        id: "impersonation",
        name: "Impersonation",
        description: "Pose as someone with authority or right to know the information",
        effectiveness: "Very high for specific targets",
        difficulty: "Very high",
        icon: <FaIdCard className="text-red-500" />
      }
    ],
    employees: [
      { id: 1, name: "John Smith", position: "IT Administrator", access: "System admin access", vulnerability: "Helpful to colleagues" },
      { id: 2, name: "Sarah Johnson", position: "Finance Director", access: "Financial records", vulnerability: "Very busy, delegates tasks" },
      { id: 3, name: "Mike Williams", position: "Receptionist", access: "Visitor logs, basic company info", vulnerability: "New employee, eager to please" },
      { id: 4, name: "Lisa Chen", position: "HR Manager", access: "Employee records", vulnerability: "Trusting personality" }
    ],
    informationTypes: [
      { id: 1, name: "Login credentials", value: "High", difficulty: "High" },
      { id: 2, name: "Employee ID numbers", value: "Medium", difficulty: "Medium" },
      { id: 3, name: "Organization structure", value: "Low", difficulty: "Low" },
      { id: 4, name: "Financial system details", value: "High", difficulty: "High" },
      { id: 5, name: "Personal information", value: "Medium", difficulty: "Medium" }
    ]
  };

  // Get current user
  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
    };
    getUser();
  }, []);

  // Start timer when simulation begins
  useEffect(() => {
    if (step > 0 && !startTime) {
      setStartTime(Date.now());
      setIsTimerRunning(true);
    }
  }, [step, startTime]);

  // Timer logic
  useEffect(() => {
    let interval;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, startTime]);

  // Handle technique selection
  const handleTechniqueSelect = (technique) => {
    setSelectedTechnique(technique);
    setStep(1);
  };

  // Handle target selection
  const handleTargetSelect = (employee) => {
    const isSelected = selectedTargets.some(target => target.id === employee.id);
    if (isSelected) {
      setSelectedTargets(selectedTargets.filter(target => target.id !== employee.id));
    } else {
      setSelectedTargets([...selectedTargets, employee]);
    }
  };

  // Handle target submission
  const handleTargetSubmit = () => {
    // Check if the selected targets are appropriate for the goal
    const hasFinanceDirector = selectedTargets.some(target => target.position === "Finance Director");
    const hasITAdmin = selectedTargets.some(target => target.position === "IT Administrator");

    if ((hasFinanceDirector || hasITAdmin) && selectedTargets.length <= 2) {
      setIsCorrect(true);
      setStep(2);
    } else {
      setIsCorrect(false);
      setAttempts(attempts + 1);
    }
  };

  // Handle information selection
  const handleInfoSelect = (info) => {
    const isSelected = selectedInfo.some(i => i.id === info.id);
    if (isSelected) {
      setSelectedInfo(selectedInfo.filter(i => i.id !== info.id));
    } else {
      setSelectedInfo([...selectedInfo, info]);
    }
  };

  // Handle information submission
  const handleInfoSubmit = () => {
    // Check if the selected information types are appropriate for the goal
    const hasLoginCredentials = selectedInfo.some(info => info.name === "Login credentials");
    const hasFinancialSystemDetails = selectedInfo.some(info => info.name === "Financial system details");

    if (hasLoginCredentials && hasFinancialSystemDetails && selectedInfo.length <= 3) {
      setIsCorrect(true);
      setIsTimerRunning(false);
      setIsCompleted(true);
      setShowCongratulations(true);

      // Record completion in database
      if (user) {
        recordCompletion();
      }

      // Award XP if available
      if (typeof awardXP === 'function') {
        awardXP('simulation_completed', {
          simulationId,
          timeTaken: elapsedTime,
          attempts
        });
      }

      // Call onComplete callback
      if (onComplete) {
        onComplete({
          success: true,
          timeTaken: elapsedTime,
          attempts
        });
      }
    } else {
      setIsCorrect(false);
      setAttempts(attempts + 1);
    }
  };

  // Record completion in database
  const recordCompletion = async () => {
    try {
      const { data, error } = await supabase
        .from('practice_simulation_attempts')
        .upsert({
          user_id: user.id,
          simulation_id: simulationId || 'social-engineering-simulation',
          is_completed: true,
          completion_time: elapsedTime,
          approach_score: Math.max(100 - (attempts * 5), 50),
          total_score: Math.max(100 - (attempts * 5), 50),
          started_at: new Date(startTime).toISOString(),
          completed_at: new Date().toISOString()
        }, { onConflict: 'user_id, simulation_id' });

      if (error) {
        console.error('Error recording completion:', error);
      }
    } catch (error) {
      console.error('Error recording completion:', error);
    }
  };

  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Render the appropriate step
  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-bold">Step 1: Choose a Social Engineering Technique</h2>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
              <h3 className="font-bold mb-2">Scenario</h3>
              <p>Target: {scenario.targetCompany}, {scenario.targetDepartment}</p>
              <p>Goal: {scenario.goal}</p>
            </div>
            <p>Select a technique to begin your social engineering attack:</p>
            <div className="grid grid-cols-1 gap-4 mt-4">
              {scenario.techniques.map((technique) => (
                <button
                  key={technique.id}
                  onClick={() => handleTechniqueSelect(technique)}
                  className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-white hover:bg-gray-100'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} text-left flex items-start`}
                >
                  <div className="mr-4 mt-1">{technique.icon}</div>
                  <div>
                    <h3 className="font-bold">{technique.name}</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{technique.description}</p>
                    <div className="flex mt-2 text-xs">
                      <span className={`mr-3 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Effectiveness: {technique.effectiveness}</span>
                      <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Difficulty: {technique.difficulty}</span>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        );
      case 1:
        return (
          <div className="space-y-6">
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-100'} text-green-500 flex items-center`}>
              <FaCheck className="mr-2" /> You've selected {selectedTechnique.name} as your technique.
            </div>
            <h2 className="text-xl font-bold">Step 2: Select Your Targets</h2>
            <p>Choose which employees to target with your {selectedTechnique.name} attack (select 1-2):</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              {scenario.employees.map((employee) => {
                const isSelected = selectedTargets.some(target => target.id === employee.id);
                return (
                  <button
                    key={employee.id}
                    onClick={() => handleTargetSelect(employee)}
                    className={`p-4 rounded-lg border text-left ${
                      isSelected
                        ? darkMode
                          ? 'bg-blue-900/30 border-blue-700'
                          : 'bg-blue-100 border-blue-300'
                        : darkMode
                          ? 'bg-gray-800 border-gray-700 hover:bg-gray-700'
                          : 'bg-white border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex justify-between">
                      <h3 className="font-bold">{employee.name}</h3>
                      {isSelected && <FaCheck className="text-green-500" />}
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Position: {employee.position}</p>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Access: {employee.access}</p>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Vulnerability: {employee.vulnerability}</p>
                  </button>
                );
              })}
            </div>
            <div className="flex justify-end mt-4">
              <button
                onClick={handleTargetSubmit}
                disabled={selectedTargets.length === 0}
                className={`px-4 py-2 rounded-lg ${
                  selectedTargets.length === 0
                    ? `${darkMode ? 'bg-gray-700' : 'bg-gray-300'} cursor-not-allowed`
                    : `${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`
                }`}
              >
                Continue
              </button>
            </div>
            {attempts > 0 && !isCorrect && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Reconsider your targets. Think about who has access to the information you need.
              </div>
            )}
            {attempts >= 2 && !isCorrect && (
              <button
                onClick={() => setShowHint(true)}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaInfoCircle className="mr-2" /> {scenario.hints[0]}
              </div>
            )}
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-100'} text-green-500 flex items-center`}>
              <FaCheck className="mr-2" /> You've selected appropriate targets for your attack.
            </div>
            <h2 className="text-xl font-bold">Step 3: Select Information to Target</h2>
            <p>What information will you try to obtain through your {selectedTechnique.name} attack? (select 2-3)</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              {scenario.informationTypes.map((info) => {
                const isSelected = selectedInfo.some(i => i.id === info.id);
                return (
                  <button
                    key={info.id}
                    onClick={() => handleInfoSelect(info)}
                    className={`p-4 rounded-lg border text-left ${
                      isSelected
                        ? darkMode
                          ? 'bg-blue-900/30 border-blue-700'
                          : 'bg-blue-100 border-blue-300'
                        : darkMode
                          ? 'bg-gray-800 border-gray-700 hover:bg-gray-700'
                          : 'bg-white border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex justify-between">
                      <h3 className="font-bold">{info.name}</h3>
                      {isSelected && <FaCheck className="text-green-500" />}
                    </div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Value: {info.value}</p>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Difficulty to obtain: {info.difficulty}</p>
                  </button>
                );
              })}
            </div>
            <div className="flex justify-end mt-4">
              <button
                onClick={handleInfoSubmit}
                disabled={selectedInfo.length === 0}
                className={`px-4 py-2 rounded-lg ${
                  selectedInfo.length === 0
                    ? `${darkMode ? 'bg-gray-700' : 'bg-gray-300'} cursor-not-allowed`
                    : `${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`
                }`}
              >
                Complete Attack
              </button>
            </div>
            {attempts > 0 && !isCorrect && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Reconsider what information you need to achieve your goal.
              </div>
            )}
            {attempts >= 2 && !isCorrect && (
              <button
                onClick={() => setShowHint(true)}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaInfoCircle className="mr-2" /> {scenario.hints[1]}
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  // Render congratulations screen
  const renderCongratulations = () => {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} text-center`}
      >
        <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center">
          <FaUnlock className="text-4xl text-green-500" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Social Engineering Attack Simulated Successfully!</h2>
        <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          You've successfully planned a social engineering attack to gain access to financial records.
        </p>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Time Taken</p>
            <p className="text-xl font-bold">{formatTime(elapsedTime)}</p>
          </div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Attempts</p>
            <p className="text-xl font-bold">{attempts + 1}</p>
          </div>
        </div>

        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500`}>
          <h3 className="font-bold text-lg mb-2">Attack Summary</h3>
          <p><strong>Technique:</strong> {selectedTechnique.name}</p>
          <p><strong>Targets:</strong> {selectedTargets.map(t => t.name).join(', ')}</p>
          <p><strong>Information Sought:</strong> {selectedInfo.map(i => i.name).join(', ')}</p>
          <div className="mt-3 p-3 bg-yellow-100 text-yellow-800 rounded-lg">
            <FaExclamationTriangle className="inline-block mr-2" />
            <span className="font-bold">Important Note:</span> This simulation is for educational purposes only. Actual social engineering attacks are illegal and unethical.
          </div>
        </div>

        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'}`}>
          <h3 className="font-bold text-lg mb-2">What You've Learned</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>Different social engineering techniques and their effectiveness</li>
            <li>How attackers select targets based on access and vulnerabilities</li>
            <li>What types of information attackers typically seek</li>
            <li>How to recognize and defend against social engineering attacks</li>
          </ul>
        </div>
        <button
          onClick={() => window.location.href = '/dashboard/start-hack'}
          className={`px-6 py-3 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
        >
          Return to Simulations
        </button>
      </motion.div>
    );
  };

  return (
    <div className={`max-w-4xl mx-auto ${darkMode ? 'text-white' : 'text-gray-900'}`}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">{scenario.title}</h1>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{scenario.description}</p>
      </div>

      {/* Progress and Timer */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
            {step + 1}
          </div>
          <div className={`h-1 w-16 mx-2 ${step >= 1 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
          <div className={`w-8 h-8 rounded-full ${step >= 1 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
            2
          </div>
          <div className={`h-1 w-16 mx-2 ${step >= 2 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
          <div className={`w-8 h-8 rounded-full ${step >= 2 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
            3
          </div>
        </div>
        <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
          <FaClock className="mr-2" />
          <span>{formatTime(elapsedTime)}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}>
        {showCongratulations ? renderCongratulations() : renderStep()}
      </div>
    </div>
  );
};

export default SocialEngineeringSimulator;
