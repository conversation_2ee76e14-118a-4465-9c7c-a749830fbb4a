import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaU<PERSON>lock, FaCheck, FaTimes, FaInfoCircle, FaExclamationTriangle, FaTerminal, FaList, FaRandom, FaBook, FaClock } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { useXP } from '../../../contexts/XPContext';
import { supabase } from '../../../lib/supabase';

const PasswordCrackingSimulator = ({ onComplete, simulationId }) => {
  const { darkMode } = useGlobalTheme();
  const { awardXP } = useXP();
  const [step, setStep] = useState(0);
  const [selectedTechnique, setSelectedTechnique] = useState(null);
  const [passwordInput, setPasswordInput] = useState('');
  const [hashType, setHashType] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [user, setUser] = useState(null);

  // Password cracking scenario data
  const scenario = {
    title: "Password Cracking Challenge",
    description: "In this simulation, you'll learn how to identify and crack different types of password hashes using various techniques.",
    targetHash: "$2a$10$N9qo8uLOickgx2ZMRZoMyeIjZAgcfl7p92ldGxad68LJZdL17lhWy",
    hashType: "bcrypt",
    correctPassword: "password123",
    hints: [
      "This is a common bcrypt hash format",
      "Try looking for common passwords in the dictionary",
      "The password contains both letters and numbers"
    ],
    techniques: [
      {
        id: "dictionary",
        name: "Dictionary Attack",
        description: "Uses a list of common words and passwords to try to crack the hash",
        effectiveness: "Medium",
        speed: "Fast",
        icon: <FaList className="text-blue-500" />
      },
      {
        id: "bruteforce",
        name: "Brute Force Attack",
        description: "Tries every possible combination of characters",
        effectiveness: "High",
        speed: "Very Slow",
        icon: <FaRandom className="text-red-500" />
      },
      {
        id: "rainbow",
        name: "Rainbow Table",
        description: "Uses precomputed tables to find passwords from hashes",
        effectiveness: "High for unsalted hashes",
        speed: "Very Fast",
        icon: <FaBook className="text-green-500" />
      }
    ]
  };

  // Get current user
  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
    };
    getUser();
  }, []);

  // Start timer when simulation begins
  useEffect(() => {
    if (step > 0 && !startTime) {
      setStartTime(Date.now());
      setIsTimerRunning(true);
    }
  }, [step, startTime]);

  // Timer logic
  useEffect(() => {
    let interval;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, startTime]);

  // Handle technique selection
  const handleTechniqueSelect = (technique) => {
    setSelectedTechnique(technique);
    setStep(2);
  };

  // Handle hash type identification
  const handleHashTypeSelect = (type) => {
    setHashType(type);
    if (type === scenario.hashType) {
      setIsCorrect(true);
      setStep(1);
    } else {
      setIsCorrect(false);
      setAttempts(attempts + 1);
    }
  };

  // Handle password submission
  const handlePasswordSubmit = (e) => {
    e.preventDefault();
    if (passwordInput.toLowerCase() === scenario.correctPassword.toLowerCase()) {
      setIsCorrect(true);
      setIsTimerRunning(false);
      setIsCompleted(true);
      setShowCongratulations(true);

      // Record completion in database
      if (user) {
        recordCompletion();
      }

      // Award XP if available
      if (typeof awardXP === 'function') {
        awardXP('simulation_completed', {
          simulationId,
          timeTaken: elapsedTime,
          attempts
        });
      }

      // Call onComplete callback
      if (onComplete) {
        onComplete({
          success: true,
          timeTaken: elapsedTime,
          attempts
        });
      }
    } else {
      setIsCorrect(false);
      setAttempts(attempts + 1);
    }
  };

  // Record completion in database
  const recordCompletion = async () => {
    try {
      const { data, error } = await supabase
        .from('practice_simulation_attempts')
        .upsert({
          user_id: user.id,
          simulation_id: simulationId || 'password-cracking-simulation',
          is_completed: true,
          completion_time: elapsedTime,
          approach_score: Math.max(100 - (attempts * 5), 50),
          total_score: Math.max(100 - (attempts * 5), 50),
          started_at: new Date(startTime).toISOString(),
          completed_at: new Date().toISOString()
        }, { onConflict: 'user_id, simulation_id' });

      if (error) {
        console.error('Error recording completion:', error);
      }
    } catch (error) {
      console.error('Error recording completion:', error);
    }
  };

  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Render the appropriate step
  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-bold">Step 1: Identify the Hash Type</h2>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <p className="font-mono text-sm break-all">{scenario.targetHash}</p>
            </div>
            <p>Examine the hash above and identify what type of hash it is:</p>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <button
                onClick={() => handleHashTypeSelect('md5')}
                className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
              >
                MD5
              </button>
              <button
                onClick={() => handleHashTypeSelect('sha1')}
                className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
              >
                SHA-1
              </button>
              <button
                onClick={() => handleHashTypeSelect('bcrypt')}
                className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
              >
                bcrypt
              </button>
              <button
                onClick={() => handleHashTypeSelect('sha256')}
                className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
              >
                SHA-256
              </button>
            </div>
            {attempts > 0 && !isCorrect && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Incorrect. Try again.
              </div>
            )}
            {attempts >= 2 && !isCorrect && (
              <button
                onClick={() => setShowHint(true)}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaInfoCircle className="mr-2" /> {scenario.hints[0]}
              </div>
            )}
          </div>
        );
      case 1:
        return (
          <div className="space-y-6">
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-100'} text-green-500 flex items-center`}>
              <FaCheck className="mr-2" /> Correct! This is a {scenario.hashType} hash.
            </div>
            <h2 className="text-xl font-bold">Step 2: Choose a Password Cracking Technique</h2>
            <p>Select a technique to attempt to crack the password:</p>
            <div className="grid grid-cols-1 gap-4 mt-4">
              {scenario.techniques.map((technique) => (
                <button
                  key={technique.id}
                  onClick={() => handleTechniqueSelect(technique)}
                  className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-white hover:bg-gray-100'} border ${darkMode ? 'border-gray-700' : 'border-gray-200'} text-left flex items-start`}
                >
                  <div className="mr-4 mt-1">{technique.icon}</div>
                  <div>
                    <h3 className="font-bold">{technique.name}</h3>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{technique.description}</p>
                    <div className="flex mt-2 text-xs">
                      <span className={`mr-3 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Effectiveness: {technique.effectiveness}</span>
                      <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Speed: {technique.speed}</span>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-bold">Step 3: Crack the Password</h2>
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
              <div className="flex items-center mb-2">
                <FaTerminal className="mr-2" />
                <span className="font-bold">Terminal</span>
              </div>
              <div className="font-mono text-sm">
                <p>$ Running {selectedTechnique.name}...</p>
                <p>$ Target hash: {scenario.targetHash}</p>
                <p>$ Analyzing hash structure...</p>
                <p>$ Identified as {scenario.hashType} hash</p>
                <p>$ Starting {selectedTechnique.id} attack...</p>
                <p>$ ...</p>
                <p>$ Enter the password you think matches this hash:</p>
              </div>
            </div>
            <form onSubmit={handlePasswordSubmit} className="mt-4">
              <div className="flex">
                <input
                  type="text"
                  value={passwordInput}
                  onChange={(e) => setPasswordInput(e.target.value)}
                  placeholder="Enter password"
                  className={`flex-grow p-2 rounded-l-lg ${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-gray-900'} border`}
                />
                <button
                  type="submit"
                  className={`px-4 py-2 rounded-r-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
                >
                  Submit
                </button>
              </div>
            </form>
            {attempts > 0 && !isCorrect && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/30' : 'bg-red-100'} text-red-500 flex items-center`}>
                <FaTimes className="mr-2" /> Incorrect password. Try again.
              </div>
            )}
            {attempts >= 2 && !isCorrect && (
              <button
                onClick={() => setShowHint(true)}
                className={`mt-2 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Need a hint?
              </button>
            )}
            {showHint && (
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'} text-blue-500 flex items-center`}>
                <FaInfoCircle className="mr-2" /> {scenario.hints[Math.min(attempts - 1, scenario.hints.length - 1)]}
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  // Render congratulations screen
  const renderCongratulations = () => {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} text-center`}
      >
        <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-green-500/20 flex items-center justify-center">
          <FaUnlock className="text-4xl text-green-500" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Password Cracked Successfully!</h2>
        <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          You've successfully identified the hash type and cracked the password.
        </p>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Time Taken</p>
            <p className="text-xl font-bold">{formatTime(elapsedTime)}</p>
          </div>
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <p className="text-sm text-gray-500">Attempts</p>
            <p className="text-xl font-bold">{attempts + 1}</p>
          </div>
        </div>
        <div className={`p-4 mb-6 rounded-lg text-left ${darkMode ? 'bg-blue-900/30' : 'bg-blue-100'}`}>
          <h3 className="font-bold text-lg mb-2">What You've Learned</h3>
          <ul className="list-disc pl-5 space-y-1">
            <li>How to identify different types of password hashes</li>
            <li>Different techniques for password cracking</li>
            <li>The importance of using strong, unique passwords</li>
            <li>How password hashing and salting works</li>
          </ul>
        </div>
        <button
          onClick={() => window.location.href = '/dashboard/start-hack'}
          className={`px-6 py-3 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
        >
          Return to Simulations
        </button>
      </motion.div>
    );
  };

  return (
    <div className={`max-w-4xl mx-auto ${darkMode ? 'text-white' : 'text-gray-900'}`}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">{scenario.title}</h1>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{scenario.description}</p>
      </div>

      {/* Progress and Timer */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
            {step + 1}
          </div>
          <div className={`h-1 w-16 mx-2 ${step >= 1 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
          <div className={`w-8 h-8 rounded-full ${step >= 1 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
            2
          </div>
          <div className={`h-1 w-16 mx-2 ${step >= 2 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
          <div className={`w-8 h-8 rounded-full ${step >= 2 ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'} flex items-center justify-center font-bold`}>
            3
          </div>
        </div>
        <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
          <FaClock className="mr-2" />
          <span>{formatTime(elapsedTime)}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}>
        {showCongratulations ? renderCongratulations() : renderStep()}
      </div>
    </div>
  );
};

export default PasswordCrackingSimulator;
