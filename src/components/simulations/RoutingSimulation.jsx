import React, { useState, useEffect } from 'react';
import { FaNetworkWired, FaServer, FaDesktop, FaExclamationTriangle, FaCheck, FaRedo, FaInfoCircle, FaRoute } from 'react-icons/fa';

/**
 * Routing Simulation Component
 * 
 * This component provides an interactive simulation of network routing concepts,
 * allowing users to visualize how packets travel through networks using different
 * routing protocols and configurations.
 */
const RoutingSimulation = ({ simulationType = 'static', onComplete }) => {
  // Simulation state
  const [networkTopology, setNetworkTopology] = useState([]);
  const [routers, setRouters] = useState([]);
  const [connections, setConnections] = useState([]);
  const [packets, setPackets] = useState([]);
  const [selectedRouter, setSelectedRouter] = useState(null);
  const [routingTable, setRoutingTable] = useState({});
  const [simulationStep, setSimulationStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [simulationSpeed, setSimulationSpeed] = useState(1000); // ms
  const [packetPath, setPacketPath] = useState([]);
  const [sourceNode, setSourceNode] = useState(null);
  const [destinationNode, setDestinationNode] = useState(null);
  const [simulationComplete, setSimulationComplete] = useState(false);
  const [showExplanation, setShowExplanation] = useState(true);
  
  // Initialize simulation based on type
  useEffect(() => {
    initializeSimulation(simulationType);
  }, [simulationType]);
  
  // Initialize the simulation with appropriate topology and routing tables
  const initializeSimulation = (type) => {
    let topology = [];
    let routerList = [];
    let connectionList = [];
    
    if (type === 'static') {
      // Simple network with static routes
      topology = [
        { id: 'router1', type: 'router', name: 'Router A', x: 200, y: 150 },
        { id: 'router2', type: 'router', name: 'Router B', x: 400, y: 150 },
        { id: 'router3', type: 'router', name: 'Router C', x: 600, y: 150 },
        { id: 'pc1', type: 'pc', name: 'PC 1', x: 100, y: 250, network: '***********/24', ip: '************' },
        { id: 'pc2', type: 'pc', name: 'PC 2', x: 700, y: 250, network: '***********/24', ip: '************' },
        { id: 'server1', type: 'server', name: 'Server', x: 400, y: 50, network: '***********/24', ip: '************' }
      ];
      
      routerList = topology.filter(node => node.type === 'router');
      
      connectionList = [
        { from: 'router1', to: 'router2', cost: 1, network: '10.0.0.0/30' },
        { from: 'router2', to: 'router3', cost: 1, network: '********/30' },
        { from: 'router1', to: 'pc1', cost: 1, network: '***********/24' },
        { from: 'router3', to: 'pc2', cost: 1, network: '***********/24' },
        { from: 'router2', to: 'server1', cost: 1, network: '***********/24' }
      ];
      
      // Initialize routing tables for static routing
      const routingTables = {
        router1: [
          { destination: '***********/24', nextHop: 'Directly Connected', interface: 'Gi0/0', metric: 0 },
          { destination: '10.0.0.0/30', nextHop: 'Directly Connected', interface: 'Gi0/1', metric: 0 },
          { destination: '***********/24', nextHop: '********', interface: 'Gi0/1', metric: 1 },
          { destination: '***********/24', nextHop: '********', interface: 'Gi0/1', metric: 2 },
          { destination: '********/30', nextHop: '********', interface: 'Gi0/1', metric: 1 }
        ],
        router2: [
          { destination: '10.0.0.0/30', nextHop: 'Directly Connected', interface: 'Gi0/0', metric: 0 },
          { destination: '********/30', nextHop: 'Directly Connected', interface: 'Gi0/1', metric: 0 },
          { destination: '***********/24', nextHop: 'Directly Connected', interface: 'Gi0/2', metric: 0 },
          { destination: '***********/24', nextHop: '10.0.0.1', interface: 'Gi0/0', metric: 1 },
          { destination: '***********/24', nextHop: '10.0.0.6', interface: 'Gi0/1', metric: 1 }
        ],
        router3: [
          { destination: '***********/24', nextHop: 'Directly Connected', interface: 'Gi0/0', metric: 0 },
          { destination: '********/30', nextHop: 'Directly Connected', interface: 'Gi0/1', metric: 0 },
          { destination: '***********/24', nextHop: '10.0.0.5', interface: 'Gi0/1', metric: 1 },
          { destination: '***********/24', nextHop: '10.0.0.5', interface: 'Gi0/1', metric: 2 },
          { destination: '10.0.0.0/30', nextHop: '10.0.0.5', interface: 'Gi0/1', metric: 1 }
        ]
      };
      
      setRoutingTable(routingTables);
      setSourceNode(topology.find(node => node.id === 'pc1'));
      setDestinationNode(topology.find(node => node.id === 'pc2'));
    } 
    else if (type === 'dynamic') {
      // More complex network for dynamic routing protocols
      topology = [
        { id: 'router1', type: 'router', name: 'Router A', x: 200, y: 150 },
        { id: 'router2', type: 'router', name: 'Router B', x: 400, y: 150 },
        { id: 'router3', type: 'router', name: 'Router C', x: 600, y: 150 },
        { id: 'router4', type: 'router', name: 'Router D', x: 400, y: 300 },
        { id: 'pc1', type: 'pc', name: 'PC 1', x: 100, y: 250, network: '***********/24', ip: '************' },
        { id: 'pc2', type: 'pc', name: 'PC 2', x: 700, y: 250, network: '***********/24', ip: '************' },
        { id: 'server1', type: 'server', name: 'Server', x: 400, y: 50, network: '***********/24', ip: '************' }
      ];
      
      routerList = topology.filter(node => node.type === 'router');
      
      connectionList = [
        { from: 'router1', to: 'router2', cost: 1, network: '10.0.0.0/30' },
        { from: 'router2', to: 'router3', cost: 1, network: '********/30' },
        { from: 'router1', to: 'router4', cost: 5, network: '********/30' },
        { from: 'router3', to: 'router4', cost: 5, network: '*********/30' },
        { from: 'router1', to: 'pc1', cost: 1, network: '***********/24' },
        { from: 'router3', to: 'pc2', cost: 1, network: '***********/24' },
        { from: 'router2', to: 'server1', cost: 1, network: '***********/24' }
      ];
      
      // Dynamic routing tables will be calculated during simulation
      const routingTables = {
        router1: [
          { destination: '***********/24', nextHop: 'Directly Connected', interface: 'Gi0/0', metric: 0 },
          { destination: '10.0.0.0/30', nextHop: 'Directly Connected', interface: 'Gi0/1', metric: 0 },
          { destination: '********/30', nextHop: 'Directly Connected', interface: 'Gi0/2', metric: 0 }
        ],
        router2: [
          { destination: '10.0.0.0/30', nextHop: 'Directly Connected', interface: 'Gi0/0', metric: 0 },
          { destination: '********/30', nextHop: 'Directly Connected', interface: 'Gi0/1', metric: 0 },
          { destination: '***********/24', nextHop: 'Directly Connected', interface: 'Gi0/2', metric: 0 }
        ],
        router3: [
          { destination: '***********/24', nextHop: 'Directly Connected', interface: 'Gi0/0', metric: 0 },
          { destination: '********/30', nextHop: 'Directly Connected', interface: 'Gi0/1', metric: 0 },
          { destination: '*********/30', nextHop: 'Directly Connected', interface: 'Gi0/2', metric: 0 }
        ],
        router4: [
          { destination: '********/30', nextHop: 'Directly Connected', interface: 'Gi0/0', metric: 0 },
          { destination: '*********/30', nextHop: 'Directly Connected', interface: 'Gi0/1', metric: 0 }
        ]
      };
      
      setRoutingTable(routingTables);
      setSourceNode(topology.find(node => node.id === 'pc1'));
      setDestinationNode(topology.find(node => node.id === 'pc2'));
    }
    
    setNetworkTopology(topology);
    setRouters(routerList);
    setConnections(connectionList);
    resetSimulation();
  };
  
  // Reset the simulation
  const resetSimulation = () => {
    setPackets([]);
    setPacketPath([]);
    setSimulationStep(0);
    setIsPlaying(false);
    setSimulationComplete(false);
  };
  
  // Start or pause the simulation
  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };
  
  // Handle router selection
  const handleRouterClick = (router) => {
    setSelectedRouter(router);
  };
  
  // Get the current router's routing table
  const getCurrentRoutingTable = () => {
    if (!selectedRouter) return [];
    return routingTable[selectedRouter.id] || [];
  };
  
  // Render device icon based on type
  const renderDeviceIcon = (type) => {
    switch (type) {
      case 'router':
        return <FaNetworkWired className="text-blue-400 text-xl" />;
      case 'server':
        return <FaServer className="text-yellow-400 text-xl" />;
      case 'pc':
        return <FaDesktop className="text-gray-400 text-xl" />;
      default:
        return <FaDesktop className="text-gray-400 text-xl" />;
    }
  };
  
  // Calculate the next step in the simulation
  useEffect(() => {
    if (!isPlaying) return;
    
    const timer = setTimeout(() => {
      if (simulationStep === 0) {
        // Initialize packet path based on routing tables
        const path = calculatePacketPath();
        setPacketPath(path);
        
        // Create initial packet
        setPackets([
          { id: 1, position: 0, pathIndex: 0 }
        ]);
        
        setSimulationStep(1);
      } else if (simulationStep < packetPath.length) {
        // Move packet along the path
        setPackets(prev => prev.map(packet => ({
          ...packet,
          pathIndex: simulationStep
        })));
        
        setSimulationStep(simulationStep + 1);
      } else {
        // Simulation complete
        setIsPlaying(false);
        setSimulationComplete(true);
        if (onComplete) {
          onComplete();
        }
      }
    }, simulationSpeed);
    
    return () => clearTimeout(timer);
  }, [isPlaying, simulationStep, packetPath.length, simulationSpeed]);
  
  // Calculate the path a packet would take from source to destination
  const calculatePacketPath = () => {
    if (!sourceNode || !destinationNode) return [];
    
    const path = [];
    let currentNode = sourceNode;
    path.push(currentNode);
    
    // Find the router connected to the source
    const sourceConnection = connections.find(conn => 
      conn.from === currentNode.id || conn.to === currentNode.id
    );
    
    if (!sourceConnection) return path;
    
    // Get the router ID
    const routerId = sourceConnection.from === currentNode.id 
      ? sourceConnection.to 
      : sourceConnection.from;
    
    currentNode = networkTopology.find(node => node.id === routerId);
    path.push(currentNode);
    
    // Follow the path through routers based on routing tables
    let destinationNetwork = destinationNode.network;
    let maxHops = 10; // Prevent infinite loops
    
    while (currentNode && currentNode.id !== destinationNode.id && maxHops > 0) {
      const routerTable = routingTable[currentNode.id] || [];
      
      // Find the route entry for the destination network
      const routeEntry = routerTable.find(entry => 
        entry.destination === destinationNetwork
      );
      
      if (!routeEntry || routeEntry.nextHop === 'Directly Connected') {
        // If directly connected to destination, add destination to path
        path.push(destinationNode);
        break;
      }
      
      // Find the connection to the next hop
      const nextConnection = connections.find(conn => 
        (conn.from === currentNode.id && getNodeByNetwork(conn.network, routeEntry.nextHop)) || 
        (conn.to === currentNode.id && getNodeByNetwork(conn.network, routeEntry.nextHop))
      );
      
      if (!nextConnection) break;
      
      // Get the next node
      const nextNodeId = nextConnection.from === currentNode.id 
        ? nextConnection.to 
        : nextConnection.from;
      
      currentNode = networkTopology.find(node => node.id === nextNodeId);
      path.push(currentNode);
      
      maxHops--;
    }
    
    return path;
  };
  
  // Helper to find a node by its network and IP
  const getNodeByNetwork = (network, ip) => {
    return networkTopology.find(node => 
      node.network === network || node.ip === ip
    );
  };
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">
          {simulationType === 'static' ? 'Static Routing Simulation' : 'Dynamic Routing Simulation'}
        </h3>
        
        <div className="flex space-x-2">
          <button
            onClick={togglePlayPause}
            className={`px-3 py-1 rounded text-sm ${isPlaying ? 'bg-gray-700 text-white' : 'bg-primary text-black'}`}
            disabled={simulationComplete}
          >
            {isPlaying ? 'Pause' : simulationComplete ? 'Completed' : 'Start'}
          </button>
          
          <button
            onClick={resetSimulation}
            className="px-3 py-1 rounded text-sm bg-[#1E293B] text-white"
          >
            <FaRedo className="inline mr-1" /> Reset
          </button>
          
          <button
            onClick={() => setShowExplanation(!showExplanation)}
            className={`px-3 py-1 rounded text-sm ${showExplanation ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            {showExplanation ? 'Hide Details' : 'Show Details'}
          </button>
        </div>
      </div>
      
      {/* Network Topology */}
      <div className="relative h-96 bg-[#1E293B] rounded-lg mb-4 overflow-hidden border border-gray-700">
        {/* Connections */}
        <svg className="absolute inset-0 w-full h-full">
          {connections.map((conn, index) => {
            const source = networkTopology.find(node => node.id === conn.from);
            const target = networkTopology.find(node => node.id === conn.to);
            
            if (!source || !target) return null;
            
            return (
              <g key={index}>
                <line
                  x1={source.x}
                  y1={source.y}
                  x2={target.x}
                  y2={target.y}
                  stroke="#4B5563"
                  strokeWidth="2"
                />
                <text
                  x={(source.x + target.x) / 2}
                  y={(source.y + target.y) / 2 - 10}
                  fill="#9CA3AF"
                  fontSize="10"
                  textAnchor="middle"
                >
                  {conn.network}
                </text>
                {conn.cost && (
                  <text
                    x={(source.x + target.x) / 2}
                    y={(source.y + target.y) / 2 + 10}
                    fill="#9CA3AF"
                    fontSize="10"
                    textAnchor="middle"
                  >
                    Cost: {conn.cost}
                  </text>
                )}
              </g>
            );
          })}
          
          {/* Packet Animation */}
          {packets.map((packet, index) => {
            if (packetPath.length < 2 || packet.pathIndex >= packetPath.length - 1) return null;
            
            const currentNode = packetPath[packet.pathIndex];
            const nextNode = packetPath[packet.pathIndex + 1];
            
            if (!currentNode || !nextNode) return null;
            
            // Calculate position along the path
            const progress = (simulationStep - packet.pathIndex) / 1;
            const x = currentNode.x + (nextNode.x - currentNode.x) * Math.min(progress, 1);
            const y = currentNode.y + (nextNode.y - currentNode.y) * Math.min(progress, 1);
            
            return (
              <circle
                key={packet.id}
                cx={x}
                cy={y}
                r="6"
                fill="#38BDF8"
              />
            );
          })}
        </svg>
        
        {/* Devices */}
        {networkTopology.map(node => (
          <div
            key={node.id}
            className={`absolute w-20 h-20 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center justify-center cursor-pointer transition-all ${
              selectedRouter?.id === node.id ? 'ring-2 ring-primary' : ''
            } ${
              sourceNode?.id === node.id ? 'ring-2 ring-green-500' : ''
            } ${
              destinationNode?.id === node.id ? 'ring-2 ring-blue-500' : ''
            }`}
            style={{ left: node.x, top: node.y }}
            onClick={() => node.type === 'router' && handleRouterClick(node)}
          >
            <div className="w-12 h-12 rounded-full flex items-center justify-center bg-[#0F172A]">
              {renderDeviceIcon(node.type)}
            </div>
            <div className="text-xs mt-1 text-center">
              <div className="font-medium">{node.name}</div>
              {node.ip && <div className="text-gray-400">{node.ip}</div>}
            </div>
          </div>
        ))}
      </div>
      
      {/* Routing Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        {/* Routing Table */}
        <div className="bg-[#1E293B] p-4 rounded-lg">
          <h4 className="font-bold mb-2 flex items-center">
            <FaRoute className="mr-2 text-primary" />
            {selectedRouter ? `${selectedRouter.name} Routing Table` : 'Select a Router to View Routing Table'}
          </h4>
          
          {selectedRouter ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-[#0F172A]">
                  <tr>
                    <th className="p-2 text-left">Destination</th>
                    <th className="p-2 text-left">Next Hop</th>
                    <th className="p-2 text-left">Interface</th>
                    <th className="p-2 text-left">Metric</th>
                  </tr>
                </thead>
                <tbody>
                  {getCurrentRoutingTable().map((route, index) => (
                    <tr key={index} className="border-t border-gray-700">
                      <td className="p-2">{route.destination}</td>
                      <td className="p-2">{route.nextHop}</td>
                      <td className="p-2">{route.interface}</td>
                      <td className="p-2">{route.metric}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-gray-400">Click on a router in the network diagram to view its routing table.</p>
          )}
        </div>
        
        {/* Simulation Details */}
        <div className="bg-[#1E293B] p-4 rounded-lg">
          <h4 className="font-bold mb-2 flex items-center">
            <FaInfoCircle className="mr-2 text-primary" />
            Simulation Details
          </h4>
          
          <div className="text-sm">
            <div className="grid grid-cols-2 gap-2 mb-2">
              <div className="text-gray-400">Source:</div>
              <div>{sourceNode?.name} ({sourceNode?.ip})</div>
              <div className="text-gray-400">Destination:</div>
              <div>{destinationNode?.name} ({destinationNode?.ip})</div>
              <div className="text-gray-400">Routing Type:</div>
              <div className="capitalize">{simulationType} Routing</div>
              <div className="text-gray-400">Status:</div>
              <div>
                {simulationComplete ? (
                  <span className="text-green-400 flex items-center">
                    <FaCheck className="mr-1" /> Complete
                  </span>
                ) : isPlaying ? (
                  <span className="text-blue-400">In Progress</span>
                ) : (
                  <span className="text-gray-400">Not Started</span>
                )}
              </div>
            </div>
            
            {packetPath.length > 0 && (
              <div>
                <div className="text-gray-400 mb-1">Packet Path:</div>
                <div className="bg-[#0F172A] p-2 rounded">
                  {packetPath.map((node, index) => (
                    <span key={index}>
                      {node.name}
                      {index < packetPath.length - 1 && <span className="mx-1">→</span>}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Explanation */}
      {showExplanation && (
        <div className="bg-[#1E293B] p-4 rounded-lg">
          <h4 className="font-bold mb-2">How {simulationType.charAt(0).toUpperCase() + simulationType.slice(1)} Routing Works</h4>
          
          {simulationType === 'static' ? (
            <div className="text-sm text-gray-300">
              <p className="mb-2">
                <strong>Static routing</strong> involves manually configuring routes in a router's routing table. The network administrator explicitly defines the path that packets should take to reach specific destinations.
              </p>
              <p className="mb-2">
                In this simulation, the routers have been configured with static routes that tell them exactly where to send packets for each destination network. When a packet is sent from the source to the destination, each router along the path consults its routing table to determine the next hop.
              </p>
              <p>
                <strong>Key characteristics of static routing:</strong>
              </p>
              <ul className="list-disc pl-5 mb-2">
                <li>Manually configured by network administrators</li>
                <li>No automatic adaptation to network changes</li>
                <li>More predictable and secure</li>
                <li>Less overhead (no routing protocol traffic)</li>
                <li>Suitable for small networks with simple topologies</li>
              </ul>
            </div>
          ) : (
            <div className="text-sm text-gray-300">
              <p className="mb-2">
                <strong>Dynamic routing</strong> uses routing protocols to automatically discover network destinations and adapt to changes in the network topology. Routers exchange routing information to build and maintain their routing tables.
              </p>
              <p className="mb-2">
                In this simulation, the routers use a dynamic routing protocol to learn about networks they're not directly connected to. They exchange routing information with their neighbors, allowing them to discover the best paths to each destination.
              </p>
              <p>
                <strong>Key characteristics of dynamic routing:</strong>
              </p>
              <ul className="list-disc pl-5 mb-2">
                <li>Automatically adapts to network changes</li>
                <li>Routers exchange routing information</li>
                <li>Can find alternate paths if links fail</li>
                <li>More overhead (protocol traffic, CPU, memory)</li>
                <li>Suitable for larger, more complex networks</li>
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RoutingSimulation;
