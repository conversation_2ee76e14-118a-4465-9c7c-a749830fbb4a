import React, { useState, useEffect } from 'react';
import { FaBug, FaSearch, FaMicroscope, FaShieldAlt, FaExclamationTriangle, FaDownload, FaCode } from 'react-icons/fa';

/**
 * Virtual Malware Analysis Laboratory
 * Safe environment for learning malware analysis techniques
 */
const MalwareAnalysisLab = ({ sampleType = 'trojan', onComplete }) => {
  const [analysisPhase, setAnalysisPhase] = useState('static'); // static, dynamic, behavioral
  const [selectedSample, setSelectedSample] = useState(null);
  const [analysisResults, setAnalysisResults] = useState({});
  const [activeTools, setActiveTools] = useState([]);
  const [findings, setFindings] = useState([]);

  // Safe malware samples for analysis
  const malwareSamples = {
    trojan: {
      name: 'Banking Trojan Sample',
      hash: 'a1b2c3d4e5f6789...',
      size: '2.4 MB',
      type: 'PE32',
      family: 'Emotet',
      description: 'Banking credential harvesting trojan',
      iocs: ['*************:8080', 'evil-bank.com', 'HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\\Update']
    },
    ransomware: {
      name: 'Ransomware Sample',
      hash: 'f7e8d9c0b1a2345...',
      size: '1.8 MB', 
      type: 'PE32',
      family: 'WannaCry',
      description: 'File encryption ransomware',
      iocs: ['*************:443', 'ransom-pay.onion', 'C:\\Windows\\System32\\ransom.exe']
    }
  };

  // Virtual analysis tools
  const analysisTools = {
    static: [
      { name: 'PEStudio', icon: '🔍', description: 'PE file analysis' },
      { name: 'Strings', icon: '📝', description: 'Extract readable strings' },
      { name: 'VirusTotal', icon: '🛡️', description: 'Multi-engine scanning' },
      { name: 'Yara', icon: '🎯', description: 'Pattern matching' }
    ],
    dynamic: [
      { name: 'Wireshark', icon: '🌐', description: 'Network traffic analysis' },
      { name: 'Process Monitor', icon: '⚙️', description: 'File/registry monitoring' },
      { name: 'VMWare', icon: '💻', description: 'Isolated execution environment' },
      { name: 'Cuckoo Sandbox', icon: '📦', description: 'Automated analysis' }
    ],
    behavioral: [
      { name: 'API Monitor', icon: '📊', description: 'API call monitoring' },
      { name: 'RegShot', icon: '📋', description: 'Registry comparison' },
      { name: 'TCPView', icon: '🔗', description: 'Network connections' },
      { name: 'Autoruns', icon: '🚀', description: 'Persistence mechanisms' }
    ]
  };

  // Start analysis process
  const startAnalysis = (tool) => {
    setActiveTools([...activeTools, tool]);
    
    // Simulate analysis results
    setTimeout(() => {
      const mockResults = generateMockResults(tool, selectedSample);
      setAnalysisResults(prev => ({ ...prev, [tool.name]: mockResults }));
      setFindings(prev => [...prev, ...mockResults.findings]);
    }, 2000);
  };

  // Generate mock analysis results
  const generateMockResults = (tool, sample) => {
    const results = {
      'PEStudio': {
        findings: [
          'Suspicious imports detected: CreateFileA, WriteFile',
          'No digital signature found',
          'High entropy sections indicate packing'
        ],
        details: 'PE analysis reveals potential malicious behavior'
      },
      'Strings': {
        findings: [
          'Embedded URLs: http://evil-c2.com/gate.php',
          'Registry keys: SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run',
          'Encrypted strings detected'
        ],
        details: 'String analysis reveals C2 infrastructure and persistence'
      },
      'Wireshark': {
        findings: [
          'DNS queries to suspicious domains',
          'HTTP POST to C2 server',
          'Encrypted data transmission detected'
        ],
        details: 'Network analysis shows malicious communication patterns'
      }
    };

    return results[tool.name] || { findings: ['Analysis in progress...'], details: 'Processing...' };
  };

  return (
    <div className="space-y-6">
      {/* Lab Header */}
      <div className="bg-gradient-to-r from-red-600 to-orange-600 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-2 flex items-center gap-2">
          <FaBug />
          Virtual Malware Analysis Laboratory
        </h2>
        <p className="text-red-100">Safe environment for malware analysis and reverse engineering</p>
      </div>

      {/* Sample Selection */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-bold mb-4">Select Malware Sample</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(malwareSamples).map(([key, sample]) => (
            <div 
              key={key}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedSample?.name === sample.name ? 'border-red-500 bg-red-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedSample(sample)}
            >
              <h4 className="font-bold text-red-800">{sample.name}</h4>
              <p className="text-sm text-gray-600 mb-2">{sample.family} - {sample.type}</p>
              <p className="text-xs text-gray-500">Hash: {sample.hash}</p>
              <p className="text-xs text-gray-500">Size: {sample.size}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Analysis Phase Selection */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-bold mb-4">Analysis Phase</h3>
        <div className="flex gap-4">
          {['static', 'dynamic', 'behavioral'].map(phase => (
            <button
              key={phase}
              onClick={() => setAnalysisPhase(phase)}
              className={`px-4 py-2 rounded-lg capitalize ${
                analysisPhase === phase ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
              }`}
            >
              {phase} Analysis
            </button>
          ))}
        </div>
      </div>

      {/* Analysis Tools */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-bold mb-4">Available Tools</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {analysisTools[analysisPhase].map((tool, index) => (
            <div 
              key={index}
              className="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
              onClick={() => startAnalysis(tool)}
            >
              <div className="text-2xl mb-2">{tool.icon}</div>
              <h4 className="font-medium text-sm">{tool.name}</h4>
              <p className="text-xs text-gray-600">{tool.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Analysis Results */}
      {Object.keys(analysisResults).length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-bold mb-4">Analysis Results</h3>
          <div className="space-y-4">
            {Object.entries(analysisResults).map(([tool, results]) => (
              <div key={tool} className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-bold text-blue-800">{tool}</h4>
                <p className="text-sm text-gray-600 mb-2">{results.details}</p>
                <ul className="text-sm space-y-1">
                  {results.findings.map((finding, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <FaExclamationTriangle className="text-orange-500 mt-0.5 text-xs" />
                      {finding}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* IOC Summary */}
      {selectedSample && (
        <div className="bg-red-50 rounded-lg border border-red-200 p-6">
          <h3 className="text-lg font-bold mb-4 text-red-800">Indicators of Compromise (IOCs)</h3>
          <div className="space-y-2">
            {selectedSample.iocs.map((ioc, index) => (
              <div key={index} className="flex items-center gap-2">
                <FaExclamationTriangle className="text-red-500" />
                <code className="bg-red-100 px-2 py-1 rounded text-sm">{ioc}</code>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MalwareAnalysisLab; 