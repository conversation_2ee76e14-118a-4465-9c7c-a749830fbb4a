import React, { useState } from 'react';
import { FaNetworkWired, FaCheck, FaTimes, FaInfoCircle, FaArrowRight } from 'react-icons/fa';

/**
 * Network Cabling Simulator Component
 *
 * This component provides an interactive simulation for learning about different
 * types of network cables, their specifications, and appropriate use cases.
 */
const NetworkCablingSimulator = ({ onComplete }) => {
  // Component state
  const [selectedCable, setSelectedCable] = useState(null);
  const [selectedScenario, setSelectedScenario] = useState(null);
  const [userSelections, setUserSelections] = useState({});
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);
  const [showExplanation, setShowExplanation] = useState(true);

  // Cable types data
  const cableTypes = [
    {
      id: 'cat5e',
      name: 'Cat5e',
      image: '🔌',
      color: 'bg-blue-500',
      maxSpeed: '1 Gbps',
      maxDistance: '100 meters',
      shielding: 'Unshielded',
      connectorType: 'RJ-45',
      description: 'Cat5e (Category 5 enhanced) is a common type of Ethernet cable that supports speeds up to 1 Gbps over distances up to 100 meters. It\'s suitable for most home and small business networks.',
      advantages: [
        'Affordable',
        'Widely available',
        'Sufficient for most home networks'
      ],
      limitations: [
        'Limited to 1 Gbps',
        'More susceptible to interference than higher categories',
        'Not future-proof for very high bandwidth needs'
      ]
    },
    {
      id: 'cat6',
      name: 'Cat6',
      image: '🔌',
      color: 'bg-green-500',
      maxSpeed: '10 Gbps (up to 55m) / 1 Gbps (up to 100m)',
      maxDistance: '100 meters (1 Gbps) / 55 meters (10 Gbps)',
      shielding: 'Unshielded or Shielded',
      connectorType: 'RJ-45',
      description: 'Cat6 (Category 6) cables offer improved performance over Cat5e, with better protection against crosstalk and system noise. They can support 10 Gbps speeds over shorter distances.',
      advantages: [
        'Higher bandwidth than Cat5e',
        'Better noise resistance',
        'Supports 10 Gbps over shorter distances'
      ],
      limitations: [
        'More expensive than Cat5e',
        'Limited to 55 meters for 10 Gbps',
        'Thicker and less flexible than Cat5e'
      ]
    },
    {
      id: 'cat6a',
      name: 'Cat6a',
      image: '🔌',
      color: 'bg-purple-500',
      maxSpeed: '10 Gbps',
      maxDistance: '100 meters',
      shielding: 'Shielded',
      connectorType: 'RJ-45',
      description: 'Cat6a (Category 6 augmented) cables support 10 Gbps speeds over the full 100-meter distance. They have improved shielding to reduce crosstalk and external interference.',
      advantages: [
        'Supports 10 Gbps up to 100 meters',
        'Excellent noise and crosstalk protection',
        'More future-proof than Cat5e/Cat6'
      ],
      limitations: [
        'More expensive than Cat6',
        'Thicker and less flexible',
        'May be overkill for basic home networks'
      ]
    },
    {
      id: 'fiber-mm',
      name: 'Multimode Fiber',
      image: '🔌',
      color: 'bg-orange-500',
      maxSpeed: '10-100 Gbps',
      maxDistance: 'Up to 2 kilometers',
      connectorType: 'LC, SC, ST, MTP/MPO',
      description: 'Multimode fiber optic cables use multiple light paths (modes) to transmit data. They\'re suitable for shorter distances within buildings or campuses.',
      advantages: [
        'Higher bandwidth than copper cables',
        'Immune to electromagnetic interference',
        'Lighter and thinner than copper cables',
        'No electrical hazards'
      ],
      limitations: [
        'More expensive than copper cables',
        'Limited distance compared to single-mode fiber',
        'Requires special equipment for installation and testing',
        'More fragile than copper cables'
      ]
    },
    {
      id: 'fiber-sm',
      name: 'Single-mode Fiber',
      image: '🔌',
      color: 'bg-yellow-500',
      maxSpeed: '10-100+ Gbps',
      maxDistance: 'Up to 100 kilometers',
      connectorType: 'LC, SC, ST, MTP/MPO',
      description: 'Single-mode fiber optic cables use a single light path to transmit data, allowing for much longer distances than multimode fiber. They\'re ideal for connecting buildings or cities.',
      advantages: [
        'Extremely long distance capability',
        'Highest bandwidth capacity',
        'Immune to electromagnetic interference',
        'Future-proof for bandwidth needs'
      ],
      limitations: [
        'More expensive than multimode fiber',
        'Requires more precise light sources (lasers)',
        'More expensive equipment needed',
        'Overkill for most local networks'
      ]
    },
    {
      id: 'coaxial',
      name: 'Coaxial Cable',
      image: '🔌',
      color: 'bg-gray-500',
      maxSpeed: 'Up to 1 Gbps',
      maxDistance: 'Up to 500 meters',
      connectorType: 'F-type, BNC',
      description: 'Coaxial cables have a central conductor surrounded by insulation and a conductive shield. They\'re commonly used for cable TV and some older network installations.',
      advantages: [
        'Good resistance to interference',
        'Can carry signals over longer distances than twisted pair',
        'Widely used in cable TV infrastructure'
      ],
      limitations: [
        'Bulkier and less flexible than twisted pair',
        'More expensive than twisted pair',
        'Less commonly used in modern networks',
        'Lower bandwidth than fiber optic'
      ]
    }
  ];

  // Scenarios for the simulation
  const scenarios = [
    {
      id: 'home-network',
      title: 'Home Network Setup',
      description: 'You need to connect devices within a small home (computers, smart TV, gaming console) to a router. The longest cable run is about 20 meters.',
      bestOption: 'cat5e',
      acceptableOptions: ['cat5e', 'cat6', 'cat6a'],
      explanation: 'For a typical home network, Cat5e is sufficient as it supports 1 Gbps speeds which is enough for most home internet connections. Cat6 or Cat6a would work too but might be unnecessary unless you have very high bandwidth requirements or plan to upgrade soon.'
    },
    {
      id: 'business-backbone',
      title: 'Business Network Backbone',
      description: 'You need to connect network switches between floors in a 10-story office building. The connections need to support high bandwidth for multiple users.',
      bestOption: 'fiber-mm',
      acceptableOptions: ['fiber-mm', 'fiber-sm', 'cat6a'],
      explanation: "Multimode fiber is ideal for this scenario as it provides high bandwidth over the required distance between floors. It is immune to electromagnetic interference which is common in buildings. Single-mode fiber would work but might be overkill. Cat6a could work for shorter runs but would not be ideal for the entire backbone."
    },
    {
      id: 'campus-connection',
      title: 'Campus Building Connection',
      description: 'You need to connect two buildings that are 500 meters apart on a college campus.',
      bestOption: 'fiber-sm',
      acceptableOptions: ['fiber-sm', 'fiber-mm'],
      explanation: "Single-mode fiber is the best choice for connecting buildings 500 meters apart. It easily handles this distance with high bandwidth. Multimode fiber could work but would be approaching its distance limitations. Copper cables (Cat5e/6/6a) are limited to 100 meters, making them unsuitable for this scenario."
    },
    {
      id: 'data-center',
      title: 'Data Center Rack Connections',
      description: 'You need to connect servers within the same rack in a data center. The connections are short (1-3 meters) but need to support 10 Gbps speeds.',
      bestOption: 'cat6a',
      acceptableOptions: ['cat6', 'cat6a', 'fiber-mm'],
      explanation: "For short connections within a rack that need 10 Gbps, Cat6a is a cost-effective solution. Cat6 would also work for these short distances. Fiber would be overkill for such short runs unless there are specific requirements for electrical isolation or extreme EMI concerns."
    },
    {
      id: 'industrial',
      title: 'Industrial Environment',
      description: 'You need to run network cables in a factory with heavy machinery and significant electromagnetic interference.',
      bestOption: 'fiber-mm',
      acceptableOptions: ['fiber-mm', 'fiber-sm'],
      explanation: "Fiber optic cables are immune to electromagnetic interference, making them ideal for industrial environments with heavy machinery. Either multimode or single-mode fiber would work well, with multimode being more cost-effective for typical factory distances."
    }
  ];

  // Handle cable selection
  const handleCableSelect = (cable) => {
    setSelectedCable(cable);
  };

  // Handle scenario selection
  const handleScenarioSelect = (scenario) => {
    setSelectedScenario(scenario);
  };

  // Handle user selection for a scenario
  const handleUserSelection = (scenarioId, cableId) => {
    setUserSelections({
      ...userSelections,
      [scenarioId]: cableId
    });
  };

  // Calculate score and show results
  const handleSubmit = () => {
    let correctAnswers = 0;

    scenarios.forEach(scenario => {
      const userSelection = userSelections[scenario.id];
      if (userSelection && scenario.acceptableOptions.includes(userSelection)) {
        correctAnswers++;
      }
    });

    setScore(correctAnswers);
    setShowResults(true);

    if (correctAnswers >= 4 && onComplete) {
      onComplete();
    }
  };

  // Reset the simulation
  const handleReset = () => {
    setUserSelections({});
    setShowResults(false);
    setScore(0);
  };

  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Network Cabling Simulator</h3>

        <div className="flex space-x-2">
          <button
            onClick={() => setShowExplanation(!showExplanation)}
            className={`px-3 py-1 rounded text-sm ${showExplanation ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            {showExplanation ? 'Hide Details' : 'Show Details'}
          </button>

          {showResults && (
            <button
              onClick={handleReset}
              className="px-3 py-1 rounded text-sm bg-[#1E293B] text-white"
            >
              Try Again
            </button>
          )}
        </div>
      </div>

      {showExplanation && (
        <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
          <h4 className="font-bold mb-2">About Network Cabling</h4>
          <p className="text-sm mb-3">
            Network cables are the physical medium that connect devices in a network. Different types of cables have different characteristics, including:
          </p>
          <ul className="list-disc pl-5 text-sm mb-3">
            <li><strong>Bandwidth/Speed:</strong> How much data can be transmitted per second</li>
            <li><strong>Distance:</strong> How far the signal can travel without degradation</li>
            <li><strong>Interference Resistance:</strong> How well the cable resists electromagnetic interference</li>
            <li><strong>Cost:</strong> Price per meter/foot</li>
          </ul>
          <p className="text-sm">
            Choosing the right cable for your network depends on your specific requirements, including distance, speed, environment, and budget.
          </p>
        </div>
      )}

      {/* Cable Types Section */}
      <div className="mb-4">
        <h4 className="font-bold mb-2">Cable Types</h4>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2">
          {cableTypes.map(cable => (
            <button
              key={cable.id}
              onClick={() => handleCableSelect(cable)}
              className={`p-2 rounded-lg flex flex-col items-center justify-center text-sm ${
                selectedCable?.id === cable.id ? 'ring-2 ring-primary' : ''
              } ${cable.color} bg-opacity-20 hover:bg-opacity-30`}
            >
              <span className="text-2xl mb-1">{cable.image}</span>
              <span>{cable.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Selected Cable Details */}
      {selectedCable && (
        <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
          <h4 className="font-bold mb-2">{selectedCable.name} Details</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <table className="w-full text-sm">
                <tbody>
                  <tr>
                    <td className="py-1 pr-2 text-gray-400">Max Speed:</td>
                    <td>{selectedCable.maxSpeed}</td>
                  </tr>
                  <tr>
                    <td className="py-1 pr-2 text-gray-400">Max Distance:</td>
                    <td>{selectedCable.maxDistance}</td>
                  </tr>
                  {selectedCable.shielding && (
                    <tr>
                      <td className="py-1 pr-2 text-gray-400">Shielding:</td>
                      <td>{selectedCable.shielding}</td>
                    </tr>
                  )}
                  <tr>
                    <td className="py-1 pr-2 text-gray-400">Connector Type:</td>
                    <td>{selectedCable.connectorType}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div>
              <p className="text-sm mb-2">{selectedCable.description}</p>
              <div className="flex space-x-4">
                <div>
                  <h5 className="text-xs font-medium text-green-400 mb-1">Advantages</h5>
                  <ul className="list-disc pl-4 text-xs">
                    {selectedCable.advantages.map((adv, index) => (
                      <li key={index}>{adv}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h5 className="text-xs font-medium text-red-400 mb-1">Limitations</h5>
                  <ul className="list-disc pl-4 text-xs">
                    {selectedCable.limitations.map((lim, index) => (
                      <li key={index}>{lim}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Scenarios Section */}
      <div className="mb-4">
        <h4 className="font-bold mb-2">Network Scenarios</h4>
        <p className="text-sm mb-3">
          Select the best cable type for each scenario below:
        </p>

        <div className="space-y-4">
          {scenarios.map(scenario => (
            <div
              key={scenario.id}
              className={`bg-[#1E293B] p-3 rounded-lg ${
                selectedScenario?.id === scenario.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => handleScenarioSelect(scenario)}
            >
              <div className="flex justify-between items-start mb-2">
                <h5 className="font-medium">{scenario.title}</h5>
                {showResults && (
                  <div>
                    {userSelections[scenario.id] && scenario.acceptableOptions.includes(userSelections[scenario.id]) ? (
                      <span className="text-green-500 flex items-center text-sm">
                        <FaCheck className="mr-1" /> Correct
                      </span>
                    ) : userSelections[scenario.id] ? (
                      <span className="text-red-500 flex items-center text-sm">
                        <FaTimes className="mr-1" /> Incorrect
                      </span>
                    ) : (
                      <span className="text-yellow-500 flex items-center text-sm">
                        <FaInfoCircle className="mr-1" /> Not answered
                      </span>
                    )}
                  </div>
                )}
              </div>

              <p className="text-sm mb-3">{scenario.description}</p>

              {showResults && (
                <div className="bg-[#0F172A] p-2 rounded text-sm mb-3">
                  <p className="font-medium text-primary">Explanation:</p>
                  <p>{scenario.explanation}</p>
                </div>
              )}

              <div className="flex flex-wrap gap-2">
                {cableTypes.map(cable => (
                  <button
                    key={`${scenario.id}-${cable.id}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (!showResults) {
                        handleUserSelection(scenario.id, cable.id);
                      }
                    }}
                    disabled={showResults}
                    className={`px-2 py-1 rounded text-xs ${
                      userSelections[scenario.id] === cable.id
                        ? `${cable.color} bg-opacity-50`
                        : `${cable.color} bg-opacity-20 hover:bg-opacity-30`
                    } ${
                      showResults && cable.id === scenario.bestOption
                        ? 'ring-2 ring-green-500'
                        : ''
                    }`}
                  >
                    {cable.name}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Submit Button */}
      {!showResults ? (
        <button
          onClick={handleSubmit}
          disabled={Object.keys(userSelections).length < scenarios.length}
          className={`px-4 py-2 rounded ${
            Object.keys(userSelections).length < scenarios.length
              ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
              : 'bg-primary text-black'
          }`}
        >
          Check Answers
        </button>
      ) : (
        <div className={`p-3 rounded-lg mb-3 ${
          score >= 4 ? 'bg-green-900/20 border border-green-500' : 'bg-blue-900/20 border border-blue-500'
        }`}>
          <p className="font-bold">
            Your Score: {score} out of {scenarios.length}
            {score >= 4 && ' - Great job!'}
          </p>
          <p className="mt-1">
            {score >= 4
              ? 'You have a good understanding of network cabling!'
              : 'Review the cable types and try again.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default NetworkCablingSimulator;
