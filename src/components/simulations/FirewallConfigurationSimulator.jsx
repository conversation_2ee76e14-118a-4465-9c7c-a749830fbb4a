import React, { useState } from 'react';
import { FaShieldAlt, FaCheck, FaTimes, FaInfoCircle, FaPlus, FaTrash } from 'react-icons/fa';

/**
 * Firewall Configuration Simulator Component
 * 
 * This component provides an interactive simulation for configuring
 * firewall rules and understanding network security concepts.
 */
const FirewallConfigurationSimulator = ({ onComplete }) => {
  // Component state
  const [rules, setRules] = useState([
    { id: 1, action: 'allow', source: '***********/24', destination: 'any', port: '80,443', protocol: 'tcp', description: 'Allow web browsing' },
    { id: 2, action: 'deny', source: 'any', destination: '************', port: '22', protocol: 'tcp', description: 'Block SSH to server' },
    { id: 3, action: 'allow', source: 'any', destination: '***********', port: '53', protocol: 'udp', description: 'Allow DNS queries' }
  ]);
  const [newRule, setNewRule] = useState({
    action: 'allow',
    source: '',
    destination: '',
    port: '',
    protocol: 'tcp',
    description: ''
  });
  const [testPacket, setTestPacket] = useState({
    source: '************0',
    destination: '*******',
    port: '443',
    protocol: 'tcp'
  });
  const [testResult, setTestResult] = useState(null);
  const [showExplanation, setShowExplanation] = useState(true);
  const [activeTab, setActiveTab] = useState('rules');
  const [errors, setErrors] = useState({});
  const [challengeMode, setChallengeMode] = useState(false);
  const [challenges, setChallenges] = useState([
    {
      id: 1,
      description: 'Configure the firewall to allow HTTP and HTTPS traffic from any source to your web server at ************',
      requirements: [
        { type: 'rule', action: 'allow', destination: '************', port: '80', protocol: 'tcp' },
        { type: 'rule', action: 'allow', destination: '************', port: '443', protocol: 'tcp' }
      ],
      completed: false
    },
    {
      id: 2,
      description: 'Block all telnet traffic (port 23) to your internal network (***********/24)',
      requirements: [
        { type: 'rule', action: 'deny', destination: '***********/24', port: '23', protocol: 'tcp' }
      ],
      completed: false
    },
    {
      id: 3,
      description: 'Allow DNS traffic (port 53 UDP) from your internal network to your DNS server at ***********',
      requirements: [
        { type: 'rule', action: 'allow', source: '***********/24', destination: '***********', port: '53', protocol: 'udp' }
      ],
      completed: false
    }
  ]);
  
  // Handle input change for new rule
  const handleNewRuleChange = (e) => {
    const { name, value } = e.target;
    setNewRule({
      ...newRule,
      [name]: value
    });
  };
  
  // Validate a new rule
  const validateRule = (rule) => {
    const newErrors = {};
    
    if (!rule.source) {
      newErrors.source = 'Source is required';
    }
    
    if (!rule.destination) {
      newErrors.destination = 'Destination is required';
    }
    
    if (!rule.port) {
      newErrors.port = 'Port is required';
    } else if (!/^(\d+(-\d+)?)(,\d+(-\d+)?)*$/.test(rule.port)) {
      newErrors.port = 'Invalid port format';
    }
    
    if (!rule.description) {
      newErrors.description = 'Description is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Add a new rule
  const addRule = () => {
    if (!validateRule(newRule)) {
      return;
    }
    
    const newRuleWithId = {
      ...newRule,
      id: rules.length > 0 ? Math.max(...rules.map(r => r.id)) + 1 : 1
    };
    
    setRules([...rules, newRuleWithId]);
    setNewRule({
      action: 'allow',
      source: '',
      destination: '',
      port: '',
      protocol: 'tcp',
      description: ''
    });
    setErrors({});
    
    // Check if any challenges are completed
    if (challengeMode) {
      checkChallengeCompletion();
    }
  };
  
  // Delete a rule
  const deleteRule = (id) => {
    setRules(rules.filter(rule => rule.id !== id));
    
    // Check if any challenges are now incomplete
    if (challengeMode) {
      checkChallengeCompletion();
    }
  };
  
  // Handle input change for test packet
  const handleTestPacketChange = (e) => {
    const { name, value } = e.target;
    setTestPacket({
      ...testPacket,
      [name]: value
    });
    setTestResult(null);
  };
  
  // Check if an IP address matches a network/IP pattern
  const ipMatches = (ip, pattern) => {
    if (pattern === 'any') return true;
    
    // Simple exact match
    if (pattern === ip) return true;
    
    // CIDR notation check (simplified)
    if (pattern.includes('/')) {
      const [network, bits] = pattern.split('/');
      const networkParts = network.split('.').map(Number);
      const ipParts = ip.split('.').map(Number);
      
      // Very simplified CIDR check - just checking if the first 2 or 3 octets match
      // depending on the subnet mask
      if (bits === '24') {
        return networkParts[0] === ipParts[0] && 
               networkParts[1] === ipParts[1] && 
               networkParts[2] === ipParts[2];
      } else if (bits === '16') {
        return networkParts[0] === ipParts[0] && 
               networkParts[1] === ipParts[1];
      } else if (bits === '8') {
        return networkParts[0] === ipParts[0];
      }
    }
    
    return false;
  };
  
  // Check if a port matches a port pattern
  const portMatches = (port, pattern) => {
    if (pattern === 'any') return true;
    
    const portNum = parseInt(port, 10);
    
    // Check for comma-separated list
    if (pattern.includes(',')) {
      const ports = pattern.split(',');
      return ports.some(p => portMatches(port, p));
    }
    
    // Check for range
    if (pattern.includes('-')) {
      const [start, end] = pattern.split('-').map(p => parseInt(p, 10));
      return portNum >= start && portNum <= end;
    }
    
    // Simple exact match
    return portNum === parseInt(pattern, 10);
  };
  
  // Test if a packet would be allowed or denied
  const testFirewall = () => {
    // Check each rule in order
    for (const rule of rules) {
      const sourceMatches = ipMatches(testPacket.source, rule.source);
      const destMatches = ipMatches(testPacket.destination, rule.destination);
      const portMatches = rule.port === 'any' || 
                          testPacket.port === 'any' || 
                          portMatches(testPacket.port, rule.port);
      const protocolMatches = rule.protocol === 'any' || 
                             testPacket.protocol === 'any' || 
                             rule.protocol === testPacket.protocol;
      
      if (sourceMatches && destMatches && portMatches && protocolMatches) {
        setTestResult({
          allowed: rule.action === 'allow',
          rule: rule
        });
        return;
      }
    }
    
    // If no rules match, default deny
    setTestResult({
      allowed: false,
      rule: null
    });
  };
  
  // Check if challenges are completed
  const checkChallengeCompletion = () => {
    const updatedChallenges = challenges.map(challenge => {
      // Check if all requirements are met
      const allRequirementsMet = challenge.requirements.every(req => {
        if (req.type === 'rule') {
          return rules.some(rule => {
            const actionMatch = rule.action === req.action;
            const sourceMatch = !req.source || rule.source === req.source || rule.source === 'any';
            const destMatch = !req.destination || rule.destination === req.destination || rule.destination === 'any';
            const portMatch = !req.port || rule.port.includes(req.port) || rule.port === 'any';
            const protocolMatch = !req.protocol || rule.protocol === req.protocol || rule.protocol === 'any';
            
            return actionMatch && sourceMatch && destMatch && portMatch && protocolMatch;
          });
        }
        return false;
      });
      
      return {
        ...challenge,
        completed: allRequirementsMet
      };
    });
    
    setChallenges(updatedChallenges);
    
    // Check if all challenges are completed
    if (updatedChallenges.every(c => c.completed) && onComplete) {
      onComplete();
    }
  };
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold flex items-center">
          <FaShieldAlt className="mr-2 text-primary" />
          Firewall Configuration Simulator
        </h3>
        
        <div className="flex space-x-2">
          <button
            onClick={() => setChallengeMode(!challengeMode)}
            className={`px-3 py-1 rounded text-sm ${challengeMode ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            {challengeMode ? 'Challenge Mode: ON' : 'Challenge Mode: OFF'}
          </button>
          
          <button
            onClick={() => setShowExplanation(!showExplanation)}
            className={`px-3 py-1 rounded text-sm ${showExplanation ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            {showExplanation ? 'Hide Details' : 'Show Details'}
          </button>
        </div>
      </div>
      
      {showExplanation && (
        <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
          <h4 className="font-bold mb-2">About Firewalls</h4>
          <p className="text-sm mb-3">
            A firewall is a network security device that monitors and filters incoming and outgoing network traffic based on predetermined security rules. It establishes a barrier between a trusted network and an untrusted network.
          </p>
          <p className="text-sm mb-3">
            Firewall rules determine whether to allow or block specific traffic based on:
          </p>
          <ul className="list-disc pl-5 text-sm mb-3">
            <li><strong>Source:</strong> Where the traffic is coming from (IP address)</li>
            <li><strong>Destination:</strong> Where the traffic is going to (IP address)</li>
            <li><strong>Port:</strong> Which service or application is being accessed</li>
            <li><strong>Protocol:</strong> The communication protocol being used (TCP, UDP, ICMP, etc.)</li>
          </ul>
          <p className="text-sm">
            Rules are processed in order from top to bottom, and the first matching rule determines whether the traffic is allowed or blocked.
          </p>
        </div>
      )}
      
      {/* Tabs */}
      <div className="flex border-b border-gray-700 mb-4">
        <button
          onClick={() => setActiveTab('rules')}
          className={`px-4 py-2 ${activeTab === 'rules' ? 'border-b-2 border-primary font-medium' : 'text-gray-400'}`}
        >
          Firewall Rules
        </button>
        <button
          onClick={() => setActiveTab('test')}
          className={`px-4 py-2 ${activeTab === 'test' ? 'border-b-2 border-primary font-medium' : 'text-gray-400'}`}
        >
          Test Firewall
        </button>
        {challengeMode && (
          <button
            onClick={() => setActiveTab('challenges')}
            className={`px-4 py-2 ${activeTab === 'challenges' ? 'border-b-2 border-primary font-medium' : 'text-gray-400'}`}
          >
            Challenges
          </button>
        )}
      </div>
      
      {/* Rules Tab */}
      {activeTab === 'rules' && (
        <div>
          <div className="bg-[#1E293B] p-4 rounded-lg mb-4 overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="bg-[#0F172A]">
                <tr>
                  <th className="p-2 text-left">Action</th>
                  <th className="p-2 text-left">Source</th>
                  <th className="p-2 text-left">Destination</th>
                  <th className="p-2 text-left">Port</th>
                  <th className="p-2 text-left">Protocol</th>
                  <th className="p-2 text-left">Description</th>
                  <th className="p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {rules.map(rule => (
                  <tr key={rule.id} className="border-t border-gray-700">
                    <td className="p-2">
                      <span className={`px-2 py-1 rounded text-xs ${
                        rule.action === 'allow' ? 'bg-green-900/30 text-green-400' : 'bg-red-900/30 text-red-400'
                      }`}>
                        {rule.action.toUpperCase()}
                      </span>
                    </td>
                    <td className="p-2">{rule.source}</td>
                    <td className="p-2">{rule.destination}</td>
                    <td className="p-2">{rule.port}</td>
                    <td className="p-2">{rule.protocol.toUpperCase()}</td>
                    <td className="p-2">{rule.description}</td>
                    <td className="p-2">
                      <button
                        onClick={() => deleteRule(rule.id)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
            <h4 className="font-bold mb-3">Add New Rule</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Action</label>
                <select
                  name="action"
                  value={newRule.action}
                  onChange={handleNewRuleChange}
                  className="w-full bg-[#0F172A] border border-gray-700 rounded p-2 text-sm"
                >
                  <option value="allow">Allow</option>
                  <option value="deny">Deny</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Source</label>
                <input
                  type="text"
                  name="source"
                  value={newRule.source}
                  onChange={handleNewRuleChange}
                  placeholder="IP, CIDR, or 'any'"
                  className={`w-full bg-[#0F172A] border ${errors.source ? 'border-red-500' : 'border-gray-700'} rounded p-2 text-sm`}
                />
                {errors.source && <p className="text-red-500 text-xs mt-1">{errors.source}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Destination</label>
                <input
                  type="text"
                  name="destination"
                  value={newRule.destination}
                  onChange={handleNewRuleChange}
                  placeholder="IP, CIDR, or 'any'"
                  className={`w-full bg-[#0F172A] border ${errors.destination ? 'border-red-500' : 'border-gray-700'} rounded p-2 text-sm`}
                />
                {errors.destination && <p className="text-red-500 text-xs mt-1">{errors.destination}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Port</label>
                <input
                  type="text"
                  name="port"
                  value={newRule.port}
                  onChange={handleNewRuleChange}
                  placeholder="Port, range (80-90), list (80,443), or 'any'"
                  className={`w-full bg-[#0F172A] border ${errors.port ? 'border-red-500' : 'border-gray-700'} rounded p-2 text-sm`}
                />
                {errors.port && <p className="text-red-500 text-xs mt-1">{errors.port}</p>}
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Protocol</label>
                <select
                  name="protocol"
                  value={newRule.protocol}
                  onChange={handleNewRuleChange}
                  className="w-full bg-[#0F172A] border border-gray-700 rounded p-2 text-sm"
                >
                  <option value="tcp">TCP</option>
                  <option value="udp">UDP</option>
                  <option value="icmp">ICMP</option>
                  <option value="any">Any</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Description</label>
                <input
                  type="text"
                  name="description"
                  value={newRule.description}
                  onChange={handleNewRuleChange}
                  placeholder="Rule description"
                  className={`w-full bg-[#0F172A] border ${errors.description ? 'border-red-500' : 'border-gray-700'} rounded p-2 text-sm`}
                />
                {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
              </div>
            </div>
            
            <button
              onClick={addRule}
              className="mt-4 px-4 py-2 bg-primary text-black rounded flex items-center"
            >
              <FaPlus className="mr-2" /> Add Rule
            </button>
          </div>
        </div>
      )}
      
      {/* Test Tab */}
      {activeTab === 'test' && (
        <div>
          <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
            <h4 className="font-bold mb-3">Test Packet</h4>
            <p className="text-sm mb-3">
              Configure a test packet to see if it would be allowed or blocked by your firewall rules.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Source IP</label>
                <input
                  type="text"
                  name="source"
                  value={testPacket.source}
                  onChange={handleTestPacketChange}
                  placeholder="Source IP address"
                  className="w-full bg-[#0F172A] border border-gray-700 rounded p-2 text-sm"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Destination IP</label>
                <input
                  type="text"
                  name="destination"
                  value={testPacket.destination}
                  onChange={handleTestPacketChange}
                  placeholder="Destination IP address"
                  className="w-full bg-[#0F172A] border border-gray-700 rounded p-2 text-sm"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Port</label>
                <input
                  type="text"
                  name="port"
                  value={testPacket.port}
                  onChange={handleTestPacketChange}
                  placeholder="Destination port"
                  className="w-full bg-[#0F172A] border border-gray-700 rounded p-2 text-sm"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Protocol</label>
                <select
                  name="protocol"
                  value={testPacket.protocol}
                  onChange={handleTestPacketChange}
                  className="w-full bg-[#0F172A] border border-gray-700 rounded p-2 text-sm"
                >
                  <option value="tcp">TCP</option>
                  <option value="udp">UDP</option>
                  <option value="icmp">ICMP</option>
                  <option value="any">Any</option>
                </select>
              </div>
            </div>
            
            <button
              onClick={testFirewall}
              className="mt-4 px-4 py-2 bg-primary text-black rounded"
            >
              Test Packet
            </button>
          </div>
          
          {testResult && (
            <div className={`bg-[#1E293B] p-4 rounded-lg mb-4 border-l-4 ${
              testResult.allowed ? 'border-green-500' : 'border-red-500'
            }`}>
              <h4 className="font-bold mb-2">Test Result</h4>
              
              <div className="flex items-center mb-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                  testResult.allowed ? 'bg-green-900/30 text-green-400' : 'bg-red-900/30 text-red-400'
                }`}>
                  {testResult.allowed ? <FaCheck /> : <FaTimes />}
                </div>
                <div>
                  <p className="font-medium">
                    {testResult.allowed ? 'Packet Allowed' : 'Packet Blocked'}
                  </p>
                  <p className="text-sm text-gray-400">
                    {testResult.rule 
                      ? `Matched rule: ${testResult.rule.description}`
                      : 'No matching rule found (default deny)'}
                  </p>
                </div>
              </div>
              
              <div className="bg-[#0F172A] p-3 rounded text-sm">
                <p className="mb-1">
                  <span className="text-gray-400">Source IP:</span> {testPacket.source}
                </p>
                <p className="mb-1">
                  <span className="text-gray-400">Destination IP:</span> {testPacket.destination}
                </p>
                <p className="mb-1">
                  <span className="text-gray-400">Port:</span> {testPacket.port}
                </p>
                <p>
                  <span className="text-gray-400">Protocol:</span> {testPacket.protocol.toUpperCase()}
                </p>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Challenges Tab */}
      {activeTab === 'challenges' && challengeMode && (
        <div>
          <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
            <h4 className="font-bold mb-3">Firewall Configuration Challenges</h4>
            <p className="text-sm mb-4">
              Complete these challenges by adding the appropriate firewall rules.
            </p>
            
            <div className="space-y-4">
              {challenges.map(challenge => (
                <div 
                  key={challenge.id}
                  className={`p-3 rounded-lg border ${
                    challenge.completed ? 'bg-green-900/10 border-green-500' : 'bg-[#0F172A] border-gray-700'
                  }`}
                >
                  <div className="flex items-start">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0 ${
                      challenge.completed ? 'bg-green-500 text-black' : 'bg-gray-700'
                    }`}>
                      {challenge.completed ? <FaCheck /> : challenge.id}
                    </div>
                    <div>
                      <p className="font-medium">{challenge.description}</p>
                      {challenge.completed && (
                        <p className="text-green-400 text-sm mt-1">Challenge completed!</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FirewallConfigurationSimulator;
