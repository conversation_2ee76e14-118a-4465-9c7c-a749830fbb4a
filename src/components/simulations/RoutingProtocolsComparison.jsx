import React, { useState } from 'react';
import { FaNetworkWired, FaExchangeAlt, FaGlobe, FaBuilding, FaCheck, FaTimes, FaInfoCircle } from 'react-icons/fa';

/**
 * Routing Protocols Comparison Component
 * 
 * This component provides an interactive comparison of different routing protocols,
 * allowing users to understand their characteristics, advantages, and use cases.
 */
const RoutingProtocolsComparison = ({ onComplete }) => {
  // Component state
  const [selectedProtocol, setSelectedProtocol] = useState('rip');
  const [showQuiz, setShowQuiz] = useState(false);
  const [quizAnswers, setQuizAnswers] = useState({});
  const [quizSubmitted, setQuizSubmitted] = useState(false);
  const [quizScore, setQuizScore] = useState(0);
  
  // Routing protocols data
  const protocols = {
    rip: {
      name: 'RIP (Routing Information Protocol)',
      type: 'Distance Vector',
      metric: 'Hop Count',
      adminDistance: '120',
      updateInterval: '30 seconds',
      maxHops: '15 hops',
      convergence: 'Slow',
      icon: <FaNetworkWired className="text-yellow-400" />,
      description: 'RIP is one of the oldest routing protocols. It uses hop count as its metric and has a maximum hop count of 15, which limits the size of networks it can support.',
      advantages: [
        'Simple to configure and understand',
        'Works well in small networks',
        'Supported by almost all routers'
      ],
      disadvantages: [
        'Limited to 15 hops (not suitable for large networks)',
        'Slow convergence time',
        'Periodic updates consume bandwidth',
        'Does not consider bandwidth or link quality in routing decisions'
      ],
      useCases: [
        'Small networks with simple topologies',
        'Networks where simplicity is more important than efficiency',
        'Legacy network environments'
      ],
      versions: ['RIPv1', 'RIPv2', 'RIPng (for IPv6)']
    },
    ospf: {
      name: 'OSPF (Open Shortest Path First)',
      type: 'Link State',
      metric: 'Cost (based on bandwidth)',
      adminDistance: '110',
      updateInterval: 'Only when changes occur',
      maxHops: 'No limit',
      convergence: 'Fast',
      icon: <FaExchangeAlt className="text-blue-400" />,
      description: 'OSPF is a link-state routing protocol that uses Dijkstra\'s algorithm to find the shortest path. It builds a complete map of the network topology and calculates the best routes based on link costs.',
      advantages: [
        'Fast convergence after network changes',
        'Supports large networks with hierarchical areas',
        'Uses bandwidth in metric calculation',
        'No hop count limitation',
        'Efficient use of bandwidth (updates only when changes occur)'
      ],
      disadvantages: [
        'More complex to configure and troubleshoot',
        'Requires more CPU and memory resources',
        'Can be challenging to design properly in large networks'
      ],
      useCases: [
        'Medium to large enterprise networks',
        'Networks requiring fast convergence',
        'Networks with complex topologies',
        'Networks with redundant paths'
      ],
      versions: ['OSPFv2 (for IPv4)', 'OSPFv3 (for IPv6)']
    },
    eigrp: {
      name: 'EIGRP (Enhanced Interior Gateway Routing Protocol)',
      type: 'Advanced Distance Vector',
      metric: 'Composite (bandwidth, delay, reliability, load)',
      adminDistance: '90 (internal), 170 (external)',
      updateInterval: 'Only when changes occur',
      maxHops: '224 by default (configurable)',
      convergence: 'Very Fast',
      icon: <FaNetworkWired className="text-green-400" />,
      description: 'EIGRP is an advanced distance-vector routing protocol developed by Cisco. It combines features of both distance-vector and link-state protocols, offering fast convergence and efficient operation.',
      advantages: [
        'Very fast convergence',
        'Efficient use of bandwidth (partial updates)',
        'Uses multiple metrics for best path selection',
        'Support for unequal-cost load balancing',
        'Supports large networks'
      ],
      disadvantages: [
        'Traditionally Cisco proprietary (now partially open)',
        'More complex than RIP',
        'Not as widely supported as OSPF or RIP'
      ],
      useCases: [
        'Cisco-based enterprise networks',
        'Networks requiring very fast convergence',
        'Networks with diverse link types and speeds'
      ],
      versions: ['EIGRP for IPv4', 'EIGRP for IPv6']
    },
    bgp: {
      name: 'BGP (Border Gateway Protocol)',
      type: 'Path Vector',
      metric: 'Path Attributes (multiple factors)',
      adminDistance: '20 (external), 200 (internal)',
      updateInterval: 'Only when changes occur',
      maxHops: 'No inherent limit',
      convergence: 'Slow (by design)',
      icon: <FaGlobe className="text-purple-400" />,
      description: 'BGP is the routing protocol of the internet. It\'s designed to exchange routing information between different autonomous systems (AS) and make policy-based routing decisions.',
      advantages: [
        'Highly scalable (runs the entire internet)',
        'Rich policy control over routing decisions',
        'Supports very large routing tables',
        'Stable and reliable for inter-domain routing'
      ],
      disadvantages: [
        'Complex to configure and manage',
        'Slow convergence by design',
        'Resource intensive',
        'Not suitable for most internal networks'
      ],
      useCases: [
        'Internet service providers',
        'Connections between different organizations',
        'Large enterprises with multiple connections to the internet',
        'Multi-homed networks'
      ],
      versions: ['BGP-4 (current version)', 'MP-BGP (Multiprotocol BGP)']
    },
    is_is: {
      name: 'IS-IS (Intermediate System to Intermediate System)',
      type: 'Link State',
      metric: 'Cost (configurable)',
      adminDistance: '115',
      updateInterval: 'Only when changes occur',
      maxHops: 'No limit',
      convergence: 'Fast',
      icon: <FaBuilding className="text-red-400" />,
      description: 'IS-IS is a link-state routing protocol similar to OSPF but operates at Layer 2 rather than Layer 3. It was originally designed for the OSI protocol stack but has been adapted for IP networks.',
      advantages: [
        'Fast convergence',
        'Efficient operation in large networks',
        'Less CPU intensive than OSPF',
        'Better scalability in very large networks',
        'Protocol-independent (can route non-IP protocols)'
      ],
      disadvantages: [
        'Less widely deployed than OSPF',
        'More complex to configure',
        'Fewer network engineers familiar with it'
      ],
      useCases: [
        'Service provider networks',
        'Very large enterprise networks',
        'Networks requiring support for non-IP protocols',
        'Networks where OSPF might have scaling issues'
      ],
      versions: ['IS-IS for IPv4', 'IS-IS for IPv6']
    }
  };
  
  // Quiz questions
  const quizQuestions = [
    {
      id: 'q1',
      question: 'Which routing protocol has a maximum hop count of 15?',
      options: ['OSPF', 'RIP', 'EIGRP', 'BGP'],
      correctAnswer: 'RIP'
    },
    {
      id: 'q2',
      question: 'Which routing protocol is primarily used for routing between different autonomous systems on the internet?',
      options: ['OSPF', 'RIP', 'EIGRP', 'BGP'],
      correctAnswer: 'BGP'
    },
    {
      id: 'q3',
      question: 'Which type of routing protocol builds a complete map of the network topology?',
      options: ['Distance Vector', 'Link State', 'Path Vector', 'Hybrid'],
      correctAnswer: 'Link State'
    },
    {
      id: 'q4',
      question: 'Which routing protocol was traditionally Cisco proprietary?',
      options: ['OSPF', 'RIP', 'EIGRP', 'IS-IS'],
      correctAnswer: 'EIGRP'
    },
    {
      id: 'q5',
      question: 'Which routing protocol would be most suitable for a small network with a simple topology?',
      options: ['OSPF', 'RIP', 'BGP', 'IS-IS'],
      correctAnswer: 'RIP'
    }
  ];
  
  // Handle protocol selection
  const handleProtocolSelect = (protocol) => {
    setSelectedProtocol(protocol);
  };
  
  // Handle quiz answer selection
  const handleAnswerSelect = (questionId, answer) => {
    setQuizAnswers({
      ...quizAnswers,
      [questionId]: answer
    });
  };
  
  // Handle quiz submission
  const handleQuizSubmit = () => {
    let score = 0;
    
    quizQuestions.forEach(question => {
      if (quizAnswers[question.id] === question.correctAnswer) {
        score++;
      }
    });
    
    setQuizScore(score);
    setQuizSubmitted(true);
    
    if (score >= 4 && onComplete) {
      onComplete();
    }
  };
  
  // Reset quiz
  const resetQuiz = () => {
    setQuizAnswers({});
    setQuizSubmitted(false);
    setQuizScore(0);
  };
  
  // Get the currently selected protocol
  const currentProtocol = protocols[selectedProtocol];
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      {!showQuiz ? (
        <>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Routing Protocols Comparison</h3>
            
            <button
              onClick={() => setShowQuiz(true)}
              className="px-3 py-1 rounded text-sm bg-primary text-black"
            >
              Test Your Knowledge
            </button>
          </div>
          
          {/* Protocol Selection */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2 mb-4">
            {Object.keys(protocols).map(protocol => (
              <button
                key={protocol}
                onClick={() => handleProtocolSelect(protocol)}
                className={`p-2 rounded-lg flex flex-col items-center justify-center text-sm ${
                  selectedProtocol === protocol ? 'bg-primary text-black' : 'bg-[#1E293B] hover:bg-[#334155]'
                }`}
              >
                <div className="text-2xl mb-1">
                  {protocols[protocol].icon}
                </div>
                <span className="text-center">
                  {protocol.toUpperCase()}
                </span>
              </button>
            ))}
          </div>
          
          {/* Protocol Details */}
          <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
            <div className="flex items-center mb-3">
              <div className="text-3xl mr-3">
                {currentProtocol.icon}
              </div>
              <div>
                <h4 className="text-lg font-bold">{currentProtocol.name}</h4>
                <p className="text-sm text-gray-400">{currentProtocol.type} Protocol</p>
              </div>
            </div>
            
            <p className="text-gray-300 mb-4">{currentProtocol.description}</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <h5 className="font-bold mb-2">Characteristics</h5>
                <div className="bg-[#0F172A] p-3 rounded-lg">
                  <table className="w-full text-sm">
                    <tbody>
                      <tr>
                        <td className="py-1 pr-2 text-gray-400">Type:</td>
                        <td>{currentProtocol.type}</td>
                      </tr>
                      <tr>
                        <td className="py-1 pr-2 text-gray-400">Metric:</td>
                        <td>{currentProtocol.metric}</td>
                      </tr>
                      <tr>
                        <td className="py-1 pr-2 text-gray-400">Admin Distance:</td>
                        <td>{currentProtocol.adminDistance}</td>
                      </tr>
                      <tr>
                        <td className="py-1 pr-2 text-gray-400">Updates:</td>
                        <td>{currentProtocol.updateInterval}</td>
                      </tr>
                      <tr>
                        <td className="py-1 pr-2 text-gray-400">Max Hops:</td>
                        <td>{currentProtocol.maxHops}</td>
                      </tr>
                      <tr>
                        <td className="py-1 pr-2 text-gray-400">Convergence:</td>
                        <td>{currentProtocol.convergence}</td>
                      </tr>
                      <tr>
                        <td className="py-1 pr-2 text-gray-400">Versions:</td>
                        <td>{currentProtocol.versions.join(', ')}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              
              <div>
                <h5 className="font-bold mb-2">Advantages & Disadvantages</h5>
                <div className="bg-[#0F172A] p-3 rounded-lg h-full">
                  <div className="mb-3">
                    <h6 className="text-green-400 font-medium mb-1">Advantages:</h6>
                    <ul className="list-disc list-inside text-sm">
                      {currentProtocol.advantages.map((advantage, index) => (
                        <li key={index}>{advantage}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h6 className="text-red-400 font-medium mb-1">Disadvantages:</h6>
                    <ul className="list-disc list-inside text-sm">
                      {currentProtocol.disadvantages.map((disadvantage, index) => (
                        <li key={index}>{disadvantage}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h5 className="font-bold mb-2">Common Use Cases</h5>
              <div className="bg-[#0F172A] p-3 rounded-lg">
                <ul className="list-disc list-inside text-sm">
                  {currentProtocol.useCases.map((useCase, index) => (
                    <li key={index}>{useCase}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
          
          {/* Protocol Comparison */}
          <div className="bg-[#1E293B] p-4 rounded-lg overflow-x-auto">
            <h4 className="font-bold mb-3">Protocol Comparison</h4>
            <table className="w-full text-sm">
              <thead className="bg-[#0F172A]">
                <tr>
                  <th className="p-2 text-left">Protocol</th>
                  <th className="p-2 text-left">Type</th>
                  <th className="p-2 text-left">Metric</th>
                  <th className="p-2 text-left">Convergence</th>
                  <th className="p-2 text-left">Best For</th>
                </tr>
              </thead>
              <tbody>
                {Object.keys(protocols).map(protocol => (
                  <tr 
                    key={protocol} 
                    className={`border-t border-gray-700 ${selectedProtocol === protocol ? 'bg-[#0F172A]' : ''}`}
                  >
                    <td className="p-2 font-medium">
                      <div className="flex items-center">
                        <span className="mr-2">{protocols[protocol].icon}</span>
                        {protocol.toUpperCase()}
                      </div>
                    </td>
                    <td className="p-2">{protocols[protocol].type}</td>
                    <td className="p-2">{protocols[protocol].metric}</td>
                    <td className="p-2">{protocols[protocol].convergence}</td>
                    <td className="p-2">{protocols[protocol].useCases[0]}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      ) : (
        <>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Routing Protocols Quiz</h3>
            
            <button
              onClick={() => setShowQuiz(false)}
              className="px-3 py-1 rounded text-sm bg-[#1E293B] text-white"
            >
              Back to Comparison
            </button>
          </div>
          
          <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
            <p className="mb-4">Test your knowledge of routing protocols by answering the following questions:</p>
            
            {quizQuestions.map((question, index) => (
              <div key={question.id} className="mb-4 pb-4 border-b border-gray-700 last:border-0">
                <p className="font-medium mb-2">{index + 1}. {question.question}</p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {question.options.map(option => (
                    <div 
                      key={option}
                      onClick={() => !quizSubmitted && handleAnswerSelect(question.id, option)}
                      className={`p-2 rounded-lg cursor-pointer border ${
                        quizAnswers[question.id] === option 
                          ? quizSubmitted
                            ? option === question.correctAnswer
                              ? 'bg-green-900/20 border-green-500'
                              : 'bg-red-900/20 border-red-500'
                            : 'bg-primary/20 border-primary'
                          : quizSubmitted && option === question.correctAnswer
                            ? 'bg-green-900/20 border-green-500'
                            : 'bg-[#0F172A] border-gray-700 hover:border-gray-500'
                      }`}
                    >
                      {option}
                      {quizSubmitted && option === question.correctAnswer && (
                        <FaCheck className="inline ml-2 text-green-500" />
                      )}
                      {quizSubmitted && quizAnswers[question.id] === option && option !== question.correctAnswer && (
                        <FaTimes className="inline ml-2 text-red-500" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
            
            {quizSubmitted ? (
              <div>
                <div className={`p-3 rounded-lg mb-3 ${
                  quizScore >= 4 ? 'bg-green-900/20 border border-green-500' : 'bg-blue-900/20 border border-blue-500'
                }`}>
                  <p className="font-bold">
                    Your Score: {quizScore} out of {quizQuestions.length}
                    {quizScore >= 4 && ' - Great job!'}
                  </p>
                  <p className="mt-1">
                    {quizScore >= 4 
                      ? 'You have a good understanding of routing protocols!' 
                      : 'Review the routing protocols section and try again.'}
                  </p>
                </div>
                
                <button
                  onClick={resetQuiz}
                  className="px-4 py-2 rounded bg-primary text-black"
                >
                  Try Again
                </button>
              </div>
            ) : (
              <button
                onClick={handleQuizSubmit}
                disabled={Object.keys(quizAnswers).length < quizQuestions.length}
                className={`px-4 py-2 rounded ${
                  Object.keys(quizAnswers).length < quizQuestions.length
                    ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                    : 'bg-primary text-black'
                }`}
              >
                Submit Answers
              </button>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default RoutingProtocolsComparison;
