import React, { useState, useEffect } from 'react';
import { FaNetworkWired, FaWifi, FaShieldAlt, FaServer, FaCheck, FaTimes, FaRedo, FaSave, FaTerminal, FaInfoCircle } from 'react-icons/fa';

/**
 * Network Device Configuration Simulator
 * 
 * This component provides an interactive simulation for configuring different types of network devices
 * including routers, switches, access points, and firewalls.
 */
const NetworkDeviceConfigSimulator = ({ deviceType = 'router', onComplete }) => {
  // Simulation state
  const [currentDevice, setCurrentDevice] = useState(deviceType);
  const [configMode, setConfigMode] = useState('basic'); // basic, advanced, security
  const [configValues, setConfigValues] = useState({});
  const [configErrors, setConfigErrors] = useState({});
  const [isConfigValid, setIsConfigValid] = useState(false);
  const [isConfigSaved, setIsConfigSaved] = useState(false);
  const [showHelpModal, setShowHelpModal] = useState(false);
  const [helpTopic, setHelpTopic] = useState('');
  const [completedTasks, setCompletedTasks] = useState({
    basicConfig: false,
    advancedConfig: false,
    securityConfig: false
  });
  
  // Device configuration templates
  const deviceConfigs = {
    router: {
      basic: [
        { id: 'hostname', label: 'Hostname', type: 'text', default: 'Router1', required: true, help: 'The name of the router on the network' },
        { id: 'ipAddress', label: 'IP Address', type: 'text', default: '***********', required: true, help: 'The IP address of the router\'s LAN interface' },
        { id: 'subnetMask', label: 'Subnet Mask', type: 'text', default: '*************', required: true, help: 'The subnet mask for the LAN network' },
        { id: 'defaultGateway', label: 'Default Gateway', type: 'text', default: '', required: false, help: 'The IP address of the upstream router (leave blank if this is the main internet router)' }
      ],
      advanced: [
        { id: 'wanIpAddress', label: 'WAN IP Address', type: 'text', default: '********', required: true, help: 'The IP address of the router\'s WAN interface' },
        { id: 'wanSubnetMask', label: 'WAN Subnet Mask', type: 'text', default: '*************', required: true, help: 'The subnet mask for the WAN network' },
        { id: 'dnsServer1', label: 'Primary DNS Server', type: 'text', default: '*******', required: true, help: 'The primary DNS server IP address' },
        { id: 'dnsServer2', label: 'Secondary DNS Server', type: 'text', default: '*******', required: false, help: 'The secondary DNS server IP address' }
      ],
      security: [
        { id: 'firewallEnabled', label: 'Enable Firewall', type: 'checkbox', default: true, required: false, help: 'Turn on the router\'s built-in firewall' },
        { id: 'dmzEnabled', label: 'Enable DMZ', type: 'checkbox', default: false, required: false, help: 'Create a Demilitarized Zone for servers that need to be accessible from the internet' },
        { id: 'dmzIpAddress', label: 'DMZ IP Address', type: 'text', default: '***********00', required: false, help: 'The IP address of the device in the DMZ' },
        { id: 'portForwarding', label: 'Port Forwarding Rules', type: 'textarea', default: '', required: false, help: 'Enter port forwarding rules (format: externalPort:internalIP:internalPort, one per line)' }
      ]
    },
    switch: {
      basic: [
        { id: 'hostname', label: 'Hostname', type: 'text', default: 'Switch1', required: true, help: 'The name of the switch on the network' },
        { id: 'managementIp', label: 'Management IP', type: 'text', default: '***********', required: true, help: 'The IP address used to manage the switch' },
        { id: 'subnetMask', label: 'Subnet Mask', type: 'text', default: '*************', required: true, help: 'The subnet mask for the management network' },
        { id: 'defaultGateway', label: 'Default Gateway', type: 'text', default: '***********', required: true, help: 'The IP address of the router on the network' }
      ],
      advanced: [
        { id: 'vlan1', label: 'VLAN 1 Name', type: 'text', default: 'Default', required: true, help: 'The name for the default VLAN' },
        { id: 'vlan2', label: 'VLAN 2 Name', type: 'text', default: 'Voice', required: false, help: 'The name for a second VLAN (e.g., for VoIP phones)' },
        { id: 'vlan3', label: 'VLAN 3 Name', type: 'text', default: 'Guest', required: false, help: 'The name for a third VLAN (e.g., for guest access)' },
        { id: 'spanningTree', label: 'Spanning Tree Protocol', type: 'select', options: ['STP', 'RSTP', 'MSTP', 'None'], default: 'RSTP', required: true, help: 'The spanning tree protocol to prevent network loops' }
      ],
      security: [
        { id: 'portSecurity', label: 'Enable Port Security', type: 'checkbox', default: false, required: false, help: 'Restrict which devices can connect to each port based on MAC address' },
        { id: 'macAddressLimit', label: 'MAC Address Limit per Port', type: 'number', default: 1, min: 1, max: 10, required: false, help: 'Maximum number of MAC addresses allowed per port' },
        { id: 'stormControl', label: 'Enable Storm Control', type: 'checkbox', default: false, required: false, help: 'Prevent broadcast storms from affecting network performance' },
        { id: 'sshAccess', label: 'Enable SSH Access', type: 'checkbox', default: true, required: false, help: 'Allow secure remote management via SSH' }
      ]
    },
    accessPoint: {
      basic: [
        { id: 'hostname', label: 'Hostname', type: 'text', default: 'AP1', required: true, help: 'The name of the access point on the network' },
        { id: 'ipAddress', label: 'IP Address', type: 'text', default: '***********', required: true, help: 'The IP address of the access point' },
        { id: 'subnetMask', label: 'Subnet Mask', type: 'text', default: '*************', required: true, help: 'The subnet mask for the network' },
        { id: 'defaultGateway', label: 'Default Gateway', type: 'text', default: '***********', required: true, help: 'The IP address of the router on the network' }
      ],
      advanced: [
        { id: 'ssid', label: 'Wireless Network Name (SSID)', type: 'text', default: 'MyNetwork', required: true, help: 'The name of your wireless network' },
        { id: 'channel', label: 'Wireless Channel', type: 'select', options: ['Auto', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'], default: 'Auto', required: true, help: 'The wireless channel to use (Auto is recommended unless you have specific interference issues)' },
        { id: 'band', label: 'Wireless Band', type: 'select', options: ['2.4 GHz', '5 GHz', 'Dual Band'], default: 'Dual Band', required: true, help: 'The frequency band for your wireless network' },
        { id: 'txPower', label: 'Transmit Power', type: 'select', options: ['Low', 'Medium', 'High'], default: 'High', required: true, help: 'The power level for the wireless signal (affects range and battery usage of connected devices)' }
      ],
      security: [
        { id: 'securityMode', label: 'Security Mode', type: 'select', options: ['None', 'WEP', 'WPA', 'WPA2', 'WPA3'], default: 'WPA2', required: true, help: 'The security protocol for your wireless network (WPA2 or WPA3 recommended)' },
        { id: 'password', label: 'Wireless Password', type: 'password', default: 'MySecurePassword123', required: true, help: 'The password for connecting to your wireless network (should be at least 8 characters)' },
        { id: 'macFiltering', label: 'Enable MAC Filtering', type: 'checkbox', default: false, required: false, help: 'Only allow specific devices to connect based on their MAC address' },
        { id: 'hideSsid', label: 'Hide Network Name (SSID)', type: 'checkbox', default: false, required: false, help: 'Prevent your network name from appearing in the list of available networks' }
      ]
    },
    firewall: {
      basic: [
        { id: 'hostname', label: 'Hostname', type: 'text', default: 'Firewall1', required: true, help: 'The name of the firewall on the network' },
        { id: 'internalIp', label: 'Internal IP Address', type: 'text', default: '***********54', required: true, help: 'The IP address of the firewall\'s internal interface' },
        { id: 'externalIp', label: 'External IP Address', type: 'text', default: '***********', required: true, help: 'The IP address of the firewall\'s external interface' },
        { id: 'defaultGateway', label: 'Default Gateway', type: 'text', default: '*************', required: true, help: 'The IP address of the upstream router or ISP gateway' }
      ],
      advanced: [
        { id: 'natEnabled', label: 'Enable NAT', type: 'checkbox', default: true, required: false, help: 'Network Address Translation allows multiple internal devices to share a single external IP address' },
        { id: 'dmzEnabled', label: 'Enable DMZ', type: 'checkbox', default: false, required: false, help: 'Create a Demilitarized Zone for servers that need to be accessible from the internet' },
        { id: 'dmzIpAddress', label: 'DMZ IP Address', type: 'text', default: '***********00', required: false, help: 'The IP address of the device in the DMZ' },
        { id: 'logLevel', label: 'Logging Level', type: 'select', options: ['None', 'Low', 'Medium', 'High'], default: 'Medium', required: true, help: 'The amount of detail in the firewall logs' }
      ],
      security: [
        { id: 'defaultPolicy', label: 'Default Policy', type: 'select', options: ['Allow All', 'Deny All'], default: 'Deny All', required: true, help: 'The default action for traffic not matching any rules (Deny All is more secure)' },
        { id: 'ipsEnabled', label: 'Enable Intrusion Prevention', type: 'checkbox', default: true, required: false, help: 'Actively block detected attack attempts' },
        { id: 'webFiltering', label: 'Enable Web Filtering', type: 'checkbox', default: false, required: false, help: 'Block access to malicious or inappropriate websites' },
        { id: 'firewallRules', label: 'Firewall Rules', type: 'textarea', default: '', required: false, help: 'Enter firewall rules (format: action:protocol:sourceIP:destIP:port, one per line)' }
      ]
    }
  };
  
  // Initialize config values
  useEffect(() => {
    const initialValues = {};
    const currentConfig = deviceConfigs[currentDevice];
    
    // Set default values for all fields
    Object.keys(currentConfig).forEach(mode => {
      currentConfig[mode].forEach(field => {
        initialValues[field.id] = field.default;
      });
    });
    
    setConfigValues(initialValues);
    setConfigMode('basic');
    setIsConfigSaved(false);
    setCompletedTasks({
      basicConfig: false,
      advancedConfig: false,
      securityConfig: false
    });
  }, [currentDevice]);
  
  // Validate configuration
  useEffect(() => {
    const errors = {};
    const currentConfig = deviceConfigs[currentDevice][configMode];
    
    // Check required fields
    currentConfig.forEach(field => {
      if (field.required && !configValues[field.id]) {
        errors[field.id] = `${field.label} is required`;
      }
    });
    
    // Validate IP addresses
    const ipFields = ['ipAddress', 'wanIpAddress', 'managementIp', 'internalIp', 'externalIp', 'defaultGateway', 'dnsServer1', 'dnsServer2', 'dmzIpAddress'];
    ipFields.forEach(field => {
      if (configValues[field] && !isValidIpAddress(configValues[field])) {
        errors[field] = 'Invalid IP address format';
      }
    });
    
    setConfigErrors(errors);
    setIsConfigValid(Object.keys(errors).length === 0);
  }, [configValues, configMode, currentDevice]);
  
  // Handle input change
  const handleInputChange = (e) => {
    const { id, value, type, checked } = e.target;
    setConfigValues({
      ...configValues,
      [id]: type === 'checkbox' ? checked : value
    });
  };
  
  // Handle device change
  const handleDeviceChange = (device) => {
    setCurrentDevice(device);
  };
  
  // Handle save configuration
  const handleSaveConfig = () => {
    if (!isConfigValid) return;
    
    // Mark current config mode as completed
    setCompletedTasks({
      ...completedTasks,
      [`${configMode}Config`]: true
    });
    
    setIsConfigSaved(true);
    
    // Check if all tasks are completed
    const allCompleted = completedTasks.basicConfig && 
                         completedTasks.advancedConfig && 
                         completedTasks.securityConfig;
    
    if (allCompleted && onComplete) {
      onComplete();
    }
    
    // Save to localStorage
    try {
      const userProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');
      if (!userProgress.deviceConfig) {
        userProgress.deviceConfig = {};
      }
      userProgress.deviceConfig[currentDevice] = {
        ...userProgress.deviceConfig[currentDevice],
        [configMode]: true,
        values: configValues
      };
      localStorage.setItem('userProgress', JSON.stringify(userProgress));
    } catch (error) {
      console.error('Error saving progress:', error);
    }
  };
  
  // Reset configuration
  const handleResetConfig = () => {
    const initialValues = {};
    const currentConfig = deviceConfigs[currentDevice][configMode];
    
    // Reset to default values for current mode
    currentConfig.forEach(field => {
      initialValues[field.id] = field.default;
    });
    
    // Preserve values from other modes
    setConfigValues({
      ...configValues,
      ...initialValues
    });
    
    setIsConfigSaved(false);
  };
  
  // Show help for a field
  const handleShowHelp = (topic) => {
    setHelpTopic(topic);
    setShowHelpModal(true);
  };
  
  // Validate IP address format
  const isValidIpAddress = (ip) => {
    const ipPattern = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipPattern.test(ip);
  };
  
  // Get device icon
  const getDeviceIcon = (device) => {
    switch (device) {
      case 'router':
        return <FaNetworkWired className="text-blue-400" />;
      case 'switch':
        return <FaNetworkWired className="text-green-400" />;
      case 'accessPoint':
        return <FaWifi className="text-purple-400" />;
      case 'firewall':
        return <FaShieldAlt className="text-red-400" />;
      default:
        return <FaServer className="text-yellow-400" />;
    }
  };
  
  // Calculate completion percentage
  const completionPercentage = Math.round(
    ((completedTasks.basicConfig ? 1 : 0) + 
     (completedTasks.advancedConfig ? 1 : 0) + 
     (completedTasks.securityConfig ? 1 : 0)) / 3 * 100
  );
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Network Device Configuration</h3>
        
        <div className="text-sm text-gray-400">
          Completion: {completionPercentage}%
        </div>
      </div>
      
      {/* Device Selection */}
      <div className="grid grid-cols-4 gap-2 mb-4">
        {Object.keys(deviceConfigs).map(device => (
          <button
            key={device}
            onClick={() => handleDeviceChange(device)}
            className={`p-3 rounded-lg flex flex-col items-center justify-center ${
              currentDevice === device ? 'bg-primary text-black' : 'bg-[#1E293B] text-white hover:bg-[#334155]'
            }`}
          >
            <div className="text-xl mb-1">
              {getDeviceIcon(device)}
            </div>
            <span className="text-sm">
              {device.charAt(0).toUpperCase() + device.slice(1)}
            </span>
          </button>
        ))}
      </div>
      
      {/* Configuration Mode Tabs */}
      <div className="flex border-b border-gray-700 mb-4">
        {['basic', 'advanced', 'security'].map(mode => (
          <button
            key={mode}
            onClick={() => setConfigMode(mode)}
            className={`py-2 px-4 ${
              configMode === mode ? 'border-b-2 border-primary text-primary' : 'text-gray-400 hover:text-white'
            }`}
          >
            {mode.charAt(0).toUpperCase() + mode.slice(1)}
            {completedTasks[`${mode}Config`] && (
              <FaCheck className="inline-block ml-2 text-green-500" />
            )}
          </button>
        ))}
      </div>
      
      {/* Configuration Form */}
      <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
        <h4 className="font-bold mb-3 flex items-center">
          {getDeviceIcon(currentDevice)}
          <span className="ml-2">
            {currentDevice.charAt(0).toUpperCase() + currentDevice.slice(1)} {configMode.charAt(0).toUpperCase() + configMode.slice(1)} Configuration
          </span>
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {deviceConfigs[currentDevice][configMode].map(field => (
            <div key={field.id} className="mb-3">
              <label className="block text-sm font-medium mb-1" htmlFor={field.id}>
                {field.label}
                <button
                  type="button"
                  onClick={() => handleShowHelp(field.help)}
                  className="ml-2 text-gray-400 hover:text-primary"
                >
                  <FaInfoCircle className="text-xs" />
                </button>
              </label>
              
              {field.type === 'text' || field.type === 'password' || field.type === 'number' ? (
                <input
                  type={field.type}
                  id={field.id}
                  value={configValues[field.id] || ''}
                  onChange={handleInputChange}
                  min={field.min}
                  max={field.max}
                  className={`w-full bg-[#0F172A] px-3 py-2 rounded border ${
                    configErrors[field.id] ? 'border-red-500' : 'border-gray-700'
                  }`}
                />
              ) : field.type === 'checkbox' ? (
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id={field.id}
                    checked={configValues[field.id] || false}
                    onChange={handleInputChange}
                    className="w-4 h-4 text-primary bg-[#0F172A] border-gray-700 rounded focus:ring-primary"
                  />
                  <label htmlFor={field.id} className="ml-2 text-sm text-gray-300">
                    {field.label}
                  </label>
                </div>
              ) : field.type === 'select' ? (
                <select
                  id={field.id}
                  value={configValues[field.id] || field.default}
                  onChange={handleInputChange}
                  className="w-full bg-[#0F172A] px-3 py-2 rounded border border-gray-700"
                >
                  {field.options.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              ) : field.type === 'textarea' ? (
                <textarea
                  id={field.id}
                  value={configValues[field.id] || ''}
                  onChange={handleInputChange}
                  rows="3"
                  className="w-full bg-[#0F172A] px-3 py-2 rounded border border-gray-700"
                  placeholder={field.placeholder}
                ></textarea>
              ) : null}
              
              {configErrors[field.id] && (
                <p className="text-red-500 text-xs mt-1">{configErrors[field.id]}</p>
              )}
            </div>
          ))}
        </div>
      </div>
      
      {/* Command Preview */}
      <div className="bg-black rounded-lg p-3 mb-4 font-mono text-sm text-green-400 overflow-x-auto">
        <div className="flex items-center mb-2">
          <FaTerminal className="mr-2" />
          <span className="text-gray-400">Configuration Preview</span>
        </div>
        
        {currentDevice === 'router' && (
          <>
            <div>Router&gt; enable</div>
            <div>Router# configure terminal</div>
            <div>Router(config)# hostname {configValues.hostname || 'Router1'}</div>
            <div>
              {configValues.hostname || 'Router1'}(config)# interface GigabitEthernet0/0
            </div>
            <div>
              {configValues.hostname || 'Router1'}(config-if)# ip address {configValues.ipAddress || '***********'} {configValues.subnetMask || '*************'}
            </div>
            <div>
              {configValues.hostname || 'Router1'}(config-if)# no shutdown
            </div>
            {configMode === 'advanced' && (
              <>
                <div>
                  {configValues.hostname || 'Router1'}(config-if)# exit
                </div>
                <div>
                  {configValues.hostname || 'Router1'}(config)# interface GigabitEthernet0/1
                </div>
                <div>
                  {configValues.hostname || 'Router1'}(config-if)# ip address {configValues.wanIpAddress || '********'} {configValues.wanSubnetMask || '*************'}
                </div>
                <div>
                  {configValues.hostname || 'Router1'}(config-if)# no shutdown
                </div>
              </>
            )}
          </>
        )}
        
        {currentDevice === 'switch' && (
          <>
            <div>Switch&gt; enable</div>
            <div>Switch# configure terminal</div>
            <div>Switch(config)# hostname {configValues.hostname || 'Switch1'}</div>
            <div>
              {configValues.hostname || 'Switch1'}(config)# interface vlan 1
            </div>
            <div>
              {configValues.hostname || 'Switch1'}(config-if)# ip address {configValues.managementIp || '***********'} {configValues.subnetMask || '*************'}
            </div>
            <div>
              {configValues.hostname || 'Switch1'}(config-if)# no shutdown
            </div>
            <div>
              {configValues.hostname || 'Switch1'}(config-if)# exit
            </div>
            <div>
              {configValues.hostname || 'Switch1'}(config)# ip default-gateway {configValues.defaultGateway || '***********'}
            </div>
          </>
        )}
      </div>
      
      {/* Action Buttons */}
      <div className="flex justify-between">
        <button
          onClick={handleResetConfig}
          className="px-4 py-2 rounded bg-[#1E293B] text-white hover:bg-[#334155] flex items-center"
        >
          <FaRedo className="mr-2" />
          Reset
        </button>
        
        <button
          onClick={handleSaveConfig}
          disabled={!isConfigValid}
          className={`px-4 py-2 rounded flex items-center ${
            isConfigValid
              ? isConfigSaved
                ? 'bg-green-600 text-white'
                : 'bg-primary text-black hover:bg-primary-hover'
              : 'bg-gray-700 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isConfigSaved ? <FaCheck className="mr-2" /> : <FaSave className="mr-2" />}
          {isConfigSaved ? 'Saved' : 'Save Configuration'}
        </button>
      </div>
      
      {/* Help Modal */}
      {showHelpModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-[#0F172A] rounded-lg p-6 max-w-md w-full border border-gray-700">
            <h3 className="text-lg font-bold mb-3 flex items-center">
              <FaInfoCircle className="text-primary mr-2" />
              Configuration Help
            </h3>
            
            <p className="text-gray-300 mb-4">{helpTopic}</p>
            
            <div className="flex justify-end">
              <button
                onClick={() => setShowHelpModal(false)}
                className="px-4 py-2 rounded bg-primary text-black"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NetworkDeviceConfigSimulator;
