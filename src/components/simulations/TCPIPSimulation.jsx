import React, { useState, useEffect } from 'react';
import { FaPlay, FaPause, FaRedo, FaCheck, FaTimes, FaInfoCircle, FaNetworkWired, FaServer, FaDesktop } from 'react-icons/fa';

/**
 * TCP/IP Protocol Simulation Component
 * 
 * This component provides an interactive simulation of TCP and UDP protocols,
 * showing the differences in connection establishment, data transfer, and reliability.
 */
const TCPIPSimulation = ({ simulationType = 'tcp', onComplete }) => {
  // Simulation state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [completed, setCompleted] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [packetLoss, setPacketLoss] = useState(false);
  const [packets, setPackets] = useState([]);
  const [acknowledgments, setAcknowledgments] = useState([]);
  const [quizActive, setQuizActive] = useState(false);
  const [quizQuestion, setQuizQuestion] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [quizResult, setQuizResult] = useState(null);
  
  // TCP simulation steps
  const tcpSteps = [
    {
      title: "Step 1: SYN",
      description: "Client sends a SYN packet to initiate connection",
      details: "The client sends a TCP packet with the SYN flag set to 1 and an initial sequence number (ISN). This begins the connection establishment process."
    },
    {
      title: "Step 2: SYN-ACK",
      description: "Server responds with SYN-ACK packet",
      details: "The server acknowledges the client's SYN by sending a packet with both SYN and ACK flags set. It includes its own ISN and acknowledges the client's sequence number by adding 1."
    },
    {
      title: "Step 3: ACK",
      description: "Client acknowledges the server's SYN-ACK",
      details: "The client sends an ACK packet to acknowledge the server's SYN-ACK. At this point, the connection is established and both sides can begin sending data."
    },
    {
      title: "Step 4: Data Transfer",
      description: "Client sends data packets to server",
      details: "The client begins sending data packets to the server. Each packet contains a sequence number so the server can reassemble them in the correct order."
    },
    {
      title: "Step 5: ACK Data",
      description: "Server acknowledges received data",
      details: "The server sends ACK packets to confirm receipt of data. If a packet is lost, the client will retransmit it after a timeout period."
    },
    {
      title: "Step 6: FIN",
      description: "Client initiates connection termination",
      details: "When data transfer is complete, the client sends a FIN packet to begin the connection termination process."
    },
    {
      title: "Step 7: FIN-ACK",
      description: "Server acknowledges and begins its own termination",
      details: "The server acknowledges the client's FIN and sends its own FIN packet to indicate it's ready to close the connection."
    },
    {
      title: "Step 8: ACK",
      description: "Client acknowledges server's FIN",
      details: "The client sends a final ACK to acknowledge the server's FIN. After a timeout period, the connection is fully closed."
    }
  ];
  
  // UDP simulation steps
  const udpSteps = [
    {
      title: "Step 1: No Connection Setup",
      description: "UDP doesn't establish a connection before sending data",
      details: "Unlike TCP, UDP is connectionless. It doesn't perform a handshake before sending data, which reduces latency but also eliminates connection guarantees."
    },
    {
      title: "Step 2: Data Transmission",
      description: "Client sends data packets directly",
      details: "The client immediately begins sending data packets (datagrams) to the server without waiting for acknowledgment or establishing a connection."
    },
    {
      title: "Step 3: Continued Transmission",
      description: "Client continues sending packets without waiting for acknowledgments",
      details: "UDP doesn't wait for acknowledgments before sending more data. This increases speed but means there's no guarantee of delivery or packet order."
    },
    {
      title: "Step 4: Packet Loss",
      description: "Some packets may be lost during transmission",
      details: "If a UDP packet is lost during transmission, it remains lost. UDP has no built-in recovery mechanism for lost packets."
    },
    {
      title: "Step 5: No Connection Termination",
      description: "UDP doesn't formally terminate connections",
      details: "Since UDP doesn't establish connections, there's no formal termination process. The client simply stops sending data when finished."
    }
  ];
  
  // Quiz questions
  const questions = [
    {
      question: "What is the main difference between TCP and UDP?",
      options: [
        "TCP is faster than UDP",
        "TCP is connection-oriented while UDP is connectionless",
        "TCP uses IP addresses while UDP uses MAC addresses",
        "TCP is used for the internet while UDP is only for local networks"
      ],
      correctAnswer: 1,
      explanation: "The main difference is that TCP is connection-oriented, establishing a connection before sending data and ensuring reliable delivery, while UDP is connectionless and doesn't guarantee delivery."
    },
    {
      question: "Which protocol would be better for streaming live video?",
      options: [
        "TCP because it guarantees all data will arrive",
        "UDP because it's faster with less overhead",
        "Both are equally suitable",
        "Neither, a different protocol should be used"
      ],
      correctAnswer: 1,
      explanation: "UDP is generally better for streaming live video because it prioritizes speed over reliability. In live video, it's usually better to skip frames (accept some packet loss) than to wait for retransmissions, which would cause buffering and delays."
    },
    {
      question: "What happens during the TCP three-way handshake?",
      options: [
        "Data is encrypted three different ways",
        "Three packets of data are sent simultaneously",
        "SYN, SYN-ACK, and ACK messages are exchanged",
        "Three different protocols are used together"
      ],
      correctAnswer: 2,
      explanation: "The TCP three-way handshake involves exchanging SYN (synchronize), SYN-ACK (synchronize-acknowledge), and ACK (acknowledge) messages to establish a connection."
    },
    {
      question: "What happens if a UDP packet is lost during transmission?",
      options: [
        "The sender automatically retransmits it",
        "The receiver requests retransmission",
        "Nothing, the data is permanently lost",
        "The router stores it and tries again later"
      ],
      correctAnswer: 2,
      explanation: "If a UDP packet is lost during transmission, nothing happens at the protocol level. UDP has no built-in mechanism for detecting or recovering lost packets. The data is permanently lost unless the application layer has implemented its own recovery mechanism."
    },
    {
      question: "Which of these applications would most likely use TCP instead of UDP?",
      options: [
        "Video conferencing",
        "Online gaming",
        "Voice over IP (VoIP)",
        "File downloading"
      ],
      correctAnswer: 3,
      explanation: "File downloading would most likely use TCP because it requires all data to be delivered accurately and completely. A single missing or corrupted packet could render the entire file unusable, so TCP's reliability mechanisms are essential."
    }
  ];
  
  // Get current steps based on simulation type
  const currentSteps = simulationType === 'tcp' ? tcpSteps : udpSteps;
  
  // Initialize simulation
  useEffect(() => {
    resetSimulation();
  }, [simulationType]);
  
  // Auto-advance steps when playing
  useEffect(() => {
    let timer;
    if (isPlaying && currentStep < currentSteps.length - 1) {
      timer = setTimeout(() => {
        advanceStep();
      }, 2000);
    } else if (currentStep >= currentSteps.length - 1) {
      setCompleted(true);
      setIsPlaying(false);
      if (onComplete) {
        onComplete();
      }
    }
    
    return () => clearTimeout(timer);
  }, [isPlaying, currentStep, currentSteps.length]);
  
  // Advance to next step
  const advanceStep = () => {
    if (currentStep < currentSteps.length - 1) {
      setCurrentStep(currentStep + 1);
      
      // Add packets based on the step
      if (simulationType === 'tcp') {
        handleTCPStep(currentStep + 1);
      } else {
        handleUDPStep(currentStep + 1);
      }
    } else {
      setCompleted(true);
      setIsPlaying(false);
    }
  };
  
  // Handle TCP step animation
  const handleTCPStep = (step) => {
    switch (step) {
      case 0: // SYN
        setPackets([{ id: 'syn', type: 'syn', direction: 'right', position: 0 }]);
        break;
      case 1: // SYN-ACK
        setPackets(prev => [...prev, { id: 'syn-ack', type: 'syn-ack', direction: 'left', position: 100 }]);
        break;
      case 2: // ACK
        setPackets(prev => [...prev, { id: 'ack', type: 'ack', direction: 'right', position: 0 }]);
        break;
      case 3: // Data Transfer
        setPackets(prev => [
          ...prev,
          { id: 'data-1', type: 'data', direction: 'right', position: 0 },
          { id: 'data-2', type: 'data', direction: 'right', position: 0, delay: 300 },
          { id: 'data-3', type: 'data', direction: 'right', position: 0, delay: 600 }
        ]);
        if (packetLoss) {
          // Mark the second packet as lost
          setTimeout(() => {
            setPackets(prev => prev.map(p => 
              p.id === 'data-2' ? { ...p, lost: true } : p
            ));
          }, 1000);
        }
        break;
      case 4: // ACK Data
        setAcknowledgments([
          { id: 'ack-data-1', type: 'ack', direction: 'left', position: 100 },
          { id: 'ack-data-3', type: 'ack', direction: 'left', position: 100, delay: 300 }
        ]);
        if (packetLoss) {
          // Retransmit lost packet
          setTimeout(() => {
            setPackets(prev => [...prev, { id: 'data-2-retry', type: 'data', direction: 'right', position: 0 }]);
            // Acknowledge retransmitted packet
            setTimeout(() => {
              setAcknowledgments(prev => [...prev, { id: 'ack-data-2', type: 'ack', direction: 'left', position: 100 }]);
            }, 1000);
          }, 1500);
        }
        break;
      case 5: // FIN
        setPackets(prev => [...prev, { id: 'fin', type: 'fin', direction: 'right', position: 0 }]);
        break;
      case 6: // FIN-ACK
        setPackets(prev => [...prev, { id: 'fin-ack', type: 'fin-ack', direction: 'left', position: 100 }]);
        break;
      case 7: // Final ACK
        setPackets(prev => [...prev, { id: 'final-ack', type: 'ack', direction: 'right', position: 0 }]);
        break;
      default:
        break;
    }
  };
  
  // Handle UDP step animation
  const handleUDPStep = (step) => {
    switch (step) {
      case 1: // Data Transmission
        setPackets([
          { id: 'udp-1', type: 'data', direction: 'right', position: 0 },
          { id: 'udp-2', type: 'data', direction: 'right', position: 0, delay: 200 }
        ]);
        break;
      case 2: // Continued Transmission
        setPackets(prev => [
          ...prev,
          { id: 'udp-3', type: 'data', direction: 'right', position: 0 },
          { id: 'udp-4', type: 'data', direction: 'right', position: 0, delay: 200 },
          { id: 'udp-5', type: 'data', direction: 'right', position: 0, delay: 400 }
        ]);
        break;
      case 3: // Packet Loss
        setPackets(prev => [
          ...prev,
          { id: 'udp-6', type: 'data', direction: 'right', position: 0 },
          { id: 'udp-7', type: 'data', direction: 'right', position: 0, delay: 200, lost: true },
          { id: 'udp-8', type: 'data', direction: 'right', position: 0, delay: 400 }
        ]);
        break;
      case 4: // No Connection Termination
        setPackets(prev => [
          ...prev,
          { id: 'udp-9', type: 'data', direction: 'right', position: 0 },
          { id: 'udp-10', type: 'data', direction: 'right', position: 0, delay: 200 }
        ]);
        break;
      default:
        break;
    }
  };
  
  // Reset simulation
  const resetSimulation = () => {
    setCurrentStep(0);
    setIsPlaying(false);
    setCompleted(false);
    setPackets([]);
    setAcknowledgments([]);
    
    // Initialize first step
    if (simulationType === 'tcp') {
      handleTCPStep(0);
    }
  };
  
  // Toggle play/pause
  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };
  
  // Start quiz
  const startQuiz = () => {
    setQuizQuestion(questions[Math.floor(Math.random() * questions.length)]);
    setQuizActive(true);
    setSelectedAnswer(null);
    setQuizResult(null);
  };
  
  // Handle answer selection
  const handleAnswerSelect = (index) => {
    setSelectedAnswer(index);
    
    const isCorrect = index === quizQuestion.correctAnswer;
    setQuizResult({
      correct: isCorrect,
      explanation: quizQuestion.explanation
    });
  };
  
  // Calculate completion percentage
  const completionPercentage = Math.round((currentStep / (currentSteps.length - 1)) * 100);
  
  // Get packet color based on type
  const getPacketColor = (type) => {
    switch (type) {
      case 'syn':
        return 'bg-blue-500';
      case 'syn-ack':
        return 'bg-green-500';
      case 'ack':
        return 'bg-purple-500';
      case 'data':
        return 'bg-yellow-500';
      case 'fin':
        return 'bg-red-500';
      case 'fin-ack':
        return 'bg-orange-500';
      default:
        return 'bg-gray-500';
    }
  };
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">{simulationType.toUpperCase()} Protocol Simulation</h3>
        
        <div className="flex space-x-2">
          <button
            onClick={togglePlay}
            className={`px-3 py-1 rounded text-sm ${isPlaying ? 'bg-gray-700 text-white' : 'bg-primary text-black'}`}
            disabled={completed}
          >
            {isPlaying ? <><FaPause className="inline mr-1" /> Pause</> : <><FaPlay className="inline mr-1" /> Play</>}
          </button>
          
          <button
            onClick={resetSimulation}
            className="px-3 py-1 rounded text-sm bg-[#1E293B] text-white"
          >
            <FaRedo className="inline mr-1" /> Reset
          </button>
          
          <button
            onClick={() => setShowDetails(!showDetails)}
            className={`px-3 py-1 rounded text-sm ${showDetails ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
          
          {simulationType === 'tcp' && (
            <button
              onClick={() => setPacketLoss(!packetLoss)}
              className={`px-3 py-1 rounded text-sm ${packetLoss ? 'bg-red-600 text-white' : 'bg-[#1E293B] text-white'}`}
            >
              {packetLoss ? 'Packet Loss: On' : 'Packet Loss: Off'}
            </button>
          )}
        </div>
      </div>
      
      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-400 mb-1">
          <span>Step {currentStep + 1} of {currentSteps.length}</span>
          <span>{completionPercentage}% Complete</span>
        </div>
        <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
          <div 
            className="h-full bg-primary transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
      </div>
      
      {/* Current Step Information */}
      <div className="bg-[#1E293B] p-4 rounded-lg mb-4 border border-gray-700">
        <h4 className="font-bold mb-1">{currentSteps[currentStep].title}</h4>
        <p className="text-gray-300 mb-2">{currentSteps[currentStep].description}</p>
        
        {showDetails && (
          <div className="bg-[#0F172A] p-3 rounded border border-gray-700 text-sm">
            <p className="text-gray-300">{currentSteps[currentStep].details}</p>
          </div>
        )}
      </div>
      
      {/* Network Visualization */}
      <div className="relative h-60 bg-[#0F172A] rounded-lg mb-4 overflow-hidden border border-gray-700">
        {/* Client */}
        <div className="absolute left-8 top-1/2 transform -translate-y-1/2 w-20 h-20 bg-[#1E293B] rounded-lg flex flex-col items-center justify-center">
          <FaDesktop className="text-2xl text-blue-400 mb-2" />
          <div className="text-sm font-bold">Client</div>
        </div>
        
        {/* Server */}
        <div className="absolute right-8 top-1/2 transform -translate-y-1/2 w-20 h-20 bg-[#1E293B] rounded-lg flex flex-col items-center justify-center">
          <FaServer className="text-2xl text-green-400 mb-2" />
          <div className="text-sm font-bold">Server</div>
        </div>
        
        {/* Connection Line */}
        <div className="absolute left-28 right-28 top-1/2 h-0.5 bg-gray-700"></div>
        
        {/* Packets */}
        {packets.map((packet, index) => {
          const startPosition = packet.direction === 'right' ? 28 : 72;
          const endPosition = packet.direction === 'right' ? 72 : 28;
          
          return (
            <div
              key={packet.id}
              className={`absolute top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full flex items-center justify-center text-xs text-white ${getPacketColor(packet.type)} ${packet.lost ? 'opacity-0' : ''}`}
              style={{
                left: `${startPosition}%`,
                transition: 'left 1.5s ease, opacity 0.3s ease',
                transitionDelay: packet.delay ? `${packet.delay}ms` : '0ms',
                animation: packet.lost ? 'none' : `movePacket-${packet.direction} 1.5s forwards ${packet.delay || 0}ms`
              }}
            >
              {packet.type.toUpperCase().substring(0, 3)}
            </div>
          );
        })}
        
        {/* Acknowledgments */}
        {acknowledgments.map((ack, index) => {
          const startPosition = ack.direction === 'right' ? 28 : 72;
          const endPosition = ack.direction === 'right' ? 72 : 28;
          
          return (
            <div
              key={ack.id}
              className={`absolute top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full flex items-center justify-center text-xs text-white ${getPacketColor(ack.type)}`}
              style={{
                left: `${startPosition}%`,
                transition: 'left 1.5s ease',
                transitionDelay: ack.delay ? `${ack.delay}ms` : '0ms',
                animation: `movePacket-${ack.direction} 1.5s forwards ${ack.delay || 0}ms`
              }}
            >
              ACK
            </div>
          );
        })}
        
        {/* CSS Animations */}
        <style jsx>{`
          @keyframes movePacket-right {
            from { left: 28%; }
            to { left: 72%; }
          }
          
          @keyframes movePacket-left {
            from { left: 72%; }
            to { left: 28%; }
          }
        `}</style>
      </div>
      
      {/* Protocol Comparison */}
      <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
        <h4 className="font-bold mb-2">TCP vs UDP Comparison</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className={`p-3 rounded ${simulationType === 'tcp' ? 'bg-blue-900/30 border border-blue-500/50' : 'bg-[#0F172A]'}`}>
            <h5 className="font-bold text-blue-400 mb-1">TCP</h5>
            <ul className="list-disc list-inside text-gray-300 space-y-1">
              <li>Connection-oriented</li>
              <li>Reliable delivery</li>
              <li>Ordered packets</li>
              <li>Flow control</li>
              <li>Error recovery</li>
              <li>Slower but reliable</li>
            </ul>
          </div>
          <div className={`p-3 rounded ${simulationType === 'udp' ? 'bg-green-900/30 border border-green-500/50' : 'bg-[#0F172A]'}`}>
            <h5 className="font-bold text-green-400 mb-1">UDP</h5>
            <ul className="list-disc list-inside text-gray-300 space-y-1">
              <li>Connectionless</li>
              <li>No delivery guarantee</li>
              <li>No packet ordering</li>
              <li>No flow control</li>
              <li>No error recovery</li>
              <li>Faster but unreliable</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Controls */}
      <div className="flex justify-between">
        <button
          onClick={() => advanceStep()}
          disabled={isPlaying || currentStep >= currentSteps.length - 1}
          className={`px-4 py-2 rounded ${
            isPlaying || currentStep >= currentSteps.length - 1
              ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
              : 'bg-[#1E293B] text-white hover:bg-[#334155]'
          }`}
        >
          Manual Step
        </button>
        
        <button
          onClick={startQuiz}
          className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700"
        >
          <FaInfoCircle className="inline mr-1" /> Test Knowledge
        </button>
      </div>
      
      {/* Quiz Modal */}
      {quizActive && quizQuestion && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-[#0F172A] rounded-lg p-6 max-w-2xl w-full border border-gray-700">
            <h3 className="text-xl font-bold mb-4">TCP/IP Protocol Quiz</h3>
            
            <div className="mb-6">
              <h4 className="font-bold mb-3">{quizQuestion.question}</h4>
              
              <div className="space-y-2">
                {quizQuestion.options.map((option, index) => (
                  <div
                    key={index}
                    onClick={() => quizResult === null && handleAnswerSelect(index)}
                    className={`p-3 rounded-lg cursor-pointer border ${
                      selectedAnswer === index
                        ? quizResult?.correct
                          ? 'bg-green-900/20 border-green-500'
                          : 'bg-red-900/20 border-red-500'
                        : quizResult !== null && index === quizQuestion.correctAnswer
                          ? 'bg-green-900/20 border-green-500'
                          : 'bg-[#1E293B] border-gray-700 hover:border-primary'
                    }`}
                  >
                    {option}
                  </div>
                ))}
              </div>
              
              {quizResult && (
                <div className={`mt-4 p-4 rounded-lg ${
                  quizResult.correct ? 'bg-green-900/20 border border-green-500' : 'bg-red-900/20 border border-red-500'
                }`}>
                  <div className="flex items-center mb-2">
                    {quizResult.correct
                      ? <FaCheck className="text-green-500 mr-2" />
                      : <FaTimes className="text-red-500 mr-2" />
                    }
                    <span className="font-bold">{quizResult.correct ? 'Correct!' : 'Incorrect'}</span>
                  </div>
                  <p>{quizResult.explanation}</p>
                </div>
              )}
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={() => setQuizActive(false)}
                className="px-4 py-2 rounded bg-primary text-black"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TCPIPSimulation;
