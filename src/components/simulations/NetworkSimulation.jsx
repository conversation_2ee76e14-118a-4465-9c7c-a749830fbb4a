import React, { useState, useEffect } from 'react';
import { FaNetworkWired, FaServer, FaDesktop, FaWifi, FaExclamationTriangle, FaCheck, FaRedo } from 'react-icons/fa';

/**
 * Network Simulation Component
 * 
 * This component provides an interactive network simulation where users can:
 * - Configure network devices
 * - Test connectivity between devices
 * - Troubleshoot network issues
 * - Learn about network topologies and protocols
 */
const NetworkSimulation = ({ exerciseType = 'basic', onComplete }) => {
  // Simulation state
  const [devices, setDevices] = useState([
    { id: 1, type: 'router', name: 'Router', ip: '***********', status: 'online', x: 400, y: 100 },
    { id: 2, type: 'switch', name: 'Switch', ip: '***********', status: 'online', x: 400, y: 200 },
    { id: 3, type: 'computer', name: 'PC-1', ip: '************', status: 'online', x: 200, y: 300 },
    { id: 4, type: 'computer', name: 'PC-2', ip: '***********1', status: 'online', x: 400, y: 300 },
    { id: 5, type: 'computer', name: 'PC-3', ip: '************', status: 'online', x: 600, y: 300 }
  ]);
  
  const [connections, setConnections] = useState([
    { from: 1, to: 2 },
    { from: 2, to: 3 },
    { from: 2, to: 4 },
    { from: 2, to: 5 }
  ]);
  
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [pingSource, setPingSource] = useState(null);
  const [pingTarget, setPingTarget] = useState(null);
  const [pingResult, setPingResult] = useState(null);
  const [pingInProgress, setPingInProgress] = useState(false);
  const [simulationMode, setSimulationMode] = useState('explore'); // explore, ping, configure
  const [completedTasks, setCompletedTasks] = useState({
    deviceConfiguration: false,
    pingTest: false,
    troubleshooting: false
  });
  const [currentTask, setCurrentTask] = useState('Explore the network');
  const [showHelp, setShowHelp] = useState(false);
  
  // Exercise-specific configurations
  useEffect(() => {
    if (exerciseType === 'troubleshooting') {
      // Create a network issue for troubleshooting
      setDevices(prev => prev.map(device => 
        device.id === 3 ? { ...device, status: 'offline' } : device
      ));
      setCurrentTask('Find and fix the network issue');
    } else if (exerciseType === 'configuration') {
      // Set up a configuration exercise
      setDevices(prev => prev.map(device => 
        device.id === 5 ? { ...device, ip: '0.0.0.0' } : device
      ));
      setCurrentTask('Configure PC-3 with the correct IP address');
    }
  }, [exerciseType]);
  
  // Check if all tasks are completed
  useEffect(() => {
    if (Object.values(completedTasks).every(task => task)) {
      // All tasks completed
      if (onComplete) {
        onComplete();
      }
    }
  }, [completedTasks, onComplete]);
  
  // Handle device selection
  const handleDeviceClick = (deviceId) => {
    const device = devices.find(d => d.id === deviceId);
    setSelectedDevice(device);
    
    if (simulationMode === 'ping' && !pingSource) {
      setPingSource(device);
    } else if (simulationMode === 'ping' && pingSource && !pingTarget) {
      setPingTarget(device);
    }
  };
  
  // Handle device configuration
  const handleConfigureDevice = (newConfig) => {
    setDevices(prev => prev.map(device => 
      device.id === selectedDevice.id ? { ...device, ...newConfig } : device
    ));
    
    // Check if configuration task is completed
    if (exerciseType === 'configuration' && newConfig.ip === '************') {
      setCompletedTasks(prev => ({ ...prev, deviceConfiguration: true }));
    }
    
    // For troubleshooting exercise
    if (exerciseType === 'troubleshooting' && newConfig.status === 'online') {
      setCompletedTasks(prev => ({ ...prev, troubleshooting: true }));
    }
  };
  
  // Perform ping test
  const handlePing = () => {
    if (!pingSource || !pingTarget) return;
    
    setPingInProgress(true);
    setPingResult(null);
    
    // Simulate network delay
    setTimeout(() => {
      // Check if there's a path between the devices
      const canReach = checkConnectivity(pingSource.id, pingTarget.id);
      const targetOnline = devices.find(d => d.id === pingTarget.id).status === 'online';
      
      setPingResult({
        success: canReach && targetOnline,
        message: canReach && targetOnline 
          ? `Ping successful: ${pingSource.ip} → ${pingTarget.ip}` 
          : `Ping failed: ${pingSource.ip} → ${pingTarget.ip}`
      });
      
      setPingInProgress(false);
      
      // Mark ping task as completed
      if (canReach && targetOnline) {
        setCompletedTasks(prev => ({ ...prev, pingTest: true }));
      }
    }, 1500);
  };
  
  // Check if there's a path between two devices
  const checkConnectivity = (sourceId, targetId) => {
    // Simple connectivity check for the simulation
    // In a real implementation, this would be more sophisticated
    const visited = new Set();
    
    const dfs = (currentId) => {
      if (currentId === targetId) return true;
      if (visited.has(currentId)) return false;
      
      visited.add(currentId);
      
      const connectedDevices = connections
        .filter(conn => conn.from === currentId || conn.to === currentId)
        .map(conn => conn.from === currentId ? conn.to : conn.from);
      
      for (const deviceId of connectedDevices) {
        if (dfs(deviceId)) return true;
      }
      
      return false;
    };
    
    return dfs(sourceId);
  };
  
  // Reset ping test
  const resetPing = () => {
    setPingSource(null);
    setPingTarget(null);
    setPingResult(null);
  };
  
  // Render device icon based on type
  const renderDeviceIcon = (type) => {
    switch (type) {
      case 'router':
        return <FaNetworkWired className="text-blue-400 text-xl" />;
      case 'switch':
        return <FaServer className="text-green-400 text-xl" />;
      case 'computer':
        return <FaDesktop className="text-amber-400 text-xl" />;
      case 'wireless':
        return <FaWifi className="text-purple-400 text-xl" />;
      default:
        return <FaDesktop className="text-gray-400 text-xl" />;
    }
  };
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Network Simulation</h3>
        
        <div className="flex space-x-2">
          <button
            onClick={() => setSimulationMode('explore')}
            className={`px-3 py-1 rounded text-sm ${simulationMode === 'explore' ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            Explore
          </button>
          <button
            onClick={() => {
              setSimulationMode('ping');
              resetPing();
            }}
            className={`px-3 py-1 rounded text-sm ${simulationMode === 'ping' ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            Ping Test
          </button>
          <button
            onClick={() => setSimulationMode('configure')}
            className={`px-3 py-1 rounded text-sm ${simulationMode === 'configure' ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            Configure
          </button>
        </div>
      </div>
      
      <div className="mb-2 text-sm text-gray-400">
        Current Task: {currentTask}
      </div>
      
      {/* Network Diagram */}
      <div className="relative h-80 bg-[#1E293B] rounded-lg mb-4 overflow-hidden border border-gray-700">
        {/* Connections */}
        <svg className="absolute inset-0 w-full h-full">
          {connections.map((conn, index) => {
            const source = devices.find(d => d.id === conn.from);
            const target = devices.find(d => d.id === conn.to);
            
            return (
              <line
                key={index}
                x1={source.x}
                y1={source.y}
                x2={target.x}
                y2={target.y}
                stroke="#4B5563"
                strokeWidth="2"
              />
            );
          })}
          
          {/* Ping Animation */}
          {pingInProgress && pingSource && pingTarget && (
            <circle
              cx={pingSource.x}
              cy={pingSource.y}
              r="5"
              fill="#38BDF8"
            >
              <animate
                attributeName="cx"
                from={pingSource.x}
                to={pingTarget.x}
                dur="1.5s"
                repeatCount="1"
              />
              <animate
                attributeName="cy"
                from={pingSource.y}
                to={pingTarget.y}
                dur="1.5s"
                repeatCount="1"
              />
            </circle>
          )}
        </svg>
        
        {/* Devices */}
        {devices.map(device => (
          <div
            key={device.id}
            className={`absolute w-16 h-16 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center justify-center cursor-pointer transition-all ${
              selectedDevice?.id === device.id ? 'ring-2 ring-primary' : ''
            } ${
              pingSource?.id === device.id ? 'ring-2 ring-blue-500' : ''
            } ${
              pingTarget?.id === device.id ? 'ring-2 ring-green-500' : ''
            }`}
            style={{ left: device.x, top: device.y }}
            onClick={() => handleDeviceClick(device.id)}
          >
            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
              device.status === 'online' ? 'bg-[#0F172A]' : 'bg-red-900/30'
            }`}>
              {renderDeviceIcon(device.type)}
              {device.status === 'offline' && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                  <FaExclamationTriangle className="text-white text-xs" />
                </div>
              )}
            </div>
            <div className="text-xs mt-1 text-center">
              <div className="font-bold">{device.name}</div>
              <div className="text-gray-400">{device.ip}</div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Control Panel */}
      <div className="bg-[#1E293B] p-4 rounded-lg">
        {simulationMode === 'explore' && selectedDevice && (
          <div>
            <h4 className="font-bold mb-2">Device Information</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-400">Name:</div>
              <div>{selectedDevice.name}</div>
              <div className="text-gray-400">Type:</div>
              <div>{selectedDevice.type}</div>
              <div className="text-gray-400">IP Address:</div>
              <div>{selectedDevice.ip}</div>
              <div className="text-gray-400">Status:</div>
              <div className={selectedDevice.status === 'online' ? 'text-green-400' : 'text-red-400'}>
                {selectedDevice.status}
              </div>
            </div>
          </div>
        )}
        
        {simulationMode === 'ping' && (
          <div>
            <h4 className="font-bold mb-2">Ping Test</h4>
            <div className="flex items-center gap-4 mb-4">
              <div>
                <div className="text-xs text-gray-400 mb-1">Source</div>
                <div className="bg-[#0F172A] px-3 py-2 rounded border border-gray-700 min-w-[120px]">
                  {pingSource ? pingSource.name : 'Select a device'}
                </div>
              </div>
              <div className="text-gray-400">→</div>
              <div>
                <div className="text-xs text-gray-400 mb-1">Destination</div>
                <div className="bg-[#0F172A] px-3 py-2 rounded border border-gray-700 min-w-[120px]">
                  {pingTarget ? pingTarget.name : 'Select a device'}
                </div>
              </div>
              <div className="ml-auto">
                <button
                  onClick={handlePing}
                  disabled={!pingSource || !pingTarget || pingInProgress}
                  className={`px-4 py-2 rounded ${
                    !pingSource || !pingTarget || pingInProgress
                      ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                      : 'bg-primary text-black'
                  }`}
                >
                  {pingInProgress ? 'Pinging...' : 'Ping'}
                </button>
                <button
                  onClick={resetPing}
                  className="ml-2 px-4 py-2 rounded bg-[#0F172A] text-gray-400 hover:bg-gray-800"
                >
                  <FaRedo />
                </button>
              </div>
            </div>
            
            {pingResult && (
              <div className={`p-3 rounded ${
                pingResult.success ? 'bg-green-900/30 text-green-400' : 'bg-red-900/30 text-red-400'
              }`}>
                {pingResult.success ? <FaCheck className="inline mr-2" /> : <FaExclamationTriangle className="inline mr-2" />}
                {pingResult.message}
              </div>
            )}
          </div>
        )}
        
        {simulationMode === 'configure' && selectedDevice && (
          <div>
            <h4 className="font-bold mb-2">Configure Device</h4>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="text-xs text-gray-400 block mb-1">IP Address</label>
                <input
                  type="text"
                  value={selectedDevice.ip}
                  onChange={(e) => handleConfigureDevice({ ip: e.target.value })}
                  className="w-full bg-[#0F172A] px-3 py-2 rounded border border-gray-700"
                />
              </div>
              <div>
                <label className="text-xs text-gray-400 block mb-1">Status</label>
                <select
                  value={selectedDevice.status}
                  onChange={(e) => handleConfigureDevice({ status: e.target.value })}
                  className="w-full bg-[#0F172A] px-3 py-2 rounded border border-gray-700"
                >
                  <option value="online">Online</option>
                  <option value="offline">Offline</option>
                </select>
              </div>
            </div>
            
            <button
              onClick={() => setSelectedDevice(null)}
              className="px-4 py-2 rounded bg-primary text-black"
            >
              Apply Changes
            </button>
          </div>
        )}
      </div>
      
      {/* Task Progress */}
      <div className="mt-4 bg-[#0F172A] p-3 rounded-lg border border-gray-700">
        <h4 className="font-bold mb-2 text-sm">Task Progress</h4>
        <div className="space-y-2">
          {Object.entries(completedTasks).map(([task, completed]) => (
            <div key={task} className="flex items-center">
              <div className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                completed ? 'bg-green-500 text-white' : 'bg-gray-700 text-gray-400'
              }`}>
                {completed ? <FaCheck className="text-xs" /> : null}
              </div>
              <span className="text-sm">
                {task === 'deviceConfiguration' && 'Configure device'}
                {task === 'pingTest' && 'Test connectivity'}
                {task === 'troubleshooting' && 'Fix network issue'}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NetworkSimulation;
