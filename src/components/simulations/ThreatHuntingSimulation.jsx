import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaS<PERSON>eldAlt, FaExclamation<PERSON><PERSON>gle, <PERSON>aCheck } from 'react-icons/fa';

/**
 * Threat Hunting Simulation Component
 * Interactive threat hunting exercises with hypothesis testing and IOC analysis
 */
const ThreatHuntingSimulation = ({ scenarioType = 'apt', onComplete }) => {
  const [currentHypothesis, setCurrentHypothesis] = useState('');
  const [huntingPhase, setHuntingPhase] = useState('planning'); // planning, investigation, analysis, reporting
  const [findings, setFindings] = useState([]);
  const [iocs, setIocs] = useState([]);
  const [selectedTTP, setSelectedTTP] = useState(null);
  
  // MITRE ATT&CK TTPs for hunting scenarios
  const attackTTPs = {
    'T1566': { name: 'Phishing', tactic: 'Initial Access', description: 'Adversaries may send phishing messages to gain access' },
    'T1055': { name: 'Process Injection', tactic: 'Defense Evasion', description: 'Adversaries may inject code into processes' },
    'T1003': { name: 'OS Credential Dumping', tactic: 'Credential Access', description: 'Adversaries may attempt to dump credentials' },
    'T1021': { name: 'Remote Services', tactic: 'Lateral Movement', description: 'Adversaries may use valid accounts to log into a service' }
  };
  
  // Sample IOCs for investigation
  const sampleIOCs = [
    { type: 'IP', value: '**************', confidence: 'High', description: 'Known C2 server' },
    { type: 'Domain', value: 'evil-update.com', confidence: 'Medium', description: 'Suspicious domain' },
    { type: 'Hash', value: 'a1b2c3d4e5f6...', confidence: 'High', description: 'Malware sample' },
    { type: 'Process', value: 'svchost.exe', confidence: 'Low', description: 'Legitimate process, check context' }
  ];
  
  // Generate hunting hypothesis
  const generateHypothesis = () => {
    const hypotheses = [
      'Adversaries are using legitimate system tools for persistence',
      'Unusual network connections indicate C2 communication',
      'Process injection techniques are being used to evade detection',
      'Credential harvesting activity is occurring during off-hours'
    ];
    
    const randomHypothesis = hypotheses[Math.floor(Math.random() * hypotheses.length)];
    setCurrentHypothesis(randomHypothesis);
  };
  
  // Test hypothesis with simulated data
  const testHypothesis = () => {
    const mockFindings = [
      { timestamp: '2024-01-15 14:30:22', event: 'Suspicious PowerShell execution', severity: 'Medium' },
      { timestamp: '2024-01-15 14:32:15', event: 'Outbound connection to known malicious IP', severity: 'High' },
      { timestamp: '2024-01-15 14:35:08', event: 'Process hollowing detected', severity: 'Critical' }
    ];
    
    setFindings(mockFindings);
    setHuntingPhase('analysis');
  };
  
  return (
    <div className="space-y-6">
      {/* Threat Hunting Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-2 flex items-center gap-2">
          <FaBullseye />
          Threat Hunting Operations Center
        </h2>
        <p className="text-blue-100">Proactive threat detection and hypothesis-driven investigations</p>
      </div>
      
      {/* Hunting Phase Indicator */}
      <div className="flex items-center justify-center space-x-4">
        {['planning', 'investigation', 'analysis', 'reporting'].map((phase, index) => (
          <div key={phase} className="flex items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
              huntingPhase === phase ? 'bg-blue-500' : 
              ['planning', 'investigation', 'analysis', 'reporting'].indexOf(huntingPhase) > index ? 'bg-green-500' : 'bg-gray-300'
            }`}>
              {['planning', 'investigation', 'analysis', 'reporting'].indexOf(huntingPhase) > index ? <FaCheck /> : index + 1}
            </div>
            <span className="ml-2 text-sm font-medium capitalize">{phase}</span>
            {index < 3 && <div className="w-8 h-1 bg-gray-300 mx-4"></div>}
          </div>
        ))}
      </div>
      
      {/* Hypothesis Generation */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
          <FaSearch />
          Hypothesis Development
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Current Hypothesis:</label>
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              {currentHypothesis || 'No hypothesis generated yet'}
            </div>
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={generateHypothesis}
              className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center gap-2"
            >
              Generate Hypothesis
            </button>
            <button
              onClick={testHypothesis}
              disabled={!currentHypothesis}
              className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 disabled:bg-gray-300 flex items-center gap-2"
            >
              <FaEye />
              Test Hypothesis
            </button>
          </div>
        </div>
      </div>
      
      {/* MITRE ATT&CK Integration */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-bold mb-4">MITRE ATT&CK Techniques</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(attackTTPs).map(([id, ttp]) => (
            <div 
              key={id}
              className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                selectedTTP === id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedTTP(id)}
            >
              <div className="font-bold text-sm">{id}: {ttp.name}</div>
              <div className="text-xs text-gray-600 mb-1">{ttp.tactic}</div>
              <div className="text-sm text-gray-700">{ttp.description}</div>
            </div>
          ))}
        </div>
      </div>
      
      {/* IOC Analysis */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-bold mb-4">Indicators of Compromise (IOCs)</h3>
        <div className="space-y-3">
          {sampleIOCs.map((ioc, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  ioc.type === 'IP' ? 'bg-red-100 text-red-800' :
                  ioc.type === 'Domain' ? 'bg-yellow-100 text-yellow-800' :
                  ioc.type === 'Hash' ? 'bg-blue-100 text-blue-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {ioc.type}
                </span>
                <code className="text-sm font-mono">{ioc.value}</code>
                <span className="text-sm text-gray-600">{ioc.description}</span>
              </div>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                ioc.confidence === 'High' ? 'bg-red-100 text-red-800' :
                ioc.confidence === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {ioc.confidence}
              </span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Investigation Findings */}
      {findings.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
            <FaExclamationTriangle className="text-orange-500" />
            Investigation Findings
          </h3>
          <div className="space-y-3">
            {findings.map((finding, index) => (
              <div key={index} className={`p-3 rounded-lg border-l-4 ${
                finding.severity === 'Critical' ? 'border-red-500 bg-red-50' :
                finding.severity === 'High' ? 'border-orange-500 bg-orange-50' :
                'border-yellow-500 bg-yellow-50'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{finding.event}</h4>
                    <p className="text-sm text-gray-600">{finding.timestamp}</p>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    finding.severity === 'Critical' ? 'bg-red-100 text-red-800' :
                    finding.severity === 'High' ? 'bg-orange-100 text-orange-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {finding.severity}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ThreatHuntingSimulation; 