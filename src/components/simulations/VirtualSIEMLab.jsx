import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, FaS<PERSON>ch, FaExclamation<PERSON><PERSON>gle, FaShieldAlt, FaBell, <PERSON>a<PERSON>ye, FaCog } from 'react-icons/fa';

const VirtualSIEMLab = ({ tool = 'splunk', scenario = 'brute_force' }) => {
  const [currentView, setCurrentView] = useState('dashboard');
  const [searchQuery, setSearchQuery] = useState('');
  const [alerts, setAlerts] = useState([]);
  const [logs, setLogs] = useState([]);
  const [activeScenario, setActiveScenario] = useState(scenario);

  // Security scenarios with real Windows event data
  const securityScenarios = {
    brute_force: {
      title: 'Brute Force Attack Detection',
      description: 'Multiple failed login attempts followed by successful authentication',
      eventLogs: [
        { time: '2024-01-15 14:30:22', host: 'DC-01', event_id: 4625, level: 'WARNING', source_ip: '************', account: 'administrator', message: 'Failed login attempt' },
        { time: '2024-01-15 14:30:24', host: 'DC-01', event_id: 4625, level: 'WARNING', source_ip: '************', account: 'admin', message: 'Failed login attempt' },
        { time: '2024-01-15 14:30:26', host: 'DC-01', event_id: 4625, level: 'WARNING', source_ip: '************', account: 'root', message: 'Failed login attempt' },
        { time: '2024-01-15 14:30:35', host: 'DC-01', event_id: 4624, level: 'INFO', source_ip: '************', account: 'administrator', message: 'Successful login' }
      ],
      correlationRules: [
        'More than 5 failed logins from same IP in 60 seconds',
        'Successful login after multiple failures from same source'
      ],
      expectedAlerts: [
        { severity: 'HIGH', title: 'Brute Force Attack Detected', description: 'Multiple failed login attempts from ************' },
        { severity: 'MEDIUM', title: 'Successful Login After Failures', description: 'Administrator account compromised after brute force' }
      ]
    },
    malware_execution: {
      title: 'Malware Execution Detection',
      description: 'Suspicious process execution and network communication',
      eventLogs: [
        { time: '2024-01-15 16:22:15', host: 'WS-001', event_id: 1, level: 'INFO', process: 'update.exe', parent: 'explorer.exe', message: 'Process creation' },
        { time: '2024-01-15 16:22:18', host: 'WS-001', event_id: 3, level: 'INFO', process: 'update.exe', dest_ip: '**************', dest_port: 443, message: 'Network connection' },
        { time: '2024-01-15 16:22:20', host: 'WS-001', event_id: 11, level: 'INFO', process: 'update.exe', file_path: 'C:\\Users\\<USER>\\winlogon.exe', message: 'File created' },
        { time: '2024-01-15 16:22:25', host: 'WS-001', event_id: 13, level: 'INFO', process: 'update.exe', reg_key: 'HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\\Update', message: 'Registry modified' }
      ],
      correlationRules: [
        'Process execution from temp directory',
        'Outbound connection to suspicious IP',
        'Registry run key modification for persistence'
      ],
      expectedAlerts: [
        { severity: 'CRITICAL', title: 'Malware Execution Detected', description: 'Suspicious process with C2 communication' },
        { severity: 'HIGH', title: 'Persistence Mechanism Created', description: 'Registry run key modified by suspicious process' }
      ]
    }
  };

  // SIEM tool interfaces
  const toolInterfaces = {
    splunk: {
      name: 'Splunk Enterprise Security',
      color: '#FF6B35',
      searchSyntax: 'index=windows EventCode=4625 | stats count by src_ip',
      dashboards: ['Security Posture', 'Incident Review', 'Risk Analysis', 'Threat Intelligence']
    },
    crowdstrike: {
      name: 'CrowdStrike Falcon',
      color: '#FF0000',
      searchSyntax: 'event_simpleName=ProcessRollup2 | table ComputerName, FileName, CommandLine',
      dashboards: ['Threat Graph', 'Host Timeline', 'Detection Summary', 'Real-time Response']
    },
    qradar: {
      name: 'IBM QRadar',
      color: '#006EB5',
      searchSyntax: 'SELECT * FROM events WHERE "Event Name" = \'Authentication Failure\'',
      dashboards: ['Executive Dashboard', 'Security Intelligence', 'Network Activity', 'Log Activity']
    }
  };

  const currentTool = toolInterfaces[tool];

  // Load scenario data
  useEffect(() => {
    const scenario = securityScenarios[activeScenario];
    setLogs(scenario.eventLogs);
    
    // Generate alerts after delay
    setTimeout(() => {
      setAlerts(scenario.expectedAlerts);
    }, 2000);
  }, [activeScenario]);

  // Render Splunk-like interface
  const renderSplunkInterface = () => (
    <div className="bg-gray-900 text-white min-h-screen">
      {/* Header */}
      <div className="bg-orange-600 px-6 py-3 flex items-center justify-between">
        <h1 className="text-xl font-bold">Splunk&gt; Enterprise Security</h1>
        <div className="flex items-center gap-4">
          <span className="text-sm">Admin User</span>
          <FaCog className="cursor-pointer" />
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-gray-800 px-6 py-2 flex gap-6">
        {['Search & Reporting', 'Security Posture', 'Incident Review', 'Notable Events'].map(tab => (
          <button
            key={tab}
            onClick={() => setCurrentView(tab.toLowerCase().replace(/ /g, '_'))}
            className={`px-3 py-1 rounded ${currentView === tab.toLowerCase().replace(/ /g, '_') ? 'bg-orange-600' : 'hover:bg-gray-700'}`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Search Bar */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex gap-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search: index=windows EventCode=4625 | stats count by src_ip"
            className="flex-1 bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white"
          />
          <button className="bg-orange-600 px-6 py-2 rounded hover:bg-orange-700">
            <FaSearch />
          </button>
        </div>
      </div>

      {/* Content Area */}
      <div className="p-6">
        {currentView === 'search_&_reporting' && renderSearchResults()}
        {currentView === 'security_posture' && renderSecurityDashboard()}
        {currentView === 'incident_review' && renderIncidentReview()}
        {currentView === 'notable_events' && renderNotableEvents()}
      </div>
    </div>
  );

  // Render search results
  const renderSearchResults = () => (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">Search Results</h2>
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-700">
            <tr>
              <th className="px-4 py-2 text-left">Time</th>
              <th className="px-4 py-2 text-left">Host</th>
              <th className="px-4 py-2 text-left">Event ID</th>
              <th className="px-4 py-2 text-left">Level</th>
              <th className="px-4 py-2 text-left">Source IP</th>
              <th className="px-4 py-2 text-left">Message</th>
            </tr>
          </thead>
          <tbody>
            {logs.map((log, index) => (
              <tr key={index} className="border-b border-gray-700 hover:bg-gray-750">
                <td className="px-4 py-2 text-sm">{log.time}</td>
                <td className="px-4 py-2 text-sm">{log.host}</td>
                <td className="px-4 py-2 text-sm">{log.event_id}</td>
                <td className="px-4 py-2">
                  <span className={`px-2 py-1 rounded text-xs ${
                    log.level === 'WARNING' ? 'bg-yellow-600' : 
                    log.level === 'INFO' ? 'bg-blue-600' : 'bg-red-600'
                  }`}>
                    {log.level}
                  </span>
                </td>
                <td className="px-4 py-2 text-sm">{log.source_ip}</td>
                <td className="px-4 py-2 text-sm">{log.message}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Render security dashboard
  const renderSecurityDashboard = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div className="bg-gray-800 p-6 rounded-lg">
        <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
          <FaExclamationTriangle className="text-red-500" />
          Critical Alerts
        </h3>
        <div className="text-3xl font-bold text-red-500">
          {alerts.filter(a => a.severity === 'CRITICAL').length}
        </div>
      </div>

      <div className="bg-gray-800 p-6 rounded-lg">
        <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
          <FaBell className="text-orange-500" />
          High Priority
        </h3>
        <div className="text-3xl font-bold text-orange-500">
          {alerts.filter(a => a.severity === 'HIGH').length}
        </div>
      </div>

      <div className="bg-gray-800 p-6 rounded-lg">
        <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
          <FaEye className="text-blue-500" />
          Total Events
        </h3>
        <div className="text-3xl font-bold text-blue-500">{logs.length}</div>
      </div>

      <div className="col-span-full bg-gray-800 p-6 rounded-lg">
        <h3 className="text-lg font-bold mb-4">Event Timeline</h3>
        <div className="space-y-2">
          {logs.map((log, index) => (
            <div key={index} className="flex items-center gap-4 p-2 bg-gray-700 rounded">
              <span className="text-sm text-gray-400">{log.time}</span>
              <span className="text-sm">{log.host}</span>
              <span className="text-sm text-orange-400">Event {log.event_id}</span>
              <span className="text-sm flex-1">{log.message}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Render notable events
  const renderNotableEvents = () => (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">Notable Events</h2>
      <div className="space-y-3">
        {alerts.map((alert, index) => (
          <div key={index} className={`p-4 rounded-lg border-l-4 ${
            alert.severity === 'CRITICAL' ? 'border-red-500 bg-red-900/20' :
            alert.severity === 'HIGH' ? 'border-orange-500 bg-orange-900/20' :
            'border-yellow-500 bg-yellow-900/20'
          }`}>
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-bold">{alert.title}</h3>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                alert.severity === 'CRITICAL' ? 'bg-red-600 text-white' :
                alert.severity === 'HIGH' ? 'bg-orange-600 text-white' :
                'bg-yellow-600 text-black'
              }`}>
                {alert.severity}
              </span>
            </div>
            <p className="text-gray-300">{alert.description}</p>
            <div className="mt-2 flex gap-2">
              <button className="bg-blue-600 px-3 py-1 rounded text-sm hover:bg-blue-700">
                Investigate
              </button>
              <button className="bg-gray-600 px-3 py-1 rounded text-sm hover:bg-gray-700">
                Assign
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Lab Header */}
      <div className="bg-blue-900 text-white p-6 rounded-lg">
        <h2 className="text-2xl font-bold mb-2">Virtual SIEM Laboratory</h2>
        <p className="text-blue-100">Hands-on experience with enterprise security tools and real-world scenarios</p>
      </div>

      {/* Tool and Scenario Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="font-bold mb-3">Select SIEM Tool</h3>
          <div className="space-y-2">
            {Object.entries(toolInterfaces).map(([key, toolInfo]) => (
              <button
                key={key}
                onClick={() => setCurrentView('search_&_reporting')}
                className={`w-full p-3 rounded-lg border-2 text-left transition-all ${
                  tool === key ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium" style={{color: toolInfo.color}}>
                  {toolInfo.name}
                </div>
                <div className="text-sm text-gray-600">
                  {toolInfo.searchSyntax}
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="font-bold mb-3">Select Security Scenario</h3>
          <div className="space-y-2">
            {Object.entries(securityScenarios).map(([key, scenarioInfo]) => (
              <button
                key={key}
                onClick={() => setActiveScenario(key)}
                className={`w-full p-3 rounded-lg border-2 text-left transition-all ${
                  activeScenario === key ? 'border-orange-500 bg-orange-50' : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium text-orange-800">
                  {scenarioInfo.title}
                </div>
                <div className="text-sm text-gray-600">
                  {scenarioInfo.description}
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* SIEM Interface */}
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        {renderSplunkInterface()}
      </div>

      {/* Learning Objectives */}
      <div className="bg-green-50 rounded-lg border border-green-200 p-6">
        <h3 className="text-lg font-bold text-green-800 mb-4">Learning Objectives</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-green-700 mb-2">SIEM Operations</h4>
            <ul className="text-sm text-green-600 space-y-1">
              <li>• Search and query log data effectively</li>
              <li>• Create and manage security dashboards</li>
              <li>• Develop correlation rules and alerts</li>
              <li>• Investigate security incidents</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-green-700 mb-2">Threat Detection</h4>
            <ul className="text-sm text-green-600 space-y-1">
              <li>• Identify attack patterns in logs</li>
              <li>• Correlate events across multiple sources</li>
              <li>• Understand Windows Event IDs</li>
              <li>• Practice incident response procedures</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VirtualSIEMLab; 