import React, { useState, useEffect } from 'react';
import { FaArrowDown, FaArrowUp, FaCheck, FaTimes, FaRedo, FaInfoCircle } from 'react-icons/fa';

/**
 * OSI Model Simulation Component
 * 
 * This component provides an interactive simulation of data encapsulation and 
 * decapsulation through the OSI model layers.
 */
const OSIModelSimulation = ({ onComplete }) => {
  // OSI Model layers
  const layers = [
    { id: 7, name: 'Application', color: '#0D9488', unit: 'Data', protocol: 'HTTP, FTP, SMTP' },
    { id: 6, name: 'Presentation', color: '#0891B2', unit: 'Data', protocol: 'SSL, TLS, JPEG' },
    { id: 5, name: 'Session', color: '#0284C7', unit: 'Data', protocol: 'NetBIOS, RPC' },
    { id: 4, name: 'Transport', color: '#2563EB', unit: 'Segment/Datagram', protocol: 'TCP, UDP' },
    { id: 3, name: 'Network', color: '#4F46E5', unit: 'Packet', protocol: 'IP, ICMP, OSPF' },
    { id: 2, name: 'Data Link', color: '#7C3AED', unit: 'Frame', protocol: 'Ethernet, PPP, HDLC' },
    { id: 1, name: 'Physical', color: '#9333EA', unit: 'Bits', protocol: 'Ethernet, USB, Bluetooth' }
  ];
  
  // Simulation state
  const [direction, setDirection] = useState('down'); // 'down' for encapsulation, 'up' for decapsulation
  const [currentLayer, setCurrentLayer] = useState(direction === 'down' ? 7 : 1);
  const [animating, setAnimating] = useState(false);
  const [completed, setCompleted] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [dataUnits, setDataUnits] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [quizActive, setQuizActive] = useState(false);
  const [quizQuestion, setQuizQuestion] = useState(null);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [quizResult, setQuizResult] = useState(null);
  const [completedQuestions, setCompletedQuestions] = useState([]);
  
  // Quiz questions
  const questions = [
    {
      id: 1,
      question: "Which layer is responsible for routing and logical addressing?",
      options: ["Transport Layer", "Network Layer", "Data Link Layer", "Session Layer"],
      correctAnswer: 1,
      explanation: "The Network Layer (Layer 3) is responsible for routing and logical addressing using protocols like IP."
    },
    {
      id: 2,
      question: "What is the Protocol Data Unit (PDU) at the Transport Layer?",
      options: ["Frame", "Packet", "Segment", "Bits"],
      correctAnswer: 2,
      explanation: "The Transport Layer (Layer 4) uses segments for TCP or datagrams for UDP as its PDU."
    },
    {
      id: 3,
      question: "Which layer adds the source and destination MAC addresses?",
      options: ["Physical Layer", "Data Link Layer", "Network Layer", "Transport Layer"],
      correctAnswer: 1,
      explanation: "The Data Link Layer (Layer 2) adds MAC addresses to frames for local network delivery."
    },
    {
      id: 4,
      question: "What happens during encapsulation?",
      options: [
        "Data is broken down into smaller units",
        "Headers are removed as data moves up the layers",
        "Headers are added as data moves down the layers",
        "Data is encrypted at each layer"
      ],
      correctAnswer: 2,
      explanation: "During encapsulation, each layer adds its own header information as data moves down the OSI model."
    },
    {
      id: 5,
      question: "Which layer is responsible for end-to-end connections and reliability?",
      options: ["Session Layer", "Transport Layer", "Network Layer", "Application Layer"],
      correctAnswer: 1,
      explanation: "The Transport Layer (Layer 4) handles end-to-end connections and reliability, particularly with TCP."
    }
  ];
  
  // Initialize data units
  useEffect(() => {
    if (direction === 'down') {
      // Start with application data for encapsulation
      setDataUnits([{ layer: 7, color: layers[0].color }]);
    } else {
      // Start with bits for decapsulation
      setDataUnits([{ layer: 1, color: layers[6].color }]);
    }
  }, [direction]);
  
  // Handle animation step
  const handleStep = () => {
    if (animating) return;
    
    setAnimating(true);
    
    if (direction === 'down') {
      // Encapsulation (moving down the OSI model)
      if (currentLayer > 1) {
        const nextLayer = currentLayer - 1;
        const layerIndex = layers.findIndex(l => l.id === nextLayer);
        
        // Add the new layer's data unit
        setTimeout(() => {
          setDataUnits(prev => [
            ...prev,
            { layer: nextLayer, color: layers[layerIndex].color }
          ]);
          setCurrentLayer(nextLayer);
          setCurrentStep(prev => prev + 1);
          setAnimating(false);
        }, 1000);
      } else {
        // Completed encapsulation
        setTimeout(() => {
          setCompleted(true);
          setAnimating(false);
          if (onComplete) {
            onComplete();
          }
        }, 1000);
      }
    } else {
      // Decapsulation (moving up the OSI model)
      if (currentLayer < 7) {
        const nextLayer = currentLayer + 1;
        const layerIndex = layers.findIndex(l => l.id === nextLayer);
        
        // Add the new layer's data unit
        setTimeout(() => {
          setDataUnits(prev => [
            ...prev,
            { layer: nextLayer, color: layers[layerIndex].color }
          ]);
          setCurrentLayer(nextLayer);
          setCurrentStep(prev => prev + 1);
          setAnimating(false);
        }, 1000);
      } else {
        // Completed decapsulation
        setTimeout(() => {
          setCompleted(true);
          setAnimating(false);
          if (onComplete) {
            onComplete();
          }
        }, 1000);
      }
    }
  };
  
  // Reset simulation
  const resetSimulation = () => {
    setCurrentLayer(direction === 'down' ? 7 : 1);
    setAnimating(false);
    setCompleted(false);
    setDataUnits(direction === 'down' ? [{ layer: 7, color: layers[0].color }] : [{ layer: 1, color: layers[6].color }]);
    setCurrentStep(0);
  };
  
  // Change direction
  const changeDirection = (newDirection) => {
    setDirection(newDirection);
    setCurrentLayer(newDirection === 'down' ? 7 : 1);
    setAnimating(false);
    setCompleted(false);
    setDataUnits(newDirection === 'down' ? [{ layer: 7, color: layers[0].color }] : [{ layer: 1, color: layers[6].color }]);
    setCurrentStep(0);
  };
  
  // Start quiz
  const startQuiz = () => {
    // Get a random question that hasn't been completed yet
    const availableQuestions = questions.filter(q => !completedQuestions.includes(q.id));
    
    if (availableQuestions.length === 0) {
      // All questions completed, reset
      setCompletedQuestions([]);
      setQuizQuestion(questions[Math.floor(Math.random() * questions.length)]);
    } else {
      setQuizQuestion(availableQuestions[Math.floor(Math.random() * availableQuestions.length)]);
    }
    
    setQuizActive(true);
    setSelectedAnswer(null);
    setQuizResult(null);
  };
  
  // Handle answer selection
  const handleAnswerSelect = (index) => {
    setSelectedAnswer(index);
    
    const isCorrect = index === quizQuestion.correctAnswer;
    setQuizResult({
      correct: isCorrect,
      explanation: quizQuestion.explanation
    });
    
    if (isCorrect) {
      setCompletedQuestions(prev => [...prev, quizQuestion.id]);
    }
  };
  
  // Get current layer info
  const getCurrentLayerInfo = () => {
    return layers.find(l => l.id === currentLayer);
  };
  
  // Calculate completion percentage
  const completionPercentage = direction === 'down'
    ? Math.round(((7 - currentLayer) / 6) * 100)
    : Math.round(((currentLayer - 1) / 6) * 100);
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">OSI Model Simulation</h3>
        
        <div className="flex space-x-2">
          <button
            onClick={() => changeDirection('down')}
            className={`px-3 py-1 rounded text-sm ${direction === 'down' ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            Encapsulation
          </button>
          <button
            onClick={() => changeDirection('up')}
            className={`px-3 py-1 rounded text-sm ${direction === 'up' ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            Decapsulation
          </button>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className={`px-3 py-1 rounded text-sm ${showDetails ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
        </div>
      </div>
      
      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-400 mb-1">
          <span>Step {currentStep} of 6</span>
          <span>{completionPercentage}% Complete</span>
        </div>
        <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
          <div 
            className="h-full bg-primary transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
      </div>
      
      {/* OSI Model Visualization */}
      <div className="relative bg-[#1E293B] rounded-lg p-4 mb-4 overflow-hidden border border-gray-700">
        <div className="flex flex-col space-y-1">
          {layers.map(layer => (
            <div 
              key={layer.id}
              className={`flex items-center p-2 rounded transition-all ${
                currentLayer === layer.id ? 'bg-[#0F172A] border border-primary/50' : ''
              }`}
              style={{ borderLeft: `4px solid ${layer.color}` }}
            >
              <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: layer.color }}>
                {layer.id}
              </div>
              <div className="flex-1">
                <div className="font-bold">{layer.name} Layer</div>
                {showDetails && (
                  <div className="text-xs text-gray-400 mt-1">
                    <div>PDU: {layer.unit}</div>
                    <div>Protocols: {layer.protocol}</div>
                  </div>
                )}
              </div>
              <div className="w-24 text-right">
                {currentLayer === layer.id && (
                  <span className="text-primary text-sm">Current</span>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {/* Data Units Visualization */}
        <div className="absolute right-8 top-0 bottom-0 w-20 flex flex-col items-center justify-center">
          <div className="h-full w-0.5 bg-gray-700"></div>
          
          {direction === 'down' && (
            <FaArrowDown className="absolute top-4 text-gray-400" />
          )}
          
          {direction === 'up' && (
            <FaArrowUp className="absolute bottom-4 text-gray-400" />
          )}
          
          {dataUnits.map((unit, index) => {
            const layerPosition = (unit.layer - 1) / 6;
            const top = direction === 'down'
              ? `${layerPosition * 100}%`
              : `${(1 - layerPosition) * 100}%`;
            
            return (
              <div
                key={index}
                className="absolute flex items-center justify-center"
                style={{ 
                  top, 
                  transform: 'translateY(-50%)',
                  transition: 'top 1s ease'
                }}
              >
                <div 
                  className="w-12 h-12 rounded flex items-center justify-center text-white text-xs"
                  style={{ backgroundColor: unit.color }}
                >
                  {layers.find(l => l.id === unit.layer).unit}
                </div>
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Current Layer Information */}
      <div className="bg-[#0F172A] p-4 rounded-lg mb-4 border border-gray-700">
        <h4 className="font-bold mb-2">
          {direction === 'down' ? 'Encapsulation' : 'Decapsulation'} Process
        </h4>
        <p className="text-sm text-gray-300 mb-3">
          {direction === 'down'
            ? `At the ${getCurrentLayerInfo()?.name} Layer, data is ${currentLayer > 1 ? 'encapsulated with header information before being passed down.' : 'converted to bits for transmission.'}`
            : `At the ${getCurrentLayerInfo()?.name} Layer, ${currentLayer < 7 ? 'headers are processed and removed before passing data up.' : 'the application receives the original data.'}`
          }
        </p>
        
        {showDetails && (
          <div className="bg-[#1E293B] p-3 rounded text-sm">
            <h5 className="font-bold mb-1">Layer {currentLayer} Functions:</h5>
            <ul className="list-disc list-inside text-gray-300 space-y-1">
              {currentLayer === 7 && (
                <>
                  <li>Provides network services to applications</li>
                  <li>Implements protocols like HTTP, FTP, SMTP</li>
                  <li>Handles user authentication and data syntax</li>
                </>
              )}
              {currentLayer === 6 && (
                <>
                  <li>Translates data between application and network formats</li>
                  <li>Handles encryption, compression, and format conversion</li>
                  <li>Ensures data is readable by the receiving system</li>
                </>
              )}
              {currentLayer === 5 && (
                <>
                  <li>Establishes, manages, and terminates sessions</li>
                  <li>Handles session checkpointing and recovery</li>
                  <li>Controls dialog between devices</li>
                </>
              )}
              {currentLayer === 4 && (
                <>
                  <li>Provides end-to-end data transport</li>
                  <li>Handles segmentation, flow control, and error correction</li>
                  <li>Implements TCP (reliable) and UDP (unreliable) protocols</li>
                </>
              )}
              {currentLayer === 3 && (
                <>
                  <li>Routes data packets between networks</li>
                  <li>Handles logical addressing (IP addresses)</li>
                  <li>Performs fragmentation and reassembly of packets</li>
                </>
              )}
              {currentLayer === 2 && (
                <>
                  <li>Provides node-to-node data transfer</li>
                  <li>Handles physical addressing (MAC addresses)</li>
                  <li>Detects and potentially corrects errors from Physical layer</li>
                </>
              )}
              {currentLayer === 1 && (
                <>
                  <li>Transmits raw bit stream over physical medium</li>
                  <li>Defines electrical, mechanical, and timing specifications</li>
                  <li>Handles physical connections and topology</li>
                </>
              )}
            </ul>
          </div>
        )}
      </div>
      
      {/* Controls */}
      <div className="flex justify-between">
        <button
          onClick={resetSimulation}
          className="px-4 py-2 rounded bg-[#1E293B] text-white hover:bg-[#334155]"
        >
          <FaRedo className="inline mr-1" /> Reset
        </button>
        
        <div>
          <button
            onClick={startQuiz}
            className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 mr-2"
          >
            <FaInfoCircle className="inline mr-1" /> Test Knowledge
          </button>
          
          <button
            onClick={handleStep}
            disabled={animating || completed}
            className={`px-4 py-2 rounded ${
              animating || completed
                ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                : 'bg-primary text-black hover:bg-primary-hover'
            }`}
          >
            {completed ? 'Completed' : animating ? 'Processing...' : 'Next Step'}
          </button>
        </div>
      </div>
      
      {/* Quiz Modal */}
      {quizActive && quizQuestion && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-[#0F172A] rounded-lg p-6 max-w-2xl w-full border border-gray-700">
            <h3 className="text-xl font-bold mb-4">OSI Model Quiz</h3>
            
            <div className="mb-6">
              <h4 className="font-bold mb-3">{quizQuestion.question}</h4>
              
              <div className="space-y-2">
                {quizQuestion.options.map((option, index) => (
                  <div
                    key={index}
                    onClick={() => quizResult === null && handleAnswerSelect(index)}
                    className={`p-3 rounded-lg cursor-pointer border ${
                      selectedAnswer === index
                        ? quizResult?.correct
                          ? 'bg-green-900/20 border-green-500'
                          : 'bg-red-900/20 border-red-500'
                        : quizResult !== null && index === quizQuestion.correctAnswer
                          ? 'bg-green-900/20 border-green-500'
                          : 'bg-[#1E293B] border-gray-700 hover:border-primary'
                    }`}
                  >
                    {option}
                  </div>
                ))}
              </div>
              
              {quizResult && (
                <div className={`mt-4 p-4 rounded-lg ${
                  quizResult.correct ? 'bg-green-900/20 border border-green-500' : 'bg-red-900/20 border border-red-500'
                }`}>
                  <div className="flex items-center mb-2">
                    {quizResult.correct
                      ? <FaCheck className="text-green-500 mr-2" />
                      : <FaTimes className="text-red-500 mr-2" />
                    }
                    <span className="font-bold">{quizResult.correct ? 'Correct!' : 'Incorrect'}</span>
                  </div>
                  <p>{quizResult.explanation}</p>
                </div>
              )}
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={() => setQuizActive(false)}
                className="px-4 py-2 rounded bg-primary text-black"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OSIModelSimulation;
