/* Dashboard UI Enhancements */

/* Enhanced Avatar Styling */
.dashboard-avatar {
  position: relative;
  overflow: hidden;
  border-radius: 50%;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  transition: all 0.3s ease;
}

.dashboard-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dashboard-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* Enhanced <PERSON><PERSON> Styles */
.btn-continue {
  background: linear-gradient(135deg, #88cc14 0%, #7ab811 100%);
  color: #000;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(136, 204, 20, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-continue:hover {
  background: linear-gradient(135deg, #7ab811 0%, #6ba00f 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(136, 204, 20, 0.4);
}

.btn-continue:active {
  transform: translateY(0);
}

.btn-continue::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-continue:hover::before {
  left: 100%;
}

.btn-continue:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(136, 204, 20, 0.2);
}

/* Enhanced Card Styles */
.dashboard-card {
  transition: all 0.3s ease;
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #88cc14, #7ab811, #6ba00f);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dashboard-card:hover::before {
  opacity: 1;
}

/* Progress Bar Enhancements */
.progress-bar {
  background: linear-gradient(90deg, #e5e7eb, #f3f4f6);
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  background: linear-gradient(90deg, #88cc14, #7ab811);
  height: 100%;
  border-radius: 1rem;
  transition: width 0.8s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced Notification Badge */
.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse-notification 2s infinite;
}

@keyframes pulse-notification {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* Enhanced Search Input */
.dashboard-search {
  transition: all 0.3s ease;
  border-radius: 0.75rem;
}

.dashboard-search:focus {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Enhanced Tab Buttons */
.tab-button {
  transition: all 0.3s ease;
  border-radius: 0.75rem;
  position: relative;
  overflow: hidden;
}

.tab-button.active {
  background: linear-gradient(135deg, #88cc14, #7ab811);
  color: #000;
  font-weight: 600;
}

.tab-button:not(.active):hover {
  background: rgba(136, 204, 20, 0.1);
  transform: translateY(-1px);
}

/* Enhanced Stats Cards */
.stats-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: rgba(136, 204, 20, 0.3);
}

/* Enhanced Icon Containers */
.icon-container {
  background: linear-gradient(135deg, rgba(136, 204, 20, 0.1), rgba(122, 184, 17, 0.1));
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.icon-container:hover {
  background: linear-gradient(135deg, rgba(136, 204, 20, 0.2), rgba(122, 184, 17, 0.2));
  transform: scale(1.05);
}

/* Enhanced Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Enhanced Dropdown Menus */
.dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 1rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: dropdown-appear 0.3s ease;
}

@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dark mode adjustments */
.dark .dropdown-menu {
  background: rgba(31, 41, 55, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .stats-card {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.8), rgba(17, 24, 39, 0.8));
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .progress-bar {
  background: linear-gradient(90deg, #374151, #4b5563);
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .btn-continue {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .dashboard-card {
    border-radius: 0.75rem;
  }
  
  .stats-card {
    border-radius: 0.75rem;
  }
}

/* Accessibility improvements */
.btn-continue:focus,
.tab-button:focus,
.dashboard-search:focus {
  outline: 2px solid #88cc14;
  outline-offset: 2px;
}

/* Smooth scrolling for dashboard content */
.dashboard-content {
  scroll-behavior: smooth;
}

/* Enhanced transitions for all interactive elements */
* {
  transition-property: transform, box-shadow, background-color, border-color, opacity;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}
