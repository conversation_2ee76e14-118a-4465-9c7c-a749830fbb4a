import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaUser, FaEdit, FaSave, FaTimes, FaCamera, FaUpload, FaCog,
  FaEye, FaEyeSlash, FaLock, FaPhone, FaEnvelope, FaMapMarkerAlt,
  FaGlobe, FaLinkedin, FaGithub, FaTwitter, FaInstagram, FaFacebook,
  FaBell, FaUserShield, FaPalette, FaLanguage, FaKey, FaTrash
} from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { supabase } from '../../lib/supabase';
import { ensureUserProfile, updateUserProfile, getUserSettings, updateUserSettings, logUserActivity, validateProfileData } from '../../utils/profileUtils';
import { avatarService } from '../../services/avatarService';
import toast from 'react-hot-toast';

/**
 * Enhanced User Profile Component
 *
 * Complete profile management with avatar upload, settings, and social links
 */
const EnhancedUserProfile = () => {
  const { user, profile, refreshProfile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const fileInputRef = useRef(null);
  
  // State management
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  // Profile form state
  const [profileForm, setProfileForm] = useState({
    full_name: '',
    username: '',
    bio: '',
    location: '',
    country: '',
    website: '',
    phone_number: '',
    linkedin_url: '',
    github_url: '',
    twitter_url: '',
    instagram_url: '',
    facebook_url: ''
  });
  
  // Settings form state
  const [settingsForm, setSettingsForm] = useState({
    email_notifications: true,
    push_notifications: true,
    marketing_emails: false,
    profile_visibility: 'public',
    show_email: false,
    show_phone: false,
    show_location: true,
    show_social_links: true,
    theme_preference: 'system',
    preferred_language: 'en'
  });
  
  // Avatar state
  const [avatarPreview, setAvatarPreview] = useState(null);
  const [currentAvatar, setCurrentAvatar] = useState(null);

  // Initialize form data and ensure profile exists
  useEffect(() => {
    const initializeProfile = async () => {
      if (user && !profile) {
        console.log('No profile found, ensuring profile exists...');
        const ensuredProfile = await ensureUserProfile(user);
        if (ensuredProfile && refreshProfile) {
          await refreshProfile();
        }
      }
    };

    initializeProfile();
  }, [user, profile, refreshProfile]);

  // Initialize form data
  useEffect(() => {
    if (profile) {
      setProfileForm({
        full_name: profile.full_name || '',
        username: profile.username || '',
        bio: profile.bio || '',
        location: profile.location || '',
        country: profile.country || '',
        website: profile.website || '',
        phone_number: profile.phone_number || '',
        linkedin_url: profile.linkedin_url || '',
        github_url: profile.github_url || '',
        twitter_url: profile.twitter_url || '',
        instagram_url: profile.instagram_url || '',
        facebook_url: profile.facebook_url || ''
      });

      setCurrentAvatar(profile.avatar_url);

      // Load user settings
      loadUserSettings();
    }
  }, [profile]);

  // Load user settings
  const loadUserSettings = async () => {
    if (!user) return;

    try {
      const data = await getUserSettings(user.id);

      if (data) {
        setSettingsForm({
          email_notifications: data.email_notifications?.achievements ?? true,
          push_notifications: data.push_notifications?.achievements ?? true,
          marketing_emails: data.email_notifications?.marketing ?? false,
          profile_visibility: data.privacy_settings?.profile_visibility ?? 'public',
          show_email: data.privacy_settings?.show_email ?? false,
          show_phone: data.privacy_settings?.show_phone ?? false,
          show_location: data.privacy_settings?.show_location ?? true,
          show_social_links: data.privacy_settings?.show_social_links ?? true,
          theme_preference: data.ui_preferences?.theme ?? 'system',
          preferred_language: data.ui_preferences?.language ?? 'en'
        });
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    }
  };

  // Handle avatar upload
  const handleAvatarUpload = async (event) => {
    const file = event.target.files[0];
    if (!file || !user) return;

    setIsUploading(true);

    try {
      // Create preview
      const preview = await avatarService.createPreview(file);
      setAvatarPreview(preview);

      // Upload avatar using the service
      const result = await avatarService.completeAvatarUpload(user.id, file);

      if (result.success) {
        setCurrentAvatar(result.data.publicUrl);
        if (refreshProfile) {
          await refreshProfile();
        }

        // Log activity
        await logUserActivity(user.id, 'avatar_upload', 'Updated profile avatar');
      }

    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('Failed to upload avatar');
    } finally {
      setIsUploading(false);
      setAvatarPreview(null);
    }
  };

  // Handle profile save
  const handleSaveProfile = async () => {
    if (!user) return;

    setIsSaving(true);

    try {
      // Validate profile data
      const validationErrors = validateProfileData(profileForm);
      if (validationErrors.length > 0) {
        toast.error(validationErrors[0]);
        setIsSaving(false);
        return;
      }

      // Update profile using utility function
      const result = await updateUserProfile(user.id, profileForm);

      if (result.success) {
        if (refreshProfile) {
          await refreshProfile();
        }
        setIsEditing(false);
        toast.success('Profile updated successfully!');

        // Log activity
        await logUserActivity(
          user.id,
          'profile_update',
          'Updated profile information',
          { updated_fields: Object.keys(profileForm) }
        );
      } else {
        toast.error(result.error || 'Failed to update profile');
      }

    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle settings save
  const handleSaveSettings = async () => {
    if (!user) return;

    setIsSaving(true);

    try {
      const settingsData = {
        email_notifications: {
          achievements: settingsForm.email_notifications,
          challenges: settingsForm.email_notifications,
          updates: settingsForm.email_notifications,
          marketing: settingsForm.marketing_emails
        },
        push_notifications: {
          achievements: settingsForm.push_notifications,
          challenges: settingsForm.push_notifications,
          reminders: settingsForm.push_notifications
        },
        privacy_settings: {
          profile_visibility: settingsForm.profile_visibility,
          show_email: settingsForm.show_email,
          show_phone: settingsForm.show_phone,
          show_location: settingsForm.show_location,
          show_social_links: settingsForm.show_social_links
        },
        ui_preferences: {
          theme: settingsForm.theme_preference,
          language: settingsForm.preferred_language
        }
      };

      const result = await updateUserSettings(user.id, settingsData);

      if (result.success) {
        toast.success('Settings updated successfully!');

        // Log activity
        await logUserActivity(user.id, 'settings_update', 'Updated account settings');
      } else {
        toast.error(result.error || 'Failed to update settings');
      }

    } catch (error) {
      console.error('Error updating settings:', error);
      toast.error('Failed to update settings');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle input changes
  const handleProfileChange = (field, value) => {
    setProfileForm(prev => ({ ...prev, [field]: value }));
  };

  const handleSettingsChange = (field, value) => {
    setSettingsForm(prev => ({ ...prev, [field]: value }));
  };

  // Tab configuration
  const tabs = [
    { id: 'profile', label: 'Profile', icon: FaUser },
    { id: 'settings', label: 'Settings', icon: FaCog },
    { id: 'security', label: 'Security', icon: FaUserShield },
    { id: 'privacy', label: 'Privacy', icon: FaEye }
  ];

  // Show loading state
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show error state if profile failed to load
  if (!profile) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className={`text-center p-8 rounded-lg shadow-lg ${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'}`}>
          <h2 className="text-2xl font-bold mb-4">Profile Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            We couldn't load your profile. This might be because your profile hasn't been created yet.
          </p>
          <button
            onClick={async () => {
              const ensuredProfile = await ensureUserProfile(user);
              if (ensuredProfile && refreshProfile) {
                await refreshProfile();
              }
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200"
          >
            Create Profile
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg overflow-hidden`}>
        {/* Cover Image */}
        <div className="h-48 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 relative">
          <div className="absolute inset-0 bg-black/20"></div>
        </div>
        
        {/* Profile Header */}
        <div className="relative px-6 pb-6">
          {/* Avatar */}
          <div className="absolute -top-16 left-6">
            <div className="relative">
              <div className={`w-32 h-32 rounded-full border-4 ${darkMode ? 'border-gray-800' : 'border-white'} overflow-hidden bg-gray-300 shadow-lg`}>
                {avatarPreview ? (
                  <img src={avatarPreview} alt="Avatar Preview" className="w-full h-full object-cover" />
                ) : currentAvatar ? (
                  <img src={currentAvatar} alt="Profile" className="w-full h-full object-cover" />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-700">
                    <FaUser className="text-4xl text-gray-400" />
                  </div>
                )}
                
                {/* Upload overlay */}
                {isUploading && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  </div>
                )}
              </div>
              
              {/* Camera button */}
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
                className="absolute bottom-2 right-2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-colors duration-200 disabled:opacity-50"
              >
                <FaCamera className="text-sm" />
              </button>
              
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleAvatarUpload}
                className="hidden"
              />
            </div>
          </div>
          
          {/* Profile Info */}
          <div className="pt-20 flex justify-between items-start">
            <div>
              <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {profileForm.full_name || profileForm.username || 'User'}
              </h1>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                @{profileForm.username || user?.email?.split('@')[0]}
              </p>
              {profileForm.bio && (
                <p className={`mt-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {profileForm.bio}
                </p>
              )}
            </div>
            
            {/* Action buttons */}
            <div className="flex gap-2">
              <button
                onClick={() => setIsEditing(!isEditing)}
                className={`px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 ${
                  isEditing 
                    ? 'bg-red-600 hover:bg-red-700 text-white' 
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {isEditing ? <FaTimes /> : <FaEdit />}
                {isEditing ? 'Cancel' : 'Edit Profile'}
              </button>
              
              {isEditing && (
                <button
                  onClick={handleSaveProfile}
                  disabled={isSaving}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 disabled:opacity-50"
                >
                  <FaSave />
                  {isSaving ? 'Saving...' : 'Save'}
                </button>
              )}
              
              <button
                onClick={() => setShowSettings(!showSettings)}
                className={`px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 ${
                  showSettings 
                    ? 'bg-gray-600 hover:bg-gray-700 text-white' 
                    : 'bg-gray-500 hover:bg-gray-600 text-white'
                }`}
              >
                <FaCog />
                Settings
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow`}>
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>
        
        {/* Tab Content */}
        <div className="p-6">
          <AnimatePresence mode="wait">
            {activeTab === 'profile' && (
              <motion.div
                key="profile"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={profileForm.full_name}
                      onChange={(e) => handleProfileChange('full_name', e.target.value)}
                      disabled={!isEditing}
                      className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                          : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                      } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Username
                    </label>
                    <input
                      type="text"
                      value={profileForm.username}
                      onChange={(e) => handleProfileChange('username', e.target.value)}
                      disabled={!isEditing}
                      className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                          : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                      } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                      placeholder="Choose a username"
                    />
                  </div>
                </div>

                {/* Bio */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Bio
                  </label>
                  <textarea
                    value={profileForm.bio}
                    onChange={(e) => handleProfileChange('bio', e.target.value)}
                    disabled={!isEditing}
                    rows={3}
                    className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                        : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                    } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                    placeholder="Tell us about yourself..."
                  />
                </div>

                {/* Contact Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaPhone className="inline mr-2" />
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={profileForm.phone_number}
                      onChange={(e) => handleProfileChange('phone_number', e.target.value)}
                      disabled={!isEditing}
                      className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                          : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                      } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                      placeholder="+****************"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaMapMarkerAlt className="inline mr-2" />
                      Location
                    </label>
                    <input
                      type="text"
                      value={profileForm.location}
                      onChange={(e) => handleProfileChange('location', e.target.value)}
                      disabled={!isEditing}
                      className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                          : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                      } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                      placeholder="City, Country"
                    />
                  </div>
                </div>

                {/* Website */}
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    <FaGlobe className="inline mr-2" />
                    Website
                  </label>
                  <input
                    type="url"
                    value={profileForm.website}
                    onChange={(e) => handleProfileChange('website', e.target.value)}
                    disabled={!isEditing}
                    className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                        : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                    } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                    placeholder="https://yourwebsite.com"
                  />
                </div>

                {/* Social Links */}
                <div>
                  <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Social Links
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        <FaLinkedin className="inline mr-2 text-blue-600" />
                        LinkedIn
                      </label>
                      <input
                        type="url"
                        value={profileForm.linkedin_url}
                        onChange={(e) => handleProfileChange('linkedin_url', e.target.value)}
                        disabled={!isEditing}
                        className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                          darkMode
                            ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                            : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                        } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                        placeholder="https://linkedin.com/in/username"
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        <FaGithub className="inline mr-2" />
                        GitHub
                      </label>
                      <input
                        type="url"
                        value={profileForm.github_url}
                        onChange={(e) => handleProfileChange('github_url', e.target.value)}
                        disabled={!isEditing}
                        className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                          darkMode
                            ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                            : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                        } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                        placeholder="https://github.com/username"
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        <FaTwitter className="inline mr-2 text-blue-400" />
                        Twitter
                      </label>
                      <input
                        type="url"
                        value={profileForm.twitter_url}
                        onChange={(e) => handleProfileChange('twitter_url', e.target.value)}
                        disabled={!isEditing}
                        className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                          darkMode
                            ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                            : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                        } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                        placeholder="https://twitter.com/username"
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        <FaInstagram className="inline mr-2 text-pink-500" />
                        Instagram
                      </label>
                      <input
                        type="url"
                        value={profileForm.instagram_url}
                        onChange={(e) => handleProfileChange('instagram_url', e.target.value)}
                        disabled={!isEditing}
                        className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 ${
                          darkMode
                            ? 'bg-gray-700 border-gray-600 text-white disabled:bg-gray-800'
                            : 'bg-white border-gray-300 text-gray-900 disabled:bg-gray-100'
                        } ${!isEditing ? 'cursor-not-allowed' : 'focus:ring-2 focus:ring-blue-500'}`}
                        placeholder="https://instagram.com/username"
                      />
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'settings' && (
              <motion.div
                key="settings"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {/* Notification Settings */}
                <div className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <h3 className={`text-lg font-semibold mb-4 flex items-center gap-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    <FaBell />
                    Notification Preferences
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          Email Notifications
                        </label>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Receive notifications about achievements and challenges
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settingsForm.email_notifications}
                          onChange={(e) => handleSettingsChange('email_notifications', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          Push Notifications
                        </label>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Get instant notifications on your device
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settingsForm.push_notifications}
                          onChange={(e) => handleSettingsChange('push_notifications', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          Marketing Emails
                        </label>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          Receive updates about new features and promotions
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settingsForm.marketing_emails}
                          onChange={(e) => handleSettingsChange('marketing_emails', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Account Actions */}
                <div className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <h3 className={`text-lg font-semibold mb-4 flex items-center gap-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    <FaKey />
                    Account Actions
                  </h3>

                  <div className="space-y-4">
                    <button className="w-full md:w-auto bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2">
                      <FaLock />
                      Change Password
                    </button>

                    <button className="w-full md:w-auto bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2">
                      <FaPhone />
                      Update Phone Number
                    </button>

                    <button className="w-full md:w-auto bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2">
                      <FaEnvelope />
                      Change Email
                    </button>
                  </div>
                </div>

                {/* Save Settings Button */}
                <div className="flex justify-end">
                  <button
                    onClick={handleSaveSettings}
                    disabled={isSaving}
                    className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 disabled:opacity-50"
                  >
                    <FaSave />
                    {isSaving ? 'Saving...' : 'Save Settings'}
                  </button>
                </div>
              </motion.div>
            )}

            {activeTab === 'security' && (
              <motion.div
                key="security"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <h3 className={`text-lg font-semibold mb-4 flex items-center gap-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    <FaUserShield />
                    Security Settings
                  </h3>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Security features are coming soon. You'll be able to manage two-factor authentication,
                    login alerts, and session management here.
                  </p>
                </div>
              </motion.div>
            )}

            {activeTab === 'privacy' && (
              <motion.div
                key="privacy"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <h3 className={`text-lg font-semibold mb-4 flex items-center gap-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    <FaEye />
                    Privacy Settings
                  </h3>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Privacy controls are coming soon. You'll be able to manage who can see your profile,
                    activity visibility, and data sharing preferences here.
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default EnhancedUserProfile;
