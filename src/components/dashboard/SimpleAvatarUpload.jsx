import React, { useState, useRef } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import toast from 'react-hot-toast';

/**
 * Simple Avatar Upload Component
 * 
 * Basic avatar upload without complex dependencies
 */
const SimpleAvatarUpload = ({ currentAvatar, onAvatarUpdate }) => {
  const { user, refreshProfile } = useAuth();
  const { darkMode } = useGlobalTheme();
  const fileInputRef = useRef(null);
  
  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState(null);

  // Handle file selection and upload
  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file || !user) return;

    // Validate file
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('Image size must be less than 5MB');
      return;
    }

    setIsUploading(true);
    
    try {
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => setPreview(e.target.result);
      reader.readAsDataURL(file);

      // Upload directly to Supabase Storage
      const fileExt = file.name.split('.').pop().toLowerCase();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;

      console.log('Uploading file:', fileName);

      // First, try to create the bucket if it doesn't exist
      try {
        await supabase.storage.createBucket('avatars', {
          public: true,
          fileSizeLimit: 5242880, // 5MB
          allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
        });
        console.log('Bucket created or already exists');
      } catch (bucketError) {
        console.log('Bucket creation skipped:', bucketError.message);
      }

      // Upload the file
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true // Allow overwriting
        });

      if (uploadError) {
        console.error('Upload error:', uploadError);
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      console.log('Upload successful:', uploadData);

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      console.log('Public URL:', publicUrl);

      // Update user profile with new avatar URL - try profiles table first, then user_profiles
      let updateError = null;
      let updateSuccess = false;

      // Try profiles table first (more common)
      try {
        const { error: profilesError } = await supabase
          .from('profiles')
          .update({
            avatar_url: publicUrl
            // Removed updated_at to avoid trigger issues
          })
          .eq('id', user.id);

        if (!profilesError) {
          updateSuccess = true;
          console.log('Profile updated in profiles table');
        } else {
          console.log('Profiles table update failed:', profilesError);
          updateError = profilesError;
        }
      } catch (err) {
        console.log('Profiles table not accessible:', err);
      }

      // If profiles table failed, try user_profiles table
      if (!updateSuccess) {
        try {
          const { error: userProfilesError } = await supabase
            .from('user_profiles')
            .update({
              avatar_url: publicUrl
              // Removed updated_at to avoid trigger issues
            })
            .eq('id', user.id);

          if (!userProfilesError) {
            updateSuccess = true;
            updateError = null;
            console.log('Profile updated in user_profiles table');
          } else {
            console.log('User_profiles table update failed:', userProfilesError);
            updateError = userProfilesError;
          }
        } catch (err) {
          console.log('User_profiles table not accessible:', err);
          updateError = err;
        }
      }

      if (!updateSuccess && updateError) {
        console.error('Profile update error:', updateError);
        throw new Error(`Failed to update profile: ${updateError.message}`);
      }

      console.log('Profile updated successfully');

      // Refresh profile and notify parent
      if (refreshProfile) {
        await refreshProfile();
      }
      
      if (onAvatarUpdate) {
        onAvatarUpdate(publicUrl);
      }

      toast.success('Avatar updated successfully!');

    } catch (error) {
      console.error('Avatar upload error:', error);
      toast.error(error.message || 'Failed to upload avatar');
    } finally {
      setIsUploading(false);
      setPreview(null);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Avatar Display Container */}
      <div className="relative group">
        <div className={`w-32 h-32 rounded-full border-4 ${
          darkMode ? 'border-gray-600' : 'border-gray-300'
        } overflow-hidden bg-gradient-to-br from-blue-100 to-blue-200 shadow-xl cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105`}
             onClick={() => fileInputRef.current?.click()}>

          {preview ? (
            <img src={preview} alt="Avatar Preview" className="w-full h-full object-cover" />
          ) : currentAvatar ? (
            <img src={currentAvatar} alt="Profile" className="w-full h-full object-cover" />
          ) : (
            <div className={`w-full h-full flex items-center justify-center ${
              darkMode ? 'bg-gray-700' : 'bg-gray-200'
            }`}>
              <div className="text-center">
                <div className="text-4xl text-gray-400 mb-2">👤</div>
                <div className="text-xs text-gray-500">No Image</div>
              </div>
            </div>
          )}

          {/* Loading overlay */}
          {isUploading && (
            <div className="absolute inset-0 bg-black bg-opacity-60 rounded-full flex items-center justify-center">
              <div className="text-center text-white">
                <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <div className="text-xs">Uploading...</div>
              </div>
            </div>
          )}

          {/* Hover overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-full flex items-center justify-center transition-all duration-300">
            <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center">
              <div className="text-2xl mb-1">📷</div>
              <div className="text-xs">Change Photo</div>
            </div>
          </div>
        </div>

        {/* Camera button */}
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="absolute -bottom-2 -right-2 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-200 disabled:opacity-50 hover:scale-110"
          title="Upload Avatar"
        >
          <span className="text-lg">📷</span>
        </button>
      </div>

      {/* Upload Button */}
      <button
        onClick={() => fileInputRef.current?.click()}
        disabled={isUploading}
        className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
          isUploading
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg'
        } text-white`}
      >
        {isUploading ? 'Uploading...' : 'Choose Photo'}
      </button>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Upload instructions */}
      <div className={`text-center ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        <p className="text-sm font-medium mb-1">Upload your profile picture</p>
        <p className="text-xs">
          Supported formats: JPG, PNG, GIF
          <br />
          Maximum size: 5MB
        </p>
      </div>
    </div>
  );
};

export default SimpleAvatarUpload;
