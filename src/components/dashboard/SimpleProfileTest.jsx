import React from 'react';
import { FaUser } from 'react-icons/fa';

/**
 * Simple Profile Test Component
 * 
 * A minimal profile component to test routing
 */
const SimpleProfileTest = () => {
  return (
    <div className="p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="text-center">
            <div className="w-24 h-24 rounded-full bg-blue-500 flex items-center justify-center mx-auto mb-4">
              <FaUser className="text-white text-3xl" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Profile Page Test
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              This is a simple test to verify the profile page routing works correctly.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h3 className="font-bold text-blue-600 dark:text-blue-400">✅ Routing</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Profile page is accessible via /dashboard/profile
                </p>
              </div>
              
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h3 className="font-bold text-green-600 dark:text-green-400">✅ Component</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Profile component loads successfully
                </p>
              </div>
              
              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <h3 className="font-bold text-purple-600 dark:text-purple-400">✅ Layout</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Dashboard layout integration working
                </p>
              </div>
            </div>
            
            <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <h3 className="font-bold text-yellow-600 dark:text-yellow-400 mb-2">
                🔧 Next Steps
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                If you can see this page, the routing is working correctly. 
                The full profile component should load next.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleProfileTest;
