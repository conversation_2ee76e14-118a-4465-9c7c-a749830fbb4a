import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FaRocket, FaGamepad, FaTrophy, FaCheck, FaClock, FaChartLine, FaExclamationTriangle } from 'react-icons/fa';
import { supabase } from '../../lib/supabase';

const HackingProgress = ({ darkMode }) => {
  const [loading, setLoading] = useState(true);
  const [startHackProgress, setStartHackProgress] = useState({
    total: 0,
    completed: 0,
    inProgress: 0,
    recentSimulations: []
  });
  const [challengerProgress, setChallengerProgress] = useState({
    total: 0,
    completed: 0,
    inProgress: 0,
    recentChallenges: []
  });

  useEffect(() => {
    const fetchProgress = async () => {
      try {
        setLoading(true);
        const { data: { user } } = await supabase.auth.getUser();

        if (user) {
          // Fetch Start Hack progress
          const { data: startHackAttempts, error: startHackError } = await supabase
            .from('start_hack_simulation_attempts')
            .select(`
              *,
              simulation:simulation_id(id, title, slug, points, difficulty_id(name))
            `)
            .eq('user_id', user.id);

          if (startHackError) throw startHackError;

          // Fetch Challenger progress
          const { data: challengerAttempts, error: challengerError } = await supabase
            .from('challenger_challenge_attempts')
            .select(`
              *,
              challenge:challenge_id(id, title, slug, points, difficulty_id(name))
            `)
            .eq('user_id', user.id);

          if (challengerError) throw challengerError;

          // Process Start Hack data
          const completedStartHack = startHackAttempts?.filter(attempt => attempt.is_completed) || [];
          const inProgressStartHack = startHackAttempts?.filter(attempt => !attempt.is_completed) || [];
          
          setStartHackProgress({
            total: startHackAttempts?.length || 0,
            completed: completedStartHack.length,
            inProgress: inProgressStartHack.length,
            recentSimulations: [...startHackAttempts || []].sort((a, b) => 
              new Date(b.started_at) - new Date(a.started_at)
            ).slice(0, 3)
          });

          // Process Challenger data
          const completedChallenger = challengerAttempts?.filter(attempt => attempt.is_completed) || [];
          const inProgressChallenger = challengerAttempts?.filter(attempt => !attempt.is_completed) || [];
          
          setChallengerProgress({
            total: challengerAttempts?.length || 0,
            completed: completedChallenger.length,
            inProgress: inProgressChallenger.length,
            recentChallenges: [...challengerAttempts || []].sort((a, b) => 
              new Date(b.started_at) - new Date(a.started_at)
            ).slice(0, 3)
          });
        }
      } catch (error) {
        console.error('Error fetching hacking progress:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProgress();
  }, []);

  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    if (!seconds) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'}`}>
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
        <h3 className="text-lg font-bold">Hacking Progress</h3>
        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Track your progress in Start Hack simulations and Challenger challenges
        </p>
      </div>

      <div className="p-4">
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Start Hack Progress */}
            <div>
              <div className="flex items-center mb-2">
                <FaRocket className="text-blue-500 mr-2" />
                <h4 className="font-bold">Start Hack</h4>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <div className="text-2xl font-bold">{startHackProgress.total}</div>
                  <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Simulations</div>
                </div>
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <div className="text-2xl font-bold text-green-500">{startHackProgress.completed}</div>
                  <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Completed</div>
                </div>
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <div className="text-2xl font-bold text-blue-500">{startHackProgress.inProgress}</div>
                  <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>In Progress</div>
                </div>
              </div>

              {startHackProgress.recentSimulations.length > 0 ? (
                <div>
                  <h5 className="font-semibold mb-2">Recent Simulations</h5>
                  <div className="space-y-2">
                    {startHackProgress.recentSimulations.map(attempt => (
                      <div key={attempt.id} className={`p-2 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                        <div className="flex justify-between items-center">
                          <div className="font-medium">{attempt.simulation?.title || 'Unknown Simulation'}</div>
                          {attempt.is_completed ? (
                            <span className="flex items-center text-green-500 text-sm">
                              <FaCheck className="mr-1" /> Completed
                            </span>
                          ) : (
                            <span className="flex items-center text-blue-500 text-sm">
                              <FaExclamationTriangle className="mr-1" /> In Progress
                            </span>
                          )}
                        </div>
                        <div className="flex justify-between items-center mt-1 text-sm">
                          <div className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {attempt.simulation?.difficulty?.name || 'Unknown'} • {attempt.simulation?.points || 0} pts
                          </div>
                          {attempt.completion_time && (
                            <div className="flex items-center">
                              <FaClock className="mr-1" />
                              {formatTime(attempt.completion_time)}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className={`text-center py-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  No simulations attempted yet
                </div>
              )}

              <div className="mt-4 text-center">
                <Link
                  to="/dashboard/start-hack"
                  className={`inline-flex items-center px-4 py-2 rounded-lg ${
                    darkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
                  } text-white transition-colors`}
                >
                  <FaRocket className="mr-2" /> Go to Start Hack
                </Link>
              </div>
            </div>

            {/* Challenger Progress */}
            <div>
              <div className="flex items-center mb-2">
                <FaGamepad className="text-purple-500 mr-2" />
                <h4 className="font-bold">Challenger</h4>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <div className="text-2xl font-bold">{challengerProgress.total}</div>
                  <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Challenges</div>
                </div>
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <div className="text-2xl font-bold text-green-500">{challengerProgress.completed}</div>
                  <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Completed</div>
                </div>
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <div className="text-2xl font-bold text-blue-500">{challengerProgress.inProgress}</div>
                  <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>In Progress</div>
                </div>
              </div>

              {challengerProgress.recentChallenges.length > 0 ? (
                <div>
                  <h5 className="font-semibold mb-2">Recent Challenges</h5>
                  <div className="space-y-2">
                    {challengerProgress.recentChallenges.map(attempt => (
                      <div key={attempt.id} className={`p-2 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                        <div className="flex justify-between items-center">
                          <div className="font-medium">{attempt.challenge?.title || 'Unknown Challenge'}</div>
                          {attempt.is_completed ? (
                            <span className="flex items-center text-green-500 text-sm">
                              <FaCheck className="mr-1" /> Completed
                            </span>
                          ) : (
                            <span className="flex items-center text-blue-500 text-sm">
                              <FaExclamationTriangle className="mr-1" /> In Progress
                            </span>
                          )}
                        </div>
                        <div className="flex justify-between items-center mt-1 text-sm">
                          <div className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {attempt.challenge?.difficulty?.name || 'Unknown'} • {attempt.challenge?.points || 0} pts
                          </div>
                          {attempt.completion_time && (
                            <div className="flex items-center">
                              <FaClock className="mr-1" />
                              {formatTime(attempt.completion_time)}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className={`text-center py-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  No challenges attempted yet
                </div>
              )}

              <div className="mt-4 text-center">
                <Link
                  to="/dashboard/challenger"
                  className={`inline-flex items-center px-4 py-2 rounded-lg ${
                    darkMode ? 'bg-purple-600 hover:bg-purple-700' : 'bg-purple-500 hover:bg-purple-600'
                  } text-white transition-colors`}
                >
                  <FaGamepad className="mr-2" /> Go to Challenger
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HackingProgress;
