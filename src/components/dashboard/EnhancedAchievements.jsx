import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FaTrophy, 
  FaMedal, 
  FaStar, 
  FaLock, 
  FaCheck, 
  FaFire,
  FaGraduationCap,
  FaShieldAlt,
  FaCode,
  FaNetworkWired,
  FaUserSecret,
  FaChartLine
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';

/**
 * EnhancedAchievements Component
 * 
 * A comprehensive achievements system with categories, progress tracking,
 * and social sharing capabilities.
 */
const EnhancedAchievements = () => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Achievement categories
  const categories = [
    { id: 'all', name: 'All Achievements', icon: FaTrophy },
    { id: 'learning', name: 'Learning', icon: FaGraduationCap },
    { id: 'security', name: 'Security', icon: FaShieldAlt },
    { id: 'coding', name: 'Coding', icon: FaCode },
    { id: 'networking', name: 'Networking', icon: FaNetworkWired },
    { id: 'hacking', name: 'Ethical Hacking', icon: FaUserSecret },
    { id: 'streaks', name: 'Streaks', icon: FaFire }
  ];

  // Mock achievements data
  const achievements = [
    {
      id: 'first-steps',
      title: 'First Steps',
      description: 'Complete your first learning module',
      category: 'learning',
      tier: 'bronze',
      progress: 100,
      maxProgress: 100,
      isUnlocked: true,
      xpReward: 50,
      unlockedDate: '2024-01-15',
      icon: FaGraduationCap
    },
    {
      id: 'network-novice',
      title: 'Network Novice',
      description: 'Complete 5 networking fundamentals modules',
      category: 'networking',
      tier: 'bronze',
      progress: 5,
      maxProgress: 5,
      isUnlocked: true,
      xpReward: 100,
      unlockedDate: '2024-01-20',
      icon: FaNetworkWired
    },
    {
      id: 'security-scholar',
      title: 'Security Scholar',
      description: 'Complete 10 security-related modules',
      category: 'security',
      tier: 'silver',
      progress: 7,
      maxProgress: 10,
      isUnlocked: false,
      xpReward: 200,
      icon: FaShieldAlt
    },
    {
      id: 'code-warrior',
      title: 'Code Warrior',
      description: 'Complete 15 coding challenges',
      category: 'coding',
      tier: 'silver',
      progress: 12,
      maxProgress: 15,
      isUnlocked: false,
      xpReward: 250,
      icon: FaCode
    },
    {
      id: 'ethical-hacker',
      title: 'Ethical Hacker',
      description: 'Complete the Ethical Hacking learning path',
      category: 'hacking',
      tier: 'gold',
      progress: 25,
      maxProgress: 50,
      isUnlocked: false,
      xpReward: 500,
      icon: FaUserSecret
    },
    {
      id: 'streak-master',
      title: 'Streak Master',
      description: 'Maintain a 30-day learning streak',
      category: 'streaks',
      tier: 'gold',
      progress: 7,
      maxProgress: 30,
      isUnlocked: false,
      xpReward: 300,
      icon: FaFire
    },
    {
      id: 'certification-ready',
      title: 'Certification Ready',
      description: 'Achieve 80% readiness on any certification',
      category: 'learning',
      tier: 'platinum',
      progress: 65,
      maxProgress: 80,
      isUnlocked: false,
      xpReward: 1000,
      icon: FaMedal
    }
  ];

  // Filter achievements by category
  const filteredAchievements = selectedCategory === 'all' 
    ? achievements 
    : achievements.filter(achievement => achievement.category === selectedCategory);

  // Get tier color
  const getTierColor = (tier) => {
    switch (tier) {
      case 'bronze': return 'text-amber-600';
      case 'silver': return 'text-gray-400';
      case 'gold': return 'text-yellow-500';
      case 'platinum': return 'text-purple-500';
      default: return 'text-gray-500';
    }
  };

  // Get tier background
  const getTierBg = (tier) => {
    switch (tier) {
      case 'bronze': return 'bg-amber-600/20';
      case 'silver': return 'bg-gray-400/20';
      case 'gold': return 'bg-yellow-500/20';
      case 'platinum': return 'bg-purple-500/20';
      default: return 'bg-gray-500/20';
    }
  };

  // Calculate total stats
  const totalAchievements = achievements.length;
  const unlockedAchievements = achievements.filter(a => a.isUnlocked).length;
  const totalXP = achievements.filter(a => a.isUnlocked).reduce((sum, a) => sum + a.xpReward, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6`}>
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold mb-2 flex items-center">
              <FaTrophy className="mr-3 text-yellow-500" />
              Achievements
            </h2>
            <p className="text-gray-500 dark:text-gray-400">
              Track your progress and unlock achievements as you advance your cybersecurity skills.
            </p>
          </div>
          
          {/* Stats Summary */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} rounded-lg p-3`}>
              <div className="text-2xl font-bold text-yellow-500">{unlockedAchievements}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Unlocked</div>
            </div>
            <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} rounded-lg p-3`}>
              <div className="text-2xl font-bold text-blue-500">{totalAchievements}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Total</div>
            </div>
            <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} rounded-lg p-3`}>
              <div className="text-2xl font-bold text-green-500">{totalXP}</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">XP Earned</div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6`}>
        <h3 className="text-lg font-bold mb-4">Categories</h3>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`
                flex items-center px-4 py-2 rounded-lg transition-colors
                ${selectedCategory === category.id
                  ? 'bg-blue-500 text-white'
                  : darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }
              `}
            >
              <category.icon className="mr-2" />
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Achievements Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAchievements.map((achievement) => (
          <motion.div
            key={achievement.id}
            whileHover={{ scale: 1.02 }}
            className={`
              ${darkMode ? 'bg-gray-800' : 'bg-white'} 
              rounded-lg p-6 border-2 transition-all
              ${achievement.isUnlocked 
                ? 'border-green-500 shadow-lg' 
                : 'border-gray-300 dark:border-gray-600'
              }
            `}
          >
            {/* Achievement Header */}
            <div className="flex items-start justify-between mb-4">
              <div className={`
                w-12 h-12 rounded-full flex items-center justify-center
                ${achievement.isUnlocked 
                  ? getTierBg(achievement.tier) 
                  : 'bg-gray-300 dark:bg-gray-600'
                }
              `}>
                {achievement.isUnlocked ? (
                  <achievement.icon className={`text-xl ${getTierColor(achievement.tier)}`} />
                ) : (
                  <FaLock className="text-gray-500" />
                )}
              </div>
              
              <div className="flex items-center">
                {achievement.isUnlocked && (
                  <FaCheck className="text-green-500 mr-2" />
                )}
                <span className={`
                  px-2 py-1 rounded-full text-xs font-medium capitalize
                  ${getTierColor(achievement.tier)} ${getTierBg(achievement.tier)}
                `}>
                  {achievement.tier}
                </span>
              </div>
            </div>

            {/* Achievement Content */}
            <h4 className="font-bold text-lg mb-2">{achievement.title}</h4>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              {achievement.description}
            </p>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Progress</span>
                <span>{achievement.progress}/{achievement.maxProgress}</span>
              </div>
              <div className="w-full bg-gray-300 dark:bg-gray-600 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    achievement.isUnlocked ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Reward Info */}
            <div className="flex justify-between items-center">
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <FaStar className="mr-1 text-yellow-500" />
                <span>{achievement.xpReward} XP</span>
              </div>
              
              {achievement.isUnlocked && achievement.unlockedDate && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Unlocked {new Date(achievement.unlockedDate).toLocaleDateString()}
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Progress Overview */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6`}>
        <h3 className="text-lg font-bold mb-4">Achievement Progress Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {['bronze', 'silver', 'gold', 'platinum'].map((tier) => {
            const tierAchievements = achievements.filter(a => a.tier === tier);
            const unlockedTier = tierAchievements.filter(a => a.isUnlocked).length;
            
            return (
              <div key={tier} className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} rounded-lg p-4 text-center`}>
                <div className={`text-2xl mb-2 ${getTierColor(tier)}`}>
                  <FaMedal />
                </div>
                <div className="text-lg font-bold capitalize">{tier}</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {unlockedTier}/{tierAchievements.length}
                </div>
                <div className="w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1 mt-2">
                  <div
                    className={`h-1 rounded-full ${
                      tier === 'bronze' ? 'bg-amber-600' :
                      tier === 'silver' ? 'bg-gray-400' :
                      tier === 'gold' ? 'bg-yellow-500' :
                      'bg-purple-500'
                    }`}
                    style={{ width: `${(unlockedTier / tierAchievements.length) * 100}%` }}
                  ></div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default EnhancedAchievements;
