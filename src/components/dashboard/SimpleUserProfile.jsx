import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { ensureUserProfile, updateUserProfile } from '../../utils/profileUtils';
import SimpleAvatarUpload from './SimpleAvatarUpload';
import LoadingSpinner from '../common/LoadingSpinner';
import toast from 'react-hot-toast';
import './SimpleUserProfile.css';
import {
  FaLinkedin,
  FaGithub,
  FaTwitter,
  FaInstagram,
  FaFacebook,
  FaGlobe,
  FaMapMarkerAlt,
  FaEnvelope,
  FaPhone,
  FaEdit,
  FaSave,
  FaTimes,
  FaUser,
  FaTrophy,
  FaFire,
  FaGraduationCap
} from 'react-icons/fa';

/**
 * Simple User Profile Component
 * 
 * Fallback profile component with basic functionality
 */
const SimpleUserProfile = () => {
  const { user, profile, refreshProfile } = useAuth();
  const { darkMode } = useGlobalTheme();

  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [currentAvatar, setCurrentAvatar] = useState(null);
  const [profileForm, setProfileForm] = useState({
    full_name: '',
    username: '',
    bio: '',
    email: '',
    phone_number: '',
    location: '',
    website: '',
    linkedin_url: '',
    github_url: '',
    twitter_url: '',
    instagram_url: '',
    facebook_url: ''
  });

  // Initialize profile
  useEffect(() => {
    const initProfile = async () => {
      if (user && !profile) {
        await ensureUserProfile(user);
        if (refreshProfile) {
          await refreshProfile();
        }
      }
    };
    initProfile();
  }, [user, profile, refreshProfile]);

  // Update form when profile changes
  useEffect(() => {
    if (profile) {
      setProfileForm({
        full_name: profile.full_name || '',
        username: profile.username || '',
        bio: profile.bio || '',
        email: profile.email || '',
        phone_number: profile.phone_number || '',
        location: profile.location || '',
        website: profile.website || '',
        linkedin_url: profile.linkedin_url || '',
        github_url: profile.github_url || '',
        twitter_url: profile.twitter_url || '',
        instagram_url: profile.instagram_url || '',
        facebook_url: profile.facebook_url || ''
      });
      setCurrentAvatar(profile.avatar_url);
    }
  }, [profile]);

  const handleSave = async () => {
    if (!user) return;

    setIsSaving(true);
    try {
      const result = await updateUserProfile(user.id, profileForm);
      
      if (result.success) {
        if (refreshProfile) {
          await refreshProfile();
        }
        setIsEditing(false);
        toast.success('Profile updated successfully!');
      } else {
        toast.error(result.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (field, value) => {
    setProfileForm(prev => ({ ...prev, [field]: value }));
  };

  // Handle avatar update
  const handleAvatarUpdate = (newAvatarUrl) => {
    setCurrentAvatar(newAvatarUrl);
  };

  if (!user) {
    return (
      <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <LoadingSpinner message="Loading your profile..." />
      </div>
    );
  }

  return (
    <div className={`profile-container min-h-screen p-4 md:p-6 ${darkMode ? 'bg-gray-900 dark' : 'bg-gray-50'}`}>
      <div className={`max-w-6xl mx-auto ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-xl shadow-2xl overflow-hidden`}>

        {/* Profile Header - View Mode */}
        {!isEditing && (
          <div className={`profile-header profile-section relative p-8 text-white`}>
            <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
              {/* Avatar */}
              <div className="relative">
                <div className="profile-avatar w-32 h-32 rounded-full border-4 border-white shadow-lg overflow-hidden bg-gray-300">
                  {currentAvatar ? (
                    <img
                      src={currentAvatar}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-400">
                      <FaUser className="text-4xl text-gray-600" />
                    </div>
                  )}
                </div>
                <div className="status-indicator absolute -bottom-2 -right-2 bg-green-500 w-8 h-8 rounded-full border-4 border-white flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>

              {/* Profile Info */}
              <div className="flex-1 text-center md:text-left">
                <h1 className="text-4xl font-bold mb-2">
                  {profileForm.full_name || profileForm.username || 'User'}
                </h1>
                <p className="text-xl opacity-90 mb-3">
                  @{profileForm.username || user?.email?.split('@')[0]}
                </p>
                {profileForm.bio && (
                  <p className="text-lg opacity-80 mb-4 max-w-2xl">
                    {profileForm.bio}
                  </p>
                )}

                {/* Contact Info */}
                <div className="flex flex-wrap gap-4 justify-center md:justify-start text-sm opacity-90">
                  {profileForm.email && (
                    <div className="flex items-center gap-2">
                      <FaEnvelope />
                      <span>{profileForm.email}</span>
                    </div>
                  )}
                  {profileForm.location && (
                    <div className="flex items-center gap-2">
                      <FaMapMarkerAlt />
                      <span>{profileForm.location}</span>
                    </div>
                  )}
                  {profileForm.phone_number && (
                    <div className="flex items-center gap-2">
                      <FaPhone />
                      <span>{profileForm.phone_number}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Edit Button */}
              <button
                onClick={() => setIsEditing(true)}
                className="edit-button bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center gap-2 shadow-lg"
              >
                <FaEdit />
                Edit Profile
              </button>
            </div>
          </div>
        )}

        {/* Social Links - View Mode */}
        {!isEditing && (
          <div className={`profile-section p-6 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Connect With Me
            </h3>
            <div className="flex flex-wrap gap-4">
              {profileForm.website && (
                <a
                  href={profileForm.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  <FaGlobe />
                  Website
                </a>
              )}
              {profileForm.linkedin_url && (
                <a
                  href={profileForm.linkedin_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <FaLinkedin />
                  LinkedIn
                </a>
              )}
              {profileForm.github_url && (
                <a
                  href={profileForm.github_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link flex items-center gap-2 px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors"
                >
                  <FaGithub />
                  GitHub
                </a>
              )}
              {profileForm.twitter_url && (
                <a
                  href={profileForm.twitter_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link flex items-center gap-2 px-4 py-2 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors"
                >
                  <FaTwitter />
                  Twitter
                </a>
              )}
              {profileForm.instagram_url && (
                <a
                  href={profileForm.instagram_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link flex items-center gap-2 px-4 py-2 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors"
                >
                  <FaInstagram />
                  Instagram
                </a>
              )}
            </div>
          </div>
        )}

        {/* Profile Stats - View Mode */}
        {!isEditing && profile && (
          <div className={`profile-section p-6 ${darkMode ? 'bg-gray-750' : 'bg-gray-50'}`}>
            <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              My Progress
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className={`stat-card text-center p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
                <div className="flex items-center justify-center mb-2">
                  <FaTrophy className="text-3xl text-yellow-500" />
                </div>
                <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {profile.total_points || 0}
                </div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Points</div>
              </div>
              <div className={`stat-card text-center p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
                <div className="flex items-center justify-center mb-2">
                  <FaFire className="text-3xl text-orange-500" />
                </div>
                <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {profile.current_streak || 0}
                </div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Day Streak</div>
              </div>
              <div className={`stat-card text-center p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
                <div className="flex items-center justify-center mb-2">
                  <FaGraduationCap className="text-3xl text-blue-500" />
                </div>
                <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {profile.modules_completed || 0}
                </div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Modules</div>
              </div>
              <div className={`stat-card text-center p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-md`}>
                <div className="flex items-center justify-center mb-2">
                  <FaTrophy className="text-3xl text-green-500" />
                </div>
                <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {profile.challenges_completed || 0}
                </div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Challenges</div>
              </div>
            </div>
          </div>
        )}

        {/* Edit Mode Form */}
        {isEditing && (
          <div className="p-8">
            {/* Edit Header */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center gap-3">
                <FaEdit className={`text-2xl ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
                <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Edit Profile
                </h2>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => setIsEditing(false)}
                  className="btn-secondary flex items-center gap-2 px-4 py-2 text-white rounded-lg transition-colors"
                >
                  <FaTimes />
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="btn-primary flex items-center gap-2 px-4 py-2 text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  <FaSave />
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </div>

            {/* Avatar Upload Section */}
            <div className={`mb-8 p-6 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Profile Picture
              </h3>
              <SimpleAvatarUpload
                currentAvatar={currentAvatar}
                onAvatarUpdate={handleAvatarUpdate}
              />
            </div>

            <div className="space-y-8">
              {/* Basic Information */}
              <div>
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Basic Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Full Name *
                    </label>
                    <input
                      type="text"
                      value={profileForm.full_name}
                      onChange={(e) => handleChange('full_name', e.target.value)}
                      className={`form-input w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Username *
                    </label>
                    <input
                      type="text"
                      value={profileForm.username}
                      onChange={(e) => handleChange('username', e.target.value)}
                      className={`form-input w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="Choose a username"
                    />
                  </div>
                </div>
              </div>

              {/* Bio Section */}
              <div>
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  About Me
                </h3>
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Bio
                  </label>
                  <textarea
                    value={profileForm.bio}
                    onChange={(e) => handleChange('bio', e.target.value)}
                    rows={4}
                    className={`w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    placeholder="Tell us about yourself, your interests, and your cybersecurity journey..."
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Contact Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaEnvelope className="inline mr-2" />
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={profileForm.email}
                      onChange={(e) => handleChange('email', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaPhone className="inline mr-2" />
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={profileForm.phone_number}
                      onChange={(e) => handleChange('phone_number', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </div>

              {/* Location and Website */}
              <div>
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Location & Website
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaMapMarkerAlt className="inline mr-2" />
                      Location
                    </label>
                    <input
                      type="text"
                      value={profileForm.location}
                      onChange={(e) => handleChange('location', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="City, Country"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaGlobe className="inline mr-2" />
                      Website
                    </label>
                    <input
                      type="url"
                      value={profileForm.website}
                      onChange={(e) => handleChange('website', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="https://yourwebsite.com"
                    />
                  </div>
                </div>
              </div>

              {/* Social Media Links */}
              <div>
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Social Media Links
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaLinkedin className="inline mr-2 text-blue-600" />
                      LinkedIn
                    </label>
                    <input
                      type="url"
                      value={profileForm.linkedin_url}
                      onChange={(e) => handleChange('linkedin_url', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="https://linkedin.com/in/username"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaGithub className="inline mr-2" />
                      GitHub
                    </label>
                    <input
                      type="url"
                      value={profileForm.github_url}
                      onChange={(e) => handleChange('github_url', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="https://github.com/username"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaTwitter className="inline mr-2 text-blue-400" />
                      Twitter
                    </label>
                    <input
                      type="url"
                      value={profileForm.twitter_url}
                      onChange={(e) => handleChange('twitter_url', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="https://twitter.com/username"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <FaInstagram className="inline mr-2 text-pink-500" />
                      Instagram
                    </label>
                    <input
                      type="url"
                      value={profileForm.instagram_url}
                      onChange={(e) => handleChange('instagram_url', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="https://instagram.com/username"
                    />
                  </div>
                </div>
              </div>

              {/* Save Actions */}
              <div className="flex justify-end gap-4 pt-6 border-t border-gray-300 dark:border-gray-600">
                <button
                  onClick={() => setIsEditing(false)}
                  className="btn-secondary flex items-center gap-2 px-6 py-3 text-white rounded-lg transition-colors"
                >
                  <FaTimes />
                  Cancel Changes
                </button>
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="btn-primary flex items-center gap-2 px-6 py-3 text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  <FaSave />
                  {isSaving ? 'Saving Profile...' : 'Save Profile'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleUserProfile;
