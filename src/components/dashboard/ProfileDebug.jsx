import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';

/**
 * Profile Debug Component
 * 
 * Helps identify profile page issues
 */
const ProfileDebug = () => {
  const { user, profile } = useAuth();
  const { darkMode } = useGlobalTheme();

  return (
    <div className={`p-8 ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="max-w-4xl mx-auto">
        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6`}>
          <h1 className="text-3xl font-bold mb-6">🔍 Profile Debug Information</h1>
          
          <div className="space-y-6">
            {/* Authentication Status */}
            <div className="border-l-4 border-blue-500 pl-4">
              <h2 className="text-xl font-bold mb-2">Authentication Status</h2>
              <p><strong>User Logged In:</strong> {user ? '✅ Yes' : '❌ No'}</p>
              <p><strong>User ID:</strong> {user?.id || 'Not available'}</p>
              <p><strong>User Email:</strong> {user?.email || 'Not available'}</p>
            </div>

            {/* Profile Data */}
            <div className="border-l-4 border-green-500 pl-4">
              <h2 className="text-xl font-bold mb-2">Profile Data</h2>
              <p><strong>Profile Loaded:</strong> {profile ? '✅ Yes' : '❌ No'}</p>
              <p><strong>Full Name:</strong> {profile?.full_name || 'Not set'}</p>
              <p><strong>Username:</strong> {profile?.username || 'Not set'}</p>
              <p><strong>Bio:</strong> {profile?.bio || 'Not set'}</p>
            </div>

            {/* Current URL */}
            <div className="border-l-4 border-purple-500 pl-4">
              <h2 className="text-xl font-bold mb-2">Current Location</h2>
              <p><strong>Current URL:</strong> {window.location.href}</p>
              <p><strong>Pathname:</strong> {window.location.pathname}</p>
            </div>

            {/* Theme Status */}
            <div className="border-l-4 border-yellow-500 pl-4">
              <h2 className="text-xl font-bold mb-2">Theme Status</h2>
              <p><strong>Dark Mode:</strong> {darkMode ? '🌙 Enabled' : '☀️ Disabled'}</p>
            </div>

            {/* Raw Data */}
            <div className="border-l-4 border-red-500 pl-4">
              <h2 className="text-xl font-bold mb-2">Raw Data (for debugging)</h2>
              <details className="mt-2">
                <summary className="cursor-pointer font-medium">Click to view user object</summary>
                <pre className={`mt-2 p-3 rounded text-xs overflow-auto ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  {JSON.stringify(user, null, 2)}
                </pre>
              </details>
              <details className="mt-2">
                <summary className="cursor-pointer font-medium">Click to view profile object</summary>
                <pre className={`mt-2 p-3 rounded text-xs overflow-auto ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  {JSON.stringify(profile, null, 2)}
                </pre>
              </details>
            </div>

            {/* Action Buttons */}
            <div className="border-l-4 border-indigo-500 pl-4">
              <h2 className="text-xl font-bold mb-2">Quick Actions</h2>
              <div className="space-x-4">
                <button 
                  onClick={() => window.location.href = '/dashboard'}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Go to Dashboard
                </button>
                <button 
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                >
                  Reload Page
                </button>
                <button 
                  onClick={() => console.log('User:', user, 'Profile:', profile)}
                  className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
                >
                  Log to Console
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileDebug;
