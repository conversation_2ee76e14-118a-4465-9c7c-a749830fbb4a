import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FaShieldAlt,
  FaTrophy,
  FaFire,
  FaChartLine,
  FaBookOpen,
  FaBullseye,
  FaClock,
  FaUsers,
  FaRocket,
  FaLightbulb,
  FaGraduationCap,
  FaMedal
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { useUserProgress } from '../../hooks/useUserProgress';

/**
 * Enhanced Dashboard Component
 * 
 * Features:
 * - Personalized greeting with user name
 * - Real-time progress tracking
 * - Interactive AI assistant
 * - Rich content and recommendations
 * - Mobile-responsive design
 */
const EnhancedDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const { user, profile } = useAuth();
  const { 
    getStats, 
    getCurrentStreak, 
    getUserLevel,
    progress,
    loading: progressLoading 
  } = useUserProgress();

  const [currentTime, setCurrentTime] = useState(new Date());
  const [recommendations, setRecommendations] = useState([]);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Get user stats
  const userStats = getStats();
  const currentStreak = getCurrentStreak();
  const userLevel = getUserLevel();

  // Get user's display name
  const getUserName = () => {
    return profile?.full_name || 
           user?.user_metadata?.full_name || 
           user?.email?.split('@')[0] || 
           'User';
  };

  // Get personalized greeting
  const getPersonalizedGreeting = () => {
    const hour = currentTime.getHours();
    const userName = getUserName();
    
    let timeGreeting;
    if (hour < 12) timeGreeting = 'Good morning';
    else if (hour < 17) timeGreeting = 'Good afternoon';
    else timeGreeting = 'Good evening';

    const greetings = [
      `${timeGreeting}, ${userName}! Ready to enhance your cybersecurity skills today?`,
      `${timeGreeting}, ${userName}! Let's continue your cybersecurity journey.`,
      `${timeGreeting}, ${userName}! Time to level up your security expertise.`,
      `${timeGreeting}, ${userName}! Your cybersecurity adventure awaits.`
    ];

    return greetings[Math.floor(Math.random() * greetings.length)];
  };

  // Get motivational message based on progress
  const getMotivationalMessage = () => {
    const { modulesCompleted, currentStreak, totalPoints } = userStats;
    
    if (currentStreak >= 7) {
      return `🔥 Amazing! You're on a ${currentStreak}-day streak. Keep the momentum going!`;
    } else if (modulesCompleted >= 10) {
      return `🎯 Great progress! You've completed ${modulesCompleted} modules. You're becoming a cybersecurity expert!`;
    } else if (totalPoints >= 500) {
      return `⭐ Excellent work! You've earned ${totalPoints} points. Your dedication is paying off!`;
    } else {
      return `🚀 Welcome to your cybersecurity journey! Every expert was once a beginner.`;
    }
  };

  // Dashboard stats cards
  const statsCards = [
    {
      title: 'Current Level',
      value: userLevel.name,
      subtitle: `${userStats.totalPoints} points`,
      icon: FaGraduationCap,
      color: 'blue',
      progress: userLevel.nextLevelPoints ? 
        (userStats.totalPoints / userLevel.nextLevelPoints) * 100 : 100
    },
    {
      title: 'Modules Completed',
      value: userStats.modulesCompleted,
      subtitle: 'Learning modules',
      icon: FaBookOpen,
      color: 'green',
      trend: '+2 this week'
    },
    {
      title: 'Current Streak',
      value: `${currentStreak} days`,
      subtitle: 'Daily challenges',
      icon: FaFire,
      color: 'orange',
      trend: currentStreak > 0 ? 'Active' : 'Start today!'
    },
    {
      title: 'Total Points',
      value: userStats.totalPoints.toLocaleString(),
      subtitle: 'Experience points',
      icon: FaTrophy,
      color: 'purple',
      trend: '+150 this week'
    }
  ];

  // Quick actions
  const quickActions = [
    {
      title: 'Continue Learning',
      description: 'Resume your current module',
      icon: FaRocket,
      color: 'blue',
      action: () => console.log('Continue learning')
    },
    {
      title: 'Daily Challenge',
      description: 'Complete today\'s challenge',
      icon: FaBullseye,
      color: 'red',
      action: () => console.log('Daily challenge')
    },
    {
      title: 'Learning Resources',
      description: 'Browse materials',
      icon: FaLightbulb,
      color: 'yellow',
      action: () => console.log('Learning resources')
    },
    {
      title: 'View Progress',
      description: 'Check your analytics',
      icon: FaChartLine,
      color: 'green',
      action: () => console.log('View progress')
    }
  ];

  // Recent achievements
  const recentAchievements = [
    {
      title: 'First Module Complete',
      description: 'Completed your first learning module',
      icon: FaMedal,
      date: '2 days ago',
      points: 50
    },
    {
      title: 'Week Warrior',
      description: 'Maintained a 7-day streak',
      icon: FaFire,
      date: '1 week ago',
      points: 100
    }
  ];

  if (progressLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className={`
            ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
            rounded-xl border p-6 shadow-sm
          `}>
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="mb-4 lg:mb-0">
                <h1 className="text-2xl lg:text-3xl font-bold mb-2">
                  {getPersonalizedGreeting()}
                </h1>
                <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {getMotivationalMessage()}
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                <div className={`
                  ${darkMode ? 'bg-gray-700' : 'bg-blue-50'}
                  px-4 py-2 rounded-lg
                `}>
                  <div className="text-sm font-medium">
                    {currentTime.toLocaleDateString('en-US', { 
                      weekday: 'long',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                  <div className="text-xs opacity-75">
                    {currentTime.toLocaleTimeString('en-US', { 
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          {statsCards.map((card, index) => (
            <motion.div
              key={card.title}
              whileHover={{ scale: 1.02 }}
              className={`
                ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
                rounded-xl border p-6 shadow-sm hover:shadow-md transition-all duration-200
              `}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`
                  w-12 h-12 rounded-lg flex items-center justify-center
                  ${card.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                    card.color === 'green' ? 'bg-green-100 text-green-600' :
                    card.color === 'orange' ? 'bg-orange-100 text-orange-600' :
                    'bg-purple-100 text-purple-600'}
                `}>
                  <card.icon className="text-xl" />
                </div>
                {card.trend && (
                  <span className={`
                    text-xs px-2 py-1 rounded-full
                    ${card.color === 'green' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}
                  `}>
                    {card.trend}
                  </span>
                )}
              </div>
              
              <div>
                <div className="text-2xl font-bold mb-1">{card.value}</div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {card.subtitle}
                </div>
                
                {card.progress && (
                  <div className="mt-3">
                    <div className={`w-full h-2 rounded-full ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                      <div 
                        className={`h-2 rounded-full ${
                          card.color === 'blue' ? 'bg-blue-600' :
                          card.color === 'green' ? 'bg-green-600' :
                          card.color === 'orange' ? 'bg-orange-600' :
                          'bg-purple-600'
                        }`}
                        style={{ width: `${Math.min(card.progress, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <h2 className="text-xl font-bold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <motion.button
                key={action.title}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={action.action}
                className={`
                  ${darkMode ? 'bg-gray-800 border-gray-700 hover:bg-gray-700' : 'bg-white border-gray-200 hover:bg-gray-50'}
                  rounded-xl border p-4 text-left transition-all duration-200
                `}
              >
                <div className={`
                  w-10 h-10 rounded-lg flex items-center justify-center mb-3
                  ${action.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                    action.color === 'red' ? 'bg-red-100 text-red-600' :
                    action.color === 'yellow' ? 'bg-yellow-100 text-yellow-600' :
                    'bg-green-100 text-green-600'}
                `}>
                  <action.icon />
                </div>
                <div className="font-medium mb-1">{action.title}</div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {action.description}
                </div>
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Recent Achievements */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mb-8"
        >
          <h2 className="text-xl font-bold mb-4">Recent Achievements</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {recentAchievements.map((achievement, index) => (
              <motion.div
                key={achievement.title}
                whileHover={{ scale: 1.01 }}
                className={`
                  ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
                  rounded-xl border p-4 flex items-center gap-4
                `}
              >
                <div className="w-12 h-12 bg-yellow-100 text-yellow-600 rounded-lg flex items-center justify-center">
                  <achievement.icon className="text-xl" />
                </div>
                <div className="flex-1">
                  <div className="font-medium">{achievement.title}</div>
                  <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {achievement.description}
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    +{achievement.points} points • {achievement.date}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default EnhancedDashboard;
