import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FaTrophy, 
  FaMedal, 
  FaStar, 
  FaFire,
  FaChartLine,
  FaCrown,
  FaUser,
  FaCalendarAlt,
  FaGlobe,
  FaUsers
} from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useAuth } from '../../contexts/AuthContext';

/**
 * EnhancedLeaderboards Component
 * 
 * A comprehensive leaderboards system with multiple categories,
 * timeframes, and real-time updates.
 */
const EnhancedLeaderboards = () => {
  const { darkMode } = useGlobalTheme();
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState('overall');
  const [selectedTimeframe, setSelectedTimeframe] = useState('all-time');

  // Leaderboard categories
  const categories = [
    { id: 'overall', name: 'Overall XP', icon: FaTrophy },
    { id: 'streaks', name: 'Learning Streaks', icon: FaFire },
    { id: 'challenges', name: 'Challenges', icon: FaStar },
    { id: 'certifications', name: 'Certifications', icon: FaMedal },
    { id: 'modules', name: 'Modules Completed', icon: FaChartLine }
  ];

  // Timeframe options
  const timeframes = [
    { id: 'all-time', name: 'All Time' },
    { id: 'monthly', name: 'This Month' },
    { id: 'weekly', name: 'This Week' },
    { id: 'daily', name: 'Today' }
  ];

  // Mock leaderboard data
  const leaderboardData = {
    overall: [
      { rank: 1, name: 'Alex Chen', avatar: null, score: 15420, change: 0, isCurrentUser: false },
      { rank: 2, name: 'Sarah Johnson', avatar: null, score: 14890, change: 1, isCurrentUser: false },
      { rank: 3, name: 'Mike Rodriguez', avatar: null, score: 14250, change: -1, isCurrentUser: false },
      { rank: 4, name: 'Emily Davis', avatar: null, score: 13800, change: 2, isCurrentUser: false },
      { rank: 5, name: 'David Kim', avatar: null, score: 13450, change: 0, isCurrentUser: false },
      { rank: 6, name: 'Lisa Wang', avatar: null, score: 12900, change: -2, isCurrentUser: false },
      { rank: 7, name: 'You', avatar: null, score: 12650, change: 1, isCurrentUser: true },
      { rank: 8, name: 'John Smith', avatar: null, score: 12400, change: -1, isCurrentUser: false },
      { rank: 9, name: 'Anna Brown', avatar: null, score: 12100, change: 0, isCurrentUser: false },
      { rank: 10, name: 'Tom Wilson', avatar: null, score: 11850, change: 1, isCurrentUser: false }
    ],
    streaks: [
      { rank: 1, name: 'Sarah Johnson', avatar: null, score: 45, change: 0, isCurrentUser: false },
      { rank: 2, name: 'Alex Chen', avatar: null, score: 42, change: 1, isCurrentUser: false },
      { rank: 3, name: 'You', avatar: null, score: 38, change: 2, isCurrentUser: true },
      { rank: 4, name: 'Mike Rodriguez', avatar: null, score: 35, change: -2, isCurrentUser: false },
      { rank: 5, name: 'Emily Davis', avatar: null, score: 32, change: 0, isCurrentUser: false }
    ],
    challenges: [
      { rank: 1, name: 'Mike Rodriguez', avatar: null, score: 156, change: 0, isCurrentUser: false },
      { rank: 2, name: 'Alex Chen', avatar: null, score: 142, change: 1, isCurrentUser: false },
      { rank: 3, name: 'You', avatar: null, score: 128, change: 3, isCurrentUser: true },
      { rank: 4, name: 'Sarah Johnson', avatar: null, score: 125, change: -2, isCurrentUser: false },
      { rank: 5, name: 'Emily Davis', avatar: null, score: 118, change: -1, isCurrentUser: false }
    ]
  };

  // Get current leaderboard data
  const currentData = leaderboardData[selectedCategory] || leaderboardData.overall;

  // Get rank change indicator
  const getRankChangeIcon = (change) => {
    if (change > 0) return { icon: '↗', color: 'text-green-500' };
    if (change < 0) return { icon: '↘', color: 'text-red-500' };
    return { icon: '→', color: 'text-gray-500' };
  };

  // Get rank badge
  const getRankBadge = (rank) => {
    if (rank === 1) return { icon: FaCrown, color: 'text-yellow-500', bg: 'bg-yellow-500/20' };
    if (rank === 2) return { icon: FaMedal, color: 'text-gray-400', bg: 'bg-gray-400/20' };
    if (rank === 3) return { icon: FaMedal, color: 'text-amber-600', bg: 'bg-amber-600/20' };
    return { icon: FaUser, color: 'text-gray-500', bg: 'bg-gray-500/20' };
  };

  // Get score label based on category
  const getScoreLabel = (category) => {
    switch (category) {
      case 'overall': return 'XP';
      case 'streaks': return 'Days';
      case 'challenges': return 'Completed';
      case 'certifications': return 'Earned';
      case 'modules': return 'Completed';
      default: return 'Points';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6`}>
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold mb-2 flex items-center">
              <FaTrophy className="mr-3 text-yellow-500" />
              Leaderboards
            </h2>
            <p className="text-gray-500 dark:text-gray-400">
              See how you rank against other cybersecurity learners worldwide.
            </p>
          </div>
          
          {/* User's Best Rank */}
          <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} rounded-lg p-4 text-center`}>
            <div className="text-2xl font-bold text-blue-500">#7</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Your Best Rank</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6`}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Category Filter */}
          <div>
            <h3 className="text-lg font-bold mb-4">Category</h3>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`
                    flex items-center px-4 py-2 rounded-lg transition-colors
                    ${selectedCategory === category.id
                      ? 'bg-blue-500 text-white'
                      : darkMode
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }
                  `}
                >
                  <category.icon className="mr-2" />
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          {/* Timeframe Filter */}
          <div>
            <h3 className="text-lg font-bold mb-4">Timeframe</h3>
            <div className="flex flex-wrap gap-2">
              {timeframes.map((timeframe) => (
                <button
                  key={timeframe.id}
                  onClick={() => setSelectedTimeframe(timeframe.id)}
                  className={`
                    px-4 py-2 rounded-lg transition-colors
                    ${selectedTimeframe === timeframe.id
                      ? 'bg-green-500 text-white'
                      : darkMode
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }
                  `}
                >
                  {timeframe.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Top 3 Podium */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6`}>
        <h3 className="text-lg font-bold mb-6">Top Performers</h3>
        <div className="flex justify-center items-end space-x-4">
          {currentData.slice(0, 3).map((user, index) => {
            const position = index + 1;
            const heights = ['h-24', 'h-32', 'h-20']; // 2nd, 1st, 3rd
            const order = [1, 0, 2]; // Display order: 2nd, 1st, 3rd
            const actualIndex = order[index];
            const actualUser = currentData[actualIndex];
            const actualPosition = actualIndex + 1;
            const badge = getRankBadge(actualPosition);
            
            return (
              <motion.div
                key={actualUser.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`
                  ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} 
                  rounded-lg p-4 text-center ${heights[index]} flex flex-col justify-end
                  ${actualUser.isCurrentUser ? 'ring-2 ring-blue-500' : ''}
                `}
              >
                <div className={`w-12 h-12 rounded-full mx-auto mb-2 ${badge.bg} flex items-center justify-center`}>
                  <badge.icon className={`text-xl ${badge.color}`} />
                </div>
                <div className="font-bold text-sm">{actualUser.name}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {actualUser.score.toLocaleString()} {getScoreLabel(selectedCategory)}
                </div>
                <div className="text-lg font-bold text-yellow-500">#{actualPosition}</div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Full Leaderboard */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6`}>
        <h3 className="text-lg font-bold mb-4">Full Rankings</h3>
        <div className="space-y-2">
          {currentData.map((user, index) => {
            const badge = getRankBadge(user.rank);
            const changeIndicator = getRankChangeIcon(user.change);
            
            return (
              <motion.div
                key={user.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                className={`
                  ${darkMode ? 'bg-gray-700' : 'bg-gray-100'} 
                  rounded-lg p-4 flex items-center justify-between
                  ${user.isCurrentUser ? 'ring-2 ring-blue-500 bg-blue-500/10' : ''}
                `}
              >
                <div className="flex items-center space-x-4">
                  {/* Rank */}
                  <div className="text-2xl font-bold w-8 text-center">
                    #{user.rank}
                  </div>
                  
                  {/* Badge */}
                  <div className={`w-10 h-10 rounded-full ${badge.bg} flex items-center justify-center`}>
                    <badge.icon className={`${badge.color}`} />
                  </div>
                  
                  {/* User Info */}
                  <div>
                    <div className="font-bold flex items-center">
                      {user.name}
                      {user.isCurrentUser && (
                        <span className="ml-2 px-2 py-1 bg-blue-500 text-white text-xs rounded-full">
                          You
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {user.score.toLocaleString()} {getScoreLabel(selectedCategory)}
                    </div>
                  </div>
                </div>
                
                {/* Rank Change */}
                <div className={`flex items-center ${changeIndicator.color}`}>
                  <span className="text-lg mr-1">{changeIndicator.icon}</span>
                  <span className="text-sm">
                    {user.change === 0 ? 'No change' : `${Math.abs(user.change)} ${user.change > 0 ? 'up' : 'down'}`}
                  </span>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Stats Summary */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6`}>
        <h3 className="text-lg font-bold mb-4">Community Stats</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} rounded-lg p-4 text-center`}>
            <FaUsers className="text-blue-500 text-2xl mx-auto mb-2" />
            <div className="text-2xl font-bold">2,847</div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Active Learners</div>
          </div>
          
          <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} rounded-lg p-4 text-center`}>
            <FaGlobe className="text-green-500 text-2xl mx-auto mb-2" />
            <div className="text-2xl font-bold">67</div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Countries</div>
          </div>
          
          <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} rounded-lg p-4 text-center`}>
            <FaCalendarAlt className="text-purple-500 text-2xl mx-auto mb-2" />
            <div className="text-2xl font-bold">156</div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Days Active</div>
          </div>
          
          <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-100'} rounded-lg p-4 text-center`}>
            <FaChartLine className="text-orange-500 text-2xl mx-auto mb-2" />
            <div className="text-2xl font-bold">94%</div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Completion Rate</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedLeaderboards;
