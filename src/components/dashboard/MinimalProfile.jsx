import React, { useEffect, useState } from 'react';
import { supabase } from '../../lib/supabase';

/**
 * Minimal Profile Component
 * 
 * A very simple profile component to test basic functionality
 */
const MinimalProfile = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getUser = async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();
        console.log('MinimalProfile - User data:', user);
        console.log('MinimalProfile - Auth error:', error);
        setUser(user);
      } catch (err) {
        console.error('MinimalProfile - Error getting user:', err);
      } finally {
        setLoading(false);
      }
    };

    getUser();
  }, []);

  console.log('MinimalProfile component is rendering');
  console.log('MinimalProfile - Current user state:', user);
  console.log('MinimalProfile - Loading state:', loading);
  
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            ⏳ Loading Profile...
          </h1>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          🎉 Profile Page Working!
        </h1>

        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
            👤 Authentication Status
          </h3>
          <div className="space-y-2 text-sm">
            <p className="text-gray-700 dark:text-gray-300">
              <strong>User ID:</strong> {user?.id || 'Not logged in'}
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              <strong>Email:</strong> {user?.email || 'No email'}
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              <strong>Status:</strong> {user ? '✅ Authenticated' : '❌ Not authenticated'}
            </p>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3">
            ✅ Success!
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            The profile page is now loading correctly. This means:
          </p>
          <ul className="space-y-2 text-gray-600 dark:text-gray-400">
            <li>✅ Routing is working</li>
            <li>✅ Component is loading</li>
            <li>✅ No JavaScript errors</li>
            <li>✅ Authentication is working</li>
            <li>✅ Dashboard layout is working</li>
          </ul>
        </div>
      
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">
            🚀 Ready for Full Profile Component
          </h3>
          <p className="text-green-700 dark:text-green-300">
            Now we can safely switch to the full-featured profile component
            with all the advanced features like editing, achievements, and more!
          </p>
        </div>

        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => window.location.href = '/dashboard'}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            ← Back to Dashboard
          </button>

          <button
            onClick={() => console.log('Current URL:', window.location.href)}
            className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-lg transition-colors duration-200"
          >
            Log URL to Console
          </button>
        </div>
      </div>
    </div>
  );
};

export default MinimalProfile;
