import React, { useState, useEffect } from 'react';
import { FaCheck, FaTimes, FaRedo, FaLightbulb } from 'react-icons/fa';

/**
 * Packet Analysis Exercise Component
 * 
 * This component provides an interactive exercise for analyzing TCP and UDP packets,
 * identifying key fields, and understanding protocol differences.
 */
const PacketAnalysisExercise = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [showExplanation, setShowExplanation] = useState(false);
  const [score, setScore] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [answeredQuestions, setAnsweredQuestions] = useState([]);
  
  // Exercise questions
  const questions = [
    {
      id: 1,
      type: 'tcp-header',
      question: "Which field in the TCP header is used to establish a connection?",
      image: "tcp-header.png",
      options: [
        "Window Size",
        "Sequence Number",
        "Flags (SYN bit)",
        "Checksum"
      ],
      correctAnswer: 2,
      explanation: "The SYN (synchronize) flag in the TCP header is used to initiate a connection. When a client wants to establish a connection with a server, it sends a packet with the SYN flag set to 1.",
      hint: "Look at the flags field in the TCP header. One specific flag is used to start the connection process."
    },
    {
      id: 2,
      type: 'tcp-handshake',
      question: "In a TCP 3-way handshake, what does the server send in response to a SYN packet?",
      image: "tcp-handshake.png",
      options: [
        "ACK packet",
        "SYN-ACK packet",
        "FIN packet",
        "RST packet"
      ],
      correctAnswer: 1,
      explanation: "In the second step of the TCP 3-way handshake, the server responds to the client's SYN packet with a SYN-ACK packet. This acknowledges the client's sequence number and establishes the server's own initial sequence number.",
      hint: "The server needs to both acknowledge the client's synchronization request and establish its own sequence number."
    },
    {
      id: 3,
      type: 'udp-header',
      question: "Which of the following fields is NOT present in a UDP header?",
      image: "udp-header.png",
      options: [
        "Source Port",
        "Destination Port",
        "Sequence Number",
        "Checksum"
      ],
      correctAnswer: 2,
      explanation: "UDP headers do not contain sequence numbers because UDP is a connectionless protocol that doesn't track the order of packets or guarantee delivery. This is one of the key differences between UDP and TCP.",
      hint: "UDP is designed to be simple and fast, without the overhead of tracking packet order or ensuring delivery."
    },
    {
      id: 4,
      type: 'wireshark',
      question: "In a Wireshark capture, how can you identify a TCP packet that is part of the 3-way handshake?",
      image: "wireshark-capture.png",
      options: [
        "By the packet length",
        "By the protocol column showing 'TCP'",
        "By the flags (SYN, SYN-ACK, ACK) in the info column",
        "By the source and destination ports"
      ],
      correctAnswer: 2,
      explanation: "In Wireshark, TCP packets that are part of the 3-way handshake can be identified by looking at the Info column, which will show the flags set in the packet. The first packet will have [SYN], the second [SYN, ACK], and the third [ACK].",
      hint: "Look at the information displayed in the 'Info' column of Wireshark for special indicators."
    },
    {
      id: 5,
      type: 'tcp-vs-udp',
      question: "Which protocol would be better for streaming live video?",
      image: "streaming-video.png",
      options: [
        "TCP, because it guarantees delivery of all packets",
        "UDP, because it's faster and some packet loss is acceptable",
        "Both are equally suitable",
        "Neither, a different protocol should be used"
      ],
      correctAnswer: 1,
      explanation: "UDP is generally better for streaming live video because it prioritizes speed over reliability. In live video streaming, it's usually better to skip frames (accept some packet loss) than to wait for retransmissions, which would cause buffering and delays.",
      hint: "Think about what's more important for live video: perfect delivery of every frame or maintaining real-time playback with minimal delay?"
    },
    {
      id: 6,
      type: 'tcp-reliability',
      question: "How does TCP ensure reliable data delivery?",
      image: "tcp-reliability.png",
      options: [
        "By using larger packets",
        "By sending multiple copies of each packet",
        "By using sequence numbers and acknowledgments",
        "By encrypting the data"
      ],
      correctAnswer: 2,
      explanation: "TCP ensures reliable data delivery through a system of sequence numbers and acknowledgments. Each byte of data is assigned a sequence number, and the receiver acknowledges receipt by sending back the next expected sequence number. If an acknowledgment isn't received within a timeout period, the sender retransmits the data.",
      hint: "TCP needs a way to track which data has been sent and received successfully."
    },
    {
      id: 7,
      type: 'udp-use-case',
      question: "Which of these applications would most likely use UDP instead of TCP?",
      image: "application-protocols.png",
      options: [
        "Email (SMTP)",
        "File transfer (FTP)",
        "Web browsing (HTTP)",
        "Online gaming"
      ],
      correctAnswer: 3,
      explanation: "Online gaming typically uses UDP because it prioritizes low latency over reliability. In fast-paced games, it's better to have the most current game state with occasional glitches than to have perfect data that arrives too late to be useful.",
      hint: "Think about which application needs the fastest possible response time, where occasional data loss wouldn't be catastrophic."
    },
    {
      id: 8,
      type: 'packet-analysis',
      question: "In a Wireshark capture, what does a TCP packet with the RST flag indicate?",
      image: "tcp-rst.png",
      options: [
        "Normal connection closure",
        "Abrupt connection termination",
        "Data retransmission",
        "Connection establishment"
      ],
      correctAnswer: 1,
      explanation: "A TCP packet with the RST (reset) flag indicates an abrupt connection termination. This can happen when a host receives a packet for a connection that doesn't exist, when there's an error condition, or when a host wants to immediately terminate a connection without going through the normal closure process.",
      hint: "Think about what might happen if there's a problem with a connection that requires immediate action."
    },
    {
      id: 9,
      type: 'tcp-flow-control',
      question: "What TCP header field is used for flow control?",
      image: "tcp-flow-control.png",
      options: [
        "Sequence Number",
        "Acknowledgment Number",
        "Window Size",
        "Urgent Pointer"
      ],
      correctAnswer: 2,
      explanation: "The Window Size field in the TCP header is used for flow control. It indicates how many bytes the receiver is willing to accept, allowing the sender to adjust its transmission rate to avoid overwhelming the receiver.",
      hint: "Flow control involves managing how much data is sent at once to prevent buffer overflow."
    },
    {
      id: 10,
      type: 'protocol-ports',
      question: "Which port number is commonly associated with HTTPS traffic?",
      image: "protocol-ports.png",
      options: [
        "80",
        "443",
        "25",
        "53"
      ],
      correctAnswer: 1,
      explanation: "Port 443 is the standard port for HTTPS (HTTP Secure) traffic. This is the secure version of HTTP that uses SSL/TLS encryption to protect data in transit.",
      hint: "HTTPS is the secure version of HTTP (which uses port 80)."
    }
  ];
  
  // Initialize the exercise
  useEffect(() => {
    // Load any saved progress
    try {
      const savedProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');
      if (savedProgress.exerciseResults && savedProgress.exerciseResults['packet-analysis-exercise']) {
        const result = savedProgress.exerciseResults['packet-analysis-exercise'];
        if (result.answeredQuestions) {
          setAnsweredQuestions(result.answeredQuestions);
          setScore(result.score || 0);
          
          // If all questions are answered, mark as complete
          if (result.answeredQuestions.length === questions.length) {
            setIsComplete(true);
          }
        }
      }
    } catch (error) {
      console.error('Error loading saved progress:', error);
    }
  }, []);
  
  // Handle answer selection
  const handleAnswerSelect = (answerIndex) => {
    setSelectedAnswer(answerIndex);
    setShowExplanation(true);
    
    // Check if this question has already been answered
    if (!answeredQuestions.includes(questions[currentQuestion].id)) {
      // Update score if correct
      if (answerIndex === questions[currentQuestion].correctAnswer) {
        setScore(score + 10);
      }
      
      // Mark question as answered
      const updatedAnsweredQuestions = [...answeredQuestions, questions[currentQuestion].id];
      setAnsweredQuestions(updatedAnsweredQuestions);
      
      // Save progress
      try {
        const userProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');
        if (!userProgress.exerciseResults) {
          userProgress.exerciseResults = {};
        }
        
        userProgress.exerciseResults['packet-analysis-exercise'] = {
          answeredQuestions: updatedAnsweredQuestions,
          score: answerIndex === questions[currentQuestion].correctAnswer ? score + 10 : score,
          lastUpdated: new Date().toISOString()
        };
        
        // If all questions are answered, mark as complete
        if (updatedAnsweredQuestions.length === questions.length) {
          setIsComplete(true);
          userProgress.exerciseResults['packet-analysis-exercise'].completed = true;
          userProgress.exerciseResults['packet-analysis-exercise'].completedAt = new Date().toISOString();
        }
        
        localStorage.setItem('userProgress', JSON.stringify(userProgress));
      } catch (error) {
        console.error('Error saving progress:', error);
      }
    }
  };
  
  // Go to next question
  const nextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
      setShowExplanation(false);
      setShowHint(false);
    }
  };
  
  // Go to previous question
  const prevQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
      setSelectedAnswer(null);
      setShowExplanation(false);
      setShowHint(false);
    }
  };
  
  // Reset the current question
  const resetQuestion = () => {
    setSelectedAnswer(null);
    setShowExplanation(false);
    setShowHint(false);
  };
  
  // Toggle hint visibility
  const toggleHint = () => {
    setShowHint(!showHint);
  };
  
  // Calculate completion percentage
  const completionPercentage = Math.round((answeredQuestions.length / questions.length) * 100);
  
  // Get current question
  const currentQuestionData = questions[currentQuestion];
  
  // Check if current question has been answered
  const isAnswered = answeredQuestions.includes(currentQuestionData.id);
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-6 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold">TCP/IP Packet Analysis Exercise</h3>
        
        {isComplete && (
          <div className="bg-green-900/30 text-green-400 px-3 py-1 rounded-full text-sm font-medium border border-green-500/30">
            Completed
          </div>
        )}
      </div>
      
      {/* Progress Indicator */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-400 mb-1">
          <span>Question {currentQuestion + 1} of {questions.length}</span>
          <span>Score: {score}/{questions.length * 10}</span>
        </div>
        <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
          <div 
            className="h-full bg-primary transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
      </div>
      
      {/* Question */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold mb-2">{currentQuestionData.question}</h4>
        
        {/* Placeholder for image - in a real implementation, this would display the actual image */}
        <div className="bg-[#1E293B] h-40 rounded-lg mb-4 flex items-center justify-center border border-gray-700">
          <span className="text-gray-400">
            [Image: {currentQuestionData.type}]
          </span>
        </div>
        
        {/* Hint */}
        {showHint && (
          <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-3 mb-4">
            <div className="flex items-center text-blue-400 font-semibold mb-1">
              <FaLightbulb className="mr-2" /> Hint
            </div>
            <p className="text-sm text-gray-300">{currentQuestionData.hint}</p>
          </div>
        )}
        
        {/* Answer Options */}
        <div className="space-y-2">
          {currentQuestionData.options.map((option, index) => (
            <button
              key={index}
              onClick={() => handleAnswerSelect(index)}
              disabled={isAnswered && selectedAnswer !== index}
              className={`w-full text-left p-3 rounded-lg border ${
                selectedAnswer === index
                  ? index === currentQuestionData.correctAnswer
                    ? 'bg-green-900/20 border-green-500 text-green-400'
                    : 'bg-red-900/20 border-red-500 text-red-400'
                  : isAnswered && index === currentQuestionData.correctAnswer
                    ? 'bg-green-900/20 border-green-500 text-green-400'
                    : 'bg-[#1E293B] border-gray-700 hover:border-primary'
              } transition-colors`}
            >
              <div className="flex justify-between items-center">
                <span>{option}</span>
                {selectedAnswer === index && (
                  index === currentQuestionData.correctAnswer
                    ? <FaCheck className="text-green-500" />
                    : <FaTimes className="text-red-500" />
                )}
                {isAnswered && selectedAnswer !== index && index === currentQuestionData.correctAnswer && (
                  <FaCheck className="text-green-500" />
                )}
              </div>
            </button>
          ))}
        </div>
      </div>
      
      {/* Explanation */}
      {showExplanation && (
        <div className={`p-4 rounded-lg mb-6 ${
          selectedAnswer === currentQuestionData.correctAnswer
            ? 'bg-green-900/20 border border-green-700'
            : 'bg-red-900/20 border border-red-700'
        }`}>
          <h4 className={`font-bold mb-2 ${
            selectedAnswer === currentQuestionData.correctAnswer
              ? 'text-green-400'
              : 'text-red-400'
          }`}>
            {selectedAnswer === currentQuestionData.correctAnswer ? 'Correct!' : 'Incorrect'}
          </h4>
          <p className="text-gray-300">{currentQuestionData.explanation}</p>
        </div>
      )}
      
      {/* Controls */}
      <div className="flex justify-between">
        <div>
          <button
            onClick={prevQuestion}
            disabled={currentQuestion === 0}
            className={`px-4 py-2 rounded-lg mr-2 ${
              currentQuestion === 0
                ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                : 'bg-[#1E293B] text-white hover:bg-[#334155]'
            }`}
          >
            Previous
          </button>
          <button
            onClick={nextQuestion}
            disabled={currentQuestion === questions.length - 1}
            className={`px-4 py-2 rounded-lg ${
              currentQuestion === questions.length - 1
                ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                : 'bg-[#1E293B] text-white hover:bg-[#334155]'
            }`}
          >
            Next
          </button>
        </div>
        
        <div>
          <button
            onClick={toggleHint}
            className="bg-blue-900/20 text-blue-400 border border-blue-700 px-4 py-2 rounded-lg mr-2 hover:bg-blue-900/30"
          >
            {showHint ? 'Hide Hint' : 'Show Hint'}
          </button>
          
          <button
            onClick={resetQuestion}
            disabled={!selectedAnswer}
            className={`px-4 py-2 rounded-lg ${
              !selectedAnswer
                ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                : 'bg-[#1E293B] text-white hover:bg-[#334155]'
            }`}
          >
            <FaRedo className="inline mr-1" /> Reset
          </button>
        </div>
      </div>
    </div>
  );
};

export default PacketAnalysisExercise;
