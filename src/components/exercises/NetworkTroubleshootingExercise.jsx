import React, { useState, useEffect } from 'react';
import { FaNetworkWired, FaWifi, FaDesktop, FaServer, FaExclamationTriangle, FaCheck, FaTools, FaSearch, FaArrowRight, FaInfoCircle } from 'react-icons/fa';

/**
 * Network Troubleshooting Exercise
 * 
 * This component provides an interactive exercise for troubleshooting common network issues.
 * Users can diagnose and fix problems in a simulated network environment.
 */
const NetworkTroubleshootingExercise = ({ scenarioType = 'connectivity', onComplete }) => {
  // Exercise state
  const [currentScenario, setCurrentScenario] = useState(null);
  const [currentStep, setCurrentStep] = useState('diagnose'); // diagnose, analyze, fix
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [selectedTool, setSelectedTool] = useState(null);
  const [toolResults, setToolResults] = useState(null);
  const [diagnosis, setDiagnosis] = useState('');
  const [solution, setSolution] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [exerciseCompleted, setExerciseCompleted] = useState(false);
  const [showHint, setShowHint] = useState(false);
  
  // Available troubleshooting scenarios
  const scenarios = [
    {
      id: 'ip-conflict',
      type: 'connectivity',
      title: 'IP Address Conflict',
      description: 'Users are reporting that they sometimes lose connection to the network. The issue seems to affect different users at different times.',
      networkMap: [
        { id: 'router', type: 'router', name: 'Main Router', x: 400, y: 80, status: 'normal' },
        { id: 'switch', type: 'switch', name: 'Core Switch', x: 400, y: 160, status: 'normal' },
        { id: 'server', type: 'server', name: 'File Server', x: 600, y: 240, status: 'normal' },
        { id: 'pc1', type: 'pc', name: 'Workstation 1', x: 200, y: 240, status: 'warning', 
          config: { ip: '*************', subnet: '*************', gateway: '***********', dns: '***********' } },
        { id: 'pc2', type: 'pc', name: 'Workstation 2', x: 400, y: 240, status: 'warning',
          config: { ip: '*************', subnet: '*************', gateway: '***********', dns: '***********' } }
      ],
      issue: 'IP address conflict between Workstation 1 and Workstation 2',
      hints: [
        'Check the IP addresses of the devices reporting issues',
        'Look for duplicate IP addresses in the network',
        'Use the "ipconfig" tool to view IP configurations'
      ],
      correctDiagnosis: ['ip conflict', 'duplicate ip', 'address conflict', 'same ip'],
      correctSolution: ['change ip', 'dhcp', 'static ip', 'different ip'],
      toolResponses: {
        'ping': {
          'pc1': 'Pinging ***********... Reply from ***********: bytes=32 time=1ms TTL=64\nPinging *************... Reply from *************: bytes=32 time<1ms TTL=128',
          'pc2': 'Pinging ***********... Reply from ***********: bytes=32 time=1ms TTL=64\nPinging *************... Reply from *************: bytes=32 time<1ms TTL=128',
          'server': 'Pinging *************... Sometimes gets a response from Workstation 1, sometimes from Workstation 2'
        },
        'ipconfig': {
          'pc1': 'IPv4 Address: *************\nSubnet Mask: *************\nDefault Gateway: ***********',
          'pc2': 'IPv4 Address: *************\nSubnet Mask: *************\nDefault Gateway: ***********'
        },
        'tracert': {
          'pc1': 'Tracing route to ***********...\n1: <1ms <1ms <1ms ***********',
          'pc2': 'Tracing route to ***********...\n1: <1ms <1ms <1ms ***********'
        },
        'arp': {
          'pc1': 'Interface: *************\nInternet Address    Physical Address      Type\n***********         00-11-22-33-44-55     dynamic\n*************       AA-BB-CC-DD-EE-FF     dynamic',
          'pc2': 'Interface: *************\nInternet Address    Physical Address      Type\n***********         00-11-22-33-44-55     dynamic\n*************       11-22-33-44-55-66     dynamic'
        }
      },
      fixOptions: [
        { id: 'dhcp', label: 'Configure both workstations to use DHCP', correct: true },
        { id: 'static', label: 'Assign a different static IP to Workstation 2', correct: true },
        { id: 'reboot', label: 'Reboot both workstations', correct: false },
        { id: 'cable', label: 'Replace the network cables', correct: false }
      ]
    },
    {
      id: 'dns-failure',
      type: 'connectivity',
      title: 'DNS Resolution Failure',
      description: 'Users can access websites by IP address but not by domain name. Internal network resources are accessible.',
      networkMap: [
        { id: 'router', type: 'router', name: 'Main Router', x: 400, y: 80, status: 'normal' },
        { id: 'switch', type: 'switch', name: 'Core Switch', x: 400, y: 160, status: 'normal' },
        { id: 'dns', type: 'server', name: 'DNS Server', x: 600, y: 160, status: 'error' },
        { id: 'pc1', type: 'pc', name: 'Workstation 1', x: 200, y: 240, status: 'warning',
          config: { ip: '***********01', subnet: '*************', gateway: '***********', dns: '***********' } },
        { id: 'pc2', type: 'pc', name: 'Workstation 2', x: 400, y: 240, status: 'warning',
          config: { ip: '***********02', subnet: '*************', gateway: '***********', dns: '***********' } }
      ],
      issue: 'DNS server is down or misconfigured',
      hints: [
        'Check if users can access websites using IP addresses',
        'Verify the DNS server settings on the workstations',
        'Test DNS resolution using nslookup or dig'
      ],
      correctDiagnosis: ['dns', 'name resolution', 'domain name', 'dns server'],
      correctSolution: ['fix dns', 'change dns', 'update dns', 'dns server'],
      toolResponses: {
        'ping': {
          'pc1': 'Pinging ***********... Reply from ***********: bytes=32 time=1ms TTL=64\nPinging *******... Reply from *******: bytes=32 time=20ms TTL=56\nPinging google.com... Ping request could not find host google.com. Please check the name and try again.',
          'pc2': 'Pinging ***********... Reply from ***********: bytes=32 time=1ms TTL=64\nPinging *******... Reply from *******: bytes=32 time=22ms TTL=56\nPinging google.com... Ping request could not find host google.com. Please check the name and try again.',
          'dns': 'Request timed out.'
        },
        'ipconfig': {
          'pc1': 'IPv4 Address: ***********01\nSubnet Mask: *************\nDefault Gateway: ***********\nDNS Servers: ***********',
          'pc2': 'IPv4 Address: ***********02\nSubnet Mask: *************\nDefault Gateway: ***********\nDNS Servers: ***********'
        },
        'nslookup': {
          'pc1': 'Server: ***********\nAddress: ***********\n*** *********** can\'t find google.com: Server failed',
          'pc2': 'Server: ***********\nAddress: ***********\n*** *********** can\'t find google.com: Server failed'
        },
        'tracert': {
          'pc1': 'Tracing route to *******...\n1: <1ms <1ms <1ms ***********\n2: 10ms 10ms 10ms ***********\n3: 15ms 15ms 15ms *******\nTrace complete.',
          'pc2': 'Tracing route to *******...\n1: <1ms <1ms <1ms ***********\n2: 12ms 12ms 12ms ***********\n3: 18ms 18ms 18ms *******\nTrace complete.'
        }
      },
      fixOptions: [
        { id: 'dns1', label: 'Change DNS server to ******* on workstations', correct: true },
        { id: 'dns2', label: 'Restart the DNS server', correct: true },
        { id: 'hosts', label: 'Edit hosts file on each workstation', correct: false },
        { id: 'flush', label: 'Flush DNS cache on workstations', correct: false }
      ]
    }
  ];
  
  // Available troubleshooting tools
  const tools = [
    { id: 'ping', name: 'Ping', description: 'Test connectivity to another device', icon: <FaNetworkWired /> },
    { id: 'ipconfig', name: 'IP Config', description: 'View network configuration', icon: <FaInfoCircle /> },
    { id: 'tracert', name: 'Trace Route', description: 'Show the path to a destination', icon: <FaArrowRight /> },
    { id: 'nslookup', name: 'NS Lookup', description: 'Query DNS records', icon: <FaSearch /> },
    { id: 'arp', name: 'ARP', description: 'View Address Resolution Protocol table', icon: <FaNetworkWired /> }
  ];
  
  // Initialize scenario based on type
  useEffect(() => {
    const matchingScenarios = scenarios.filter(s => s.type === scenarioType);
    if (matchingScenarios.length > 0) {
      // Select a random scenario of the specified type
      const randomIndex = Math.floor(Math.random() * matchingScenarios.length);
      setCurrentScenario(matchingScenarios[randomIndex]);
    } else {
      // Fallback to the first scenario
      setCurrentScenario(scenarios[0]);
    }
  }, [scenarioType]);
  
  // Handle device selection
  const handleDeviceSelect = (device) => {
    setSelectedDevice(device);
    setToolResults(null);
    setSelectedTool(null);
  };
  
  // Handle tool selection
  const handleToolSelect = (tool) => {
    setSelectedTool(tool);
    
    // Get tool results if a device is selected
    if (selectedDevice && currentScenario.toolResponses[tool.id] && currentScenario.toolResponses[tool.id][selectedDevice.id]) {
      setToolResults({
        tool: tool.name,
        device: selectedDevice.name,
        output: currentScenario.toolResponses[tool.id][selectedDevice.id]
      });
    } else {
      setToolResults({
        tool: tool.name,
        device: selectedDevice.name,
        output: 'No data available or command not applicable to this device.'
      });
    }
  };
  
  // Handle diagnosis submission
  const handleDiagnosisSubmit = () => {
    // Check if diagnosis contains any of the correct keywords
    const diagnosisLower = diagnosis.toLowerCase();
    const isCorrectDiagnosis = currentScenario.correctDiagnosis.some(keyword => 
      diagnosisLower.includes(keyword.toLowerCase())
    );
    
    if (isCorrectDiagnosis) {
      setIsCorrect(true);
      setCurrentStep('fix');
    } else {
      setIsCorrect(false);
    }
  };
  
  // Handle solution selection
  const handleSolutionSelect = (option) => {
    setSolution(option.label);
    
    if (option.correct) {
      setIsCorrect(true);
      setExerciseCompleted(true);
      
      // Save progress
      try {
        const userProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');
        if (!userProgress.troubleshooting) {
          userProgress.troubleshooting = {};
        }
        userProgress.troubleshooting[currentScenario.id] = {
          completed: true,
          completedAt: new Date().toISOString()
        };
        localStorage.setItem('userProgress', JSON.stringify(userProgress));
        
        // Call onComplete callback if provided
        if (onComplete) {
          onComplete();
        }
      } catch (error) {
        console.error('Error saving progress:', error);
      }
    } else {
      setIsCorrect(false);
    }
  };
  
  // Render device icon based on type
  const renderDeviceIcon = (type) => {
    switch (type) {
      case 'router':
        return <FaNetworkWired className="text-blue-400 text-xl" />;
      case 'switch':
        return <FaNetworkWired className="text-green-400 text-xl" />;
      case 'server':
        return <FaServer className="text-yellow-400 text-xl" />;
      case 'pc':
        return <FaDesktop className="text-gray-400 text-xl" />;
      case 'ap':
        return <FaWifi className="text-purple-400 text-xl" />;
      default:
        return <FaDesktop className="text-gray-400 text-xl" />;
    }
  };
  
  // Render device status indicator
  const renderDeviceStatus = (status) => {
    switch (status) {
      case 'normal':
        return <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"></div>;
      case 'warning':
        return <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full"></div>;
      case 'error':
        return <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>;
      default:
        return null;
    }
  };
  
  // If no scenario is loaded yet, show loading
  if (!currentScenario) {
    return <div className="text-center p-4">Loading scenario...</div>;
  }
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Network Troubleshooting: {currentScenario.title}</h3>
        
        <div className="flex space-x-2">
          <button
            onClick={() => setShowHint(!showHint)}
            className={`px-3 py-1 rounded text-sm ${showHint ? 'bg-primary text-black' : 'bg-[#1E293B] text-white'}`}
          >
            {showHint ? 'Hide Hint' : 'Show Hint'}
          </button>
        </div>
      </div>
      
      {/* Scenario Description */}
      <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
        <h4 className="font-bold mb-2">Problem Description</h4>
        <p className="text-gray-300">{currentScenario.description}</p>
        
        {showHint && (
          <div className="mt-3 bg-blue-900/20 border border-blue-500/30 p-3 rounded-lg">
            <h5 className="font-bold text-blue-400 mb-1">Hints</h5>
            <ul className="list-disc list-inside text-gray-300">
              {currentScenario.hints.map((hint, index) => (
                <li key={index}>{hint}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
      
      {/* Network Diagram */}
      <div className="relative h-80 bg-[#1E293B] rounded-lg mb-4 overflow-hidden border border-gray-700">
        {/* Connections */}
        <svg className="absolute inset-0 w-full h-full">
          {currentScenario.networkMap.map((device, i) => 
            currentScenario.networkMap.slice(i + 1).map((otherDevice, j) => {
              // Simple algorithm to draw connections between devices at different levels
              if (Math.abs(device.y - otherDevice.y) === 80 && 
                  (device.x === otherDevice.x || Math.abs(device.x - otherDevice.x) <= 200)) {
                return (
                  <line
                    key={`${device.id}-${otherDevice.id}`}
                    x1={device.x}
                    y1={device.y}
                    x2={otherDevice.x}
                    y2={otherDevice.y}
                    stroke="#4B5563"
                    strokeWidth="2"
                  />
                );
              }
              return null;
            }).filter(Boolean)
          )}
        </svg>
        
        {/* Devices */}
        {currentScenario.networkMap.map(device => (
          <div
            key={device.id}
            className={`absolute w-20 h-20 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center justify-center cursor-pointer transition-all ${
              selectedDevice?.id === device.id ? 'ring-2 ring-primary' : ''
            }`}
            style={{ left: device.x, top: device.y }}
            onClick={() => handleDeviceSelect(device)}
          >
            <div className="relative w-12 h-12 rounded-full flex items-center justify-center bg-[#0F172A]">
              {renderDeviceIcon(device.type)}
              {renderDeviceStatus(device.status)}
            </div>
            <div className="text-xs mt-1 text-center font-medium">
              {device.name}
            </div>
          </div>
        ))}
      </div>
      
      {/* Troubleshooting Interface */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        {/* Device Information */}
        <div className="bg-[#1E293B] p-4 rounded-lg">
          <h4 className="font-bold mb-3">Selected Device</h4>
          
          {selectedDevice ? (
            <div>
              <div className="flex items-center mb-3">
                {renderDeviceIcon(selectedDevice.type)}
                <span className="ml-2 font-medium">{selectedDevice.name}</span>
                {renderDeviceStatus(selectedDevice.status)}
              </div>
              
              {selectedDevice.config && (
                <div className="bg-[#0F172A] p-3 rounded-lg text-sm">
                  <h5 className="font-bold mb-1">Configuration</h5>
                  <div className="grid grid-cols-2 gap-x-2 gap-y-1">
                    <div className="text-gray-400">IP Address:</div>
                    <div>{selectedDevice.config.ip}</div>
                    <div className="text-gray-400">Subnet Mask:</div>
                    <div>{selectedDevice.config.subnet}</div>
                    <div className="text-gray-400">Gateway:</div>
                    <div>{selectedDevice.config.gateway}</div>
                    <div className="text-gray-400">DNS Server:</div>
                    <div>{selectedDevice.config.dns}</div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-400">Select a device from the network diagram</p>
          )}
        </div>
        
        {/* Tools */}
        <div className="bg-[#1E293B] p-4 rounded-lg">
          <h4 className="font-bold mb-3">Troubleshooting Tools</h4>
          
          {selectedDevice ? (
            <div className="grid grid-cols-2 gap-2">
              {tools.map(tool => (
                <button
                  key={tool.id}
                  onClick={() => handleToolSelect(tool)}
                  className={`p-2 rounded-lg flex flex-col items-center justify-center text-sm ${
                    selectedTool?.id === tool.id ? 'bg-primary text-black' : 'bg-[#0F172A] hover:bg-[#334155]'
                  }`}
                >
                  <div className="text-lg mb-1">
                    {tool.icon}
                  </div>
                  <span>{tool.name}</span>
                </button>
              ))}
            </div>
          ) : (
            <p className="text-gray-400">Select a device to use tools</p>
          )}
        </div>
        
        {/* Tool Results */}
        <div className="bg-[#1E293B] p-4 rounded-lg">
          <h4 className="font-bold mb-3">Results</h4>
          
          {toolResults ? (
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">{toolResults.tool} on {toolResults.device}</span>
              </div>
              <div className="bg-black p-3 rounded-lg font-mono text-sm text-green-400 overflow-x-auto whitespace-pre-wrap">
                {toolResults.output}
              </div>
            </div>
          ) : (
            <p className="text-gray-400">Select a tool to see results</p>
          )}
        </div>
      </div>
      
      {/* Diagnosis and Solution */}
      <div className="bg-[#1E293B] p-4 rounded-lg mb-4">
        {currentStep === 'diagnose' ? (
          <div>
            <h4 className="font-bold mb-3 flex items-center">
              <FaSearch className="mr-2 text-primary" />
              Diagnose the Problem
            </h4>
            <p className="text-gray-300 mb-3">
              Based on your troubleshooting, what do you think is causing the issue?
            </p>
            <textarea
              value={diagnosis}
              onChange={(e) => setDiagnosis(e.target.value)}
              className="w-full bg-[#0F172A] p-3 rounded-lg border border-gray-700 mb-3"
              rows="3"
              placeholder="Enter your diagnosis here..."
            ></textarea>
            <div className="flex justify-end">
              <button
                onClick={handleDiagnosisSubmit}
                disabled={!diagnosis.trim()}
                className={`px-4 py-2 rounded ${
                  diagnosis.trim() ? 'bg-primary text-black' : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                }`}
              >
                Submit Diagnosis
              </button>
            </div>
            
            {isCorrect === false && (
              <div className="mt-3 bg-red-900/20 border border-red-500/30 p-3 rounded-lg">
                <p className="text-red-400 font-bold flex items-center">
                  <FaTimes className="mr-2" />
                  Incorrect Diagnosis
                </p>
                <p className="text-gray-300 mt-1">
                  Your diagnosis doesn't seem to identify the root cause. Try using more tools to gather information.
                </p>
              </div>
            )}
          </div>
        ) : (
          <div>
            <h4 className="font-bold mb-3 flex items-center">
              <FaTools className="mr-2 text-primary" />
              Fix the Problem
            </h4>
            <p className="text-gray-300 mb-3">
              Now that you've identified the issue, select the best solution:
            </p>
            <div className="space-y-2">
              {currentScenario.fixOptions.map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleSolutionSelect(option)}
                  className="w-full text-left p-3 rounded-lg bg-[#0F172A] hover:bg-[#334155] border border-gray-700"
                >
                  {option.label}
                </button>
              ))}
            </div>
            
            {solution && !isCorrect && (
              <div className="mt-3 bg-red-900/20 border border-red-500/30 p-3 rounded-lg">
                <p className="text-red-400 font-bold flex items-center">
                  <FaTimes className="mr-2" />
                  Incorrect Solution
                </p>
                <p className="text-gray-300 mt-1">
                  This solution won't fix the problem. Try another approach.
                </p>
              </div>
            )}
            
            {exerciseCompleted && (
              <div className="mt-3 bg-green-900/20 border border-green-500/30 p-3 rounded-lg">
                <p className="text-green-400 font-bold flex items-center">
                  <FaCheck className="mr-2" />
                  Problem Solved!
                </p>
                <p className="text-gray-300 mt-1">
                  You've successfully diagnosed and fixed the network issue.
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default NetworkTroubleshootingExercise;
