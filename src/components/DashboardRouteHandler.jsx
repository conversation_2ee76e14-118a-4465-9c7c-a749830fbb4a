import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import LoadingSpinner from './LoadingSpinner';

/**
 * Enhanced Dashboard Route Handler
 * 
 * Handles dashboard routing with better session management
 */
const DashboardRouteHandler = ({ children }) => {
  const { user, loading: authLoading } = useAuth();
  const [isValidating, setIsValidating] = useState(true);
  const [validationComplete, setValidationComplete] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const validateDashboardAccess = async () => {
      console.log('🔍 DashboardRouteHandler: Validating dashboard access...');
      
      try {
        // If auth is still loading, wait a bit
        if (authLoading) {
          console.log('⏳ DashboardRouteHandler: Auth still loading, waiting...');
          return;
        }

        // If we have a user from AuthContext, we're good
        if (user) {
          console.log('✅ DashboardRouteHandler: User found in AuthContext');
          setValidationComplete(true);
          setIsValidating(false);
          return;
        }

        // If no user, try to get session directly from Supabase
        console.log('🔄 DashboardRouteHandler: No user in context, checking Supabase session...');
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('❌ DashboardRouteHandler: Session error:', error);
          throw error;
        }

        if (session?.user) {
          console.log('✅ DashboardRouteHandler: Found valid Supabase session');
          setValidationComplete(true);
          setIsValidating(false);
          return;
        }

        // No valid session found
        console.log('❌ DashboardRouteHandler: No valid session - redirecting to login');
        navigate('/login', { 
          state: { from: location.pathname },
          replace: true 
        });

      } catch (error) {
        console.error('❌ DashboardRouteHandler: Validation error:', error);
        navigate('/login', { 
          state: { from: location.pathname },
          replace: true 
        });
      } finally {
        setIsValidating(false);
      }
    };

    // Start validation
    validateDashboardAccess();

    // Set a maximum timeout for validation
    const timeout = setTimeout(() => {
      if (isValidating) {
        console.warn('⚠️ DashboardRouteHandler: Validation timeout - forcing completion');
        setIsValidating(false);
        
        // If we still don't have a user after timeout, redirect to login
        if (!user) {
          navigate('/login', { 
            state: { from: location.pathname },
            replace: true 
          });
        } else {
          setValidationComplete(true);
        }
      }
    }, 3000); // 3 second timeout

    return () => clearTimeout(timeout);
  }, [user, authLoading, navigate, location.pathname, isValidating]);

  // Show loading while validating
  if (isValidating || authLoading) {
    return (
      <LoadingSpinner 
        fullScreen 
        message="Validating dashboard access..." 
      />
    );
  }

  // If validation failed and no user, the useEffect will handle redirect
  if (!validationComplete && !user) {
    return (
      <LoadingSpinner 
        fullScreen 
        message="Redirecting to login..." 
      />
    );
  }

  // Render dashboard content
  console.log('✅ DashboardRouteHandler: Rendering dashboard content');
  return children;
};

export default DashboardRouteHandler;
