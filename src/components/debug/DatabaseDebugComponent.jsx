import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';
import { debugDatabaseTables, testProfileUpdate } from '../../utils/databaseDebug';

const DatabaseDebugComponent = () => {
  const { user } = useAuth();
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState(false);

  const runDatabaseTests = async () => {
    setLoading(true);
    setResults({});

    try {
      // Test table accessibility
      const profilesTest = await supabase
        .from('profiles')
        .select('id, avatar_url, updated_at')
        .limit(1);

      const userProfilesTest = await supabase
        .from('user_profiles')
        .select('id, avatar_url, updated_at')
        .limit(1);

      setResults(prev => ({
        ...prev,
        profilesTable: {
          accessible: !profilesTest.error,
          error: profilesTest.error?.message,
          data: profilesTest.data
        },
        userProfilesTable: {
          accessible: !userProfilesTest.error,
          error: userProfilesTest.error?.message,
          data: userProfilesTest.data
        }
      }));

      // Test profile update if user is logged in
      if (user) {
        const updateTest = await testProfileUpdate(user.id);
        setResults(prev => ({
          ...prev,
          updateTest
        }));
      }

      // Test current user profile
      if (user) {
        const currentProfileTest = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        const currentUserProfileTest = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        setResults(prev => ({
          ...prev,
          currentProfile: {
            profiles: {
              exists: !currentProfileTest.error,
              error: currentProfileTest.error?.message,
              data: currentProfileTest.data
            },
            userProfiles: {
              exists: !currentUserProfileTest.error,
              error: currentUserProfileTest.error?.message,
              data: currentUserProfileTest.data
            }
          }
        }));
      }

    } catch (error) {
      setResults(prev => ({
        ...prev,
        generalError: error.message
      }));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    runDatabaseTests();
  }, [user]);

  const testAvatarUpdate = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const testUrl = 'https://example.com/test-avatar.png';
      
      // Test profiles table update
      const profilesUpdate = await supabase
        .from('profiles')
        .update({ 
          avatar_url: testUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      // Test user_profiles table update
      const userProfilesUpdate = await supabase
        .from('user_profiles')
        .update({ 
          avatar_url: testUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      setResults(prev => ({
        ...prev,
        avatarUpdateTest: {
          profiles: {
            success: !profilesUpdate.error,
            error: profilesUpdate.error?.message
          },
          userProfiles: {
            success: !userProfilesUpdate.error,
            error: userProfilesUpdate.error?.message
          }
        }
      }));

    } catch (error) {
      setResults(prev => ({
        ...prev,
        avatarUpdateTest: {
          error: error.message
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-6xl mx-auto space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Database Debug</h1>
        
        <div className="flex space-x-4">
          <button
            onClick={runDatabaseTests}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Run Database Tests'}
          </button>
          
          {user && (
            <button
              onClick={testAvatarUpdate}
              disabled={loading}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
            >
              Test Avatar Update
            </button>
          )}
        </div>

        {/* Results Display */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Profiles Table */}
          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-3">Profiles Table</h2>
            {results.profilesTable && (
              <div className="space-y-2">
                <p><strong>Accessible:</strong> {results.profilesTable.accessible ? '✅ Yes' : '❌ No'}</p>
                {results.profilesTable.error && (
                  <p><strong>Error:</strong> {results.profilesTable.error}</p>
                )}
                {results.profilesTable.data && (
                  <p><strong>Sample Data:</strong> {results.profilesTable.data.length} records</p>
                )}
              </div>
            )}
          </div>

          {/* User Profiles Table */}
          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-3">User Profiles Table</h2>
            {results.userProfilesTable && (
              <div className="space-y-2">
                <p><strong>Accessible:</strong> {results.userProfilesTable.accessible ? '✅ Yes' : '❌ No'}</p>
                {results.userProfilesTable.error && (
                  <p><strong>Error:</strong> {results.userProfilesTable.error}</p>
                )}
                {results.userProfilesTable.data && (
                  <p><strong>Sample Data:</strong> {results.userProfilesTable.data.length} records</p>
                )}
              </div>
            )}
          </div>

          {/* Current User Profile */}
          {user && results.currentProfile && (
            <div className="bg-white p-4 rounded-lg shadow md:col-span-2">
              <h2 className="text-lg font-semibold mb-3">Current User Profile</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium">Profiles Table</h3>
                  <p><strong>Exists:</strong> {results.currentProfile.profiles.exists ? '✅ Yes' : '❌ No'}</p>
                  {results.currentProfile.profiles.error && (
                    <p><strong>Error:</strong> {results.currentProfile.profiles.error}</p>
                  )}
                </div>
                <div>
                  <h3 className="font-medium">User Profiles Table</h3>
                  <p><strong>Exists:</strong> {results.currentProfile.userProfiles.exists ? '✅ Yes' : '❌ No'}</p>
                  {results.currentProfile.userProfiles.error && (
                    <p><strong>Error:</strong> {results.currentProfile.userProfiles.error}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Avatar Update Test */}
          {results.avatarUpdateTest && (
            <div className="bg-white p-4 rounded-lg shadow md:col-span-2">
              <h2 className="text-lg font-semibold mb-3">Avatar Update Test</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium">Profiles Table Update</h3>
                  <p><strong>Success:</strong> {results.avatarUpdateTest.profiles?.success ? '✅ Yes' : '❌ No'}</p>
                  {results.avatarUpdateTest.profiles?.error && (
                    <p><strong>Error:</strong> {results.avatarUpdateTest.profiles.error}</p>
                  )}
                </div>
                <div>
                  <h3 className="font-medium">User Profiles Table Update</h3>
                  <p><strong>Success:</strong> {results.avatarUpdateTest.userProfiles?.success ? '✅ Yes' : '❌ No'}</p>
                  {results.avatarUpdateTest.userProfiles?.error && (
                    <p><strong>Error:</strong> {results.avatarUpdateTest.userProfiles.error}</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Raw Results */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Raw Results</h2>
          <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-96">
            {JSON.stringify(results, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default DatabaseDebugComponent;
