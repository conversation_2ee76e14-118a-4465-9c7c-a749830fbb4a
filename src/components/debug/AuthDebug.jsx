import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';

const AuthDebug = () => {
  const { user, profile, loading, error } = useAuth();
  const [sessionInfo, setSessionInfo] = useState(null);
  const [localStorageInfo, setLocalStorageInfo] = useState({});

  useEffect(() => {
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        setSessionInfo({ session, error });
      } catch (err) {
        setSessionInfo({ session: null, error: err.message });
      }
    };

    const checkLocalStorage = () => {
      const keys = Object.keys(localStorage).filter(key => 
        key.includes('supabase') || key.includes('auth') || key.includes('session')
      );
      const info = {};
      keys.forEach(key => {
        try {
          info[key] = JSON.parse(localStorage.getItem(key));
        } catch {
          info[key] = localStorage.getItem(key);
        }
      });
      setLocalStorageInfo(info);
    };

    checkSession();
    checkLocalStorage();
  }, []);

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-2xl font-bold text-gray-900">Authentication Debug</h1>
        
        {/* Auth Context State */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Auth Context State</h2>
          <div className="space-y-2">
            <p><strong>Loading:</strong> {loading ? 'true' : 'false'}</p>
            <p><strong>Error:</strong> {error || 'None'}</p>
            <p><strong>User:</strong> {user ? 'Authenticated' : 'Not authenticated'}</p>
            {user && (
              <div className="ml-4 space-y-1">
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>Email Confirmed:</strong> {user.email_confirmed_at ? 'Yes' : 'No'}</p>
                <p><strong>Created:</strong> {user.created_at}</p>
              </div>
            )}
            <p><strong>Profile:</strong> {profile ? 'Loaded' : 'Not loaded'}</p>
            {profile && (
              <div className="ml-4 space-y-1">
                <p><strong>Username:</strong> {profile.username}</p>
                <p><strong>Full Name:</strong> {profile.full_name}</p>
              </div>
            )}
          </div>
        </div>

        {/* Supabase Session */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Supabase Session</h2>
          {sessionInfo ? (
            <div className="space-y-2">
              <p><strong>Session Error:</strong> {sessionInfo.error || 'None'}</p>
              <p><strong>Session:</strong> {sessionInfo.session ? 'Active' : 'None'}</p>
              {sessionInfo.session && (
                <div className="ml-4 space-y-1">
                  <p><strong>Access Token:</strong> {sessionInfo.session.access_token ? 'Present' : 'Missing'}</p>
                  <p><strong>Refresh Token:</strong> {sessionInfo.session.refresh_token ? 'Present' : 'Missing'}</p>
                  <p><strong>Expires At:</strong> {sessionInfo.session.expires_at}</p>
                  <p><strong>User ID:</strong> {sessionInfo.session.user?.id}</p>
                </div>
              )}
            </div>
          ) : (
            <p>Loading session info...</p>
          )}
        </div>

        {/* Local Storage */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Local Storage (Auth Related)</h2>
          {Object.keys(localStorageInfo).length > 0 ? (
            <div className="space-y-2">
              {Object.entries(localStorageInfo).map(([key, value]) => (
                <div key={key} className="border-b pb-2">
                  <p><strong>{key}:</strong></p>
                  <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                    {typeof value === 'object' ? JSON.stringify(value, null, 2) : value}
                  </pre>
                </div>
              ))}
            </div>
          ) : (
            <p>No auth-related items in localStorage</p>
          )}
        </div>

        {/* Actions */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-3">Actions</h2>
          <div className="space-x-2">
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Refresh Page
            </button>
            <button
              onClick={() => {
                localStorage.clear();
                window.location.reload();
              }}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Clear Storage & Refresh
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthDebug;
