import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

/**
 * Navbar Test Component
 * 
 * Tests navbar functionality and authentication state
 */
const NavbarTest = () => {
  const { user, profile, loading } = useAuth();
  const [testResults, setTestResults] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    const runTests = () => {
      const results = [];

      // Test 1: Auth Context Loading
      results.push({
        test: 'Auth Context Loading',
        status: loading ? 'LOADING' : 'COMPLETE',
        details: `Loading: ${loading}`
      });

      // Test 2: User Authentication
      results.push({
        test: 'User Authentication',
        status: user ? 'PASS' : 'FAIL',
        details: user ? `User: ${user.email}` : 'No user found'
      });

      // Test 3: Profile Data
      results.push({
        test: 'Profile Data',
        status: profile ? 'PASS' : 'FAIL',
        details: profile ? `Username: ${profile.username || 'N/A'}` : 'No profile found'
      });

      // Test 4: Navigation Functions
      results.push({
        test: 'Navigation Functions',
        status: navigate ? 'PASS' : 'FAIL',
        details: 'Navigate function available'
      });

      setTestResults(results);
    };

    runTests();
  }, [user, profile, loading, navigate]);

  const testDashboardNavigation = () => {
    console.log('🧪 Testing dashboard navigation...');
    navigate('/dashboard');
  };

  const testHomeNavigation = () => {
    console.log('🧪 Testing home navigation...');
    navigate('/');
  };

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">🧪 Navbar & Auth Test</h1>
        
        {/* Test Results */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <div className="space-y-3">
            {testResults.map((result, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                <span className="font-medium">{result.test}</span>
                <div className="flex items-center gap-3">
                  <span className="text-sm text-gray-300">{result.details}</span>
                  <span className={`px-2 py-1 rounded text-xs font-bold ${
                    result.status === 'PASS' ? 'bg-green-600' :
                    result.status === 'FAIL' ? 'bg-red-600' :
                    'bg-yellow-600'
                  }`}>
                    {result.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Tests */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Navigation Tests</h2>
          <div className="flex gap-4">
            <button
              onClick={testDashboardNavigation}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
            >
              Test Dashboard Navigation
            </button>
            <button
              onClick={testHomeNavigation}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
            >
              Test Home Navigation
            </button>
          </div>
        </div>

        {/* Current State */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Current State</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-blue-400 mb-2">User Data</h3>
              <pre className="bg-gray-900 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
            <div>
              <h3 className="font-semibold text-green-400 mb-2">Profile Data</h3>
              <pre className="bg-gray-900 p-3 rounded text-sm overflow-auto">
                {JSON.stringify(profile, null, 2)}
              </pre>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-6 p-4 bg-blue-900 rounded-lg">
          <h3 className="font-semibold mb-2">🔍 Test Instructions</h3>
          <ul className="text-sm space-y-1">
            <li>• Check if all tests show PASS status</li>
            <li>• Test navigation buttons work correctly</li>
            <li>• Verify user and profile data are loaded</li>
            <li>• Check browser console for any errors</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default NavbarTest;
