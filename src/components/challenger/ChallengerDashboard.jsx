import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import ChallengerHub from './ChallengerHub';
import { FaRocket, FaChevronDown, FaChevronUp } from 'react-icons/fa';

/**
 * ChallengerDashboard Component
 * 
 * A wrapper component for the ChallengerHub that ensures it stays within the dashboard
 * and provides navigation between Challenger and Start Hack sections.
 */
const ChallengerDashboard = () => {
  const { darkMode } = useGlobalTheme();
  const { challengeId } = useParams();
  const [showStartHack, setShowStartHack] = useState(false);
  const navigate = useNavigate();

  // Toggle the Start Hack section
  const toggleStartHack = () => {
    setShowStartHack(!showStartHack);
  };

  // Navigate to Start Hack
  const goToStartHack = () => {
    navigate('/dashboard/start-hack');
  };

  return (
    <div className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>
      <div className="px-4 py-4">
        {/* Content */}
        <div className="mb-8">
          <ChallengerHub challengeId={challengeId} />
        </div>

        {/* Start Hack Section Toggle */}
        <div className={`mt-12 p-6 rounded-lg ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border`}>
          <div className="flex justify-between items-center cursor-pointer" onClick={toggleStartHack}>
            <div>
              <h2 className="text-2xl font-bold flex items-center">
                <FaRocket className="text-blue-500 mr-2" />
                Need More Practice?
              </h2>
              <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Practice cybersecurity techniques in guided simulations
              </p>
            </div>
            <div>
              {showStartHack ? (
                <FaChevronUp className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'}`} />
              ) : (
                <FaChevronDown className={`text-xl ${darkMode ? 'text-gray-400' : 'text-gray-600'}`} />
              )}
            </div>
          </div>

          {showStartHack && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              transition={{ duration: 0.3 }}
              className="mt-6"
            >
              <p className="mb-4">
                The Start Hack section offers guided simulations where you can practice cybersecurity techniques in a safe environment.
                Learn at your own pace with step-by-step guidance and immediate feedback.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <h3 className="font-bold mb-2">Guided Simulations</h3>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Practice techniques with step-by-step guidance and immediate feedback
                  </p>
                </div>
                <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
                  <h3 className="font-bold mb-2">Real-world Scenarios</h3>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Apply your skills in realistic cybersecurity scenarios
                  </p>
                </div>
              </div>
              <button
                onClick={goToStartHack}
                className={`px-4 py-2 rounded-lg ${
                  darkMode
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
              >
                Go to Start Hack
              </button>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChallengerDashboard;
