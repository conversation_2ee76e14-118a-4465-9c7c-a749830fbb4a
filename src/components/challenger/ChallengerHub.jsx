import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaLock, FaTrophy, FaSearch, FaFilter, FaStar, FaClock, FaCode, FaNetworkWired, FaDatabase, FaServer, FaShieldAlt, FaUserSecret, FaInfoCircle, FaCheck, FaArrowLeft, FaStopwatch, FaChartLine, FaUsers, FaFire, FaKey } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { supabase } from '../../lib/supabase';
import WebExploitChallenge from './challenges/WebExploitChallenge';
import NetworkPenetrationChallenge from './challenges/NetworkPenetrationChallenge';
import PasswordCrackingChallenge from './challenges/PasswordCrackingChallenge';
import SubscriptionRequired from '../SubscriptionRequired';

const ChallengerHub = () => {
  const { darkMode } = useGlobalTheme();
  const { subscriptionLevel } = useSubscription();
  const navigate = useNavigate();
  const { challengeId } = useParams();
  const [challenges, setChallenges] = useState([]);
  const [userChallenges, setUserChallenges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeChallenge, setActiveChallenge] = useState(null);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [difficultyLevels, setDifficultyLevels] = useState([]);
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [leaderboard, setLeaderboard] = useState([]);
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [user, setUser] = useState(null);
  const [authChecked, setAuthChecked] = useState(false);

  // Get current user from auth context
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);
      } catch (error) {
        console.error('Error getting current user:', error);
      } finally {
        setAuthChecked(true);
      }
    };

    getCurrentUser();
  }, []);

  // Fetch challenges from the database
  useEffect(() => {
    const fetchChallenges = async () => {
      try {
        setLoading(true);

        // Fetch categories (reuse the same categories as practice simulations)
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('practice_simulation_categories')
          .select('*')
          .order('display_order');

        if (categoriesError) throw categoriesError;
        setCategories(categoriesData || []);

        // Fetch difficulty levels (reuse the same difficulty levels as practice simulations)
        const { data: difficultyData, error: difficultyError } = await supabase
          .from('practice_simulation_difficulty_levels')
          .select('*')
          .order('display_order');

        if (difficultyError) throw difficultyError;
        setDifficultyLevels(difficultyData || []);

        // Fetch challenges
        const { data: challengesData, error: challengesError } = await supabase
          .from('challenger_challenges')
          .select(`
            *,
            category:category_id(id, name),
            difficulty:difficulty_id(id, name)
          `)
          .eq('is_active', true)
          .order('created_at');

        if (challengesError) throw challengesError;

        // If no data in the database yet, use static data
        const challengesToUse = challengesData?.length > 0 ? challengesData : STATIC_CHALLENGES;
        setChallenges(challengesToUse);

        // Fetch user's challenge attempts
        const { data: user } = await supabase.auth.getUser();

        if (user && user.id) {
          const { data: userChallengesData, error: userChallengesError } = await supabase
            .from('challenger_challenge_attempts')
            .select('*')
            .eq('user_id', user.id);

          if (userChallengesError) throw userChallengesError;
          setUserChallenges(userChallengesData || []);
        }

        // If challengeId is provided, set it as the active challenge
        if (challengeId) {
          const challenge = challengesToUse.find(ch => ch.id === challengeId || ch.slug === challengeId);
          if (challenge) {
            setActiveChallenge(challenge);

            // Fetch leaderboard for this challenge
            const { data: leaderboardData, error: leaderboardError } = await supabase
              .from('leaderboards')
              .select(`
                *,
                user:user_id(id, email, user_metadata)
              `)
              .eq('challenge_id', challenge.id)
              .order('completion_time', { ascending: true })
              .limit(10);

            if (leaderboardError) throw leaderboardError;
            setLeaderboard(leaderboardData || []);
          }
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching challenges:', error);
        setLoading(false);
      }
    };

    fetchChallenges();
  }, [challengeId]);

  // Filter challenges based on search query, category, and difficulty
  const filteredChallenges = challenges.filter(challenge => {
    const matchesSearch = challenge.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         challenge.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory = selectedCategory === 'all' ||
                           (challenge.category && challenge.category.id === selectedCategory);

    const matchesDifficulty = selectedDifficulty === 'all' ||
                             (challenge.difficulty && challenge.difficulty.id === selectedDifficulty);

    const matchesFilter = filter === 'all' ||
                         (filter === 'completed' && userChallenges.some(uc => uc.challenge_id === challenge.id && uc.is_completed)) ||
                         (filter === 'in-progress' && userChallenges.some(uc => uc.challenge_id === challenge.id && !uc.is_completed)) ||
                         (filter === 'not-started' && !userChallenges.some(uc => uc.challenge_id === challenge.id));

    return matchesSearch && matchesCategory && matchesDifficulty && matchesFilter;
  });

  // Handle challenge completion
  const handleChallengeComplete = async (result) => {
    console.log('Challenge completed:', result);

    // Save challenge completion to database
    if (activeChallenge && result.success) {
      try {
        const { data: user } = await supabase.auth.getUser();

        if (user && user.id) {
          // Save challenge attempt
          const { data: attemptData, error: attemptError } = await supabase
            .from('challenger_challenge_attempts')
            .upsert({
              user_id: user.id,
              challenge_id: activeChallenge.id,
              is_completed: true,
              completion_time: result.timeSpent,
              approach_score: result.approachScore || 80, // Default score if not provided
              total_score: result.totalScore || result.approachScore || 80,
              completed_at: new Date().toISOString()
            })
            .select();

          if (attemptError) throw attemptError;

          // Update leaderboard
          const { error: leaderboardError } = await supabase
            .from('leaderboards')
            .upsert({
              challenge_id: activeChallenge.id,
              user_id: user.id,
              completion_time: result.timeSpent,
              approach_score: result.approachScore || 80,
              total_score: result.totalScore || result.approachScore || 80
            });

          if (leaderboardError) throw leaderboardError;

          // Refresh leaderboard
          const { data: leaderboardData } = await supabase
            .from('leaderboards')
            .select(`
              *,
              user:user_id(id, email, user_metadata)
            `)
            .eq('challenge_id', activeChallenge.id)
            .order('completion_time', { ascending: true })
            .limit(10);

          setLeaderboard(leaderboardData || []);
          setShowLeaderboard(true);

          // Refresh user challenges
          const { data: userChallengesData } = await supabase
            .from('challenger_challenge_attempts')
            .select('*')
            .eq('user_id', user.id);

          setUserChallenges(userChallengesData || []);
        }
      } catch (error) {
        console.error('Error saving challenge completion:', error);
      }
    }
  };

  // Render the appropriate challenge component based on the challenge type
  const renderChallenge = (challenge) => {
    switch (challenge.challenge_type) {
      case 'web_exploit':
        return <WebExploitChallenge onComplete={handleChallengeComplete} challengeId={challenge.id} />;
      case 'network_penetration':
        return <NetworkPenetrationChallenge onComplete={handleChallengeComplete} challengeId={challenge.id} />;
      case 'password_cracking':
        return <PasswordCrackingChallenge onComplete={handleChallengeComplete} challengeId={challenge.id} />;
      default:
        return (
          <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border text-center`}>
            <h3 className="text-xl font-bold mb-4">Challenge Not Available</h3>
            <p className="mb-6">This challenge type is not yet implemented.</p>
          </div>
        );
    }
  };

  // If a premium challenge is selected but user doesn't have premium subscription
  const isPremiumLocked = activeChallenge?.is_premium && subscriptionLevel === 'free';
  const isBusinessLocked = activeChallenge?.is_business && subscriptionLevel !== 'business';

  // If a challenge is active, show the challenge and leaderboard
  if (activeChallenge) {
    return (
      <div className="container mx-auto px-4 py-8">
        <button
          onClick={() => {
            setActiveChallenge(null);
            setShowLeaderboard(false);
            navigate('/dashboard/challenger');
          }}
          className={`mb-4 flex items-center ${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-gray-900'}`}
        >
          <FaArrowLeft className="mr-2" /> Back to Challenges
        </button>

        <div className="flex flex-col lg:flex-row gap-6">
          <div className="lg:w-3/4">
            <h1 className="text-2xl font-bold mb-6">{activeChallenge.title}</h1>

            {isPremiumLocked ? (
              <SubscriptionRequired
                feature="premium challenges"
                message="This challenge requires a premium subscription."
              />
            ) : isBusinessLocked ? (
              <SubscriptionRequired
                feature="business challenges"
                message="This challenge requires a business subscription."
                requiredTier="business"
              />
            ) : (
              renderChallenge(activeChallenge)
            )}
          </div>

          <div className="lg:w-1/4">
            <div className={`rounded-lg border ${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'}`}>
              <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                <h3 className="text-lg font-bold flex items-center">
                  <FaTrophy className="text-yellow-500 mr-2" />
                  Leaderboard
                </h3>
              </div>

              <div className="p-4">
                {leaderboard.length === 0 ? (
                  <div className="text-center py-4">
                    <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      No entries yet. Be the first to complete this challenge!
                    </p>
                  </div>
                ) : (
                  <div>
                    <div className={`grid grid-cols-12 gap-2 py-2 font-bold ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      <div className="col-span-1">#</div>
                      <div className="col-span-5">User</div>
                      <div className="col-span-3">Time</div>
                      <div className="col-span-3">Score</div>
                    </div>

                    {leaderboard.map((entry, index) => (
                      <div
                        key={entry.id}
                        className={`grid grid-cols-12 gap-2 py-2 ${
                          index % 2 === 0
                            ? darkMode ? 'bg-gray-800' : 'bg-gray-50'
                            : ''
                        } ${
                          entry.user?.id === (supabase.auth.getUser()?.data?.user?.id)
                            ? darkMode ? 'bg-blue-900' : 'bg-blue-50'
                            : ''
                        }`}
                      >
                        <div className="col-span-1">
                          {index === 0 ? <FaTrophy className="text-yellow-500" /> :
                           index === 1 ? <FaTrophy className="text-gray-400" /> :
                           index === 2 ? <FaTrophy className="text-yellow-700" /> :
                           index + 1}
                        </div>
                        <div className="col-span-5 truncate">
                          {entry.user?.user_metadata?.full_name || entry.user?.email || 'Anonymous'}
                        </div>
                        <div className="col-span-3 flex items-center">
                          <FaStopwatch className="mr-1 text-xs" />
                          {formatTime(entry.completion_time)}
                        </div>
                        <div className="col-span-3 flex items-center">
                          <FaChartLine className="mr-1 text-xs" />
                          {entry.total_score}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Otherwise, show the list of available challenges
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-2">Challenger</h1>
      <p className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        Compete in cybersecurity challenges and climb the leaderboards
      </p>

      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
          </div>
          <input
            type="text"
            placeholder="Search challenges..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`pl-10 pr-4 py-2 w-full rounded-lg ${
              darkMode
                ? 'bg-gray-800 border-gray-700 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border`}
          />
        </div>

        <div className="flex flex-wrap gap-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-gray-800 border-gray-700 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border`}
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>

          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-gray-800 border-gray-700 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border`}
          >
            <option value="all">All Difficulties</option>
            {difficultyLevels.map(difficulty => (
              <option key={difficulty.id} value={difficulty.id}>{difficulty.name}</option>
            ))}
          </select>

          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className={`px-4 py-2 rounded-lg ${
              darkMode
                ? 'bg-gray-800 border-gray-700 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } border`}
          >
            <option value="all">All Challenges</option>
            <option value="completed">Completed</option>
            <option value="in-progress">In Progress</option>
            <option value="not-started">Not Started</option>
          </select>
        </div>
      </div>

      {/* Challenges Grid */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : filteredChallenges.length === 0 ? (
        <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          <FaInfoCircle className="mx-auto text-4xl mb-4" />
          <p className="text-xl">No challenges found matching your filters.</p>
          <p className="mt-2">Try adjusting your search criteria or filters.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredChallenges.map(challenge => {
            const userChallenge = userChallenges.find(uc => uc.challenge_id === challenge.id);
            const isCompleted = userChallenge?.is_completed;
            const isInProgress = userChallenge && !isCompleted;
            const isPremium = challenge.is_premium;
            const isBusiness = challenge.is_business;

            return (
              <motion.div
                key={challenge.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`rounded-lg overflow-hidden border ${
                  darkMode
                    ? 'bg-[#1A1F35] border-gray-800'
                    : 'bg-white border-gray-200'
                }`}
              >
                {/* Challenge Card Header */}
                <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'}`}>
                  <div className="flex justify-between items-start">
                    <h3 className="text-lg font-bold">{challenge.title}</h3>
                    <div className="flex items-center">
                      {isPremium && (
                        <span className="ml-2 px-2 py-0.5 text-xs rounded bg-yellow-500 text-white">Premium</span>
                      )}
                      {isBusiness && (
                        <span className="ml-2 px-2 py-0.5 text-xs rounded bg-purple-500 text-white">Business</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center mt-2 text-sm">
                    <span className={`px-2 py-0.5 rounded ${
                      challenge.difficulty?.name === 'Beginner' ? 'bg-green-100 text-green-800' :
                      challenge.difficulty?.name === 'Intermediate' ? 'bg-blue-100 text-blue-800' :
                      challenge.difficulty?.name === 'Advanced' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {challenge.difficulty?.name || 'Unknown'}
                    </span>
                    <span className={`ml-2 px-2 py-0.5 rounded ${
                      darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {challenge.category?.name || 'Uncategorized'}
                    </span>
                    {challenge.estimated_time && (
                      <span className={`ml-2 flex items-center ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        <FaClock className="mr-1" /> {challenge.estimated_time} min
                      </span>
                    )}
                  </div>
                </div>

                {/* Challenge Card Body */}
                <div className="p-4">
                  <p className={`mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {challenge.description}
                  </p>

                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center">
                      <FaTrophy className="text-yellow-500 mr-1" />
                      <span className="font-bold">{challenge.points} pts</span>
                    </div>

                    <div className="flex items-center">
                      <FaUsers className="text-blue-500 mr-1" />
                      <span>{Math.floor(Math.random() * 100) + 10} participants</span>
                    </div>

                    <div className="flex items-center">
                      <FaFire className="text-red-500 mr-1" />
                      <span>{Math.floor(Math.random() * 5) + 1}/5</span>
                    </div>
                  </div>

                  {/* Status and Start Button */}
                  <div className="flex items-center justify-between">
                    <div>
                      {isCompleted && (
                        <span className="flex items-center text-green-500">
                          <FaCheck className="mr-1" /> Completed
                        </span>
                      )}
                      {isInProgress && (
                        <span className="flex items-center text-blue-500">
                          <FaInfoCircle className="mr-1" /> In Progress
                        </span>
                      )}
                    </div>

                    <button
                      onClick={() => {
                        setActiveChallenge(challenge);
                        navigate(`/dashboard/challenger/${challenge.slug || challenge.id}`);
                      }}
                      className={`px-4 py-2 rounded-lg ${
                        isPremium && subscriptionLevel === 'free'
                          ? `${darkMode ? 'bg-gray-700' : 'bg-gray-300'} cursor-not-allowed`
                          : isBusiness && subscriptionLevel !== 'business'
                            ? `${darkMode ? 'bg-gray-700' : 'bg-gray-300'} cursor-not-allowed`
                            : `${darkMode ? 'bg-blue-600' : 'bg-blue-500'} text-white hover:bg-blue-700`
                      }`}
                      disabled={
                        (isPremium && subscriptionLevel === 'free') ||
                        (isBusiness && subscriptionLevel !== 'business')
                      }
                    >
                      {isPremium && subscriptionLevel === 'free' ? (
                        <span className="flex items-center">
                          <FaLock className="mr-1" /> Premium
                        </span>
                      ) : isBusiness && subscriptionLevel !== 'business' ? (
                        <span className="flex items-center">
                          <FaLock className="mr-1" /> Business
                        </span>
                      ) : isCompleted ? (
                        "Try Again"
                      ) : isInProgress ? (
                        "Continue"
                      ) : (
                        "Start Challenge"
                      )}
                    </button>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      )}
    </div>
  );
};

// Helper function to format time (seconds) to MM:SS
const formatTime = (seconds) => {
  if (!seconds) return '00:00';
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// Static challenges data for initial development
const STATIC_CHALLENGES = [
  {
    id: 'web-exploit-challenge',
    slug: 'web-exploit-challenge',
    title: 'Web Application Exploitation',
    description: 'Exploit vulnerabilities in a web application to gain unauthorized access.',
    challenge_type: 'web_exploit',
    category: { id: 'web-security', name: 'Web Security' },
    difficulty: { id: 'intermediate', name: 'Intermediate' },
    points: 250,
    estimated_time: 45,
    is_premium: false,
    is_business: false,
    objectives: [
      { id: 1, description: 'Identify vulnerabilities in the web application' },
      { id: 2, description: 'Exploit the vulnerabilities to gain access' },
      { id: 3, description: 'Extract the secret flag' }
    ],
    completion_criteria: {
      flag_format: 'flag{...}',
      time_limit: 3600 // 1 hour in seconds
    }
  },
  {
    id: 'password-cracking-challenge',
    slug: 'password-cracking-challenge',
    title: 'Password Cracking Challenge',
    description: 'Crack password hashes using various techniques to demonstrate the importance of strong password security.',
    challenge_type: 'password_cracking',
    category: { id: 'cryptography', name: 'Cryptography' },
    difficulty: { id: 'beginner', name: 'Beginner' },
    points: 150,
    estimated_time: 30,
    is_premium: false,
    is_business: false,
    objectives: [
      { id: 1, description: 'Analyze the password hash' },
      { id: 2, description: 'Identify the hash type' },
      { id: 3, description: 'Crack the password hash' },
      { id: 4, description: 'Submit the cracked password' }
    ],
    completion_criteria: {
      time_limit: 1800 // 30 minutes in seconds
    }
  },
  {
    id: 'network-penetration-challenge',
    slug: 'network-penetration-challenge',
    title: 'Network Penetration Testing',
    description: 'Perform a penetration test on a simulated network to identify and exploit vulnerabilities.',
    challenge_type: 'network_penetration',
    category: { id: 'network-security', name: 'Network Security' },
    difficulty: { id: 'advanced', name: 'Advanced' },
    points: 500,
    estimated_time: 60,
    is_premium: true,
    is_business: false,
    objectives: [
      { id: 1, description: 'Perform network reconnaissance' },
      { id: 2, description: 'Identify vulnerable services' },
      { id: 3, description: 'Exploit vulnerabilities to gain access' },
      { id: 4, description: 'Escalate privileges' },
      { id: 5, description: 'Extract the secret flag' }
    ],
    completion_criteria: {
      flag_format: 'flag{...}',
      time_limit: 7200 // 2 hours in seconds
    }
  }
];

export default ChallengerHub;
