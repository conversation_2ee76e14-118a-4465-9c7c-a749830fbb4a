import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>ock, FaUnlock, FaKey, FaCheck, FaInfoCircle, FaExclamationTriangle, FaTrophy, FaStopwatch } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const PasswordCrackingChallenge = ({ onComplete, challengeId }) => {
  const { darkMode } = useGlobalTheme();
  const [command, setCommand] = useState('');
  const [message, setMessage] = useState(null);
  const [attempts, setAttempts] = useState(0);
  const [isSuccess, setIsSuccess] = useState(false);
  const [startTime] = useState(new Date());
  const [elapsedTime, setElapsedTime] = useState(0);
  const [objectives, setObjectives] = useState([
    { id: 1, description: 'Analyze the password hash', completed: false },
    { id: 2, description: 'Identify the hash type', completed: false },
    { id: 3, description: 'Crack the password hash', completed: false },
    { id: 4, description: 'Submit the cracked password', completed: false }
  ]);
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'system', text: 'Password Cracking Challenge initialized...' },
    { type: 'system', text: 'Target: Password Hash' },
    { type: 'system', text: 'Type "help" for available commands.' }
  ]);
  const [challengeData, setChallengeData] = useState(null);
  const [password, setPassword] = useState('');
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [passwordHash, setPasswordHash] = useState('5f4dcc3b5aa765d61d8327deb882cf99');
  const [hashType, setHashType] = useState('MD5');
  
  const timerRef = useRef(null);
  
  // Start the timer
  useEffect(() => {
    timerRef.current = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now - startTime) / 1000); // in seconds
      setElapsedTime(elapsed);
    }, 1000);
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [startTime]);
  
  // Fetch challenge data
  useEffect(() => {
    const fetchChallengeData = async () => {
      if (!challengeId) return;
      
      try {
        const { data, error } = await supabase
          .from('challenger_challenges')
          .select('*')
          .eq('id', challengeId)
          .single();
        
        if (error) throw error;
        
        if (data) {
          setChallengeData(data);
          
          // If objectives are defined in the database, use them
          if (data.objectives) {
            setObjectives(data.objectives.map(obj => ({ ...obj, completed: false })));
          }
        }
      } catch (error) {
        console.error('Error fetching challenge data:', error);
      }
    };
    
    fetchChallengeData();
  }, [challengeId]);
  
  const addToConsole = (type, text) => {
    setConsoleOutput(prev => [...prev, { type, text }]);
    // Auto-scroll console to bottom
    setTimeout(() => {
      const consoleElement = document.getElementById('password-cracking-console');
      if (consoleElement) {
        consoleElement.scrollTop = consoleElement.scrollHeight;
      }
    }, 100);
  };
  
  const handleCommand = (e) => {
    e.preventDefault();
    
    if (!command.trim()) return;
    
    // Add command to console
    addToConsole('command', `$ ${command}`);
    
    // Process command
    processCommand(command);
    
    // Clear command input
    setCommand('');
  };
  
  const processCommand = (cmd) => {
    const lowerCmd = cmd.toLowerCase().trim();
    
    // Increment attempts
    setAttempts(prev => prev + 1);
    
    // Process different commands
    if (lowerCmd === 'help') {
      addToConsole('system', 'Available commands:');
      addToConsole('system', '  help - Show this help message');
      addToConsole('system', '  analyze <hash> - Analyze a password hash');
      addToConsole('system', '  identify <hash> - Identify the hash type');
      addToConsole('system', '  crack <hash> - Attempt to crack the password hash');
      addToConsole('system', '  submit <password> - Submit the cracked password');
      addToConsole('system', '  clear - Clear the console');
    } else if (lowerCmd.startsWith('analyze')) {
      const hash = lowerCmd.split(' ')[1] || passwordHash;
      
      addToConsole('system', `Analyzing hash: ${hash}`);
      
      // Simulate analysis delay
      setTimeout(() => {
        addToConsole('success', 'Hash analysis complete:');
        addToConsole('info', `Hash: ${hash}`);
        addToConsole('info', `Length: ${hash.length} characters`);
        addToConsole('info', `Character set: ${/^[0-9a-f]+$/.test(hash) ? 'Hexadecimal (0-9, a-f)' : 'Mixed'}`);
        
        // Mark first objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 1 ? { ...obj, completed: true } : obj
        ));
      }, 1500);
    } else if (lowerCmd.startsWith('identify')) {
      const hash = lowerCmd.split(' ')[1] || passwordHash;
      
      // Check if first objective is completed
      if (!objectives.find(obj => obj.id === 1)?.completed) {
        addToConsole('error', 'You need to analyze the hash first.');
        return;
      }
      
      addToConsole('system', `Identifying hash type for: ${hash}`);
      
      // Simulate identification delay
      setTimeout(() => {
        if (hash.length === 32) {
          addToConsole('success', 'Hash type identified:');
          addToConsole('info', 'Hash type: MD5');
          addToConsole('info', 'Confidence: High');
          
          // Mark second objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 2 ? { ...obj, completed: true } : obj
          ));
        } else if (hash.length === 40) {
          addToConsole('success', 'Hash type identified:');
          addToConsole('info', 'Hash type: SHA-1');
          addToConsole('info', 'Confidence: High');
          
          // Mark second objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 2 ? { ...obj, completed: true } : obj
          ));
        } else if (hash.length === 64) {
          addToConsole('success', 'Hash type identified:');
          addToConsole('info', 'Hash type: SHA-256');
          addToConsole('info', 'Confidence: High');
          
          // Mark second objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 2 ? { ...obj, completed: true } : obj
          ));
        } else {
          addToConsole('error', 'Could not identify hash type with confidence.');
          addToConsole('info', 'Try analyzing the hash first or provide a valid hash.');
        }
      }, 2000);
    } else if (lowerCmd.startsWith('crack')) {
      const hash = lowerCmd.split(' ')[1] || passwordHash;
      
      // Check if second objective is completed
      if (!objectives.find(obj => obj.id === 2)?.completed) {
        addToConsole('error', 'You need to identify the hash type first.');
        return;
      }
      
      addToConsole('system', `Attempting to crack hash: ${hash}`);
      addToConsole('system', 'Using dictionary attack with common passwords...');
      
      // Simulate cracking delay with progress updates
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += 10;
        if (progress <= 100) {
          addToConsole('info', `Progress: ${progress}%`);
        }
        
        if (progress >= 100) {
          clearInterval(progressInterval);
          
          addToConsole('success', 'Password cracked successfully!');
          addToConsole('info', 'The password is: password');
          
          // Mark third objective as completed
          setObjectives(prev => prev.map(obj => 
            obj.id === 3 ? { ...obj, completed: true } : obj
          ));
          
          // Show password input
          setShowPasswordInput(true);
        }
      }, 1000);
    } else if (lowerCmd.startsWith('submit')) {
      const submittedPassword = cmd.split(' ').slice(1).join(' ');
      
      if (!submittedPassword) {
        addToConsole('error', 'Please specify a password to submit.');
        addToConsole('system', 'Usage: submit <password>');
        return;
      }
      
      // Check if the password is correct
      if (submittedPassword.toLowerCase() === 'password') {
        addToConsole('success', 'Correct password! Challenge completed!');
        
        // Stop the timer
        clearInterval(timerRef.current);
        
        // Mark challenge as completed
        setIsSuccess(true);
        
        // Mark fourth objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 4 ? { ...obj, completed: true } : obj
        ));
        
        // Show success message
        setMessage({ type: 'success', text: 'Challenge completed successfully!' });
        
        // Call the onComplete callback
        if (onComplete) {
          onComplete({
            success: true,
            password: submittedPassword,
            attempts,
            timeSpent: elapsedTime,
            approachScore: calculateApproachScore(),
            totalScore: calculateTotalScore(elapsedTime)
          });
        }
      } else {
        addToConsole('error', 'Incorrect password. Try again.');
      }
    } else if (lowerCmd === 'clear') {
      setConsoleOutput([
        { type: 'system', text: 'Console cleared.' },
        { type: 'system', text: 'Type "help" for available commands.' }
      ]);
    } else {
      addToConsole('error', `Unknown command: ${cmd}`);
      addToConsole('system', 'Type "help" for available commands.');
    }
  };
  
  const handlePasswordSubmit = (e) => {
    e.preventDefault();
    
    // Process the password submission
    processCommand(`submit ${password}`);
    
    // Clear the password input
    setPassword('');
  };
  
  // Calculate approach score based on number of attempts and objectives completed
  const calculateApproachScore = () => {
    const objectivesCompleted = objectives.filter(obj => obj.completed).length;
    const objectivesTotal = objectives.length;
    const objectivesScore = (objectivesCompleted / objectivesTotal) * 50;
    
    // Penalize for excessive attempts
    const attemptsScore = Math.max(0, 50 - (attempts > 10 ? (attempts - 10) * 2 : 0));
    
    return Math.round(objectivesScore + attemptsScore);
  };
  
  // Calculate total score based on approach score and time
  const calculateTotalScore = (time) => {
    const approachScore = calculateApproachScore();
    
    // Time bonus: max 50 points, decreases as time increases
    const timeLimit = challengeData?.completion_criteria?.time_limit || 3600; // Default: 1 hour
    const timeScore = Math.max(0, 50 - Math.floor((time / timeLimit) * 50));
    
    return approachScore + timeScore;
  };
  
  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
      {/* Challenge Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex justify-between items-center`}>
        <h2 className="text-xl font-bold flex items-center">
          <FaKey className="text-yellow-500 mr-2" />
          Password Cracking Challenge
        </h2>
        <div className="flex items-center space-x-2">
          <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
            <FaStopwatch className="mr-1 text-blue-500" />
            <span className="text-sm">{formatTime(elapsedTime)}</span>
          </div>
        </div>
      </div>
      
      {/* Challenge Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Objectives */}
          <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <FaLock className="mr-2 text-red-500" />
              Objectives
            </h3>
            
            {message && (
              <div className={`p-3 rounded-lg mb-4 ${
                message.type === 'success' 
                  ? 'bg-green-100 text-green-800 border-green-200' 
                  : message.type === 'warning'
                    ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                    : 'bg-red-100 text-red-800 border-red-200'
              } border`}>
                <div className="flex items-center">
                  {message.type === 'success' ? (
                    <FaCheck className="mr-2" />
                  ) : message.type === 'warning' ? (
                    <FaExclamationTriangle className="mr-2" />
                  ) : (
                    <FaInfoCircle className="mr-2" />
                  )}
                  <span>{message.text}</span>
                </div>
              </div>
            )}
            
            <ul className="space-y-4">
              {objectives.map((objective) => (
                <li key={objective.id} className="flex items-start">
                  <div className={`mt-0.5 mr-2 flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                    objective.completed 
                      ? 'bg-green-500 text-white' 
                      : darkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-200 text-gray-500'
                  }`}>
                    {objective.completed ? (
                      <FaCheck className="text-xs" />
                    ) : (
                      <span className="text-xs">{objective.id}</span>
                    )}
                  </div>
                  <span className={objective.completed ? 'line-through opacity-70' : ''}>
                    {objective.description}
                  </span>
                </li>
              ))}
            </ul>
            
            {showPasswordInput && !isSuccess && (
              <div className="mt-6">
                <h4 className="font-bold mb-2">Submit Password:</h4>
                <form onSubmit={handlePasswordSubmit} className="flex">
                  <input
                    type="text"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter password"
                    className={`flex-grow p-2 rounded-l-lg ${
                      darkMode 
                        ? 'bg-gray-800 border-gray-700 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    } border`}
                  />
                  <button
                    type="submit"
                    className="px-4 py-2 rounded-r-lg bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Submit
                  </button>
                </form>
              </div>
            )}
            
            {isSuccess && (
              <div className="mt-6 p-4 bg-green-100 text-green-800 border border-green-200 rounded-lg">
                <div className="flex items-center mb-2">
                  <FaTrophy className="mr-2 text-yellow-500" />
                  <span className="font-bold">Challenge Completed!</span>
                </div>
                <p>You successfully completed the Password Cracking Challenge.</p>
                <div className="mt-2 grid grid-cols-2 gap-2">
                  <div>
                    <span className="text-sm font-bold">Time:</span>
                    <span className="ml-2">{formatTime(elapsedTime)}</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Attempts:</span>
                    <span className="ml-2">{attempts}</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Approach Score:</span>
                    <span className="ml-2">{calculateApproachScore()}/100</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Total Score:</span>
                    <span className="ml-2">{calculateTotalScore(elapsedTime)}/100</span>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Middle and Right Columns - Terminal and Info */}
          <div className="lg:col-span-2">
            <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <FaKey className="mr-2 text-yellow-500" />
                Password Cracker
              </h3>
              
              <div 
                id="password-cracking-console"
                className={`h-80 overflow-y-auto p-4 rounded-lg font-mono text-sm ${
                  darkMode 
                    ? 'bg-gray-900 text-gray-300' 
                    : 'bg-gray-800 text-gray-200'
                }`}
              >
                {consoleOutput.map((output, index) => (
                  <div key={index} className="mb-1">
                    <span className={
                      output.type === 'system' 
                        ? 'text-blue-400' 
                        : output.type === 'command' 
                          ? 'text-green-400'
                          : output.type === 'error'
                            ? 'text-red-400'
                            : output.type === 'success'
                              ? 'text-green-400'
                              : output.type === 'info'
                                ? 'text-yellow-400'
                                : 'text-gray-400'
                    }>
                      {output.text}
                    </span>
                  </div>
                ))}
              </div>
              
              <form onSubmit={handleCommand} className="mt-4 flex">
                <div className={`px-3 py-2 ${
                  darkMode 
                    ? 'bg-gray-900 text-green-400' 
                    : 'bg-gray-800 text-green-400'
                } rounded-l-lg`}>
                  $
                </div>
                <input
                  type="text"
                  value={command}
                  onChange={(e) => setCommand(e.target.value)}
                  className={`flex-grow p-2 ${
                    darkMode 
                      ? 'bg-gray-900 border-gray-700 text-gray-300' 
                      : 'bg-gray-800 border-gray-700 text-gray-200'
                  } border-y border-r rounded-r-lg font-mono`}
                  placeholder="Type a command..."
                  disabled={isSuccess}
                />
              </form>
              
              <div className="mt-4">
                <h4 className={`font-bold mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Challenge Description:</h4>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Your task is to crack a password hash using password cracking techniques. Analyze the hash,
                  identify its type, and use appropriate methods to recover the original password.
                  Type "help" to see available commands.
                </p>
                <div className="mt-2 p-3 rounded-lg bg-blue-500/10 border border-blue-500/30">
                  <p className="text-sm flex items-start">
                    <FaInfoCircle className="text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                    <span>
                      Target Hash: <span className="font-mono">{passwordHash}</span>
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordCrackingChallenge;
