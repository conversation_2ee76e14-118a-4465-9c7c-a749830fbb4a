import React, { useState, useEffect, useRef } from 'react';
import { FaNetworkWired, FaLightbulb, FaCheck, FaInfoCircle, FaExclamationTriangle, FaTrophy, FaArrowLeft, FaServer, FaCode, FaTerminal, FaFlag, FaStopwatch, FaLock, FaUnlock, FaShieldAlt, FaUserSecret } from 'react-icons/fa';
import { useGlobalTheme } from '../../../contexts/GlobalThemeContext';
import { supabase } from '../../../lib/supabase';

const NetworkPenetrationChallenge = ({ onComplete, challengeId }) => {
  const { darkMode } = useGlobalTheme();
  const [command, setCommand] = useState('');
  const [message, setMessage] = useState(null);
  const [attempts, setAttempts] = useState(0);
  const [isSuccess, setIsSuccess] = useState(false);
  const [startTime] = useState(new Date());
  const [elapsedTime, setElapsedTime] = useState(0);
  const [objectives, setObjectives] = useState([
    { id: 1, description: 'Perform network reconnaissance', completed: false },
    { id: 2, description: 'Identify vulnerable services', completed: false },
    { id: 3, description: 'Exploit vulnerabilities to gain access', completed: false },
    { id: 4, description: 'Escalate privileges', completed: false },
    { id: 5, description: 'Extract the secret flag', completed: false }
  ]);
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'system', text: 'Network Penetration Challenge initialized...' },
    { type: 'system', text: 'Target Network: ***********/24' },
    { type: 'system', text: 'Type "help" for available commands.' }
  ]);
  const [challengeData, setChallengeData] = useState(null);
  const [flag, setFlag] = useState('');
  const [showFlagInput, setShowFlagInput] = useState(false);
  const [networkMap, setNetworkMap] = useState(null);
  
  const timerRef = useRef(null);
  
  // Start the timer
  useEffect(() => {
    timerRef.current = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now - startTime) / 1000); // in seconds
      setElapsedTime(elapsed);
    }, 1000);
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [startTime]);
  
  // Fetch challenge data
  useEffect(() => {
    const fetchChallengeData = async () => {
      if (!challengeId) return;
      
      try {
        const { data, error } = await supabase
          .from('challenger_challenges')
          .select('*')
          .eq('id', challengeId)
          .single();
        
        if (error) throw error;
        
        if (data) {
          setChallengeData(data);
          
          // If objectives are defined in the database, use them
          if (data.objectives) {
            setObjectives(data.objectives.map(obj => ({ ...obj, completed: false })));
          }
        }
      } catch (error) {
        console.error('Error fetching challenge data:', error);
      }
    };
    
    fetchChallengeData();
  }, [challengeId]);
  
  const addToConsole = (type, text) => {
    setConsoleOutput(prev => [...prev, { type, text }]);
    // Auto-scroll console to bottom
    setTimeout(() => {
      const consoleElement = document.getElementById('network-pentest-console');
      if (consoleElement) {
        consoleElement.scrollTop = consoleElement.scrollHeight;
      }
    }, 100);
  };
  
  const handleCommand = (e) => {
    e.preventDefault();
    
    if (!command.trim()) return;
    
    // Add command to console
    addToConsole('command', `$ ${command}`);
    
    // Process command
    processCommand(command);
    
    // Clear command input
    setCommand('');
  };
  
  const processCommand = (cmd) => {
    const lowerCmd = cmd.toLowerCase().trim();
    
    // Increment attempts
    setAttempts(prev => prev + 1);
    
    // Process different commands
    if (lowerCmd === 'help') {
      addToConsole('system', 'Available commands:');
      addToConsole('system', '  help - Show this help message');
      addToConsole('system', '  scan <target> - Scan a target for open ports and services');
      addToConsole('system', '  enum <target> <service> - Enumerate a specific service on a target');
      addToConsole('system', '  exploit <target> <service> - Exploit a vulnerable service');
      addToConsole('system', '  privesc - Attempt to escalate privileges');
      addToConsole('system', '  extract - Extract data from the compromised system');
      addToConsole('system', '  submit <flag> - Submit a flag');
      addToConsole('system', '  clear - Clear the console');
    } else if (lowerCmd.startsWith('scan')) {
      const target = lowerCmd.split(' ')[1] || '***********/24';
      
      addToConsole('system', `Scanning ${target} for open ports and services...`);
      
      // Simulate scanning delay
      setTimeout(() => {
        if (target === '***********/24' || target === 'network') {
          addToConsole('success', 'Network scan complete. Hosts discovered:');
          addToConsole('info', '*********** - Router/Gateway');
          addToConsole('info', '***********0 - Web Server');
          addToConsole('info', '************ - Database Server');
          addToConsole('info', '************ - File Server');
          
          // Update network map
          setNetworkMap({
            hosts: [
              { ip: '***********', type: 'router', services: ['ssh:22', 'http:80'] },
              { ip: '***********0', type: 'webserver', services: ['http:80', 'https:443', 'ssh:22'] },
              { ip: '************', type: 'database', services: ['mysql:3306', 'ssh:22'] },
              { ip: '************', type: 'fileserver', services: ['ftp:21', 'smb:445', 'ssh:22'] }
            ]
          });
        } else if (target === '***********') {
          addToConsole('success', 'Host scan complete for ***********:');
          addToConsole('info', 'Port 22: SSH - OpenSSH 7.9');
          addToConsole('info', 'Port 80: HTTP - Router Admin Interface');
        } else if (target === '***********0') {
          addToConsole('success', 'Host scan complete for ***********0:');
          addToConsole('info', 'Port 22: SSH - OpenSSH 7.4');
          addToConsole('info', 'Port 80: HTTP - Apache 2.4.6');
          addToConsole('info', 'Port 443: HTTPS - Apache 2.4.6');
        } else if (target === '************') {
          addToConsole('success', 'Host scan complete for ************:');
          addToConsole('info', 'Port 22: SSH - OpenSSH 7.4');
          addToConsole('info', 'Port 3306: MySQL 5.7.30');
        } else if (target === '************') {
          addToConsole('success', 'Host scan complete for ************:');
          addToConsole('info', 'Port 21: FTP - vsftpd 3.0.2');
          addToConsole('info', 'Port 22: SSH - OpenSSH 7.4');
          addToConsole('info', 'Port 445: SMB - Samba 4.9.1');
        } else {
          addToConsole('error', `No hosts found at ${target}`);
        }
        
        // Mark first objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 1 ? { ...obj, completed: true } : obj
        ));
      }, 2000);
    } else if (lowerCmd.startsWith('enum')) {
      const parts = lowerCmd.split(' ');
      const target = parts[1];
      const service = parts[2];
      
      if (!target || !service) {
        addToConsole('error', 'Please specify a target and service to enumerate.');
        addToConsole('system', 'Usage: enum <target> <service>');
        addToConsole('system', 'Example: enum ************ ftp');
        return;
      }
      
      // Check if first objective is completed
      if (!objectives.find(obj => obj.id === 1)?.completed) {
        addToConsole('error', 'You need to scan the network first.');
        return;
      }
      
      addToConsole('system', `Enumerating ${service} service on ${target}...`);
      
      // Simulate enumeration delay
      setTimeout(() => {
        if (target === '***********0' && (service === 'http' || service === 'web' || service === '80')) {
          addToConsole('success', 'Web server enumeration complete:');
          addToConsole('info', 'Web application: Custom CMS');
          addToConsole('info', 'Potential vulnerabilities:');
          addToConsole('info', '- SQL Injection in login form');
          addToConsole('info', '- Outdated Apache version with known CVEs');
        } else if (target === '************' && (service === 'mysql' || service === 'db' || service === '3306')) {
          addToConsole('success', 'Database enumeration complete:');
          addToConsole('info', 'MySQL 5.7.30 with weak configuration');
          addToConsole('info', 'Anonymous access enabled');
          addToConsole('info', 'Default credentials may be in use');
        } else if (target === '************' && (service === 'ftp' || service === '21')) {
          addToConsole('success', 'FTP enumeration complete:');
          addToConsole('info', 'Anonymous FTP access enabled');
          addToConsole('info', 'Writable directories found');
          addToConsole('info', 'Outdated vsftpd 3.0.2 with known vulnerabilities');
        } else if (target === '************' && (service === 'smb' || service === '445')) {
          addToConsole('success', 'SMB enumeration complete:');
          addToConsole('info', 'Samba 4.9.1 with misconfigured shares');
          addToConsole('info', 'Readable shares without authentication');
          addToConsole('info', 'Potential for SMB relay attacks');
        } else {
          addToConsole('error', `Could not enumerate ${service} on ${target}`);
          return;
        }
        
        // Mark second objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 2 ? { ...obj, completed: true } : obj
        ));
      }, 2000);
    } else if (lowerCmd.startsWith('exploit')) {
      const parts = lowerCmd.split(' ');
      const target = parts[1];
      const service = parts[2];
      
      if (!target || !service) {
        addToConsole('error', 'Please specify a target and service to exploit.');
        addToConsole('system', 'Usage: exploit <target> <service>');
        addToConsole('system', 'Example: exploit ************ ftp');
        return;
      }
      
      // Check if second objective is completed
      if (!objectives.find(obj => obj.id === 2)?.completed) {
        addToConsole('error', 'You need to enumerate services first.');
        return;
      }
      
      addToConsole('system', `Exploiting ${service} service on ${target}...`);
      
      // Simulate exploitation delay
      setTimeout(() => {
        if (target === '***********0' && (service === 'http' || service === 'web' || service === '80')) {
          addToConsole('success', 'SQL Injection successful!');
          addToConsole('info', 'Dumped user credentials from database');
          addToConsole('data', 'admin:5f4dcc3b5aa765d61d8327deb882cf99');
          addToConsole('data', 'user1:e10adc3949ba59abbe56e057f20f883e');
          addToConsole('info', 'Gained access to web admin panel');
        } else if (target === '************' && (service === 'mysql' || service === 'db' || service === '3306')) {
          addToConsole('success', 'MySQL exploitation successful!');
          addToConsole('info', 'Connected to database with default credentials');
          addToConsole('data', 'Connected to: information_schema');
          addToConsole('data', 'Available databases: information_schema, mysql, performance_schema, sys, webapp_db');
        } else if (target === '************' && (service === 'ftp' || service === '21')) {
          addToConsole('success', 'FTP exploitation successful!');
          addToConsole('info', 'Logged in with anonymous access');
          addToConsole('data', 'Directory listing:');
          addToConsole('data', '- backup/');
          addToConsole('data', '- config/');
          addToConsole('data', '- logs/');
        } else if (target === '************' && (service === 'smb' || service === '445')) {
          addToConsole('success', 'SMB exploitation successful!');
          addToConsole('info', 'Accessed shares without authentication');
          addToConsole('data', 'Available shares:');
          addToConsole('data', '- public');
          addToConsole('data', '- backups');
          addToConsole('data', '- admin$');
        } else {
          addToConsole('error', `Could not exploit ${service} on ${target}`);
          return;
        }
        
        // Mark third objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 3 ? { ...obj, completed: true } : obj
        ));
      }, 2000);
    } else if (lowerCmd === 'privesc') {
      // Check if third objective is completed
      if (!objectives.find(obj => obj.id === 3)?.completed) {
        addToConsole('error', 'You need to exploit a service first.');
        return;
      }
      
      addToConsole('system', 'Attempting privilege escalation...');
      
      // Simulate privilege escalation delay
      setTimeout(() => {
        addToConsole('success', 'Privilege escalation successful!');
        addToConsole('info', 'Exploited kernel vulnerability CVE-2021-4034');
        addToConsole('info', 'Gained root access to the system');
        addToConsole('data', 'uid=0(root) gid=0(root) groups=0(root)');
        
        // Mark fourth objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 4 ? { ...obj, completed: true } : obj
        ));
      }, 2000);
    } else if (lowerCmd === 'extract') {
      // Check if fourth objective is completed
      if (!objectives.find(obj => obj.id === 4)?.completed) {
        addToConsole('error', 'You need to escalate privileges first.');
        return;
      }
      
      addToConsole('system', 'Extracting sensitive data from the system...');
      
      // Simulate extraction delay
      setTimeout(() => {
        addToConsole('success', 'Data extraction successful!');
        addToConsole('info', 'Found secret flag in /root/flag.txt:');
        addToConsole('flag', 'flag{n3tw0rk_p3n3tr4t10n_m4st3r_2023}');
        
        // Mark fifth objective as completed
        setObjectives(prev => prev.map(obj => 
          obj.id === 5 ? { ...obj, completed: true } : obj
        ));
        
        // Show flag input
        setShowFlagInput(true);
      }, 2000);
    } else if (lowerCmd.startsWith('submit')) {
      const submittedFlag = cmd.split(' ').slice(1).join(' ');
      
      if (!submittedFlag) {
        addToConsole('error', 'Please specify a flag to submit.');
        addToConsole('system', 'Usage: submit <flag>');
        return;
      }
      
      // Check if the flag is correct
      if (submittedFlag === 'flag{n3tw0rk_p3n3tr4t10n_m4st3r_2023}') {
        addToConsole('success', 'Flag is correct! Challenge completed!');
        
        // Stop the timer
        clearInterval(timerRef.current);
        
        // Mark challenge as completed
        setIsSuccess(true);
        
        // Show success message
        setMessage({ type: 'success', text: 'Challenge completed successfully!' });
        
        // Call the onComplete callback
        if (onComplete) {
          onComplete({
            success: true,
            flag: submittedFlag,
            attempts,
            timeSpent: elapsedTime,
            approachScore: calculateApproachScore(),
            totalScore: calculateTotalScore(elapsedTime)
          });
        }
      } else {
        addToConsole('error', 'Incorrect flag. Try again.');
      }
    } else if (lowerCmd === 'clear') {
      setConsoleOutput([
        { type: 'system', text: 'Console cleared.' },
        { type: 'system', text: 'Type "help" for available commands.' }
      ]);
    } else {
      addToConsole('error', `Unknown command: ${cmd}`);
      addToConsole('system', 'Type "help" for available commands.');
    }
  };
  
  const handleFlagSubmit = (e) => {
    e.preventDefault();
    
    // Process the flag submission
    processCommand(`submit ${flag}`);
    
    // Clear the flag input
    setFlag('');
  };
  
  // Calculate approach score based on number of attempts and objectives completed
  const calculateApproachScore = () => {
    const objectivesCompleted = objectives.filter(obj => obj.completed).length;
    const objectivesTotal = objectives.length;
    const objectivesScore = (objectivesCompleted / objectivesTotal) * 50;
    
    // Penalize for excessive attempts
    const attemptsScore = Math.max(0, 50 - (attempts > 15 ? (attempts - 15) * 2 : 0));
    
    return Math.round(objectivesScore + attemptsScore);
  };
  
  // Calculate total score based on approach score and time
  const calculateTotalScore = (time) => {
    const approachScore = calculateApproachScore();
    
    // Time bonus: max 50 points, decreases as time increases
    const timeLimit = challengeData?.completion_criteria?.time_limit || 7200; // Default: 2 hours
    const timeScore = Math.max(0, 50 - Math.floor((time / timeLimit) * 50));
    
    return approachScore + timeScore;
  };
  
  // Format time (seconds) to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} border rounded-lg overflow-hidden`}>
      {/* Challenge Header */}
      <div className={`p-4 border-b ${darkMode ? 'border-gray-800' : 'border-gray-200'} flex justify-between items-center`}>
        <h2 className="text-xl font-bold flex items-center">
          <FaNetworkWired className="text-blue-500 mr-2" />
          Network Penetration Challenge
        </h2>
        <div className="flex items-center space-x-2">
          <div className={`px-3 py-1 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-100'} flex items-center`}>
            <FaStopwatch className="mr-1 text-blue-500" />
            <span className="text-sm">{formatTime(elapsedTime)}</span>
          </div>
        </div>
      </div>
      
      {/* Challenge Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Objectives */}
          <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <FaFlag className="mr-2 text-red-500" />
              Objectives
            </h3>
            
            {message && (
              <div className={`p-3 rounded-lg mb-4 ${
                message.type === 'success' 
                  ? 'bg-green-100 text-green-800 border-green-200' 
                  : message.type === 'warning'
                    ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                    : 'bg-red-100 text-red-800 border-red-200'
              } border`}>
                <div className="flex items-center">
                  {message.type === 'success' ? (
                    <FaCheck className="mr-2" />
                  ) : message.type === 'warning' ? (
                    <FaExclamationTriangle className="mr-2" />
                  ) : (
                    <FaInfoCircle className="mr-2" />
                  )}
                  <span>{message.text}</span>
                </div>
              </div>
            )}
            
            <ul className="space-y-4">
              {objectives.map((objective) => (
                <li key={objective.id} className="flex items-start">
                  <div className={`mt-0.5 mr-2 flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
                    objective.completed 
                      ? 'bg-green-500 text-white' 
                      : darkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-200 text-gray-500'
                  }`}>
                    {objective.completed ? (
                      <FaCheck className="text-xs" />
                    ) : (
                      <span className="text-xs">{objective.id}</span>
                    )}
                  </div>
                  <span className={objective.completed ? 'line-through opacity-70' : ''}>
                    {objective.description}
                  </span>
                </li>
              ))}
            </ul>
            
            {showFlagInput && !isSuccess && (
              <div className="mt-6">
                <h4 className="font-bold mb-2">Submit Flag:</h4>
                <form onSubmit={handleFlagSubmit} className="flex">
                  <input
                    type="text"
                    value={flag}
                    onChange={(e) => setFlag(e.target.value)}
                    placeholder="flag{...}"
                    className={`flex-grow p-2 rounded-l-lg ${
                      darkMode 
                        ? 'bg-gray-800 border-gray-700 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    } border`}
                  />
                  <button
                    type="submit"
                    className="px-4 py-2 rounded-r-lg bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Submit
                  </button>
                </form>
              </div>
            )}
            
            {isSuccess && (
              <div className="mt-6 p-4 bg-green-100 text-green-800 border border-green-200 rounded-lg">
                <div className="flex items-center mb-2">
                  <FaTrophy className="mr-2 text-yellow-500" />
                  <span className="font-bold">Challenge Completed!</span>
                </div>
                <p>You successfully completed the Network Penetration Challenge.</p>
                <div className="mt-2 grid grid-cols-2 gap-2">
                  <div>
                    <span className="text-sm font-bold">Time:</span>
                    <span className="ml-2">{formatTime(elapsedTime)}</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Attempts:</span>
                    <span className="ml-2">{attempts}</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Approach Score:</span>
                    <span className="ml-2">{calculateApproachScore()}/100</span>
                  </div>
                  <div>
                    <span className="text-sm font-bold">Total Score:</span>
                    <span className="ml-2">{calculateTotalScore(elapsedTime)}/100</span>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Middle and Right Columns - Terminal and Info */}
          <div className="lg:col-span-2">
            <div className={`p-6 rounded-lg ${darkMode ? 'bg-[#252D4A] border-gray-700' : 'bg-gray-50 border-gray-200'} border`}>
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <FaTerminal className="mr-2 text-green-500" />
                Terminal
              </h3>
              
              <div 
                id="network-pentest-console"
                className={`h-80 overflow-y-auto p-4 rounded-lg font-mono text-sm ${
                  darkMode 
                    ? 'bg-gray-900 text-gray-300' 
                    : 'bg-gray-800 text-gray-200'
                }`}
              >
                {consoleOutput.map((output, index) => (
                  <div key={index} className="mb-1">
                    <span className={
                      output.type === 'system' 
                        ? 'text-blue-400' 
                        : output.type === 'command' 
                          ? 'text-green-400'
                          : output.type === 'error'
                            ? 'text-red-400'
                            : output.type === 'success'
                              ? 'text-green-400'
                              : output.type === 'info'
                                ? 'text-yellow-400'
                                : output.type === 'data'
                                  ? 'text-purple-400'
                                  : output.type === 'flag'
                                    ? 'text-red-400 font-bold'
                                    : 'text-gray-400'
                    }>
                      {output.text}
                    </span>
                  </div>
                ))}
              </div>
              
              <form onSubmit={handleCommand} className="mt-4 flex">
                <div className={`px-3 py-2 ${
                  darkMode 
                    ? 'bg-gray-900 text-green-400' 
                    : 'bg-gray-800 text-green-400'
                } rounded-l-lg`}>
                  $
                </div>
                <input
                  type="text"
                  value={command}
                  onChange={(e) => setCommand(e.target.value)}
                  className={`flex-grow p-2 ${
                    darkMode 
                      ? 'bg-gray-900 border-gray-700 text-gray-300' 
                      : 'bg-gray-800 border-gray-700 text-gray-200'
                  } border-y border-r rounded-r-lg font-mono`}
                  placeholder="Type a command..."
                  disabled={isSuccess}
                />
              </form>
              
              <div className="mt-4">
                <h4 className={`font-bold mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Challenge Description:</h4>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Your task is to perform a penetration test on a simulated network. Identify vulnerabilities, 
                  exploit them to gain access, escalate privileges, and extract the secret flag.
                  Use the terminal to interact with the target network. Type "help" to see available commands.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkPenetrationChallenge;
