import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaInfoCircle, FaShieldAlt, FaBell, FaTimes } from 'react-icons/fa';
import { useGlobalTheme } from '../../contexts/GlobalThemeContext';
import { supabase } from '../../lib/supabase';
import SecurityInsightsMiddleware from '../../middleware/SecurityInsightsMiddleware';

/**
 * SecurityNotifications Component
 * 
 * This component displays real-time security notifications and alerts
 * from the security insights middleware.
 */
const SecurityNotifications = () => {
  const { darkMode } = useGlobalTheme();
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  
  // Fetch notifications on component mount
  useEffect(() => {
    fetchNotifications();
    
    // Set up listener for new notifications
    const removeListener = SecurityInsightsMiddleware.addListener(handleNewNotification);
    
    // Set up real-time subscription for notifications
    const subscription = supabase
      .channel('security-notifications')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'notifications' }, 
        (payload) => {
          if (payload.eventType === 'INSERT') {
            handleNewNotification({
              type: 'notification',
              action: 'INSERT',
              data: payload.new
            });
          }
        }
      )
      .subscribe();
    
    // Clean up on unmount
    return () => {
      removeListener();
      subscription.unsubscribe();
    };
  }, []);
  
  // Fetch notifications from the database
  const fetchNotifications = async () => {
    try {
      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        console.log('No user logged in, skipping notification fetch');
        return;
      }
      
      // Fetch notifications for the current user
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })
        .limit(10);
        
      if (error) {
        console.error('Error fetching notifications:', error);
        return;
      }
      
      // Update state with fetched notifications
      setNotifications(data || []);
      
      // Count unread notifications
      const unread = data?.filter(notification => !notification.is_read).length || 0;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Error in fetchNotifications:', error);
    }
  };
  
  // Handle new notification
  const handleNewNotification = (notification) => {
    if (notification.type === 'notification' || notification.type === 'security_event') {
      // Add the new notification to the state
      setNotifications(prev => {
        // Check if notification already exists
        const exists = prev.some(n => n.id === notification.data.id);
        
        if (exists) {
          return prev;
        }
        
        // Add new notification at the beginning
        return [notification.data, ...prev].slice(0, 10);
      });
      
      // Increment unread count
      setUnreadCount(prev => prev + 1);
    }
  };
  
  // Mark a notification as read
  const markAsRead = async (notificationId) => {
    try {
      // Update the notification in the database
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);
        
      if (error) {
        console.error('Error marking notification as read:', error);
        return;
      }
      
      // Update the notification in the state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, is_read: true } 
            : notification
        )
      );
      
      // Decrement unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error in markAsRead:', error);
    }
  };
  
  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        console.log('No user logged in, skipping mark all as read');
        return;
      }
      
      // Update all notifications in the database
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', session.user.id)
        .eq('is_read', false);
        
      if (error) {
        console.error('Error marking all notifications as read:', error);
        return;
      }
      
      // Update all notifications in the state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, is_read: true }))
      );
      
      // Reset unread count
      setUnreadCount(0);
    } catch (error) {
      console.error('Error in markAllAsRead:', error);
    }
  };
  
  // Get icon for notification type
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'alert':
        return <FaExclamationTriangle className="text-red-500" />;
      case 'warning':
        return <FaExclamationTriangle className="text-yellow-500" />;
      case 'info':
        return <FaInfoCircle className="text-blue-500" />;
      default:
        return <FaShieldAlt className="text-green-500" />;
    }
  };
  
  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setShowNotifications(!showNotifications)}
        className="relative p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
      >
        <FaBell className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-700'}`} />
        
        {/* Unread Badge */}
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">
            {unreadCount}
          </span>
        )}
      </button>
      
      {/* Notifications Panel */}
      {showNotifications && (
        <div className={`absolute right-0 mt-2 w-80 sm:w-96 rounded-lg shadow-lg z-50 ${
          darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
        }`}>
          <div className="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 className="font-medium">Security Notifications</h3>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Mark all as read
              </button>
            )}
          </div>
          
          <div className="max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                No notifications
              </div>
            ) : (
              notifications.map(notification => (
                <div
                  key={notification.id}
                  className={`p-3 border-b border-gray-200 dark:border-gray-700 ${
                    !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-3 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <h4 className="font-medium text-sm">{notification.title}</h4>
                        {!notification.is_read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                          >
                            <FaTimes size={12} />
                          </button>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{notification.message}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {new Date(notification.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityNotifications;
