import React, { useState, useEffect } from 'react';
import { FaServer, FaNetworkWired, FaWifi, FaDesktop, FaLaptop, FaShieldAlt, FaGlobe, FaInfoCircle, FaCheck } from 'react-icons/fa';

/**
 * Interactive Network Topology Diagram
 * 
 * This component provides an interactive visualization of a network topology
 * with different types of network devices and their connections.
 * Users can click on devices to learn about their functions and properties.
 */
const NetworkTopologyInteractive = () => {
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [completedDevices, setCompletedDevices] = useState([]);
  const [showCompletionMessage, setShowCompletionMessage] = useState(false);
  
  // Network devices data
  const devices = [
    {
      id: 'router1',
      type: 'router',
      name: 'Internet Router',
      x: 400,
      y: 80,
      description: 'Connects your local network to the internet. It directs traffic between different networks using IP addresses.',
      details: [
        'Operates at Layer 3 (Network Layer)',
        'Uses IP addresses to make forwarding decisions',
        'Creates separate broadcast domains',
        'Provides NAT (Network Address Translation)',
        'Often includes basic firewall functionality'
      ]
    },
    {
      id: 'firewall1',
      type: 'firewall',
      name: 'Network Firewall',
      x: 400,
      y: 160,
      description: 'Monitors and controls incoming and outgoing network traffic based on security rules.',
      details: [
        'Acts as a barrier between trusted and untrusted networks',
        'Filters traffic based on rules and policies',
        'Can block unauthorized access attempts',
        'May include intrusion prevention features',
        'Some firewalls can inspect application layer data (Layer 7)'
      ]
    },
    {
      id: 'switch1',
      type: 'switch',
      name: 'Core Switch',
      x: 400,
      y: 240,
      description: 'Connects devices within your local network and forwards data based on MAC addresses.',
      details: [
        'Operates at Layer 2 (Data Link Layer)',
        'Uses MAC addresses to make forwarding decisions',
        'Creates separate collision domains for each port',
        'Maintains a MAC address table',
        'All ports share the same broadcast domain'
      ]
    },
    {
      id: 'ap1',
      type: 'ap',
      name: 'Wireless Access Point',
      x: 240,
      y: 240,
      description: 'Allows wireless devices to connect to a wired network using Wi-Fi.',
      details: [
        'Converts wired Ethernet to wireless signals',
        'Uses radio waves to transmit data',
        'Supports various Wi-Fi standards (802.11a/b/g/n/ac/ax)',
        'Can create multiple wireless networks (SSIDs)',
        'May include security features like WPA3 encryption'
      ]
    },
    {
      id: 'server1',
      type: 'server',
      name: 'File Server',
      x: 560,
      y: 240,
      description: 'Stores and manages files for network users. Provides centralized storage accessible to multiple users.',
      details: [
        'Typically has more storage and memory than regular computers',
        'Runs server operating systems (Windows Server, Linux)',
        'Can host multiple services (file sharing, printing, etc.)',
        'Often uses RAID for data redundancy',
        'May have redundant power supplies for reliability'
      ]
    },
    {
      id: 'pc1',
      type: 'pc',
      name: 'Desktop Computer',
      x: 320,
      y: 320,
      description: 'End-user device that connects to the network to access resources and services.',
      details: [
        'Contains a Network Interface Card (NIC) to connect to the network',
        'Can connect via Ethernet cable or Wi-Fi',
        'Receives an IP address from DHCP or static configuration',
        'Uses DNS to resolve domain names to IP addresses',
        'Typically part of a workgroup or domain'
      ]
    },
    {
      id: 'laptop1',
      type: 'laptop',
      name: 'Laptop',
      x: 160,
      y: 320,
      description: 'Portable computer that can connect to the network wirelessly or via Ethernet.',
      details: [
        'Contains both wired and wireless network interfaces',
        'Can switch between different networks',
        'Uses power-saving features for wireless communications',
        'May support multiple wireless bands (2.4GHz and 5GHz)',
        'Can create ad-hoc networks with other devices'
      ]
    },
    {
      id: 'printer1',
      type: 'printer',
      name: 'Network Printer',
      x: 480,
      y: 320,
      description: 'Shared printing device that connects directly to the network rather than to a specific computer.',
      details: [
        'Has its own network interface and IP address',
        'Can be accessed by multiple users across the network',
        'Supports various printing protocols (IPP, AirPrint, etc.)',
        'May include a built-in print server',
        'Often supports scanning to email or network folders'
      ]
    }
  ];
  
  // Network connections
  const connections = [
    { from: 'router1', to: 'firewall1' },
    { from: 'firewall1', to: 'switch1' },
    { from: 'switch1', to: 'ap1' },
    { from: 'switch1', to: 'server1' },
    { from: 'switch1', to: 'pc1' },
    { from: 'switch1', to: 'printer1' },
    { from: 'ap1', to: 'laptop1' }
  ];
  
  // Handle device selection
  const handleDeviceClick = (device) => {
    setSelectedDevice(device);
    
    // Mark device as completed if not already
    if (!completedDevices.includes(device.id)) {
      const updatedCompletedDevices = [...completedDevices, device.id];
      setCompletedDevices(updatedCompletedDevices);
      
      // Save progress to localStorage
      try {
        const userProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');
        if (!userProgress.networkDevices) {
          userProgress.networkDevices = {};
        }
        userProgress.networkDevices.completedDevices = updatedCompletedDevices;
        localStorage.setItem('userProgress', JSON.stringify(userProgress));
        
        // Check if all devices have been explored
        if (updatedCompletedDevices.length === devices.length) {
          setShowCompletionMessage(true);
          
          // Save completion status
          userProgress.networkDevices.completed = true;
          userProgress.networkDevices.completedAt = new Date().toISOString();
          localStorage.setItem('userProgress', JSON.stringify(userProgress));
        }
      } catch (error) {
        console.error('Error saving progress:', error);
      }
    }
  };
  
  // Load saved progress
  useEffect(() => {
    try {
      const userProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');
      if (userProgress.networkDevices && userProgress.networkDevices.completedDevices) {
        setCompletedDevices(userProgress.networkDevices.completedDevices);
        
        // Check if all devices have been explored
        if (userProgress.networkDevices.completedDevices.length === devices.length) {
          setShowCompletionMessage(true);
        }
      }
    } catch (error) {
      console.error('Error loading progress:', error);
    }
  }, []);
  
  // Render device icon based on type
  const renderDeviceIcon = (type, size = 'text-2xl') => {
    switch (type) {
      case 'router':
        return <FaNetworkWired className={`${size} text-blue-400`} />;
      case 'switch':
        return <FaNetworkWired className={`${size} text-green-400`} />;
      case 'ap':
        return <FaWifi className={`${size} text-purple-400`} />;
      case 'server':
        return <FaServer className={`${size} text-yellow-400`} />;
      case 'pc':
        return <FaDesktop className={`${size} text-gray-400`} />;
      case 'laptop':
        return <FaLaptop className={`${size} text-gray-400`} />;
      case 'firewall':
        return <FaShieldAlt className={`${size} text-red-400`} />;
      case 'internet':
        return <FaGlobe className={`${size} text-blue-400`} />;
      default:
        return <FaDesktop className={`${size} text-gray-400`} />;
    }
  };
  
  // Calculate completion percentage
  const completionPercentage = Math.round((completedDevices.length / devices.length) * 100);
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Network Topology Explorer</h3>
        
        <div className="text-sm text-gray-400">
          Explored: {completedDevices.length}/{devices.length} devices ({completionPercentage}%)
        </div>
      </div>
      
      {/* Network Diagram */}
      <div className="relative h-96 bg-[#1E293B] rounded-lg mb-4 overflow-hidden border border-gray-700">
        {/* Connections */}
        <svg className="absolute inset-0 w-full h-full">
          {connections.map((conn, index) => {
            const source = devices.find(d => d.id === conn.from);
            const target = devices.find(d => d.id === conn.to);
            
            return (
              <line
                key={index}
                x1={source.x}
                y1={source.y}
                x2={target.x}
                y2={target.y}
                stroke="#4B5563"
                strokeWidth="2"
              />
            );
          })}
        </svg>
        
        {/* Devices */}
        {devices.map(device => (
          <div
            key={device.id}
            className={`absolute w-20 h-20 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center justify-center cursor-pointer transition-all ${
              selectedDevice?.id === device.id ? 'ring-2 ring-primary' : ''
            }`}
            style={{ left: device.x, top: device.y }}
            onClick={() => handleDeviceClick(device)}
          >
            <div className={`w-12 h-12 rounded-full flex items-center justify-center bg-[#0F172A] ${
              completedDevices.includes(device.id) ? 'border-2 border-green-500' : ''
            }`}>
              {renderDeviceIcon(device.type)}
              {completedDevices.includes(device.id) && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                  <FaCheck className="text-white text-xs" />
                </div>
              )}
            </div>
            <div className="text-xs mt-1 text-center font-medium">
              {device.name}
            </div>
          </div>
        ))}
        
        {/* Internet Cloud */}
        <div
          className="absolute w-24 h-16 transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center"
          style={{ left: 400, top: 20 }}
        >
          <div className="w-full h-full bg-[#0F172A] rounded-full flex items-center justify-center border border-blue-500/30">
            <FaGlobe className="text-2xl text-blue-400" />
          </div>
          <div className="absolute -bottom-5 text-xs font-medium">Internet</div>
        </div>
      </div>
      
      {/* Device Information Panel */}
      {selectedDevice ? (
        <div className="bg-[#1E293B] p-4 rounded-lg">
          <div className="flex items-center mb-3">
            {renderDeviceIcon(selectedDevice.type, 'text-3xl')}
            <h4 className="text-lg font-bold ml-3">{selectedDevice.name}</h4>
          </div>
          
          <p className="text-gray-300 mb-4">{selectedDevice.description}</p>
          
          <div className="bg-[#0F172A] p-3 rounded-lg">
            <h5 className="font-bold mb-2 flex items-center">
              <FaInfoCircle className="mr-2 text-primary" />
              Key Characteristics
            </h5>
            <ul className="list-disc list-inside space-y-1 text-gray-300">
              {selectedDevice.details.map((detail, index) => (
                <li key={index}>{detail}</li>
              ))}
            </ul>
          </div>
        </div>
      ) : (
        <div className="bg-[#1E293B] p-4 rounded-lg text-center">
          <p className="text-gray-300">Click on a device in the network diagram to learn more about it.</p>
        </div>
      )}
      
      {/* Completion Message */}
      {showCompletionMessage && (
        <div className="mt-4 bg-green-900/20 border border-green-500 p-4 rounded-lg">
          <h4 className="font-bold text-green-400 flex items-center">
            <FaCheck className="mr-2" />
            Exploration Complete!
          </h4>
          <p className="text-gray-300 mt-2">
            You've successfully explored all the network devices in this topology. You now understand the basic functions and characteristics of common network devices.
          </p>
        </div>
      )}
    </div>
  );
};

export default NetworkTopologyInteractive;
