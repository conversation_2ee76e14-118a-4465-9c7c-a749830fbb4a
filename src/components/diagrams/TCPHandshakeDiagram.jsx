import React, { useState, useEffect } from 'react';
import { Fa<PERSON>lay, FaPause, FaRedo, FaInfoCircle } from 'react-icons/fa';

/**
 * TCP Handshake Diagram Component
 * 
 * This component provides an interactive visualization of the TCP 3-way handshake
 * process with step-by-step animation and explanations.
 */
const TCPHandshakeDiagram = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  
  // Define the handshake steps
  const handshakeSteps = [
    {
      title: "Initial State",
      description: "Before the TCP connection begins, both the client and server are in a CLOSED state.",
      clientState: "CLOSED",
      serverState: "CLOSED (Listening)",
      packetType: null,
      packetPosition: 0,
      details: "TCP requires a connection to be established before data can be sent. The server must be in a LISTENING state, ready to accept connections."
    },
    {
      title: "Step 1: SYN",
      description: "The client sends a SYN (synchronize) packet to the server.",
      clientState: "SYN_SENT",
      serverState: "LISTENING",
      packetType: "SYN",
      packetPosition: 30,
      details: "The SYN packet contains an initial sequence number (ISN) chosen by the client. This sequence number will be used to track bytes in the data stream. The SYN flag in the TCP header is set to 1."
    },
    {
      title: "Step 2: SYN-ACK",
      description: "The server responds with a SYN-ACK (synchronize-acknowledge) packet.",
      clientState: "SYN_SENT",
      serverState: "SYN_RECEIVED",
      packetType: "SYN-ACK",
      packetPosition: 70,
      details: "The server acknowledges the client's SYN by incrementing the sequence number by 1. It also includes its own initial sequence number. Both the SYN and ACK flags in the TCP header are set to 1."
    },
    {
      title: "Step 3: ACK",
      description: "The client sends an ACK (acknowledge) packet to the server.",
      clientState: "ESTABLISHED",
      serverState: "SYN_RECEIVED",
      packetType: "ACK",
      packetPosition: 30,
      details: "The client acknowledges the server's SYN by incrementing the sequence number by 1. The ACK flag in the TCP header is set to 1. At this point, the client enters the ESTABLISHED state."
    },
    {
      title: "Connection Established",
      description: "The connection is now established, and data can be transmitted in both directions.",
      clientState: "ESTABLISHED",
      serverState: "ESTABLISHED",
      packetType: "DATA",
      packetPosition: 50,
      details: "Both sides have agreed on initial sequence numbers and acknowledged each other's sequence numbers. The connection is now full-duplex, meaning data can flow in both directions simultaneously."
    }
  ];
  
  // Auto-play functionality
  useEffect(() => {
    let timer;
    if (isPlaying) {
      timer = setInterval(() => {
        setCurrentStep(prev => {
          const nextStep = prev + 1;
          if (nextStep >= handshakeSteps.length) {
            setIsPlaying(false);
            return prev;
          }
          return nextStep;
        });
      }, 2000);
    }
    
    return () => clearInterval(timer);
  }, [isPlaying, handshakeSteps.length]);
  
  // Update completion percentage
  useEffect(() => {
    setCompletionPercentage(Math.round((currentStep / (handshakeSteps.length - 1)) * 100));
    
    // Save progress to localStorage when animation is complete
    if (currentStep === handshakeSteps.length - 1) {
      try {
        const userProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');
        if (!userProgress.completedExercises) {
          userProgress.completedExercises = {};
        }
        userProgress.completedExercises['tcp-handshake-visualization'] = {
          completed: true,
          completedAt: new Date().toISOString(),
          score: 100
        };
        localStorage.setItem('userProgress', JSON.stringify(userProgress));
      } catch (error) {
        console.error('Error updating user progress:', error);
      }
    }
  }, [currentStep, handshakeSteps.length]);
  
  // Toggle play/pause
  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };
  
  // Reset animation
  const resetAnimation = () => {
    setCurrentStep(0);
    setIsPlaying(false);
  };
  
  // Go to specific step
  const goToStep = (step) => {
    if (step >= 0 && step < handshakeSteps.length) {
      setCurrentStep(step);
      setIsPlaying(false);
    }
  };
  
  // Toggle details
  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };
  
  // Get the current step data
  const currentStepData = handshakeSteps[currentStep];
  
  // Render packet animation
  const renderPacket = () => {
    if (!currentStepData.packetType) return null;
    
    let packetColor;
    switch (currentStepData.packetType) {
      case 'SYN':
        packetColor = 'bg-blue-500';
        break;
      case 'SYN-ACK':
        packetColor = 'bg-green-500';
        break;
      case 'ACK':
        packetColor = 'bg-purple-500';
        break;
      case 'DATA':
        packetColor = 'bg-yellow-500';
        break;
      default:
        packetColor = 'bg-gray-500';
    }
    
    return (
      <div 
        className={`absolute h-8 px-2 rounded flex items-center justify-center text-xs font-bold text-white ${packetColor}`}
        style={{
          left: `${currentStepData.packetPosition}%`,
          top: '50%',
          transform: 'translateY(-50%)',
          transition: 'left 1s ease'
        }}
      >
        {currentStepData.packetType}
      </div>
    );
  };
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-6 border border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold">TCP 3-Way Handshake</h3>
        
        {completionPercentage === 100 && (
          <div className="bg-green-900/30 text-green-400 px-3 py-1 rounded-full text-sm font-medium border border-green-500/30">
            Completed
          </div>
        )}
      </div>
      
      <p className="text-gray-300 mb-6">
        The TCP 3-way handshake is the process used to establish a connection between two devices before data can be exchanged.
      </p>
      
      {/* Animation Controls */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex space-x-2">
          <button
            onClick={togglePlay}
            className="bg-primary hover:bg-primary-hover text-black px-3 py-1 rounded flex items-center"
          >
            {isPlaying ? <><FaPause className="mr-1" /> Pause</> : <><FaPlay className="mr-1" /> Play</>}
          </button>
          
          <button
            onClick={resetAnimation}
            className="bg-[#1E293B] hover:bg-[#334155] text-white px-3 py-1 rounded flex items-center"
          >
            <FaRedo className="mr-1" /> Reset
          </button>
        </div>
        
        <button
          onClick={toggleDetails}
          className="bg-[#1E293B] hover:bg-[#334155] text-white px-3 py-1 rounded flex items-center"
        >
          <FaInfoCircle className="mr-1" /> {showDetails ? 'Hide Details' : 'Show Details'}
        </button>
      </div>
      
      {/* Step Title and Description */}
      <div className="mb-4">
        <h4 className="text-lg font-semibold">{currentStepData.title}</h4>
        <p className="text-gray-300">{currentStepData.description}</p>
      </div>
      
      {/* Handshake Visualization */}
      <div className="relative h-40 bg-[#1E293B] rounded-lg mb-4 overflow-hidden">
        {/* Client */}
        <div className="absolute left-4 top-4 bottom-4 w-24 bg-blue-900/30 rounded flex flex-col items-center justify-center">
          <div className="text-blue-400 font-bold mb-2">Client</div>
          <div className={`text-xs px-2 py-1 rounded ${
            currentStepData.clientState === 'ESTABLISHED' 
              ? 'bg-green-900/30 text-green-400 border border-green-500/30' 
              : 'bg-yellow-900/30 text-yellow-400 border border-yellow-500/30'
          }`}>
            {currentStepData.clientState}
          </div>
        </div>
        
        {/* Server */}
        <div className="absolute right-4 top-4 bottom-4 w-24 bg-blue-900/30 rounded flex flex-col items-center justify-center">
          <div className="text-blue-400 font-bold mb-2">Server</div>
          <div className={`text-xs px-2 py-1 rounded ${
            currentStepData.serverState === 'ESTABLISHED' 
              ? 'bg-green-900/30 text-green-400 border border-green-500/30' 
              : 'bg-yellow-900/30 text-yellow-400 border border-yellow-500/30'
          }`}>
            {currentStepData.serverState}
          </div>
        </div>
        
        {/* Connection Line */}
        <div className="absolute left-28 right-28 top-1/2 h-0.5 bg-gray-700"></div>
        
        {/* Packet */}
        {renderPacket()}
      </div>
      
      {/* Detailed Explanation */}
      {showDetails && (
        <div className="bg-[#1E293B] p-4 rounded-lg mb-4 border border-gray-700">
          <h4 className="text-base font-semibold mb-2">Technical Details</h4>
          <p className="text-sm text-gray-300">{currentStepData.details}</p>
          
          {currentStep === 1 && (
            <div className="mt-2 bg-[#0F172A] p-2 rounded border border-gray-700">
              <h5 className="text-sm font-semibold text-blue-400">SYN Packet</h5>
              <pre className="text-xs text-gray-300 font-mono mt-1">
                Flags: SYN=1, ACK=0<br />
                Seq=x (Initial Sequence Number)<br />
                Ack=0
              </pre>
            </div>
          )}
          
          {currentStep === 2 && (
            <div className="mt-2 bg-[#0F172A] p-2 rounded border border-gray-700">
              <h5 className="text-sm font-semibold text-green-400">SYN-ACK Packet</h5>
              <pre className="text-xs text-gray-300 font-mono mt-1">
                Flags: SYN=1, ACK=1<br />
                Seq=y (Server's Initial Sequence Number)<br />
                Ack=x+1 (Client's Seq + 1)
              </pre>
            </div>
          )}
          
          {currentStep === 3 && (
            <div className="mt-2 bg-[#0F172A] p-2 rounded border border-gray-700">
              <h5 className="text-sm font-semibold text-purple-400">ACK Packet</h5>
              <pre className="text-xs text-gray-300 font-mono mt-1">
                Flags: SYN=0, ACK=1<br />
                Seq=x+1<br />
                Ack=y+1 (Server's Seq + 1)
              </pre>
            </div>
          )}
        </div>
      )}
      
      {/* Progress Indicator */}
      <div className="mb-2">
        <div className="flex justify-between text-sm text-gray-400 mb-1">
          <span>Step {currentStep + 1} of {handshakeSteps.length}</span>
          <span>Completion: {completionPercentage}%</span>
        </div>
        <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
          <div 
            className="h-full bg-primary transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
      </div>
      
      {/* Step Navigation */}
      <div className="flex justify-between mt-4">
        <div className="flex space-x-2">
          {handshakeSteps.map((_, index) => (
            <button
              key={index}
              onClick={() => goToStep(index)}
              className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === index 
                  ? 'bg-primary text-black' 
                  : 'bg-[#1E293B] text-gray-400 hover:bg-[#334155]'
              }`}
            >
              {index + 1}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TCPHandshakeDiagram;
