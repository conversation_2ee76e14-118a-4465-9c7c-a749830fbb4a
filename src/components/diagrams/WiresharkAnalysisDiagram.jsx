import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaInfoCircle, FaChevronDown, FaChevronRight } from 'react-icons/fa';

/**
 * Wireshark Packet Analysis Diagram Component
 * 
 * This component provides an interactive visualization of TCP and UDP packets
 * as they would appear in Wireshark, with explanations of key fields.
 */
const WiresharkAnalysisDiagram = () => {
  const [activeTab, setActiveTab] = useState('tcp');
  const [expandedPacket, setExpandedPacket] = useState(null);
  const [showTooltip, setShowTooltip] = useState(null);
  
  // Sample packet data
  const tcpPackets = [
    {
      id: 1,
      time: '0.000000',
      source: '*************',
      destination: '*************',
      protocol: 'TCP',
      length: 74,
      info: 'SYN [SEQ=1000] [WIN=65535] [LEN=0]',
      type: 'SYN',
      color: 'bg-blue-500/20 border-blue-500'
    },
    {
      id: 2,
      time: '0.050123',
      source: '*************',
      destination: '*************',
      protocol: 'TCP',
      length: 74,
      info: 'SYN, ACK [SEQ=2000] [ACK=1001] [WIN=64240] [LEN=0]',
      type: 'SYN-ACK',
      color: 'bg-green-500/20 border-green-500'
    },
    {
      id: 3,
      time: '0.050234',
      source: '*************',
      destination: '*************',
      protocol: 'TCP',
      length: 66,
      info: 'ACK [SEQ=1001] [ACK=2001] [WIN=65535] [LEN=0]',
      type: 'ACK',
      color: 'bg-purple-500/20 border-purple-500'
    },
    {
      id: 4,
      time: '0.050345',
      source: '*************',
      destination: '*************',
      protocol: 'HTTP',
      length: 546,
      info: 'GET / HTTP/1.1',
      type: 'DATA',
      color: 'bg-yellow-500/20 border-yellow-500'
    },
    {
      id: 5,
      time: '0.150456',
      source: '*************',
      destination: '*************',
      protocol: 'TCP',
      length: 66,
      info: 'ACK [SEQ=2001] [ACK=1481] [WIN=64240] [LEN=0]',
      type: 'ACK',
      color: 'bg-purple-500/20 border-purple-500'
    }
  ];
  
  const udpPackets = [
    {
      id: 1,
      time: '0.000000',
      source: '*************',
      destination: '8.8.8.8',
      protocol: 'DNS',
      length: 73,
      info: 'Standard query 0x1234 A example.com',
      type: 'REQUEST',
      color: 'bg-blue-500/20 border-blue-500'
    },
    {
      id: 2,
      time: '0.045678',
      source: '8.8.8.8',
      destination: '*************',
      protocol: 'DNS',
      length: 89,
      info: 'Standard query response 0x1234 A example.com A *************',
      type: 'RESPONSE',
      color: 'bg-green-500/20 border-green-500'
    },
    {
      id: 3,
      time: '1.234567',
      source: '*************',
      destination: '192.168.1.255',
      protocol: 'UDP',
      length: 78,
      info: 'Source port: 5678  Destination port: 5678',
      type: 'BROADCAST',
      color: 'bg-yellow-500/20 border-yellow-500'
    },
    {
      id: 4,
      time: '2.345678',
      source: '*************',
      destination: '224.0.0.251',
      protocol: 'MDNS',
      length: 64,
      info: 'Standard query 0x0000 PTR _http._tcp.local',
      type: 'MULTICAST',
      color: 'bg-purple-500/20 border-purple-500'
    }
  ];
  
  // Get active packets based on selected tab
  const activePackets = activeTab === 'tcp' ? tcpPackets : udpPackets;
  
  // Toggle packet expansion
  const togglePacket = (packetId) => {
    if (expandedPacket === packetId) {
      setExpandedPacket(null);
    } else {
      setExpandedPacket(packetId);
    }
  };
  
  // Render packet details
  const renderPacketDetails = (packet) => {
    if (activeTab === 'tcp') {
      return (
        <div className="pl-8 py-2 bg-[#0F172A] border-t border-gray-700 text-xs">
          <div className="mb-2">
            <h4 className="font-bold text-gray-300">Transmission Control Protocol</h4>
            <div className="grid grid-cols-2 gap-2 mt-1">
              <div>
                <span className="text-gray-400">Source Port:</span> 49152
              </div>
              <div>
                <span className="text-gray-400">Destination Port:</span> 80
              </div>
              <div>
                <span className="text-gray-400">Sequence Number:</span> {packet.type === 'SYN' ? '1000' : packet.type === 'SYN-ACK' ? '2000' : '1001'}
              </div>
              <div>
                <span className="text-gray-400">Acknowledgment Number:</span> {packet.type === 'SYN' ? '0' : packet.type === 'SYN-ACK' ? '1001' : '2001'}
              </div>
              <div>
                <span className="text-gray-400">Flags:</span> {packet.type === 'SYN' ? 'SYN' : packet.type === 'SYN-ACK' ? 'SYN, ACK' : 'ACK'}
              </div>
              <div>
                <span className="text-gray-400">Window Size:</span> {packet.type === 'SYN-ACK' ? '64240' : '65535'}
              </div>
            </div>
          </div>
          
          {packet.protocol === 'HTTP' && (
            <div>
              <h4 className="font-bold text-gray-300">Hypertext Transfer Protocol</h4>
              <div className="mt-1">
                <div><span className="text-gray-400">Request Method:</span> GET</div>
                <div><span className="text-gray-400">Request URI:</span> /</div>
                <div><span className="text-gray-400">Request Version:</span> HTTP/1.1</div>
                <div><span className="text-gray-400">Host:</span> example.com</div>
              </div>
            </div>
          )}
        </div>
      );
    } else {
      return (
        <div className="pl-8 py-2 bg-[#0F172A] border-t border-gray-700 text-xs">
          <div className="mb-2">
            <h4 className="font-bold text-gray-300">User Datagram Protocol</h4>
            <div className="grid grid-cols-2 gap-2 mt-1">
              <div>
                <span className="text-gray-400">Source Port:</span> {packet.protocol === 'DNS' ? '53123' : '5678'}
              </div>
              <div>
                <span className="text-gray-400">Destination Port:</span> {packet.protocol === 'DNS' ? '53' : '5678'}
              </div>
              <div>
                <span className="text-gray-400">Length:</span> {packet.length}
              </div>
              <div>
                <span className="text-gray-400">Checksum:</span> 0x{Math.floor(Math.random() * 65535).toString(16).padStart(4, '0')}
              </div>
            </div>
          </div>
          
          {packet.protocol === 'DNS' && (
            <div>
              <h4 className="font-bold text-gray-300">Domain Name System</h4>
              <div className="mt-1">
                <div><span className="text-gray-400">Transaction ID:</span> 0x1234</div>
                <div><span className="text-gray-400">Type:</span> {packet.type === 'REQUEST' ? 'Standard query' : 'Standard query response'}</div>
                <div><span className="text-gray-400">Query:</span> A example.com</div>
                {packet.type === 'RESPONSE' && (
                  <div><span className="text-gray-400">Answer:</span> example.com A *************</div>
                )}
              </div>
            </div>
          )}
        </div>
      );
    }
  };
  
  return (
    <div className="bg-[#0F172A] rounded-lg p-4 border border-gray-700">
      <h3 className="text-lg font-bold mb-4">Wireshark Packet Analysis</h3>
      
      <div className="mb-4 text-sm text-gray-300">
        <p>This visualization shows how TCP and UDP packets appear in Wireshark, a popular network protocol analyzer. Examine the differences between the protocols and how they handle data transmission.</p>
      </div>
      
      {/* Tabs */}
      <div className="flex border-b border-gray-700 mb-4">
        <button
          className={`px-4 py-2 ${activeTab === 'tcp' ? 'text-primary border-b-2 border-primary' : 'text-gray-400 hover:text-gray-300'}`}
          onClick={() => setActiveTab('tcp')}
        >
          TCP Packets
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'udp' ? 'text-primary border-b-2 border-primary' : 'text-gray-400 hover:text-gray-300'}`}
          onClick={() => setActiveTab('udp')}
        >
          UDP Packets
        </button>
      </div>
      
      {/* Wireshark Interface */}
      <div className="border border-gray-700 rounded overflow-hidden mb-4">
        {/* Toolbar */}
        <div className="bg-[#1E293B] px-3 py-2 border-b border-gray-700 flex items-center text-xs">
          <div className="flex items-center mr-4">
            <FaSearch className="text-gray-400 mr-1" />
            <input
              type="text"
              placeholder="Display filter..."
              className="bg-[#0F172A] text-gray-300 px-2 py-1 rounded w-64 border border-gray-700"
            />
          </div>
          <div className="flex items-center">
            <FaFilter className="text-gray-400 mr-1" />
            <span className="text-gray-300">{activeTab === 'tcp' ? 'tcp' : 'udp'}</span>
          </div>
        </div>
        
        {/* Column Headers */}
        <div className="bg-[#1E293B] px-3 py-1 border-b border-gray-700 grid grid-cols-7 text-xs font-bold text-gray-300">
          <div className="col-span-1">No.</div>
          <div className="col-span-1">Time</div>
          <div className="col-span-1">Source</div>
          <div className="col-span-1">Destination</div>
          <div className="col-span-1">Protocol</div>
          <div className="col-span-1">Length</div>
          <div className="col-span-1">Info</div>
        </div>
        
        {/* Packet List */}
        <div className="max-h-80 overflow-y-auto">
          {activePackets.map(packet => (
            <React.Fragment key={packet.id}>
              <div 
                className={`px-3 py-1 grid grid-cols-7 text-xs border-b border-gray-700 cursor-pointer hover:bg-[#1E293B] ${packet.color}`}
                onClick={() => togglePacket(packet.id)}
              >
                <div className="col-span-1 flex items-center">
                  {expandedPacket === packet.id ? <FaChevronDown className="mr-1" /> : <FaChevronRight className="mr-1" />}
                  {packet.id}
                </div>
                <div className="col-span-1">{packet.time}</div>
                <div className="col-span-1">{packet.source}</div>
                <div className="col-span-1">{packet.destination}</div>
                <div className="col-span-1">{packet.protocol}</div>
                <div className="col-span-1">{packet.length}</div>
                <div className="col-span-1 truncate">{packet.info}</div>
              </div>
              
              {expandedPacket === packet.id && renderPacketDetails(packet)}
            </React.Fragment>
          ))}
        </div>
      </div>
      
      {/* Protocol Explanation */}
      <div className="bg-[#1E293B] p-4 rounded-lg">
        <h4 className="text-base font-semibold mb-2">
          {activeTab === 'tcp' ? 'TCP Packet Analysis' : 'UDP Packet Analysis'}
        </h4>
        
        <p className="text-sm text-gray-300 mb-3">
          {activeTab === 'tcp' 
            ? 'TCP packets show the 3-way handshake (SYN, SYN-ACK, ACK) and reliable data transmission with sequence and acknowledgment numbers.'
            : 'UDP packets are simpler with no handshake or acknowledgments. Notice the absence of sequence numbers and connection state tracking.'}
        </p>
        
        <div className="text-xs text-gray-400">
          <p className="mb-1"><strong>Key observations:</strong></p>
          <ul className="list-disc pl-5 space-y-1">
            {activeTab === 'tcp' ? (
              <>
                <li>TCP establishes a connection with the 3-way handshake before sending data</li>
                <li>Each packet has sequence and acknowledgment numbers to track data</li>
                <li>TCP acknowledges received data to ensure reliability</li>
                <li>The connection remains established until explicitly closed</li>
              </>
            ) : (
              <>
                <li>UDP sends data without establishing a connection first</li>
                <li>No sequence or acknowledgment numbers are used</li>
                <li>Each packet is independent and not tracked</li>
                <li>No guarantee of delivery or packet order</li>
                <li>Lower overhead means faster transmission</li>
              </>
            )}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default WiresharkAnalysisDiagram;
