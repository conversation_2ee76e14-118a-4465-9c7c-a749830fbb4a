import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabaseClient';
import { useAuth } from '../contexts/AuthContext';

const AuthCallback = () => {
  const navigate = useNavigate();
  const { setUser } = useAuth();
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) throw error;

        if (session) {
          setUser(session.user);
          navigate('/dashboard');
        } else {
          // If no session, check if this is a password reset
          const hash = window.location.hash;
          if (hash.includes('type=recovery')) {
            navigate('/reset-password');
          } else {
            navigate('/login');
          }
        }
      } catch (error) {
        console.error('Error handling auth callback:', error);
        setError(error.message);
        setTimeout(() => navigate('/login'), 3000);
      }
    };

    handleAuthCallback();
  }, [navigate, setUser]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="max-w-md w-full space-y-8 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 dark:text-red-400">Error</h2>
            <p className="mt-2 text-gray-600 dark:text-gray-300">{error}</p>
            <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
              Redirecting to login...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Processing...</h2>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            Please wait while we complete the authentication process.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthCallback; 