import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaLock, FaArrowLeft } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { supabase } from '../lib/supabase';

const ResetPassword = () => {
  const navigate = useNavigate();
  const { darkMode } = useGlobalTheme();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    if (password !== confirmPassword) {
      setError("Passwords don't match");
      setIsLoading(false);
      return;
    }

    try {
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) throw error;

      setSuccess('Password has been reset successfully!');
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    } catch (err) {
      console.error('Password reset error:', err);
      setError(err.message || 'Failed to reset password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-center p-4`}>
      <div className="max-w-md w-full">
        {/* Back to login */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/login')}
            className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} flex items-center gap-2`}
          >
            <FaArrowLeft />
            <span>Back to Login</span>
          </button>
        </div>

        {/* Reset password form */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-xl border overflow-hidden`}>
          <div className="p-8">
            <div className="text-center mb-8">
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>Set New Password</h1>
              <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Enter your new password below</p>
            </div>

            {error && (
              <div className={`${darkMode ? 'bg-red-500/20 text-red-400' : 'bg-red-100 text-red-800'} px-4 py-3 rounded mb-6`}>
                {error}
              </div>
            )}

            {success && (
              <div className={`${darkMode ? 'bg-green-500/20 text-green-400' : 'bg-green-100 text-green-800'} px-4 py-3 rounded mb-6`}>
                {success}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="password" className={`block ${darkMode ? 'text-gray-400' : 'text-gray-700'} mb-2`}>New Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <FaLock className={`${darkMode ? 'text-gray-500' : 'text-gray-500'}`} />
                  </div>
                  <input
                    type="password"
                    id="password"
                    className={`${darkMode ? 'bg-[#0B1120] border-gray-800 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`}
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    minLength={8}
                  />
                </div>
                <p className={`mt-1 text-sm ${darkMode ? 'text-gray-500' : 'text-gray-600'}`}>
                  Password must be at least 8 characters
                </p>
              </div>

              <div className="mb-6">
                <label htmlFor="confirmPassword" className={`block ${darkMode ? 'text-gray-400' : 'text-gray-700'} mb-2`}>Confirm New Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <FaLock className={`${darkMode ? 'text-gray-500' : 'text-gray-500'}`} />
                  </div>
                  <input
                    type="password"
                    id="confirmPassword"
                    className={`${darkMode ? 'bg-[#0B1120] border-gray-800 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`}
                    placeholder="••••••••"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                  />
                </div>
              </div>

              <button
                type="submit"
                className={`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${
                  isLoading ? 'opacity-70 cursor-not-allowed' : ''
                }`}
                disabled={isLoading}
              >
                {isLoading ? 'Resetting Password...' : 'Reset Password'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword; 