import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaEnvelope, FaArrowLeft } from 'react-icons/fa';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import { supabase } from '../lib/supabase';

const ForgotPassword = () => {
  const { darkMode } = useGlobalTheme();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;

      setSuccess('Password reset instructions have been sent to your email.');
    } catch (err) {
      console.error('Password reset error:', err);
      setError(err.message || 'Failed to send password reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-[#0B1120]' : 'bg-gray-50'} flex items-center justify-center p-4`}>
      <div className="max-w-md w-full">
        {/* Back to login */}
        <div className="mb-8">
          <Link to="/login" className={`${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} flex items-center gap-2`}>
            <FaArrowLeft />
            <span>Back to Login</span>
          </Link>
        </div>

        {/* Forgot password form */}
        <div className={`${darkMode ? 'bg-[#1A1F35] border-gray-800' : 'bg-white border-gray-200'} rounded-xl border overflow-hidden`}>
          <div className="p-8">
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                Reset your password
              </h2>
            </div>

            {error && (
              <div className={`${darkMode ? 'bg-red-500/20 text-red-400' : 'bg-red-100 text-red-800'} px-4 py-3 rounded mb-6`}>
                {error}
              </div>
            )}

            {success && (
              <div className={`${darkMode ? 'bg-green-500/20 text-green-400' : 'bg-green-100 text-green-800'} px-4 py-3 rounded mb-6`}>
                {success}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label htmlFor="email" className={`block ${darkMode ? 'text-gray-400' : 'text-gray-700'} mb-2`}>Email Address</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <FaEnvelope className={`${darkMode ? 'text-gray-500' : 'text-gray-500'}`} />
                  </div>
                  <input
                    type="email"
                    id="email"
                    className={`${darkMode ? 'bg-[#0B1120] border-gray-800 text-white' : 'bg-gray-50 border-gray-300 text-gray-900'} border rounded-lg block w-full pl-10 p-3 focus:outline-none focus:ring-1 focus:ring-[#88cc14] focus:border-[#88cc14]`}
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
              </div>

              <button
                type="submit"
                className={`w-full bg-[#88cc14] hover:bg-[#7ab811] text-black font-bold py-3 px-4 rounded-lg transition-colors ${
                  isLoading ? 'opacity-70 cursor-not-allowed' : ''
                }`}
                disabled={isLoading}
              >
                {isLoading ? 'Sending Instructions...' : 'Send Reset Instructions'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;