import React from 'react';
import { useParams } from 'react-router-dom';
import { useGlobalTheme } from '../contexts/GlobalThemeContext';
import ChallengerHub from '../components/challenger/ChallengerHub';

const Challenger = () => {
  const { darkMode } = useGlobalTheme();
  const { challengeId } = useParams();

  return (
    <div className={`${darkMode ? 'text-white' : 'text-gray-900'}`}>
      <div className="px-4 py-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Challenger</h1>
          <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Compete in cybersecurity challenges and climb the leaderboards
          </p>
        </div>
        <ChallengerHub challengeId={challengeId} />
      </div>
    </div>
  );
};

export default Challenger;
