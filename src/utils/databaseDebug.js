import { supabase } from '../lib/supabase';

/**
 * Debug database table structures and check for issues
 */
export const debugDatabaseTables = async () => {
  console.log('🔍 Debugging database tables...');
  
  try {
    // Check profiles table structure
    const { data: profilesColumns, error: profilesError } = await supabase
      .rpc('get_table_columns', { table_name: 'profiles' });
    
    if (!profilesError) {
      console.log('✅ Profiles table columns:', profilesColumns);
    } else {
      console.log('❌ Profiles table error:', profilesError);
    }

    // Check user_profiles table structure
    const { data: userProfilesColumns, error: userProfilesError } = await supabase
      .rpc('get_table_columns', { table_name: 'user_profiles' });
    
    if (!userProfilesError) {
      console.log('✅ User_profiles table columns:', userProfilesColumns);
    } else {
      console.log('❌ User_profiles table error:', userProfilesError);
    }

    // Try a simple select to see what tables exist
    const { data: profilesTest, error: profilesTestError } = await supabase
      .from('profiles')
      .select('id, avatar_url')
      .limit(1);

    if (!profilesTestError) {
      console.log('✅ Profiles table accessible');
    } else {
      console.log('❌ Profiles table not accessible:', profilesTestError);
    }

    const { data: userProfilesTest, error: userProfilesTestError } = await supabase
      .from('user_profiles')
      .select('id, avatar_url')
      .limit(1);

    if (!userProfilesTestError) {
      console.log('✅ User_profiles table accessible');
    } else {
      console.log('❌ User_profiles table not accessible:', userProfilesTestError);
    }

  } catch (error) {
    console.error('Database debug error:', error);
  }
};

/**
 * Test profile update without problematic fields
 */
export const testProfileUpdate = async (userId) => {
  console.log('🧪 Testing profile update for user:', userId);
  
  try {
    // Test update with minimal data
    const testData = {
      updated_at: new Date().toISOString()
    };

    // Try profiles table
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .update(testData)
      .eq('id', userId)
      .select()
      .single();

    if (!profilesError) {
      console.log('✅ Profiles table update successful');
      return { success: true, table: 'profiles', data: profilesData };
    }

    // Try user_profiles table
    const { data: userProfilesData, error: userProfilesError } = await supabase
      .from('user_profiles')
      .update(testData)
      .eq('id', userId)
      .select()
      .single();

    if (!userProfilesError) {
      console.log('✅ User_profiles table update successful');
      return { success: true, table: 'user_profiles', data: userProfilesData };
    }

    console.log('❌ Both table updates failed');
    console.log('Profiles error:', profilesError);
    console.log('User_profiles error:', userProfilesError);
    
    return { success: false, errors: { profiles: profilesError, user_profiles: userProfilesError } };

  } catch (error) {
    console.error('Test profile update error:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Check for database triggers that might be causing issues
 */
export const checkDatabaseTriggers = async () => {
  console.log('🔍 Checking database triggers...');
  
  try {
    // This would require a custom RPC function to check triggers
    // For now, we'll just log that we're checking
    console.log('Trigger check would require custom RPC function');
    
    return { success: true, message: 'Trigger check requires custom RPC function' };
  } catch (error) {
    console.error('Trigger check error:', error);
    return { success: false, error: error.message };
  }
};

export default {
  debugDatabaseTables,
  testProfileUpdate,
  checkDatabaseTriggers
};
