import { supabase } from '../lib/supabase';
import toast from 'react-hot-toast';

/**
 * Profile Utilities
 * 
 * Helper functions for profile management and database operations
 */

/**
 * Ensure user has a profile in the database
 */
export const ensureUserProfile = async (user) => {
  if (!user) return null;

  try {
    // Check if profile exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error checking profile:', fetchError);
      return null;
    }

    if (existingProfile) {
      return existingProfile;
    }

    // Create profile if it doesn't exist
    const defaultProfile = {
      id: user.id,
      user_id: user.id,
      email: user.email,
      subscription_tier: 'free',
      full_name: user.user_metadata?.full_name || '',
      username: user.user_metadata?.username || user.email?.split('@')[0] || '',
      bio: '',
      avatar_url: '',
      phone_number: '',
      location: '',
      country: '',
      website: '',
      linkedin_url: '',
      github_url: '',
      twitter_url: '',
      instagram_url: '',
      facebook_url: '',
      profile_visibility: 'public',
      show_email: false,
      show_phone: false,
      show_location: true,
      show_social_links: true,
      email_notifications: true,
      push_notifications: true,
      marketing_emails: false,
      theme_preference: 'system',
      preferred_language: 'en',
      total_xp: 0,
      current_level: 1,
      total_points: 0,
      total_coins: 100,
      coins: 100,
      current_streak: 0,
      longest_streak: 0,
      modules_completed: 0,
      challenges_completed: 0,
      achievements_unlocked: 0,
      profile_completion_percentage: 0,
      onboarding_completed: false,
      email_verified: false,
      phone_verified: false,
      is_admin: false,
      role: 'user',
      permissions: [],
      two_factor_enabled: false,
      login_attempts: 0,
      login_count: 0,
      last_activity: new Date().toISOString(),
      last_login: new Date().toISOString()
    };

    const { data: newProfile, error: insertError } = await supabase
      .from('user_profiles')
      .insert(defaultProfile)
      .select()
      .single();

    if (insertError) {
      console.error('Error creating profile:', insertError);
      toast.error('Failed to create user profile');
      return null;
    }

    console.log('Profile created successfully:', newProfile);
    toast.success('Profile created successfully!');
    return newProfile;

  } catch (error) {
    console.error('Error in ensureUserProfile:', error);
    toast.error('Failed to initialize profile');
    return null;
  }
};

/**
 * Update user profile with validation
 */
export const updateUserProfile = async (userId, profileData) => {
  try {
    // Validate required fields
    if (!userId) {
      throw new Error('User ID is required');
    }

    // Clean the data - remove undefined values and problematic fields
    const cleanData = Object.fromEntries(
      Object.entries(profileData).filter(([_, value]) => value !== undefined)
    );

    // Remove fields that might cause trigger issues
    delete cleanData.profile_completion_percentage;

    // Add updated timestamp
    cleanData.updated_at = new Date().toISOString();

    // Try profiles table first (more common)
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .update(cleanData)
      .eq('id', userId)
      .select()
      .single();

    if (!profilesError) {
      return { success: true, data: profilesData };
    }

    // If profiles table fails, try user_profiles table
    const { data: userProfilesData, error: userProfilesError } = await supabase
      .from('user_profiles')
      .update(cleanData)
      .eq('id', userId)
      .select()
      .single();

    if (userProfilesError) {
      console.error('Error updating profile:', userProfilesError);
      return { success: false, error: userProfilesError.message };
    }

    return { success: true, data: userProfilesData };

  } catch (error) {
    console.error('Error updating profile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get user settings
 */
export const getUserSettings = async (userId) => {
  try {
    const { data, error } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    return data;

  } catch (error) {
    console.error('Error fetching user settings:', error);
    return null;
  }
};

/**
 * Update user settings
 */
export const updateUserSettings = async (userId, settingsData) => {
  try {
    const { data, error } = await supabase
      .from('user_settings')
      .upsert({
        user_id: userId,
        ...settingsData,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;

    return { success: true, data };

  } catch (error) {
    console.error('Error updating settings:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Log user activity
 */
export const logUserActivity = async (userId, activityType, description, data = null) => {
  try {
    await supabase
      .from('user_activity_log')
      .insert({
        user_id: userId,
        activity_type: activityType,
        activity_description: description,
        activity_data: data
      });

  } catch (error) {
    console.error('Error logging activity:', error);
    // Don't throw error for logging failures
  }
};

/**
 * Check if user profile is complete
 */
export const calculateProfileCompletion = (profile) => {
  if (!profile) return 0;

  let score = 0;
  const fields = [
    'full_name',
    'bio',
    'avatar_url',
    'location',
    'email',
    'phone_number',
    'linkedin_url',
    'github_url',
    'website'
  ];

  fields.forEach(field => {
    if (profile[field] && profile[field].trim() !== '') {
      score += 10;
    }
  });

  // Bonus points for verification
  if (profile.email_verified) score += 5;
  if (profile.phone_verified) score += 5;

  return Math.min(score, 100);
};

/**
 * Validate profile data
 */
export const validateProfileData = (data) => {
  const errors = [];

  // Email validation
  if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('Invalid email format');
  }

  // Phone validation
  if (data.phone_number && !/^\+?[1-9]\d{1,14}$/.test(data.phone_number)) {
    errors.push('Invalid phone number format');
  }

  // URL validation
  const urlFields = ['website', 'linkedin_url', 'github_url', 'twitter_url', 'instagram_url', 'facebook_url'];
  urlFields.forEach(field => {
    if (data[field] && data[field].trim() !== '') {
      try {
        new URL(data[field]);
      } catch {
        errors.push(`Invalid ${field.replace('_url', '')} URL format`);
      }
    }
  });

  // Username validation
  if (data.username && (data.username.length < 3 || data.username.length > 30)) {
    errors.push('Username must be between 3 and 30 characters');
  }

  if (data.username && !/^[a-zA-Z0-9_-]+$/.test(data.username)) {
    errors.push('Username can only contain letters, numbers, underscores, and hyphens');
  }

  return errors;
};

/**
 * Initialize user settings if they don't exist
 */
export const ensureUserSettings = async (userId) => {
  try {
    const existing = await getUserSettings(userId);
    if (existing) return existing;

    const defaultSettings = {
      user_id: userId,
      email_notifications: {
        achievements: true,
        challenges: true,
        updates: true,
        marketing: false
      },
      push_notifications: {
        achievements: true,
        challenges: true,
        reminders: true
      },
      privacy_settings: {
        profile_visibility: 'public',
        show_progress: true,
        show_achievements: true,
        show_email: false,
        show_phone: false,
        show_location: true,
        show_social_links: true
      },
      learning_preferences: {
        difficulty: 'intermediate',
        pace: 'normal',
        topics: []
      },
      ui_preferences: {
        theme: 'system',
        language: 'en',
        sidebar_collapsed: false
      },
      security_settings: {
        two_factor: false,
        login_alerts: true,
        session_timeout: 30
      }
    };

    const { data, error } = await supabase
      .from('user_settings')
      .insert(defaultSettings)
      .select()
      .single();

    if (error) throw error;

    return data;

  } catch (error) {
    console.error('Error ensuring user settings:', error);
    return null;
  }
};
