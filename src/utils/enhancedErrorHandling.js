
// Enhanced Error Handling Utility
export class APIError extends Error {
  constructor(message, status, code, details) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

export const withRetry = async (operation, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Don't retry on client errors (4xx)
      if (error.status >= 400 && error.status < 500) {
        throw error;
      }
      
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
  }
  
  throw lastError;
};

export const handleAPIError = (error, context = '') => {
  console.error(`API Error in ${context}:`, {
    message: error.message,
    status: error.status,
    code: error.code,
    details: error.details,
    timestamp: error.timestamp
  });
  
  // Send to monitoring service
  if (window.gtag) {
    window.gtag('event', 'api_error', {
      error_message: error.message,
      error_code: error.code,
      context: context
    });
  }
  
  return error;
};
