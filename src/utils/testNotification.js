/**
 * Test utility to create a security notification
 */
import { supabase } from '../lib/supabase';
import SecurityInsightsMiddleware from '../middleware/SecurityInsightsMiddleware';

/**
 * Create a test security notification
 * @param {string} type - The type of notification (info, warning, alert)
 * @param {string} title - The notification title
 * @param {string} message - The notification message
 * @returns {Promise<Object>} - The created notification
 */
export const createTestNotification = async (type = 'info', title = 'Test Notification', message = 'This is a test notification') => {
  try {
    // Get the current user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session?.user) {
      console.error('No user logged in, cannot create notification');
      return null;
    }
    
    // Create the notification
    const notification = {
      title,
      message,
      type,
      data: {
        test: true,
        timestamp: new Date().toISOString()
      }
    };
    
    // Use the middleware to create the notification
    await SecurityInsightsMiddleware.createNotification(notification);
    
    console.log('Test notification created:', notification);
    return notification;
  } catch (error) {
    console.error('Error creating test notification:', error);
    return null;
  }
};

/**
 * Create a test security event
 * @param {string} severity - The severity of the event (critical, high, medium, low, info)
 * @param {string} eventType - The type of event
 * @param {string} message - The event message
 * @returns {Promise<Object>} - The created event
 */
export const createTestSecurityEvent = async (severity = 'medium', eventType = 'test_event', message = 'This is a test security event') => {
  try {
    // Get the current user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session?.user) {
      console.error('No user logged in, cannot create security event');
      return null;
    }
    
    // Create the security event
    const { data, error } = await supabase
      .from('security_events')
      .insert({
        user_id: session.user.id,
        event_type: eventType,
        severity,
        event_data: {
          message,
          source: 'test',
          timestamp: new Date().toISOString()
        }
      })
      .select();
      
    if (error) {
      console.error('Error creating test security event:', error);
      return null;
    }
    
    console.log('Test security event created:', data[0]);
    return data[0];
  } catch (error) {
    console.error('Error creating test security event:', error);
    return null;
  }
};

export default {
  createTestNotification,
  createTestSecurityEvent
};
