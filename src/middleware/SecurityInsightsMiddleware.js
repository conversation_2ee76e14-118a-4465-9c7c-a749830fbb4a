/**
 * SecurityInsightsMiddleware.js
 * 
 * This middleware handles real-time data processing and synchronization
 * between the client and server for security insights data.
 */

import { supabase } from '../lib/supabase';
import SecurityInsightsService from '../services/SecurityInsightsService';
import RealThreatDataService from '../services/RealThreatDataService';

// Cache for middleware data
const middlewareCache = {
  initialized: false,
  subscriptions: [],
  listeners: [],
  processingQueue: [],
  isProcessing: false
};

/**
 * Initialize the security insights middleware
 * @returns {Promise} Promise that resolves when initialization is complete
 */
const initialize = async () => {
  if (middlewareCache.initialized) {
    console.log('Security Insights Middleware already initialized');
    return true;
  }
  
  console.log('Initializing Security Insights Middleware...');
  
  try {
    // Initialize the security insights service
    await SecurityInsightsService.initialize();
    
    // Set up real-time subscriptions
    setupRealtimeSubscriptions();
    
    // Start the processing queue
    startProcessingQueue();
    
    middlewareCache.initialized = true;
    console.log('Security Insights Middleware initialized successfully');
    return true;
  } catch (error) {
    console.error('Failed to initialize Security Insights Middleware:', error);
    return false;
  }
};

/**
 * Set up real-time subscriptions for security insights data
 */
const setupRealtimeSubscriptions = () => {
  // Unsubscribe from any existing subscriptions
  middlewareCache.subscriptions.forEach(subscription => {
    if (subscription && typeof subscription.unsubscribe === 'function') {
      subscription.unsubscribe();
    }
  });
  
  middlewareCache.subscriptions = [];
  
  // Subscribe to security_posture table changes
  const securityPostureSubscription = supabase
    .channel('security-posture-changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'security_posture' }, 
      (payload) => {
        console.log('Security posture update received in middleware:', payload);
        // Add to processing queue
        addToProcessingQueue({
          type: 'security_posture',
          action: payload.eventType,
          data: payload.new,
          old: payload.old,
          timestamp: new Date().toISOString()
        });
      }
    )
    .subscribe();
  
  middlewareCache.subscriptions.push(securityPostureSubscription);
  
  // Subscribe to threat_intelligence table changes
  const threatIntelligenceSubscription = supabase
    .channel('threat-intelligence-changes')
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'threat_intelligence' },
      (payload) => {
        console.log('Threat intelligence update received in middleware:', payload);
        // Add to processing queue
        addToProcessingQueue({
          type: 'threat_intelligence',
          action: payload.eventType,
          data: payload.new,
          old: payload.old,
          timestamp: new Date().toISOString()
        });
      }
    )
    .subscribe();
  
  middlewareCache.subscriptions.push(threatIntelligenceSubscription);
  
  // Subscribe to security_events table changes
  const securityEventsSubscription = supabase
    .channel('security-events-changes')
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'security_events' },
      (payload) => {
        console.log('Security event update received in middleware:', payload);
        // Add to processing queue
        addToProcessingQueue({
          type: 'security_event',
          action: payload.eventType,
          data: payload.new,
          old: payload.old,
          timestamp: new Date().toISOString()
        });
      }
    )
    .subscribe();
  
  middlewareCache.subscriptions.push(securityEventsSubscription);
  
  console.log('Real-time subscriptions set up for security insights middleware');
};

/**
 * Add an item to the processing queue
 * @param {Object} item - The item to add to the queue
 */
const addToProcessingQueue = (item) => {
  middlewareCache.processingQueue.push(item);
  
  // Start processing if not already processing
  if (!middlewareCache.isProcessing) {
    processQueue();
  }
};

/**
 * Process the queue of data updates
 */
const processQueue = async () => {
  if (middlewareCache.processingQueue.length === 0) {
    middlewareCache.isProcessing = false;
    return;
  }
  
  middlewareCache.isProcessing = true;
  
  // Get the next item from the queue
  const item = middlewareCache.processingQueue.shift();
  
  try {
    // Process the item based on its type
    switch (item.type) {
      case 'security_posture':
        await processSecurityPostureUpdate(item);
        break;
      case 'threat_intelligence':
        await processThreatIntelligenceUpdate(item);
        break;
      case 'security_event':
        await processSecurityEventUpdate(item);
        break;
      default:
        console.warn(`Unknown item type: ${item.type}`);
    }
    
    // Notify listeners
    notifyListeners(item);
  } catch (error) {
    console.error('Error processing queue item:', error);
  }
  
  // Process the next item in the queue
  setTimeout(processQueue, 100);
};

/**
 * Start processing the queue
 */
const startProcessingQueue = () => {
  if (!middlewareCache.isProcessing) {
    processQueue();
  }
};

/**
 * Process a security posture update
 * @param {Object} item - The update item
 */
const processSecurityPostureUpdate = async (item) => {
  console.log('Processing security posture update:', item);
  
  // Update the security insights service
  await SecurityInsightsService.fetchSecurityInsights();
};

/**
 * Process a threat intelligence update
 * @param {Object} item - The update item
 */
const processThreatIntelligenceUpdate = async (item) => {
  console.log('Processing threat intelligence update:', item);
  
  // Update the real threat data service
  await RealThreatDataService.fetchThreatData();
};

/**
 * Process a security event update
 * @param {Object} item - The update item
 */
const processSecurityEventUpdate = async (item) => {
  console.log('Processing security event update:', item);
  
  // If this is a new security event, check if it requires immediate attention
  if (item.action === 'INSERT') {
    const event = item.data;
    
    // Check if this is a high-severity event
    if (event.severity === 'critical' || event.severity === 'high') {
      // Create a notification
      createNotification({
        title: 'Security Alert',
        message: `A ${event.severity} security event has been detected: ${event.event_type}`,
        type: 'alert',
        data: event
      });
    }
  }
};

/**
 * Create a notification for the user
 * @param {Object} notification - The notification to create
 */
const createNotification = async (notification) => {
  try {
    // Get the current user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session?.user) {
      console.log('No user logged in, skipping notification');
      return;
    }
    
    // Insert the notification into the database
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: session.user.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        data: notification.data,
        is_read: false,
        created_at: new Date().toISOString()
      });
      
    if (error) {
      console.error('Error creating notification:', error);
    }
  } catch (error) {
    console.error('Error creating notification:', error);
  }
};

/**
 * Add a listener for real-time updates
 * @param {Function} listener - The listener function
 * @returns {Function} A function to remove the listener
 */
const addListener = (listener) => {
  if (typeof listener !== 'function') {
    console.error('Listener must be a function');
    return () => {};
  }
  
  middlewareCache.listeners.push(listener);
  
  // Return a function to remove the listener
  return () => {
    const index = middlewareCache.listeners.indexOf(listener);
    if (index !== -1) {
      middlewareCache.listeners.splice(index, 1);
    }
  };
};

/**
 * Notify all listeners of an update
 * @param {Object} item - The update item
 */
const notifyListeners = (item) => {
  middlewareCache.listeners.forEach(listener => {
    try {
      listener(item);
    } catch (error) {
      console.error('Error in listener:', error);
    }
  });
};

// Export the middleware
export default {
  initialize,
  addListener,
  createNotification
};
