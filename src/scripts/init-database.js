import { supabase } from '../lib/supabase';
import fs from 'fs';
import path from 'path';

/**
 * Initialize the database with the required tables and sample data for Start Hack and Challenger
 * This script should be run once to set up the database
 */
const initDatabase = async () => {
  try {
    console.log('Initializing database for Start Hack and Challenger...');
    
    // Create categories table if it doesn't exist
    const { error: categoriesError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'practice_simulation_categories',
      create_statement: `
        CREATE TABLE practice_simulation_categories (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          description TEXT,
          display_order INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `
    });
    
    if (categoriesError) {
      console.error('Error creating categories table:', categoriesError);
    } else {
      console.log('Categories table created or already exists');
      
      // Insert sample categories
      const { error: insertCategoriesError } = await supabase
        .from('practice_simulation_categories')
        .upsert([
          { name: 'Web Security', description: 'Web application security concepts and techniques', display_order: 1 },
          { name: 'Network Security', description: 'Network security concepts and techniques', display_order: 2 },
          { name: 'System Security', description: 'Operating system and system security', display_order: 3 },
          { name: 'Cryptography', description: 'Cryptographic concepts and techniques', display_order: 4 },
          { name: 'Social Engineering', description: 'Social engineering concepts and techniques', display_order: 5 }
        ], { onConflict: 'name' });
      
      if (insertCategoriesError) {
        console.error('Error inserting categories:', insertCategoriesError);
      } else {
        console.log('Sample categories inserted');
      }
    }
    
    // Create difficulty levels table if it doesn't exist
    const { error: difficultyError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'practice_simulation_difficulty_levels',
      create_statement: `
        CREATE TABLE practice_simulation_difficulty_levels (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          description TEXT,
          display_order INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `
    });
    
    if (difficultyError) {
      console.error('Error creating difficulty levels table:', difficultyError);
    } else {
      console.log('Difficulty levels table created or already exists');
      
      // Insert sample difficulty levels
      const { error: insertDifficultyError } = await supabase
        .from('practice_simulation_difficulty_levels')
        .upsert([
          { name: 'Beginner', description: 'Suitable for beginners with little to no experience', display_order: 1 },
          { name: 'Intermediate', description: 'Suitable for users with some experience', display_order: 2 },
          { name: 'Advanced', description: 'Suitable for users with significant experience', display_order: 3 },
          { name: 'Expert', description: 'Suitable for expert users with extensive experience', display_order: 4 }
        ], { onConflict: 'name' });
      
      if (insertDifficultyError) {
        console.error('Error inserting difficulty levels:', insertDifficultyError);
      } else {
        console.log('Sample difficulty levels inserted');
      }
    }
    
    // Create Start Hack simulations table if it doesn't exist
    const { error: simulationsError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'practice_simulations',
      create_statement: `
        CREATE TABLE practice_simulations (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          slug TEXT UNIQUE,
          description TEXT NOT NULL,
          simulation_type TEXT NOT NULL,
          category_id UUID REFERENCES practice_simulation_categories(id),
          difficulty_id UUID REFERENCES practice_simulation_difficulty_levels(id),
          points INTEGER NOT NULL DEFAULT 100,
          estimated_time INTEGER,
          is_premium BOOLEAN DEFAULT false,
          is_business BOOLEAN DEFAULT false,
          objectives JSONB,
          completion_criteria JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          is_active BOOLEAN DEFAULT true
        )
      `
    });
    
    if (simulationsError) {
      console.error('Error creating simulations table:', simulationsError);
    } else {
      console.log('Simulations table created or already exists');
    }
    
    // Create Start Hack simulation attempts table if it doesn't exist
    const { error: attemptsError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'practice_simulation_attempts',
      create_statement: `
        CREATE TABLE practice_simulation_attempts (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          simulation_id UUID REFERENCES practice_simulations(id) ON DELETE CASCADE,
          is_completed BOOLEAN DEFAULT false,
          completion_time INTEGER,
          approach_score INTEGER,
          total_score INTEGER,
          started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          completed_at TIMESTAMP WITH TIME ZONE,
          UNIQUE(user_id, simulation_id)
        )
      `
    });
    
    if (attemptsError) {
      console.error('Error creating simulation attempts table:', attemptsError);
    } else {
      console.log('Simulation attempts table created or already exists');
    }
    
    // Create Challenger challenges table if it doesn't exist
    const { error: challengesError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'challenger_challenges',
      create_statement: `
        CREATE TABLE challenger_challenges (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          slug TEXT UNIQUE,
          description TEXT NOT NULL,
          challenge_type TEXT NOT NULL,
          category_id UUID REFERENCES practice_simulation_categories(id),
          difficulty_id UUID REFERENCES practice_simulation_difficulty_levels(id),
          points INTEGER NOT NULL DEFAULT 100,
          estimated_time INTEGER,
          is_premium BOOLEAN DEFAULT false,
          is_business BOOLEAN DEFAULT false,
          objectives JSONB,
          completion_criteria JSONB,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          is_active BOOLEAN DEFAULT true
        )
      `
    });
    
    if (challengesError) {
      console.error('Error creating challenges table:', challengesError);
    } else {
      console.log('Challenges table created or already exists');
    }
    
    // Create Challenger challenge attempts table if it doesn't exist
    const { error: challengeAttemptsError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'challenger_challenge_attempts',
      create_statement: `
        CREATE TABLE challenger_challenge_attempts (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          challenge_id UUID REFERENCES challenger_challenges(id) ON DELETE CASCADE,
          is_completed BOOLEAN DEFAULT false,
          completion_time INTEGER,
          approach_score INTEGER,
          total_score INTEGER,
          started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          completed_at TIMESTAMP WITH TIME ZONE,
          UNIQUE(user_id, challenge_id)
        )
      `
    });
    
    if (challengeAttemptsError) {
      console.error('Error creating challenge attempts table:', challengeAttemptsError);
    } else {
      console.log('Challenge attempts table created or already exists');
    }
    
    // Create leaderboards table if it doesn't exist
    const { error: leaderboardsError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'leaderboards',
      create_statement: `
        CREATE TABLE leaderboards (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          challenge_id UUID REFERENCES challenger_challenges(id) ON DELETE SET NULL,
          simulation_id UUID REFERENCES practice_simulations(id) ON DELETE SET NULL,
          completion_time INTEGER NOT NULL,
          approach_score INTEGER,
          total_score INTEGER NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          CHECK (
            (challenge_id IS NOT NULL AND simulation_id IS NULL) OR
            (challenge_id IS NULL AND simulation_id IS NOT NULL)
          )
        )
      `
    });
    
    if (leaderboardsError) {
      console.error('Error creating leaderboards table:', leaderboardsError);
    } else {
      console.log('Leaderboards table created or already exists');
    }
    
    console.log('Database initialization complete!');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
};

export default initDatabase;
