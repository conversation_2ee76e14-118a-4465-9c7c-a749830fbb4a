import { supabase } from '../lib/supabase.js';

// Database initialization script for CyberForce
export const initializeDatabase = async () => {
  console.log('🚀 Starting database initialization...');

  try {
    // Check if we can connect to Supabase
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    if (authError) {
      console.warn('⚠️ Auth check failed, but continuing with database setup:', authError.message);
    }

    // Initialize learning paths
    await initializeLearningPaths();
    
    // Initialize learning modules
    await initializeLearningModules();
    
    // Initialize challenges
    await initializeChallenges();
    
    console.log('✅ Database initialization completed successfully!');
    return { success: true };
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    return { success: false, error: error.message };
  }
};

// Initialize learning paths
const initializeLearningPaths = async () => {
  console.log('📚 Initializing learning paths...');

  const learningPaths = [
    {
      id: 'ethical-hacking-fundamentals',
      title: 'Ethical Hacking Fundamentals',
      description: 'Master the fundamentals of ethical hacking and penetration testing',
      difficulty_level: 'beginner',
      estimated_duration: 120,
      total_modules: 50,
      is_premium: false,
      display_order: 1,
      icon_name: 'shield',
      color_scheme: 'blue'
    },
    {
      id: 'network-fundamentals',
      title: 'Network Fundamentals',
      description: 'Learn networking concepts essential for cybersecurity',
      difficulty_level: 'beginner',
      estimated_duration: 80,
      total_modules: 25,
      is_premium: false,
      display_order: 2,
      icon_name: 'network',
      color_scheme: 'green'
    },
    {
      id: 'red-teaming',
      title: 'Red Teaming',
      description: 'Advanced offensive security and red team operations',
      difficulty_level: 'advanced',
      estimated_duration: 200,
      total_modules: 50,
      is_premium: true,
      display_order: 3,
      icon_name: 'sword',
      color_scheme: 'red'
    },
    {
      id: 'blue-teaming',
      title: 'Blue Teaming',
      description: 'Defensive security and blue team operations',
      difficulty_level: 'intermediate',
      estimated_duration: 180,
      total_modules: 50,
      is_premium: true,
      display_order: 4,
      icon_name: 'shield-check',
      color_scheme: 'blue'
    },
    {
      id: 'threat-hunting',
      title: 'Threat Hunting',
      description: 'Proactive threat detection and hunting techniques',
      difficulty_level: 'advanced',
      estimated_duration: 160,
      total_modules: 40,
      is_premium: true,
      display_order: 5,
      icon_name: 'search',
      color_scheme: 'purple'
    }
  ];

  for (const path of learningPaths) {
    try {
      const { data, error } = await supabase
        .from('learning_paths')
        .upsert(path, { onConflict: 'id' })
        .select();

      if (error) {
        console.warn(`⚠️ Could not insert learning path ${path.title}:`, error.message);
      } else {
        console.log(`✅ Learning path "${path.title}" initialized`);
      }
    } catch (err) {
      console.warn(`⚠️ Error with learning path ${path.title}:`, err.message);
    }
  }
};

// Initialize sample learning modules
const initializeLearningModules = async () => {
  console.log('📖 Initializing learning modules...');

  const sampleModules = [
    {
      id: 'eh-1',
      learning_path_id: 'ethical-hacking-fundamentals',
      title: 'Introduction to Ethical Hacking',
      description: 'Learn the basics of ethical hacking and penetration testing',
      difficulty_level: 'beginner',
      estimated_duration: 60,
      points_reward: 50,
      is_premium: false,
      is_free: true,
      display_order: 1,
      content: {
        type: 'lesson',
        sections: [
          {
            title: 'What is Ethical Hacking?',
            content: 'Ethical hacking involves authorized testing of systems to find vulnerabilities...',
            type: 'text'
          },
          {
            title: 'Legal and Ethical Considerations',
            content: 'Understanding the legal framework and ethical guidelines...',
            type: 'text'
          }
        ]
      }
    },
    {
      id: 'nf-1',
      learning_path_id: 'network-fundamentals',
      title: 'Introduction to Networking',
      description: 'Understand basic networking concepts and protocols',
      difficulty_level: 'beginner',
      estimated_duration: 45,
      points_reward: 50,
      is_premium: false,
      is_free: true,
      display_order: 1,
      content: {
        type: 'lesson',
        sections: [
          {
            title: 'OSI Model',
            content: 'The Open Systems Interconnection model...',
            type: 'text'
          },
          {
            title: 'TCP/IP Protocol Suite',
            content: 'Understanding the TCP/IP protocol stack...',
            type: 'text'
          }
        ]
      }
    }
  ];

  for (const module of sampleModules) {
    try {
      const { data, error } = await supabase
        .from('learning_modules')
        .upsert(module, { onConflict: 'id' })
        .select();

      if (error) {
        console.warn(`⚠️ Could not insert module ${module.title}:`, error.message);
      } else {
        console.log(`✅ Module "${module.title}" initialized`);
      }
    } catch (err) {
      console.warn(`⚠️ Error with module ${module.title}:`, err.message);
    }
  }
};

// Initialize sample challenges
const initializeChallenges = async () => {
  console.log('🎯 Initializing challenges...');

  const sampleChallenges = [
    {
      id: 'network-recon-1',
      title: 'Network Reconnaissance',
      description: 'Identify open ports and services on a target system using Nmap',
      difficulty_level: 'beginner',
      category: 'Network Security',
      points_reward: 100,
      time_limit: 30,
      is_premium: false,
      is_free: true,
      is_daily: true,
      content: {
        type: 'practical',
        instructions: 'Use Nmap to scan the target system and identify open ports...',
        target: '*************',
        tools: ['nmap', 'netcat'],
        objectives: [
          'Scan for open ports',
          'Identify running services',
          'Document findings'
        ]
      }
    },
    {
      id: 'web-vuln-1',
      title: 'SQL Injection Detection',
      description: 'Find and exploit SQL injection vulnerabilities in a web application',
      difficulty_level: 'intermediate',
      category: 'Web Security',
      points_reward: 150,
      time_limit: 45,
      is_premium: false,
      is_free: true,
      is_daily: false,
      content: {
        type: 'practical',
        instructions: 'Analyze the web application for SQL injection vulnerabilities...',
        target: 'http://vulnerable-app.local',
        tools: ['burp-suite', 'sqlmap'],
        objectives: [
          'Identify injection points',
          'Craft SQL payloads',
          'Extract database information'
        ]
      }
    }
  ];

  for (const challenge of sampleChallenges) {
    try {
      const { data, error } = await supabase
        .from('challenges')
        .upsert(challenge, { onConflict: 'id' })
        .select();

      if (error) {
        console.warn(`⚠️ Could not insert challenge ${challenge.title}:`, error.message);
      } else {
        console.log(`✅ Challenge "${challenge.title}" initialized`);
      }
    } catch (err) {
      console.warn(`⚠️ Error with challenge ${challenge.title}:`, err.message);
    }
  }
};

// Function to run the initialization
export const runDatabaseInit = async () => {
  console.log('🔧 Running database initialization...');
  
  try {
    const result = await initializeDatabase();
    
    if (result.success) {
      console.log('🎉 Database initialization completed successfully!');
      console.log('📊 You can now use the CyberForce platform with sample data.');
    } else {
      console.error('💥 Database initialization failed:', result.error);
    }
    
    return result;
  } catch (error) {
    console.error('💥 Unexpected error during initialization:', error);
    return { success: false, error: error.message };
  }
};

// Auto-run if this script is executed directly
if (typeof window !== 'undefined' && window.location) {
  // Browser environment - don't auto-run
  console.log('🌐 Database initialization script loaded in browser');
} else {
  // Node environment - could auto-run if needed
  console.log('🖥️ Database initialization script loaded in Node.js');
}

export default { initializeDatabase, runDatabaseInit };
