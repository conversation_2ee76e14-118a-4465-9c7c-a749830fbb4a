import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import ProgressService from '../services/progressService';

export const useUserProgress = () => {
  const { user } = useAuth();
  const [progress, setProgress] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load user progress
  const loadProgress = useCallback(async () => {
    if (!user?.id) {
      setProgress(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Initialize progress if it doesn't exist
      await ProgressService.initializeUserProgress(user.id);
      
      // Load current progress
      const userProgress = await ProgressService.getUserProgress(user.id);
      setProgress(userProgress);
    } catch (err) {
      console.error('Error loading user progress:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Update module progress
  const updateModuleProgress = useCallback(async (moduleId, sectionId = null, completed = false) => {
    if (!user?.id) return null;

    try {
      const result = await ProgressService.updateModuleProgress(user.id, moduleId, sectionId, completed);
      
      // Reload progress to get updated stats
      await loadProgress();
      
      return result;
    } catch (err) {
      console.error('Error updating module progress:', err);
      setError(err.message);
      throw err;
    }
  }, [user?.id, loadProgress]);

  // Update challenge progress
  const updateChallengeProgress = useCallback(async (challengeId, status, pointsEarned = 0) => {
    if (!user?.id) return null;

    try {
      const result = await ProgressService.updateChallengeProgress(user.id, challengeId, status, pointsEarned);
      
      // Reload progress to get updated stats
      await loadProgress();
      
      return result;
    } catch (err) {
      console.error('Error updating challenge progress:', err);
      setError(err.message);
      throw err;
    }
  }, [user?.id, loadProgress]);

  // Update daily challenge progress
  const updateDailyChallengeProgress = useCallback(async (challengeDate, completed = true, pointsEarned = 0) => {
    if (!user?.id) return null;

    try {
      const result = await ProgressService.updateDailyChallengeProgress(user.id, challengeDate, completed, pointsEarned);
      
      // Reload progress to get updated stats
      await loadProgress();
      
      return result;
    } catch (err) {
      console.error('Error updating daily challenge progress:', err);
      setError(err.message);
      throw err;
    }
  }, [user?.id, loadProgress]);

  // Get learning path progress
  const getLearningPathProgress = useCallback(async (pathId) => {
    if (!user?.id) return [];

    try {
      return await ProgressService.getLearningPathProgress(user.id, pathId);
    } catch (err) {
      console.error('Error getting learning path progress:', err);
      return [];
    }
  }, [user?.id]);

  // Get user level information
  const getUserLevel = useCallback(() => {
    if (!progress) return { level: 1, name: 'Beginner', nextLevelPoints: 100 };
    return ProgressService.getUserLevel(progress.total_points || 0);
  }, [progress]);

  // Calculate progress percentage for a learning path
  const calculatePathProgress = useCallback((pathModules, userProgress) => {
    if (!pathModules || pathModules.length === 0) return 0;
    if (!userProgress || userProgress.length === 0) return 0;

    const completedModules = userProgress.filter(p => p.completed).length;
    return Math.round((completedModules / pathModules.length) * 100);
  }, []);

  // Check if user has completed a specific module
  const isModuleCompleted = useCallback((moduleId) => {
    if (!progress?.user_module_progress) return false;
    const moduleProgress = progress.user_module_progress.find(p => p.module_id === moduleId);
    return moduleProgress?.completed || false;
  }, [progress]);

  // Check if user has completed a specific challenge
  const isChallengeCompleted = useCallback((challengeId) => {
    if (!progress?.user_challenge_progress) return false;
    const challengeProgress = progress.user_challenge_progress.find(p => p.challenge_id === challengeId);
    return challengeProgress?.status === 'completed';
  }, [progress]);

  // Get module progress percentage
  const getModuleProgress = useCallback((moduleId) => {
    if (!progress?.user_module_progress) return 0;
    const moduleProgress = progress.user_module_progress.find(p => p.module_id === moduleId);
    return moduleProgress?.progress_percentage || 0;
  }, [progress]);

  // Get challenge status
  const getChallengeStatus = useCallback((challengeId) => {
    if (!progress?.user_challenge_progress) return 'not_started';
    const challengeProgress = progress.user_challenge_progress.find(p => p.challenge_id === challengeId);
    return challengeProgress?.status || 'not_started';
  }, [progress]);

  // Check if today's daily challenge is completed
  const isTodaysChallengeCompleted = useCallback(() => {
    if (!progress?.user_daily_challenges) return false;
    const today = new Date().toISOString().split('T')[0];
    const todayChallenge = progress.user_daily_challenges.find(c => c.challenge_date === today);
    return todayChallenge?.completed || false;
  }, [progress]);

  // Get current streak
  const getCurrentStreak = useCallback(() => {
    return progress?.current_streak || 0;
  }, [progress]);

  // Get total stats
  const getStats = useCallback(() => {
    return {
      totalCoins: progress?.total_coins || 0,
      totalPoints: progress?.total_points || 0,
      modulesCompleted: progress?.modules_completed || 0,
      challengesCompleted: progress?.challenges_completed || 0,
      currentStreak: progress?.current_streak || 0,
      longestStreak: progress?.longest_streak || 0
    };
  }, [progress]);

  // Load progress when user changes
  useEffect(() => {
    loadProgress();
  }, [loadProgress]);

  return {
    progress,
    loading,
    error,
    loadProgress,
    updateModuleProgress,
    updateChallengeProgress,
    updateDailyChallengeProgress,
    getLearningPathProgress,
    getUserLevel,
    calculatePathProgress,
    isModuleCompleted,
    isChallengeCompleted,
    getModuleProgress,
    getChallengeStatus,
    isTodaysChallengeCompleted,
    getCurrentStreak,
    getStats
  };
};

export default useUserProgress;
