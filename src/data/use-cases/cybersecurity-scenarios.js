/**
 * Comprehensive Cybersecurity Use Cases Database
 * 200+ real-world scenarios for hands-on learning across all cybersecurity domains
 */

export const cybersecurityUseCases = {
  // Windows Event Analysis Use Cases (Event IDs 4624, 4625, 4648, etc.)
  windowsEvents: [
    {
      id: "WE-001",
      title: "Brute Force Attack Detection",
      category: "Authentication",
      eventId: 4625,
      difficulty: "Beginner",
      scenario: "Multiple failed login attempts detected from external IP address",
      logSample: `
Event ID: 4625
Source: Security
Date: 2024-01-15 14:30:22
Computer: WORKSTATION-01
Account Name: administrator
Account Domain: COMPANY
Logon Type: 3
Failure Reason: Unknown user name or bad password
Workstation Name: ATTACKER-PC
Source Network Address: ************
Source Port: 52847`,
      analysisSteps: [
        "Identify the frequency of failed login attempts",
        "Analyze source IP and geolocation",
        "Check for successful logins after failures",
        "Review other systems for similar activity",
        "Implement account lockout if necessary"
      ],
      mitreMapping: "T1110.001 - Password Guessing",
      iocs: ["************", "ATTACKER-PC"],
      remediation: [
        "Enable account lockout policies",
        "Implement IP-based blocking",
        "Deploy MFA for administrative accounts",
        "Monitor for successful logins from same source"
      ]
    },
    {
      id: "WE-002", 
      title: "Successful Logon After Brute Force",
      category: "Authentication",
      eventId: 4624,
      difficulty: "Intermediate",
      scenario: "Successful authentication following multiple failed attempts",
      logSample: `
Event ID: 4624
Source: Security
Date: 2024-01-15 14:35:45
Computer: WORKSTATION-01
Account Name: administrator
Account Domain: COMPANY
Logon Type: 3
Logon Process: NtLmSsp
Authentication Package: NTLM
Workstation Name: ATTACKER-PC
Source Network Address: ************
Logon GUID: {********-0000-0000-0000-************}`,
      analysisSteps: [
        "Correlate with previous failed login attempts",
        "Verify legitimacy of successful logon",
        "Check for privilege escalation activities",
        "Monitor for lateral movement attempts",
        "Review account activity post-compromise"
      ],
      mitreMapping: "T1078.002 - Domain Accounts",
      iocs: ["************", "ATTACKER-PC", "administrator account"],
      remediation: [
        "Immediately reset compromised account password",
        "Review and revoke active sessions",
        "Check for persistence mechanisms",
        "Audit recent administrative activities"
      ]
    },
    {
      id: "WE-003",
      title: "Service Installation Persistence",
      category: "Persistence",
      eventId: 7045,
      difficulty: "Advanced",
      scenario: "Malicious service installed for persistence mechanism",
      logSample: `
Event ID: 7045
Source: System
Date: 2024-01-15 16:22:18
Computer: WORKSTATION-02
Service Name: WindowsDefenderUpdate
Service File Name: C:\\Windows\\System32\\svchost.exe -k netsvcs -p -s WinDefender
Service Type: own process
Service Start Type: auto start
Account: LocalSystem`,
      analysisSteps: [
        "Verify legitimacy of service name and path",
        "Check digital signature of service binary",
        "Review service configuration and dependencies",
        "Analyze parent process that installed service",
        "Search for similar services across network"
      ],
      mitreMapping: "T1543.003 - Windows Service",
      iocs: ["WindowsDefenderUpdate", "WinDefender service"],
      remediation: [
        "Stop and delete malicious service",
        "Remove service binary from system",
        "Check for additional persistence mechanisms",
        "Scan system for malware infections"
      ]
    }
  ],

  // MITRE ATT&CK Framework Scenarios
  mitreAttackScenarios: [
    {
      id: "MA-001",
      title: "Spear Phishing Email Campaign",
      tacticId: "TA0001",
      tactic: "Initial Access",
      techniqueId: "T1566.001",
      technique: "Spearphishing Attachment",
      difficulty: "Intermediate",
      scenario: "Targeted phishing email with malicious Office document attachment",
      detectionMethods: [
        "Email security gateway alerts",
        "Suspicious attachment analysis",
        "User behavior anomalies",
        "Network connection monitoring"
      ],
      indicators: [
        "Email from spoofed domain: secure-bank-update.com",
        "Malicious macro in Excel document",
        "C2 communication to **************",
        "Process hollowing of legitimate Windows binary"
      ],
      timeline: [
        "09:15 - Phishing email received",
        "09:18 - User opens attachment",
        "09:19 - Macro execution begins",
        "09:20 - Payload download initiated",
        "09:22 - C2 beacon established"
      ],
      analysisQuestions: [
        "What makes this email appear legitimate?",
        "How does the malware achieve persistence?",
        "What data exfiltration methods are used?",
        "How can similar attacks be prevented?"
      ]
    },
    {
      id: "MA-002",
      title: "PowerShell Empire Lateral Movement",
      tacticId: "TA0008",
      tactic: "Lateral Movement",
      techniqueId: "T1021.006",
      technique: "Windows Remote Management",
      difficulty: "Advanced",
      scenario: "Adversary uses PowerShell Empire for lateral movement across domain",
      detectionMethods: [
        "PowerShell logging and monitoring",
        "Network traffic analysis",
        "Process creation events",
        "Authentication anomalies"
      ],
      indicators: [
        "Encoded PowerShell commands",
        "WinRM connections to multiple hosts",
        "Memory-only payloads",
        "Credential dumping artifacts"
      ],
      timeline: [
        "14:30 - Initial compromise established",
        "14:32 - Credentials harvested from memory",
        "14:35 - PowerShell Empire agent deployed",
        "14:38 - WinRM connection to DC-01",
        "14:42 - Domain admin privileges obtained"
      ],
      analysisQuestions: [
        "How was credential dumping detected?",
        "What network signatures indicate Empire usage?",
        "How can PowerShell logging improve detection?",
        "What containment actions are most effective?"
      ]
    }
  ],

  // SOC Analyst Scenarios
  socScenarios: [
    {
      id: "SOC-001",
      title: "DDoS Attack Detection and Response",
      severity: "High",
      category: "Availability",
      description: "Large-scale DDoS attack against web services",
      alertTrigger: "Network traffic threshold exceeded",
      initialIndicators: [
        "Inbound traffic increased 1000% over baseline",
        "Web server response times degraded",
        "Multiple source IPs with similar patterns",
        "TCP SYN flood patterns detected"
      ],
      investigationSteps: [
        "Confirm attack pattern and scope",
        "Identify attack vectors and source characteristics",
        "Implement traffic filtering and rate limiting",
        "Coordinate with ISP for upstream filtering",
        "Monitor service availability during mitigation"
      ],
      tools: ["SIEM", "Network monitoring", "Traffic analysis", "Firewall logs"],
      escalationCriteria: [
        "Service outage duration > 30 minutes",
        "Customer impact reports increase",
        "Attack pattern changes significantly",
        "Mitigation efforts prove ineffective"
      ],
      lessons: [
        "Importance of baseline traffic monitoring",
        "Pre-configured DDoS response procedures",
        "Coordination with external partners",
        "Business impact assessment during incidents"
      ]
    },
    {
      id: "SOC-002",
      title: "Insider Threat Data Exfiltration",
      severity: "Critical",
      category: "Data Protection",
      description: "Employee attempting to exfiltrate sensitive company data",
      alertTrigger: "Unusual file access patterns detected",
      initialIndicators: [
        "After-hours access to sensitive file shares",
        "Large volume file downloads",
        "USB device usage anomalies",
        "Email to external personal account"
      ],
      investigationSteps: [
        "Review user's recent access patterns",
        "Analyze file access logs and download volumes",
        "Check for unauthorized data storage devices",
        "Monitor outbound network traffic",
        "Coordinate with HR and legal teams"
      ],
      tools: ["DLP", "File access monitoring", "Email security", "Endpoint detection"],
      escalationCriteria: [
        "Confirmed sensitive data access",
        "Evidence of data transfer attempts",
        "Policy violations confirmed",
        "Legal action consideration required"
      ],
      lessons: [
        "Importance of user behavior monitoring",
        "DLP policy effectiveness review",
        "Insider threat program evaluation",
        "HR and security team coordination"
      ]
    }
  ],

  // Malware Analysis Use Cases
  malwareAnalysis: [
    {
      id: "MAL-001",
      title: "Banking Trojan Static Analysis",
      family: "Emotet",
      type: "Financial Malware",
      difficulty: "Intermediate",
      sample: {
        hash: "sha256:a1b2c3d4e5f6789...",
        size: "2.4 MB",
        type: "PE32 executable"
      },
      analysisPhases: {
        static: [
          "PE header analysis and entropy calculation",
          "Import/export table examination",
          "String extraction and analysis",
          "Digital signature verification",
          "Yara rule matching"
        ],
        dynamic: [
          "Sandbox execution monitoring",
          "Network traffic capture",
          "File system modifications tracking",
          "Registry changes monitoring",
          "Process behavior analysis"
        ],
        behavioral: [
          "API call sequence analysis",
          "Persistence mechanism identification",
          "C2 communication patterns",
          "Data exfiltration methods",
          "Evasion technique detection"
        ]
      },
      findings: [
        "Credential harvesting capabilities",
        "Web browser hook installation",
        "Encrypted C2 communication",
        "Anti-analysis techniques",
        "Banking website targeting"
      ],
      iocs: [
        "Domain: evil-banking.com",
        "IP: **************",
        "Mutex: Global\\EmoteTMutex2020",
        "Registry: HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\\Update",
        "File: %APPDATA%\\Microsoft\\Windows\\update.exe"
      ]
    }
  ],

  // Threat Hunting Scenarios
  threatHunting: [
    {
      id: "TH-001",
      title: "Living Off The Land Hunting",
      hypothesis: "Adversaries are using legitimate Windows tools for malicious purposes",
      huntingQuestions: [
        "Are PowerShell executions following normal patterns?",
        "Is WMI being used for lateral movement?",
        "Are legitimate tools downloading unusual content?",
        "Do process creation chains show suspicious behavior?"
      ],
      dataSourcesRequired: [
        "Windows Event Logs (4688 - Process Creation)",
        "PowerShell operational logs",
        "WMI activity logs",
        "Network connection logs",
        "DNS query logs"
      ],
      huntingTechniques: [
        "Statistical analysis of PowerShell usage",
        "Frequency analysis of rarely used utilities",
        "Parent-child process relationship mapping",
        "Command line argument analysis",
        "Time-based anomaly detection"
      ],
      expectedFindings: [
        "Encoded PowerShell commands",
        "Unusual parent processes for system tools",
        "Command line obfuscation attempts",
        "Tool usage outside normal hours",
        "Suspicious network connections from system tools"
      ],
      tools: ["Splunk/ELK", "Sigma rules", "Custom scripts", "Timeline analysis"],
      validation: [
        "Reproduce suspicious command sequences",
        "Analyze tool behavior in controlled environment",
        "Correlate with known threat intelligence",
        "Interview users about legitimate tool usage"
      ]
    }
  ],

  // Red Team Operation Scenarios
  redTeamOps: [
    {
      id: "RT-001",
      title: "Corporate Network Penetration",
      objective: "Gain domain administrator access and exfiltrate sensitive data",
      scope: "Corporate network with 500+ employees",
      timeline: "2 weeks engagement",
      phases: {
        reconnaissance: [
          "OSINT gathering on company and employees",
          "Social media profiling of key personnel", 
          "DNS enumeration and subdomain discovery",
          "Email address harvesting",
          "Technology stack identification"
        ],
        initialAccess: [
          "Spear phishing campaign targeting IT staff",
          "Credential stuffing against VPN endpoints",
          "Exploitation of public-facing applications",
          "Physical security assessment",
          "Social engineering attempts"
        ],
        execution: [
          "Malware deployment via phishing",
          "Living off the land techniques",
          "PowerShell execution for persistence",
          "WMI abuse for lateral movement",
          "Scheduled task creation"
        ],
        persistence: [
          "Registry run key modification",
          "Service installation", 
          "WMI event subscription",
          "DLL hijacking",
          "Golden ticket creation"
        ],
        privilegeEscalation: [
          "Local privilege escalation exploits",
          "Token impersonation",
          "Service account compromise",
          "Kerberoasting attacks",
          "GPO modification"
        ],
        defensiveEvasion: [
          "Process hollowing techniques",
          "AV evasion through packing",
          "Log clearing and tampering",
          "Timestomping artifacts",
          "Alternative data streams"
        ],
        credentialAccess: [
          "LSASS memory dumping",
          "Registry SAM extraction",
          "DCSync attacks",
          "Credential stuffing",
          "Password spraying"
        ],
        discovery: [
          "Network enumeration",
          "Active Directory reconnaissance", 
          "File share discovery",
          "Service enumeration",
          "Security control identification"
        ],
        lateralMovement: [
          "Pass-the-hash attacks",
          "WinRM exploitation",
          "SMB relay attacks",
          "RDP compromise",
          "DCOM exploitation"
        ],
        collection: [
          "Sensitive file identification",
          "Email archive access",
          "Database enumeration",
          "Screen capture collection",
          "Keylogger deployment"
        ],
        exfiltration: [
          "DNS tunneling for data theft",
          "HTTPS exfiltration",
          "Cloud storage abuse",
          "Email-based data transfer",
          "Physical media usage"
        ]
      },
      successMetrics: [
        "Domain administrator access achieved",
        "Sensitive data successfully exfiltrated",
        "Persistence maintained for engagement duration",
        "Detection evasion throughout operation",
        "Business impact demonstration"
      ]
    }
  ],

  // Blue Team Defense Scenarios
  blueTeamOps: [
    {
      id: "BT-001",
      title: "Advanced Persistent Threat Response",
      threatActor: "APT29 (Cozy Bear)",
      incidentType: "Multi-stage targeted attack",
      severity: "Critical",
      affectedSystems: ["Domain Controllers", "Email Servers", "Financial Systems"],
      attackTimeline: [
        "Day 1: Initial spear phishing email",
        "Day 3: Lateral movement to finance systems",
        "Day 7: Domain admin compromise",
        "Day 10: Data staging for exfiltration",
        "Day 14: Exfiltration attempt detected"
      ],
      detectionMethods: [
        "Anomalous PowerShell activity",
        "Unusual network traffic patterns",
        "Privilege escalation alerts",
        "Suspicious file modifications",
        "C2 communication detection"
      ],
      responseActions: [
        "Immediate network segmentation",
        "Affected system isolation",
        "Forensic image creation",
        "Threat hunting across environment",
        "Credential reset for compromised accounts"
      ],
      analysisFindings: [
        "Custom backdoor deployment",
        "Living off the land techniques",
        "Advanced evasion methods",
        "Targeted data collection",
        "Long-term persistence mechanisms"
      ],
      remediation: [
        "Malware removal and system rebuild",
        "Security control enhancement",
        "User awareness training",
        "Incident response plan updates",
        "Threat intelligence integration"
      ]
    }
  ],

  // Bug Bounty Scenarios
  bugBountyScenarios: [
    {
      id: "BB-001",
      title: "SQL Injection in Web Application",
      application: "E-commerce Platform",
      vulnerability: "SQL Injection",
      severity: "High",
      cvssScore: 8.1,
      affected: "User authentication system",
      scenario: "Union-based SQL injection in login form allows data extraction",
      exploitSteps: [
        "Identify injection point in username field",
        "Test for SQL injection with basic payloads",
        "Determine database type and version",
        "Extract database schema information",
        "Dump user credentials from database"
      ],
      payloads: [
        "admin' OR '1'='1' --",
        "admin' UNION SELECT 1,2,3 --",
        "admin' UNION SELECT table_name,column_name,1 FROM information_schema.columns --",
        "admin' UNION SELECT username,password,email FROM users --"
      ],
      impact: [
        "Complete user database compromise",
        "Customer personal information exposure",
        "Financial data access",
        "Administrative account takeover",
        "Regulatory compliance violations"
      ],
      remediation: [
        "Implement parameterized queries",
        "Input validation and sanitization",
        "Least privilege database access",
        "Web application firewall deployment",
        "Regular security testing"
      ]
    }
  ],

  // Incident Response Playbooks
  incidentResponse: [
    {
      id: "IR-001",
      title: "Ransomware Incident Response",
      incidentType: "Ransomware Attack",
      severity: "Critical",
      playbook: {
        preparation: [
          "Ensure incident response team availability",
          "Verify backup system integrity",
          "Confirm communication channels",
          "Review ransomware response procedures",
          "Validate decryption tool availability"
        ],
        identification: [
          "Confirm ransomware presence and variant",
          "Assess scope of encrypted systems",
          "Identify patient zero and attack vector",
          "Document affected system inventory",
          "Preserve forensic evidence"
        ],
        containment: [
          "Isolate infected systems from network",
          "Prevent lateral spread to other systems",
          "Preserve running memory dumps",
          "Block C2 communication if identified",
          "Secure backup systems from compromise"
        ],
        eradication: [
          "Remove ransomware from affected systems",
          "Patch vulnerabilities used for initial access",
          "Update security controls and signatures",
          "Strengthen access controls",
          "Validate system integrity"
        ],
        recovery: [
          "Restore systems from clean backups",
          "Verify system functionality post-recovery",
          "Monitor for recurring infections",
          "Gradually reconnect to network",
          "Validate business process restoration"
        ],
        lessonsLearned: [
          "Review incident timeline and response effectiveness",
          "Identify process improvement opportunities",
          "Update incident response procedures",
          "Enhance backup and recovery capabilities",
          "Improve user awareness training"
        ]
      }
    }
  ]
};

// Use case difficulty levels
export const difficultyLevels = {
  beginner: {
    color: "#10B981",
    description: "Basic concepts and straightforward analysis",
    prerequisites: "Fundamental cybersecurity knowledge"
  },
  intermediate: {
    color: "#F59E0B", 
    description: "Moderate complexity requiring analytical skills",
    prerequisites: "Understanding of security tools and techniques"
  },
  advanced: {
    color: "#EF4444",
    description: "Complex scenarios requiring expert knowledge",
    prerequisites: "Extensive experience in cybersecurity operations"
  },
  expert: {
    color: "#8B5CF6",
    description: "Real-world complex incidents and advanced techniques",
    prerequisites: "Senior-level expertise and specialized knowledge"
  }
};

// Learning objectives for each use case category
export const learningObjectives = {
  windowsEvents: [
    "Master Windows Event Log analysis techniques",
    "Identify attack patterns in authentication logs",
    "Correlate events across multiple systems",
    "Develop automated detection rules"
  ],
  mitreAttackScenarios: [
    "Map real attacks to MITRE ATT&CK framework",
    "Understand adversary tactics and techniques",
    "Develop detection strategies for each technique",
    "Practice threat hunting methodologies"
  ],
  socScenarios: [
    "Execute SOC analyst responsibilities effectively",
    "Practice incident triage and escalation",
    "Develop communication skills for incident response",
    "Master SIEM and security tool usage"
  ],
  malwareAnalysis: [
    "Perform static and dynamic malware analysis",
    "Identify malware families and capabilities",
    "Extract indicators of compromise",
    "Develop detection signatures and rules"
  ],
  threatHunting: [
    "Develop and test threat hunting hypotheses",
    "Master proactive threat detection techniques",
    "Analyze large datasets for anomalies",
    "Create custom hunting queries and tools"
  ],
  redTeamOps: [
    "Execute sophisticated attack scenarios",
    "Practice adversarial simulation techniques",
    "Understand offensive security methodologies",
    "Develop realistic threat scenarios"
  ],
  blueTeamOps: [
    "Master defensive security operations",
    "Practice incident detection and response",
    "Develop security monitoring capabilities",
    "Implement effective defense strategies"
  ],
  bugBountyScenarios: [
    "Identify and exploit web application vulnerabilities",
    "Practice responsible disclosure processes",
    "Develop vulnerability assessment skills",
    "Master bug bounty hunting methodologies"
  ]
};

export default cybersecurityUseCases; 