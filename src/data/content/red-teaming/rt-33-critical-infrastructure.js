/**
 * Critical Infrastructure Attacks Module
 */

export const criticalInfrastructureContent = {
  id: "rt-33",
  pathId: "red-teaming",
  title: "Critical Infrastructure Attacks",
  description: "Master attacks against critical infrastructure including SCADA/ICS systems, power grids, transportation networks, and industrial control systems.",
  objectives: [
    "Understand critical infrastructure architectures and protocols",
    "Master SCADA and ICS system exploitation techniques",
    "Learn power grid and energy system attack methods",
    "Explore transportation and logistics system attacks",
    "Understand water treatment and utility system security",
    "Master safety system bypass and physical impact attacks"
  ],
  difficulty: "Expert",
  estimatedTime: 380,
  sections: [
    {
      title: "Critical Infrastructure Fundamentals",
      content: `
        <h2>Critical Infrastructure Systems and Architecture</h2>
        <p>Critical infrastructure systems control essential services and require specialized knowledge of industrial protocols, safety systems, and operational technology.</p>
        
        <h3>Critical Infrastructure Sectors</h3>
        <ul>
          <li><strong>Energy and Power Systems:</strong>
            <ul>
              <li>Electrical power generation and distribution</li>
              <li>Oil and gas production and refining</li>
              <li>Nuclear power and radioactive materials</li>
              <li>Renewable energy and smart grid systems</li>
              <li>Energy storage and battery systems</li>
            </ul>
          </li>
          <li><strong>Transportation Networks:</strong>
            <ul>
              <li>Aviation and air traffic control systems</li>
              <li>Railway and mass transit systems</li>
              <li>Maritime and port management systems</li>
              <li>Highway and traffic management systems</li>
              <li>Pipeline and freight transportation</li>
            </ul>
          </li>
          <li><strong>Water and Wastewater Systems:</strong>
            <ul>
              <li>Water treatment and purification plants</li>
              <li>Wastewater treatment and sewage systems</li>
              <li>Water distribution and storage systems</li>
              <li>Dam and reservoir control systems</li>
              <li>Irrigation and agricultural water systems</li>
            </ul>
          </li>
        </ul>
        
        <h3>Industrial Control System Architecture</h3>
        <ul>
          <li><strong>SCADA (Supervisory Control and Data Acquisition):</strong>
            <ul>
              <li>Master Terminal Units (MTUs) and control centers</li>
              <li>Remote Terminal Units (RTUs) and field devices</li>
              <li>Communication networks and protocols</li>
              <li>Human Machine Interfaces (HMIs)</li>
              <li>Historian and data logging systems</li>
            </ul>
          </li>
          <li><strong>Distributed Control Systems (DCS):</strong>
            <ul>
              <li>Process control and automation systems</li>
              <li>Field controllers and I/O modules</li>
              <li>Engineering workstations and configuration tools</li>
              <li>Operator interfaces and control rooms</li>
              <li>Safety and emergency shutdown systems</li>
            </xs>
          </li>
          <li><strong>Programmable Logic Controllers (PLCs):</strong>
            <ul>
              <li>Industrial automation and control logic</li>
              <li>Input/output modules and field devices</li>
              <li>Programming software and development environments</li>
              <li>Communication interfaces and networking</li>
              <li>Safety and interlock systems</li>
            </xs>
          </li>
        </ul>
        
        <h3>Industrial Communication Protocols</h3>
        <ul>
          <li><strong>Legacy Serial Protocols:</strong>
            <ul>
              <li>Modbus RTU and ASCII protocols</li>
              <li>DNP3 (Distributed Network Protocol)</li>
              <li>IEC 60870-5 series protocols</li>
              <li>Profibus and DeviceNet</li>
              <li>Hart and Foundation Fieldbus</li>
            </xs>
          </li>
          <li><strong>Ethernet-Based Protocols:</strong>
            <ul>
              <li>Modbus TCP and Ethernet/IP</li>
              <li>Profinet and EtherCAT</li>
              <li>IEC 61850 for power systems</li>
              <li>OPC and OPC UA</li>
              <li>BACnet for building automation</li>
            </xs>
          </li>
          <li><strong>Wireless and IoT Protocols:</strong>
            <ul>
              <li>Wireless sensor networks and mesh protocols</li>
              <li>Cellular and satellite communication</li>
              <li>LoRa and NB-IoT for remote monitoring</li>
              <li>Zigbee and Z-Wave for automation</li>
              <li>Bluetooth and WiFi for local connectivity</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "SCADA and ICS System Exploitation",
      content: `
        <h2>Industrial Control System Attack Techniques</h2>
        <p>SCADA and ICS systems present unique attack surfaces due to their operational requirements, legacy protocols, and safety-critical functions.</p>
        
        <h3>ICS Network Reconnaissance</h3>
        <ul>
          <li><strong>Network Discovery and Enumeration:</strong>
            <ul>
              <li>Industrial protocol scanning and identification</li>
              <li>Device fingerprinting and vendor identification</li>
              <li>Network topology mapping and segmentation analysis</li>
              <li>Service and port enumeration</li>
              <li>Wireless and remote access point discovery</li>
            </xs>
          </li>
          <li><strong>Protocol Analysis and Reverse Engineering:</strong>
            <ul>
              <li>Protocol capture and traffic analysis</li>
              <li>Command and function code identification</li>
              <li>Data structure and format analysis</li>
              <li>Authentication and security mechanism assessment</li>
              <li>Custom protocol reverse engineering</li>
            </xs>
          </li>
          <li><strong>Device and System Identification:</strong>
            <ul>
              <li>PLC and controller model identification</li>
              <li>Firmware version and patch level assessment</li>
              <li>Configuration and programming interface discovery</li>
              <li>Safety system and interlock identification</li>
              <li>Critical process and control loop mapping</li>
            </xs>
          </li>
        </ul>
        
        <h3>Protocol-Specific Attack Techniques</h3>
        <ul>
          <li><strong>Modbus Exploitation:</strong>
            <ul>
              <li>Function code abuse and unauthorized commands</li>
              <li>Coil and register manipulation</li>
              <li>Device identification and enumeration</li>
              <li>Man-in-the-middle attacks and traffic manipulation</li>
              <li>Denial of service and device disruption</li>
            </xs>
          </li>
          <li><strong>DNP3 Attacks:</strong>
            <ul>
              <li>Unsolicited response manipulation</li>
              <li>Authentication bypass and replay attacks</li>
              <li>Data object manipulation and falsification</li>
              <li>Time synchronization attacks</li>
              <li>Secure authentication exploitation</li>
            </xs>
          </li>
          <li><strong>IEC 61850 Exploitation:</strong>
            <ul>
              <li>GOOSE and SMV message manipulation</li>
              <li>Logical node and data object access</li>
              <li>MMS (Manufacturing Message Specification) attacks</li>
              <li>Configuration and setting modification</li>
              <li>Protection and control system interference</li>
            </xs>
          </li>
        </ul>
        
        <h3>PLC and Controller Attacks</h3>
        <ul>
          <li><strong>Programming and Configuration Attacks:</strong>
            <ul>
              <li>Ladder logic and function block modification</li>
              <li>Unauthorized program upload and download</li>
              <li>Configuration parameter manipulation</li>
              <li>Memory and register direct access</li>
              <li>Firmware modification and replacement</li>
            </xs>
          </li>
          <li><strong>Runtime Manipulation:</strong>
            <ul>
              <li>Real-time data manipulation and falsification</li>
              <li>Control logic bypass and override</li>
              <li>Safety interlock and alarm suppression</li>
              <li>Process variable and setpoint modification</li>
              <li>Actuator and output device control</li>
            </xs>
          </li>
          <li><strong>Persistence and Backdoor Installation:</strong>
            <ul>
              <li>Malicious logic injection and hiding</li>
              <li>Firmware rootkit and implant installation</li>
              <li>Covert communication channel establishment</li>
              <li>Remote access and control capability</li>
              <li>Anti-forensics and detection evasion</li>
            </xs>
          </li>
        </ul>
        
        <h3>HMI and Engineering Workstation Attacks</h3>
        <ul>
          <li><strong>HMI Compromise and Manipulation:</strong>
            <ul>
              <li>Operator interface falsification and spoofing</li>
              <li>Alarm and event suppression</li>
              <li>Historical data manipulation and deletion</li>
              <li>Trend and report falsification</li>
              <li>User authentication and authorization bypass</li>
            </xs>
          </li>
          <li><strong>Engineering Workstation Exploitation:</strong>
            <ul>
              <li>Development environment compromise</li>
              <li>Project file and configuration theft</li>
              <li>Malicious code injection during development</li>
              <li>Version control and backup manipulation</li>
              <li>Supply chain and software distribution attacks</li>
            </xs>
          </li>
          <li><strong>Historian and Data System Attacks:</strong>
            <ul>
              <li>Historical data manipulation and falsification</li>
              <li>Database and archive system compromise</li>
              <li>Reporting and analytics system manipulation</li>
              <li>Data exfiltration and intellectual property theft</li>
              <li>Compliance and audit trail manipulation</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Physical Impact and Safety System Attacks",
      content: `
        <h2>Attacks with Physical World Consequences</h2>
        <p>Critical infrastructure attacks can have severe physical consequences, requiring understanding of safety systems, process control, and potential impact scenarios.</p>
        
        <h3>Power Grid and Energy System Attacks</h3>
        <ul>
          <li><strong>Generation System Attacks:</strong>
            <ul>
              <li>Power plant control system manipulation</li>
              <li>Turbine and generator control interference</li>
              <li>Fuel system and combustion control attacks</li>
              <li>Cooling and safety system manipulation</li>
              <li>Emergency shutdown and protection system bypass</li>
            </xs>
          </li>
          <li><strong>Transmission and Distribution Attacks:</strong>
            <ul>
              <li>Substation automation system compromise</li>
              <li>Protective relay and circuit breaker manipulation</li>
              <li>Load dispatch and energy management attacks</li>
              <li>Smart grid and advanced metering attacks</li>
              <li>Demand response and load control manipulation</li>
            </xs>
          </li>
          <li><strong>Grid Stability and Blackout Scenarios:</strong>
            <ul>
              <li>Frequency and voltage regulation attacks</li>
              <li>Load shedding and islanding manipulation</li>
              <li>Cascading failure and blackout induction</li>
              <li>Market manipulation and economic attacks</li>
              <li>Renewable energy and storage system attacks</li>
            </xs>
          </li>
        </ul>
        
        <h3>Transportation System Attacks</h3>
        <ul>
          <li><strong>Aviation and Air Traffic Control:</strong>
            <ul>
              <li>Air traffic management system compromise</li>
              <li>Aircraft navigation and communication attacks</li>
              <li>Airport ground control and baggage systems</li>
              <li>Weather and radar system manipulation</li>
              <li>Flight planning and scheduling system attacks</li>
            </xs>
          </li>
          <li><strong>Railway and Mass Transit:</strong>
            <ul>
              <li>Train control and signaling system attacks</li>
              <li>Positive train control (PTC) manipulation</li>
              <li>Station and platform control systems</li>
              <li>Ticketing and passenger information systems</li>
              <li>Maintenance and inspection system compromise</li>
            </xs>
          </li>
          <li><strong>Maritime and Port Systems:</strong>
            <ul>
              <li>Port management and cargo handling systems</li>
              <li>Vessel traffic management and navigation</li>
              <li>Container tracking and logistics systems</li>
              <li>Ballast and fuel management systems</li>
              <li>Security and access control systems</li>
            </xs>
          </li>
        </ul>
        
        <h3>Water and Wastewater System Attacks</h3>
        <ul>
          <li><strong>Water Treatment Plant Attacks:</strong>
            <ul>
              <li>Chemical dosing and treatment process manipulation</li>
              <li>Filtration and purification system interference</li>
              <li>Quality monitoring and testing system attacks</li>
              <li>Pump and valve control system manipulation</li>
              <li>Disinfection and sterilization system bypass</li>
            </xs>
          </li>
          <li><strong>Distribution System Attacks:</strong>
            <ul>
              <li>Pressure and flow control manipulation</li>
              <li>Valve and pump station control attacks</li>
              <li>Storage tank and reservoir level manipulation</li>
              <li>Quality monitoring and alarm suppression</li>
              <li>Leak detection and response system interference</li>
            </xs>
          </li>
          <li><strong>Wastewater Treatment Attacks:</strong>
            <ul>
              <li>Biological treatment process manipulation</li>
              <li>Chemical treatment and disinfection attacks</li>
              <li>Sludge handling and disposal system interference</li>
              <li>Effluent quality and discharge control attacks</li>
              <li>Environmental monitoring and compliance bypass</li>
            </xs>
          </li>
        </ul>
        
        <h3>Safety System Bypass and Override</h3>
        <ul>
          <li><strong>Safety Instrumented Systems (SIS):</strong>
            <ul>
              <li>Safety logic solver and controller attacks</li>
              <li>Safety sensor and transmitter manipulation</li>
              <li>Final element and actuator interference</li>
              <li>Safety integrity level (SIL) degradation</li>
              <li>Proof testing and maintenance bypass</li>
            </xs>
          </li>
          <li><strong>Emergency Shutdown Systems:</strong>
            <ul>
              <li>Emergency stop and shutdown logic bypass</li>
              <li>Fire and gas detection system manipulation</li>
              <li>Pressure relief and safety valve interference</li>
              <li>Alarm and notification system suppression</li>
              <li>Emergency response and evacuation system attacks</li>
            </xs>
          </li>
          <li><strong>Physical Protection and Containment:</strong>
            <ul>
              <li>Containment and barrier system manipulation</li>
              <li>Ventilation and air handling system attacks</li>
              <li>Temperature and pressure monitoring bypass</li>
              <li>Radiation and chemical detection interference</li>
              <li>Physical security and access control compromise</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Critical Infrastructure Attack Simulation Lab",
    description: "Hands-on exercise in attacking critical infrastructure systems including SCADA, ICS, and industrial protocols.",
    tasks: [
      {
        category: "ICS Protocol Exploitation",
        commands: [
          {
            command: "Exploit Modbus TCP protocol vulnerabilities",
            description: "Perform unauthorized read/write operations on Modbus devices",
            hint: "Use Modbus function codes to manipulate coils and registers",
            expectedOutput: "Successful unauthorized control of Modbus-connected devices"
          },
          {
            command: "Analyze and attack DNP3 communications",
            description: "Intercept and manipulate DNP3 protocol messages",
            hint: "Focus on unsolicited responses and data object manipulation",
            expectedOutput: "Successful DNP3 protocol manipulation and control"
          }
        ]
      },
      {
        category: "SCADA System Compromise",
        commands: [
          {
            command: "Compromise HMI and falsify operator displays",
            description: "Manipulate HMI to show false process information",
            hint: "Target alarm suppression and historical data manipulation",
            expectedOutput: "Successful HMI compromise with false information display"
          },
          {
            command: "Implement PLC logic manipulation attack",
            description: "Modify PLC ladder logic to alter process control",
            hint: "Focus on safety interlock bypass and control logic modification",
            expectedOutput: "Successful PLC logic modification with process impact"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary difference between IT and OT (Operational Technology) security?",
      options: [
        "OT uses newer protocols",
        "IT focuses on confidentiality while OT prioritizes availability and safety",
        "OT has better security controls",
        "IT systems are more critical"
      ],
      correct: 1,
      explanation: "IT focuses on confidentiality while OT prioritizes availability and safety because OT systems control physical processes where downtime or safety failures can have severe real-world consequences."
    },
    {
      question: "Which attack technique is most dangerous in critical infrastructure environments?",
      options: [
        "Data theft",
        "Website defacement",
        "Safety system bypass leading to physical damage",
        "Email compromise"
      ],
      correct: 2,
      explanation: "Safety system bypass leading to physical damage is most dangerous because it can result in loss of life, environmental damage, and catastrophic infrastructure failure with long-lasting consequences."
    },
    {
      question: "What makes Modbus protocol particularly vulnerable to attacks?",
      options: [
        "Complex encryption",
        "Lack of authentication and authorization mechanisms",
        "High bandwidth requirements",
        "Frequent updates"
      ],
      correct: 1,
      explanation: "Modbus protocol lacks authentication and authorization mechanisms, making it vulnerable because any device that can communicate with a Modbus device can potentially read from or write to it without verification."
    }
  ]
};
