/**
 * Red Team Execution Module
 */

export const executionContent = {
  id: "rt-execution",
  title: "Execution Techniques",
  description: "Learn execution techniques for red team operations and payload delivery.",
  difficulty: "Advanced",
  estimatedTime: 90,
  objectives: [
    "Understand execution methods",
    "Learn payload delivery techniques",
    "Master code execution strategies",
    "Develop evasion capabilities"
  ],
  sections: [
    {
      title: "Execution Fundamentals",
      content: `
        <h2>Execution Techniques</h2>
        <p>Learn how to execute code and payloads on target systems during red team operations.</p>
        <h3>Execution Methods</h3>
        <ul>
          <li>PowerShell execution</li>
          <li>WMI execution</li>
          <li>Scheduled task execution</li>
          <li>Service execution</li>
        </ul>
      `,
      type: "text"
    }
  ]
}; 