/**
 * Web Application Exploitation Module
 */

export const webApplicationExploitationContent = {
  id: "rt-15",
  pathId: "red-teaming",
  title: "Web Application Exploitation",
  description: "Master advanced web application security testing and exploitation techniques including injection attacks, authentication bypasses, and modern web vulnerabilities.",
  objectives: [
    "Understand modern web application architecture and security",
    "Master injection attack techniques and exploitation",
    "Learn authentication and session management attacks",
    "Explore client-side and browser-based vulnerabilities",
    "Understand API security testing and exploitation",
    "Master advanced web application attack techniques"
  ],
  difficulty: "Advanced",
  estimatedTime: 280,
  sections: [
    {
      title: "Modern Web Application Security",
      content: `
        <h2>Modern Web Application Architecture</h2>
        <p>Understanding modern web application architecture is essential for effective security testing and exploitation in today's complex web environments.</p>
        
        <h3>Web Application Components</h3>
        <ul>
          <li><strong>Frontend Technologies:</strong>
            <ul>
              <li>Single Page Applications (SPAs)</li>
              <li>JavaScript frameworks (React, Angular, Vue)</li>
              <li>Progressive Web Apps (PWAs)</li>
              <li>WebAssembly and client-side processing</li>
              <li>Mobile and hybrid applications</li>
            </ul>
          </li>
          <li><strong>Backend Architecture:</strong>
            <ul>
              <li>Microservices and API-first design</li>
              <li>Serverless and Function-as-a-Service</li>
              <li>Container-based deployments</li>
              <li>Cloud-native applications</li>
              <li>Event-driven architectures</li>
            </ul>
          </li>
          <li><strong>Data Layer:</strong>
            <ul>
              <li>SQL and NoSQL databases</li>
              <li>In-memory data stores</li>
              <li>Message queues and streaming</li>
              <li>Distributed storage systems</li>
              <li>Blockchain and decentralized storage</li>
            </ul>
          </li>
        </ul>
        
        <h3>Web Security Standards and Controls</h3>
        <ul>
          <li><strong>HTTP Security Headers:</strong>
            <ul>
              <li>Content Security Policy (CSP)</li>
              <li>HTTP Strict Transport Security (HSTS)</li>
              <li>X-Frame-Options and frame ancestors</li>
              <li>X-Content-Type-Options</li>
              <li>Referrer-Policy and Feature-Policy</li>
            </ul>
          </li>
          <li><strong>Authentication and Authorization:</strong>
            <ul>
              <li>OAuth 2.0 and OpenID Connect</li>
              <li>JSON Web Tokens (JWT)</li>
              <li>SAML and federated identity</li>
              <li>Multi-factor authentication (MFA)</li>
              <li>Biometric and passwordless authentication</li>
            </ul>
          </li>
          <li><strong>Input Validation and Sanitization:</strong>
            <ul>
              <li>Server-side validation frameworks</li>
              <li>Client-side validation bypass</li>
              <li>Content type validation</li>
              <li>File upload restrictions</li>
              <li>Rate limiting and throttling</li>
            </ul>
          </li>
        </ul>
        
        <h3>OWASP Top 10 and Beyond</h3>
        <ul>
          <li><strong>OWASP Top 10 2021:</strong>
            <ul>
              <li>A01 - Broken Access Control</li>
              <li>A02 - Cryptographic Failures</li>
              <li>A03 - Injection</li>
              <li>A04 - Insecure Design</li>
              <li>A05 - Security Misconfiguration</li>
              <li>A06 - Vulnerable and Outdated Components</li>
              <li>A07 - Identification and Authentication Failures</li>
              <li>A08 - Software and Data Integrity Failures</li>
              <li>A09 - Security Logging and Monitoring Failures</li>
              <li>A10 - Server-Side Request Forgery (SSRF)</li>
            </ul>
          </li>
          <li><strong>Emerging Vulnerabilities:</strong>
            <ul>
              <li>GraphQL injection and abuse</li>
              <li>WebSocket security issues</li>
              <li>Container and orchestration attacks</li>
              <li>Supply chain and dependency attacks</li>
              <li>AI/ML model poisoning and evasion</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced Injection Attacks",
      content: `
        <h2>Advanced Injection Attack Techniques</h2>
        <p>Injection attacks remain among the most critical web application vulnerabilities, requiring sophisticated techniques for modern applications.</p>
        
        <h3>SQL Injection Advanced Techniques</h3>
        <ul>
          <li><strong>Blind SQL Injection:</strong>
            <ul>
              <li>Boolean-based blind injection</li>
              <li>Time-based blind injection</li>
              <li>Error-based information extraction</li>
              <li>Out-of-band data exfiltration</li>
              <li>DNS and HTTP-based exfiltration</li>
            </ul>
          </li>
          <li><strong>Database-Specific Techniques:</strong>
            <ul>
              <li>MySQL advanced functions and features</li>
              <li>PostgreSQL array and JSON manipulation</li>
              <li>MSSQL xp_cmdshell and CLR integration</li>
              <li>Oracle PL/SQL and Java integration</li>
              <li>SQLite and embedded database attacks</li>
            </ul>
          </li>
          <li><strong>WAF and Filter Bypass:</strong>
            <ul>
              <li>Encoding and obfuscation techniques</li>
              <li>Comment and whitespace manipulation</li>
              <li>Alternative syntax and functions</li>
              <li>HTTP parameter pollution</li>
              <li>Content-Type and charset bypass</li>
            </ul>
          </li>
        </ul>
        
        <h3>NoSQL Injection Attacks</h3>
        <ul>
          <li><strong>MongoDB Injection:</strong>
            <ul>
              <li>JavaScript injection in queries</li>
              <li>Operator injection ($where, $regex)</li>
              <li>Authentication bypass techniques</li>
              <li>Data extraction and enumeration</li>
              <li>Aggregation pipeline abuse</li>
            </ul>
          </li>
          <li><strong>Other NoSQL Databases:</strong>
            <ul>
              <li>CouchDB and view injection</li>
              <li>Redis command injection</li>
              <li>Cassandra CQL injection</li>
              <li>ElasticSearch query injection</li>
              <li>Graph database injection (Neo4j)</li>
            </ul>
          </li>
        </ul>
        
        <h3>Command and Code Injection</h3>
        <ul>
          <li><strong>OS Command Injection:</strong>
            <ul>
              <li>Command chaining and piping</li>
              <li>Environment variable manipulation</li>
              <li>Path traversal and file inclusion</li>
              <li>Shell metacharacter abuse</li>
              <li>Blind command injection techniques</li>
            </ul>
          </li>
          <li><strong>Code Injection Techniques:</strong>
            <ul>
              <li>PHP code injection and eval() abuse</li>
              <li>Python pickle and exec() injection</li>
              <li>JavaScript injection and eval()</li>
              <li>Template injection (SSTI)</li>
              <li>Expression language injection</li>
            </ul>
          </li>
          <li><strong>Server-Side Template Injection:</strong>
            <ul>
              <li>Jinja2 and Django template injection</li>
              <li>Twig and Smarty template abuse</li>
              <li>Freemarker and Velocity injection</li>
              <li>Handlebars and Mustache attacks</li>
              <li>Custom template engine exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>XML and Serialization Attacks</h3>
        <ul>
          <li><strong>XML External Entity (XXE):</strong>
            <ul>
              <li>External entity file disclosure</li>
              <li>SSRF via XXE attacks</li>
              <li>Blind XXE and out-of-band techniques</li>
              <li>XXE in different file formats</li>
              <li>XXE prevention bypass</li>
            </ul>
          </li>
          <li><strong>Deserialization Attacks:</strong>
            <ul>
              <li>Java deserialization gadget chains</li>
              <li>Python pickle exploitation</li>
              <li>.NET deserialization attacks</li>
              <li>PHP object injection</li>
              <li>Custom serialization format abuse</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Authentication and Session Attacks",
      content: `
        <h2>Authentication and Session Management Attacks</h2>
        <p>Modern authentication systems present complex attack surfaces requiring sophisticated exploitation techniques.</p>
        
        <h3>Authentication Bypass Techniques</h3>
        <ul>
          <li><strong>Logic Flaw Exploitation:</strong>
            <ul>
              <li>Multi-step authentication bypass</li>
              <li>Race condition exploitation</li>
              <li>State manipulation attacks</li>
              <li>Parameter pollution and confusion</li>
              <li>Business logic circumvention</li>
            </ul>
          </li>
          <li><strong>Credential-based Attacks:</strong>
            <ul>
              <li>Password spraying and credential stuffing</li>
              <li>Default and weak credential exploitation</li>
              <li>Password reset vulnerabilities</li>
              <li>Account enumeration techniques</li>
              <li>Timing attack exploitation</li>
            </ul>
          </li>
          <li><strong>Multi-Factor Authentication Bypass:</strong>
            <ul>
              <li>SMS and email interception</li>
              <li>TOTP synchronization attacks</li>
              <li>Backup code enumeration</li>
              <li>Push notification manipulation</li>
              <li>Biometric spoofing techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Session Management Attacks</h3>
        <ul>
          <li><strong>Session Token Attacks:</strong>
            <ul>
              <li>Session fixation and hijacking</li>
              <li>Session prediction and brute force</li>
              <li>Cross-site session transfer</li>
              <li>Session token in URL exploitation</li>
              <li>Concurrent session abuse</li>
            </ul>
          </li>
          <li><strong>JWT (JSON Web Token) Attacks:</strong>
            <ul>
              <li>Algorithm confusion attacks</li>
              <li>Key confusion and substitution</li>
              <li>JWT header manipulation</li>
              <li>Weak secret key exploitation</li>
              <li>JWT injection and claim manipulation</li>
            </ul>
          </li>
          <li><strong>Cookie Security Bypass:</strong>
            <ul>
              <li>HttpOnly and Secure flag bypass</li>
              <li>SameSite attribute manipulation</li>
              <li>Cookie injection and pollution</li>
              <li>Domain and path confusion</li>
              <li>Cookie tossing attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>OAuth and SSO Attacks</h3>
        <ul>
          <li><strong>OAuth 2.0 Vulnerabilities:</strong>
            <ul>
              <li>Authorization code interception</li>
              <li>Redirect URI manipulation</li>
              <li>State parameter bypass</li>
              <li>Scope elevation attacks</li>
              <li>Token substitution and confusion</li>
            </ul>
          </li>
          <li><strong>SAML Attacks:</strong>
            <ul>
              <li>SAML assertion manipulation</li>
              <li>XML signature wrapping</li>
              <li>Replay attack exploitation</li>
              <li>Identity provider spoofing</li>
              <li>Assertion injection attacks</li>
            </ul>
          </li>
          <li><strong>OpenID Connect Exploitation:</strong>
            <ul>
              <li>ID token manipulation</li>
              <li>Nonce and state bypass</li>
              <li>Discovery document poisoning</li>
              <li>Userinfo endpoint abuse</li>
              <li>Dynamic client registration abuse</li>
            </ul>
          </li>
        </ul>
        
        <h3>API Security Testing</h3>
        <ul>
          <li><strong>REST API Attacks:</strong>
            <ul>
              <li>HTTP method manipulation</li>
              <li>Parameter pollution and confusion</li>
              <li>Rate limiting bypass</li>
              <li>API versioning exploitation</li>
              <li>Mass assignment vulnerabilities</li>
            </ul>
          </li>
          <li><strong>GraphQL Security Testing:</strong>
            <ul>
              <li>Query complexity and depth attacks</li>
              <li>Introspection and schema discovery</li>
              <li>Injection in GraphQL queries</li>
              <li>Authorization bypass in resolvers</li>
              <li>Subscription and real-time abuse</li>
            </ul>
          </li>
          <li><strong>API Gateway and Microservice Attacks:</strong>
            <ul>
              <li>Service mesh security bypass</li>
              <li>Inter-service communication abuse</li>
              <li>API gateway configuration flaws</li>
              <li>Service discovery exploitation</li>
              <li>Container and orchestration attacks</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Advanced Web Application Exploitation Lab",
    description: "Hands-on exercise in advanced web application security testing including injection attacks, authentication bypasses, and API exploitation.",
    tasks: [
      {
        category: "SQL Injection",
        commands: [
          {
            command: "Exploit blind SQL injection vulnerability",
            description: "Use time-based blind SQL injection for data extraction",
            hint: "Use conditional statements and time delays for information extraction",
            expectedOutput: "Successful data extraction through blind SQL injection"
          },
          {
            command: "Bypass WAF protection",
            description: "Circumvent web application firewall using encoding techniques",
            hint: "Try various encoding methods and alternative syntax",
            expectedOutput: "Successful WAF bypass and SQL injection execution"
          }
        ]
      },
      {
        category: "Authentication Bypass",
        commands: [
          {
            command: "Exploit JWT vulnerabilities",
            description: "Manipulate JWT tokens for authentication bypass",
            hint: "Try algorithm confusion and key substitution attacks",
            expectedOutput: "Successful authentication bypass via JWT manipulation"
          },
          {
            command: "Test OAuth implementation flaws",
            description: "Identify and exploit OAuth 2.0 vulnerabilities",
            hint: "Focus on redirect URI manipulation and state bypass",
            expectedOutput: "OAuth flow compromise and unauthorized access"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which technique is most effective for bypassing modern web application firewalls (WAFs)?",
      options: [
        "Using only uppercase characters",
        "Encoding and obfuscation techniques",
        "Sending requests very slowly",
        "Using only GET requests"
      ],
      correct: 1,
      explanation: "Encoding and obfuscation techniques are most effective for WAF bypass because they can disguise malicious payloads while maintaining their functionality, exploiting parsing differences between WAF and application."
    },
    {
      question: "What is the primary security risk of JWT algorithm confusion attacks?",
      options: [
        "Token expiration bypass",
        "Signature verification bypass",
        "Token replay attacks",
        "Session fixation"
      ],
      correct: 1,
      explanation: "Algorithm confusion attacks allow signature verification bypass by changing the algorithm from asymmetric (RS256) to symmetric (HS256), using the public key as the HMAC secret to forge valid tokens."
    },
    {
      question: "Which GraphQL vulnerability is most commonly exploited for denial of service?",
      options: [
        "Query injection",
        "Schema introspection",
        "Query complexity and depth attacks",
        "Subscription abuse"
      ],
      correct: 2,
      explanation: "Query complexity and depth attacks are most commonly used for DoS because they can create exponentially complex queries that consume excessive server resources, leading to performance degradation or service unavailability."
    }
  ]
};
