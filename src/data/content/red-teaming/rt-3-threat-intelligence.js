/**
 * Threat Intelligence for Red Teams Module
 */

export const threatIntelligenceRedTeamContent = {
  id: "rt-3",
  pathId: "red-teaming",
  title: "Threat Intelligence for Red Teams",
  description: "Leverage threat intelligence to enhance red team operations through intelligence-driven adversary simulation and realistic attack scenarios.",
  objectives: [
    "Understand threat intelligence fundamentals for red teams",
    "Learn OSINT collection techniques and tools",
    "Master threat actor profiling and TTP analysis",
    "Explore intelligence-driven red team operations",
    "Understand threat modeling and attack simulation",
    "Learn to create realistic adversary personas"
  ],
  difficulty: "Intermediate",
  estimatedTime: 180,
  sections: [
    {
      title: "Threat Intelligence Fundamentals",
      content: `
        <h2>Threat Intelligence Fundamentals for Red Teams</h2>
        <p>Threat intelligence provides the foundation for realistic and effective red team operations by informing attack scenarios with real-world adversary behavior.</p>
        
        <h3>Types of Threat Intelligence</h3>
        <ul>
          <li><strong>Strategic Intelligence:</strong>
            <ul>
              <li>High-level threat landscape analysis</li>
              <li>Industry-specific threat trends</li>
              <li>Geopolitical threat factors</li>
              <li>Long-term threat predictions</li>
            </ul>
          </li>
          <li><strong>Tactical Intelligence:</strong>
            <ul>
              <li>Specific TTPs and attack methods</li>
              <li>Tool and technique analysis</li>
              <li>Campaign-specific information</li>
              <li>Adversary capability assessment</li>
            </ul>
          </li>
          <li><strong>Operational Intelligence:</strong>
            <ul>
              <li>Ongoing campaign tracking</li>
              <li>Real-time threat indicators</li>
              <li>Attack attribution data</li>
              <li>Infrastructure analysis</li>
            </ul>
          </li>
          <li><strong>Technical Intelligence:</strong>
            <ul>
              <li>Indicators of Compromise (IoCs)</li>
              <li>Malware analysis results</li>
              <li>Network signatures</li>
              <li>Vulnerability information</li>
            </ul>
          </li>
        </ul>
        
        <h3>Intelligence Sources for Red Teams</h3>
        <ul>
          <li><strong>Open Source Intelligence (OSINT):</strong>
            <ul>
              <li>Public threat reports and research</li>
              <li>Social media and forums</li>
              <li>Government advisories</li>
              <li>Academic research papers</li>
            </ul>
          </li>
          <li><strong>Commercial Intelligence:</strong>
            <ul>
              <li>Threat intelligence platforms</li>
              <li>Vendor-specific reports</li>
              <li>Industry sharing groups</li>
              <li>Subscription-based feeds</li>
            </ul>
          </li>
          <li><strong>Internal Intelligence:</strong>
            <ul>
              <li>Previous incident data</li>
              <li>Security monitoring logs</li>
              <li>Vulnerability assessments</li>
              <li>Penetration test results</li>
            </ul>
          </li>
        </ul>
        
        <h3>Intelligence-Driven Red Team Benefits</h3>
        <ul>
          <li><strong>Realistic Scenarios:</strong> Based on actual threat actor behavior</li>
          <li><strong>Targeted Testing:</strong> Focus on relevant threats to the organization</li>
          <li><strong>Improved Detection:</strong> Test against known adversary TTPs</li>
          <li><strong>Better Preparation:</strong> Prepare defenses for likely attack vectors</li>
          <li><strong>Enhanced Training:</strong> Train blue teams against realistic threats</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "OSINT Collection for Red Teams",
      content: `
        <h2>OSINT Collection for Red Teams</h2>
        <p>Open Source Intelligence (OSINT) collection provides valuable information for red team operations, enabling realistic target profiling and attack planning.</p>
        
        <h3>OSINT Collection Framework</h3>
        <ul>
          <li><strong>Planning and Direction:</strong>
            <ul>
              <li>Define intelligence requirements</li>
              <li>Identify information gaps</li>
              <li>Establish collection priorities</li>
              <li>Set operational boundaries</li>
            </ul>
          </li>
          <li><strong>Collection:</strong>
            <ul>
              <li>Automated data gathering</li>
              <li>Manual research and analysis</li>
              <li>Social media monitoring</li>
              <li>Technical reconnaissance</li>
            </ul>
          </li>
          <li><strong>Processing:</strong>
            <ul>
              <li>Data normalization and cleaning</li>
              <li>Information correlation</li>
              <li>Pattern identification</li>
              <li>Relevance assessment</li>
            </ul>
          </li>
          <li><strong>Analysis:</strong>
            <ul>
              <li>Threat actor attribution</li>
              <li>TTP extraction and mapping</li>
              <li>Campaign reconstruction</li>
              <li>Capability assessment</li>
            </ul>
          </li>
          <li><strong>Dissemination:</strong>
            <ul>
              <li>Intelligence product creation</li>
              <li>Stakeholder communication</li>
              <li>Actionable recommendations</li>
              <li>Feedback collection</li>
            </ul>
          </li>
        </ul>
        
        <h3>OSINT Tools and Techniques</h3>
        <ul>
          <li><strong>Search Engines and Databases:</strong>
            <ul>
              <li>Google dorking and advanced search</li>
              <li>Shodan for internet-connected devices</li>
              <li>Censys for internet scanning data</li>
              <li>Have I Been Pwned for breach data</li>
            </ul>
          </li>
          <li><strong>Social Media Intelligence:</strong>
            <ul>
              <li>LinkedIn for organizational structure</li>
              <li>Twitter for real-time information</li>
              <li>Facebook for personal information</li>
              <li>GitHub for code and configuration data</li>
            </ul>
          </li>
          <li><strong>Domain and Infrastructure Analysis:</strong>
            <ul>
              <li>WHOIS databases for registration data</li>
              <li>DNS enumeration and analysis</li>
              <li>Certificate transparency logs</li>
              <li>Passive DNS databases</li>
            </ul>
          </li>
          <li><strong>Specialized OSINT Tools:</strong>
            <ul>
              <li>Maltego for link analysis</li>
              <li>Recon-ng for automated reconnaissance</li>
              <li>theHarvester for email and subdomain discovery</li>
              <li>SpiderFoot for automated OSINT collection</li>
            </ul>
          </li>
        </ul>
        
        <h3>OSINT Operational Security</h3>
        <ul>
          <li><strong>Attribution Avoidance:</strong>
            <ul>
              <li>Use of VPNs and proxy services</li>
              <li>Anonymous browsing techniques</li>
              <li>Burner accounts and identities</li>
              <li>Traffic obfuscation methods</li>
            </ul>
          </li>
          <li><strong>Legal and Ethical Considerations:</strong>
            <ul>
              <li>Respect for privacy and terms of service</li>
              <li>Compliance with applicable laws</li>
              <li>Responsible disclosure practices</li>
              <li>Data handling and retention policies</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Threat Actor Profiling and TTP Analysis",
      content: `
        <h2>Threat Actor Profiling and TTP Analysis</h2>
        <p>Understanding threat actors and their tactics, techniques, and procedures (TTPs) enables red teams to conduct realistic adversary simulation exercises.</p>
        
        <h3>Threat Actor Categories</h3>
        <ul>
          <li><strong>Nation-State Actors:</strong>
            <ul>
              <li>Advanced Persistent Threats (APTs)</li>
              <li>Government-sponsored groups</li>
              <li>Sophisticated tools and techniques</li>
              <li>Long-term strategic objectives</li>
            </ul>
          </li>
          <li><strong>Cybercriminal Groups:</strong>
            <ul>
              <li>Financially motivated actors</li>
              <li>Ransomware operators</li>
              <li>Banking trojans and fraud</li>
              <li>Cryptocurrency theft</li>
            </ul>
          </li>
          <li><strong>Hacktivists:</strong>
            <ul>
              <li>Ideologically motivated groups</li>
              <li>Website defacements</li>
              <li>DDoS attacks</li>
              <li>Information disclosure</li>
            </ul>
          </li>
          <li><strong>Insider Threats:</strong>
            <ul>
              <li>Malicious employees</li>
              <li>Compromised insiders</li>
              <li>Privileged access abuse</li>
              <li>Data theft and sabotage</li>
            </ul>
          </li>
        </ul>
        
        <h3>TTP Analysis Framework</h3>
        <ul>
          <li><strong>Tactics Analysis:</strong>
            <ul>
              <li>High-level adversary goals</li>
              <li>Attack phase preferences</li>
              <li>Target selection criteria</li>
              <li>Operational patterns</li>
            </ul>
          </li>
          <li><strong>Techniques Analysis:</strong>
            <ul>
              <li>Specific attack methods</li>
              <li>Tool and exploit usage</li>
              <li>Evasion techniques</li>
              <li>Persistence mechanisms</li>
            </ul>
          </li>
          <li><strong>Procedures Analysis:</strong>
            <ul>
              <li>Implementation details</li>
              <li>Operational workflows</li>
              <li>Command and control patterns</li>
              <li>Infrastructure preferences</li>
            </ul>
          </li>
        </ul>
        
        <h3>Notable Threat Actor Examples</h3>
        <ul>
          <li><strong>APT29 (Cozy Bear):</strong>
            <ul>
              <li>Russian intelligence-linked group</li>
              <li>Sophisticated spear-phishing campaigns</li>
              <li>Living-off-the-land techniques</li>
              <li>Long-term persistence focus</li>
            </ul>
          </li>
          <li><strong>Lazarus Group:</strong>
            <ul>
              <li>North Korean state-sponsored</li>
              <li>Financial institution targeting</li>
              <li>Custom malware development</li>
              <li>Destructive attack capabilities</li>
            </ul>
          </li>
          <li><strong>FIN7:</strong>
            <ul>
              <li>Financially motivated cybercriminal group</li>
              <li>Point-of-sale system targeting</li>
              <li>Social engineering expertise</li>
              <li>Retail and hospitality focus</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Threat Intelligence Collection and Analysis Lab",
    description: "Hands-on exercise in collecting and analyzing threat intelligence to support red team operations.",
    tasks: [
      {
        category: "OSINT Collection",
        commands: [
          {
            command: "Use theHarvester for email enumeration",
            description: "Collect email addresses and subdomains for a target domain",
            hint: "Try different data sources and search engines",
            expectedOutput: "List of discovered emails and subdomains"
          },
          {
            command: "Perform Google dorking reconnaissance",
            description: "Use advanced Google search operators to find sensitive information",
            hint: "Focus on file types, login pages, and configuration files",
            expectedOutput: "Discovered sensitive files and information"
          }
        ]
      },
      {
        category: "Threat Actor Research",
        commands: [
          {
            command: "Research APT29 TTPs using MITRE ATT&CK",
            description: "Analyze APT29's tactics and techniques for emulation planning",
            hint: "Focus on commonly used techniques and recent campaigns",
            expectedOutput: "Comprehensive TTP profile for APT29"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which type of threat intelligence focuses on specific attack methods and tools used by adversaries?",
      options: [
        "Strategic Intelligence",
        "Tactical Intelligence",
        "Operational Intelligence", 
        "Technical Intelligence"
      ],
      correct: 1,
      explanation: "Tactical Intelligence focuses on specific TTPs, attack methods, tools, and techniques used by adversaries, making it particularly valuable for red team operations."
    },
    {
      question: "What is the primary benefit of using threat intelligence in red team operations?",
      options: [
        "Faster exploitation of vulnerabilities",
        "More realistic adversary simulation",
        "Reduced operational costs",
        "Simplified reporting requirements"
      ],
      correct: 1,
      explanation: "The primary benefit is creating more realistic adversary simulation based on actual threat actor behavior, which provides better testing of detection and response capabilities."
    },
    {
      question: "Which OSINT technique is most effective for discovering an organization's technology stack and potential attack surface?",
      options: [
        "Social media monitoring",
        "Google dorking and Shodan searches",
        "WHOIS database queries",
        "Certificate transparency logs"
      ],
      correct: 1,
      explanation: "Google dorking and Shodan searches are most effective for discovering technology stacks, exposed services, and potential attack surfaces through internet-facing systems and indexed content."
    }
  ]
};
