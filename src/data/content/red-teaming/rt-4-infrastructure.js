/**
 * Red Team Infrastructure Module
 */

export const redTeamInfrastructureContent = {
  id: "rt-4",
  pathId: "red-teaming",
  title: "Red Team Infrastructure",
  description: "Build and manage secure, resilient red team infrastructure for command and control, payload delivery, and operational security.",
  objectives: [
    "Design secure red team infrastructure architectures",
    "Implement command and control (C2) frameworks",
    "Configure redirectors and domain fronting",
    "Establish secure communication channels",
    "Manage infrastructure operational security",
    "Deploy cloud-based attack infrastructure"
  ],
  difficulty: "Advanced",
  estimatedTime: 240,
  sections: [
    {
      title: "Infrastructure Architecture Fundamentals",
      content: `
        <h2>Red Team Infrastructure Architecture</h2>
        <p>Red team infrastructure forms the backbone of offensive operations, providing secure command and control, payload delivery, and data exfiltration capabilities while maintaining operational security.</p>
        
        <h3>Infrastructure Components</h3>
        <ul>
          <li><strong>Command and Control (C2) Servers:</strong>
            <ul>
              <li>Primary communication hub for compromised systems</li>
              <li>Payload generation and management</li>
              <li>Task assignment and result collection</li>
              <li>Session management and operator interface</li>
            </ul>
          </li>
          <li><strong>Redirectors:</strong>
            <ul>
              <li>Traffic forwarding and filtering systems</li>
              <li>Protection of backend C2 infrastructure</li>
              <li>Geographic distribution of access points</li>
              <li>Load balancing and failover capabilities</li>
            </ul>
          </li>
          <li><strong>Payload Hosting:</strong>
            <ul>
              <li>Staging servers for initial payloads</li>
              <li>Secondary payload delivery systems</li>
              <li>Legitimate-looking hosting platforms</li>
              <li>Content delivery networks (CDNs)</li>
            </ul>
          </li>
          <li><strong>Data Exfiltration Points:</strong>
            <ul>
              <li>Secure data collection servers</li>
              <li>Encrypted storage systems</li>
              <li>Covert communication channels</li>
              <li>Data processing and analysis platforms</li>
            </ul>
          </li>
        </ul>
        
        <h3>Infrastructure Design Principles</h3>
        <ul>
          <li><strong>Compartmentalization:</strong>
            <ul>
              <li>Separate infrastructure for different operations</li>
              <li>Isolated networks and systems</li>
              <li>Limited cross-contamination risk</li>
              <li>Independent failure domains</li>
            </ul>
          </li>
          <li><strong>Redundancy:</strong>
            <ul>
              <li>Multiple C2 channels and servers</li>
              <li>Backup communication methods</li>
              <li>Failover mechanisms</li>
              <li>Geographic distribution</li>
            </ul>
          </li>
          <li><strong>Scalability:</strong>
            <ul>
              <li>Dynamic resource allocation</li>
              <li>Automated deployment capabilities</li>
              <li>Load balancing and distribution</li>
              <li>Elastic infrastructure scaling</li>
            </ul>
          </li>
          <li><strong>Stealth:</strong>
            <ul>
              <li>Legitimate-looking infrastructure</li>
              <li>Traffic obfuscation and encryption</li>
              <li>Attribution avoidance</li>
              <li>Minimal digital footprint</li>
            </ul>
          </li>
        </ul>
        
        <h3>Infrastructure Lifecycle Management</h3>
        <ul>
          <li><strong>Planning Phase:</strong>
            <ul>
              <li>Requirements analysis and design</li>
              <li>Resource allocation and budgeting</li>
              <li>Risk assessment and mitigation</li>
              <li>Timeline and milestone planning</li>
            </ul>
          </li>
          <li><strong>Deployment Phase:</strong>
            <ul>
              <li>Automated provisioning and configuration</li>
              <li>Security hardening and testing</li>
              <li>Connectivity and integration testing</li>
              <li>Operational readiness validation</li>
            </ul>
          </li>
          <li><strong>Operations Phase:</strong>
            <ul>
              <li>Continuous monitoring and maintenance</li>
              <li>Performance optimization</li>
              <li>Security updates and patches</li>
              <li>Incident response and recovery</li>
            </ul>
          </li>
          <li><strong>Decommissioning Phase:</strong>
            <ul>
              <li>Secure data destruction</li>
              <li>Infrastructure cleanup</li>
              <li>Audit trail preservation</li>
              <li>Lessons learned documentation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Command and Control Frameworks",
      content: `
        <h2>Command and Control (C2) Frameworks</h2>
        <p>C2 frameworks provide the core functionality for managing compromised systems, executing commands, and maintaining persistent access during red team operations.</p>
        
        <h3>Popular C2 Frameworks</h3>
        <ul>
          <li><strong>Cobalt Strike:</strong>
            <ul>
              <li>Commercial red team platform</li>
              <li>Advanced post-exploitation capabilities</li>
              <li>Malleable C2 profiles for traffic customization</li>
              <li>Integrated social engineering tools</li>
              <li>Team collaboration features</li>
            </ul>
          </li>
          <li><strong>Metasploit Framework:</strong>
            <ul>
              <li>Open-source penetration testing platform</li>
              <li>Extensive exploit and payload library</li>
              <li>Meterpreter advanced payload</li>
              <li>Post-exploitation modules</li>
              <li>Integration with other security tools</li>
            </ul>
          </li>
          <li><strong>Empire/Starkiller:</strong>
            <ul>
              <li>PowerShell-based post-exploitation framework</li>
              <li>Living-off-the-land techniques</li>
              <li>Encrypted communication channels</li>
              <li>Modular agent architecture</li>
              <li>Web-based management interface</li>
            </ul>
          </li>
          <li><strong>Covenant:</strong>
            <ul>
              <li>.NET-based C2 framework</li>
              <li>Cross-platform compatibility</li>
              <li>Web-based collaborative interface</li>
              <li>Customizable communication protocols</li>
              <li>Integrated development environment</li>
            </ul>
          </li>
        </ul>
        
        <h3>C2 Communication Protocols</h3>
        <ul>
          <li><strong>HTTP/HTTPS:</strong>
            <ul>
              <li>Most common and versatile protocol</li>
              <li>Blends with normal web traffic</li>
              <li>Support for various data formats</li>
              <li>Easy to implement and debug</li>
            </ul>
          </li>
          <li><strong>DNS:</strong>
            <ul>
              <li>Covert channel using DNS queries</li>
              <li>Difficult to block completely</li>
              <li>Slow but reliable communication</li>
              <li>Useful for initial callback</li>
            </ul>
          </li>
          <li><strong>SMB/Named Pipes:</strong>
            <ul>
              <li>Internal network communication</li>
              <li>Peer-to-peer agent communication</li>
              <li>Useful for air-gapped networks</li>
              <li>Windows-specific protocol</li>
            </ul>
          </li>
          <li><strong>Custom Protocols:</strong>
            <ul>
              <li>Proprietary communication methods</li>
              <li>Difficult to detect and analyze</li>
              <li>Requires custom development</li>
              <li>Higher operational security</li>
            </ul>
          </li>
        </ul>
        
        <h3>C2 Profile Development</h3>
        <ul>
          <li><strong>Malleable C2 Profiles:</strong>
            <ul>
              <li>Customizable traffic patterns</li>
              <li>Mimicking legitimate applications</li>
              <li>User-agent and header customization</li>
              <li>Traffic timing and jitter control</li>
            </ul>
          </li>
          <li><strong>Traffic Analysis Evasion:</strong>
            <ul>
              <li>Encryption and obfuscation</li>
              <li>Traffic volume and timing variation</li>
              <li>Protocol tunneling techniques</li>
              <li>Legitimate service impersonation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Redirectors and Domain Fronting",
      content: `
        <h2>Redirectors and Domain Fronting</h2>
        <p>Redirectors and domain fronting techniques provide additional layers of protection and obfuscation for red team infrastructure, making detection and attribution more difficult.</p>
        
        <h3>Redirector Architecture</h3>
        <ul>
          <li><strong>Apache mod_rewrite:</strong>
            <ul>
              <li>HTTP request filtering and forwarding</li>
              <li>User-agent and IP-based filtering</li>
              <li>Legitimate traffic redirection</li>
              <li>Flexible rule-based configuration</li>
            </ul>
          </li>
          <li><strong>Nginx Reverse Proxy:</strong>
            <ul>
              <li>High-performance traffic forwarding</li>
              <li>SSL termination and re-encryption</li>
              <li>Load balancing capabilities</li>
              <li>Advanced filtering options</li>
            </ul>
          </li>
          <li><strong>CDN-based Redirectors:</strong>
            <ul>
              <li>CloudFlare, AWS CloudFront integration</li>
              <li>Geographic distribution</li>
              <li>Built-in DDoS protection</li>
              <li>Legitimate infrastructure appearance</li>
            </ul>
          </li>
        </ul>
        
        <h3>Domain Fronting Techniques</h3>
        <ul>
          <li><strong>CDN Domain Fronting:</strong>
            <ul>
              <li>Using CDN edge servers as proxies</li>
              <li>Legitimate domain in SNI/Host headers</li>
              <li>Hidden backend C2 infrastructure</li>
              <li>Difficult to block without collateral damage</li>
            </ul>
          </li>
          <li><strong>Cloud Service Fronting:</strong>
            <ul>
              <li>Azure Front Door, AWS API Gateway</li>
              <li>Legitimate cloud service appearance</li>
              <li>Built-in SSL and caching</li>
              <li>Enterprise-grade infrastructure</li>
            </ul>
          </li>
          <li><strong>Application-layer Fronting:</strong>
            <ul>
              <li>Using legitimate applications as proxies</li>
              <li>Social media and messaging platforms</li>
              <li>File sharing and collaboration tools</li>
              <li>Gaming and entertainment services</li>
            </ul>
          </li>
        </ul>
        
        <h3>Traffic Filtering and Protection</h3>
        <ul>
          <li><strong>Analyst Detection:</strong>
            <ul>
              <li>Security researcher IP ranges</li>
              <li>Sandbox and analysis environments</li>
              <li>Automated scanning tools</li>
              <li>Threat intelligence feeds</li>
            </ul>
          </li>
          <li><strong>Geographic Filtering:</strong>
            <ul>
              <li>Target-specific geographic regions</li>
              <li>Blocking unwanted countries</li>
              <li>Time-zone based filtering</li>
              <li>Language and locale detection</li>
            </ul>
          </li>
          <li><strong>Behavioral Analysis:</strong>
            <ul>
              <li>Request pattern analysis</li>
              <li>Session timing and frequency</li>
              <li>User-agent consistency</li>
              <li>Referrer and origin validation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Red Team Infrastructure Setup Lab",
    description: "Hands-on exercise in building and configuring red team infrastructure with C2 frameworks and redirectors.",
    tasks: [
      {
        category: "C2 Framework Setup",
        commands: [
          {
            command: "Install and configure Metasploit Framework",
            description: "Set up Metasploit with custom listeners and payloads",
            hint: "Focus on HTTPS listeners and staged payloads",
            expectedOutput: "Functional Metasploit C2 infrastructure"
          },
          {
            command: "Configure Cobalt Strike team server",
            description: "Deploy Cobalt Strike with custom malleable C2 profile",
            hint: "Use legitimate application profiles for stealth",
            expectedOutput: "Operational Cobalt Strike infrastructure"
          }
        ]
      },
      {
        category: "Redirector Configuration",
        commands: [
          {
            command: "Set up Apache mod_rewrite redirector",
            description: "Configure traffic filtering and forwarding rules",
            hint: "Implement user-agent and IP-based filtering",
            expectedOutput: "Functional redirector with traffic filtering"
          },
          {
            command: "Implement domain fronting with CDN",
            description: "Configure CloudFlare for domain fronting",
            hint: "Use legitimate domains for front-end appearance",
            expectedOutput: "Working domain fronting setup"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary purpose of using redirectors in red team infrastructure?",
      options: [
        "To increase bandwidth and performance",
        "To protect backend C2 servers from detection",
        "To reduce infrastructure costs",
        "To simplify configuration management"
      ],
      correct: 1,
      explanation: "Redirectors protect backend C2 servers from detection by filtering traffic, blocking analysts, and providing an additional layer of obfuscation between targets and the actual command and control infrastructure."
    },
    {
      question: "Which C2 communication protocol is most difficult to completely block?",
      options: [
        "HTTP/HTTPS",
        "DNS",
        "SMB/Named Pipes",
        "Custom TCP protocols"
      ],
      correct: 1,
      explanation: "DNS is most difficult to completely block because it's essential for normal network operations. Organizations cannot easily block all DNS traffic without breaking legitimate functionality."
    },
    {
      question: "What is domain fronting primarily used for in red team operations?",
      options: [
        "Improving connection speed",
        "Hiding C2 traffic behind legitimate domains",
        "Reducing server costs",
        "Simplifying SSL certificate management"
      ],
      correct: 1,
      explanation: "Domain fronting hides C2 traffic behind legitimate domains by using CDN or cloud services, making the traffic appear to come from trusted sources and making it difficult to block without affecting legitimate services."
    }
  ]
};
