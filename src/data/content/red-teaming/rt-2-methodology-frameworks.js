/**
 * Red Team Methodology & Frameworks Module
 */

export const redTeamMethodologyContent = {
  id: "rt-2",
  pathId: "red-teaming",
  title: "Red Team Methodology & Frameworks",
  description: "Master industry-standard red team frameworks, methodologies, and structured approaches to adversary simulation and security testing.",
  objectives: [
    "Understand various red team methodologies and frameworks",
    "Learn the MITRE ATT&CK framework in depth",
    "Master the Cyber Kill Chain methodology",
    "Explore NIST Cybersecurity Framework applications",
    "Understand OWASP Testing Guide for web applications",
    "Learn custom methodology development for specific environments"
  ],
  difficulty: "Intermediate",
  estimatedTime: 150,
  sections: [
    {
      title: "Red Team Methodologies Overview",
      content: `
        <h2>Red Team Methodologies Overview</h2>
        <p>Red team methodologies provide structured approaches to conducting adversary simulation exercises, ensuring comprehensive and repeatable testing processes.</p>
        
        <h3>Why Methodologies Matter</h3>
        <ul>
          <li><strong>Consistency:</strong> Standardized approaches across different engagements</li>
          <li><strong>Completeness:</strong> Ensuring all attack vectors are considered</li>
          <li><strong>Repeatability:</strong> Ability to reproduce and validate results</li>
          <li><strong>Communication:</strong> Common language for describing activities</li>
          <li><strong>Measurement:</strong> Metrics and KPIs for effectiveness assessment</li>
        </ul>
        
        <h3>Popular Red Team Methodologies</h3>
        <ul>
          <li><strong>MITRE ATT&CK:</strong> Comprehensive adversary tactics and techniques matrix</li>
          <li><strong>Cyber Kill Chain:</strong> Linear attack progression model</li>
          <li><strong>NIST Framework:</strong> Risk-based cybersecurity approach</li>
          <li><strong>OWASP Testing Guide:</strong> Web application security testing methodology</li>
          <li><strong>PTES (Penetration Testing Execution Standard):</strong> Structured penetration testing approach</li>
          <li><strong>OSSTMM:</strong> Open Source Security Testing Methodology Manual</li>
        </ul>
        
        <h3>Methodology Selection Criteria</h3>
        <ul>
          <li><strong>Engagement Scope:</strong> Technical vs. holistic assessment</li>
          <li><strong>Target Environment:</strong> Enterprise, cloud, web applications, IoT</li>
          <li><strong>Threat Landscape:</strong> Relevant adversary groups and TTPs</li>
          <li><strong>Compliance Requirements:</strong> Industry standards and regulations</li>
          <li><strong>Organizational Maturity:</strong> Security program sophistication level</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "MITRE ATT&CK Framework Deep Dive",
      content: `
        <h2>MITRE ATT&CK Framework Deep Dive</h2>
        <p>The MITRE ATT&CK framework is the most comprehensive and widely adopted methodology for understanding adversary behavior and conducting threat-informed defense.</p>
        
        <h3>ATT&CK Matrix Structure</h3>
        <ul>
          <li><strong>Tactics (14 Categories):</strong>
            <ul>
              <li>Reconnaissance - Gathering information about targets</li>
              <li>Resource Development - Establishing resources for operations</li>
              <li>Initial Access - Getting into the target network</li>
              <li>Execution - Running malicious code</li>
              <li>Persistence - Maintaining access</li>
              <li>Privilege Escalation - Gaining higher permissions</li>
              <li>Defense Evasion - Avoiding detection</li>
              <li>Credential Access - Stealing credentials</li>
              <li>Discovery - Learning about the environment</li>
              <li>Lateral Movement - Moving through the network</li>
              <li>Collection - Gathering information</li>
              <li>Command and Control - Communicating with systems</li>
              <li>Exfiltration - Stealing data</li>
              <li>Impact - Disrupting or destroying systems</li>
            </ul>
          </li>
        </ul>
        
        <h3>ATT&CK Techniques and Sub-techniques</h3>
        <ul>
          <li><strong>Technique Examples:</strong>
            <ul>
              <li>T1566 - Phishing (Initial Access)</li>
              <li>T1059 - Command and Scripting Interpreter (Execution)</li>
              <li>T1543 - Create or Modify System Process (Persistence)</li>
              <li>T1055 - Process Injection (Defense Evasion)</li>
              <li>T1003 - OS Credential Dumping (Credential Access)</li>
            </ul>
          </li>
          <li><strong>Sub-technique Granularity:</strong>
            <ul>
              <li>T1566.001 - Spearphishing Attachment</li>
              <li>T1566.002 - Spearphishing Link</li>
              <li>T1566.003 - Spearphishing via Service</li>
            </ul>
          </li>
        </ul>
        
        <h3>ATT&CK Data Sources and Detection</h3>
        <ul>
          <li><strong>Data Components:</strong> Specific log sources and telemetry</li>
          <li><strong>Detection Methods:</strong> Analytics and signatures</li>
          <li><strong>Mitigation Strategies:</strong> Preventive and detective controls</li>
          <li><strong>Test Cases:</strong> Atomic Red Team and other testing frameworks</li>
        </ul>
        
        <h3>ATT&CK Navigator</h3>
        <ul>
          <li><strong>Visualization Tool:</strong> Interactive matrix exploration</li>
          <li><strong>Layer Creation:</strong> Custom views and annotations</li>
          <li><strong>Threat Actor Mapping:</strong> Group-specific technique usage</li>
          <li><strong>Coverage Analysis:</strong> Detection and mitigation gaps</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Cyber Kill Chain Methodology",
      content: `
        <h2>Cyber Kill Chain Methodology</h2>
        <p>The Cyber Kill Chain, developed by Lockheed Martin, provides a linear model for understanding and defending against cyber attacks.</p>
        
        <h3>Kill Chain Phases</h3>
        <ul>
          <li><strong>1. Reconnaissance:</strong>
            <ul>
              <li>Passive information gathering</li>
              <li>Target identification and profiling</li>
              <li>Infrastructure mapping</li>
              <li>Social media and public records research</li>
            </ul>
          </li>
          <li><strong>2. Weaponization:</strong>
            <ul>
              <li>Exploit and payload development</li>
              <li>Malware creation and customization</li>
              <li>Delivery mechanism preparation</li>
              <li>Anti-detection measures implementation</li>
            </ul>
          </li>
          <li><strong>3. Delivery:</strong>
            <ul>
              <li>Email attachments and links</li>
              <li>Watering hole attacks</li>
              <li>USB drops and physical media</li>
              <li>Supply chain compromise</li>
            </ul>
          </li>
          <li><strong>4. Exploitation:</strong>
            <ul>
              <li>Vulnerability exploitation</li>
              <li>Code execution on target systems</li>
              <li>Initial system compromise</li>
              <li>Privilege escalation attempts</li>
            </ul>
          </li>
          <li><strong>5. Installation:</strong>
            <ul>
              <li>Malware installation and persistence</li>
              <li>Backdoor creation</li>
              <li>System modification</li>
              <li>Registry and file system changes</li>
            </ul>
          </li>
          <li><strong>6. Command and Control:</strong>
            <ul>
              <li>Communication channel establishment</li>
              <li>Remote access capability</li>
              <li>Command execution infrastructure</li>
              <li>Data staging and preparation</li>
            </ul>
          </li>
          <li><strong>7. Actions on Objectives:</strong>
            <ul>
              <li>Data collection and exfiltration</li>
              <li>System disruption or destruction</li>
              <li>Lateral movement and expansion</li>
              <li>Mission objective completion</li>
            </ul>
          </li>
        </ul>
        
        <h3>Kill Chain Applications in Red Teaming</h3>
        <ul>
          <li><strong>Campaign Planning:</strong> Structured approach to operation design</li>
          <li><strong>Defense Testing:</strong> Evaluating controls at each phase</li>
          <li><strong>Gap Analysis:</strong> Identifying weak points in security posture</li>
          <li><strong>Training and Education:</strong> Teaching attack progression concepts</li>
        </ul>
        
        <h3>Limitations and Considerations</h3>
        <ul>
          <li><strong>Linear Model:</strong> Real attacks may not follow sequential phases</li>
          <li><strong>Modern Threats:</strong> Living-off-the-land and fileless attacks</li>
          <li><strong>Insider Threats:</strong> May bypass early phases entirely</li>
          <li><strong>Cloud Environments:</strong> Traditional boundaries may not apply</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Red Team Methodology Application Lab",
    description: "Hands-on exercise applying different red team methodologies to plan a comprehensive adversary simulation campaign.",
    tasks: [
      {
        category: "ATT&CK Navigator Usage",
        commands: [
          {
            command: "Access MITRE ATT&CK Navigator",
            description: "Use the online ATT&CK Navigator tool to create custom layers",
            hint: "Focus on creating a threat actor emulation layer",
            expectedOutput: "Custom ATT&CK layer with selected techniques"
          }
        ]
      },
      {
        category: "Kill Chain Mapping",
        commands: [
          {
            command: "Map ATT&CK techniques to Kill Chain phases",
            description: "Create a mapping between MITRE ATT&CK and Cyber Kill Chain",
            hint: "Consider how techniques align with kill chain phases",
            expectedOutput: "Comprehensive technique-to-phase mapping"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which MITRE ATT&CK tactic focuses on maintaining access to compromised systems?",
      options: [
        "Initial Access",
        "Persistence", 
        "Lateral Movement",
        "Command and Control"
      ],
      correct: 1,
      explanation: "Persistence is the tactic that focuses on maintaining access to compromised systems across restarts, changed credentials, and other interruptions."
    },
    {
      question: "In the Cyber Kill Chain, which phase involves creating and preparing the attack payload?",
      options: [
        "Reconnaissance",
        "Weaponization",
        "Delivery", 
        "Exploitation"
      ],
      correct: 1,
      explanation: "Weaponization is the phase where attackers create and prepare the attack payload, combining exploits with malware to create deliverable weapons."
    },
    {
      question: "What is the primary advantage of using the MITRE ATT&CK framework over traditional vulnerability-focused approaches?",
      options: [
        "It focuses only on technical vulnerabilities",
        "It provides behavior-based threat modeling",
        "It's easier to implement",
        "It requires fewer resources"
      ],
      correct: 1,
      explanation: "MITRE ATT&CK provides behavior-based threat modeling that focuses on adversary tactics and techniques rather than just technical vulnerabilities, enabling more comprehensive threat-informed defense."
    }
  ]
};
