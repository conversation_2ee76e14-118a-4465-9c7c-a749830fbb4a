/**
 * AI/ML Security Testing Module
 */

export const aiMlSecurityContent = {
  id: "rt-36",
  pathId: "red-teaming",
  title: "AI/ML Security Testing",
  description: "Master artificial intelligence and machine learning security testing including adversarial attacks, model extraction, and AI system exploitation.",
  objectives: [
    "Understand AI/ML system architectures and attack surfaces",
    "Master adversarial attack techniques and generation",
    "Learn model extraction and intellectual property theft",
    "Explore data poisoning and training-time attacks",
    "Understand AI system infrastructure vulnerabilities",
    "Master AI-powered attack and defense techniques"
  ],
  difficulty: "Expert",
  estimatedTime: 360,
  sections: [
    {
      title: "AI/ML System Architecture and Attack Surfaces",
      content: `
        <h2>AI/ML System Components and Security Models</h2>
        <p>AI/ML systems present unique attack surfaces across the entire machine learning pipeline from data collection to model deployment and inference.</p>
        
        <h3>Machine Learning Pipeline Components</h3>
        <ul>
          <li><strong>Data Collection and Preprocessing:</strong>
            <ul>
              <li>Data sources and acquisition systems</li>
              <li>Data cleaning and preprocessing pipelines</li>
              <li>Feature engineering and selection</li>
              <li>Data validation and quality assurance</li>
              <li>Privacy and anonymization techniques</li>
            </ul>
          </li>
          <li><strong>Model Training and Development:</strong>
            <ul>
              <li>Training algorithms and optimization methods</li>
              <li>Model architecture and hyperparameter selection</li>
              <li>Cross-validation and performance evaluation</li>
              <li>Model versioning and experiment tracking</li>
              <li>Distributed and federated learning systems</li>
            </xs>
          </li>
          <li><strong>Model Deployment and Inference:</strong>
            <ul>
              <li>Model serving and inference engines</li>
              <li>API endpoints and web service interfaces</li>
              <li>Edge deployment and mobile integration</li>
              <li>Real-time and batch processing systems</li>
              <li>Model monitoring and performance tracking</li>
            </xs>
          </li>
        </ul>
        
        <h3>AI/ML Attack Taxonomy</h3>
        <ul>
          <li><strong>Training-Time Attacks:</strong>
            <ul>
              <li>Data poisoning and backdoor insertion</li>
              <li>Label flipping and corruption attacks</li>
              <li>Distribution shift and concept drift</li>
              <li>Adversarial training data generation</li>
              <li>Supply chain and dataset compromise</li>
            </xs>
          </li>
          <li><strong>Inference-Time Attacks:</strong>
            <ul>
              <li>Adversarial examples and evasion attacks</li>
              <li>Model extraction and stealing attacks</li>
              <li>Membership inference and privacy attacks</li>
              <li>Property inference and attribute attacks</li>
              <li>Model inversion and reconstruction attacks</li>
            </xs>
          </li>
          <li><strong>Infrastructure and System Attacks:</strong>
            <ul>
              <li>ML pipeline and workflow compromise</li>
              <li>Container and orchestration attacks</li>
              <li>Cloud ML platform vulnerabilities</li>
              <li>Hardware and accelerator attacks</li>
              <li>Communication and network attacks</li>
            </xs>
          </li>
        </ul>
        
        <h3>AI/ML Security Threat Models</h3>
        <ul>
          <li><strong>Adversary Capabilities:</strong>
            <ul>
              <li>White-box vs. black-box access levels</li>
              <li>Query budget and interaction limitations</li>
              <li>Training data access and manipulation</li>
              <li>Model architecture and parameter knowledge</li>
              <li>Infrastructure and system access</li>
            </xs>
          </li>
          <li><strong>Attack Objectives:</strong>
            <ul>
              <li>Confidentiality and privacy violations</li>
              <li>Integrity and correctness compromise</li>
              <li>Availability and denial of service</li>
              <li>Intellectual property theft</li>
              <li>Bias amplification and fairness attacks</li>
            </xs>
          </li>
          <li><strong>Deployment Scenarios:</strong>
            <ul>
              <li>Cloud-based ML services and APIs</li>
              <li>Edge and mobile device deployment</li>
              <li>Federated and distributed learning</li>
              <li>Autonomous and safety-critical systems</li>
              <li>Multi-tenant and shared infrastructure</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Adversarial Attacks and Evasion Techniques",
      content: `
        <h2>Adversarial Example Generation and Evasion</h2>
        <p>Adversarial attacks manipulate input data to cause machine learning models to make incorrect predictions while appearing normal to human observers.</p>
        
        <h3>Adversarial Example Generation Methods</h3>
        <ul>
          <li><strong>Gradient-Based Attacks:</strong>
            <ul>
              <li>Fast Gradient Sign Method (FGSM)</li>
              <li>Projected Gradient Descent (PGD)</li>
              <li>Basic Iterative Method (BIM)</li>
              <li>Momentum Iterative Method (MIM)</li>
              <li>Adam and adaptive optimization attacks</li>
            </xs>
          </li>
          <li><strong>Optimization-Based Attacks:</strong>
            <ul>
              <li>Carlini & Wagner (C&W) attacks</li>
              <li>DeepFool and minimal perturbation attacks</li>
              <li>Elastic-net and regularized attacks</li>
              <li>AutoAttack and ensemble methods</li>
              <li>Evolutionary and genetic algorithms</li>
            </xs>
          </li>
          <li><strong>Black-Box Attack Methods:</strong>
            <ul>
              <li>Query-based and decision boundary attacks</li>
              <li>Transfer attacks and surrogate models</li>
              <li>Zeroth-order optimization methods</li>
              <li>Boundary and HopSkipJump attacks</li>
              <li>Natural evolution strategies (NES)</li>
            </xs>
          </li>
        </ul>
        
        <h3>Domain-Specific Adversarial Attacks</h3>
        <ul>
          <li><strong>Computer Vision Attacks:</strong>
            <ul>
              <li>Image classification and object detection</li>
              <li>Semantic segmentation and instance recognition</li>
              <li>Face recognition and biometric systems</li>
              <li>Medical imaging and diagnostic systems</li>
              <li>Autonomous vehicle and perception systems</li>
            </xs>
          </li>
          <li><strong>Natural Language Processing Attacks:</strong>
            <ul>
              <li>Text classification and sentiment analysis</li>
              <li>Machine translation and language models</li>
              <li>Question answering and reading comprehension</li>
              <li>Chatbots and conversational AI systems</li>
              <li>Code analysis and programming assistants</li>
            </xs>
          </li>
          <li><strong>Audio and Speech Attacks:</strong>
            <ul>
              <li>Speech recognition and voice assistants</li>
              <li>Speaker identification and verification</li>
              <li>Audio classification and music recognition</li>
              <li>Deepfake and voice synthesis detection</li>
              <li>Acoustic and ultrasonic attacks</li>
            </xs>
          </li>
        </ul>
        
        <h3>Physical and Real-World Attacks</h3>
        <ul>
          <li><strong>Physical Adversarial Examples:</strong>
            <ul>
              <li>Printed and fabricated adversarial objects</li>
              <li>Adversarial patches and stickers</li>
              <li>3D adversarial objects and sculptures</li>
              <li>Wearable and clothing-based attacks</li>
              <li>Environmental and lighting manipulation</li>
            </xs>
          </li>
          <li><strong>Robust Physical Attacks:</strong>
            <ul>
              <li>Expectation over Transformation (EOT)</li>
              <li>Viewpoint and angle invariance</li>
              <li>Lighting and weather robustness</li>
              <li>Distance and scale invariance</li>
              <li>Multi-camera and sensor fusion attacks</li>
            </xs>
          </li>
          <li><strong>Stealth and Imperceptibility:</strong>
            <ul>
              <li>Perceptual distance metrics and constraints</li>
              <li>Human visual system modeling</li>
              <li>Frequency domain and transform attacks</li>
              <li>Semantic and content-preserving attacks</li>
              <li>Universal and transferable perturbations</li>
            </xs>
          </li>
        </ul>
        
        <h3>Advanced Evasion Techniques</h3>
        <ul>
          <li><strong>Adaptive and Dynamic Attacks:</strong>
            <ul>
              <li>Defense-aware and adaptive attacks</li>
              <li>Gradient masking and obfuscation bypass</li>
              <li>Ensemble and committee evasion</li>
              <li>Multi-step and iterative attacks</li>
              <li>Online learning and adaptation</li>
            </xs>
          </li>
          <li><strong>Backdoor and Trojan Attacks:</strong>
            <ul>
              <li>Training-time backdoor insertion</li>
              <li>Trigger pattern and activation design</li>
              <li>Stealth and detection evasion</li>
              <li>Multi-trigger and conditional backdoors</li>
              <li>Neural trojan and weight manipulation</li>
            </xs>
          </li>
          <li><strong>Poisoning and Data Manipulation:</strong>
            <ul>
              <li>Training data poisoning attacks</li>
              <li>Label flipping and corruption</li>
              <li>Feature space poisoning</li>
              <li>Availability and performance degradation</li>
              <li>Targeted and indiscriminate poisoning</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Model Extraction and Privacy Attacks",
      content: `
        <h2>Model Stealing and Privacy Violation Techniques</h2>
        <p>Model extraction and privacy attacks aim to steal intellectual property or extract sensitive information from machine learning models and training data.</p>
        
        <h3>Model Extraction and Stealing Attacks</h3>
        <ul>
          <li><strong>Functionality Extraction:</strong>
            <ul>
              <li>Input-output behavior replication</li>
              <li>Decision boundary approximation</li>
              <li>Feature importance and sensitivity analysis</li>
              <li>Hyperparameter and architecture inference</li>
              <li>Performance and accuracy matching</li>
            </xs>
          </li>
          <li><strong>Parameter Extraction:</strong>
            <ul>
              <li>Weight and bias parameter recovery</li>
              <li>Gradient and update information leakage</li>
              <li>Model compression and distillation</li>
              <li>Quantization and pruning reverse engineering</li>
              <li>Hardware and timing side-channel attacks</li>
            </xs>
          </li>
          <li><strong>Query Optimization Strategies:</strong>
            <ul>
              <li>Active learning and uncertainty sampling</li>
              <li>Adversarial query generation</li>
              <li>Gradient estimation and finite differences</li>
              <li>Bayesian optimization and Gaussian processes</li>
              <li>Transfer learning and domain adaptation</li>
            </xs>
          </li>
        </ul>
        
        <h3>Privacy and Membership Inference Attacks</h3>
        <ul>
          <li><strong>Membership Inference:</strong>
            <ul>
              <li>Training set membership determination</li>
              <li>Confidence score and prediction analysis</li>
              <li>Shadow model and attack model training</li>
              <li>Differential privacy and noise analysis</li>
              <li>Federated learning membership attacks</li>
            </xs>
          </li>
          <li><strong>Property Inference:</strong>
            <ul>
              <li>Dataset property and statistic inference</li>
              <li>Demographic and sensitive attribute inference</li>
              <li>Distribution and bias property extraction</li>
              <li>Correlation and relationship discovery</li>
              <li>Meta-learning and few-shot inference</li>
            </xs>
          </li>
          <li><strong>Model Inversion and Reconstruction:</strong>
            <ul>
              <li>Training data reconstruction attacks</li>
              <li>Feature and representation inversion</li>
              <li>Generative model and synthesis attacks</li>
              <li>Class representative and prototype extraction</li>
              <li>Gradient and activation-based reconstruction</li>
            </xs>
          </li>
        </ul>
        
        <h3>Federated Learning Attacks</h3>
        <ul>
          <li><strong>Gradient and Update Attacks:</strong>
            <ul>
              <li>Gradient inversion and reconstruction</li>
              <li>Model update poisoning and manipulation</li>
              <li>Byzantine and adversarial participants</li>
              <li>Sybil attacks and identity manipulation</li>
              <li>Communication and aggregation attacks</li>
            </xs>
          </li>
          <li><strong>Inference and Privacy Attacks:</strong>
            <ul>
              <li>Cross-participant inference attacks</li>
              <li>Collaborative and multi-party attacks</li>
              <li>Timing and communication pattern analysis</li>
              <li>Dropout and participation pattern attacks</li>
              <li>Auxiliary information and side-channel attacks</li>
            </xs>
          </li>
          <li><strong>Backdoor and Trojan Insertion:</strong>
            <ul>
              <li>Distributed backdoor and trigger insertion</li>
              <li>Coordinated and multi-participant attacks</li>
              <li>Stealth and detection evasion techniques</li>
              <li>Persistent and robust backdoor design</li>
              <li>Semantic and natural trigger patterns</li>
            </xs>
          </li>
        </ul>
        
        <h3>AI System Infrastructure Attacks</h3>
        <ul>
          <li><strong>ML Pipeline and Workflow Attacks:</strong>
            <ul>
              <li>Data pipeline and ETL compromise</li>
              <li>Model training and experiment manipulation</li>
              <li>Version control and artifact tampering</li>
              <li>Continuous integration and deployment attacks</li>
              <li>Monitoring and logging system compromise</li>
            </xs>
          </li>
          <li><strong>Cloud ML Platform Vulnerabilities:</strong>
            <ul>
              <li>Multi-tenant isolation and side-channel attacks</li>
              <li>Container and orchestration vulnerabilities</li>
              <li>API and service endpoint exploitation</li>
              <li>Resource exhaustion and denial of service</li>
              <li>Credential and access token theft</li>
            </xs>
          </li>
          <li><strong>Hardware and Accelerator Attacks:</strong>
            <ul>
              <li>GPU and TPU side-channel attacks</li>
              <li>Memory and cache-based information leakage</li>
              <li>Power and electromagnetic emanation analysis</li>
              <li>Fault injection and glitching attacks</li>
              <li>Hardware trojan and supply chain attacks</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "AI/ML Security Testing Lab",
    description: "Hands-on exercise in AI/ML security testing including adversarial attacks, model extraction, and privacy attacks.",
    tasks: [
      {
        category: "Adversarial Attacks",
        commands: [
          {
            command: "Generate adversarial examples using FGSM",
            description: "Create adversarial examples to fool image classification model",
            hint: "Use gradient information to generate minimal perturbations",
            expectedOutput: "Successful adversarial examples causing misclassification"
          },
          {
            command: "Implement black-box model evasion",
            description: "Attack model without access to gradients or architecture",
            hint: "Use query-based methods and transfer attacks",
            expectedOutput: "Successful black-box evasion with limited queries"
          }
        ]
      },
      {
        category: "Model Extraction",
        commands: [
          {
            command: "Perform model extraction attack",
            description: "Steal model functionality through query-based extraction",
            hint: "Use active learning and uncertainty sampling for efficient queries",
            expectedOutput: "Extracted model with similar performance to target"
          },
          {
            command: "Conduct membership inference attack",
            description: "Determine if specific data was used in model training",
            hint: "Train shadow models and analyze prediction confidence",
            expectedOutput: "Successful identification of training set membership"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which adversarial attack method is most effective against black-box models?",
      options: [
        "Fast Gradient Sign Method (FGSM)",
        "Projected Gradient Descent (PGD)",
        "Transfer attacks using surrogate models",
        "Carlini & Wagner (C&W) attacks"
      ],
      correct: 2,
      explanation: "Transfer attacks using surrogate models are most effective against black-box models because they don't require gradient access and can leverage the transferability of adversarial examples across similar models."
    },
    {
      question: "What is the primary goal of model extraction attacks?",
      options: [
        "Causing misclassification",
        "Stealing model functionality and intellectual property",
        "Degrading model performance",
        "Injecting backdoors"
      ],
      correct: 1,
      explanation: "Model extraction attacks aim to steal model functionality and intellectual property by replicating the model's behavior through strategic queries, allowing attackers to create equivalent models without access to training data or architecture."
    },
    {
      question: "Which technique is most effective for defending against adversarial examples?",
      options: [
        "Input preprocessing",
        "Adversarial training with diverse attack methods",
        "Model ensemble",
        "Feature squeezing"
      ],
      correct: 1,
      explanation: "Adversarial training with diverse attack methods is most effective because it exposes the model to various adversarial examples during training, improving robustness against multiple attack types rather than just specific perturbations."
    }
  ]
};
