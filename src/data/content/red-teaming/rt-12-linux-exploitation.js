/**
 * Linux Exploitation Module
 */

export const linuxExploitationContent = {
  id: "rt-12",
  pathId: "red-teaming",
  title: "Linux Exploitation",
  description: "Master advanced Linux system exploitation and privilege escalation techniques including kernel exploits, container escapes, and post-exploitation methods.",
  objectives: [
    "Understand Linux internals and security model",
    "Master Linux privilege escalation techniques",
    "Learn kernel exploitation and development",
    "Explore container and virtualization escapes",
    "Understand Linux post-exploitation techniques",
    "Master persistence and stealth methods"
  ],
  difficulty: "Advanced",
  estimatedTime: 260,
  sections: [
    {
      title: "Linux Internals and Security Model",
      content: `
        <h2>Linux Internals and Security Model</h2>
        <p>Understanding Linux internals is fundamental for effective exploitation and post-exploitation activities in Linux environments.</p>
        
        <h3>Linux Architecture Overview</h3>
        <ul>
          <li><strong>Kernel Space vs User Space:</strong>
            <ul>
              <li>Ring 0 (Kernel) - Privileged execution mode</li>
              <li>Ring 3 (User) - Unprivileged application mode</li>
              <li>System call interface and transitions</li>
              <li>Kernel modules and drivers</li>
              <li>Virtual file systems and proc/sys</li>
            </ul>
          </li>
          <li><strong>Process and Memory Management:</strong>
            <ul>
              <li>Process creation and lifecycle</li>
              <li>Virtual memory and address spaces</li>
              <li>Memory mapping and shared memory</li>
              <li>Process scheduling and priorities</li>
              <li>Inter-process communication (IPC)</li>
            </ul>
          </li>
          <li><strong>File System and Permissions:</strong>
            <ul>
              <li>Unix file permissions model</li>
              <li>Special file types and attributes</li>
              <li>Access Control Lists (ACLs)</li>
              <li>SELinux and AppArmor</li>
              <li>Capabilities and namespaces</li>
            </ul>
          </li>
        </ul>
        
        <h3>Linux Security Features</h3>
        <ul>
          <li><strong>Address Space Layout Randomization (ASLR):</strong>
            <ul>
              <li>Stack, heap, and library randomization</li>
              <li>Position Independent Executables (PIE)</li>
              <li>ASLR bypass techniques</li>
              <li>Information disclosure exploitation</li>
              <li>Brute force and timing attacks</li>
            </ul>
          </li>
          <li><strong>Stack Protection:</strong>
            <ul>
              <li>Stack canaries and guard pages</li>
              <li>Non-executable stack (NX bit)</li>
              <li>Stack smashing protection</li>
              <li>Return address protection</li>
              <li>Stack overflow mitigation bypass</li>
            </ul>
          </li>
          <li><strong>Control Flow Integrity:</strong>
            <ul>
              <li>Return-oriented programming (ROP) protection</li>
              <li>Jump-oriented programming (JOP) mitigation</li>
              <li>Control flow hijacking prevention</li>
              <li>Intel CET and ARM Pointer Authentication</li>
              <li>CFI bypass techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Linux Privilege Model</h3>
        <ul>
          <li><strong>User and Group Management:</strong>
            <ul>
              <li>UID/GID and effective permissions</li>
              <li>Supplementary groups</li>
              <li>Set-UID and Set-GID programs</li>
              <li>Sudo and privilege escalation</li>
              <li>PAM (Pluggable Authentication Modules)</li>
            </ul>
          </li>
          <li><strong>Capabilities System:</strong>
            <ul>
              <li>POSIX capabilities overview</li>
              <li>Capability sets (effective, permitted, inheritable)</li>
              <li>File capabilities and ambient capabilities</li>
              <li>Capability-based privilege escalation</li>
              <li>Capability dropping and sandboxing</li>
            </ul>
          </li>
          <li><strong>Mandatory Access Control:</strong>
            <ul>
              <li>SELinux policies and contexts</li>
              <li>AppArmor profiles and enforcement</li>
              <li>Grsecurity and PaX protections</li>
              <li>MAC bypass techniques</li>
              <li>Policy manipulation and exploitation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Linux Privilege Escalation Techniques",
      content: `
        <h2>Linux Privilege Escalation Techniques</h2>
        <p>Linux privilege escalation involves exploiting misconfigurations, vulnerabilities, and design flaws to gain elevated privileges.</p>
        
        <h3>SUID/SGID Exploitation</h3>
        <ul>
          <li><strong>SUID Binary Analysis:</strong>
            <ul>
              <li>Finding SUID/SGID binaries</li>
              <li>Binary vulnerability assessment</li>
              <li>Command injection in SUID programs</li>
              <li>Path manipulation attacks</li>
              <li>Library hijacking techniques</li>
            </ul>
          </li>
          <li><strong>Common SUID Exploits:</strong>
            <ul>
              <li>find command exploitation</li>
              <li>vim/nano editor escapes</li>
              <li>nmap interactive mode</li>
              <li>Custom SUID binary analysis</li>
              <li>GTFOBins technique database</li>
            </ul>
          </li>
          <li><strong>SGID Group Exploitation:</strong>
            <ul>
              <li>Group-writable file exploitation</li>
              <li>Shadow file group access</li>
              <li>Docker group membership</li>
              <li>Disk group raw device access</li>
              <li>Video group framebuffer access</li>
            </ul>
          </li>
        </ul>
        
        <h3>Sudo and Configuration Exploitation</h3>
        <ul>
          <li><strong>Sudo Misconfigurations:</strong>
            <ul>
              <li>Wildcard and path exploitation</li>
              <li>Environment variable manipulation</li>
              <li>LD_PRELOAD and LD_LIBRARY_PATH</li>
              <li>Sudo version vulnerabilities</li>
              <li>NOPASSWD directive abuse</li>
            </ul>
          </li>
          <li><strong>Cron Job Exploitation:</strong>
            <ul>
              <li>World-writable cron scripts</li>
              <li>PATH manipulation in cron</li>
              <li>Wildcard injection in scripts</li>
              <li>Cron job timing attacks</li>
              <li>Anacron and systemd timer abuse</li>
            </ul>
          </li>
          <li><strong>Service and Daemon Exploitation:</strong>
            <ul>
              <li>Weak service configurations</li>
              <li>Service account exploitation</li>
              <li>Systemd service manipulation</li>
              <li>Init script vulnerabilities</li>
              <li>Socket and port binding abuse</li>
            </ul>
          </li>
        </ul>
        
        <h3>Kernel Exploitation</h3>
        <ul>
          <li><strong>Kernel Vulnerability Classes:</strong>
            <ul>
              <li>Buffer overflows in kernel space</li>
              <li>Use-after-free vulnerabilities</li>
              <li>Integer overflow and underflow</li>
              <li>Race conditions and TOCTOU</li>
              <li>Information disclosure bugs</li>
            </ul>
          </li>
          <li><strong>Kernel Exploit Development:</strong>
            <ul>
              <li>Kernel debugging and analysis</li>
              <li>Exploit primitive development</li>
              <li>Kernel shellcode and payloads</li>
              <li>SMEP/SMAP bypass techniques</li>
              <li>Kernel ASLR and KASLR bypass</li>
            </ul>
          </li>
          <li><strong>Kernel Module Exploitation:</strong>
            <ul>
              <li>Loadable kernel module (LKM) analysis</li>
              <li>Module parameter exploitation</li>
              <li>Device driver vulnerabilities</li>
              <li>Procfs and sysfs interface abuse</li>
              <li>Kernel module rootkit techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Container and Virtualization Escapes</h3>
        <ul>
          <li><strong>Docker Container Escapes:</strong>
            <ul>
              <li>Privileged container exploitation</li>
              <li>Docker socket exposure</li>
              <li>Volume mount escapes</li>
              <li>Capability-based escapes</li>
              <li>Namespace and cgroup bypasses</li>
            </ul>
          </li>
          <li><strong>LXC/LXD Container Escapes:</strong>
            <ul>
              <li>LXD group membership exploitation</li>
              <li>Container privilege escalation</li>
              <li>Host filesystem access</li>
              <li>Network namespace escapes</li>
              <li>Resource limit bypasses</li>
            </ul>
          </li>
          <li><strong>Hypervisor Escapes:</strong>
            <ul>
              <li>VM escape vulnerabilities</li>
              <li>Hypervisor exploitation techniques</li>
              <li>Guest-to-host communication abuse</li>
              <li>Shared memory exploitation</li>
              <li>Hardware virtualization bypasses</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Linux Post-Exploitation and Persistence",
      content: `
        <h2>Linux Post-Exploitation and Persistence</h2>
        <p>Advanced techniques for maintaining access and conducting post-exploitation activities in Linux environments.</p>
        
        <h3>Credential Harvesting</h3>
        <ul>
          <li><strong>Password and Hash Extraction:</strong>
            <ul>
              <li>/etc/passwd and /etc/shadow analysis</li>
              <li>Memory credential extraction</li>
              <li>Browser and application passwords</li>
              <li>SSH key and certificate theft</li>
              <li>Kerberos ticket extraction</li>
            </ul>
          </li>
          <li><strong>Configuration File Analysis:</strong>
            <ul>
              <li>Application configuration files</li>
              <li>Database connection strings</li>
              <li>API keys and tokens</li>
              <li>Cloud service credentials</li>
              <li>Network service passwords</li>
            </ul>
          </li>
          <li><strong>Memory and Process Analysis:</strong>
            <ul>
              <li>Process memory dumping</li>
              <li>Core dump analysis</li>
              <li>Swap file examination</li>
              <li>Hibernation file analysis</li>
              <li>Volatile memory forensics</li>
            </ul>
          </li>
        </ul>
        
        <h3>Persistence Mechanisms</h3>
        <ul>
          <li><strong>System-level Persistence:</strong>
            <ul>
              <li>Systemd service creation</li>
              <li>Init script modification</li>
              <li>Cron job installation</li>
              <li>At job scheduling</li>
              <li>Kernel module persistence</li>
            </ul>
          </li>
          <li><strong>User-level Persistence:</strong>
            <ul>
              <li>Shell startup file modification</li>
              <li>User crontab entries</li>
              <li>SSH authorized_keys manipulation</li>
              <li>Desktop autostart entries</li>
              <li>User systemd services</li>
            </ul>
          </li>
          <li><strong>Application-level Persistence:</strong>
            <ul>
              <li>Web server module installation</li>
              <li>Database stored procedures</li>
              <li>Application plugin installation</li>
              <li>Library preloading (LD_PRELOAD)</li>
              <li>Binary patching and replacement</li>
            </ul>
          </li>
        </ul>
        
        <h3>Stealth and Anti-Forensics</h3>
        <ul>
          <li><strong>Log Manipulation:</strong>
            <ul>
              <li>System log clearing and modification</li>
              <li>Application log tampering</li>
              <li>Audit log evasion</li>
              <li>Syslog redirection and filtering</li>
              <li>Log rotation manipulation</li>
            </ul>
          </li>
          <li><strong>File System Hiding:</strong>
            <ul>
              <li>Hidden file and directory creation</li>
              <li>Timestamp manipulation</li>
              <li>File attribute modification</li>
              <li>Alternate data streams (ext4)</li>
              <li>Steganographic file hiding</li>
            </ul>
          </li>
          <li><strong>Process and Network Hiding:</strong>
            <ul>
              <li>Process name manipulation</li>
              <li>Process tree hiding</li>
              <li>Network connection hiding</li>
              <li>Port and service masquerading</li>
              <li>Rootkit installation and usage</li>
            </ul>
          </li>
        </ul>
        
        <h3>Lateral Movement Preparation</h3>
        <ul>
          <li><strong>Network Discovery:</strong>
            <ul>
              <li>Network interface enumeration</li>
              <li>ARP table analysis</li>
              <li>Network service scanning</li>
              <li>SSH host key collection</li>
              <li>Network share discovery</li>
            </ul>
          </li>
          <li><strong>Remote Access Setup:</strong>
            <ul>
              <li>SSH tunnel establishment</li>
              <li>Reverse shell deployment</li>
              <li>VPN and proxy configuration</li>
              <li>Port forwarding setup</li>
              <li>Covert channel creation</li>
            </ul>
          </li>
          <li><strong>Tool and Payload Deployment:</strong>
            <ul>
              <li>Binary compilation and deployment</li>
              <li>Script and interpreter usage</li>
              <li>Memory-only tool execution</li>
              <li>Living-off-the-land techniques</li>
              <li>Custom tool development</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Linux Exploitation and Privilege Escalation Lab",
    description: "Hands-on exercise in Linux exploitation techniques including SUID exploitation, kernel vulnerabilities, and container escapes.",
    tasks: [
      {
        category: "SUID/SGID Exploitation",
        commands: [
          {
            command: "Find and exploit SUID binaries",
            description: "Identify SUID binaries and exploit for privilege escalation",
            hint: "Use find command to locate SUID binaries and check GTFOBins",
            expectedOutput: "Successful privilege escalation to root"
          },
          {
            command: "Exploit sudo misconfigurations",
            description: "Identify and exploit sudo configuration weaknesses",
            hint: "Check sudo -l output and look for wildcard or path issues",
            expectedOutput: "Root access through sudo exploitation"
          }
        ]
      },
      {
        category: "Container Escape",
        commands: [
          {
            command: "Escape from Docker container",
            description: "Break out of Docker container to host system",
            hint: "Check for privileged containers, Docker socket, or capability abuse",
            expectedOutput: "Successful container escape to host"
          },
          {
            command: "Exploit kernel vulnerability",
            description: "Use kernel exploit for privilege escalation",
            hint: "Identify kernel version and search for known exploits",
            expectedOutput: "Kernel-level privilege escalation"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which Linux feature is most commonly exploited for privilege escalation?",
      options: [
        "Cron jobs",
        "SUID/SGID binaries",
        "Kernel modules",
        "Network services"
      ],
      correct: 1,
      explanation: "SUID/SGID binaries are most commonly exploited because they run with elevated privileges and often contain vulnerabilities or misconfigurations that can be abused for privilege escalation."
    },
    {
      question: "What is the primary security benefit of Linux capabilities over traditional SUID?",
      options: [
        "Better performance",
        "Easier administration",
        "Fine-grained privilege separation",
        "Improved compatibility"
      ],
      correct: 2,
      explanation: "Linux capabilities provide fine-grained privilege separation by breaking down root privileges into specific capabilities, allowing processes to have only the minimum privileges needed."
    },
    {
      question: "Which technique is most effective for escaping from a Docker container?",
      options: [
        "Buffer overflow exploitation",
        "Privileged container or Docker socket access",
        "Network protocol exploitation",
        "File system corruption"
      ],
      correct: 1,
      explanation: "Privileged containers or Docker socket access are most effective for container escapes because they provide direct access to host resources and the Docker daemon."
    }
  ]
};
