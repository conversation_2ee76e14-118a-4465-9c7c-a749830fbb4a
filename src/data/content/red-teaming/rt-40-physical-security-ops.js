/**
 * Advanced Physical Security Operations Module
 */

export const physicalSecurityOpsContent = {
  id: "rt-40",
  pathId: "red-teaming",
  title: "Advanced Physical Security Operations",
  description: "Master sophisticated physical security operations including facility penetration, surveillance evasion, and covert access techniques.",
  objectives: [
    "Understand physical security architectures and defense layers",
    "Master advanced lock picking and bypass techniques",
    "Learn surveillance detection and evasion methods",
    "Explore covert entry and infiltration techniques",
    "Understand electronic security system exploitation",
    "Master operational security and tradecraft"
  ],
  difficulty: "Expert",
  estimatedTime: 360,
  sections: [
    {
      title: "Physical Security Architecture and Assessment",
      content: `
        <h2>Physical Security Systems and Defense-in-Depth</h2>
        <p>Advanced physical security operations require comprehensive understanding of layered defense systems, detection mechanisms, and human factors.</p>
        
        <h3>Physical Security Layers and Zones</h3>
        <ul>
          <li><strong>Perimeter Security:</strong>
            <ul>
              <li>Fencing, barriers, and boundary controls</li>
              <li>Vehicle barriers and anti-ram devices</li>
              <li>Perimeter intrusion detection systems</li>
              <li>Lighting and visibility controls</li>
              <li>Natural and environmental barriers</li>
            </ul>
          </li>
          <li><strong>Building Envelope Security:</strong>
            <ul>
              <li>Structural hardening and reinforcement</li>
              <li>Window and glazing protection</li>
              <li>Roof and overhead access controls</li>
              <li>Utility and service entry points</li>
              <li>Emergency exits and fire safety systems</li>
            </xs>
          </li>
          <li><strong>Interior Zone Protection:</strong>
            <ul>
              <li>Access control and authentication systems</li>
              <li>Motion detection and occupancy sensors</li>
              <li>Video surveillance and monitoring</li>
              <li>Asset protection and secure storage</li>
              <li>Environmental and safety systems</li>
            </xs>
          </li>
        </ul>
        
        <h3>Electronic Security Systems</h3>
        <ul>
          <li><strong>Access Control Systems:</strong>
            <ul>
              <li>Card readers and proximity systems</li>
              <li>Biometric authentication devices</li>
              <li>PIN pads and keypad systems</li>
              <li>Turnstiles and mantrap systems</li>
              <li>Visitor management and escort systems</li>
            </xs>
          </li>
          <li><strong>Intrusion Detection Systems:</strong>
            <ul>
              <li>Motion sensors and PIR detectors</li>
              <li>Glass break and vibration sensors</li>
              <li>Magnetic contacts and door sensors</li>
              <li>Beam break and photoelectric systems</li>
              <li>Seismic and ground sensors</li>
            </xs>
          </li>
          <li><strong>Video Surveillance Systems:</strong>
            <ul>
              <li>CCTV cameras and recording systems</li>
              <li>IP cameras and network video systems</li>
              <li>Analytics and intelligent video systems</li>
              <li>Thermal and infrared imaging</li>
              <li>Facial recognition and behavioral analysis</li>
            </xs>
          </li>
        </ul>
        
        <h3>Physical Security Assessment Methodology</h3>
        <ul>
          <li><strong>Reconnaissance and Intelligence Gathering:</strong>
            <ul>
              <li>Open source intelligence collection</li>
              <li>Physical surveillance and observation</li>
              <li>Social engineering and human intelligence</li>
              <li>Technical surveillance and SIGINT</li>
              <li>Architectural and blueprint analysis</li>
            </xs>
          </li>
          <li><strong>Vulnerability Assessment:</strong>
            <ul>
              <li>Physical barrier and control testing</li>
              <li>Electronic system vulnerability analysis</li>
              <li>Procedural and policy gap assessment</li>
              <li>Human factor and social engineering</li>
              <li>Environmental and situational factors</li>
            </xs>
          </li>
          <li><strong>Threat Modeling and Risk Analysis:</strong>
            <ul>
              <li>Threat actor capability and motivation</li>
              <li>Attack vector and pathway analysis</li>
              <li>Asset value and criticality assessment</li>
              <li>Impact and consequence evaluation</li>
              <li>Risk mitigation and countermeasure planning</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced Lock Picking and Mechanical Bypass",
      content: `
        <h2>Mechanical Security Defeat and Bypass Techniques</h2>
        <p>Mechanical security systems rely on physical mechanisms that can be defeated through specialized techniques, tools, and understanding of lock mechanisms.</p>
        
        <h3>Lock Mechanisms and Vulnerabilities</h3>
        <ul>
          <li><strong>Pin Tumbler Locks:</strong>
            <ul>
              <li>Standard pin tumbler mechanisms</li>
              <li>Security pins and anti-pick features</li>
              <li>Sidebar and secondary locking mechanisms</li>
              <li>High-security and restricted keyways</li>
              <li>Master key systems and vulnerabilities</li>
            </xs>
          </li>
          <li><strong>Wafer and Disc Tumbler Locks:</strong>
            <ul>
              <li>Wafer tumbler construction and operation</li>
              <li>Disc detainer and rotating disc systems</li>
              <li>Automotive and specialty applications</li>
              <li>Picking techniques and specialized tools</li>
              <li>Bypass and destructive entry methods</li>
            </xs>
          </li>
          <li><strong>High-Security and Specialty Locks:</strong>
            <ul>
              <li>Medeco and ASSA high-security systems</li>
              <li>Magnetic and electronic hybrid locks</li>
              <li>Tubular and radial pin systems</li>
              <li>Lever tumbler and warded locks</li>
              <li>Combination and mechanical code locks</li>
            </xs>
          </li>
        </ul>
        
        <h3>Lock Picking Techniques and Tools</h3>
        <ul>
          <li><strong>Single Pin Picking (SPP):</strong>
            <ul>
              <li>Tension control and feedback techniques</li>
              <li>Pin setting and binding order</li>
              <li>Security pin defeat and manipulation</li>
              <li>Progressive pinning and skill development</li>
              <li>Advanced picking techniques and methods</li>
            </xs>
          </li>
          <li><strong>Raking and Rapid Entry:</strong>
            <ul>
              <li>Rake pick designs and techniques</li>
              <li>Bump key construction and usage</li>
              <li>Snap gun and pick gun operation</li>
              <li>Electric and automatic pick tools</li>
              <li>Impressioning and key cutting techniques</li>
            </xs>
          </li>
          <li><strong>Specialized Tools and Equipment:</strong>
            <ul>
              <li>Custom pick and tension tool fabrication</li>
              <li>Electronic and motorized pick tools</li>
              <li>Scope and visualization equipment</li>
              <li>Measurement and analysis tools</li>
              <li>Portable and covert tool sets</li>
            </xs>
          </li>
        </ul>
        
        <h3>Bypass and Alternative Entry Methods</h3>
        <ul>
          <li><strong>Non-Destructive Bypass Techniques:</strong>
            <ul>
              <li>Shimming and latch manipulation</li>
              <li>Under-door and gap exploitation</li>
              <li>Window and glazing bypass methods</li>
              <li>Ceiling and floor access techniques</li>
              <li>Utility and service entry exploitation</li>
            </xs>
          </li>
          <li><strong>Electronic Lock Bypass:</strong>
            <ul>
              <li>Magnetic stripe and proximity card cloning</li>
              <li>RFID and NFC card emulation</li>
              <li>Keypad and PIN code attacks</li>
              <li>Biometric sensor spoofing</li>
              <li>Wiring and circuit manipulation</li>
            </xs>
          </li>
          <li><strong>Covert Entry and Minimal Trace:</strong>
            <ul>
              <li>Lock manipulation without damage</li>
              <li>Evidence minimization and cleanup</li>
              <li>Tool mark and forensic avoidance</li>
              <li>Time optimization and efficiency</li>
              <li>Stealth and noise reduction techniques</li>
            </xs>
          </li>
        </ul>
        
        <h3>Safe and Vault Penetration</h3>
        <ul>
          <li><strong>Safe Construction and Mechanisms:</strong>
            <ul>
              <li>Fire-resistant and burglary-resistant ratings</li>
              <li>Combination lock mechanisms and vulnerabilities</li>
              <li>Electronic lock and time delay systems</li>
              <li>Relocker and anti-drill features</li>
              <li>Composite and hardened materials</li>
            </xs>
          </li>
          <li><strong>Safe Opening Techniques:</strong>
            <ul>
              <li>Combination manipulation and decoding</li>
              <li>Drilling and scope-assisted entry</li>
              <li>Thermal and cutting techniques</li>
              <li>Electronic attack and bypass methods</li>
              <li>Explosive and destructive entry</li>
            </xs>
          </li>
          <li><strong>Vault and High-Security Facilities:</strong>
            <ul>
              <li>Bank vault and treasury security</li>
              <li>Data center and server room protection</li>
              <li>Government and military facilities</li>
              <li>Jewelry and precious metal storage</li>
              <li>Museum and artifact protection</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Surveillance Evasion and Covert Operations",
      content: `
        <h2>Counter-Surveillance and Operational Security</h2>
        <p>Advanced physical operations require sophisticated surveillance detection, evasion techniques, and operational security to avoid detection and maintain mission success.</p>
        
        <h3>Surveillance Detection and Analysis</h3>
        <ul>
          <li><strong>Human Surveillance Detection:</strong>
            <ul>
              <li>Foot surveillance and mobile teams</li>
              <li>Vehicle surveillance and convoy operations</li>
              <li>Static observation posts and fixed positions</li>
              <li>Behavioral indicators and surveillance tells</li>
              <li>Pattern recognition and anomaly detection</li>
            </xs>
          </li>
          <li><strong>Technical Surveillance Detection:</strong>
            <ul>
              <li>CCTV and video surveillance systems</li>
              <li>Audio surveillance and listening devices</li>
              <li>Electronic tracking and GPS monitoring</li>
              <li>Communication interception and SIGINT</li>
              <li>Biometric and facial recognition systems</li>
            </xs>
          </li>
          <li><strong>Surveillance Detection Routes (SDR):</strong>
            <ul>
              <li>Route planning and selection criteria</li>
              <li>Chokepoint and funnel identification</li>
              <li>Timing and pace variation techniques</li>
              <li>Natural and forced surveillance breaks</li>
              <li>Confirmation and validation methods</li>
            </xs>
          </li>
        </ul>
        
        <h3>Counter-Surveillance Techniques</h3>
        <ul>
          <li><strong>Evasion and Misdirection:</strong>
            <ul>
              <li>Route variation and unpredictability</li>
              <li>Disguise and appearance modification</li>
              <li>Decoy and diversion operations</li>
              <li>Timing and schedule manipulation</li>
              <li>Transportation and vehicle changes</li>
            </xs>
          </li>
          <li><strong>Technical Counter-Surveillance:</strong>
            <ul>
              <li>RF detection and signal analysis</li>
              <li>Camera and lens detection equipment</li>
              <li>GPS jamming and spoofing devices</li>
              <li>Communication security and encryption</li>
              <li>Electronic countermeasure deployment</li>
            </xs>
          </li>
          <li><strong>Operational Security (OPSEC):</strong>
            <ul>
              <li>Information compartmentalization</li>
              <li>Communication protocols and procedures</li>
              <li>Identity protection and cover stories</li>
              <li>Digital footprint minimization</li>
              <li>Evidence destruction and sanitization</li>
            </xs>
          </li>
        </ul>
        
        <h3>Covert Entry and Infiltration</h3>
        <ul>
          <li><strong>Social Engineering and Pretexting:</strong>
            <ul>
              <li>Authority figure impersonation</li>
              <li>Service provider and contractor roles</li>
              <li>Emergency and crisis exploitation</li>
              <li>Insider recruitment and collaboration</li>
              <li>Psychological manipulation techniques</li>
            </xs>
          </li>
          <li><strong>Physical Infiltration Methods:</strong>
            <ul>
              <li>Tailgating and piggybacking techniques</li>
              <li>Badge cloning and credential forgery</li>
              <li>Uniform and equipment acquisition</li>
              <li>Vehicle and transportation spoofing</li>
              <li>Timing and opportunity exploitation</li>
            </xs>
          </li>
          <li><strong>Covert Movement and Navigation:</strong>
            <ul>
              <li>Building layout and architectural analysis</li>
              <li>Utility tunnel and service corridor access</li>
              <li>Vertical movement and climbing techniques</li>
              <li>Concealment and hiding techniques</li>
              <li>Emergency egress and escape planning</li>
            </xs>
          </li>
        </ul>
        
        <h3>Advanced Operational Techniques</h3>
        <ul>
          <li><strong>Multi-Phase Operations:</strong>
            <ul>
              <li>Long-term surveillance and reconnaissance</li>
              <li>Staged approach and incremental access</li>
              <li>Asset placement and pre-positioning</li>
              <li>Coordination and team synchronization</li>
              <li>Contingency planning and abort procedures</li>
            </xs>
          </li>
          <li><strong>Technology Integration:</strong>
            <ul>
              <li>Miniaturized surveillance equipment</li>
              <li>Wireless communication and coordination</li>
              <li>GPS tracking and navigation systems</li>
              <li>Night vision and thermal imaging</li>
              <li>Electronic warfare and jamming</li>
            </xs>
          </li>
          <li><strong>Post-Operation Security:</strong>
            <ul>
              <li>Evidence removal and sanitization</li>
              <li>Forensic countermeasures and anti-analysis</li>
              <li>Attribution avoidance and misdirection</li>
              <li>Exfiltration and extraction procedures</li>
              <li>Operational assessment and lessons learned</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Advanced Physical Security Operations Lab",
    description: "Hands-on exercise in physical security operations including lock picking, surveillance evasion, and covert entry techniques.",
    tasks: [
      {
        category: "Lock Picking and Bypass",
        commands: [
          {
            command: "Pick high-security lock with security pins",
            description: "Defeat advanced pin tumbler lock with anti-pick features",
            hint: "Use single pin picking with proper tension and pin identification",
            expectedOutput: "Successful lock opening without damage or tool marks"
          },
          {
            command: "Bypass electronic access control system",
            description: "Circumvent card reader and electronic lock system",
            hint: "Use RFID cloning, wiring manipulation, or credential spoofing",
            expectedOutput: "Successful access control bypass and facility entry"
          }
        ]
      },
      {
        category: "Surveillance Evasion",
        commands: [
          {
            command: "Execute surveillance detection route",
            description: "Detect and confirm surveillance through planned route",
            hint: "Use chokepoints, timing variations, and observation techniques",
            expectedOutput: "Successful surveillance detection and confirmation"
          },
          {
            command: "Perform covert facility infiltration",
            description: "Gain unauthorized access to secured facility",
            hint: "Combine social engineering, physical bypass, and timing",
            expectedOutput: "Successful covert entry without detection or alarm"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which lock picking technique is most effective against high-security locks with security pins?",
      options: [
        "Raking",
        "Bump keys",
        "Single pin picking with proper feedback",
        "Electric pick guns"
      ],
      correct: 2,
      explanation: "Single pin picking with proper feedback is most effective against high-security locks because it allows precise manipulation of individual pins and detection of security pin behavior that other techniques cannot handle."
    },
    {
      question: "What is the most critical factor in successful surveillance detection?",
      options: [
        "Advanced equipment",
        "Fast movement",
        "Pattern recognition and baseline establishment",
        "Multiple team members"
      ],
      correct: 2,
      explanation: "Pattern recognition and baseline establishment are most critical because surveillance detection relies on identifying anomalies and deviations from normal environmental patterns and behaviors."
    },
    {
      question: "Which approach is most effective for covert facility infiltration?",
      options: [
        "Pure technical bypass",
        "Social engineering only",
        "Coordinated multi-vector approach combining technical and social elements",
        "Brute force entry"
      ],
      correct: 2,
      explanation: "A coordinated multi-vector approach is most effective because it leverages multiple attack vectors simultaneously, increasing success probability while providing fallback options if one vector fails."
    }
  ]
};
