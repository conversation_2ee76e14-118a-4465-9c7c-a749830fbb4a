/**
 * Active Directory Attacks Module
 */

export const activeDirectoryAttacksContent = {
  id: "rt-13",
  pathId: "red-teaming",
  title: "Active Directory Attacks",
  description: "Master comprehensive Active Directory attack techniques including Kerberos attacks, domain persistence, and advanced AD exploitation methods.",
  objectives: [
    "Understand Active Directory architecture and security model",
    "Master Kerberos authentication attacks",
    "Learn domain enumeration and reconnaissance techniques",
    "Explore privilege escalation and persistence in AD",
    "Understand advanced AD attack techniques",
    "Master post-exploitation and lateral movement in domains"
  ],
  difficulty: "Advanced",
  estimatedTime: 300,
  sections: [
    {
      title: "Active Directory Fundamentals",
      content: `
        <h2>Active Directory Architecture and Security Model</h2>
        <p>Active Directory is Microsoft's directory service that provides authentication, authorization, and directory services for Windows domain networks.</p>
        
        <h3>AD Core Components</h3>
        <ul>
          <li><strong>Domain Controllers (DCs):</strong>
            <ul>
              <li>Primary and backup domain controllers</li>
              <li>Global Catalog servers</li>
              <li>FSMO (Flexible Single Master Operations) roles</li>
              <li>Read-Only Domain Controllers (RODCs)</li>
              <li>Domain controller replication</li>
            </ul>
          </li>
          <li><strong>Domain Structure:</strong>
            <ul>
              <li>Forests, trees, and domains</li>
              <li>Organizational Units (OUs)</li>
              <li>Trust relationships and boundaries</li>
              <li>Sites and subnets</li>
              <li>Schema and configuration partitions</li>
            </ul>
          </li>
          <li><strong>Security Principals:</strong>
            <ul>
              <li>Users, groups, and computer accounts</li>
              <li>Service accounts and managed service accounts</li>
              <li>Security groups and distribution groups</li>
              <li>Built-in and custom security groups</li>
              <li>Group nesting and membership</li>
            </ul>
          </li>
        </ul>
        
        <h3>Kerberos Authentication</h3>
        <ul>
          <li><strong>Kerberos Protocol Overview:</strong>
            <ul>
              <li>Key Distribution Center (KDC)</li>
              <li>Authentication Server (AS)</li>
              <li>Ticket Granting Server (TGS)</li>
              <li>Service Principal Names (SPNs)</li>
              <li>Ticket Granting Tickets (TGTs)</li>
            </ul>
          </li>
          <li><strong>Kerberos Authentication Flow:</strong>
            <ul>
              <li>AS-REQ and AS-REP (Authentication)</li>
              <li>TGS-REQ and TGS-REP (Service tickets)</li>
              <li>AP-REQ and AP-REP (Service access)</li>
              <li>Ticket lifetime and renewal</li>
              <li>Pre-authentication and encryption</li>
            </ul>
          </li>
          <li><strong>Kerberos Security Features:</strong>
            <ul>
              <li>Mutual authentication</li>
              <li>Ticket encryption and integrity</li>
              <li>Replay attack prevention</li>
              <li>Time synchronization requirements</li>
              <li>Delegation and impersonation</li>
            </ul>
          </li>
        </ul>
        
        <h3>AD Security Model</h3>
        <ul>
          <li><strong>Access Control:</strong>
            <ul>
              <li>Discretionary Access Control Lists (DACLs)</li>
              <li>System Access Control Lists (SACLs)</li>
              <li>Access Control Entries (ACEs)</li>
              <li>Inheritance and effective permissions</li>
              <li>Object ownership and security descriptors</li>
            </ul>
          </li>
          <li><strong>Privileged Groups:</strong>
            <ul>
              <li>Domain Admins and Enterprise Admins</li>
              <li>Schema Admins and Administrators</li>
              <li>Account Operators and Server Operators</li>
              <li>Backup Operators and Print Operators</li>
              <li>Custom privileged groups</li>
            </ul>
          </li>
          <li><strong>Group Policy:</strong>
            <ul>
              <li>Group Policy Objects (GPOs)</li>
              <li>Policy inheritance and precedence</li>
              <li>Security settings and configurations</li>
              <li>Software installation and scripts</li>
              <li>Administrative templates</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Kerberos Attacks and Exploitation",
      content: `
        <h2>Kerberos Attacks and Exploitation</h2>
        <p>Kerberos attacks target weaknesses in the authentication protocol and implementation to gain unauthorized access and escalate privileges.</p>
        
        <h3>Kerberoasting Attacks</h3>
        <ul>
          <li><strong>Service Principal Name (SPN) Enumeration:</strong>
            <ul>
              <li>LDAP queries for SPN discovery</li>
              <li>SetSPN command enumeration</li>
              <li>PowerShell and .NET SPN discovery</li>
              <li>Service account identification</li>
              <li>High-value target prioritization</li>
            </ul>
          </li>
          <li><strong>TGS Ticket Extraction:</strong>
            <ul>
              <li>Requesting service tickets for SPNs</li>
              <li>Ticket extraction from memory</li>
              <li>Offline ticket cracking</li>
              <li>Hash format conversion</li>
              <li>Cracking optimization techniques</li>
            </ul>
          </li>
          <li><strong>Kerberoasting Tools and Techniques:</strong>
            <ul>
              <li>Rubeus for ticket manipulation</li>
              <li>Impacket GetUserSPNs.py</li>
              <li>PowerShell Invoke-Kerberoast</li>
              <li>Hashcat and John the Ripper</li>
              <li>Custom kerberoasting scripts</li>
            </ul>
          </li>
        </ul>
        
        <h3>ASREPRoasting Attacks</h3>
        <ul>
          <li><strong>Pre-authentication Bypass:</strong>
            <ul>
              <li>Accounts without pre-authentication</li>
              <li>AS-REP message extraction</li>
              <li>Offline password cracking</li>
              <li>User enumeration techniques</li>
              <li>Targeted account identification</li>
            </ul>
          </li>
          <li><strong>ASREPRoasting Execution:</strong>
            <ul>
              <li>Rubeus ASREPRoast module</li>
              <li>Impacket GetNPUsers.py</li>
              <li>PowerShell ASREPRoast scripts</li>
              <li>LDAP query optimization</li>
              <li>Bulk user processing</li>
            </ul>
          </li>
        </ul>
        
        <h3>Golden and Silver Ticket Attacks</h3>
        <ul>
          <li><strong>Golden Ticket Attacks:</strong>
            <ul>
              <li>KRBTGT account hash extraction</li>
              <li>Domain SID identification</li>
              <li>Ticket creation and injection</li>
              <li>Persistence and stealth techniques</li>
              <li>Cross-domain golden tickets</li>
            </ul>
          </li>
          <li><strong>Silver Ticket Attacks:</strong>
            <ul>
              <li>Service account hash extraction</li>
              <li>Service-specific ticket creation</li>
              <li>Limited scope persistence</li>
              <li>Service impersonation</li>
              <li>Detection evasion methods</li>
            </ul>
          </li>
          <li><strong>Ticket Manipulation Tools:</strong>
            <ul>
              <li>Mimikatz ticket creation</li>
              <li>Rubeus ticket manipulation</li>
              <li>Impacket ticket tools</li>
              <li>Custom ticket generators</li>
              <li>Ticket injection techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Kerberos Attacks</h3>
        <ul>
          <li><strong>Delegation Attacks:</strong>
            <ul>
              <li>Unconstrained delegation exploitation</li>
              <li>Constrained delegation abuse</li>
              <li>Resource-based constrained delegation</li>
              <li>S4U2Self and S4U2Proxy attacks</li>
              <li>Cross-domain delegation</li>
            </ul>
          </li>
          <li><strong>Certificate-based Attacks:</strong>
            <ul>
              <li>AD Certificate Services exploitation</li>
              <li>Certificate template abuse</li>
              <li>PKINIT authentication</li>
              <li>Certificate persistence</li>
              <li>ESC1-ESC8 attack techniques</li>
            </ul>
          </li>
          <li><strong>Kerberos Relay Attacks:</strong>
            <ul>
              <li>Cross-protocol relay attacks</li>
              <li>NTLM to Kerberos relay</li>
              <li>Service ticket relay</li>
              <li>Authentication coercion</li>
              <li>Machine account takeover</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Domain Enumeration and Privilege Escalation",
      content: `
        <h2>Domain Enumeration and Privilege Escalation</h2>
        <p>Systematic enumeration and privilege escalation techniques for gaining administrative access in Active Directory environments.</p>
        
        <h3>Domain Reconnaissance</h3>
        <ul>
          <li><strong>Domain Information Gathering:</strong>
            <ul>
              <li>Domain and forest enumeration</li>
              <li>Domain controller identification</li>
              <li>Trust relationship mapping</li>
              <li>Site and subnet discovery</li>
              <li>Schema and configuration analysis</li>
            </ul>
          </li>
          <li><strong>User and Group Enumeration:</strong>
            <ul>
              <li>User account discovery and profiling</li>
              <li>Group membership analysis</li>
              <li>Privileged account identification</li>
              <li>Service account enumeration</li>
              <li>Inactive and stale account detection</li>
            </ul>
          </li>
          <li><strong>Computer and Service Discovery:</strong>
            <ul>
              <li>Computer account enumeration</li>
              <li>Operating system and version detection</li>
              <li>Service and application inventory</li>
              <li>Network share discovery</li>
              <li>Administrative access mapping</li>
            </ul>
          </li>
        </ul>
        
        <h3>BloodHound and Graph Analysis</h3>
        <ul>
          <li><strong>BloodHound Data Collection:</strong>
            <ul>
              <li>SharpHound collector deployment</li>
              <li>LDAP and SMB enumeration</li>
              <li>Session and logon data collection</li>
              <li>Group membership mapping</li>
              <li>Local admin rights enumeration</li>
            </ul>
          </li>
          <li><strong>Attack Path Analysis:</strong>
            <ul>
              <li>Shortest path to Domain Admin</li>
              <li>Privilege escalation chains</li>
              <li>Cross-domain attack paths</li>
              <li>High-value target identification</li>
              <li>Custom Cypher queries</li>
            </ul>
          </li>
          <li><strong>Advanced BloodHound Techniques:</strong>
            <ul>
              <li>Custom collectors and data sources</li>
              <li>Graph database manipulation</li>
              <li>Automated attack path execution</li>
              <li>Defense evasion in collection</li>
              <li>Large environment optimization</li>
            </ul>
          </li>
        </ul>
        
        <h3>ACL and Permission Abuse</h3>
        <ul>
          <li><strong>Dangerous ACL Permissions:</strong>
            <ul>
              <li>GenericAll and GenericWrite</li>
              <li>WriteOwner and WriteDACL</li>
              <li>Self and Validated-SPN</li>
              <li>ForceChangePassword</li>
              <li>AddMember and RemoveMember</li>
            </ul>
          </li>
          <li><strong>ACL Exploitation Techniques:</strong>
            <ul>
              <li>User and computer account takeover</li>
              <li>Group membership manipulation</li>
              <li>Password reset attacks</li>
              <li>SPN modification and kerberoasting</li>
              <li>DACL modification for persistence</li>
            </ul>
          </li>
          <li><strong>GPO and OU Abuse:</strong>
            <ul>
              <li>Group Policy Object modification</li>
              <li>Immediate scheduled tasks</li>
              <li>Startup and logon scripts</li>
              <li>Software installation abuse</li>
              <li>Security setting manipulation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Domain Persistence Techniques</h3>
        <ul>
          <li><strong>Credential-based Persistence:</strong>
            <ul>
              <li>Golden and silver ticket persistence</li>
              <li>Skeleton key and directory synchronization</li>
              <li>DSRM password abuse</li>
              <li>Backup operator privileges</li>
              <li>Service account password changes</li>
            </ul>
          </li>
          <li><strong>Object-based Persistence:</strong>
            <ul>
              <li>AdminSDHolder manipulation</li>
              <li>DACL backdoors</li>
              <li>SID history injection</li>
              <li>Foreign security principals</li>
              <li>Computer account manipulation</li>
            </ul>
          </li>
          <li><strong>Certificate-based Persistence:</strong>
            <ul>
              <li>Certificate template modification</li>
              <li>User and computer certificate enrollment</li>
              <li>Certificate authority compromise</li>
              <li>Certificate-based authentication</li>
              <li>Long-term certificate persistence</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Active Directory Attack Simulation Lab",
    description: "Hands-on exercise in Active Directory attack techniques including Kerberoasting, privilege escalation, and domain persistence.",
    tasks: [
      {
        category: "Kerberos Attacks",
        commands: [
          {
            command: "Perform Kerberoasting attack",
            description: "Extract and crack service account passwords using Kerberoasting",
            hint: "Use Rubeus or Impacket to request TGS tickets for SPNs",
            expectedOutput: "Cracked service account passwords"
          },
          {
            command: "Execute ASREPRoasting attack",
            description: "Target accounts without pre-authentication enabled",
            hint: "Use GetNPUsers.py or Rubeus to extract AS-REP hashes",
            expectedOutput: "AS-REP hashes for offline cracking"
          }
        ]
      },
      {
        category: "Domain Enumeration",
        commands: [
          {
            command: "Collect BloodHound data",
            description: "Use SharpHound to collect domain enumeration data",
            hint: "Run SharpHound with appropriate collection methods",
            expectedOutput: "Complete BloodHound dataset for analysis"
          },
          {
            command: "Identify privilege escalation paths",
            description: "Analyze BloodHound data for attack paths to Domain Admin",
            hint: "Use built-in queries and custom Cypher queries",
            expectedOutput: "Viable attack paths and high-value targets"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary target of a Kerberoasting attack?",
      options: [
        "Domain controller computers",
        "Service accounts with SPNs",
        "User accounts without passwords",
        "Computer accounts in the domain"
      ],
      correct: 1,
      explanation: "Kerberoasting targets service accounts with Service Principal Names (SPNs) because their TGS tickets can be requested and cracked offline to recover passwords."
    },
    {
      question: "Which Kerberos attack provides the highest level of domain persistence?",
      options: [
        "Silver Ticket",
        "Golden Ticket",
        "ASREPRoasting",
        "Kerberoasting"
      ],
      correct: 1,
      explanation: "Golden Ticket attacks provide the highest level of persistence because they use the KRBTGT hash to create valid TGTs for any user, including non-existent ones, with domain admin privileges."
    },
    {
      question: "What is the most dangerous ACL permission for privilege escalation in Active Directory?",
      options: [
        "ReadProperty",
        "WriteProperty",
        "GenericAll",
        "ListChildren"
      ],
      correct: 2,
      explanation: "GenericAll is the most dangerous ACL permission because it grants full control over an object, allowing attackers to modify any attribute, change passwords, or take ownership."
    }
  ]
};
