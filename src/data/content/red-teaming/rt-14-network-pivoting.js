/**
 * Network Pivoting & Tunneling Module
 */

export const networkPivotingContent = {
  id: "rt-14",
  pathId: "red-teaming",
  title: "Network Pivoting & Tunneling",
  description: "Master advanced network pivoting and tunneling techniques for lateral movement and accessing segmented network environments.",
  objectives: [
    "Understand network segmentation and pivoting concepts",
    "Master SSH tunneling and port forwarding techniques",
    "Learn SOCKS proxy and HTTP tunneling methods",
    "Explore DNS tunneling and covert channels",
    "Understand VPN and overlay network techniques",
    "Master multi-hop pivoting and complex network traversal"
  ],
  difficulty: "Advanced",
  estimatedTime: 240,
  sections: [
    {
      title: "Network Pivoting Fundamentals",
      content: `
        <h2>Network Pivoting and Segmentation</h2>
        <p>Network pivoting enables attackers to access network segments that are not directly reachable from their initial compromise point.</p>
        
        <h3>Network Segmentation Concepts</h3>
        <ul>
          <li><strong>Network Architecture Types:</strong>
            <ul>
              <li>Flat networks vs segmented networks</li>
              <li>DMZ and perimeter networks</li>
              <li>Internal network zones</li>
              <li>VLAN and subnet segmentation</li>
              <li>Air-gapped and isolated networks</li>
            </ul>
          </li>
          <li><strong>Security Boundaries:</strong>
            <ul>
              <li>Firewalls and access control lists</li>
              <li>Network access control (NAC)</li>
              <li>Intrusion detection and prevention</li>
              <li>Network monitoring and logging</li>
              <li>Micro-segmentation and zero trust</li>
            </ul>
          </li>
          <li><strong>Pivoting Scenarios:</strong>
            <ul>
              <li>External to internal network access</li>
              <li>Cross-VLAN and subnet traversal</li>
              <li>Management network access</li>
              <li>Cloud and hybrid environment pivoting</li>
              <li>OT/ICS network segmentation bypass</li>
            </ul>
          </li>
        </ul>
        
        <h3>Pivoting Methodologies</h3>
        <ul>
          <li><strong>Reconnaissance and Discovery:</strong>
            <ul>
              <li>Network topology mapping</li>
              <li>Route and gateway discovery</li>
              <li>Network interface enumeration</li>
              <li>Service and port scanning</li>
              <li>Trust relationship identification</li>
            </ul>
          </li>
          <li><strong>Pivot Point Selection:</strong>
            <ul>
              <li>Multi-homed host identification</li>
              <li>Network access evaluation</li>
              <li>Security control assessment</li>
              <li>Performance and reliability factors</li>
              <li>Stealth and detection considerations</li>
            </ul>
          </li>
          <li><strong>Tunneling Protocol Selection:</strong>
            <ul>
              <li>Protocol compatibility and support</li>
              <li>Firewall and filtering bypass</li>
              <li>Performance and bandwidth requirements</li>
              <li>Encryption and security features</li>
              <li>Detection and monitoring evasion</li>
            </ul>
          </li>
        </ul>
        
        <h3>Pivoting Tools and Frameworks</h3>
        <ul>
          <li><strong>Native System Tools:</strong>
            <ul>
              <li>SSH client and server</li>
              <li>Netsh port forwarding</li>
              <li>Socat and netcat variants</li>
              <li>PowerShell remoting</li>
              <li>Windows RDP and terminal services</li>
            </ul>
          </li>
          <li><strong>Specialized Pivoting Tools:</strong>
            <ul>
              <li>Metasploit pivoting modules</li>
              <li>Cobalt Strike beacon pivoting</li>
              <li>Chisel HTTP tunneling</li>
              <li>Ligolo-ng tunneling</li>
              <li>Rpivot and reGeorg</li>
            </ul>
          </li>
          <li><strong>Custom Solutions:</strong>
            <ul>
              <li>Custom tunnel implementations</li>
              <li>Protocol-specific proxies</li>
              <li>Application-layer tunnels</li>
              <li>Steganographic channels</li>
              <li>Blockchain and P2P networks</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "SSH Tunneling and Port Forwarding",
      content: `
        <h2>SSH Tunneling and Port Forwarding</h2>
        <p>SSH provides robust tunneling capabilities for secure network pivoting and accessing remote services through encrypted channels.</p>
        
        <h3>SSH Local Port Forwarding</h3>
        <ul>
          <li><strong>Local Forward Configuration:</strong>
            <ul>
              <li>ssh -L [local_port]:[target_host]:[target_port] [user]@[ssh_server]</li>
              <li>Binding to specific interfaces</li>
              <li>Multiple port forwarding rules</li>
              <li>Dynamic port allocation</li>
              <li>Background and daemon mode</li>
            </ul>
          </li>
          <li><strong>Use Cases and Applications:</strong>
            <ul>
              <li>Database and service access</li>
              <li>Web application tunneling</li>
              <li>RDP and VNC forwarding</li>
              <li>File transfer and synchronization</li>
              <li>Management interface access</li>
            </ul>
          </li>
          <li><strong>Security Considerations:</strong>
            <ul>
              <li>Key-based authentication</li>
              <li>Connection encryption and integrity</li>
              <li>Host key verification</li>
              <li>User privilege restrictions</li>
              <li>Connection logging and monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>SSH Remote Port Forwarding</h3>
        <ul>
          <li><strong>Remote Forward Configuration:</strong>
            <ul>
              <li>ssh -R [remote_port]:[local_host]:[local_port] [user]@[ssh_server]</li>
              <li>Reverse shell establishment</li>
              <li>Firewall and NAT traversal</li>
              <li>Service exposure and sharing</li>
              <li>Gateway and relay configuration</li>
            </ul>
          </li>
          <li><strong>Advanced Remote Forwarding:</strong>
            <ul>
              <li>GatewayPorts configuration</li>
              <li>AllowTcpForwarding settings</li>
              <li>PermitTunnel and TunnelDevice</li>
              <li>X11 and agent forwarding</li>
              <li>Connection multiplexing</li>
            </ul>
          </li>
        </ul>
        
        <h3>SSH Dynamic Port Forwarding (SOCKS)</h3>
        <ul>
          <li><strong>SOCKS Proxy Setup:</strong>
            <ul>
              <li>ssh -D [local_port] [user]@[ssh_server]</li>
              <li>SOCKS4 and SOCKS5 protocol support</li>
              <li>Application proxy configuration</li>
              <li>Browser and tool integration</li>
              <li>DNS resolution handling</li>
            </ul>
          </li>
          <li><strong>SOCKS Proxy Applications:</strong>
            <ul>
              <li>Web browsing and reconnaissance</li>
              <li>Network scanning and enumeration</li>
              <li>Application testing and exploitation</li>
              <li>Multi-protocol tunneling</li>
              <li>Chain proxy configurations</li>
            </ul>
          </li>
          <li><strong>SOCKS Proxy Tools:</strong>
            <ul>
              <li>ProxyChains and proxychains-ng</li>
              <li>Proxifier and SocksCap</li>
              <li>Burp Suite proxy integration</li>
              <li>Nmap proxy scanning</li>
              <li>Custom SOCKS implementations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced SSH Techniques</h3>
        <ul>
          <li><strong>SSH Escape Sequences:</strong>
            <ul>
              <li>~C command line access</li>
              <li>Dynamic port forwarding addition</li>
              <li>Connection status and control</li>
              <li>Background and foreground switching</li>
              <li>Connection termination and cleanup</li>
            </ul>
          </li>
          <li><strong>SSH Configuration Optimization:</strong>
            <ul>
              <li>ControlMaster and ControlPath</li>
              <li>ControlPersist for connection reuse</li>
              <li>Compression and cipher selection</li>
              <li>KeepAlive and timeout settings</li>
              <li>ProxyCommand and ProxyJump</li>
            </ul>
          </li>
          <li><strong>SSH Tunneling Evasion:</strong>
            <ul>
              <li>Non-standard port usage</li>
              <li>Traffic obfuscation techniques</li>
              <li>Protocol tunneling over SSH</li>
              <li>Timing and pattern randomization</li>
              <li>Legitimate service mimicry</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced Tunneling and Covert Channels",
      content: `
        <h2>Advanced Tunneling and Covert Channels</h2>
        <p>Sophisticated tunneling techniques for bypassing network restrictions and maintaining covert communication channels.</p>
        
        <h3>HTTP and HTTPS Tunneling</h3>
        <ul>
          <li><strong>HTTP CONNECT Method:</strong>
            <ul>
              <li>Proxy server CONNECT requests</li>
              <li>SSL/TLS tunnel establishment</li>
              <li>Firewall and proxy bypass</li>
              <li>Custom HTTP tunnel implementations</li>
              <li>WebSocket upgrade tunneling</li>
            </ul>
          </li>
          <li><strong>HTTP Tunneling Tools:</strong>
            <ul>
              <li>HTTPTunnel and HTTHost</li>
              <li>Chisel HTTP/HTTP2 tunneling</li>
              <li>Ngrok and localtunnel</li>
              <li>Stunnel SSL wrapper</li>
              <li>Custom web-based tunnels</li>
            </ul>
          </li>
          <li><strong>Application Layer Tunneling:</strong>
            <ul>
              <li>WebSocket-based tunnels</li>
              <li>RESTful API tunneling</li>
              <li>GraphQL query tunneling</li>
              <li>File upload/download channels</li>
              <li>Social media and cloud services</li>
            </ul>
          </li>
        </ul>
        
        <h3>DNS Tunneling Techniques</h3>
        <ul>
          <li><strong>DNS Protocol Abuse:</strong>
            <ul>
              <li>Subdomain data encoding</li>
              <li>TXT record data storage</li>
              <li>A record IP encoding</li>
              <li>CNAME and MX record abuse</li>
              <li>DNS over HTTPS (DoH) tunneling</li>
            </ul>
          </li>
          <li><strong>DNS Tunneling Tools:</strong>
            <ul>
              <li>dnscat2 tunneling framework</li>
              <li>Iodine IP over DNS</li>
              <li>DNSStager payload delivery</li>
              <li>Cobalt Strike DNS beacons</li>
              <li>Custom DNS tunnel implementations</li>
            </ul>
          </li>
          <li><strong>DNS Evasion Techniques:</strong>
            <ul>
              <li>Fast flux and domain generation</li>
              <li>Legitimate DNS service abuse</li>
              <li>DNS cache poisoning</li>
              <li>Recursive resolver exploitation</li>
              <li>DNS over TLS (DoT) encryption</li>
            </ul>
          </li>
        </ul>
        
        <h3>ICMP and Protocol Tunneling</h3>
        <ul>
          <li><strong>ICMP Tunneling:</strong>
            <ul>
              <li>Ping packet data embedding</li>
              <li>ICMP echo request/reply abuse</li>
              <li>ICMP error message tunneling</li>
              <li>Firewall ICMP bypass</li>
              <li>ICMP tunnel detection evasion</li>
            </ul>
          </li>
          <li><strong>Protocol Encapsulation:</strong>
            <ul>
              <li>GRE (Generic Routing Encapsulation)</li>
              <li>IPsec and VPN protocols</li>
              <li>VXLAN and overlay networks</li>
              <li>Custom protocol tunneling</li>
              <li>Steganographic protocols</li>
            </ul>
          </li>
          <li><strong>Covert Channel Techniques:</strong>
            <ul>
              <li>Timing-based channels</li>
              <li>Storage-based channels</li>
              <li>Network protocol field abuse</li>
              <li>Behavioral pattern encoding</li>
              <li>Physical layer covert channels</li>
            </ul>
          </li>
        </ul>
        
        <h3>Multi-hop and Complex Pivoting</h3>
        <ul>
          <li><strong>Pivot Chain Management:</strong>
            <ul>
              <li>Multi-stage pivot planning</li>
              <li>Hop reliability and redundancy</li>
              <li>Performance optimization</li>
              <li>Security and encryption layers</li>
              <li>Failure recovery and fallback</li>
            </ul>
          </li>
          <li><strong>Network Topology Mapping:</strong>
            <ul>
              <li>Route discovery and tracing</li>
              <li>Network device identification</li>
              <li>Trust boundary mapping</li>
              <li>Segmentation analysis</li>
              <li>Attack path optimization</li>
            </ul>
          </li>
          <li><strong>Automated Pivoting:</strong>
            <ul>
              <li>Framework-based automation</li>
              <li>Dynamic route discovery</li>
              <li>Intelligent hop selection</li>
              <li>Load balancing and failover</li>
              <li>Self-healing tunnel networks</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Network Pivoting and Tunneling Lab",
    description: "Hands-on exercise in network pivoting techniques including SSH tunneling, SOCKS proxies, and advanced covert channels.",
    tasks: [
      {
        category: "SSH Tunneling",
        commands: [
          {
            command: "Set up SSH local port forwarding",
            description: "Create SSH tunnel to access internal services",
            hint: "Use ssh -L to forward local ports to remote services",
            expectedOutput: "Functional SSH tunnel with service access"
          },
          {
            command: "Configure SSH SOCKS proxy",
            description: "Establish SOCKS proxy for network pivoting",
            hint: "Use ssh -D to create dynamic SOCKS proxy",
            expectedOutput: "Working SOCKS proxy for network access"
          }
        ]
      },
      {
        category: "Advanced Tunneling",
        commands: [
          {
            command: "Implement DNS tunneling",
            description: "Set up DNS-based covert communication channel",
            hint: "Use dnscat2 or similar tools for DNS tunneling",
            expectedOutput: "Functional DNS tunnel for data exfiltration"
          },
          {
            command: "Create HTTP tunnel",
            description: "Establish HTTP-based tunnel through web proxy",
            hint: "Use Chisel or custom HTTP tunnel implementation",
            expectedOutput: "HTTP tunnel bypassing firewall restrictions"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which SSH tunneling technique is most effective for bypassing outbound firewall restrictions?",
      options: [
        "Local port forwarding (-L)",
        "Remote port forwarding (-R)",
        "Dynamic port forwarding (-D)",
        "X11 forwarding (-X)"
      ],
      correct: 1,
      explanation: "Remote port forwarding (-R) is most effective for bypassing outbound restrictions because it establishes the connection from inside the network to an external SSH server, then forwards traffic back."
    },
    {
      question: "What is the primary advantage of DNS tunneling over other covert channels?",
      options: [
        "Higher bandwidth capacity",
        "Better encryption support",
        "Difficult to block without breaking functionality",
        "Faster connection establishment"
      ],
      correct: 2,
      explanation: "DNS tunneling is difficult to block without breaking functionality because DNS is essential for normal network operations, making it an effective covert channel that's hard to filter completely."
    },
    {
      question: "Which tool is most commonly used for chaining SOCKS proxies in penetration testing?",
      options: [
        "Netcat",
        "ProxyChains",
        "Socat",
        "Stunnel"
      ],
      correct: 1,
      explanation: "ProxyChains is most commonly used for chaining SOCKS proxies because it can force any TCP connection through a chain of proxies, enabling complex multi-hop network pivoting."
    }
  ]
};
