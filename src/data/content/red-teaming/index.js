/**
 * Red Teaming Learning Path
 * Advanced offensive security operations and adversary simulation
 */

// Import all module content
import { redTeamIntroContent } from './red-team-intro.js';
import { redTeamMethodologyContent } from './rt-2-methodology-frameworks.js';
import { threatIntelligenceRedTeamContent } from './rt-3-threat-intelligence.js';
import { redTeamInfrastructureContent } from './rt-4-infrastructure.js';
import { opsecContent } from './rt-5-opsec.js';
import { socialEngineeringContent } from './rt-6-social-engineering.js';
import { reconnaissanceContent } from './rt-7-reconnaissance.js';
import { initialAccessContent as rtInitialAccessContent } from './rt-8-initial-access.js';
import { commandControlContent } from './rt-9-command-control.js';
import { persistenceMechanismsContent } from './rt-10-persistence.js';
import { windowsExploitationContent } from './rt-11-windows-exploitation.js';
import { linuxExploitationContent } from './rt-12-linux-exploitation.js';
import { activeDirectoryAttacksContent } from './rt-13-active-directory.js';
import { networkPivotingContent } from './rt-14-network-pivoting.js';
import { webApplicationExploitationContent } from './rt-15-web-application.js';
import { wirelessAttacksContent } from './rt-16-wireless-attacks.js';
import { cloudAttacksContent } from './rt-17-cloud-attacks.js';
import { mobileExploitationContent } from './rt-18-mobile-exploitation.js';
import { iotEmbeddedContent } from './rt-19-iot-embedded.js';
import { physicalSecurityContent } from './rt-20-physical-security.js';
import { advancedPersistenceContent } from './rt-21-advanced-persistence.js';
import { evasionAntiForensicsContent } from './rt-22-evasion-antiforensics.js';
import { customToolDevelopmentContent } from './rt-23-custom-tool-development.js';
import { advancedC2Content } from './rt-24-advanced-c2.js';
import { threatHuntingEvasionContent } from './rt-25-threat-hunting-evasion.js';
import { supplyChainAttacksContent } from './rt-26-supply-chain-attacks.js';
import { zeroDayDevelopmentContent } from './rt-27-zero-day-development.js';
import { advancedSocialEngineeringContent } from './rt-28-advanced-social-engineering.js';
import { incidentResponseEvasionContent } from './rt-29-incident-response-evasion.js';
import { operationsManagementContent } from './rt-30-operations-management.js';
import { threatIntelligenceOpsContent } from './rt-31-threat-intelligence-ops.js';
import { nationStateSimulationContent } from './rt-32-nation-state-simulation.js';
import { criticalInfrastructureContent } from './rt-33-critical-infrastructure.js';
import { cryptographicAttacksContent } from './rt-34-cryptographic-attacks.js';
import { quantumSecurityContent } from './rt-35-quantum-security.js';
import { aiMlSecurityContent } from './rt-36-ai-ml-security.js';
import { blockchainAttacksContent } from './rt-37-blockchain-attacks.js';
import { wirelessRfAttacksContent } from './rt-38-wireless-rf-attacks.js';
import { biometricBypassContent } from './rt-39-biometric-bypass.js';
import { physicalSecurityOpsContent } from './rt-40-physical-security-ops.js';
import { advancedThreatHuntingEvasionContent } from './rt-41-threat-hunting-evasion.js';
import { autonomousAiOperationsContent } from './rt-42-autonomous-ai-operations.js';
import { spaceSatelliteSecurityContent } from './rt-43-space-satellite-security.js';
import { advancedMalwareContent } from './rt-44-advanced-malware.js';
import { cyberWarfareContent } from './rt-45-cyber-warfare.js';
import { forensicsAntiAnalysisContent } from './rt-46-forensics-anti-analysis.js';
import { emergingTechSecurityContent } from './rt-47-emerging-tech-security.js';
import { leadershipManagementContent } from './rt-48-leadership-management.js';
import { operationalPlanningContent } from './rt-49-operational-planning.js';
import { futureThreatLandscapeContent } from './rt-50-future-threats.js';
import { threatIntelligenceContent } from './threat-intelligence.js';
import { infrastructureSetupContent } from './infrastructure-setup.js';
import { initialAccessContent } from './initial-access.js';
import { executionContent } from './execution.js';
import { persistenceContent } from './persistence.js';
import { privilegeEscalationContent } from './privilege-escalation.js';
// Temporarily comment out missing imports until files are created
// import { defensiveEvasionContent } from './defensive-evasion.js';
// import { credentialAccessContent } from './credential-access.js';
// import { discoveryContent } from './discovery.js';
// import { lateralMovementContent } from './lateral-movement.js';
// import { collectionContent } from './collection.js';
// import { commandControlContent } from './command-control.js';
// import { exfiltrationContent } from './exfiltration.js';
// import { impactContent } from './impact.js';

export const redTeamingLearningPath = {
  id: "red-teaming",
  title: "Red Teaming: Advanced Adversary Simulation & Operations",
  description: "Master advanced red team operations, adversary simulation, and multi-stage attack campaigns against enterprise environments. Learn to think and operate like sophisticated threat actors.",
  category: "Advanced Offensive Security",
  difficulty: "Intermediate to Expert",
  estimatedTime: "200+ hours",
  prerequisites: [
    "Solid ethical hacking foundation",
    "Advanced networking and systems administration",
    "Programming and scripting proficiency",
    "Understanding of enterprise environments",
    "Knowledge of security controls and defenses"
  ],
  outcomes: [
    "Lead sophisticated red team operations and campaigns",
    "Master MITRE ATT&CK framework and TTPs",
    "Develop custom tools and exploit techniques",
    "Conduct advanced adversary simulation exercises",
    "Build and maintain covert infrastructure",
    "Evade modern security controls and EDR solutions"
  ],
  modules: [
    redTeamIntroContent,
    redTeamMethodologyContent,
    threatIntelligenceRedTeamContent,
    redTeamInfrastructureContent,
    opsecContent,
    socialEngineeringContent,
    reconnaissanceContent,
    rtInitialAccessContent,
    commandControlContent,
    persistenceMechanismsContent,
    windowsExploitationContent,
    linuxExploitationContent,
    activeDirectoryAttacksContent,
    networkPivotingContent,
    webApplicationExploitationContent,
    wirelessAttacksContent,
    cloudAttacksContent,
    mobileExploitationContent,
    iotEmbeddedContent,
    physicalSecurityContent,
    advancedPersistenceContent,
    evasionAntiForensicsContent,
    customToolDevelopmentContent,
    advancedC2Content,
    threatHuntingEvasionContent,
    supplyChainAttacksContent,
    zeroDayDevelopmentContent,
    advancedSocialEngineeringContent,
    incidentResponseEvasionContent,
    operationsManagementContent,
    threatIntelligenceOpsContent,
    nationStateSimulationContent,
    criticalInfrastructureContent,
    cryptographicAttacksContent,
    quantumSecurityContent,
    aiMlSecurityContent,
    blockchainAttacksContent,
    wirelessRfAttacksContent,
    biometricBypassContent,
    physicalSecurityOpsContent,
    advancedThreatHuntingEvasionContent,
    autonomousAiOperationsContent,
    spaceSatelliteSecurityContent,
    advancedMalwareContent,
    cyberWarfareContent,
    forensicsAntiAnalysisContent,
    emergingTechSecurityContent,
    leadershipManagementContent,
    operationalPlanningContent,
    futureThreatLandscapeContent,
    threatIntelligenceContent,
    infrastructureSetupContent,
    initialAccessContent,
    executionContent,
    persistenceContent,
    privilegeEscalationContent
    // Additional modules will be added as files are created
    // defensiveEvasionContent,
    // credentialAccessContent,
    // discoveryContent,
    // lateralMovementContent,
    // collectionContent,
    // exfiltrationContent,
    // impactContent
  ]
};

export const getAllRedTeamingModules = () => {
  return redTeamingLearningPath.modules;
};

export const getRedTeamingModuleById = (id) => {
  return redTeamingLearningPath.modules.find(module => module.id === id);
}; 