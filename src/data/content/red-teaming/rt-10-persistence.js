/**
 * Persistence Mechanisms Module
 */

export const persistenceMechanismsContent = {
  id: "rt-10",
  pathId: "red-teaming",
  title: "Persistence Mechanisms",
  description: "Master techniques for maintaining persistent access to compromised systems across reboots, updates, and security changes.",
  objectives: [
    "Understand persistence fundamentals and categories",
    "Master Windows persistence techniques and mechanisms",
    "Learn Linux and Unix persistence methods",
    "Explore cloud and container persistence strategies",
    "Understand stealth and evasion in persistence",
    "Develop advanced and custom persistence techniques"
  ],
  difficulty: "Advanced",
  estimatedTime: 240,
  sections: [
    {
      title: "Persistence Fundamentals",
      content: `
        <h2>Persistence Fundamentals</h2>
        <p>Persistence mechanisms ensure continued access to compromised systems by surviving system reboots, user logouts, and security updates.</p>
        
        <h3>Persistence Categories</h3>
        <ul>
          <li><strong>Boot and Logon Persistence:</strong>
            <ul>
              <li>System startup and initialization</li>
              <li>User logon and session establishment</li>
              <li>Service and daemon startup</li>
              <li>Scheduled task execution</li>
              <li>Registry and configuration modifications</li>
            </ul>
          </li>
          <li><strong>Privilege-based Persistence:</strong>
            <ul>
              <li>Administrative and root-level access</li>
              <li>Service account exploitation</li>
              <li>Kernel and driver-level persistence</li>
              <li>Hypervisor and firmware persistence</li>
              <li>Hardware-based persistence</li>
            </ul>
          </li>
          <li><strong>Application-based Persistence:</strong>
            <ul>
              <li>Browser extension and plugin persistence</li>
              <li>Office macro and template persistence</li>
              <li>Application configuration modifications</li>
              <li>DLL hijacking and side-loading</li>
              <li>Process injection and hollowing</li>
            </ul>
          </li>
          <li><strong>Network-based Persistence:</strong>
            <ul>
              <li>Network service backdoors</li>
              <li>Router and switch persistence</li>
              <li>DNS and DHCP server modifications</li>
              <li>Wireless access point persistence</li>
              <li>Cloud service account persistence</li>
            </ul>
          </li>
        </ul>
        
        <h3>Persistence Design Principles</h3>
        <ul>
          <li><strong>Stealth and Evasion:</strong>
            <ul>
              <li>Minimal system footprint</li>
              <li>Legitimate process mimicry</li>
              <li>Anti-forensics techniques</li>
              <li>Detection evasion methods</li>
              <li>Attribution avoidance</li>
            </ul>
          </li>
          <li><strong>Resilience and Redundancy:</strong>
            <ul>
              <li>Multiple persistence mechanisms</li>
              <li>Self-healing capabilities</li>
              <li>Backup and recovery methods</li>
              <li>Cross-platform compatibility</li>
              <li>Update and patch survival</li>
            </ul>
          </li>
          <li><strong>Operational Security:</strong>
            <ul>
              <li>Encrypted communication channels</li>
              <li>Secure key management</li>
              <li>Access control and authentication</li>
              <li>Audit trail minimization</li>
              <li>Emergency cleanup procedures</li>
            </ul>
          </li>
        </ul>
        
        <h3>MITRE ATT&CK Persistence Techniques</h3>
        <ul>
          <li><strong>T1543 - Create or Modify System Process:</strong>
            <ul>
              <li>Windows Service (T1543.003)</li>
              <li>Systemd Service (T1543.002)</li>
              <li>Launch Agent (T1543.001)</li>
              <li>Launch Daemon (T1543.004)</li>
            </ul>
          </li>
          <li><strong>T1547 - Boot or Logon Autostart Execution:</strong>
            <ul>
              <li>Registry Run Keys (T1547.001)</li>
              <li>Startup Folder (T1547.001)</li>
              <li>Winlogon Helper DLL (T1547.004)</li>
              <li>Login Hook (T1547.011)</li>
            </ul>
          </li>
          <li><strong>T1053 - Scheduled Task/Job:</strong>
            <ul>
              <li>Scheduled Task (T1053.005)</li>
              <li>Cron (T1053.003)</li>
              <li>At (T1053.002)</li>
              <li>Container Orchestration Job (T1053.007)</li>
            </ul>
          </li>
          <li><strong>T1574 - Hijack Execution Flow:</strong>
            <ul>
              <li>DLL Search Order Hijacking (T1574.001)</li>
              <li>DLL Side-Loading (T1574.002)</li>
              <li>Path Interception (T1574.007)</li>
              <li>Services File Permissions Weakness (T1574.010)</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Windows Persistence Techniques",
      content: `
        <h2>Windows Persistence Techniques</h2>
        <p>Windows systems offer numerous persistence mechanisms through registry modifications, services, scheduled tasks, and system process manipulation.</p>
        
        <h3>Registry-based Persistence</h3>
        <ul>
          <li><strong>Run and RunOnce Keys:</strong>
            <ul>
              <li>HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\Run</li>
              <li>HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run</li>
              <li>RunOnce keys for single execution</li>
              <li>RunServices and RunServicesOnce</li>
              <li>Policies\\Explorer\\Run keys</li>
            </ul>
          </li>
          <li><strong>Winlogon Registry Keys:</strong>
            <ul>
              <li>Userinit and Shell values</li>
              <li>Notify packages and DLLs</li>
              <li>ScreenSaver executable</li>
              <li>TaskMan replacement</li>
              <li>System and SecurityProviders</li>
            </ul>
          </li>
          <li><strong>Service and Driver Keys:</strong>
            <ul>
              <li>Services registry hive modifications</li>
              <li>Driver loading and initialization</li>
              <li>Service dependencies and triggers</li>
              <li>Service failure recovery actions</li>
              <li>Boot and system start services</li>
            </ul>
          </li>
        </ul>
        
        <h3>Service-based Persistence</h3>
        <ul>
          <li><strong>Windows Service Creation:</strong>
            <ul>
              <li>sc.exe service creation and management</li>
              <li>PowerShell New-Service cmdlet</li>
              <li>WMI service creation methods</li>
              <li>Service Control Manager API</li>
              <li>Service binary path modifications</li>
            </ul>
          </li>
          <li><strong>Service Hijacking:</strong>
            <ul>
              <li>Unquoted service path exploitation</li>
              <li>Service binary replacement</li>
              <li>Service DLL hijacking</li>
              <li>Service permissions weakness</li>
              <li>Service recovery action abuse</li>
            </ul>
          </li>
          <li><strong>Service Masquerading:</strong>
            <ul>
              <li>Legitimate service name mimicry</li>
              <li>System service impersonation</li>
              <li>Service description spoofing</li>
              <li>Service account reuse</li>
              <li>Service dependency manipulation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Scheduled Task Persistence</h3>
        <ul>
          <li><strong>Task Scheduler Mechanisms:</strong>
            <ul>
              <li>schtasks.exe command-line utility</li>
              <li>PowerShell ScheduledTasks module</li>
              <li>Task Scheduler COM interface</li>
              <li>WMI Win32_ScheduledJob class</li>
              <li>AT command (legacy systems)</li>
            </ul>
          </li>
          <li><strong>Task Trigger Types:</strong>
            <ul>
              <li>Time-based triggers (daily, weekly)</li>
              <li>Event-based triggers (logon, startup)</li>
              <li>Idle and session state triggers</li>
              <li>Custom event log triggers</li>
              <li>Registration and modification triggers</li>
            </ul>
          </li>
          <li><strong>Task Evasion Techniques:</strong>
            <ul>
              <li>Hidden task creation</li>
              <li>System account execution</li>
              <li>Highest privilege execution</li>
              <li>Task folder organization</li>
              <li>Task history and logging control</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Windows Persistence</h3>
        <ul>
          <li><strong>DLL Hijacking and Side-loading:</strong>
            <ul>
              <li>Search order hijacking</li>
              <li>Phantom DLL hijacking</li>
              <li>COM hijacking techniques</li>
              <li>WinSxS DLL replacement</li>
              <li>Application-specific DLL loading</li>
            </ul>
          </li>
          <li><strong>WMI Persistence:</strong>
            <ul>
              <li>WMI event subscriptions</li>
              <li>Permanent event consumers</li>
              <li>WMI filter and binding</li>
              <li>ActiveScript event consumers</li>
              <li>CommandLine event consumers</li>
            </ul>
          </li>
          <li><strong>COM and DCOM Persistence:</strong>
            <ul>
              <li>COM object hijacking</li>
              <li>DCOM application modification</li>
              <li>InprocServer32 replacement</li>
              <li>LocalServer32 modification</li>
              <li>TreatAs registry manipulation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Linux and Cross-Platform Persistence",
      content: `
        <h2>Linux and Cross-Platform Persistence</h2>
        <p>Linux and Unix systems provide various persistence mechanisms through init systems, cron jobs, shell configurations, and system modifications.</p>
        
        <h3>Linux Init System Persistence</h3>
        <ul>
          <li><strong>Systemd Service Persistence:</strong>
            <ul>
              <li>Service unit file creation</li>
              <li>User and system service directories</li>
              <li>Service dependencies and targets</li>
              <li>Timer-based service activation</li>
              <li>Socket-activated services</li>
            </ul>
          </li>
          <li><strong>SysV Init Scripts:</strong>
            <ul>
              <li>/etc/init.d/ script placement</li>
              <li>Runlevel configuration</li>
              <li>update-rc.d and chkconfig</li>
              <li>Service priority and ordering</li>
              <li>Legacy init system compatibility</li>
            </ul>
          </li>
          <li><strong>Upstart Configuration:</strong>
            <ul>
              <li>/etc/init/ job configuration</li>
              <li>Event-driven service startup</li>
              <li>Job dependencies and conditions</li>
              <li>Respawn and restart policies</li>
              <li>User session jobs</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cron and Scheduled Job Persistence</h3>
        <ul>
          <li><strong>Crontab Mechanisms:</strong>
            <ul>
              <li>User crontab files</li>
              <li>System-wide cron directories</li>
              <li>/etc/cron.d/ drop-in files</li>
              <li>Anacron for irregular schedules</li>
              <li>At command for one-time jobs</li>
            </ul>
          </li>
          <li><strong>Cron Evasion Techniques:</strong>
            <ul>
              <li>Hidden crontab entries</li>
              <li>Non-standard cron directories</li>
              <li>Cron job masquerading</li>
              <li>Environment variable manipulation</li>
              <li>Cron logging suppression</li>
            </ul>
          </li>
        </ul>
        
        <h3>Shell and Profile Persistence</h3>
        <ul>
          <li><strong>Shell Startup Files:</strong>
            <ul>
              <li>.bashrc and .bash_profile</li>
              <li>.zshrc and .zsh_profile</li>
              <li>/etc/profile and /etc/bash.bashrc</li>
              <li>.profile and .login files</li>
              <li>Shell-specific configuration files</li>
            </ul>
          </li>
          <li><strong>Environment Modifications:</strong>
            <ul>
              <li>PATH variable manipulation</li>
              <li>LD_PRELOAD library injection</li>
              <li>PROMPT_COMMAND execution</li>
              <li>Alias and function definitions</li>
              <li>Environment variable persistence</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Linux Persistence</h3>
        <ul>
          <li><strong>Kernel Module Persistence:</strong>
            <ul>
              <li>Loadable kernel module (LKM) rootkits</li>
              <li>/etc/modules and modprobe configuration</li>
              <li>Kernel module signing bypass</li>
              <li>DKMS automatic module building</li>
              <li>Kernel symbol table manipulation</li>
            </ul>
          </li>
          <li><strong>Library and Binary Modification:</strong>
            <ul>
              <li>Shared library replacement</li>
              <li>Binary patching and modification</li>
              <li>LD_PRELOAD and LD_LIBRARY_PATH</li>
              <li>Dynamic linker manipulation</li>
              <li>SUID and capability abuse</li>
            </ul>
          </li>
          <li><strong>Container and Cloud Persistence:</strong>
            <ul>
              <li>Container image modification</li>
              <li>Kubernetes persistent volumes</li>
              <li>Docker daemon socket abuse</li>
              <li>Container orchestration jobs</li>
              <li>Cloud metadata service persistence</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cross-Platform Techniques</h3>
        <ul>
          <li><strong>Application-based Persistence:</strong>
            <ul>
              <li>Browser extension persistence</li>
              <li>Office macro and add-in persistence</li>
              <li>Java application persistence</li>
              <li>Python and script-based persistence</li>
              <li>Cross-platform framework abuse</li>
            </ul>
          </li>
          <li><strong>Network Service Persistence:</strong>
            <ul>
              <li>SSH authorized_keys modification</li>
              <li>Web server configuration changes</li>
              <li>Database stored procedures</li>
              <li>Network device configuration</li>
              <li>Cloud service account persistence</li>
            </ul>
          </li>
          <li><strong>Firmware and Hardware Persistence:</strong>
            <ul>
              <li>UEFI and BIOS modification</li>
              <li>Hardware implant persistence</li>
              <li>Network device firmware</li>
              <li>USB and peripheral device persistence</li>
              <li>Hypervisor-level persistence</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Persistence Mechanism Implementation Lab",
    description: "Hands-on exercise in implementing various persistence mechanisms across different operating systems and environments.",
    tasks: [
      {
        category: "Windows Persistence",
        commands: [
          {
            command: "Create Windows service for persistence",
            description: "Implement service-based persistence with stealth techniques",
            hint: "Use legitimate service names and descriptions for camouflage",
            expectedOutput: "Functional Windows service with persistence capabilities"
          },
          {
            command: "Implement registry-based persistence",
            description: "Create multiple registry-based persistence mechanisms",
            hint: "Use various registry keys and evasion techniques",
            expectedOutput: "Multiple registry persistence entries with stealth"
          }
        ]
      },
      {
        category: "Linux Persistence",
        commands: [
          {
            command: "Create systemd service persistence",
            description: "Implement systemd-based persistence with proper configuration",
            hint: "Use user services and timer activation for stealth",
            expectedOutput: "Functional systemd service with persistence"
          },
          {
            command: "Implement cron-based persistence",
            description: "Create cron job persistence with evasion techniques",
            hint: "Use non-standard locations and hidden entries",
            expectedOutput: "Stealthy cron-based persistence mechanism"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which Windows persistence mechanism is most difficult to detect through standard administrative tools?",
      options: [
        "Registry Run keys",
        "Windows Services",
        "WMI event subscriptions",
        "Scheduled Tasks"
      ],
      correct: 2,
      explanation: "WMI event subscriptions are most difficult to detect because they operate at a lower level than standard administrative tools typically monitor, and require specialized WMI queries to discover."
    },
    {
      question: "What is the primary advantage of using systemd timers over cron jobs for persistence on Linux?",
      options: [
        "Better performance",
        "Easier configuration",
        "More granular scheduling and logging integration",
        "Lower resource usage"
      ],
      correct: 2,
      explanation: "Systemd timers provide more granular scheduling options, better integration with system logging, and more sophisticated dependency management compared to traditional cron jobs."
    },
    {
      question: "Which persistence technique provides the highest level of system access and stealth?",
      options: [
        "User-level scheduled tasks",
        "Application startup modifications",
        "Kernel module persistence",
        "Registry autostart entries"
      ],
      correct: 2,
      explanation: "Kernel module persistence provides the highest level of system access and stealth because it operates at the kernel level with full system privileges and can hide from most detection mechanisms."
    }
  ]
};
