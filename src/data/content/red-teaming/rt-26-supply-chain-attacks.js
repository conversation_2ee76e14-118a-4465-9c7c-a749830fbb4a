/**
 * Supply Chain Attacks Module
 */

export const supplyChainAttacksContent = {
  id: "rt-26",
  pathId: "red-teaming",
  title: "Supply Chain Attacks",
  description: "Master supply chain attack techniques including software supply chain compromise, hardware attacks, and third-party vendor exploitation.",
  objectives: [
    "Understand supply chain attack vectors and methodologies",
    "Master software supply chain compromise techniques",
    "Learn hardware supply chain attack methods",
    "Explore vendor and third-party exploitation",
    "Understand cloud and SaaS supply chain attacks",
    "Master supply chain persistence and attribution evasion"
  ],
  difficulty: "Expert",
  estimatedTime: 320,
  sections: [
    {
      title: "Supply Chain Attack Fundamentals",
      content: `
        <h2>Supply Chain Attack Landscape and Methodology</h2>
        <p>Supply chain attacks target the complex ecosystem of vendors, suppliers, and dependencies that modern organizations rely upon.</p>
        
        <h3>Supply Chain Attack Categories</h3>
        <ul>
          <li><strong>Software Supply Chain Attacks:</strong>
            <ul>
              <li>Source code repository compromise</li>
              <li>Build system and CI/CD pipeline attacks</li>
              <li>Package repository and dependency poisoning</li>
              <li>Code signing certificate theft and abuse</li>
              <li>Software update mechanism compromise</li>
            </ul>
          </li>
          <li><strong>Hardware Supply Chain Attacks:</strong>
            <ul>
              <li>Manufacturing process compromise</li>
              <li>Component and chip-level modifications</li>
              <li>Firmware and BIOS/UEFI implants</li>
              <li>Hardware trojan insertion</li>
              <li>Supply chain interdiction and modification</li>
            </ul>
          </li>
          <li><strong>Service and Vendor Attacks:</strong>
            <ul>
              <li>Managed service provider (MSP) compromise</li>
              <li>Cloud service provider attacks</li>
              <li>Third-party vendor and contractor exploitation</li>
              <li>Software-as-a-Service (SaaS) provider attacks</li>
              <li>Outsourced development and support compromise</li>
            </ul>
          </li>
        </ul>
        
        <h3>Attack Lifecycle and Methodology</h3>
        <ul>
          <li><strong>Target Selection and Analysis:</strong>
            <ul>
              <li>Supply chain mapping and dependency analysis</li>
              <li>High-value target identification</li>
              <li>Vendor and supplier assessment</li>
              <li>Trust relationship analysis</li>
              <li>Attack surface and opportunity evaluation</li>
            </ul>
          </li>
          <li><strong>Initial Compromise:</strong>
            <ul>
              <li>Vendor and supplier network infiltration</li>
              <li>Development environment compromise</li>
              <li>Build and deployment system access</li>
              <li>Code repository and version control access</li>
              <li>Certificate and signing key theft</li>
            </ul>
          </li>
          <li><strong>Payload Insertion and Distribution:</strong>
            <ul>
              <li>Malicious code injection and modification</li>
              <li>Backdoor and implant insertion</li>
              <li>Legitimate software trojanization</li>
              <li>Update and patch mechanism abuse</li>
              <li>Distribution channel compromise</li>
            </ul>
          </li>
        </ul>
        
        <h3>Supply Chain Trust Models</h3>
        <ul>
          <li><strong>Traditional Trust Models:</strong>
            <ul>
              <li>Vendor certification and validation</li>
              <li>Code signing and digital certificates</li>
              <li>Security audits and assessments</li>
              <li>Compliance and regulatory frameworks</li>
              <li>Contractual security requirements</li>
            </ul>
          </li>
          <li><strong>Zero Trust Supply Chain:</strong>
            <ul>
              <li>Continuous verification and validation</li>
              <li>Runtime integrity monitoring</li>
              <li>Behavioral analysis and anomaly detection</li>
              <li>Micro-segmentation and isolation</li>
              <li>Least privilege and access controls</li>
            </ul>
          </li>
          <li><strong>Supply Chain Risk Management:</strong>
            <ul>
              <li>Vendor risk assessment and scoring</li>
              <li>Dependency mapping and analysis</li>
              <li>Third-party security monitoring</li>
              <li>Incident response and containment</li>
              <li>Business continuity and resilience</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Software Supply Chain Compromise",
      content: `
        <h2>Software Development and Distribution Attacks</h2>
        <p>Software supply chain attacks target the development, build, and distribution processes to inject malicious code into legitimate software.</p>
        
        <h3>Source Code and Repository Attacks</h3>
        <ul>
          <li><strong>Version Control System Compromise:</strong>
            <ul>
              <li>Git, SVN, and Mercurial repository access</li>
              <li>Developer account and credential theft</li>
              <li>Commit history manipulation and rewriting</li>
              <li>Branch and merge process exploitation</li>
              <li>Code review and approval bypass</li>
            </ul>
          </li>
          <li><strong>Malicious Code Injection:</strong>
            <ul>
              <li>Backdoor insertion in source code</li>
              <li>Logic bomb and time-delayed payloads</li>
              <li>Dependency and library modification</li>
              <li>Configuration and build script tampering</li>
              <li>Test and documentation file abuse</li>
            </ul>
          </li>
          <li><strong>Developer Environment Compromise:</strong>
            <ul>
              <li>Development workstation infiltration</li>
              <li>IDE and development tool modification</li>
              <li>Compiler and toolchain compromise</li>
              <li>Local repository and workspace tampering</li>
              <li>SSH key and credential theft</li>
            </ul>
          </li>
        </ul>
        
        <h3>Build System and CI/CD Attacks</h3>
        <ul>
          <li><strong>Continuous Integration Compromise:</strong>
            <ul>
              <li>Jenkins, GitLab CI, and GitHub Actions attacks</li>
              <li>Build server and agent compromise</li>
              <li>Pipeline configuration modification</li>
              <li>Secret and credential extraction</li>
              <li>Artifact and dependency manipulation</li>
            </ul>
          </li>
          <li><strong>Build Process Manipulation:</strong>
            <ul>
              <li>Compiler and linker modification</li>
              <li>Build script and makefile tampering</li>
              <li>Dependency resolution and fetching abuse</li>
              <li>Code generation and preprocessing attacks</li>
              <li>Binary and executable modification</li>
            </ul>
          </li>
          <li><strong>Deployment and Release Attacks:</strong>
            <ul>
              <li>Release pipeline compromise</li>
              <li>Artifact repository and registry attacks</li>
              <li>Code signing and certificate abuse</li>
              <li>Distribution channel manipulation</li>
              <li>Update and patch mechanism compromise</li>
            </ul>
          </li>
        </ul>
        
        <h3>Package and Dependency Attacks</h3>
        <ul>
          <li><strong>Package Repository Poisoning:</strong>
            <ul>
              <li>npm, PyPI, and Maven repository attacks</li>
              <li>Typosquatting and name confusion</li>
              <li>Package maintainer account compromise</li>
              <li>Malicious package upload and distribution</li>
              <li>Dependency confusion and substitution</li>
            </ul>
          </li>
          <li><strong>Dependency Chain Attacks:</strong>
            <ul>
              <li>Transitive dependency exploitation</li>
              <li>Version pinning and update attacks</li>
              <li>Dependency resolution manipulation</li>
              <li>Lock file and manifest tampering</li>
              <li>Mirror and proxy repository attacks</li>
            </ul>
          </li>
          <li><strong>Open Source Software Attacks:</strong>
            <ul>
              <li>Popular library and framework targeting</li>
              <li>Maintainer social engineering</li>
              <li>Abandoned project takeover</li>
              <li>Pull request and contribution attacks</li>
              <li>Issue tracker and communication compromise</li>
            </ul>
          </li>
        </ul>
        
        <h3>Code Signing and Certificate Attacks</h3>
        <ul>
          <li><strong>Certificate Authority Compromise:</strong>
            <ul>
              <li>Root and intermediate CA attacks</li>
              <li>Certificate issuance process manipulation</li>
              <li>Rogue certificate generation</li>
              <li>Certificate transparency log manipulation</li>
              <li>Trust store and root certificate attacks</li>
            </ul>
          </li>
          <li><strong>Code Signing Key Theft:</strong>
            <ul>
              <li>Developer and organization key extraction</li>
              <li>Hardware security module (HSM) attacks</li>
              <li>Key escrow and backup compromise</li>
              <li>Certificate and key management attacks</li>
              <li>Timestamp authority compromise</li>
            </ul>
          </li>
          <li><strong>Signature Validation Bypass:</strong>
            <ul>
              <li>Signature verification logic flaws</li>
              <li>Certificate chain validation bypass</li>
              <li>Revocation checking evasion</li>
              <li>Time and date manipulation attacks</li>
              <li>Alternative signature scheme abuse</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Hardware and Infrastructure Attacks",
      content: `
        <h2>Hardware Supply Chain and Infrastructure Compromise</h2>
        <p>Hardware supply chain attacks target the physical components and infrastructure that form the foundation of computing systems.</p>
        
        <h3>Manufacturing and Production Attacks</h3>
        <ul>
          <li><strong>Chip and Component Modification:</strong>
            <ul>
              <li>Silicon-level trojan insertion</li>
              <li>Microprocessor and controller modification</li>
              <li>Memory and storage device tampering</li>
              <li>Network and communication chip attacks</li>
              <li>Sensor and peripheral device modification</li>
            </ul>
          </li>
          <li><strong>Manufacturing Process Compromise:</strong>
            <ul>
              <li>Foundry and fabrication facility infiltration</li>
              <li>Design and mask modification</li>
              <li>Production line and assembly attacks</li>
              <li>Quality control and testing bypass</li>
              <li>Supply chain logistics manipulation</li>
            </ul>
          </li>
          <li><strong>Hardware Trojan Techniques:</strong>
            <ul>
              <li>Analog and digital trojan circuits</li>
              <li>Trigger-based and always-on trojans</li>
              <li>Functional and parametric trojans</li>
              <li>Covert channel and side-channel trojans</li>
              <li>Self-destructing and stealth trojans</li>
            </ul>
          </li>
        </ul>
        
        <h3>Firmware and BIOS/UEFI Attacks</h3>
        <ul>
          <li><strong>Firmware Supply Chain Compromise:</strong>
            <ul>
              <li>Firmware development environment attacks</li>
              <li>Firmware signing and validation bypass</li>
              <li>Update and distribution mechanism compromise</li>
              <li>Vendor and OEM firmware modification</li>
              <li>Third-party firmware component attacks</li>
            </ul>
          </li>
          <li><strong>BIOS and UEFI Implants:</strong>
            <ul>
              <li>Boot process and initialization attacks</li>
              <li>Secure Boot and measured boot bypass</li>
              <li>Option ROM and driver modification</li>
              <li>System Management Mode (SMM) attacks</li>
              <li>Platform Configuration Register (PCR) manipulation</li>
            </ul>
          </li>
          <li><strong>Embedded System Attacks:</strong>
            <ul>
              <li>IoT device firmware modification</li>
              <li>Industrial control system attacks</li>
              <li>Network equipment firmware compromise</li>
              <li>Mobile device baseband attacks</li>
              <li>Automotive and transportation system attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>Infrastructure and Service Provider Attacks</h3>
        <ul>
          <li><strong>Cloud Service Provider Compromise:</strong>
            <ul>
              <li>Hypervisor and virtualization attacks</li>
              <li>Container and orchestration platform attacks</li>
              <li>Shared infrastructure and multi-tenancy attacks</li>
              <li>Cloud management and control plane attacks</li>
              <li>Data center and physical infrastructure attacks</li>
            </ul>
          </li>
          <li><strong>Managed Service Provider (MSP) Attacks:</strong>
            <ul>
              <li>Remote monitoring and management (RMM) tools</li>
              <li>Centralized management and deployment systems</li>
              <li>Customer environment access and credentials</li>
              <li>Service delivery and support channel attacks</li>
              <li>Multi-tenant service platform compromise</li>
            </ul>
          </li>
          <li><strong>Telecommunications and Network Attacks:</strong>
            <ul>
              <li>Internet service provider (ISP) compromise</li>
              <li>Content delivery network (CDN) attacks</li>
              <li>Domain name system (DNS) infrastructure attacks</li>
              <li>Certificate authority and PKI attacks</li>
              <li>Submarine cable and backbone attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>Supply Chain Interdiction and Modification</h3>
        <ul>
          <li><strong>Physical Interdiction Techniques:</strong>
            <ul>
              <li>Shipping and logistics interception</li>
              <li>Customs and border control exploitation</li>
              <li>Warehouse and distribution center attacks</li>
              <li>Retail and reseller channel compromise</li>
              <li>End-user delivery and installation attacks</li>
            </ul>
          </li>
          <li><strong>Hardware Modification Methods:</strong>
            <ul>
              <li>Component replacement and substitution</li>
              <li>Circuit board and PCB modification</li>
              <li>Connector and interface tampering</li>
              <li>Firmware and software implantation</li>
              <li>Packaging and labeling manipulation</li>
            </ul>
          </li>
          <li><strong>Detection and Anti-Tamper Evasion:</strong>
            <ul>
              <li>Tamper-evident seal bypass</li>
              <li>Security hologram and marking replication</li>
              <li>X-ray and inspection evasion</li>
              <li>Electromagnetic and RF signature masking</li>
              <li>Thermal and optical signature manipulation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Supply Chain Attack Simulation Lab",
    description: "Hands-on exercise in supply chain attack techniques including software compromise, dependency poisoning, and infrastructure attacks.",
    tasks: [
      {
        category: "Software Supply Chain",
        commands: [
          {
            command: "Perform dependency confusion attack",
            description: "Create malicious package to exploit dependency confusion",
            hint: "Research internal package names and create public packages with higher versions",
            expectedOutput: "Successful dependency confusion leading to malicious package installation"
          },
          {
            command: "Compromise CI/CD pipeline",
            description: "Inject malicious code through build system compromise",
            hint: "Target build scripts, environment variables, or pipeline configurations",
            expectedOutput: "Malicious code injection through automated build process"
          }
        ]
      },
      {
        category: "Hardware Supply Chain",
        commands: [
          {
            command: "Analyze firmware for supply chain compromise",
            description: "Extract and analyze firmware for potential backdoors",
            hint: "Use binwalk, strings, and disassemblers to analyze firmware",
            expectedOutput: "Identification of potential supply chain compromises in firmware"
          },
          {
            command: "Simulate hardware trojan insertion",
            description: "Design theoretical hardware trojan for specific component",
            hint: "Consider trigger mechanisms, payload delivery, and stealth features",
            expectedOutput: "Detailed hardware trojan design and implementation plan"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which supply chain attack vector has the highest potential impact on multiple organizations?",
      options: [
        "Individual developer workstation compromise",
        "Popular open-source library compromise",
        "Single vendor hardware modification",
        "Local build system attack"
      ],
      correct: 1,
      explanation: "Popular open-source library compromise has the highest potential impact because widely-used libraries can affect thousands of downstream applications and organizations simultaneously."
    },
    {
      question: "What is the primary advantage of hardware supply chain attacks over software attacks?",
      options: [
        "Easier to implement",
        "Lower cost",
        "Persistence below the operating system level",
        "Faster execution"
      ],
      correct: 2,
      explanation: "Hardware supply chain attacks provide persistence below the operating system level, making them extremely difficult to detect and remove since they operate at the firmware or silicon level."
    },
    {
      question: "Which technique is most effective for detecting supply chain compromises in software dependencies?",
      options: [
        "Static code analysis",
        "Runtime monitoring",
        "Software Bill of Materials (SBOM) and integrity verification",
        "Network traffic analysis"
      ],
      correct: 2,
      explanation: "Software Bill of Materials (SBOM) and integrity verification are most effective because they provide visibility into all components and can detect unauthorized modifications through cryptographic verification."
    }
  ]
};
