/**
 * Advanced Persistence Techniques Module
 */

export const advancedPersistenceContent = {
  id: "rt-21",
  pathId: "red-teaming",
  title: "Advanced Persistence Techniques",
  description: "Master sophisticated persistence mechanisms including rootkits, firmware persistence, and advanced stealth techniques for long-term access maintenance.",
  objectives: [
    "Understand advanced persistence concepts and methodologies",
    "Master rootkit development and deployment techniques",
    "Learn firmware and hardware-level persistence",
    "Explore cloud and container persistence strategies",
    "Understand supply chain and software persistence",
    "Master detection evasion and anti-forensics techniques"
  ],
  difficulty: "Expert",
  estimatedTime: 320,
  sections: [
    {
      title: "Advanced Persistence Fundamentals",
      content: `
        <h2>Advanced Persistence Concepts and Methodologies</h2>
        <p>Advanced persistence techniques go beyond traditional methods to establish deep, resilient, and stealthy access that survives security updates, system rebuilds, and forensic analysis.</p>
        
        <h3>Persistence Classification Framework</h3>
        <ul>
          <li><strong>Persistence Levels:</strong>
            <ul>
              <li>Application-level persistence (user space)</li>
              <li>Operating system persistence (kernel space)</li>
              <li>Firmware and BIOS/UEFI persistence</li>
              <li>Hardware and silicon-level persistence</li>
              <li>Network infrastructure persistence</li>
            </ul>
          </li>
          <li><strong>Persistence Characteristics:</strong>
            <ul>
              <li>Stealth and detection evasion capabilities</li>
              <li>Resilience against removal attempts</li>
              <li>Self-healing and recovery mechanisms</li>
              <li>Cross-platform and cross-architecture support</li>
              <li>Minimal resource footprint and impact</li>
            </ul>
          </li>
          <li><strong>Persistence Lifecycle:</strong>
            <ul>
              <li>Initial implantation and establishment</li>
              <li>Activation and dormancy cycles</li>
              <li>Communication and command reception</li>
              <li>Self-preservation and adaptation</li>
              <li>Cleanup and evidence removal</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Persistence Strategies</h3>
        <ul>
          <li><strong>Multi-Layer Persistence:</strong>
            <ul>
              <li>Redundant persistence across multiple layers</li>
              <li>Cross-layer communication and coordination</li>
              <li>Hierarchical persistence with fallback mechanisms</li>
              <li>Distributed persistence across multiple systems</li>
              <li>Time-delayed and conditional activation</li>
            </ul>
          </li>
          <li><strong>Adaptive Persistence:</strong>
            <ul>
              <li>Environment-aware persistence selection</li>
              <li>Dynamic persistence method switching</li>
              <li>Threat landscape adaptation</li>
              <li>Security control evasion evolution</li>
              <li>Machine learning-driven persistence</li>
            </ul>
          </li>
          <li><strong>Covert Persistence:</strong>
            <ul>
              <li>Legitimate process and service mimicry</li>
              <li>Steganographic hiding techniques</li>
              <li>Timing-based and event-driven activation</li>
              <li>Memory-only and fileless persistence</li>
              <li>Supply chain and trusted component abuse</li>
            </ul>
          </li>
        </ul>
        
        <h3>Persistence Threat Modeling</h3>
        <ul>
          <li><strong>Defender Capabilities Assessment:</strong>
            <ul>
              <li>Security tool and technology inventory</li>
              <li>Detection and response capabilities</li>
              <li>Forensic and analysis capabilities</li>
              <li>Incident response procedures and timelines</li>
              <li>Security awareness and training levels</li>
            </ul>
          </li>
          <li><strong>Environment Analysis:</strong>
            <ul>
              <li>System architecture and configuration</li>
              <li>Update and patching procedures</li>
              <li>Backup and recovery processes</li>
              <li>Network segmentation and monitoring</li>
              <li>Compliance and regulatory requirements</li>
            </ul>
          </li>
          <li><strong>Risk-Benefit Analysis:</strong>
            <ul>
              <li>Persistence value vs detection risk</li>
              <li>Resource requirements and constraints</li>
              <li>Operational security considerations</li>
              <li>Mission timeline and objectives</li>
              <li>Exit strategy and cleanup planning</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Rootkit Development and Deployment",
      content: `
        <h2>Advanced Rootkit Techniques</h2>
        <p>Rootkits provide deep system-level persistence by operating at the kernel level and below, offering powerful stealth and persistence capabilities.</p>
        
        <h3>Kernel-Level Rootkits</h3>
        <ul>
          <li><strong>Windows Kernel Rootkits:</strong>
            <ul>
              <li>Kernel driver development and loading</li>
              <li>System Service Descriptor Table (SSDT) hooking</li>
              <li>Kernel API hooking and inline patching</li>
              <li>Direct Kernel Object Manipulation (DKOM)</li>
              <li>Filter driver and minifilter exploitation</li>
            </ul>
          </li>
          <li><strong>Linux Kernel Rootkits:</strong>
            <ul>
              <li>Loadable Kernel Module (LKM) development</li>
              <li>System call table manipulation</li>
              <li>Kernel symbol table hiding</li>
              <li>Process and file hiding techniques</li>
              <li>Network traffic interception and hiding</li>
            </ul>
          </li>
          <li><strong>Cross-Platform Techniques:</strong>
            <ul>
              <li>Hypervisor-based rootkits (Blue Pill, SubVirt)</li>
              <li>Hardware abstraction layer manipulation</li>
              <li>Interrupt descriptor table (IDT) hooking</li>
              <li>Memory management unit (MMU) exploitation</li>
              <li>CPU microcode and firmware modification</li>
            </ul>
          </li>
        </ul>
        
        <h3>User-Mode Rootkits</h3>
        <ul>
          <li><strong>DLL Injection and Hooking:</strong>
            <ul>
              <li>API hooking and function interception</li>
              <li>Import Address Table (IAT) manipulation</li>
              <li>Export Address Table (EAT) modification</li>
              <li>Inline function hooking and trampolines</li>
              <li>COM interface hijacking</li>
            </ul>
          </li>
          <li><strong>Process and Thread Manipulation:</strong>
            <ul>
              <li>Process hollowing and replacement</li>
              <li>Thread execution hijacking</li>
              <li>Asynchronous Procedure Call (APC) injection</li>
              <li>Manual DLL mapping and loading</li>
              <li>Reflective DLL injection techniques</li>
            </ul>
          </li>
          <li><strong>Registry and File System Hiding:</strong>
            <ul>
              <li>Registry key and value hiding</li>
              <li>File and directory cloaking</li>
              <li>Alternate Data Stream (ADS) abuse</li>
              <li>Volume Shadow Copy manipulation</li>
              <li>File system filter driver exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Bootkit and Firmware Rootkits</h3>
        <ul>
          <li><strong>Master Boot Record (MBR) Rootkits:</strong>
            <ul>
              <li>MBR infection and modification</li>
              <li>Boot sector manipulation</li>
              <li>Volume Boot Record (VBR) infection</li>
              <li>Partition table manipulation</li>
              <li>Legacy BIOS exploitation</li>
            </ul>
          </li>
          <li><strong>UEFI Rootkits:</strong>
            <ul>
              <li>UEFI firmware modification</li>
              <li>Boot Service and Runtime Service hooking</li>
              <li>System Management Mode (SMM) exploitation</li>
              <li>Secure Boot bypass techniques</li>
              <li>Platform Configuration Register (PCR) manipulation</li>
            </ul>
          </li>
          <li><strong>Hardware-Level Persistence:</strong>
            <ul>
              <li>Network interface card (NIC) firmware modification</li>
              <li>Hard drive firmware manipulation</li>
              <li>Graphics card and peripheral exploitation</li>
              <li>Baseboard Management Controller (BMC) compromise</li>
              <li>CPU microcode and silicon-level implants</li>
            </ul>
          </li>
        </ul>
        
        <h3>Rootkit Detection Evasion</h3>
        <ul>
          <li><strong>Anti-Analysis Techniques:</strong>
            <ul>
              <li>Virtual machine and sandbox detection</li>
              <li>Debugger and analysis tool detection</li>
              <li>Timing-based and behavioral analysis evasion</li>
              <li>Code obfuscation and packing</li>
              <li>Self-modifying and polymorphic code</li>
            </ul>
          </li>
          <li><strong>Stealth Mechanisms:</strong>
            <ul>
              <li>Minimal system footprint and resource usage</li>
              <li>Legitimate process and service mimicry</li>
              <li>Encrypted and compressed payloads</li>
              <li>Dormancy and activation triggers</li>
              <li>Communication channel obfuscation</li>
            </ul>
          </li>
          <li><strong>Persistence Protection:</strong>
            <ul>
              <li>Self-healing and recovery mechanisms</li>
              <li>Redundant installation and backup copies</li>
              <li>Tamper detection and response</li>
              <li>Privilege escalation and re-establishment</li>
              <li>Evidence cleanup and anti-forensics</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Cloud and Container Persistence",
      content: `
        <h2>Cloud and Container Persistence Strategies</h2>
        <p>Modern cloud and containerized environments require specialized persistence techniques that leverage cloud-native services and container orchestration platforms.</p>
        
        <h3>Cloud Service Persistence</h3>
        <ul>
          <li><strong>Identity and Access Management Persistence:</strong>
            <ul>
              <li>Service account and role creation</li>
              <li>API key and access token generation</li>
              <li>Cross-account role assumption chains</li>
              <li>Federated identity provider manipulation</li>
              <li>Certificate-based authentication persistence</li>
            </ul>
          </li>
          <li><strong>Compute Service Persistence:</strong>
            <ul>
              <li>Virtual machine image modification</li>
              <li>Auto-scaling group and launch template abuse</li>
              <li>Serverless function persistence</li>
              <li>Container image poisoning</li>
              <li>Spot instance and preemptible VM exploitation</li>
            </ul>
          </li>
          <li><strong>Storage and Database Persistence:</strong>
            <ul>
              <li>Object storage bucket manipulation</li>
              <li>Database stored procedures and triggers</li>
              <li>Backup and snapshot modification</li>
              <li>Content delivery network (CDN) abuse</li>
              <li>Data pipeline and ETL process manipulation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Container Orchestration Persistence</h3>
        <ul>
          <li><strong>Kubernetes Persistence:</strong>
            <ul>
              <li>Pod and deployment manifest modification</li>
              <li>ConfigMap and Secret manipulation</li>
              <li>Persistent Volume and storage class abuse</li>
              <li>Service account and RBAC exploitation</li>
              <li>Admission controller and webhook persistence</li>
            </ul>
          </li>
          <li><strong>Docker Swarm and Compose:</strong>
            <ul>
              <li>Service definition modification</li>
              <li>Stack and compose file manipulation</li>
              <li>Volume and network persistence</li>
              <li>Secret and configuration management abuse</li>
              <li>Node and manager compromise</li>
            </ul>
          </li>
          <li><strong>Container Runtime Persistence:</strong>
            <ul>
              <li>Container image layer modification</li>
              <li>Runtime security policy bypass</li>
              <li>Container escape and host persistence</li>
              <li>Registry and image repository poisoning</li>
              <li>Build pipeline and CI/CD compromise</li>
            </ul>
          </li>
        </ul>
        
        <h3>Serverless and Function Persistence</h3>
        <ul>
          <li><strong>Function-as-a-Service (FaaS) Persistence:</strong>
            <ul>
              <li>Function code and dependency modification</li>
              <li>Environment variable and configuration abuse</li>
              <li>Event trigger and scheduling manipulation</li>
              <li>Layer and runtime modification</li>
              <li>Cold start and warm instance exploitation</li>
            </ul>
          </li>
          <li><strong>Event-Driven Persistence:</strong>
            <ul>
              <li>Event source and trigger manipulation</li>
              <li>Message queue and streaming persistence</li>
              <li>Workflow and orchestration abuse</li>
              <li>API gateway and proxy modification</li>
              <li>Monitoring and alerting system compromise</li>
            </ul>
          </li>
          <li><strong>Microservice Persistence:</strong>
            <ul>
              <li>Service mesh and sidecar injection</li>
              <li>Inter-service communication manipulation</li>
              <li>Service discovery and registry abuse</li>
              <li>Load balancer and traffic routing modification</li>
              <li>Circuit breaker and resilience pattern exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Supply Chain and Software Persistence</h3>
        <ul>
          <li><strong>Software Supply Chain Compromise:</strong>
            <ul>
              <li>Package repository and dependency poisoning</li>
              <li>Build system and CI/CD pipeline compromise</li>
              <li>Code signing certificate theft and abuse</li>
              <li>Update mechanism and auto-updater exploitation</li>
              <li>Third-party library and component modification</li>
            </ul>
          </li>
          <li><strong>Development Environment Persistence:</strong>
            <ul>
              <li>IDE and development tool modification</li>
              <li>Version control system compromise</li>
              <li>Build script and configuration manipulation</li>
              <li>Testing and quality assurance bypass</li>
              <li>Documentation and knowledge base poisoning</li>
            </ul>
          </li>
          <li><strong>Distribution and Deployment Persistence:</strong>
            <ul>
              <li>Software distribution platform compromise</li>
              <li>App store and marketplace manipulation</li>
              <li>Content delivery network (CDN) poisoning</li>
              <li>Mirror and repository server compromise</li>
              <li>Digital signature and certificate abuse</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Advanced Persistence Implementation Lab",
    description: "Hands-on exercise in developing and deploying advanced persistence mechanisms across multiple platforms and environments.",
    tasks: [
      {
        category: "Rootkit Development",
        commands: [
          {
            command: "Develop Windows kernel rootkit",
            description: "Create kernel driver with SSDT hooking capabilities",
            hint: "Use Windows Driver Kit (WDK) and implement stealth features",
            expectedOutput: "Functional kernel rootkit with process hiding"
          },
          {
            command: "Implement Linux LKM rootkit",
            description: "Create loadable kernel module with system call hooking",
            hint: "Use kernel development environment and syscall table manipulation",
            expectedOutput: "Working LKM rootkit with file and network hiding"
          }
        ]
      },
      {
        category: "Cloud Persistence",
        commands: [
          {
            command: "Establish AWS Lambda persistence",
            description: "Create persistent backdoor using serverless functions",
            hint: "Use CloudFormation or Terraform for infrastructure persistence",
            expectedOutput: "Resilient Lambda-based persistence mechanism"
          },
          {
            command: "Deploy Kubernetes persistence",
            description: "Implement container-based persistence in K8s cluster",
            hint: "Use DaemonSets, ConfigMaps, and RBAC for persistence",
            expectedOutput: "Kubernetes cluster persistence with stealth features"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which persistence technique provides the highest level of stealth and resilience?",
      options: [
        "Registry autorun entries",
        "Scheduled tasks",
        "UEFI firmware rootkits",
        "Service installation"
      ],
      correct: 2,
      explanation: "UEFI firmware rootkits provide the highest level of stealth and resilience because they operate below the operating system, survive OS reinstallation, and are extremely difficult to detect and remove."
    },
    {
      question: "What is the primary advantage of serverless function persistence in cloud environments?",
      options: [
        "Lower cost",
        "Better performance",
        "Event-driven activation and minimal footprint",
        "Easier implementation"
      ],
      correct: 2,
      explanation: "Event-driven activation and minimal footprint make serverless persistence highly effective because functions only execute when triggered, reducing detection chances while maintaining persistent access."
    },
    {
      question: "Which rootkit technique is most effective against modern endpoint detection and response (EDR) systems?",
      options: [
        "User-mode API hooking",
        "Kernel SSDT hooking",
        "Hypervisor-based rootkits",
        "Registry modification"
      ],
      correct: 2,
      explanation: "Hypervisor-based rootkits are most effective against EDR systems because they operate below the operating system and can intercept and modify all system activity before it reaches security tools."
    }
  ]
};
