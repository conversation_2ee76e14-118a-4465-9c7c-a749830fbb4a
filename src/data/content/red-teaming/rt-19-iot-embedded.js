/**
 * IoT & Embedded Systems Module
 */

export const iotEmbeddedContent = {
  id: "rt-19",
  pathId: "red-teaming",
  title: "IoT & Embedded Systems",
  description: "Master IoT and embedded systems security assessment including hardware analysis, firmware exploitation, and protocol attacks.",
  objectives: [
    "Understand IoT and embedded system architectures",
    "Master hardware analysis and reverse engineering",
    "Learn firmware extraction and analysis techniques",
    "Explore IoT protocol and communication attacks",
    "Understand industrial control system security",
    "Master IoT persistence and lateral movement"
  ],
  difficulty: "Advanced",
  estimatedTime: 280,
  sections: [
    {
      title: "IoT and Embedded Systems Fundamentals",
      content: `
        <h2>IoT and Embedded System Architecture</h2>
        <p>Understanding IoT and embedded system architectures is crucial for effective security assessment and exploitation of these resource-constrained devices.</p>
        
        <h3>IoT Device Categories</h3>
        <ul>
          <li><strong>Consumer IoT Devices:</strong>
            <ul>
              <li>Smart home devices (lights, thermostats, cameras)</li>
              <li>Wearable technology and fitness trackers</li>
              <li>Smart appliances and kitchen devices</li>
              <li>Entertainment and media streaming devices</li>
              <li>Personal assistants and voice controllers</li>
            </ul>
          </li>
          <li><strong>Industrial IoT (IIoT) Systems:</strong>
            <ul>
              <li>Supervisory Control and Data Acquisition (SCADA)</li>
              <li>Programmable Logic Controllers (PLCs)</li>
              <li>Human Machine Interfaces (HMIs)</li>
              <li>Distributed Control Systems (DCS)</li>
              <li>Safety Instrumented Systems (SIS)</li>
            </ul>
          </li>
          <li><strong>Infrastructure and Utility Systems:</strong>
            <ul>
              <li>Smart grid and energy management</li>
              <li>Water treatment and distribution</li>
              <li>Transportation and traffic control</li>
              <li>Building automation and HVAC</li>
              <li>Environmental monitoring systems</li>
            </ul>
          </li>
        </ul>
        
        <h3>Embedded System Components</h3>
        <ul>
          <li><strong>Hardware Components:</strong>
            <ul>
              <li>Microcontrollers and microprocessors</li>
              <li>Memory systems (Flash, RAM, EEPROM)</li>
              <li>Communication interfaces (UART, SPI, I2C)</li>
              <li>Sensors and actuators</li>
              <li>Power management and battery systems</li>
            </ul>
          </li>
          <li><strong>Software Stack:</strong>
            <ul>
              <li>Bootloaders and firmware initialization</li>
              <li>Real-time operating systems (RTOS)</li>
              <li>Device drivers and hardware abstraction</li>
              <li>Application software and business logic</li>
              <li>Communication protocols and networking</li>
            </ul>
          </li>
          <li><strong>Communication Technologies:</strong>
            <ul>
              <li>WiFi and Bluetooth connectivity</li>
              <li>Cellular (2G/3G/4G/5G) communication</li>
              <li>Low-power wide-area networks (LPWAN)</li>
              <li>Zigbee, Z-Wave, and mesh protocols</li>
              <li>LoRa, Sigfox, and NB-IoT</li>
            </ul>
          </li>
        </ul>
        
        <h3>IoT Security Challenges</h3>
        <ul>
          <li><strong>Resource Constraints:</strong>
            <ul>
              <li>Limited processing power and memory</li>
              <li>Battery life and power consumption</li>
              <li>Cost optimization and component selection</li>
              <li>Size and form factor limitations</li>
              <li>Real-time performance requirements</li>
            </ul>
          </li>
          <li><strong>Security Implementation Challenges:</strong>
            <ul>
              <li>Weak or absent encryption</li>
              <li>Default and hardcoded credentials</li>
              <li>Insecure communication protocols</li>
              <li>Lack of secure update mechanisms</li>
              <li>Insufficient access controls</li>
            </ul>
          </li>
          <li><strong>Lifecycle and Management Issues:</strong>
            <ul>
              <li>Long deployment lifecycles</li>
              <li>Difficult or impossible updates</li>
              <li>Remote and inaccessible locations</li>
              <li>Lack of monitoring and visibility</li>
              <li>End-of-life and disposal concerns</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Hardware Analysis and Firmware Extraction",
      content: `
        <h2>Hardware Analysis and Reverse Engineering</h2>
        <p>Physical hardware analysis and firmware extraction are fundamental skills for IoT and embedded system security assessment.</p>
        
        <h3>Hardware Reconnaissance and Analysis</h3>
        <ul>
          <li><strong>Physical Device Examination:</strong>
            <ul>
              <li>Visual inspection and component identification</li>
              <li>PCB layout analysis and trace following</li>
              <li>Chip identification and datasheet research</li>
              <li>Test point and debug interface discovery</li>
              <li>Connector and port identification</li>
            </ul>
          </li>
          <li><strong>Hardware Debugging Interfaces:</strong>
            <ul>
              <li>JTAG (Joint Test Action Group) interfaces</li>
              <li>SWD (Serial Wire Debug) connections</li>
              <li>UART (Universal Asynchronous Receiver-Transmitter)</li>
              <li>SPI (Serial Peripheral Interface) buses</li>
              <li>I2C (Inter-Integrated Circuit) communication</li>
            </ul>
          </li>
          <li><strong>Hardware Analysis Tools:</strong>
            <ul>
              <li>Multimeters and oscilloscopes</li>
              <li>Logic analyzers and protocol decoders</li>
              <li>Hot air stations and soldering equipment</li>
              <li>Microscopes and magnification tools</li>
              <li>Bus Pirate and hardware hacking tools</li>
            </ul>
          </li>
        </ul>
        
        <h3>Firmware Extraction Techniques</h3>
        <ul>
          <li><strong>Non-invasive Extraction:</strong>
            <ul>
              <li>Bootloader and recovery mode access</li>
              <li>Debug interface exploitation</li>
              <li>Over-the-air (OTA) update interception</li>
              <li>Network-based firmware download</li>
              <li>Vendor tools and software exploitation</li>
            </ul>
          </li>
          <li><strong>Invasive Extraction:</strong>
            <ul>
              <li>Flash memory chip removal and reading</li>
              <li>In-circuit programming and dumping</li>
              <li>JTAG boundary scan and memory access</li>
              <li>Voltage glitching and fault injection</li>
              <li>Decapsulation and die-level analysis</li>
            </ul>
          </li>
          <li><strong>Firmware Analysis Tools:</strong>
            <ul>
              <li>Binwalk for firmware analysis and extraction</li>
              <li>Firmware Analysis Toolkit (FAT)</li>
              <li>FACT (Firmware Analysis and Comparison Tool)</li>
              <li>Ghidra and IDA Pro for reverse engineering</li>
              <li>Custom scripts and automation tools</li>
            </ul>
          </li>
        </ul>
        
        <h3>Firmware Reverse Engineering</h3>
        <ul>
          <li><strong>Firmware Structure Analysis:</strong>
            <ul>
              <li>File system extraction and analysis</li>
              <li>Bootloader and kernel identification</li>
              <li>Application and library enumeration</li>
              <li>Configuration file and credential discovery</li>
              <li>Encryption key and certificate extraction</li>
            </ul>
          </li>
          <li><strong>Binary Analysis and Disassembly:</strong>
            <ul>
              <li>Architecture identification (ARM, MIPS, x86)</li>
              <li>Entry point and function discovery</li>
              <li>Control flow and data flow analysis</li>
              <li>String and constant extraction</li>
              <li>Vulnerability identification and exploitation</li>
            </ul>
          </li>
          <li><strong>Dynamic Analysis and Emulation:</strong>
            <ul>
              <li>QEMU and firmware emulation</li>
              <li>Dynamic instrumentation and debugging</li>
              <li>Runtime behavior analysis</li>
              <li>Memory and register examination</li>
              <li>Function hooking and interception</li>
            </ul>
          </li>
        </ul>
        
        <h3>Hardware Security Bypass Techniques</h3>
        <ul>
          <li><strong>Debug Interface Exploitation:</strong>
            <ul>
              <li>JTAG chain discovery and exploitation</li>
              <li>Boundary scan and memory access</li>
              <li>Debug authentication bypass</li>
              <li>Secure boot and chain of trust bypass</li>
              <li>Hardware security module (HSM) attacks</li>
            </ul>
          </li>
          <li><strong>Side-Channel Attacks:</strong>
            <ul>
              <li>Power analysis and differential power analysis</li>
              <li>Electromagnetic emanation analysis</li>
              <li>Timing attack exploitation</li>
              <li>Acoustic and optical side channels</li>
              <li>Cache timing and microarchitectural attacks</li>
            </ul>
          </li>
          <li><strong>Fault Injection Attacks:</strong>
            <ul>
              <li>Voltage glitching and power analysis</li>
              <li>Clock glitching and timing manipulation</li>
              <li>Electromagnetic fault injection</li>
              <li>Laser fault injection and optical attacks</li>
              <li>Temperature and environmental manipulation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "IoT Protocol and Communication Attacks",
      content: `
        <h2>IoT Protocol Security and Communication Attacks</h2>
        <p>IoT devices rely on various communication protocols that present unique attack surfaces and security challenges.</p>
        
        <h3>Wireless Protocol Attacks</h3>
        <ul>
          <li><strong>Zigbee and Z-Wave Attacks:</strong>
            <ul>
              <li>Network key extraction and decryption</li>
              <li>Device pairing and association attacks</li>
              <li>Replay and injection attacks</li>
              <li>Mesh network topology discovery</li>
              <li>Coordinator and hub compromise</li>
            </ul>
          </li>
          <li><strong>LoRa and LoRaWAN Exploitation:</strong>
            <ul>
              <li>Device key extraction and cloning</li>
              <li>Network server and gateway attacks</li>
              <li>Downlink command injection</li>
              <li>Join procedure manipulation</li>
              <li>Geolocation and tracking attacks</li>
            </ul>
          </li>
          <li><strong>Bluetooth and BLE IoT Attacks:</strong>
            <ul>
              <li>Device discovery and enumeration</li>
              <li>Pairing and bonding exploitation</li>
              <li>Service and characteristic abuse</li>
              <li>Man-in-the-middle attacks</li>
              <li>Firmware update interception</li>
            </ul>
          </li>
        </ul>
        
        <h3>Internet Protocol Attacks</h3>
        <ul>
          <li><strong>CoAP (Constrained Application Protocol):</strong>
            <ul>
              <li>Resource discovery and enumeration</li>
              <li>Observe and notification abuse</li>
              <li>Proxy and caching attacks</li>
              <li>DTLS security bypass</li>
              <li>Amplification and DoS attacks</li>
            </ul>
          </li>
          <li><strong>MQTT (Message Queuing Telemetry Transport):</strong>
            <ul>
              <li>Broker discovery and enumeration</li>
              <li>Topic subscription and publishing abuse</li>
              <li>Authentication and authorization bypass</li>
              <li>Message interception and manipulation</li>
              <li>Retained message and will message abuse</li>
            </ul>
          </li>
          <li><strong>HTTP/HTTPS and REST APIs:</strong>
            <ul>
              <li>API endpoint discovery and testing</li>
              <li>Authentication token extraction</li>
              <li>Parameter manipulation and injection</li>
              <li>Certificate validation bypass</li>
              <li>Firmware update manipulation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Industrial Protocol Attacks</h3>
        <ul>
          <li><strong>Modbus Protocol Exploitation:</strong>
            <ul>
              <li>Function code enumeration and abuse</li>
              <li>Register and coil manipulation</li>
              <li>Device identification and fingerprinting</li>
              <li>Man-in-the-middle attacks</li>
              <li>Denial of service and disruption</li>
            </ul>
          </li>
          <li><strong>DNP3 (Distributed Network Protocol):</strong>
            <ul>
              <li>Object and variation enumeration</li>
              <li>Authentication challenge bypass</li>
              <li>Unsolicited response manipulation</li>
              <li>Secure authentication exploitation</li>
              <li>Time synchronization attacks</li>
            </ul>
          </li>
          <li><strong>IEC 61850 and Power System Protocols:</strong>
            <ul>
              <li>Logical node and data object access</li>
              <li>GOOSE and SMV message manipulation</li>
              <li>MMS (Manufacturing Message Specification) attacks</li>
              <li>Configuration and setting modification</li>
              <li>Protection and control system disruption</li>
            </ul>
          </li>
        </ul>
        
        <h3>IoT Attack Tools and Frameworks</h3>
        <ul>
          <li><strong>Protocol Analysis Tools:</strong>
            <ul>
              <li>Wireshark with IoT protocol dissectors</li>
              <li>Scapy for custom packet crafting</li>
              <li>Killerbee for Zigbee analysis</li>
              <li>Universal Radio Hacker (URH)</li>
              <li>GNU Radio for signal processing</li>
            </ul>
          </li>
          <li><strong>IoT Security Frameworks:</strong>
            <ul>
              <li>IoT Inspector for automated analysis</li>
              <li>FACT for firmware analysis</li>
              <li>Routersploit for router exploitation</li>
              <li>IoTSeeker for device discovery</li>
              <li>Custom IoT testing frameworks</li>
            </ul>
          </li>
          <li><strong>Hardware Hacking Tools:</strong>
            <ul>
              <li>Bus Pirate for protocol analysis</li>
              <li>JTAGulator for JTAG discovery</li>
              <li>ChipWhisperer for side-channel attacks</li>
              <li>Proxmark3 for RFID/NFC analysis</li>
              <li>HackRF and SDR platforms</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "IoT and Embedded Systems Security Lab",
    description: "Hands-on exercise in IoT device analysis including firmware extraction, protocol analysis, and hardware exploitation.",
    tasks: [
      {
        category: "Firmware Analysis",
        commands: [
          {
            command: "Extract and analyze IoT device firmware",
            description: "Use binwalk to extract and analyze firmware components",
            hint: "Look for file systems, encryption, and embedded credentials",
            expectedOutput: "Complete firmware analysis with security findings"
          },
          {
            command: "Identify hardware debug interfaces",
            description: "Locate and access UART, JTAG, or SPI interfaces",
            hint: "Use multimeter and logic analyzer for interface discovery",
            expectedOutput: "Successful hardware interface access and exploitation"
          }
        ]
      },
      {
        category: "Protocol Exploitation",
        commands: [
          {
            command: "Analyze and exploit MQTT communication",
            description: "Intercept and manipulate MQTT messages",
            hint: "Use mosquitto tools and Wireshark for MQTT analysis",
            expectedOutput: "Successful MQTT message interception and manipulation"
          },
          {
            command: "Perform Zigbee network attack",
            description: "Capture and decrypt Zigbee network traffic",
            hint: "Use Killerbee or similar tools for Zigbee analysis",
            expectedOutput: "Zigbee network compromise and device control"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which hardware interface is most commonly found on IoT devices for debugging and firmware extraction?",
      options: [
        "JTAG",
        "UART",
        "SPI",
        "I2C"
      ],
      correct: 1,
      explanation: "UART is most commonly found on IoT devices because it's simple to implement, requires minimal pins, and is often used for console access and debugging during development."
    },
    {
      question: "What is the primary security weakness of the Zigbee protocol?",
      options: [
        "Lack of encryption",
        "Weak network key management",
        "No authentication mechanism",
        "Susceptible to jamming attacks"
      ],
      correct: 1,
      explanation: "Weak network key management is the primary security weakness because Zigbee often uses default or predictable network keys, and key distribution mechanisms can be exploited to compromise the entire network."
    },
    {
      question: "Which technique is most effective for extracting encryption keys from embedded devices?",
      options: [
        "Network traffic analysis",
        "Firmware reverse engineering",
        "Side-channel attacks",
        "Social engineering"
      ],
      correct: 2,
      explanation: "Side-channel attacks are most effective for key extraction because they can reveal cryptographic keys through physical characteristics like power consumption, electromagnetic emissions, or timing variations during cryptographic operations."
    }
  ]
};
