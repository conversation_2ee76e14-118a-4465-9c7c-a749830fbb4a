/**
 * Introduction to Red Teaming Module
 */

export const redTeamIntroContent = {
  id: "rt-1",
  pathId: "red-teaming",
  title: "Introduction to Red Team Operations",
  description: "Master the foundations of red team operations, adversary simulation, and advanced multi-stage attack campaigns against enterprise environments.",
  objectives: [
    "Understand red teaming vs penetration testing differences",
    "Learn the red team lifecycle and engagement methodology",
    "Master MITRE ATT&CK framework and adversary TTPs",
    "Understand operational security (OPSEC) in red team operations",
    "Explore advanced threat actor simulation techniques",
    "Learn red team planning and objective-based testing"
  ],
  difficulty: "Intermediate",
  estimatedTime: 120,
  sections: [
    {
      title: "Red Teaming Fundamentals",
      content: `
        <h2>Red Teaming Fundamentals</h2>
        <p>Red teaming is an advanced form of adversary simulation that mimics real-world threat actors to test an organization's detection and response capabilities.</p>
        
        <h3>Red Team vs Penetration Testing</h3>
        <ul>
          <li><strong>Scope:</strong>
            <ul>
              <li><strong>Penetration Testing:</strong> Technical vulnerability assessment with defined scope</li>
              <li><strong>Red Teaming:</strong> Holistic security assessment including people, processes, and technology</li>
            </ul>
          </li>
          <li><strong>Objectives:</strong>
            <ul>
              <li><strong>Penetration Testing:</strong> Find and exploit vulnerabilities</li>
              <li><strong>Red Teaming:</strong> Test detection and response capabilities</li>
            </ul>
          </li>
          <li><strong>Approach:</strong>
            <ul>
              <li><strong>Penetration Testing:</strong> Broad coverage, maximum vulnerability discovery</li>
              <li><strong>Red Teaming:</strong> Stealth-focused, goal-oriented, realistic adversary simulation</li>
            </ul>
          </li>
          <li><strong>Duration:</strong>
            <ul>
              <li><strong>Penetration Testing:</strong> Days to weeks</li>
              <li><strong>Red Teaming:</strong> Weeks to months</li>
            </ul>
          </li>
        </ul>
        
        <h3>Red Team Objectives</h3>
        <ul>
          <li><strong>Test Detection Capabilities:</strong> Evaluate security monitoring and alerting</li>
          <li><strong>Assess Response Procedures:</strong> Test incident response and containment</li>
          <li><strong>Validate Security Controls:</strong> Determine effectiveness of defensive measures</li>
          <li><strong>Improve Security Posture:</strong> Provide actionable recommendations</li>
          <li><strong>Train Blue Team:</strong> Enhance defensive team skills and procedures</li>
        </ul>
        
        <h3>Red Team Engagement Types</h3>
        <ul>
          <li><strong>Assumed Breach:</strong> Start with initial access, focus on lateral movement</li>
          <li><strong>Full-Scope:</strong> Complete attack lifecycle from reconnaissance to objectives</li>
          <li><strong>Targeted:</strong> Focus on specific assets, data, or business processes</li>
          <li><strong>Purple Team:</strong> Collaborative exercise with blue team</li>
          <li><strong>Tabletop:</strong> Simulation-based exercises without actual exploitation</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "MITRE ATT&CK Framework",
      content: `
        <h2>MITRE ATT&CK Framework</h2>
        <p>The MITRE ATT&CK framework provides a comprehensive matrix of adversary tactics, techniques, and procedures (TTPs) used in real-world attacks.</p>
        
        <h3>ATT&CK Tactics (The "Why")</h3>
        <ul>
          <li><strong>Initial Access:</strong> Gaining entry into the target environment</li>
          <li><strong>Execution:</strong> Running malicious code on target systems</li>
          <li><strong>Persistence:</strong> Maintaining access across system restarts</li>
          <li><strong>Privilege Escalation:</strong> Gaining higher-level permissions</li>
          <li><strong>Defense Evasion:</strong> Avoiding detection by security controls</li>
          <li><strong>Credential Access:</strong> Stealing account credentials</li>
          <li><strong>Discovery:</strong> Learning about the target environment</li>
          <li><strong>Lateral Movement:</strong> Moving through the network</li>
          <li><strong>Collection:</strong> Gathering information of interest</li>
          <li><strong>Command and Control:</strong> Communicating with compromised systems</li>
          <li><strong>Exfiltration:</strong> Stealing data from the network</li>
          <li><strong>Impact:</strong> Manipulating, interrupting, or destroying systems/data</li>
        </ul>
        
        <h3>ATT&CK Techniques (The "How")</h3>
        <ul>
          <li><strong>Technique Examples:</strong>
            <ul>
              <li>T1566 - Phishing (Initial Access)</li>
              <li>T1059 - Command and Scripting Interpreter (Execution)</li>
              <li>T1543 - Create or Modify System Process (Persistence)</li>
              <li>T1055 - Process Injection (Defense Evasion)</li>
              <li>T1003 - OS Credential Dumping (Credential Access)</li>
            </ul>
          </li>
        </ul>
        
        <h3>ATT&CK Sub-techniques (The "Details")</h3>
        <ul>
          <li><strong>Granular Implementation:</strong> Specific methods within techniques</li>
          <li><strong>Platform-Specific:</strong> Windows, Linux, macOS, cloud-specific variations</li>
          <li><strong>Detection Opportunities:</strong> Specific indicators and detection methods</li>
        </ul>
        
        <h3>Using ATT&CK in Red Team Operations</h3>
        <ul>
          <li><strong>Campaign Planning:</strong> Select appropriate TTPs for objectives</li>
          <li><strong>Adversary Emulation:</strong> Mimic specific threat actor behaviors</li>
          <li><strong>Coverage Analysis:</strong> Ensure comprehensive testing across tactics</li>
          <li><strong>Detection Testing:</strong> Validate security control effectiveness</li>
          <li><strong>Gap Identification:</strong> Identify areas lacking detection coverage</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Red Team Lifecycle",
      content: `
        <h2>Red Team Lifecycle</h2>
        <p>The red team lifecycle provides a structured approach to conducting sophisticated adversary simulation exercises.</p>
        
        <h3>Phase 1: Planning and Preparation</h3>
        <ul>
          <li><strong>Engagement Scoping:</strong>
            <ul>
              <li>Define objectives and success criteria</li>
              <li>Establish rules of engagement (RoE)</li>
              <li>Identify target systems and data</li>
              <li>Set timeline and resource requirements</li>
            </ul>
          </li>
          <li><strong>Threat Intelligence:</strong>
            <ul>
              <li>Research relevant threat actors</li>
              <li>Analyze applicable attack patterns</li>
              <li>Identify industry-specific threats</li>
              <li>Select appropriate adversary TTPs</li>
            </ul>
          </li>
          <li><strong>Team Preparation:</strong>
            <ul>
              <li>Assign roles and responsibilities</li>
              <li>Prepare tools and infrastructure</li>
              <li>Establish communication channels</li>
              <li>Plan operational security measures</li>
            </ul>
          </li>
        </ul>
        
        <h3>Phase 2: Initial Access</h3>
        <ul>
          <li><strong>Reconnaissance:</strong> Passive and active information gathering</li>
          <li><strong>Initial Compromise:</strong> Gaining first foothold in the environment</li>
          <li><strong>Command and Control:</strong> Establishing communication channels</li>
          <li><strong>Situational Awareness:</strong> Understanding the compromised environment</li>
        </ul>
        
        <h3>Phase 3: Establish Foothold</h3>
        <ul>
          <li><strong>Persistence:</strong> Maintaining access across system changes</li>
          <li><strong>Privilege Escalation:</strong> Gaining administrative privileges</li>
          <li><strong>Defense Evasion:</strong> Avoiding detection by security controls</li>
          <li><strong>Local Discovery:</strong> Mapping the immediate environment</li>
        </ul>
        
        <h3>Phase 4: Escalate Privileges</h3>
        <ul>
          <li><strong>Credential Harvesting:</strong> Collecting user credentials</li>
          <li><strong>Token Manipulation:</strong> Leveraging Windows access tokens</li>
          <li><strong>Exploitation:</strong> Using privilege escalation vulnerabilities</li>
          <li><strong>Alternative Authentication:</strong> Pass-the-hash, Kerberos attacks</li>
        </ul>
        
        <h3>Phase 5: Internal Reconnaissance</h3>
        <ul>
          <li><strong>Network Discovery:</strong> Mapping internal network topology</li>
          <li><strong>System Enumeration:</strong> Identifying hosts and services</li>
          <li><strong>Active Directory Reconnaissance:</strong> Mapping domain structure</li>
          <li><strong>Data Discovery:</strong> Locating sensitive information</li>
        </ul>
        
        <h3>Phase 6: Lateral Movement</h3>
        <ul>
          <li><strong>Credential Reuse:</strong> Using harvested credentials</li>
          <li><strong>Remote Services:</strong> Leveraging administrative protocols</li>
          <li><strong>Internal Phishing:</strong> Targeting internal users</li>
          <li><strong>Trust Relationships:</strong> Exploiting system trust</li>
        </ul>
        
        <h3>Phase 7: Maintain Presence</h3>
        <ul>
          <li><strong>Advanced Persistence:</strong> Surviving security updates and reboots</li>
          <li><strong>Backup Access:</strong> Multiple persistence mechanisms</li>
          <li><strong>Stealth Maintenance:</strong> Avoiding detection during long-term access</li>
          <li><strong>Living off the Land:</strong> Using legitimate tools for malicious purposes</li>
        </ul>
        
        <h3>Phase 8: Complete Mission</h3>
        <ul>
          <li><strong>Objective Achievement:</strong> Accomplishing defined goals</li>
          <li><strong>Data Collection:</strong> Gathering target information</li>
          <li><strong>Impact Demonstration:</strong> Showing potential consequences</li>
          <li><strong>Evidence Gathering:</strong> Documenting successful techniques</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Operational Security (OPSEC)",
      content: `
        <h2>Operational Security (OPSEC)</h2>
        <p>OPSEC is critical for red team operations to maintain stealth and avoid detection while conducting long-term campaigns.</p>
        
        <h3>OPSEC Principles</h3>
        <ul>
          <li><strong>Operational Discipline:</strong> Consistent application of security practices</li>
          <li><strong>Need-to-Know:</strong> Limiting information access to necessary personnel</li>
          <li><strong>Compartmentalization:</strong> Isolating operations and information</li>
          <li><strong>Plausible Deniability:</strong> Maintaining cover for actions</li>
          <li><strong>Minimal Footprint:</strong> Reducing detectable indicators</li>
        </ul>
        
        <h3>Technical OPSEC Measures</h3>
        <ul>
          <li><strong>Infrastructure Isolation:</strong>
            <ul>
              <li>Separate attack infrastructure from corporate networks</li>
              <li>Use dedicated systems for red team operations</li>
              <li>Implement network segmentation and VPNs</li>
            </ul>
          </li>
          <li><strong>Communication Security:</strong>
            <ul>
              <li>Encrypted communication channels</li>
              <li>Secure key exchange mechanisms</li>
              <li>Anonymous or pseudonymous identities</li>
            </ul>
          </li>
          <li><strong>Anti-Forensics:</strong>
            <ul>
              <li>Log clearing and tampering</li>
              <li>Timestomping and artifact manipulation</li>
              <li>Memory-only execution techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Behavioral OPSEC</h3>
        <ul>
          <li><strong>Timing Analysis:</strong> Operating during business hours</li>
          <li><strong>Volume Control:</strong> Limiting activity to avoid detection</li>
          <li><strong>Pattern Avoidance:</strong> Varying techniques and timing</li>
          <li><strong>Legitimate Behavior:</strong> Mimicking normal user activities</li>
        </ul>
        
        <h3>OPSEC Failures and Consequences</h3>
        <ul>
          <li><strong>Detection and Response:</strong> Premature discovery of operations</li>
          <li><strong>Attribution:</strong> Linking activities to red team</li>
          <li><strong>Containment:</strong> Loss of access and operational capability</li>
          <li><strong>Intelligence Loss:</strong> Revealing tactics and techniques</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Red Team Planning and MITRE ATT&CK Lab",
    description: "Hands-on exercise in red team planning using MITRE ATT&CK framework for adversary simulation design.",
    tasks: [
      {
        category: "ATT&CK Framework Analysis",
        commands: [
          {
            command: "Browse MITRE ATT&CK Enterprise Matrix",
            description: "Explore the MITRE ATT&CK framework online",
            hint: "Focus on understanding tactic-technique relationships",
            expectedOutput: "Familiarity with ATT&CK structure and navigation"
          }
        ]
      },
      {
        category: "Threat Actor Research",
        commands: [
          {
            command: "Research APT29 (Cozy Bear) TTPs",
            description: "Analyze a real threat actor's techniques using ATT&CK",
            hint: "Look for commonly used techniques and patterns",
            expectedOutput: "Understanding of threat actor behavior modeling"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary difference between red teaming and penetration testing?",
      options: [
        "Red teaming uses more advanced tools",
        "Red teaming focuses on testing detection and response capabilities",
        "Red teaming only targets external systems",
        "Red teaming is faster than penetration testing"
      ],
      correct: 1,
      explanation: "Red teaming focuses on testing an organization's detection and response capabilities through realistic adversary simulation, while penetration testing focuses on finding and exploiting vulnerabilities."
    },
    {
      question: "In the MITRE ATT&CK framework, what does a 'Tactic' represent?",
      options: [
        "The specific tool used in an attack",
        "The technical implementation of an attack",
        "The adversary's goal or objective",
        "The target system being attacked"
      ],
      correct: 2,
      explanation: "A tactic in MITRE ATT&CK represents the adversary's goal or objective (the 'why'), such as Initial Access, Persistence, or Lateral Movement."
    },
    {
      question: "Which OPSEC principle is most important for maintaining stealth in long-term red team operations?",
      options: [
        "Using the latest exploit tools",
        "Maintaining minimal footprint and avoiding detection patterns",
        "Documenting all activities in detail",
        "Working only during weekend hours"
      ],
      correct: 1,
      explanation: "Maintaining a minimal footprint and avoiding detectable patterns is crucial for long-term stealth in red team operations, as it reduces the chances of discovery by security monitoring."
    }
  ]
}; 