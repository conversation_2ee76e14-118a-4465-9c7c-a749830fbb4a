/**
 * Windows Exploitation Module
 */

export const windowsExploitationContent = {
  id: "rt-11",
  pathId: "red-teaming",
  title: "Windows Exploitation",
  description: "Master advanced Windows system exploitation techniques including privilege escalation, token manipulation, and process injection.",
  objectives: [
    "Understand Windows internals and security architecture",
    "Master Windows privilege escalation techniques",
    "Learn token manipulation and impersonation",
    "Explore process injection and code execution methods",
    "Understand Windows exploit development fundamentals",
    "Master post-exploitation techniques and persistence"
  ],
  difficulty: "Advanced",
  estimatedTime: 280,
  sections: [
    {
      title: "Windows Internals and Security Architecture",
      content: `
        <h2>Windows Internals and Security Architecture</h2>
        <p>Understanding Windows internals is crucial for effective exploitation and post-exploitation activities in Windows environments.</p>
        
        <h3>Windows Architecture Overview</h3>
        <ul>
          <li><strong>Kernel Mode vs User Mode:</strong>
            <ul>
              <li>Ring 0 (Kernel) - Full system access</li>
              <li>Ring 3 (User) - Limited application access</li>
              <li>System call interface and transitions</li>
              <li>Hardware abstraction layer (HAL)</li>
              <li>Executive services and subsystems</li>
            </ul>
          </li>
          <li><strong>Windows Security Model:</strong>
            <ul>
              <li>Security Reference Monitor (SRM)</li>
              <li>Local Security Authority (LSA)</li>
              <li>Security Account Manager (SAM)</li>
              <li>Access Control Lists (ACLs)</li>
              <li>Security Identifiers (SIDs)</li>
            </ul>
          </li>
          <li><strong>Process and Thread Management:</strong>
            <ul>
              <li>Process creation and termination</li>
              <li>Thread scheduling and execution</li>
              <li>Virtual memory management</li>
              <li>Handle and object management</li>
              <li>Inter-process communication (IPC)</li>
            </ul>
          </li>
        </ul>
        
        <h3>Windows Authentication and Authorization</h3>
        <ul>
          <li><strong>Authentication Mechanisms:</strong>
            <ul>
              <li>NTLM (NT LAN Manager) protocol</li>
              <li>Kerberos authentication</li>
              <li>Certificate-based authentication</li>
              <li>Smart card and biometric authentication</li>
              <li>Multi-factor authentication (MFA)</li>
            </ul>
          </li>
          <li><strong>Access Tokens and Privileges:</strong>
            <ul>
              <li>Primary and impersonation tokens</li>
              <li>Token structure and components</li>
              <li>Privilege escalation mechanisms</li>
              <li>User Account Control (UAC)</li>
              <li>Mandatory Integrity Control (MIC)</li>
            </ul>
          </li>
          <li><strong>Windows Rights and Privileges:</strong>
            <ul>
              <li>SeDebugPrivilege - Debug programs</li>
              <li>SeImpersonatePrivilege - Impersonate clients</li>
              <li>SeLoadDriverPrivilege - Load device drivers</li>
              <li>SeTakeOwnershipPrivilege - Take ownership</li>
              <li>SeBackupPrivilege - Backup files and directories</li>
            </ul>
          </li>
        </ul>
        
        <h3>Windows Security Features</h3>
        <ul>
          <li><strong>Address Space Layout Randomization (ASLR):</strong>
            <ul>
              <li>Memory layout randomization</li>
              <li>Base address randomization</li>
              <li>Stack and heap randomization</li>
              <li>ASLR bypass techniques</li>
              <li>High entropy ASLR</li>
            </ul>
          </li>
          <li><strong>Data Execution Prevention (DEP):</strong>
            <ul>
              <li>Hardware and software DEP</li>
              <li>No-execute (NX) bit enforcement</li>
              <li>DEP bypass techniques</li>
              <li>Return-oriented programming (ROP)</li>
              <li>Jump-oriented programming (JOP)</li>
            </ul>
          </li>
          <li><strong>Control Flow Guard (CFG):</strong>
            <ul>
              <li>Indirect call protection</li>
              <li>CFG bitmap and validation</li>
              <li>CFG bypass techniques</li>
              <li>Return Flow Guard (RFG)</li>
              <li>Hardware-enforced stack protection</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Windows Privilege Escalation Techniques",
      content: `
        <h2>Windows Privilege Escalation Techniques</h2>
        <p>Privilege escalation is essential for gaining administrative access and expanding control over Windows systems.</p>
        
        <h3>Service-based Privilege Escalation</h3>
        <ul>
          <li><strong>Unquoted Service Paths:</strong>
            <ul>
              <li>Service path parsing vulnerabilities</li>
              <li>DLL search order exploitation</li>
              <li>Service binary replacement</li>
              <li>Service configuration manipulation</li>
              <li>Service restart and execution</li>
            </ul>
          </li>
          <li><strong>Service Permissions Weakness:</strong>
            <ul>
              <li>Weak service ACLs and permissions</li>
              <li>Service modification rights</li>
              <li>Service binary path changes</li>
              <li>Service account impersonation</li>
              <li>Service dependency exploitation</li>
            </ul>
          </li>
          <li><strong>Service Account Exploitation:</strong>
            <ul>
              <li>LocalSystem account privileges</li>
              <li>Network Service account abuse</li>
              <li>Service account token theft</li>
              <li>Service impersonation attacks</li>
              <li>Service account credential extraction</li>
            </ul>
          </li>
        </ul>
        
        <h3>Registry and File System Exploitation</h3>
        <ul>
          <li><strong>Registry Privilege Escalation:</strong>
            <ul>
              <li>Registry key permissions weakness</li>
              <li>AlwaysInstallElevated exploitation</li>
              <li>Registry autorun manipulation</li>
              <li>Service registry modification</li>
              <li>Registry hive manipulation</li>
            </ul>
          </li>
          <li><strong>File System Permissions:</strong>
            <ul>
              <li>Weak file and folder permissions</li>
              <li>DLL hijacking opportunities</li>
              <li>Binary planting attacks</li>
              <li>Symbolic link attacks</li>
              <li>Junction point exploitation</li>
            </ul>
          </li>
          <li><strong>Scheduled Task Exploitation:</strong>
            <ul>
              <li>Weak scheduled task permissions</li>
              <li>Task binary replacement</li>
              <li>Task configuration modification</li>
              <li>Task trigger manipulation</li>
              <li>Task account impersonation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Kernel and Driver Exploitation</h3>
        <ul>
          <li><strong>Kernel Vulnerabilities:</strong>
            <ul>
              <li>Kernel pool corruption</li>
              <li>Use-after-free vulnerabilities</li>
              <li>Integer overflow exploitation</li>
              <li>Kernel stack overflow</li>
              <li>Kernel information disclosure</li>
            </ul>
          </li>
          <li><strong>Driver Exploitation:</strong>
            <ul>
              <li>Third-party driver vulnerabilities</li>
              <li>Driver signature bypass</li>
              <li>Driver loading and unloading</li>
              <li>Driver communication exploitation</li>
              <li>Hardware driver abuse</li>
            </ul>
          </li>
          <li><strong>Exploit Techniques:</strong>
            <ul>
              <li>Kernel shellcode development</li>
              <li>SMEP and SMAP bypass</li>
              <li>Kernel ASLR bypass</li>
              <li>Token privilege escalation</li>
              <li>Process token replacement</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Token Manipulation and Process Injection",
      content: `
        <h2>Token Manipulation and Process Injection</h2>
        <p>Advanced post-exploitation techniques for maintaining access and executing code in the context of other processes.</p>
        
        <h3>Access Token Manipulation</h3>
        <ul>
          <li><strong>Token Types and Structure:</strong>
            <ul>
              <li>Primary tokens vs impersonation tokens</li>
              <li>Token security attributes</li>
              <li>Token privileges and groups</li>
              <li>Token integrity levels</li>
              <li>Token session information</li>
            </ul>
          </li>
          <li><strong>Token Theft and Duplication:</strong>
            <ul>
              <li>Process token enumeration</li>
              <li>Token duplication techniques</li>
              <li>Token impersonation methods</li>
              <li>Token privilege adjustment</li>
              <li>Token security descriptor modification</li>
            </ul>
          </li>
          <li><strong>Token-based Attacks:</strong>
            <ul>
              <li>Pass-the-token attacks</li>
              <li>Golden and silver ticket attacks</li>
              <li>Token kidnapping</li>
              <li>Token manipulation for persistence</li>
              <li>Cross-session token theft</li>
            </ul>
          </li>
        </ul>
        
        <h3>Process Injection Techniques</h3>
        <ul>
          <li><strong>Classic Injection Methods:</strong>
            <ul>
              <li>DLL injection via SetWindowsHookEx</li>
              <li>CreateRemoteThread injection</li>
              <li>Manual DLL mapping</li>
              <li>Process hollowing (RunPE)</li>
              <li>Atom bombing technique</li>
            </ul>
          </li>
          <li><strong>Advanced Injection Techniques:</strong>
            <ul>
              <li>Process doppelgänging</li>
              <li>Process herpaderping</li>
              <li>Thread execution hijacking</li>
              <li>APC (Asynchronous Procedure Call) injection</li>
              <li>Early bird APC injection</li>
            </ul>
          </li>
          <li><strong>Reflective Techniques:</strong>
            <ul>
              <li>Reflective DLL injection</li>
              <li>Reflective PE loading</li>
              <li>Memory-only execution</li>
              <li>Fileless payload execution</li>
              <li>In-memory assembly loading</li>
            </ul>
          </li>
        </ul>
        
        <h3>Code Execution and Evasion</h3>
        <ul>
          <li><strong>Shellcode Execution:</strong>
            <ul>
              <li>Position-independent shellcode</li>
              <li>Encoder and decoder techniques</li>
              <li>Polymorphic shellcode</li>
              <li>Metamorphic code generation</li>
              <li>Anti-analysis techniques</li>
            </ul>
          </li>
          <li><strong>Living off the Land:</strong>
            <ul>
              <li>PowerShell execution and bypass</li>
              <li>WMI command execution</li>
              <li>WMIC and legitimate binaries</li>
              <li>MSBuild and compilation</li>
              <li>RegSvr32 and DLL execution</li>
            </ul>
          </li>
          <li><strong>Evasion Techniques:</strong>
            <ul>
              <li>API hooking detection and bypass</li>
              <li>Sandbox and analysis evasion</li>
              <li>Anti-debugging techniques</li>
              <li>Timing-based evasion</li>
              <li>Environment-aware execution</li>
            </ul>
          </li>
        </ul>
        
        <h3>Post-Exploitation Techniques</h3>
        <ul>
          <li><strong>Credential Harvesting:</strong>
            <ul>
              <li>LSASS memory dumping</li>
              <li>SAM and SYSTEM hive extraction</li>
              <li>Cached credential extraction</li>
              <li>Browser credential theft</li>
              <li>Application-specific credentials</li>
            </ul>
          </li>
          <li><strong>System Information Gathering:</strong>
            <ul>
              <li>System and network enumeration</li>
              <li>Process and service discovery</li>
              <li>Installed software inventory</li>
              <li>Security product detection</li>
              <li>Network configuration analysis</li>
            </ul>
          </li>
          <li><strong>Lateral Movement Preparation:</strong>
            <ul>
              <li>Network share enumeration</li>
              <li>Remote service exploitation</li>
              <li>WMI and PowerShell remoting</li>
              <li>RDP and terminal services</li>
              <li>Administrative share access</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Windows Exploitation and Post-Exploitation Lab",
    description: "Hands-on exercise in Windows exploitation techniques including privilege escalation, token manipulation, and process injection.",
    tasks: [
      {
        category: "Privilege Escalation",
        commands: [
          {
            command: "Identify and exploit unquoted service paths",
            description: "Find services with unquoted paths and exploit for privilege escalation",
            hint: "Use wmic and sc commands to enumerate services and check permissions",
            expectedOutput: "Successful privilege escalation to SYSTEM"
          },
          {
            command: "Exploit weak service permissions",
            description: "Identify services with weak ACLs and modify for escalation",
            hint: "Use accesschk.exe to identify weak service permissions",
            expectedOutput: "Service modification leading to SYSTEM access"
          }
        ]
      },
      {
        category: "Token Manipulation",
        commands: [
          {
            command: "Perform token impersonation attack",
            description: "Steal and impersonate tokens from other processes",
            hint: "Use tools like Incognito or manual token manipulation",
            expectedOutput: "Successful token theft and impersonation"
          },
          {
            command: "Execute process injection",
            description: "Inject code into legitimate processes for stealth",
            hint: "Use CreateRemoteThread or reflective DLL injection",
            expectedOutput: "Successful code injection and execution"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which Windows privilege is most commonly abused for privilege escalation?",
      options: [
        "SeBackupPrivilege",
        "SeDebugPrivilege",
        "SeImpersonatePrivilege",
        "SeTakeOwnershipPrivilege"
      ],
      correct: 2,
      explanation: "SeImpersonatePrivilege is most commonly abused because it allows a process to impersonate any token, enabling privilege escalation through techniques like token kidnapping and service account impersonation."
    },
    {
      question: "What is the primary purpose of process hollowing in Windows exploitation?",
      options: [
        "To increase execution speed",
        "To evade detection by running malicious code in legitimate processes",
        "To reduce memory usage",
        "To bypass network firewalls"
      ],
      correct: 1,
      explanation: "Process hollowing evades detection by replacing the memory of a legitimate process with malicious code, making the malicious activity appear to come from a trusted process."
    },
    {
      question: "Which Windows security feature is designed to prevent code execution in data areas?",
      options: [
        "ASLR (Address Space Layout Randomization)",
        "DEP (Data Execution Prevention)",
        "CFG (Control Flow Guard)",
        "UAC (User Account Control)"
      ],
      correct: 1,
      explanation: "DEP (Data Execution Prevention) prevents code execution in data areas by marking memory pages as non-executable, helping prevent buffer overflow exploits and shellcode execution."
    }
  ]
};
