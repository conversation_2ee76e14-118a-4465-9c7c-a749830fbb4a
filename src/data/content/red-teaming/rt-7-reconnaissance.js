/**
 * Reconnaissance & Target Analysis Module
 */

export const reconnaissanceContent = {
  id: "rt-7",
  pathId: "red-teaming",
  title: "Reconnaissance & Target Analysis",
  description: "Master comprehensive target reconnaissance and analysis techniques for effective red team operation planning and execution.",
  objectives: [
    "Understand passive and active reconnaissance methodologies",
    "Master OSINT collection and analysis techniques",
    "Learn network and infrastructure reconnaissance",
    "Explore social media and human intelligence gathering",
    "Understand attack surface analysis and mapping",
    "Develop comprehensive target profiling capabilities"
  ],
  difficulty: "Intermediate",
  estimatedTime: 180,
  sections: [
    {
      title: "Reconnaissance Fundamentals and Methodology",
      content: `
        <h2>Reconnaissance Fundamentals</h2>
        <p>Reconnaissance is the systematic gathering of information about targets to identify vulnerabilities, attack vectors, and operational opportunities for red team engagements.</p>
        
        <h3>Reconnaissance Categories</h3>
        <ul>
          <li><strong>Passive Reconnaissance:</strong>
            <ul>
              <li>Information gathering without direct target interaction</li>
              <li>Public records and databases</li>
              <li>Social media and online presence</li>
              <li>Search engines and cached content</li>
              <li>Third-party services and APIs</li>
            </ul>
          </li>
          <li><strong>Active Reconnaissance:</strong>
            <ul>
              <li>Direct interaction with target systems</li>
              <li>Network scanning and enumeration</li>
              <li>Service identification and fingerprinting</li>
              <li>Vulnerability scanning and assessment</li>
              <li>Social engineering and human interaction</li>
            </ul>
          </li>
          <li><strong>Semi-Passive Reconnaissance:</strong>
            <ul>
              <li>Indirect target interaction through intermediaries</li>
              <li>DNS queries and zone transfers</li>
              <li>WHOIS and registration data</li>
              <li>Certificate transparency logs</li>
              <li>Internet scanning databases</li>
            </ul>
          </li>
        </ul>
        
        <h3>Reconnaissance Lifecycle</h3>
        <ul>
          <li><strong>1. Planning and Scoping:</strong>
            <ul>
              <li>Define reconnaissance objectives</li>
              <li>Identify information requirements</li>
              <li>Establish operational boundaries</li>
              <li>Select appropriate tools and techniques</li>
              <li>Plan operational security measures</li>
            </ul>
          </li>
          <li><strong>2. Information Collection:</strong>
            <ul>
              <li>Systematic data gathering</li>
              <li>Multiple source validation</li>
              <li>Automated and manual collection</li>
              <li>Continuous monitoring and updates</li>
              <li>Data organization and storage</li>
            </ul>
          </li>
          <li><strong>3. Analysis and Processing:</strong>
            <ul>
              <li>Data correlation and validation</li>
              <li>Pattern identification and analysis</li>
              <li>Relationship mapping and visualization</li>
              <li>Gap identification and prioritization</li>
              <li>Intelligence product creation</li>
            </ul>
          </li>
          <li><strong>4. Reporting and Dissemination:</strong>
            <ul>
              <li>Structured intelligence reports</li>
              <li>Actionable recommendations</li>
              <li>Visual representations and maps</li>
              <li>Stakeholder communication</li>
              <li>Operational planning support</li>
            </ul>
          </li>
        </ul>
        
        <h3>Information Categories and Targets</h3>
        <ul>
          <li><strong>Organizational Information:</strong>
            <ul>
              <li>Corporate structure and hierarchy</li>
              <li>Business processes and operations</li>
              <li>Partnerships and relationships</li>
              <li>Financial and regulatory information</li>
              <li>Strategic plans and initiatives</li>
            </ul>
          </li>
          <li><strong>Technical Infrastructure:</strong>
            <ul>
              <li>Network topology and architecture</li>
              <li>System and service inventory</li>
              <li>Technology stack and platforms</li>
              <li>Security controls and defenses</li>
              <li>Cloud services and providers</li>
            </ul>
          </li>
          <li><strong>Human Resources:</strong>
            <ul>
              <li>Employee directories and roles</li>
              <li>Contact information and communication</li>
              <li>Social media presence and activity</li>
              <li>Professional networks and relationships</li>
              <li>Personal interests and activities</li>
            </ul>
          </li>
          <li><strong>Physical Assets:</strong>
            <ul>
              <li>Facility locations and layouts</li>
              <li>Security measures and controls</li>
              <li>Access points and vulnerabilities</li>
              <li>Surveillance and monitoring systems</li>
              <li>Environmental and operational factors</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "OSINT Collection and Analysis",
      content: `
        <h2>Open Source Intelligence (OSINT) Collection</h2>
        <p>OSINT involves collecting and analyzing publicly available information to support red team operations and target understanding.</p>
        
        <h3>OSINT Sources and Categories</h3>
        <ul>
          <li><strong>Search Engines and Web Resources:</strong>
            <ul>
              <li>Google, Bing, DuckDuckGo advanced operators</li>
              <li>Specialized search engines (Shodan, Censys)</li>
              <li>Cached and archived content (Wayback Machine)</li>
              <li>Code repositories (GitHub, GitLab)</li>
              <li>Document and file sharing platforms</li>
            </ul>
          </li>
          <li><strong>Social Media and Professional Networks:</strong>
            <ul>
              <li>LinkedIn professional profiles and connections</li>
              <li>Facebook personal and business pages</li>
              <li>Twitter feeds and interactions</li>
              <li>Instagram photos and location data</li>
              <li>Professional forums and communities</li>
            </ul>
          </li>
          <li><strong>Public Records and Databases:</strong>
            <ul>
              <li>Corporate registrations and filings</li>
              <li>Domain and IP registration data</li>
              <li>Patent and trademark databases</li>
              <li>Government and regulatory filings</li>
              <li>Legal and court records</li>
            </ul>
          </li>
          <li><strong>Technical Resources:</strong>
            <ul>
              <li>DNS and network infrastructure data</li>
              <li>SSL certificate transparency logs</li>
              <li>Internet scanning and measurement data</li>
              <li>Vulnerability databases and advisories</li>
              <li>Threat intelligence feeds and reports</li>
            </ul>
          </li>
        </ul>
        
        <h3>OSINT Tools and Techniques</h3>
        <ul>
          <li><strong>Automated Collection Tools:</strong>
            <ul>
              <li>theHarvester - Email and subdomain discovery</li>
              <li>Recon-ng - Modular reconnaissance framework</li>
              <li>SpiderFoot - Automated OSINT collection</li>
              <li>Maltego - Link analysis and visualization</li>
              <li>FOCA - Metadata analysis and extraction</li>
            </ul>
          </li>
          <li><strong>Search Engine Techniques:</strong>
            <ul>
              <li>Google dorking and advanced operators</li>
              <li>Site-specific searches and filters</li>
              <li>File type and content searches</li>
              <li>Time-based and cached searches</li>
              <li>Image and reverse image searches</li>
            </ul>
          </li>
          <li><strong>Social Media Intelligence:</strong>
            <ul>
              <li>Profile enumeration and analysis</li>
              <li>Connection and relationship mapping</li>
              <li>Content and metadata extraction</li>
              <li>Geolocation and timeline analysis</li>
              <li>Sentiment and behavioral analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Analysis and Correlation</h3>
        <ul>
          <li><strong>Information Validation:</strong>
            <ul>
              <li>Cross-source verification</li>
              <li>Temporal consistency checking</li>
              <li>Logical relationship validation</li>
              <li>Source credibility assessment</li>
              <li>Bias and deception detection</li>
            </ul>
          </li>
          <li><strong>Pattern Recognition:</strong>
            <ul>
              <li>Behavioral pattern identification</li>
              <li>Communication pattern analysis</li>
              <li>Temporal pattern recognition</li>
              <li>Geographic pattern mapping</li>
              <li>Anomaly detection and analysis</li>
            </ul>
          </li>
          <li><strong>Relationship Mapping:</strong>
            <ul>
              <li>Personal and professional networks</li>
              <li>Organizational hierarchies and structures</li>
              <li>Technical infrastructure relationships</li>
              <li>Business and partnership connections</li>
              <li>Communication and interaction patterns</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Network and Infrastructure Reconnaissance",
      content: `
        <h2>Network and Infrastructure Reconnaissance</h2>
        <p>Technical reconnaissance focuses on identifying and analyzing target network infrastructure, services, and security controls.</p>
        
        <h3>Network Discovery and Mapping</h3>
        <ul>
          <li><strong>Domain and Subdomain Enumeration:</strong>
            <ul>
              <li>DNS zone transfers and enumeration</li>
              <li>Subdomain brute-forcing and discovery</li>
              <li>Certificate transparency log analysis</li>
              <li>Search engine and archive mining</li>
              <li>DNS record analysis and correlation</li>
            </ul>
          </li>
          <li><strong>IP Range and Network Identification:</strong>
            <ul>
              <li>WHOIS and registration data analysis</li>
              <li>BGP routing table analysis</li>
              <li>Autonomous System (AS) identification</li>
              <li>IP geolocation and ownership</li>
              <li>Cloud provider identification</li>
            </ul>
          </li>
          <li><strong>Port Scanning and Service Discovery:</strong>
            <ul>
              <li>TCP and UDP port scanning</li>
              <li>Service version identification</li>
              <li>Operating system fingerprinting</li>
              <li>Application and protocol detection</li>
              <li>Load balancer and proxy identification</li>
            </ul>
          </li>
        </ul>
        
        <h3>Web Application Reconnaissance</h3>
        <ul>
          <li><strong>Web Technology Identification:</strong>
            <ul>
              <li>Content management systems (CMS)</li>
              <li>Web frameworks and platforms</li>
              <li>Server software and versions</li>
              <li>Programming languages and libraries</li>
              <li>Third-party integrations and plugins</li>
            </ul>
          </li>
          <li><strong>Directory and File Discovery:</strong>
            <ul>
              <li>Directory brute-forcing and enumeration</li>
              <li>Backup and configuration file discovery</li>
              <li>Administrative interface identification</li>
              <li>API endpoint discovery</li>
              <li>Hidden and development resources</li>
            </ul>
          </li>
          <li><strong>Application Functionality Analysis:</strong>
            <ul>
              <li>User registration and authentication</li>
              <li>Input validation and processing</li>
              <li>Session management and cookies</li>
              <li>File upload and download capabilities</li>
              <li>Database and backend interactions</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud and Modern Infrastructure</h3>
        <ul>
          <li><strong>Cloud Service Identification:</strong>
            <ul>
              <li>AWS, Azure, GCP service enumeration</li>
              <li>Cloud storage bucket discovery</li>
              <li>CDN and edge service identification</li>
              <li>Serverless function discovery</li>
              <li>Container and orchestration platforms</li>
            </ul>
          </li>
          <li><strong>API and Microservice Discovery:</strong>
            <ul>
              <li>REST and GraphQL API enumeration</li>
              <li>API documentation and specification</li>
              <li>Microservice architecture mapping</li>
              <li>Service mesh and communication patterns</li>
              <li>Authentication and authorization mechanisms</li>
            </ul>
          </li>
          <li><strong>DevOps and CI/CD Infrastructure:</strong>
            <ul>
              <li>Version control system identification</li>
              <li>Build and deployment pipeline discovery</li>
              <li>Container registry and image analysis</li>
              <li>Infrastructure as Code (IaC) discovery</li>
              <li>Monitoring and logging system identification</li>
            </ul>
          </li>
        </ul>
        
        <h3>Attack Surface Analysis</h3>
        <ul>
          <li><strong>External Attack Surface:</strong>
            <ul>
              <li>Internet-facing services and applications</li>
              <li>Remote access and VPN endpoints</li>
              <li>Email and collaboration systems</li>
              <li>Cloud services and storage</li>
              <li>Third-party integrations and dependencies</li>
            </ul>
          </li>
          <li><strong>Internal Attack Surface:</strong>
            <ul>
              <li>Internal network segmentation</li>
              <li>Administrative and management interfaces</li>
              <li>Database and file servers</li>
              <li>Network infrastructure devices</li>
              <li>Endpoint and workstation systems</li>
            </ul>
          </li>
          <li><strong>Supply Chain and Dependencies:</strong>
            <ul>
              <li>Third-party vendor relationships</li>
              <li>Software and library dependencies</li>
              <li>Managed service providers</li>
              <li>Business partner integrations</li>
              <li>Outsourced functions and services</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Comprehensive Target Reconnaissance Lab",
    description: "Hands-on exercise in conducting systematic reconnaissance and target analysis using multiple techniques and tools.",
    tasks: [
      {
        category: "OSINT Collection",
        commands: [
          {
            command: "Use theHarvester for email and subdomain discovery",
            description: "Collect email addresses and subdomains for target organization",
            hint: "Try multiple data sources and search engines",
            expectedOutput: "Comprehensive list of emails, subdomains, and associated data"
          },
          {
            command: "Perform advanced Google dorking",
            description: "Use Google search operators to find sensitive information",
            hint: "Focus on file types, login pages, and configuration files",
            expectedOutput: "Discovered sensitive files and exposed information"
          }
        ]
      },
      {
        category: "Network Reconnaissance",
        commands: [
          {
            command: "Conduct comprehensive port scanning with Nmap",
            description: "Identify open ports, services, and operating systems",
            hint: "Use stealth scanning techniques and service detection",
            expectedOutput: "Detailed network map with service information"
          },
          {
            command: "Analyze web applications with Burp Suite",
            description: "Identify web technologies and application structure",
            hint: "Focus on technology stack and hidden functionality",
            expectedOutput: "Web application technology profile and attack surface"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary advantage of passive reconnaissance over active reconnaissance?",
      options: [
        "It provides more detailed information",
        "It's faster and more efficient",
        "It doesn't alert the target to reconnaissance activities",
        "It requires fewer tools and resources"
      ],
      correct: 2,
      explanation: "Passive reconnaissance doesn't alert the target because it doesn't involve direct interaction with target systems, making it stealthier and reducing the risk of detection."
    },
    {
      question: "Which OSINT source is most valuable for understanding organizational structure and employee relationships?",
      options: [
        "Search engines",
        "LinkedIn",
        "DNS records",
        "Certificate transparency logs"
      ],
      correct: 1,
      explanation: "LinkedIn is most valuable for understanding organizational structure and employee relationships as it provides professional profiles, job titles, connections, and company hierarchies."
    },
    {
      question: "What is the primary purpose of attack surface analysis in reconnaissance?",
      options: [
        "To identify all possible entry points and vulnerabilities",
        "To determine the target's budget and resources",
        "To understand the target's business operations",
        "To map the target's physical locations"
      ],
      correct: 0,
      explanation: "Attack surface analysis identifies all possible entry points and vulnerabilities that could be exploited during an attack, helping prioritize targets and attack vectors."
    }
  ]
};
