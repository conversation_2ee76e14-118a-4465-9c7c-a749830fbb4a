/**
 * Command & Control (C2) Module
 */

export const commandControlContent = {
  id: "rt-9",
  pathId: "red-teaming",
  title: "Command & Control (C2)",
  description: "Master command and control techniques for maintaining communication with compromised systems and managing red team operations.",
  objectives: [
    "Understand C2 architecture and communication protocols",
    "Master C2 framework deployment and configuration",
    "Learn covert communication channels and techniques",
    "Explore traffic obfuscation and evasion methods",
    "Understand C2 operational security and resilience",
    "Develop custom C2 solutions and protocols"
  ],
  difficulty: "Advanced",
  estimatedTime: 220,
  sections: [
    {
      title: "C2 Architecture and Fundamentals",
      content: `
        <h2>Command & Control (C2) Fundamentals</h2>
        <p>Command and Control systems provide the communication backbone for red team operations, enabling remote management of compromised systems and coordination of attack activities.</p>
        
        <h3>C2 Architecture Models</h3>
        <ul>
          <li><strong>Centralized Architecture:</strong>
            <ul>
              <li>Single C2 server managing all agents</li>
              <li>Direct communication between server and agents</li>
              <li>Simple deployment and management</li>
              <li>Single point of failure vulnerability</li>
              <li>Easier detection and blocking</li>
            </ul>
          </li>
          <li><strong>Decentralized Architecture:</strong>
            <ul>
              <li>Multiple C2 servers with distributed control</li>
              <li>Load balancing and redundancy</li>
              <li>Geographic distribution of infrastructure</li>
              <li>Improved resilience and availability</li>
              <li>Complex management and coordination</li>
            </ul>
          </li>
          <li><strong>Peer-to-Peer (P2P) Architecture:</strong>
            <ul>
              <li>Agent-to-agent communication networks</li>
              <li>No central server dependency</li>
              <li>Self-healing and adaptive networks</li>
              <li>Difficult to disrupt completely</li>
              <li>Complex implementation and debugging</li>
            </ul>
          </li>
          <li><strong>Hybrid Architecture:</strong>
            <ul>
              <li>Combination of centralized and P2P elements</li>
              <li>Adaptive communication strategies</li>
              <li>Fallback and redundancy mechanisms</li>
              <li>Optimized for specific scenarios</li>
              <li>Balanced complexity and resilience</li>
            </ul>
          </li>
        </ul>
        
        <h3>C2 Communication Models</h3>
        <ul>
          <li><strong>Pull Model (Beacon):</strong>
            <ul>
              <li>Agents initiate communication to C2 server</li>
              <li>Regular check-in intervals (heartbeat)</li>
              <li>Commands queued on server for retrieval</li>
              <li>Better firewall and NAT traversal</li>
              <li>Predictable communication patterns</li>
            </ul>
          </li>
          <li><strong>Push Model:</strong>
            <ul>
              <li>C2 server initiates communication to agents</li>
              <li>Real-time command delivery</li>
              <li>Requires open ports or reverse connections</li>
              <li>Faster response times</li>
              <li>More difficult firewall traversal</li>
            </ul>
          </li>
          <li><strong>Hybrid Model:</strong>
            <ul>
              <li>Combination of pull and push mechanisms</li>
              <li>Adaptive communication based on conditions</li>
              <li>Optimized for different network environments</li>
              <li>Fallback communication methods</li>
              <li>Complex implementation and management</li>
            </ul>
          </li>
        </ul>
        
        <h3>C2 Protocol Categories</h3>
        <ul>
          <li><strong>Application Layer Protocols:</strong>
            <ul>
              <li>HTTP/HTTPS - Most common and versatile</li>
              <li>DNS - Covert and difficult to block</li>
              <li>SMTP/POP3/IMAP - Email-based communication</li>
              <li>FTP/SFTP - File transfer protocols</li>
              <li>Social media and cloud services</li>
            </ul>
          </li>
          <li><strong>Network Layer Protocols:</strong>
            <ul>
              <li>TCP - Reliable connection-oriented communication</li>
              <li>UDP - Fast connectionless communication</li>
              <li>ICMP - Covert communication using ping</li>
              <li>Custom protocols - Proprietary implementations</li>
              <li>Tunneling protocols - VPN and overlay networks</li>
            </ul>
          </li>
          <li><strong>Covert Channels:</strong>
            <ul>
              <li>Steganography - Hidden data in images/files</li>
              <li>Timing channels - Information in timing patterns</li>
              <li>Storage channels - Data in unused fields</li>
              <li>Behavioral channels - Information in actions</li>
              <li>Physical channels - Air-gapped communication</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "C2 Framework Implementation",
      content: `
        <h2>C2 Framework Implementation</h2>
        <p>Modern C2 frameworks provide comprehensive platforms for managing red team operations with advanced features for communication, evasion, and operational management.</p>
        
        <h3>Popular C2 Frameworks</h3>
        <ul>
          <li><strong>Cobalt Strike:</strong>
            <ul>
              <li>Commercial red team platform</li>
              <li>Malleable C2 profiles for traffic customization</li>
              <li>Advanced post-exploitation capabilities</li>
              <li>Team collaboration and reporting features</li>
              <li>Extensive documentation and support</li>
            </ul>
          </li>
          <li><strong>Metasploit Framework:</strong>
            <ul>
              <li>Open-source penetration testing platform</li>
              <li>Meterpreter advanced payload system</li>
              <li>Extensive module library</li>
              <li>Multi-platform support</li>
              <li>Integration with other security tools</li>
            </ul>
          </li>
          <li><strong>Empire/Starkiller:</strong>
            <ul>
              <li>PowerShell-based post-exploitation framework</li>
              <li>Living-off-the-land techniques</li>
              <li>Encrypted communication channels</li>
              <li>Web-based management interface</li>
              <li>Modular agent architecture</li>
            </ul>
          </li>
          <li><strong>Covenant:</strong>
            <ul>
              <li>.NET-based C2 framework</li>
              <li>Cross-platform compatibility</li>
              <li>Web-based collaborative interface</li>
              <li>Customizable communication protocols</li>
              <li>Integrated development environment</li>
            </ul>
          </li>
          <li><strong>Sliver:</strong>
            <ul>
              <li>Open-source C2 framework</li>
              <li>Cross-platform implant support</li>
              <li>Multiple communication protocols</li>
              <li>Multiplayer collaboration</li>
              <li>Extensible architecture</li>
            </ul>
          </li>
        </ul>
        
        <h3>C2 Profile Development</h3>
        <ul>
          <li><strong>Malleable C2 Profiles:</strong>
            <ul>
              <li>HTTP/HTTPS traffic customization</li>
              <li>User-agent and header manipulation</li>
              <li>URI and parameter customization</li>
              <li>Data encoding and transformation</li>
              <li>Traffic timing and jitter control</li>
            </ul>
          </li>
          <li><strong>Traffic Mimicry:</strong>
            <ul>
              <li>Legitimate application impersonation</li>
              <li>Popular service traffic patterns</li>
              <li>Regional and cultural adaptations</li>
              <li>Time-based behavior simulation</li>
              <li>Error and retry pattern mimicry</li>
            </ul>
          </li>
          <li><strong>Evasion Techniques:</strong>
            <ul>
              <li>Domain fronting and CDN usage</li>
              <li>Traffic encryption and obfuscation</li>
              <li>Protocol tunneling and encapsulation</li>
              <li>Legitimate service abuse</li>
              <li>Anti-analysis and sandbox evasion</li>
            </ul>
          </li>
        </ul>
        
        <h3>Agent and Implant Development</h3>
        <ul>
          <li><strong>Agent Architecture:</strong>
            <ul>
              <li>Modular and extensible design</li>
              <li>Platform-specific implementations</li>
              <li>Memory-only execution capabilities</li>
              <li>Self-deletion and cleanup mechanisms</li>
              <li>Error handling and recovery</li>
            </ul>
          </li>
          <li><strong>Communication Modules:</strong>
            <ul>
              <li>Protocol-specific implementations</li>
              <li>Encryption and authentication</li>
              <li>Compression and encoding</li>
              <li>Retry and failover logic</li>
              <li>Traffic shaping and timing</li>
            </ul>
          </li>
          <li><strong>Capability Modules:</strong>
            <ul>
              <li>System information gathering</li>
              <li>File system operations</li>
              <li>Process and service management</li>
              <li>Network reconnaissance</li>
              <li>Credential harvesting</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Covert Communication and Evasion",
      content: `
        <h2>Covert Communication and Evasion</h2>
        <p>Advanced C2 operations require sophisticated techniques to evade detection and maintain persistent communication channels.</p>
        
        <h3>DNS-based Communication</h3>
        <ul>
          <li><strong>DNS Tunneling Techniques:</strong>
            <ul>
              <li>Subdomain encoding and data exfiltration</li>
              <li>TXT record data storage and retrieval</li>
              <li>A record IP address encoding</li>
              <li>CNAME and MX record abuse</li>
              <li>DNS over HTTPS (DoH) tunneling</li>
            </ul>
          </li>
          <li><strong>DNS Evasion Methods:</strong>
            <ul>
              <li>Fast flux and domain generation algorithms</li>
              <li>Legitimate DNS service abuse</li>
              <li>DNS cache poisoning and manipulation</li>
              <li>Recursive resolver exploitation</li>
              <li>DNS over TLS (DoT) encryption</li>
            </ul>
          </li>
          <li><strong>DNS C2 Tools:</strong>
            <ul>
              <li>dnscat2 - DNS tunneling framework</li>
              <li>Iodine - IP over DNS tunnel</li>
              <li>DNSStager - DNS-based payload delivery</li>
              <li>Cobalt Strike DNS beacons</li>
              <li>Custom DNS C2 implementations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Social Media and Cloud Service C2</h3>
        <ul>
          <li><strong>Social Media Platforms:</strong>
            <ul>
              <li>Twitter API and direct messages</li>
              <li>Facebook posts and comments</li>
              <li>LinkedIn messaging and posts</li>
              <li>Instagram and image steganography</li>
              <li>Discord and gaming platforms</li>
            </ul>
          </li>
          <li><strong>Cloud Storage Services:</strong>
            <ul>
              <li>Dropbox and Google Drive file sharing</li>
              <li>OneDrive and iCloud synchronization</li>
              <li>GitHub and GitLab repositories</li>
              <li>Pastebin and text sharing services</li>
              <li>Cloud database services</li>
            </ul>
          </li>
          <li><strong>Legitimate Service Abuse:</strong>
            <ul>
              <li>CDN and edge service exploitation</li>
              <li>Email and messaging services</li>
              <li>File sharing and collaboration tools</li>
              <li>Gaming and entertainment platforms</li>
              <li>IoT and smart device services</li>
            </ul>
          </li>
        </ul>
        
        <h3>Traffic Analysis Evasion</h3>
        <ul>
          <li><strong>Encryption and Obfuscation:</strong>
            <ul>
              <li>Custom encryption algorithms</li>
              <li>Multi-layer encryption schemes</li>
              <li>Traffic padding and noise injection</li>
              <li>Protocol obfuscation techniques</li>
              <li>Steganographic data hiding</li>
            </ul>
          </li>
          <li><strong>Timing and Pattern Evasion:</strong>
            <ul>
              <li>Jitter and randomization</li>
              <li>Sleep and delay mechanisms</li>
              <li>Burst and sustained traffic patterns</li>
              <li>Time-based communication windows</li>
              <li>Adaptive timing algorithms</li>
            </ul>
          </li>
          <li><strong>Protocol Hopping:</strong>
            <ul>
              <li>Dynamic protocol selection</li>
              <li>Fallback communication methods</li>
              <li>Multi-protocol agent capabilities</li>
              <li>Conditional protocol switching</li>
              <li>Environment-aware adaptations</li>
            </ul>
          </li>
        </ul>
        
        <h3>C2 Resilience and Recovery</h3>
        <ul>
          <li><strong>Infrastructure Redundancy:</strong>
            <ul>
              <li>Multiple C2 servers and domains</li>
              <li>Geographic distribution</li>
              <li>Load balancing and failover</li>
              <li>Backup communication channels</li>
              <li>Emergency contact mechanisms</li>
            </ul>
          </li>
          <li><strong>Agent Recovery Mechanisms:</strong>
            <ul>
              <li>Automatic reconnection logic</li>
              <li>Alternative server discovery</li>
              <li>Peer-to-peer recovery networks</li>
              <li>Dead drop communication</li>
              <li>Self-healing capabilities</li>
            </ul>
          </li>
          <li><strong>Operational Continuity:</strong>
            <ul>
              <li>Session state preservation</li>
              <li>Command queue management</li>
              <li>Data synchronization</li>
              <li>Operator handoff procedures</li>
              <li>Emergency shutdown protocols</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "C2 Framework Deployment and Management Lab",
    description: "Hands-on exercise in deploying, configuring, and managing command and control infrastructure for red team operations.",
    tasks: [
      {
        category: "C2 Framework Setup",
        commands: [
          {
            command: "Deploy Cobalt Strike team server",
            description: "Set up Cobalt Strike with custom malleable C2 profile",
            hint: "Configure HTTPS listeners with domain fronting",
            expectedOutput: "Functional Cobalt Strike infrastructure with custom profile"
          },
          {
            command: "Configure Metasploit multi/handler",
            description: "Set up Metasploit listeners for various payload types",
            hint: "Use staged and stageless payloads with different protocols",
            expectedOutput: "Multiple active listeners for different scenarios"
          }
        ]
      },
      {
        category: "Covert Communication",
        commands: [
          {
            command: "Implement DNS C2 channel",
            description: "Set up DNS-based command and control communication",
            hint: "Use dnscat2 or similar tools for DNS tunneling",
            expectedOutput: "Working DNS C2 channel with data exfiltration"
          },
          {
            command: "Configure social media C2",
            description: "Implement C2 using social media platforms",
            hint: "Use Twitter API or similar for command delivery",
            expectedOutput: "Functional social media-based C2 channel"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary advantage of using DNS for C2 communication?",
      options: [
        "It provides faster data transfer",
        "It's difficult to block without breaking legitimate functionality",
        "It supports larger payload sizes",
        "It's easier to implement than HTTP"
      ],
      correct: 1,
      explanation: "DNS is difficult to block without breaking legitimate functionality because DNS is essential for normal network operations, making it an effective covert communication channel."
    },
    {
      question: "Which C2 architecture model provides the best resilience against takedown efforts?",
      options: [
        "Centralized architecture",
        "Decentralized architecture",
        "Peer-to-peer (P2P) architecture",
        "Hybrid architecture"
      ],
      correct: 2,
      explanation: "Peer-to-peer (P2P) architecture provides the best resilience because it has no central points of failure and can self-heal when nodes are removed or compromised."
    },
    {
      question: "What is the purpose of malleable C2 profiles in frameworks like Cobalt Strike?",
      options: [
        "To increase payload execution speed",
        "To customize network traffic to evade detection",
        "To reduce memory usage",
        "To simplify configuration management"
      ],
      correct: 1,
      explanation: "Malleable C2 profiles customize network traffic patterns to evade detection by making C2 communication appear like legitimate application traffic, helping avoid network monitoring and analysis."
    }
  ]
};
