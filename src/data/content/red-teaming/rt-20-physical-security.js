/**
 * Physical Security Testing Module
 */

export const physicalSecurityContent = {
  id: "rt-20",
  pathId: "red-teaming",
  title: "Physical Security Testing",
  description: "Master physical security assessment techniques including lock picking, access control bypass, and physical penetration testing methodologies.",
  objectives: [
    "Understand physical security fundamentals and threat modeling",
    "Master lock picking and mechanical bypass techniques",
    "Learn electronic access control system exploitation",
    "Explore RFID and badge cloning techniques",
    "Understand surveillance and detection evasion",
    "Master physical penetration testing methodologies"
  ],
  difficulty: "Advanced",
  estimatedTime: 240,
  sections: [
    {
      title: "Physical Security Fundamentals",
      content: `
        <h2>Physical Security Architecture and Threat Modeling</h2>
        <p>Physical security forms the foundation of overall security posture, protecting assets, personnel, and information through layered defense mechanisms.</p>
        
        <h3>Physical Security Principles</h3>
        <ul>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Perimeter security and boundary protection</li>
              <li>Building and facility access controls</li>
              <li>Internal zone and area restrictions</li>
              <li>Asset-specific protection measures</li>
              <li>Personnel security and background checks</li>
            </ul>
          </li>
          <li><strong>Physical Security Controls:</strong>
            <ul>
              <li>Deterrent controls (signage, lighting, cameras)</li>
              <li>Preventive controls (locks, barriers, access systems)</li>
              <li>Detective controls (alarms, sensors, monitoring)</li>
              <li>Corrective controls (response procedures, recovery)</li>
              <li>Compensating controls (alternative measures)</li>
            </ul>
          </li>
          <li><strong>Risk Assessment Framework:</strong>
            <ul>
              <li>Asset identification and valuation</li>
              <li>Threat actor analysis and capabilities</li>
              <li>Vulnerability assessment and gap analysis</li>
              <li>Risk calculation and prioritization</li>
              <li>Mitigation strategy development</li>
            </ul>
          </li>
        </ul>
        
        <h3>Physical Security Components</h3>
        <ul>
          <li><strong>Perimeter Security:</strong>
            <ul>
              <li>Fencing, walls, and barrier systems</li>
              <li>Gates, turnstiles, and vehicle controls</li>
              <li>Lighting and visibility enhancement</li>
              <li>Landscaping and natural barriers</li>
              <li>Signage and warning systems</li>
            </ul>
          </li>
          <li><strong>Access Control Systems:</strong>
            <ul>
              <li>Mechanical locks and key management</li>
              <li>Electronic access control and card readers</li>
              <li>Biometric authentication systems</li>
              <li>Multi-factor authentication requirements</li>
              <li>Visitor management and escort procedures</li>
            </ul>
          </li>
          <li><strong>Surveillance and Detection:</strong>
            <ul>
              <li>CCTV systems and video analytics</li>
              <li>Motion detectors and intrusion alarms</li>
              <li>Environmental sensors and monitoring</li>
              <li>Security personnel and guard services</li>
              <li>Incident response and escalation procedures</li>
            </ul>
          </li>
        </ul>
        
        <h3>Physical Threat Landscape</h3>
        <ul>
          <li><strong>External Threats:</strong>
            <ul>
              <li>Unauthorized entry and trespassing</li>
              <li>Theft and asset removal</li>
              <li>Vandalism and property damage</li>
              <li>Espionage and information gathering</li>
              <li>Terrorism and targeted attacks</li>
            </ul>
          </li>
          <li><strong>Internal Threats:</strong>
            <ul>
              <li>Insider threats and malicious employees</li>
              <li>Negligent and accidental security breaches</li>
              <li>Contractor and vendor access abuse</li>
              <li>Social engineering and manipulation</li>
              <li>Data theft and intellectual property loss</li>
            </ul>
          </li>
          <li><strong>Environmental Threats:</strong>
            <ul>
              <li>Natural disasters and weather events</li>
              <li>Fire and smoke damage</li>
              <li>Flooding and water damage</li>
              <li>Power outages and utility failures</li>
              <li>Chemical and biological hazards</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Lock Picking and Mechanical Bypass",
      content: `
        <h2>Mechanical Security Bypass Techniques</h2>
        <p>Understanding mechanical security mechanisms and their vulnerabilities is essential for comprehensive physical security assessment.</p>
        
        <h3>Lock Types and Mechanisms</h3>
        <ul>
          <li><strong>Pin Tumbler Locks:</strong>
            <ul>
              <li>Standard pin tumbler construction</li>
              <li>Security pins and anti-pick features</li>
              <li>High-security pin tumbler variants</li>
              <li>Master key systems and MACS</li>
              <li>Bump key vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Wafer and Disk Detainer Locks:</strong>
            <ul>
              <li>Wafer tumbler mechanisms</li>
              <li>Disk detainer construction</li>
              <li>Sidebar and fence mechanisms</li>
              <li>Automotive and cabinet applications</li>
              <li>Specialized picking techniques</li>
            </ul>
          </li>
          <li><strong>High-Security Mechanical Locks:</strong>
            <ul>
              <li>Medeco and ASSA high-security systems</li>
              <li>Magnetic and electronic hybrid locks</li>
              <li>Restricted keyway systems</li>
              <li>Patent protection and key control</li>
              <li>Attack resistance and certification</li>
            </ul>
          </li>
        </ul>
        
        <h3>Lock Picking Techniques</h3>
        <ul>
          <li><strong>Single Pin Picking (SPP):</strong>
            <ul>
              <li>Tension wrench application and control</li>
              <li>Pick selection and manipulation</li>
              <li>Binding order and pin setting</li>
              <li>Feedback interpretation and adjustment</li>
              <li>Security pin defeat techniques</li>
            </ul>
          </li>
          <li><strong>Raking and Scrubbing:</strong>
            <ul>
              <li>Rake pick selection and technique</li>
              <li>Zipping and scrubbing motions</li>
              <li>Tension control and timing</li>
              <li>Multiple pin manipulation</li>
              <li>Speed picking applications</li>
            </ul>
          </li>
          <li><strong>Specialized Techniques:</strong>
            <ul>
              <li>Bump key construction and usage</li>
              <li>Snap gun and electric pick guns</li>
              <li>Impressioning and key cutting</li>
              <li>Decoder and progression methods</li>
              <li>Bypass and shim techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Physical Bypass Methods</h3>
        <ul>
          <li><strong>Door and Frame Attacks:</strong>
            <ul>
              <li>Latch slipping and credit card attacks</li>
              <li>Door frame spreading and manipulation</li>
              <li>Hinge pin removal and door lifting</li>
              <li>Glass breaking and reach-around</li>
              <li>Destructive entry techniques</li>
            </ul>
          </li>
          <li><strong>Lock Bypass Techniques:</strong>
            <ul>
              <li>Shim attacks on padlocks</li>
              <li>Lever handle manipulation</li>
              <li>Cylinder pulling and drilling</li>
              <li>Magnetic and electronic interference</li>
              <li>Mechanical override exploitation</li>
            </ul>
          </li>
          <li><strong>Specialized Tools and Equipment:</strong>
            <ul>
              <li>Lock pick sets and tension tools</li>
              <li>Bump keys and snap guns</li>
              <li>Shims and bypass tools</li>
              <li>Drilling and destructive tools</li>
              <li>Magnets and electronic devices</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Electronic Access Control and RFID Attacks",
      content: `
        <h2>Electronic Security System Exploitation</h2>
        <p>Modern access control systems rely on electronic components that present unique attack vectors and vulnerabilities.</p>
        
        <h3>Electronic Access Control Systems</h3>
        <ul>
          <li><strong>Card Reader Technologies:</strong>
            <ul>
              <li>Magnetic stripe card systems</li>
              <li>Proximity and RFID card readers</li>
              <li>Smart card and chip-based systems</li>
              <li>Contactless and NFC technologies</li>
              <li>Mobile and smartphone-based access</li>
            </ul>
          </li>
          <li><strong>Biometric Access Systems:</strong>
            <ul>
              <li>Fingerprint recognition systems</li>
              <li>Iris and retinal scanning</li>
              <li>Facial recognition technology</li>
              <li>Voice recognition and authentication</li>
              <li>Multi-modal biometric systems</li>
            </ul>
          </li>
          <li><strong>Keypad and PIN Systems:</strong>
            <ul>
              <li>Numeric keypad entry systems</li>
              <li>Alphanumeric and custom keypads</li>
              <li>Combination and sequence entry</li>
              <li>Time-based and rotating codes</li>
              <li>Duress and panic codes</li>
            </ul>
          </li>
        </ul>
        
        <h3>RFID and Proximity Card Attacks</h3>
        <ul>
          <li><strong>RFID Technology Overview:</strong>
            <ul>
              <li>Low frequency (125 kHz) systems</li>
              <li>High frequency (13.56 MHz) systems</li>
              <li>Ultra-high frequency (860-960 MHz) systems</li>
              <li>Near Field Communication (NFC) protocols</li>
              <li>Passive and active RFID systems</li>
            </ul>
          </li>
          <li><strong>RFID Attack Techniques:</strong>
            <ul>
              <li>Card cloning and duplication</li>
              <li>Eavesdropping and sniffing attacks</li>
              <li>Replay and man-in-the-middle attacks</li>
              <li>Brute force and fuzzing attacks</li>
              <li>Cryptographic key extraction</li>
            </ul>
          </li>
          <li><strong>RFID Attack Tools:</strong>
            <ul>
              <li>Proxmark3 and RFID research tools</li>
              <li>Chameleon Mini and emulation devices</li>
              <li>HID and EM card cloners</li>
              <li>Software-defined radio (SDR) platforms</li>
              <li>Custom RFID readers and writers</li>
            </ul>
          </li>
        </ul>
        
        <h3>Electronic System Vulnerabilities</h3>
        <ul>
          <li><strong>Hardware Vulnerabilities:</strong>
            <ul>
              <li>Tamper detection bypass</li>
              <li>Power analysis and side-channel attacks</li>
              <li>Electromagnetic interference and jamming</li>
              <li>Physical component replacement</li>
              <li>Debug interface exploitation</li>
            </ul>
          </li>
          <li><strong>Software and Firmware Attacks:</strong>
            <ul>
              <li>Firmware extraction and analysis</li>
              <li>Default credential exploitation</li>
              <li>Buffer overflow and injection attacks</li>
              <li>Update mechanism compromise</li>
              <li>Configuration and database manipulation</li>
            </ul>
          </li>
          <li><strong>Communication Protocol Attacks:</strong>
            <ul>
              <li>Wiegand protocol manipulation</li>
              <li>RS-485 and serial communication attacks</li>
              <li>Network protocol exploitation</li>
              <li>Wireless communication interception</li>
              <li>Encryption and authentication bypass</li>
            </ul>
          </li>
        </ul>
        
        <h3>Physical Penetration Testing Methodology</h3>
        <ul>
          <li><strong>Reconnaissance and Planning:</strong>
            <ul>
              <li>Target facility assessment</li>
              <li>Security control identification</li>
              <li>Personnel and schedule analysis</li>
              <li>Entry point and route planning</li>
              <li>Tool and equipment preparation</li>
            </ul>
          </li>
          <li><strong>Social Engineering Integration:</strong>
            <ul>
              <li>Pretexting and cover story development</li>
              <li>Uniform and credential preparation</li>
              <li>Tailgating and piggybacking techniques</li>
              <li>Authority and urgency exploitation</li>
              <li>Distraction and misdirection tactics</li>
            </ul>
          </li>
          <li><strong>Documentation and Reporting:</strong>
            <ul>
              <li>Evidence collection and photography</li>
              <li>Vulnerability assessment and risk rating</li>
              <li>Remediation recommendations</li>
              <li>Executive summary and technical details</li>
              <li>Compliance and regulatory considerations</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Physical Security Assessment Lab",
    description: "Hands-on exercise in physical security testing including lock picking, RFID attacks, and access control bypass techniques.",
    tasks: [
      {
        category: "Lock Picking",
        commands: [
          {
            command: "Pick standard pin tumbler lock",
            description: "Use single pin picking technique to open lock",
            hint: "Apply proper tension and feel for binding pins",
            expectedOutput: "Successfully opened lock using SPP technique"
          },
          {
            command: "Perform bump key attack",
            description: "Create and use bump key for rapid lock opening",
            hint: "File appropriate key blank and use bump technique",
            expectedOutput: "Successful lock opening using bump key method"
          }
        ]
      },
      {
        category: "RFID Exploitation",
        commands: [
          {
            command: "Clone RFID access card",
            description: "Read and duplicate RFID card using Proxmark3",
            hint: "Identify card type and use appropriate cloning commands",
            expectedOutput: "Successfully cloned RFID card with working duplicate"
          },
          {
            command: "Analyze and exploit keypad system",
            description: "Identify PIN through observation or brute force",
            hint: "Look for wear patterns or use systematic approach",
            expectedOutput: "Successful keypad bypass and access gained"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which lock picking technique is most effective against standard pin tumbler locks?",
      options: [
        "Raking",
        "Single Pin Picking (SPP)",
        "Bump key attack",
        "Snap gun"
      ],
      correct: 1,
      explanation: "Single Pin Picking (SPP) is most effective because it allows precise control over each pin, works against security pins, and provides the highest success rate with proper technique and practice."
    },
    {
      question: "What is the primary vulnerability of most proximity card systems?",
      options: [
        "Weak encryption",
        "Lack of authentication",
        "Easy cloning capability",
        "Short read range"
      ],
      correct: 2,
      explanation: "Easy cloning capability is the primary vulnerability because many proximity cards use simple, unencrypted protocols that can be easily read and duplicated with readily available tools like Proxmark3."
    },
    {
      question: "Which physical security control provides the best return on investment for most organizations?",
      options: [
        "High-security locks",
        "CCTV surveillance",
        "Security guards",
        "Access control systems"
      ],
      correct: 1,
      explanation: "CCTV surveillance provides the best ROI because it serves multiple functions (deterrent, detective, and evidence collection), covers large areas cost-effectively, and can be monitored remotely with modern systems."
    }
  ]
};
