/**
 * Initial Access Techniques Module
 */

export const initialAccessContent = {
  id: "rt-8",
  pathId: "red-teaming",
  title: "Initial Access Techniques",
  description: "Master various methods for gaining initial access to target environments, including phishing, exploitation, and alternative attack vectors.",
  objectives: [
    "Understand initial access tactics and techniques",
    "Master spear-phishing and social engineering attacks",
    "Learn exploitation of public-facing applications",
    "Explore supply chain and watering hole attacks",
    "Understand physical access and insider threats",
    "Develop multi-vector initial access strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 200,
  sections: [
    {
      title: "Initial Access Fundamentals",
      content: `
        <h2>Initial Access Fundamentals</h2>
        <p>Initial access represents the first stage of a successful attack, where adversaries gain their initial foothold in the target environment through various attack vectors.</p>
        
        <h3>MITRE ATT&CK Initial Access Techniques</h3>
        <ul>
          <li><strong>T1566 - Phishing:</strong>
            <ul>
              <li>Spearphishing Attachment (T1566.001)</li>
              <li>Spearphishing Link (T1566.002)</li>
              <li>Spearphishing via Service (T1566.003)</li>
              <li>Mass phishing campaigns</li>
              <li>Business Email Compromise (BEC)</li>
            </ul>
          </li>
          <li><strong>T1190 - Exploit Public-Facing Application:</strong>
            <ul>
              <li>Web application vulnerabilities</li>
              <li>Remote code execution exploits</li>
              <li>SQL injection and command injection</li>
              <li>Deserialization vulnerabilities</li>
              <li>Zero-day exploitation</li>
            </ul>
          </li>
          <li><strong>T1133 - External Remote Services:</strong>
            <ul>
              <li>VPN and remote access solutions</li>
              <li>Remote Desktop Protocol (RDP)</li>
              <li>SSH and terminal services</li>
              <li>Cloud-based remote access</li>
              <li>Credential stuffing and brute force</li>
            </ul>
          </li>
          <li><strong>T1200 - Hardware Additions:</strong>
            <ul>
              <li>USB drops and malicious devices</li>
              <li>Network taps and implants</li>
              <li>Wireless access points</li>
              <li>Physical keyloggers</li>
              <li>Malicious charging stations</li>
            </ul>
          </li>
          <li><strong>T1091 - Replication Through Removable Media:</strong>
            <ul>
              <li>USB and external drive infections</li>
              <li>Autorun and autoplay exploitation</li>
              <li>Cross-platform malware delivery</li>
              <li>Air-gapped network bridging</li>
              <li>Social engineering with physical media</li>
            </ul>
          </li>
        </ul>
        
        <h3>Attack Vector Selection Criteria</h3>
        <ul>
          <li><strong>Target Environment Analysis:</strong>
            <ul>
              <li>Security posture and controls</li>
              <li>User awareness and training levels</li>
              <li>Technology stack and platforms</li>
              <li>Remote work and access patterns</li>
              <li>Physical security measures</li>
            </ul>
          </li>
          <li><strong>Success Probability Assessment:</strong>
            <ul>
              <li>Historical attack success rates</li>
              <li>Vulnerability exposure and patching</li>
              <li>User susceptibility to social engineering</li>
              <li>Detection and response capabilities</li>
              <li>Attribution and forensic challenges</li>
            </ul>
          </li>
          <li><strong>Operational Considerations:</strong>
            <ul>
              <li>Resource requirements and complexity</li>
              <li>Timeline and urgency constraints</li>
              <li>Legal and ethical boundaries</li>
              <li>Operational security requirements</li>
              <li>Backup and alternative vectors</li>
            </ul>
          </li>
        </ul>
        
        <h3>Multi-Vector Attack Strategies</h3>
        <ul>
          <li><strong>Coordinated Campaigns:</strong>
            <ul>
              <li>Simultaneous multiple attack vectors</li>
              <li>Sequential escalation strategies</li>
              <li>Diversified target selection</li>
              <li>Cross-platform and cross-channel attacks</li>
              <li>Adaptive vector selection</li>
            </ul>
          </li>
          <li><strong>Persistence and Redundancy:</strong>
            <ul>
              <li>Multiple entry points establishment</li>
              <li>Backup access mechanisms</li>
              <li>Distributed attack infrastructure</li>
              <li>Failover and recovery procedures</li>
              <li>Long-term access maintenance</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced Phishing and Social Engineering",
      content: `
        <h2>Advanced Phishing and Social Engineering</h2>
        <p>Sophisticated phishing attacks remain one of the most effective initial access vectors, leveraging human psychology and technical deception.</p>
        
        <h3>Spear-Phishing Campaign Development</h3>
        <ul>
          <li><strong>Target Research and Profiling:</strong>
            <ul>
              <li>Individual and organizational intelligence</li>
              <li>Communication patterns and preferences</li>
              <li>Professional relationships and hierarchies</li>
              <li>Personal interests and activities</li>
              <li>Technology usage and platforms</li>
            </ul>
          </li>
          <li><strong>Pretext Development:</strong>
            <ul>
              <li>Believable scenarios and contexts</li>
              <li>Timely and relevant content</li>
              <li>Authority and urgency elements</li>
              <li>Personalization and customization</li>
              <li>Multi-stage relationship building</li>
            </ul>
          </li>
          <li><strong>Technical Implementation:</strong>
            <ul>
              <li>Domain spoofing and typosquatting</li>
              <li>Email authentication bypass</li>
              <li>Landing page development and hosting</li>
              <li>Payload delivery mechanisms</li>
              <li>Tracking and analytics integration</li>
            </ul>
          </li>
        </ul>
        
        <h3>Business Email Compromise (BEC)</h3>
        <ul>
          <li><strong>Executive Impersonation:</strong>
            <ul>
              <li>CEO and C-level executive targeting</li>
              <li>Financial and procurement fraud</li>
              <li>Wire transfer and payment requests</li>
              <li>Vendor and supplier impersonation</li>
              <li>Urgent business transaction scenarios</li>
            </ul>
          </li>
          <li><strong>Account Takeover:</strong>
            <ul>
              <li>Credential harvesting and reuse</li>
              <li>Email account compromise</li>
              <li>Internal communication monitoring</li>
              <li>Business process understanding</li>
              <li>Trust relationship exploitation</li>
            </ul>
          </li>
          <li><strong>Supply Chain Targeting:</strong>
            <ul>
              <li>Vendor and partner impersonation</li>
              <li>Invoice and payment manipulation</li>
              <li>Contract and agreement fraud</li>
              <li>Service provider compromise</li>
              <li>Third-party trust exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Delivery Mechanisms</h3>
        <ul>
          <li><strong>Malicious Attachments:</strong>
            <ul>
              <li>Office macro-enabled documents</li>
              <li>PDF and archive file exploits</li>
              <li>Executable disguised as documents</li>
              <li>Polyglot and multi-format files</li>
              <li>Cloud storage and sharing links</li>
            </ul>
          </li>
          <li><strong>Malicious Links and Redirects:</strong>
            <ul>
              <li>URL shortening and obfuscation</li>
              <li>Legitimate service abuse</li>
              <li>Drive-by download attacks</li>
              <li>Browser and plugin exploitation</li>
              <li>Social media and messaging platforms</li>
            </ul>
          </li>
          <li><strong>Watering Hole Attacks:</strong>
            <ul>
              <li>Industry-specific website compromise</li>
              <li>Professional association portals</li>
              <li>News and information sites</li>
              <li>Software download repositories</li>
              <li>Conference and event websites</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Exploitation of Public-Facing Applications",
      content: `
        <h2>Exploitation of Public-Facing Applications</h2>
        <p>Web applications and internet-facing services provide direct attack vectors for gaining initial access through technical vulnerabilities.</p>
        
        <h3>Web Application Vulnerabilities</h3>
        <ul>
          <li><strong>Injection Attacks:</strong>
            <ul>
              <li>SQL injection and database compromise</li>
              <li>Command injection and OS execution</li>
              <li>LDAP and NoSQL injection</li>
              <li>XML and XXE injection</li>
              <li>Template and expression injection</li>
            </ul>
          </li>
          <li><strong>Authentication and Session Flaws:</strong>
            <ul>
              <li>Broken authentication mechanisms</li>
              <li>Session fixation and hijacking</li>
              <li>Password reset vulnerabilities</li>
              <li>Multi-factor authentication bypass</li>
              <li>OAuth and SSO implementation flaws</li>
            </ul>
          </li>
          <li><strong>Access Control Vulnerabilities:</strong>
            <ul>
              <li>Broken authorization and privilege escalation</li>
              <li>Insecure direct object references</li>
              <li>Missing function-level access controls</li>
              <li>Cross-origin resource sharing (CORS) misconfigurations</li>
              <li>API security vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Deserialization and Logic Flaws:</strong>
            <ul>
              <li>Insecure deserialization exploitation</li>
              <li>Business logic vulnerabilities</li>
              <li>Race conditions and timing attacks</li>
              <li>State manipulation and workflow bypass</li>
              <li>Input validation and sanitization flaws</li>
            </ul>
          </li>
        </ul>
        
        <h3>Remote Code Execution Techniques</h3>
        <ul>
          <li><strong>File Upload Vulnerabilities:</strong>
            <ul>
              <li>Unrestricted file upload exploitation</li>
              <li>File type and extension bypass</li>
              <li>Path traversal and directory manipulation</li>
              <li>Image and document metadata exploitation</li>
              <li>Archive extraction vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Server-Side Template Injection:</strong>
            <ul>
              <li>Template engine exploitation</li>
              <li>Expression language injection</li>
              <li>Code generation and execution</li>
              <li>Sandbox escape techniques</li>
              <li>Framework-specific vulnerabilities</li>
            </ul>
          </li>
          <li><strong>API and Service Exploitation:</strong>
            <ul>
              <li>REST and GraphQL API vulnerabilities</li>
              <li>SOAP and XML service attacks</li>
              <li>Microservice communication exploitation</li>
              <li>Container and orchestration attacks</li>
              <li>Serverless function exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Infrastructure and Service Attacks</h3>
        <ul>
          <li><strong>Network Service Exploitation:</strong>
            <ul>
              <li>SSH and remote access services</li>
              <li>Database and storage services</li>
              <li>Email and messaging systems</li>
              <li>File sharing and collaboration tools</li>
              <li>Network infrastructure devices</li>
            </ul>
          </li>
          <li><strong>Cloud Service Attacks:</strong>
            <ul>
              <li>Cloud storage bucket exploitation</li>
              <li>Container registry compromise</li>
              <li>Serverless function attacks</li>
              <li>Cloud database exposure</li>
              <li>API gateway vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Supply Chain Compromise:</strong>
            <ul>
              <li>Third-party library vulnerabilities</li>
              <li>Software update mechanism compromise</li>
              <li>Package repository attacks</li>
              <li>Build pipeline compromise</li>
              <li>Vendor and partner system access</li>
            </ul>
          </li>
        </ul>
        
        <h3>Zero-Day and Advanced Exploitation</h3>
        <ul>
          <li><strong>Vulnerability Research and Development:</strong>
            <ul>
              <li>Target application analysis</li>
              <li>Fuzzing and vulnerability discovery</li>
              <li>Exploit development and testing</li>
              <li>Reliability and stability optimization</li>
              <li>Evasion and anti-detection techniques</li>
            </ul>
          </li>
          <li><strong>Exploit Chaining and Combination:</strong>
            <ul>
              <li>Multi-stage exploitation workflows</li>
              <li>Privilege escalation integration</li>
              <li>Defense evasion techniques</li>
              <li>Persistence mechanism deployment</li>
              <li>Lateral movement preparation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Initial Access Techniques Lab",
    description: "Hands-on exercise in developing and executing various initial access techniques in controlled environments.",
    tasks: [
      {
        category: "Phishing Campaign Development",
        commands: [
          {
            command: "Create spear-phishing email campaign",
            description: "Develop targeted phishing emails with personalized content",
            hint: "Use OSINT research for personalization and credibility",
            expectedOutput: "Complete phishing campaign with emails and landing pages"
          },
          {
            command: "Set up credential harvesting infrastructure",
            description: "Deploy phishing infrastructure with credential collection",
            hint: "Implement proper OPSEC and attribution avoidance",
            expectedOutput: "Functional credential harvesting system"
          }
        ]
      },
      {
        category: "Web Application Exploitation",
        commands: [
          {
            command: "Identify and exploit SQL injection vulnerability",
            description: "Find and exploit SQL injection for initial access",
            hint: "Use manual testing and automated tools for discovery",
            expectedOutput: "Successful database compromise and access"
          },
          {
            command: "Exploit file upload vulnerability for RCE",
            description: "Achieve remote code execution through file upload",
            hint: "Bypass file type restrictions and execute payloads",
            expectedOutput: "Remote code execution and system access"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which initial access technique is most commonly used by advanced persistent threats (APTs)?",
      options: [
        "Exploitation of public-facing applications",
        "Spear-phishing emails",
        "Hardware additions",
        "Replication through removable media"
      ],
      correct: 1,
      explanation: "Spear-phishing emails are the most commonly used initial access technique by APTs because they are highly effective, difficult to detect, and can be precisely targeted to specific individuals or organizations."
    },
    {
      question: "What is the primary advantage of Business Email Compromise (BEC) attacks?",
      options: [
        "They require advanced technical skills",
        "They exploit human trust and business processes",
        "They are difficult to execute",
        "They only target technical vulnerabilities"
      ],
      correct: 1,
      explanation: "BEC attacks exploit human trust and established business processes, making them highly effective because they appear legitimate and often bypass technical security controls by targeting human decision-making."
    },
    {
      question: "Which factor is most important when selecting an initial access vector?",
      options: [
        "The complexity of the attack",
        "The cost of implementation",
        "The probability of success against the specific target",
        "The number of tools required"
      ],
      correct: 2,
      explanation: "The probability of success against the specific target is most important because it determines the likelihood of achieving the initial access objective, considering the target's security posture, user awareness, and defensive capabilities."
    }
  ]
};
