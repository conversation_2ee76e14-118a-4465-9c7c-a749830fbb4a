/**
 * Wireless Network Attacks Module
 */

export const wirelessAttacksContent = {
  id: "rt-16",
  pathId: "red-teaming",
  title: "Wireless Network Attacks",
  description: "Master wireless network penetration testing including WiFi attacks, Bluetooth exploitation, and radio frequency security assessment.",
  objectives: [
    "Understand wireless network protocols and security",
    "Master WiFi attack techniques and tools",
    "Learn Bluetooth and BLE exploitation methods",
    "Explore radio frequency and SDR attacks",
    "Understand wireless network defense evasion",
    "Master wireless pivoting and persistence techniques"
  ],
  difficulty: "Advanced",
  estimatedTime: 220,
  sections: [
    {
      title: "Wireless Network Fundamentals",
      content: `
        <h2>Wireless Network Technologies and Security</h2>
        <p>Understanding wireless network technologies and their security mechanisms is essential for effective wireless penetration testing.</p>
        
        <h3>WiFi Standards and Protocols</h3>
        <ul>
          <li><strong>IEEE 802.11 Standards:</strong>
            <ul>
              <li>802.11a/b/g/n/ac/ax (WiFi 6) evolution</li>
              <li>Frequency bands (2.4GHz, 5GHz, 6GHz)</li>
              <li>Channel allocation and interference</li>
              <li>MIMO and beamforming technologies</li>
              <li>Mesh networking and WiFi Direct</li>
            </ul>
          </li>
          <li><strong>WiFi Security Protocols:</strong>
            <ul>
              <li>WEP (Wired Equivalent Privacy) - deprecated</li>
              <li>WPA/WPA2 (WiFi Protected Access)</li>
              <li>WPA3 and enhanced security features</li>
              <li>Enterprise authentication (802.1X/EAP)</li>
              <li>Captive portals and guest networks</li>
            </ul>
          </li>
          <li><strong>WiFi Frame Types and Structure:</strong>
            <ul>
              <li>Management frames (beacon, probe, auth)</li>
              <li>Control frames (RTS, CTS, ACK)</li>
              <li>Data frames and encryption</li>
              <li>Frame injection and manipulation</li>
              <li>Radiotap headers and metadata</li>
            </ul>
          </li>
        </ul>
        
        <h3>Bluetooth and Short-Range Protocols</h3>
        <ul>
          <li><strong>Bluetooth Technology:</strong>
            <ul>
              <li>Bluetooth Classic vs Bluetooth Low Energy (BLE)</li>
              <li>Pairing and bonding mechanisms</li>
              <li>Service discovery and profiles</li>
              <li>Frequency hopping and adaptive frequency</li>
              <li>Bluetooth mesh networking</li>
            </ul>
          </li>
          <li><strong>Other Short-Range Protocols:</strong>
            <ul>
              <li>Zigbee and Z-Wave IoT protocols</li>
              <li>NFC (Near Field Communication)</li>
              <li>RFID and contactless systems</li>
              <li>LoRa and LoRaWAN</li>
              <li>Proprietary ISM band protocols</li>
            </ul>
          </li>
        </ul>
        
        <h3>Radio Frequency Fundamentals</h3>
        <ul>
          <li><strong>RF Concepts:</strong>
            <ul>
              <li>Frequency, wavelength, and propagation</li>
              <li>Modulation schemes (AM, FM, PSK, QAM)</li>
              <li>Antenna types and radiation patterns</li>
              <li>Path loss and signal attenuation</li>
              <li>Interference and noise analysis</li>
            </ul>
          </li>
          <li><strong>Software Defined Radio (SDR):</strong>
            <ul>
              <li>SDR hardware platforms (RTL-SDR, HackRF, USRP)</li>
              <li>GNU Radio and signal processing</li>
              <li>Spectrum analysis and visualization</li>
              <li>Signal demodulation and decoding</li>
              <li>Transmit capabilities and regulations</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "WiFi Attack Techniques",
      content: `
        <h2>WiFi Network Attack Techniques</h2>
        <p>WiFi networks present multiple attack vectors from passive reconnaissance to active exploitation and man-in-the-middle attacks.</p>
        
        <h3>WiFi Reconnaissance and Discovery</h3>
        <ul>
          <li><strong>Passive Reconnaissance:</strong>
            <ul>
              <li>Wireless network scanning and enumeration</li>
              <li>SSID and BSSID collection</li>
              <li>Client device identification</li>
              <li>Signal strength and coverage mapping</li>
              <li>Hidden network detection</li>
            </ul>
          </li>
          <li><strong>Active Reconnaissance:</strong>
            <ul>
              <li>Probe request injection</li>
              <li>Deauthentication attacks</li>
              <li>Fake access point deployment</li>
              <li>Client device probing</li>
              <li>Network topology mapping</li>
            </ul>
          </li>
          <li><strong>Wardriving and Mapping:</strong>
            <ul>
              <li>Mobile wireless surveying</li>
              <li>GPS coordinate logging</li>
              <li>Coverage area mapping</li>
              <li>Vulnerability assessment</li>
              <li>Database and reporting tools</li>
            </ul>
          </li>
        </ul>
        
        <h3>WEP and WPA/WPA2 Attacks</h3>
        <ul>
          <li><strong>WEP Attacks (Legacy):</strong>
            <ul>
              <li>IV (Initialization Vector) collection</li>
              <li>Statistical cryptanalysis</li>
              <li>ARP replay and packet injection</li>
              <li>Fragmentation and chopchop attacks</li>
              <li>Weak IV exploitation</li>
            </ul>
          </li>
          <li><strong>WPA/WPA2 PSK Attacks:</strong>
            <ul>
              <li>4-way handshake capture</li>
              <li>Dictionary and brute force attacks</li>
              <li>Rainbow table attacks</li>
              <li>PMKID attack (hashcat mode 22000)</li>
              <li>GPU-accelerated cracking</li>
            </ul>
          </li>
          <li><strong>WPA/WPA2 Enterprise Attacks:</strong>
            <ul>
              <li>EAP method downgrade attacks</li>
              <li>Certificate validation bypass</li>
              <li>Credential harvesting</li>
              <li>RADIUS server attacks</li>
              <li>Inner authentication exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced WiFi Attacks</h3>
        <ul>
          <li><strong>Evil Twin and Rogue AP:</strong>
            <ul>
              <li>Fake access point creation</li>
              <li>SSID spoofing and impersonation</li>
              <li>Captive portal attacks</li>
              <li>DNS spoofing and redirection</li>
              <li>SSL/TLS certificate attacks</li>
            </ul>
          </li>
          <li><strong>WiFi Pineapple Attacks:</strong>
            <ul>
              <li>PineAP and karma attacks</li>
              <li>Probe request harvesting</li>
              <li>Automatic client association</li>
              <li>Man-in-the-middle positioning</li>
              <li>Payload delivery and exploitation</li>
            </ul>
          </li>
          <li><strong>WPA3 and Modern Attacks:</strong>
            <ul>
              <li>Dragonfly handshake attacks</li>
              <li>Timing and side-channel attacks</li>
              <li>Downgrade to WPA2 attacks</li>
              <li>Implementation vulnerability exploitation</li>
              <li>Enhanced Open network attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>WiFi Attack Tools and Frameworks</h3>
        <ul>
          <li><strong>Aircrack-ng Suite:</strong>
            <ul>
              <li>airodump-ng for packet capture</li>
              <li>aireplay-ng for packet injection</li>
              <li>aircrack-ng for password cracking</li>
              <li>airbase-ng for fake AP creation</li>
              <li>airdecap-ng for traffic decryption</li>
            </ul>
          </li>
          <li><strong>Modern WiFi Tools:</strong>
            <ul>
              <li>Hashcat for GPU-accelerated cracking</li>
              <li>Bettercap for network attacks</li>
              <li>Wifite for automated attacks</li>
              <li>Kismet for wireless detection</li>
              <li>Reaver for WPS attacks</li>
            </ul>
          </li>
          <li><strong>Hardware Platforms:</strong>
            <ul>
              <li>WiFi Pineapple devices</li>
              <li>Alfa wireless adapters</li>
              <li>Raspberry Pi wireless platforms</li>
              <li>Custom antenna systems</li>
              <li>Portable attack platforms</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Bluetooth and RF Exploitation",
      content: `
        <h2>Bluetooth and Radio Frequency Exploitation</h2>
        <p>Bluetooth and other RF protocols present unique attack surfaces requiring specialized tools and techniques.</p>
        
        <h3>Bluetooth Classic Attacks</h3>
        <ul>
          <li><strong>Bluetooth Discovery and Enumeration:</strong>
            <ul>
              <li>Device discovery and inquiry</li>
              <li>Service discovery protocol (SDP)</li>
              <li>Device name and class identification</li>
              <li>Manufacturer and version detection</li>
              <li>Hidden and non-discoverable devices</li>
            </ul>
          </li>
          <li><strong>Bluetooth Pairing Attacks:</strong>
            <ul>
              <li>PIN brute force attacks</li>
              <li>Pairing eavesdropping</li>
              <li>Man-in-the-middle attacks</li>
              <li>SSP (Secure Simple Pairing) bypass</li>
              <li>Legacy pairing exploitation</li>
            </ul>
          </li>
          <li><strong>Bluetooth Service Exploitation:</strong>
            <ul>
              <li>OBEX (Object Exchange) attacks</li>
              <li>HID (Human Interface Device) injection</li>
              <li>Audio profile exploitation</li>
              <li>File transfer and phonebook access</li>
              <li>AT command injection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Bluetooth Low Energy (BLE) Attacks</h3>
        <ul>
          <li><strong>BLE Reconnaissance:</strong>
            <ul>
              <li>Advertisement packet analysis</li>
              <li>Service and characteristic discovery</li>
              <li>Device fingerprinting</li>
              <li>Connection parameter analysis</li>
              <li>Manufacturer data extraction</li>
            </ul>
          </li>
          <li><strong>BLE Exploitation Techniques:</strong>
            <ul>
              <li>Unauthorized characteristic access</li>
              <li>Notification and indication abuse</li>
              <li>Connection hijacking</li>
              <li>Replay and injection attacks</li>
              <li>Firmware and bootloader attacks</li>
            </ul>
          </li>
          <li><strong>BLE Security Bypass:</strong>
            <ul>
              <li>Encryption and authentication bypass</li>
              <li>Bonding and key management attacks</li>
              <li>Privacy feature circumvention</li>
              <li>Cross-transport key derivation</li>
              <li>Implementation vulnerability exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Software Defined Radio Attacks</h3>
        <ul>
          <li><strong>Signal Analysis and Reverse Engineering:</strong>
            <ul>
              <li>Unknown protocol identification</li>
              <li>Modulation scheme analysis</li>
              <li>Protocol reverse engineering</li>
              <li>Timing and synchronization analysis</li>
              <li>Error correction and encoding</li>
            </ul>
          </li>
          <li><strong>RF Replay and Injection:</strong>
            <ul>
              <li>Signal capture and replay</li>
              <li>Rolling code attacks</li>
              <li>Jamming and denial of service</li>
              <li>Signal amplification attacks</li>
              <li>Frequency hopping exploitation</li>
            </ul>
          </li>
          <li><strong>Specific Protocol Attacks:</strong>
            <ul>
              <li>Garage door and car key fobs</li>
              <li>TPMS (Tire Pressure Monitoring)</li>
              <li>Smart meter and utility protocols</li>
              <li>Industrial control systems</li>
              <li>Emergency services radio</li>
            </ul>
          </li>
        </ul>
        
        <h3>Wireless Attack Tools and Hardware</h3>
        <ul>
          <li><strong>Bluetooth Tools:</strong>
            <ul>
              <li>BlueZ and hcitool suite</li>
              <li>Ubertooth for BLE sniffing</li>
              <li>Btlejack for BLE attacks</li>
              <li>Spooftooph for device spoofing</li>
              <li>BlueMaho attack framework</li>
            </ul>
          </li>
          <li><strong>SDR Software:</strong>
            <ul>
              <li>GNU Radio signal processing</li>
              <li>GQRX spectrum analyzer</li>
              <li>Universal Radio Hacker (URH)</li>
              <li>Inspectrum signal analysis</li>
              <li>SDR# and CubicSDR</li>
            </ul>
          </li>
          <li><strong>Hardware Platforms:</strong>
            <ul>
              <li>RTL-SDR dongles</li>
              <li>HackRF One transceiver</li>
              <li>USRP software radio</li>
              <li>BladeRF and LimeSDR</li>
              <li>Ubertooth One for Bluetooth</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Wireless Network Penetration Testing Lab",
    description: "Hands-on exercise in wireless network attacks including WiFi exploitation, Bluetooth attacks, and RF signal analysis.",
    tasks: [
      {
        category: "WiFi Attacks",
        commands: [
          {
            command: "Capture WPA2 handshake and crack password",
            description: "Use aircrack-ng to capture and crack WPA2 network",
            hint: "Use airodump-ng for capture and aircrack-ng with wordlist",
            expectedOutput: "Successfully cracked WPA2 password"
          },
          {
            command: "Deploy evil twin access point",
            description: "Create fake AP to capture credentials",
            hint: "Use airbase-ng or hostapd with captive portal",
            expectedOutput: "Functional evil twin with credential harvesting"
          }
        ]
      },
      {
        category: "Bluetooth Exploitation",
        commands: [
          {
            command: "Enumerate Bluetooth devices and services",
            description: "Discover and analyze Bluetooth devices in range",
            hint: "Use hcitool and sdptool for discovery and enumeration",
            expectedOutput: "Complete Bluetooth device and service inventory"
          },
          {
            command: "Perform BLE characteristic analysis",
            description: "Analyze BLE device characteristics and permissions",
            hint: "Use gatttool or custom BLE scripts for analysis",
            expectedOutput: "BLE device security assessment report"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which WiFi attack technique is most effective against WPA2 networks without requiring client interaction?",
      options: [
        "Evil twin attack",
        "Deauthentication attack",
        "PMKID attack",
        "WPS PIN attack"
      ],
      correct: 2,
      explanation: "PMKID attack is most effective because it can extract the PMKID from the first EAPOL frame without requiring a full 4-way handshake or client interaction, making it stealthier and more reliable."
    },
    {
      question: "What is the primary security advantage of WPA3 over WPA2?",
      options: [
        "Stronger encryption algorithms",
        "Protection against offline dictionary attacks",
        "Faster authentication process",
        "Better backward compatibility"
      ],
      correct: 1,
      explanation: "WPA3's primary advantage is protection against offline dictionary attacks through the Dragonfly handshake (SAE), which provides forward secrecy and makes captured handshakes useless for offline cracking."
    },
    {
      question: "Which tool is most commonly used for Bluetooth Low Energy (BLE) packet capture and analysis?",
      options: [
        "Wireshark",
        "Ubertooth",
        "BlueZ",
        "Aircrack-ng"
      ],
      correct: 1,
      explanation: "Ubertooth is most commonly used for BLE packet capture and analysis because it's specifically designed for Bluetooth monitoring and can capture BLE advertisements and connections that other tools cannot."
    }
  ]
};
