/**
 * Network Performance and Optimization Module
 *
 * This file contains the content for the Network Performance and Optimization module
 * in the Network Fundamentals learning path.
 */

export const networkPerformanceAndOptimizationContent = {
  id: "nf-21",
  pathId: "networking-fundamentals",
  title: "Network Performance and Optimization",
  description: "Learn techniques to measure, analyze, and improve network performance.",
  objectives: [
    "Understand key network performance metrics and how to measure them",
    "Learn common causes of network performance issues",
    "Explore techniques for optimizing network performance",
    "Understand Quality of Service (QoS) and traffic prioritization"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Network Performance Metrics",
      type: "text",
      content: `
        <h2>Understanding Network Performance</h2>
        <p>Imagine you're trying to move water through a system of pipes. You'd want to know how much water can flow through (capacity), how fast it moves (speed), if there are any leaks (errors), and if there are any delays when you turn on the faucet (latency). Network performance metrics are similar measurements for data networks!</p>
        
        <p>Network performance refers to how efficiently and reliably a network can transmit data. It's measured using several key metrics that help identify how well the network is functioning and where improvements might be needed.</p>

        <div class="interactive-diagram">
          <svg id="performance-metrics-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Performance Metrics -->
            <rect x="100" y="50" width="600" height="300" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="80" text-anchor="middle" font-size="18" fill="#ffffff">Key Network Performance Metrics</text>
            
            <!-- Bandwidth -->
            <rect x="150" y="120" width="200" height="100" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="250" y="150" text-anchor="middle" font-size="16" fill="#ffffff">Bandwidth</text>
            <text x="250" y="175" text-anchor="middle" font-size="12" fill="#ffffff">Maximum data transfer rate</text>
            <text x="250" y="195" text-anchor="middle" font-size="12" fill="#ffffff">Measured in bits per second</text>
            <text x="250" y="215" text-anchor="middle" font-size="12" fill="#ffffff">(Mbps, Gbps)</text>
            
            <!-- Latency -->
            <rect x="450" y="120" width="200" height="100" rx="10" fill="#553c9a" stroke="#9f7aea" />
            <text x="550" y="150" text-anchor="middle" font-size="16" fill="#ffffff">Latency</text>
            <text x="550" y="175" text-anchor="middle" font-size="12" fill="#ffffff">Delay in data transmission</text>
            <text x="550" y="195" text-anchor="middle" font-size="12" fill="#ffffff">Measured in milliseconds</text>
            <text x="550" y="215" text-anchor="middle" font-size="12" fill="#ffffff">(ms)</text>
            
            <!-- Packet Loss -->
            <rect x="150" y="240" width="200" height="100" rx="10" fill="#2c5282" stroke="#63b3ed" />
            <text x="250" y="270" text-anchor="middle" font-size="16" fill="#ffffff">Packet Loss</text>
            <text x="250" y="295" text-anchor="middle" font-size="12" fill="#ffffff">Percentage of packets that</text>
            <text x="250" y="315" text-anchor="middle" font-size="12" fill="#ffffff">fail to reach destination</text>
            <text x="250" y="335" text-anchor="middle" font-size="12" fill="#ffffff">(%)</text>
            
            <!-- Jitter -->
            <rect x="450" y="240" width="200" height="100" rx="10" fill="#c53030" stroke="#fc8181" />
            <text x="550" y="270" text-anchor="middle" font-size="16" fill="#ffffff">Jitter</text>
            <text x="550" y="295" text-anchor="middle" font-size="12" fill="#ffffff">Variation in packet delay</text>
            <text x="550" y="315" text-anchor="middle" font-size="12" fill="#ffffff">Measured in milliseconds</text>
            <text x="550" y="335" text-anchor="middle" font-size="12" fill="#ffffff">(ms)</text>
          </svg>
        </div>

        <h3>Key Performance Metrics</h3>
        <p>There are several important metrics used to measure network performance:</p>
        
        <h4>Bandwidth</h4>
        <p>Bandwidth refers to the maximum rate at which data can be transferred across a network connection in a given amount of time.</p>
        <ul>
          <li><strong>Measured in:</strong> Bits per second (bps, Kbps, Mbps, Gbps)</li>
          <li><strong>Example:</strong> A 100 Mbps connection can theoretically transfer 100 million bits per second</li>
          <li><strong>Importance:</strong> Higher bandwidth allows more data to be transferred simultaneously</li>
        </ul>

        <h4>Latency</h4>
        <p>Latency is the time it takes for data to travel from source to destination.</p>
        <ul>
          <li><strong>Measured in:</strong> Milliseconds (ms)</li>
          <li><strong>Example:</strong> A ping time of 50ms means it takes 50 milliseconds for data to travel to a server and back</li>
          <li><strong>Importance:</strong> Lower latency means more responsive applications, especially important for real-time applications like video calls and online gaming</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Highway Analogy</h3>
          <p>Think of a network like a highway system:</p>
          <ul>
            <li><strong>Bandwidth</strong> is like the number of lanes on the highway - more lanes allow more cars to travel at once</li>
            <li><strong>Latency</strong> is like the time it takes to drive from one point to another - affected by distance and speed limits</li>
            <li><strong>Packet loss</strong> is like cars breaking down or taking wrong exits - they never reach their destination</li>
            <li><strong>Jitter</strong> is like inconsistent travel times due to varying traffic conditions - sometimes the trip takes 30 minutes, sometimes 45</li>
            <li><strong>Throughput</strong> is the actual number of cars that successfully complete the journey in a given time</li>
          </ul>
          <p>Just as highway engineers monitor these factors to improve road systems, network engineers monitor performance metrics to optimize networks!</p>
        </div>

        <h4>Packet Loss</h4>
        <p>Packet loss occurs when data packets fail to reach their destination.</p>
        <ul>
          <li><strong>Measured in:</strong> Percentage (%)</li>
          <li><strong>Example:</strong> 2% packet loss means 2 out of every 100 packets are lost during transmission</li>
          <li><strong>Importance:</strong> Even small amounts of packet loss can significantly impact application performance, especially for real-time applications</li>
        </ul>

        <h4>Jitter</h4>
        <p>Jitter is the variation in latency over time.</p>
        <ul>
          <li><strong>Measured in:</strong> Milliseconds (ms)</li>
          <li><strong>Example:</strong> If packets sometimes take 20ms to arrive and other times take 40ms, the jitter would be around 20ms</li>
          <li><strong>Importance:</strong> High jitter can cause stuttering in voice and video applications</li>
        </ul>

        <h4>Throughput</h4>
        <p>Throughput is the actual amount of data successfully transferred over a network in a given time period.</p>
        <ul>
          <li><strong>Measured in:</strong> Bits per second (bps, Kbps, Mbps, Gbps)</li>
          <li><strong>Example:</strong> A 100 Mbps connection might have an actual throughput of 85 Mbps due to overhead and other factors</li>
          <li><strong>Importance:</strong> Throughput is the real-world performance users experience, as opposed to theoretical bandwidth</li>
        </ul>
      `
    },
    {
      title: "Network Optimization Techniques",
      type: "text",
      content: `
        <h2>Improving Network Performance</h2>
        <p>Once you understand your network's performance metrics, you can apply various techniques to optimize it. Let's explore some common optimization strategies:</p>

        <div class="interactive-diagram">
          <svg id="optimization-techniques-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- QoS -->
            <rect x="100" y="50" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">Quality of Service (QoS)</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">Prioritizes critical traffic</text>
            
            <rect x="500" y="70" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="90" text-anchor="middle" font-size="10" fill="#ffffff">Voice/Video: High Priority</text>
            <rect x="510" y="100" width="130" height="10" rx="5" fill="#38b2ac" stroke="none" />
            
            <text x="575" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Email: Low Priority</text>
            <rect x="510" y="130" width="50" height="10" rx="5" fill="#c53030" stroke="none" />
            
            <!-- Caching -->
            <rect x="100" y="170" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="200" text-anchor="start" font-size="16" fill="#ffffff">Caching</text>
            <text x="150" y="220" text-anchor="start" font-size="12" fill="#ffffff">Stores frequently accessed data locally</text>
            
            <rect x="500" y="190" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <circle cx="525" cy="220" r="15" fill="#553c9a" stroke="#9f7aea" />
            <text x="525" y="225" text-anchor="middle" font-size="12" fill="#ffffff">$</text>
            <text x="575" y="210" text-anchor="middle" font-size="10" fill="#ffffff">Cache Server</text>
            <path d="M 550 230 L 600 230" stroke="#ffffff" fill="none" stroke-width="1" />
            <text x="575" y="245" text-anchor="middle" font-size="8" fill="#ffffff">Stores local copies</text>
            
            <!-- Load Balancing -->
            <rect x="100" y="290" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="320" text-anchor="start" font-size="16" fill="#ffffff">Load Balancing</text>
            <text x="150" y="340" text-anchor="start" font-size="12" fill="#ffffff">Distributes traffic across multiple servers</text>
            
            <rect x="500" y="310" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <rect x="520" y="320" width="30" height="20" rx="2" fill="#2c5282" stroke="#63b3ed" />
            <text x="535" y="335" text-anchor="middle" font-size="8" fill="#ffffff">Server 1</text>
            
            <rect x="560" y="320" width="30" height="20" rx="2" fill="#2c5282" stroke="#63b3ed" />
            <text x="575" y="335" text-anchor="middle" font-size="8" fill="#ffffff">Server 2</text>
            
            <rect x="600" y="320" width="30" height="20" rx="2" fill="#2c5282" stroke="#63b3ed" />
            <text x="615" y="335" text-anchor="middle" font-size="8" fill="#ffffff">Server 3</text>
            
            <circle cx="575" cy="355" r="10" fill="#553c9a" stroke="#9f7aea" />
            <text x="575" y="360" text-anchor="middle" font-size="12" fill="#ffffff">⚖️</text>
            <text x="575" y="375" text-anchor="middle" font-size="8" fill="#ffffff">Load Balancer</text>
            
            <!-- Compression -->
            <rect x="100" y="410" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="440" text-anchor="start" font-size="16" fill="#ffffff">Compression</text>
            <text x="150" y="460" text-anchor="start" font-size="12" fill="#ffffff">Reduces data size before transmission</text>
            
            <rect x="500" y="430" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <rect x="510" y="440" width="60" height="20" rx="2" fill="#c53030" stroke="#fc8181" />
            <text x="540" y="455" text-anchor="middle" font-size="8" fill="#ffffff">10 MB File</text>
            
            <text x="580" y="455" text-anchor="middle" font-size="12" fill="#ffffff">→</text>
            
            <rect x="590" y="440" width="30" height="20" rx="2" fill="#38a169" stroke="#9ae6b4" />
            <text x="605" y="455" text-anchor="middle" font-size="8" fill="#ffffff">2 MB</text>
            
            <text x="575" y="480" text-anchor="middle" font-size="8" fill="#ffffff">80% Reduction</text>
            
            <!-- Traffic Shaping -->
            <rect x="100" y="530" width="600" height="50" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="560" text-anchor="start" font-size="16" fill="#ffffff">Traffic Shaping</text>
            <text x="350" y="560" text-anchor="start" font-size="12" fill="#ffffff">Controls flow of traffic to optimize performance</text>
          </svg>
        </div>

        <h3>Quality of Service (QoS)</h3>
        <p>QoS is a set of technologies that work on a network to guarantee its ability to dependably run high-priority applications and traffic under limited network capacity.</p>
        <p><strong>How QoS works:</strong></p>
        <ul>
          <li>Classifies network traffic and assigns priorities</li>
          <li>Allocates bandwidth based on priorities</li>
          <li>Manages congestion by queuing lower-priority traffic</li>
          <li>Shapes traffic to control flow rates</li>
        </ul>
        <p><strong>Common QoS priorities:</strong></p>
        <ul>
          <li>Voice and video calls (highest priority)</li>
          <li>Interactive applications</li>
          <li>Business-critical applications</li>
          <li>Email and file transfers</li>
          <li>Background downloads (lowest priority)</li>
        </ul>

        <h3>Caching</h3>
        <p>Caching stores frequently accessed data closer to users to reduce latency and bandwidth usage.</p>
        <p><strong>Types of caching:</strong></p>
        <ul>
          <li><strong>Browser caching:</strong> Stores web content locally on user devices</li>
          <li><strong>Proxy caching:</strong> Intermediate servers that store content for multiple users</li>
          <li><strong>Content Delivery Networks (CDNs):</strong> Distributed servers that cache content globally</li>
          <li><strong>DNS caching:</strong> Stores DNS query results to speed up domain resolution</li>
        </ul>

        <h3>Load Balancing</h3>
        <p>Load balancing distributes network traffic across multiple servers to ensure no single server becomes overwhelmed.</p>
        <p><strong>Benefits of load balancing:</strong></p>
        <ul>
          <li>Improves application responsiveness</li>
          <li>Increases availability and reliability</li>
          <li>Enables scalability</li>
          <li>Provides redundancy</li>
        </ul>
        <p><strong>Load balancing methods:</strong></p>
        <ul>
          <li>Round-robin (equal distribution)</li>
          <li>Least connections (sends to least busy server)</li>
          <li>IP hash (consistent routing based on client IP)</li>
          <li>Weighted (based on server capacity)</li>
        </ul>

        <h3>Compression</h3>
        <p>Compression reduces the size of data before transmission, saving bandwidth and reducing transfer times.</p>
        <p><strong>Common compression techniques:</strong></p>
        <ul>
          <li>HTTP compression (GZIP, Brotli)</li>
          <li>Image optimization (JPEG, WebP)</li>
          <li>Video compression (H.264, H.265)</li>
          <li>Text compression</li>
        </ul>

        <h3>Traffic Shaping</h3>
        <p>Traffic shaping controls the flow of traffic to optimize network performance.</p>
        <p><strong>Traffic shaping techniques:</strong></p>
        <ul>
          <li><strong>Rate limiting:</strong> Restricts bandwidth for certain types of traffic</li>
          <li><strong>Packet scheduling:</strong> Determines the order in which packets are transmitted</li>
          <li><strong>Bandwidth allocation:</strong> Assigns specific amounts of bandwidth to different applications</li>
          <li><strong>Burst handling:</strong> Manages temporary spikes in traffic</li>
        </ul>
      `
    }
  ]
};
