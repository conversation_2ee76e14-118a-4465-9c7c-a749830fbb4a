/**
 * Network Monitoring and Management Module
 *
 * This file contains the content for the Network Monitoring and Management module
 * in the Network Fundamentals learning path.
 */

export const networkMonitoringAndManagementContent = {
  id: "nf-17",
  pathId: "networking-fundamentals",
  title: "Network Monitoring and Management",
  description: "Learn tools and techniques for monitoring and managing network performance and security.",
  objectives: [
    "Understand the importance of network monitoring and management",
    "Learn about common monitoring tools and protocols like SNMP, NetFlow, and Syslog",
    "Explore network performance metrics and how to interpret them",
    "Understand network management best practices and strategies"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to Network Monitoring",
      type: "text",
      content: `
        <h2>What is Network Monitoring?</h2>
        <p>Imagine you're responsible for a busy highway system. You'd want to know if there's a traffic jam, an accident, or if someone is driving dangerously. Network monitoring is similar - it's about keeping an eye on your network to make sure everything is running smoothly!</p>
        
        <p>Network monitoring is the practice of continuously observing a computer network to detect problems, track performance, and ensure everything is working as expected. It helps network administrators identify issues before they become critical and affect users.</p>

        <div class="interactive-diagram">
          <svg id="network-monitoring-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Network Operations Center -->
            <rect x="300" y="50" width="200" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="90" text-anchor="middle" font-size="16" fill="#ffffff">Network Operations</text>
            <text x="400" y="110" text-anchor="middle" font-size="16" fill="#ffffff">Center (NOC)</text>
            
            <!-- Monitoring Dashboard -->
            <rect x="325" y="80" width="150" height="50" rx="5" fill="#1a202c" stroke="#4a5568" />
            <rect x="335" y="90" width="40" height="10" rx="2" fill="#38b2ac" stroke="none" />
            <rect x="385" y="90" width="40" height="10" rx="2" fill="#38b2ac" stroke="none" />
            <rect x="435" y="90" width="30" height="10" rx="2" fill="#c53030" stroke="none" />
            <rect x="335" y="110" width="50" height="10" rx="2" fill="#38b2ac" stroke="none" />
            <rect x="395" y="110" width="70" height="10" rx="2" fill="#38b2ac" stroke="none" />
            
            <!-- Network Devices -->
            <rect x="100" y="250" width="100" height="60" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="150" y="280" text-anchor="middle" font-size="14" fill="#ffffff">Router</text>
            <text x="150" y="300" text-anchor="middle" font-size="10" fill="#ffffff">CPU: 15%</text>
            
            <rect x="250" y="250" width="100" height="60" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="300" y="280" text-anchor="middle" font-size="14" fill="#ffffff">Switch</text>
            <text x="300" y="300" text-anchor="middle" font-size="10" fill="#ffffff">Ports: 24/48</text>
            
            <rect x="400" y="250" width="100" height="60" rx="10" fill="#c53030" stroke="#fc8181" />
            <text x="450" y="280" text-anchor="middle" font-size="14" fill="#ffffff">Server</text>
            <text x="450" y="300" text-anchor="middle" font-size="10" fill="#ffffff">CPU: 95%</text>
            
            <rect x="550" y="250" width="100" height="60" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="600" y="280" text-anchor="middle" font-size="14" fill="#ffffff">Firewall</text>
            <text x="600" y="300" text-anchor="middle" font-size="10" fill="#ffffff">Connections: 1.2K</text>
            
            <!-- Monitoring Connections -->
            <path d="M 150 250 L 350 150" stroke="#38b2ac" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <path d="M 300 250 L 375 150" stroke="#38b2ac" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <path d="M 450 250 L 400 150" stroke="#c53030" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <path d="M 600 250 L 425 150" stroke="#38b2ac" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            
            <!-- Alert -->
            <rect x="450" y="200" width="80" height="30" rx="15" fill="#c53030" stroke="#fc8181" />
            <text x="490" y="220" text-anchor="middle" font-size="12" fill="#ffffff">ALERT!</text>
          </svg>
        </div>

        <h3>Why is Network Monitoring Important?</h3>
        <p>Network monitoring is crucial for several reasons:</p>
        <ul>
          <li><strong>Proactive Problem Detection:</strong> Identify issues before they affect users</li>
          <li><strong>Performance Optimization:</strong> Ensure the network is running efficiently</li>
          <li><strong>Capacity Planning:</strong> Determine when upgrades are needed</li>
          <li><strong>Security:</strong> Detect unusual activity that might indicate a security breach</li>
          <li><strong>Compliance:</strong> Meet regulatory requirements for network management</li>
          <li><strong>Troubleshooting:</strong> Quickly identify the root cause of problems</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Network Doctor</h3>
          <p>Think of network monitoring like a doctor monitoring a patient's vital signs:</p>
          <ul>
            <li>The doctor regularly checks the patient's heart rate, blood pressure, and temperature (like monitoring bandwidth, CPU usage, and error rates)</li>
            <li>If any vital sign goes outside the normal range, an alarm sounds (like network alerts)</li>
            <li>The doctor can see trends over time to predict potential health issues (like performance trending)</li>
            <li>Regular checkups can catch problems before the patient feels sick (like proactive monitoring)</li>
          </ul>
          <p>Just as a doctor needs to monitor vital signs to keep a patient healthy, network administrators need to monitor network metrics to keep the network healthy!</p>
        </div>

        <h3>What to Monitor</h3>
        <p>Effective network monitoring covers several key areas:</p>
        
        <ol>
          <li><strong>Availability:</strong> Is the device or service up and running?</li>
          <li><strong>Performance:</strong> How quickly is data moving through the network?</li>
          <li><strong>Utilization:</strong> How much of the network capacity is being used?</li>
          <li><strong>Errors:</strong> Are there transmission errors or packet drops?</li>
          <li><strong>Configuration:</strong> Have there been unauthorized changes?</li>
          <li><strong>Security:</strong> Is there suspicious activity on the network?</li>
        </ol>
      `
    },
    {
      title: "Network Monitoring Protocols",
      type: "text",
      content: `
        <h2>Common Network Monitoring Protocols</h2>
        <p>Several protocols and technologies are used for network monitoring. Let's explore the most important ones:</p>

        <div class="interactive-diagram">
          <svg id="monitoring-protocols-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- SNMP -->
            <rect x="100" y="50" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">SNMP (Simple Network Management Protocol)</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">Collects information from network devices</text>
            
            <rect x="150" y="120" width="100" height="50" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="200" y="145" text-anchor="middle" font-size="10" fill="#ffffff">SNMP Manager</text>
            <text x="200" y="160" text-anchor="middle" font-size="8" fill="#ffffff">Monitoring System</text>
            
            <rect x="400" y="120" width="100" height="50" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="450" y="145" text-anchor="middle" font-size="10" fill="#ffffff">SNMP Agent</text>
            <text x="450" y="160" text-anchor="middle" font-size="8" fill="#ffffff">Network Device</text>
            
            <path d="M 250 130 L 400 130" stroke="#38b2ac" fill="none" stroke-width="2" />
            <text x="325" y="125" text-anchor="middle" font-size="8" fill="#ffffff">GET Request</text>
            
            <path d="M 400 160 L 250 160" stroke="#38b2ac" fill="none" stroke-width="2" />
            <text x="325" y="175" text-anchor="middle" font-size="8" fill="#ffffff">Response with Data</text>
            
            <path d="M 500 145 L 600 145" stroke="#38b2ac" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <text x="550" y="135" text-anchor="middle" font-size="8" fill="#ffffff">TRAP (Alert)</text>
            <rect x="600" y="120" width="50" height="50" rx="5" fill="#c53030" stroke="#fc8181" />
            <text x="625" y="145" text-anchor="middle" font-size="10" fill="#ffffff">Alert!</text>
            
            <!-- NetFlow -->
            <rect x="100" y="220" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="250" text-anchor="start" font-size="16" fill="#ffffff">NetFlow</text>
            <text x="150" y="270" text-anchor="start" font-size="12" fill="#ffffff">Analyzes network traffic patterns</text>
            
            <rect x="150" y="290" width="100" height="50" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="200" y="315" text-anchor="middle" font-size="10" fill="#ffffff">NetFlow Collector</text>
            <text x="200" y="330" text-anchor="middle" font-size="8" fill="#ffffff">Analysis System</text>
            
            <rect x="400" y="290" width="100" height="50" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="450" y="315" text-anchor="middle" font-size="10" fill="#ffffff">NetFlow Exporter</text>
            <text x="450" y="330" text-anchor="middle" font-size="8" fill="#ffffff">Router/Switch</text>
            
            <path d="M 400 315 L 250 315" stroke="#9f7aea" fill="none" stroke-width="2" />
            <text x="325" y="305" text-anchor="middle" font-size="8" fill="#ffffff">Flow Data</text>
            
            <rect x="550" y="290" width="100" height="50" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="600" y="315" text-anchor="middle" font-size="10" fill="#ffffff">Network Traffic</text>
            <path d="M 550 315 L 500 315" stroke="#9f7aea" fill="none" stroke-width="2" />
            
            <!-- Syslog -->
            <rect x="100" y="390" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="420" text-anchor="start" font-size="16" fill="#ffffff">Syslog</text>
            <text x="150" y="440" text-anchor="start" font-size="12" fill="#ffffff">Collects and centralizes log messages</text>
            
            <rect x="150" y="460" width="100" height="50" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="200" y="485" text-anchor="middle" font-size="10" fill="#ffffff">Syslog Server</text>
            <text x="200" y="500" text-anchor="middle" font-size="8" fill="#ffffff">Log Collection</text>
            
            <rect x="350" y="460" width="100" height="50" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="400" y="485" text-anchor="middle" font-size="10" fill="#ffffff">Network Device</text>
            <path d="M 350 485 L 250 485" stroke="#63b3ed" fill="none" stroke-width="2" />
            <text x="300" y="475" text-anchor="middle" font-size="8" fill="#ffffff">Log Messages</text>
            
            <rect x="500" y="460" width="100" height="50" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="550" y="485" text-anchor="middle" font-size="10" fill="#ffffff">Server</text>
            <path d="M 500 485 L 250 485" stroke="#63b3ed" fill="none" stroke-width="2" />
            <text x="375" y="505" text-anchor="middle" font-size="8" fill="#ffffff">Log Messages</text>
          </svg>
        </div>

        <h3>SNMP (Simple Network Management Protocol)</h3>
        <p>SNMP is one of the most widely used protocols for network monitoring. It allows monitoring systems to collect information from network devices.</p>
        <p><strong>Key components:</strong></p>
        <ul>
          <li><strong>SNMP Manager:</strong> The monitoring system that collects and processes data</li>
          <li><strong>SNMP Agent:</strong> Software on network devices that collects and stores management information</li>
          <li><strong>Management Information Base (MIB):</strong> A database of information that can be queried</li>
          <li><strong>SNMP Traps:</strong> Alerts sent by agents when specific events occur</li>
        </ul>

        <h3>NetFlow</h3>
        <p>NetFlow is a network protocol developed by Cisco for collecting IP traffic information. It provides detailed visibility into network traffic patterns.</p>
        <p><strong>What NetFlow monitors:</strong></p>
        <ul>
          <li>Source and destination IP addresses</li>
          <li>Source and destination ports</li>
          <li>Protocol type</li>
          <li>Class of service</li>
          <li>Router or switch interface</li>
          <li>Next-hop IP addresses</li>
        </ul>

        <h3>Syslog</h3>
        <p>Syslog is a standard for message logging. It allows devices to send event notification messages to logging servers.</p>
        <p><strong>Benefits of Syslog:</strong></p>
        <ul>
          <li>Centralized logging from multiple devices</li>
          <li>Real-time monitoring of system events</li>
          <li>Historical data for troubleshooting</li>
          <li>Security event monitoring</li>
          <li>Compliance with regulatory requirements</li>
        </ul>
      `
    }
  ]
};
