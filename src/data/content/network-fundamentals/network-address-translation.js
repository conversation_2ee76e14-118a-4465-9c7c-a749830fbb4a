/**
 * Network Address Translation (NAT) Module
 *
 * This file contains the content for the Network Address Translation module
 * in the Network Fundamentals learning path.
 */

export const networkAddressTranslationContent = {
  id: "nf-12",
  pathId: "networking-fundamentals",
  title: "Network Address Translation",
  description: "Understand how NAT extends IPv4 addressing and provides a layer of security.",
  objectives: [
    "Understand what Network Address Translation (NAT) is and why it's important",
    "Learn about different types of NAT implementations",
    "Explore how Port Forwarding works",
    "Understand NAT traversal techniques"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to NAT",
      type: "text",
      content: `
        <h2>What is Network Address Translation (NAT)?</h2>
        <p>Imagine your home has one mailing address, but multiple people live there. When mail comes to your house, someone sorts it and makes sure each person gets their own letters. Network Address Translation (NAT) works in a similar way for computer networks!</p>
        
        <p>NAT is a process that changes the IP address information in packet headers while they're traveling between networks. It's most commonly used to allow multiple devices on a private network to connect to the internet using a single public IP address.</p>

        <div class="interactive-diagram">
          <svg id="nat-intro-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Internet Cloud -->
            <ellipse cx="650" cy="100" rx="120" ry="70" fill="#2a3548" stroke="#ffffff" />
            <text x="650" y="100" text-anchor="middle" font-size="16" fill="#ffffff">Internet</text>
            <text x="650" y="125" text-anchor="middle" font-size="12" fill="#ffffff">Public IP Addresses</text>
            
            <!-- Router with NAT -->
            <rect x="350" y="150" width="150" height="80" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="425" y="180" text-anchor="middle" font-size="14" fill="#ffffff">Router with NAT</text>
            <text x="425" y="200" text-anchor="middle" font-size="12" fill="#ffffff">Public IP: ***********</text>
            
            <!-- Home Network -->
            <rect x="100" y="300" width="600" height="80" rx="10" fill="#2c5282" stroke="#63b3ed" />
            <text x="400" y="330" text-anchor="middle" font-size="14" fill="#ffffff">Home Network</text>
            <text x="400" y="350" text-anchor="middle" font-size="12" fill="#ffffff">Private IPs: 192.168.1.x</text>
            
            <!-- Devices -->
            <rect x="150" y="320" width="80" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="190" y="345" text-anchor="middle" font-size="10" fill="#ffffff">************</text>
            
            <rect x="250" y="320" width="80" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="290" y="345" text-anchor="middle" font-size="10" fill="#ffffff">************</text>
            
            <rect x="350" y="320" width="80" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="390" y="345" text-anchor="middle" font-size="10" fill="#ffffff">************</text>
            
            <rect x="450" y="320" width="80" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="490" y="345" text-anchor="middle" font-size="10" fill="#ffffff">************</text>
            
            <rect x="550" y="320" width="80" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="590" y="345" text-anchor="middle" font-size="10" fill="#ffffff">192.168.1.14</text>
            
            <!-- Connecting lines -->
            <path d="M 425 150 L 650 100" stroke="#ffffff" fill="none" stroke-width="2" />
            <path d="M 425 230 L 425 300" stroke="#ffffff" fill="none" stroke-width="2" />
            
            <!-- NAT Process -->
            <path d="M 190 320 L 350 180" stroke="#38b2ac" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <text x="250" y="250" text-anchor="middle" font-size="10" fill="#ffffff">Source: ************</text>
            
            <path d="M 425 150 L 550 100" stroke="#38b2ac" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <text x="500" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Source: ***********</text>
          </svg>
        </div>

        <h3>Why Do We Need NAT?</h3>
        <p>NAT serves several important purposes:</p>
        <ul>
          <li><strong>IP Address Conservation:</strong> There aren't enough IPv4 addresses for every device in the world. NAT allows multiple devices to share one public IP address.</li>
          <li><strong>Security:</strong> NAT provides a basic level of security by hiding your internal network addresses from the outside world.</li>
          <li><strong>Network Flexibility:</strong> You can change your internal network addressing without affecting how you connect to external networks.</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Mail Sorting Office</h3>
          <p>Think of NAT like a mail sorting office for an apartment building:</p>
          <ul>
            <li>The apartment building has one street address (like your public IP)</li>
            <li>Each apartment has its own apartment number (like private IPs)</li>
            <li>When residents send mail, the mail room adds the building's address</li>
            <li>When mail comes back, the mail room looks at the apartment number and delivers it to the right resident</li>
          </ul>
          <p>This way, people outside only need to know the building's address, not every individual apartment number!</p>
        </div>

        <h3>How NAT Works</h3>
        <p>Here's a simple explanation of how NAT works:</p>
        
        <ol>
          <li>A device on your private network (like ************) sends a request to a website</li>
          <li>Your router receives this request and changes the source IP address from the private address (************) to its public IP address (***********)</li>
          <li>The router keeps track of this translation in a NAT table</li>
          <li>The website responds to your router's public IP address</li>
          <li>Your router receives the response, checks its NAT table, and forwards the response to the correct private IP address (************)</li>
        </ol>
      `
    },
    {
      title: "Types of NAT",
      type: "text",
      content: `
        <h2>Different Types of NAT</h2>
        <p>There are several different ways to implement Network Address Translation. Let's explore the most common types:</p>

        <div class="interactive-diagram">
          <svg id="nat-types-svg" viewBox="0 0 800 500" width="100%" height="500">
            <!-- Static NAT -->
            <rect x="100" y="50" width="600" height="120" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">Static NAT</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">One-to-one mapping between private and public IPs</text>
            
            <rect x="150" y="120" width="120" height="30" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="210" y="140" text-anchor="middle" font-size="12" fill="#ffffff">************</text>
            
            <text x="300" y="140" text-anchor="middle" font-size="16" fill="#ffffff">→</text>
            
            <rect x="330" y="120" width="120" height="30" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="390" y="140" text-anchor="middle" font-size="12" fill="#ffffff">************</text>
            
            <text x="500" y="140" text-anchor="start" font-size="12" fill="#ffffff">Always the same mapping</text>
            
            <!-- Dynamic NAT -->
            <rect x="100" y="190" width="600" height="120" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="220" text-anchor="start" font-size="16" fill="#ffffff">Dynamic NAT</text>
            <text x="150" y="240" text-anchor="start" font-size="12" fill="#ffffff">Maps private IPs to a pool of public IPs</text>
            
            <rect x="150" y="260" width="120" height="30" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="210" y="280" text-anchor="middle" font-size="12" fill="#ffffff">192.168.1.x</text>
            
            <text x="300" y="280" text-anchor="middle" font-size="16" fill="#ffffff">→</text>
            
            <rect x="330" y="260" width="120" height="30" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="390" y="280" text-anchor="middle" font-size="12" fill="#ffffff">203.0.113.x</text>
            
            <text x="500" y="280" text-anchor="start" font-size="12" fill="#ffffff">From a pool of public IPs</text>
            
            <!-- PAT/NAT Overload -->
            <rect x="100" y="330" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="360" text-anchor="start" font-size="16" fill="#ffffff">PAT (Port Address Translation)</text>
            <text x="150" y="380" text-anchor="start" font-size="12" fill="#ffffff">Many-to-one mapping using different ports</text>
            
            <rect x="150" y="400" width="120" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="210" y="420" text-anchor="middle" font-size="12" fill="#ffffff">************:1234</text>
            
            <text x="300" y="420" text-anchor="middle" font-size="16" fill="#ffffff">→</text>
            
            <rect x="330" y="400" width="120" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="390" y="420" text-anchor="middle" font-size="12" fill="#ffffff">***********:5000</text>
            
            <rect x="150" y="440" width="120" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="210" y="460" text-anchor="middle" font-size="12" fill="#ffffff">************:1234</text>
            
            <text x="300" y="460" text-anchor="middle" font-size="16" fill="#ffffff">→</text>
            
            <rect x="330" y="440" width="120" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="390" y="460" text-anchor="middle" font-size="12" fill="#ffffff">***********:5001</text>
            
            <text x="500" y="430" text-anchor="start" font-size="12" fill="#ffffff">Most common type</text>
            <text x="500" y="450" text-anchor="start" font-size="12" fill="#ffffff">Also called "NAT Overload"</text>
          </svg>
        </div>

        <h3>Static NAT</h3>
        <p>Static NAT creates a one-to-one mapping between a private IP address and a public IP address. This mapping stays the same all the time.</p>
        <p><strong>Use case:</strong> When you have servers that need to be accessible from the internet, like a web server or email server.</p>

        <h3>Dynamic NAT</h3>
        <p>Dynamic NAT maps private IP addresses to a pool of public IP addresses. When a device needs to connect to the internet, it gets assigned one of the available public IPs from the pool.</p>
        <p><strong>Use case:</strong> When you have more devices than public IP addresses, but not all devices need internet access at the same time.</p>

        <h3>Port Address Translation (PAT)</h3>
        <p>PAT, also called NAT Overload, is the most common type of NAT. It maps multiple private IP addresses to a single public IP address by using different port numbers.</p>
        <p><strong>Use case:</strong> Home networks and small businesses where many devices need to share one public IP address.</p>
        
        <p>For example, with PAT:</p>
        <ul>
          <li>Computer 1 (************) connects to a website using port 1234</li>
          <li>The router translates this to ***********:5000</li>
          <li>Computer 2 (************) also connects to a website using port 1234</li>
          <li>The router translates this to ***********:5001</li>
        </ul>
        <p>This way, both computers can use the same public IP address at the same time!</p>
      `
    }
  ]
};
