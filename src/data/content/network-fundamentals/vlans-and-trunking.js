/**
 * VLANs and Trunking Module
 *
 * This file contains the content for the VLANs and Trunking module
 * in the Network Fundamentals learning path.
 */

export const vlansAndTrunkingContent = {
  id: "nf-11",
  pathId: "networking-fundamentals",
  title: "VLANs and Trunking",
  description: "Learn how to segment networks using Virtual LANs and trunking protocols.",
  objectives: [
    "Understand what VLANs are and why they're important for network segmentation",
    "Learn how to configure VLANs on switches",
    "Understand trunking protocols and their role in VLAN communication",
    "Explore inter-VLAN routing techniques"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to VLANs",
      type: "text",
      content: `
        <h2>What are VLANs?</h2>
        <p>Imagine if your school had one big cafeteria where everyone ate together. It might get noisy and crowded! Now imagine if the cafeteria was divided into different sections - one for each grade level. That's similar to how VLANs work in computer networks.</p>
        
        <p>VLAN stands for <strong>Virtual Local Area Network</strong>. VLANs let you divide one physical network into multiple separate virtual networks. It's like having multiple separate switches, even though you only have one physical switch.</p>

        <div class="interactive-diagram">
          <svg id="vlan-intro-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Physical Switch -->
            <rect x="300" y="50" width="200" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="90" text-anchor="middle" font-size="16" fill="#ffffff">Physical Switch</text>
            <text x="400" y="120" text-anchor="middle" font-size="14" fill="#ffffff">One Device</text>
            
            <!-- VLAN 1: Engineering -->
            <rect x="100" y="250" width="150" height="80" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="175" y="280" text-anchor="middle" font-size="14" fill="#ffffff">VLAN 10</text>
            <text x="175" y="300" text-anchor="middle" font-size="12" fill="#ffffff">Engineering Dept</text>
            
            <!-- VLAN 2: Marketing -->
            <rect x="325" y="250" width="150" height="80" rx="10" fill="#553c9a" stroke="#9f7aea" />
            <text x="400" y="280" text-anchor="middle" font-size="14" fill="#ffffff">VLAN 20</text>
            <text x="400" y="300" text-anchor="middle" font-size="12" fill="#ffffff">Marketing Dept</text>
            
            <!-- VLAN 3: Finance -->
            <rect x="550" y="250" width="150" height="80" rx="10" fill="#2c5282" stroke="#63b3ed" />
            <text x="625" y="280" text-anchor="middle" font-size="14" fill="#ffffff">VLAN 30</text>
            <text x="625" y="300" text-anchor="middle" font-size="12" fill="#ffffff">Finance Dept</text>
            
            <!-- Connecting lines -->
            <path d="M 400 150 L 175 250" stroke="#38b2ac" fill="none" stroke-width="2" />
            <path d="M 400 150 L 400 250" stroke="#9f7aea" fill="none" stroke-width="2" />
            <path d="M 400 150 L 625 250" stroke="#63b3ed" fill="none" stroke-width="2" />
            
            <text x="250" y="200" text-anchor="middle" font-size="12" fill="#ffffff">Virtual Division</text>
            <text x="550" y="200" text-anchor="middle" font-size="12" fill="#ffffff">Virtual Division</text>
          </svg>
        </div>

        <h3>Why Use VLANs?</h3>
        <p>VLANs are super useful for several reasons:</p>
        <ul>
          <li><strong>Security:</strong> Keep sensitive departments (like Finance) separate from others</li>
          <li><strong>Organization:</strong> Group devices by department or function, not physical location</li>
          <li><strong>Broadcast Control:</strong> Reduce network traffic by limiting broadcasts to smaller groups</li>
          <li><strong>Flexibility:</strong> Move devices between networks without physically relocating them</li>
          <li><strong>Cost Savings:</strong> Use one physical switch instead of multiple switches</li>
        </ul>

        <div class="learning-module-story">
          <h3>The School Cafeteria</h3>
          <p>Think of a school cafeteria with different sections:</p>
          <ul>
            <li>The elementary students sit in one section (VLAN 10)</li>
            <li>The middle school students sit in another section (VLAN 20)</li>
            <li>The high school students sit in a third section (VLAN 30)</li>
          </ul>
          <p>Each group can talk among themselves, but they can't easily talk to the other groups. This keeps things organized and quieter. If a teacher makes an announcement to the elementary section, only those students hear it - not the entire cafeteria.</p>
        </div>

        <h3>How VLANs Work</h3>
        <p>VLANs work by adding a special tag to network frames (packets of data). This tag identifies which VLAN the frame belongs to. Switches use these tags to keep traffic separate between different VLANs.</p>
        
        <p>The standard for VLAN tagging is called <strong>IEEE 802.1Q</strong>. It adds a 4-byte tag to the Ethernet frame that includes the VLAN ID.</p>
      `
    },
    {
      title: "VLAN Configuration",
      type: "text",
      content: `
        <h2>Configuring VLANs on Switches</h2>
        <p>Setting up VLANs on a switch involves a few basic steps:</p>
        
        <ol>
          <li>Create the VLANs and give them names</li>
          <li>Assign switch ports to the appropriate VLANs</li>
          <li>Configure any trunk ports (we'll learn about these next)</li>
        </ol>

        <div class="interactive-diagram">
          <svg id="vlan-config-svg" viewBox="0 0 800 500" width="100%" height="500">
            <!-- Switch -->
            <rect x="300" y="50" width="200" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="90" text-anchor="middle" font-size="16" fill="#ffffff">Switch</text>
            
            <!-- Ports -->
            <rect x="310" y="130" width="20" height="20" rx="2" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="320" y="160" text-anchor="middle" font-size="10" fill="#ffffff">1</text>
            
            <rect x="340" y="130" width="20" height="20" rx="2" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="350" y="160" text-anchor="middle" font-size="10" fill="#ffffff">2</text>
            
            <rect x="370" y="130" width="20" height="20" rx="2" fill="#553c9a" stroke="#9f7aea" />
            <text x="380" y="160" text-anchor="middle" font-size="10" fill="#ffffff">3</text>
            
            <rect x="400" y="130" width="20" height="20" rx="2" fill="#553c9a" stroke="#9f7aea" />
            <text x="410" y="160" text-anchor="middle" font-size="10" fill="#ffffff">4</text>
            
            <rect x="430" y="130" width="20" height="20" rx="2" fill="#2c5282" stroke="#63b3ed" />
            <text x="440" y="160" text-anchor="middle" font-size="10" fill="#ffffff">5</text>
            
            <rect x="460" y="130" width="20" height="20" rx="2" fill="#2c5282" stroke="#63b3ed" />
            <text x="470" y="160" text-anchor="middle" font-size="10" fill="#ffffff">6</text>
            
            <!-- Devices -->
            <rect x="100" y="250" width="100" height="60" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="150" y="280" text-anchor="middle" font-size="12" fill="#ffffff">Engineering PC</text>
            <text x="150" y="300" text-anchor="middle" font-size="10" fill="#ffffff">VLAN 10</text>
            
            <rect x="250" y="250" width="100" height="60" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="300" y="280" text-anchor="middle" font-size="12" fill="#ffffff">Engineering PC</text>
            <text x="300" y="300" text-anchor="middle" font-size="10" fill="#ffffff">VLAN 10</text>
            
            <rect x="400" y="250" width="100" height="60" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="450" y="280" text-anchor="middle" font-size="12" fill="#ffffff">Marketing PC</text>
            <text x="450" y="300" text-anchor="middle" font-size="10" fill="#ffffff">VLAN 20</text>
            
            <rect x="550" y="250" width="100" height="60" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="600" y="280" text-anchor="middle" font-size="12" fill="#ffffff">Marketing PC</text>
            <text x="600" y="300" text-anchor="middle" font-size="10" fill="#ffffff">VLAN 20</text>
            
            <rect x="250" y="350" width="100" height="60" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="300" y="380" text-anchor="middle" font-size="12" fill="#ffffff">Finance PC</text>
            <text x="300" y="400" text-anchor="middle" font-size="10" fill="#ffffff">VLAN 30</text>
            
            <rect x="400" y="350" width="100" height="60" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="450" y="380" text-anchor="middle" font-size="12" fill="#ffffff">Finance PC</text>
            <text x="450" y="400" text-anchor="middle" font-size="10" fill="#ffffff">VLAN 30</text>
            
            <!-- Connecting lines -->
            <path d="M 320 150 L 150 250" stroke="#38b2ac" fill="none" stroke-width="2" />
            <path d="M 350 150 L 300 250" stroke="#38b2ac" fill="none" stroke-width="2" />
            <path d="M 380 150 L 450 250" stroke="#9f7aea" fill="none" stroke-width="2" />
            <path d="M 410 150 L 600 250" stroke="#9f7aea" fill="none" stroke-width="2" />
            <path d="M 440 150 L 300 350" stroke="#63b3ed" fill="none" stroke-width="2" />
            <path d="M 470 150 L 450 350" stroke="#63b3ed" fill="none" stroke-width="2" />
            
            <!-- Configuration Example -->
            <rect x="600" y="350" width="180" height="130" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="690" y="370" text-anchor="middle" font-size="12" fill="#ffffff">Switch Configuration</text>
            <text x="610" y="390" text-anchor="start" font-size="10" fill="#38b2ac">switch> enable</text>
            <text x="610" y="405" text-anchor="start" font-size="10" fill="#38b2ac">switch# configure terminal</text>
            <text x="610" y="420" text-anchor="start" font-size="10" fill="#38b2ac">switch(config)# vlan 10</text>
            <text x="610" y="435" text-anchor="start" font-size="10" fill="#38b2ac">switch(config-vlan)# name Engineering</text>
            <text x="610" y="450" text-anchor="start" font-size="10" fill="#38b2ac">switch(config)# interface fa0/1</text>
            <text x="610" y="465" text-anchor="start" font-size="10" fill="#38b2ac">switch(config-if)# switchport mode access</text>
            <text x="610" y="480" text-anchor="start" font-size="10" fill="#38b2ac">switch(config-if)# switchport access vlan 10</text>
          </svg>
        </div>
      `
    }
  ]
};
