/**
 * Knowledge Check for Routing Fundamentals Module
 */

export const routingKnowledgeCheckContent = {
  title: "Routing Fundamentals Knowledge Check",
  content: {
    questions: [
      {
        question: "What is the main function of a router in a network?",
        options: [
          "To connect devices within the same network using MAC addresses",
          "To forward data packets between different networks using IP addresses",
          "To amplify network signals over long distances",
          "To filter traffic based on security rules"
        ],
        correctAnswer: 1,
        explanation: "The main function of a router is to forward data packets between different networks using IP addresses. Routers operate at Layer 3 (Network Layer) of the OSI model and make forwarding decisions based on logical addressing (IP addresses)."
      },
      {
        question: "What information does a routing table contain?",
        options: [
          "MAC addresses and switch port mappings",
          "Username and password credentials",
          "Network destinations, next hops, and metrics",
          "Firewall rules and access control lists"
        ],
        correctAnswer: 2,
        explanation: "A routing table contains information about network destinations (where to send packets), next hops (the next router in the path), interfaces (which router interface to use), and metrics (values that help determine the best path)."
      },
      {
        question: "What is the main difference between static and dynamic routing?",
        options: [
          "Static routing is faster than dynamic routing",
          "Static routing uses IP addresses while dynamic routing uses MAC addresses",
          "Static routing requires manual configuration while dynamic routing automatically adapts to network changes",
          "Static routing works with IPv6 while dynamic routing only works with IPv4"
        ],
        correctAnswer: 2,
        explanation: "The main difference is that static routing requires manual configuration by network administrators, while dynamic routing uses protocols to automatically discover routes and adapt to network changes."
      },
      {
        question: "Which of the following is NOT a characteristic of RIP (Routing Information Protocol)?",
        options: [
          "Uses hop count as its metric",
          "Has a maximum hop count of 15",
          "Uses link state algorithm to calculate best paths",
          "Broadcasts its entire routing table to neighbors"
        ],
        correctAnswer: 2,
        explanation: "RIP is a distance vector protocol, not a link state protocol. It does not use a link state algorithm to calculate best paths. OSPF and IS-IS are examples of link state protocols."
      },
      {
        question: "Which routing protocol is most commonly used for routing between different autonomous systems on the internet?",
        options: [
          "RIP",
          "OSPF",
          "EIGRP",
          "BGP"
        ],
        correctAnswer: 3,
        explanation: "BGP (Border Gateway Protocol) is the routing protocol of the internet and is used for routing between different autonomous systems (AS). It's designed for exchanging routing information between different organizations."
      },
      {
        question: "What is route summarization (also called route aggregation)?",
        options: [
          "The process of combining multiple specific routes into a single more general route",
          "The process of breaking down a large network into smaller subnets",
          "The process of calculating the fastest route between two points",
          "The process of removing unused routes from a routing table"
        ],
        correctAnswer: 0,
        explanation: "Route summarization is the process of combining multiple specific routes into a single more general route. For example, instead of advertising four routes (***********/24, ***********/24, ***********/24, and ***********/24), you could advertise one summary route (***********/22)."
      },
      {
        question: "What is the administrative distance (AD) used for in routing?",
        options: [
          "To measure the physical distance between routers",
          "To determine the trustworthiness of routing information from different sources",
          "To calculate the bandwidth available on a link",
          "To measure the delay in milliseconds between routers"
        ],
        correctAnswer: 1,
        explanation: "Administrative distance (AD) is used to determine the trustworthiness of routing information received from different sources. When a router learns about a route to the same destination from multiple routing protocols, it will prefer the route with the lowest administrative distance."
      },
      {
        question: "Which of the following is a characteristic of link state routing protocols?",
        options: [
          "They send their entire routing table to neighbors periodically",
          "They have a maximum hop count limitation",
          "They build a complete map of the network topology",
          "They are simpler to configure than distance vector protocols"
        ],
        correctAnswer: 2,
        explanation: "Link state routing protocols build a complete map of the network topology. Each router maintains a database of the entire network structure and runs an algorithm (typically Dijkstra's shortest path algorithm) to calculate the best routes."
      },
      {
        question: "What is a default route in a routing table?",
        options: [
          "The route that was configured first",
          "The route with the lowest metric",
          "The route used when no specific match is found for a destination",
          "The route to the router's own interfaces"
        ],
        correctAnswer: 2,
        explanation: "A default route (sometimes called the gateway of last resort) is used when no specific match is found for a destination in the routing table. It's typically represented as 0.0.0.0/0 in IPv4 or ::/0 in IPv6."
      },
      {
        question: "Which of the following would be the best choice for a small network with a simple topology?",
        options: [
          "BGP",
          "OSPF",
          "Static routing",
          "IS-IS"
        ],
        correctAnswer: 2,
        explanation: "Static routing would be the best choice for a small network with a simple topology. It's simple to configure, doesn't require router resources for running routing protocols, and is predictable. For small networks, the administrative overhead of manually configuring routes is minimal."
      }
    ]
  },
  type: "quiz"
};
