/**
 * Packet Analysis with Wireshark Module
 *
 * This file contains the content for the Packet Analysis with Wireshark module
 * in the Network Fundamentals learning path.
 */

export const packetAnalysisWithWiresharkContent = {
  id: "nf-19",
  pathId: "networking-fundamentals",
  title: "Packet Analysis with Wireshark",
  description: "Learn to capture and analyze network traffic for troubleshooting and security analysis.",
  objectives: [
    "Understand what packet analysis is and why it's important",
    "Learn how to install and configure Wireshark",
    "Master basic and advanced packet capture techniques",
    "Develop skills to analyze common protocols and identify network issues",
    "Learn to use Wireshark for security analysis and threat detection"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to Packet Analysis",
      type: "text",
      content: `
        <h2>What is Packet Analysis?</h2>
        <p>Imagine if you could see every letter and package moving through the postal system - who sent it, where it's going, and even peek inside to see what's being sent. That's similar to what packet analysis lets you do with network traffic!</p>
        
        <p>Packet analysis (also called packet sniffing or protocol analysis) is the process of capturing and examining data packets as they travel across a network. It allows you to see the raw data being transmitted, helping you understand network behavior, troubleshoot problems, and identify security issues.</p>

        <div class="interactive-diagram">
          <svg id="packet-analysis-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Network Traffic -->
            <rect x="100" y="50" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="90" text-anchor="middle" font-size="16" fill="#ffffff">Network Traffic</text>
            <text x="400" y="120" text-anchor="middle" font-size="12" fill="#ffffff">Packets flowing through the network</text>
            
            <!-- Packet Capture -->
            <path d="M 400 150 L 400 200" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <text x="420" y="180" text-anchor="start" font-size="12" fill="#ffffff">Packet Capture</text>
            
            <!-- Wireshark -->
            <rect x="250" y="200" width="300" height="80" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="400" y="230" text-anchor="middle" font-size="16" fill="#ffffff">Wireshark</text>
            <text x="400" y="260" text-anchor="middle" font-size="12" fill="#ffffff">Packet Analysis Tool</text>
            
            <!-- Analysis Results -->
            <path d="M 400 280 L 400 330" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <text x="420" y="310" text-anchor="start" font-size="12" fill="#ffffff">Analysis</text>
            
            <!-- Results -->
            <rect x="100" y="330" width="200" height="50" rx="10" fill="#553c9a" stroke="#9f7aea" />
            <text x="200" y="360" text-anchor="middle" font-size="14" fill="#ffffff">Troubleshooting</text>
            
            <rect x="350" y="330" width="200" height="50" rx="10" fill="#2c5282" stroke="#63b3ed" />
            <text x="450" y="360" text-anchor="middle" font-size="14" fill="#ffffff">Security Analysis</text>
            
            <rect x="600" y="330" width="100" height="50" rx="10" fill="#c53030" stroke="#fc8181" />
            <text x="650" y="360" text-anchor="middle" font-size="14" fill="#ffffff">Forensics</text>
            
            <!-- Packets -->
            <rect x="150" y="80" width="40" height="20" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="170" y="95" text-anchor="middle" font-size="10" fill="#ffffff">HTTP</text>
            
            <rect x="250" y="100" width="40" height="20" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="270" y="115" text-anchor="middle" font-size="10" fill="#ffffff">DNS</text>
            
            <rect x="350" y="70" width="40" height="20" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="370" y="85" text-anchor="middle" font-size="10" fill="#ffffff">TCP</text>
            
            <rect x="450" y="90" width="40" height="20" rx="5" fill="#c53030" stroke="#fc8181" />
            <text x="470" y="105" text-anchor="middle" font-size="10" fill="#ffffff">ICMP</text>
            
            <rect x="550" y="80" width="40" height="20" rx="5" fill="#38a169" stroke="#9ae6b4" />
            <text x="570" y="95" text-anchor="middle" font-size="10" fill="#ffffff">TLS</text>
            
            <!-- Arrow Definitions -->
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#ffffff" />
              </marker>
            </defs>
          </svg>
        </div>

        <h3>Why is Packet Analysis Important?</h3>
        <p>Packet analysis is a crucial skill for network professionals for several reasons:</p>
        <ul>
          <li><strong>Troubleshooting:</strong> Identify and resolve network issues by seeing exactly what's happening at the packet level</li>
          <li><strong>Security:</strong> Detect suspicious activity, malware communication, and potential security breaches</li>
          <li><strong>Performance Optimization:</strong> Analyze traffic patterns to improve network performance</li>
          <li><strong>Network Visibility:</strong> Gain deep insights into how your network is being used</li>
          <li><strong>Protocol Understanding:</strong> Learn how network protocols actually work in practice</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Network Detective</h3>
          <p>Think of packet analysis like being a detective investigating a crime scene:</p>
          <ul>
            <li>The network is the crime scene</li>
            <li>Packets are the evidence and clues</li>
            <li>Wireshark is your magnifying glass and forensic tools</li>
            <li>You're looking for anything unusual or out of place</li>
            <li>You need to piece together what happened by examining the evidence</li>
          </ul>
          <p>Just as a detective needs to understand what normal looks like to spot what's abnormal, a network analyst needs to understand normal network traffic to identify problems!</p>
        </div>

        <h3>Introduction to Wireshark</h3>
        <p>Wireshark is the world's most popular network protocol analyzer. It's a free and open-source tool that allows you to capture and interactively browse the traffic running on a computer network.</p>
        
        <p><strong>Key features of Wireshark:</strong></p>
        <ul>
          <li>Deep inspection of hundreds of protocols</li>
          <li>Live capture and offline analysis</li>
          <li>Standard three-pane packet browser</li>
          <li>Multi-platform: Windows, Linux, macOS, and more</li>
          <li>Powerful display filters</li>
          <li>Rich VoIP analysis</li>
          <li>Decryption support for many protocols</li>
        </ul>
      `
    },
    {
      title: "Wireshark Basics",
      type: "text",
      content: `
        <h2>Getting Started with Wireshark</h2>
        <p>Let's explore the basic components of Wireshark and how to use them for packet analysis:</p>

        <div class="interactive-diagram">
          <svg id="wireshark-interface-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- Wireshark Interface -->
            <rect x="100" y="50" width="600" height="500" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="80" text-anchor="middle" font-size="18" fill="#ffffff">Wireshark Interface</text>
            
            <!-- Packet List Pane -->
            <rect x="120" y="100" width="560" height="150" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="150" y="120" text-anchor="start" font-size="14" fill="#ffffff">Packet List Pane</text>
            
            <rect x="130" y="130" width="540" height="20" rx="0" fill="#1e3a5f" stroke="none" />
            <text x="140" y="145" text-anchor="start" font-size="10" fill="#ffffff">No. Time Source Destination Protocol Length Info</text>
            
            <rect x="130" y="150" width="540" height="20" rx="0" fill="#1a202c" stroke="none" />
            <text x="140" y="165" text-anchor="start" font-size="10" fill="#38b2ac">1 0.000000 *********** 8.8.8.8 DNS 74 Standard query A example.com</text>
            
            <rect x="130" y="170" width="540" height="20" rx="0" fill="#1a202c" stroke="none" />
            <text x="140" y="185" text-anchor="start" font-size="10" fill="#38b2ac">2 0.045123 8.8.8.8 *********** DNS 90 Standard response A 93.184.216.34</text>
            
            <rect x="130" y="190" width="540" height="20" rx="0" fill="#1a202c" stroke="none" />
            <text x="140" y="205" text-anchor="start" font-size="10" fill="#38b2ac">3 0.045234 *********** 93.184.216.34 TCP 74 52431→80 [SYN] Seq=0</text>
            
            <rect x="130" y="210" width="540" height="20" rx="0" fill="#1a202c" stroke="none" />
            <text x="140" y="225" text-anchor="start" font-size="10" fill="#38b2ac">4 0.090345 93.184.216.34 *********** TCP 74 80→52431 [SYN, ACK] Seq=0 Ack=1</text>
            
            <!-- Packet Details Pane -->
            <rect x="120" y="260" width="560" height="150" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="150" y="280" text-anchor="start" font-size="14" fill="#ffffff">Packet Details Pane</text>
            
            <rect x="130" y="290" width="540" height="20" rx="0" fill="#1e3a5f" stroke="none" />
            <text x="140" y="305" text-anchor="start" font-size="10" fill="#ffffff">▶ Frame 2: 90 bytes on wire</text>
            
            <rect x="130" y="310" width="540" height="20" rx="0" fill="#1e3a5f" stroke="none" />
            <text x="140" y="325" text-anchor="start" font-size="10" fill="#ffffff">▶ Ethernet II, Src: 00:1a:2b:3c:4d:5e, Dst: 00:11:22:33:44:55</text>
            
            <rect x="130" y="330" width="540" height="20" rx="0" fill="#1e3a5f" stroke="none" />
            <text x="140" y="345" text-anchor="start" font-size="10" fill="#ffffff">▶ Internet Protocol Version 4, Src: 8.8.8.8, Dst: ***********</text>
            
            <rect x="130" y="350" width="540" height="20" rx="0" fill="#1e3a5f" stroke="none" />
            <text x="140" y="365" text-anchor="start" font-size="10" fill="#ffffff">▶ User Datagram Protocol, Src Port: 53, Dst Port: 34567</text>
            
            <rect x="130" y="370" width="540" height="20" rx="0" fill="#1e3a5f" stroke="none" />
            <text x="140" y="385" text-anchor="start" font-size="10" fill="#ffffff">▼ Domain Name System (response)</text>
            
            <!-- Packet Bytes Pane -->
            <rect x="120" y="420" width="560" height="110" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="150" y="440" text-anchor="start" font-size="14" fill="#ffffff">Packet Bytes Pane</text>
            
            <rect x="130" y="450" width="540" height="70" rx="0" fill="#1a202c" stroke="none" />
            <text x="140" y="465" text-anchor="start" font-size="10" fill="#38b2ac">0000 00 11 22 33 44 55 00 1a 2b 3c 4d 5e 08 00 45 00 ..\"3DU..+&lt;M^..E.</text>
            <text x="140" y="480" text-anchor="start" font-size="10" fill="#38b2ac">0010 00 4c 00 00 40 00 40 11 34 08 08 08 08 c0 a8 01 .L..@.@.4.......</text>
            <text x="140" y="495" text-anchor="start" font-size="10" fill="#38b2ac">0020 05 00 35 87 07 00 38 cd 21 34 81 80 00 01 00 01 ..5...8.!4......</text>
            <text x="140" y="510" text-anchor="start" font-size="10" fill="#38b2ac">0030 00 00 00 00 07 65 78 61 6d 70 6c 65 03 63 6f 6d .....example.com</text>
            
            <!-- Display Filter -->
            <rect x="120" y="540" width="560" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="140" y="560" text-anchor="start" font-size="10" fill="#38b2ac">dns || http || tcp.port == 80</text>
            <text x="500" y="560" text-anchor="start" font-size="10" fill="#ffffff">Display Filter</text>
          </svg>
        </div>

        <h3>The Wireshark Interface</h3>
        <p>Wireshark's interface consists of three main panes:</p>
        
        <ol>
          <li><strong>Packet List Pane (Top):</strong> Shows a summary of each packet captured, including:
            <ul>
              <li>Packet number</li>
              <li>Timestamp</li>
              <li>Source and destination addresses</li>
              <li>Protocol</li>
              <li>Length</li>
              <li>Info (brief description of the packet)</li>
            </ul>
          </li>
          <li><strong>Packet Details Pane (Middle):</strong> Shows the protocols and protocol fields of the selected packet in a tree view that you can expand to see more details.</li>
          <li><strong>Packet Bytes Pane (Bottom):</strong> Shows the raw data of the selected packet in hexadecimal and ASCII format.</li>
        </ol>

        <h3>Capturing Packets</h3>
        <p>To start capturing packets in Wireshark:</p>
        <ol>
          <li>Open Wireshark</li>
          <li>Select the network interface you want to capture from (e.g., Wi-Fi, Ethernet)</li>
          <li>Click the "Start capturing packets" button (blue shark fin)</li>
          <li>Wireshark will begin displaying packets as they're captured</li>
          <li>Click the "Stop capturing packets" button (red square) when you're done</li>
        </ol>

        <h3>Using Display Filters</h3>
        <p>Display filters are one of Wireshark's most powerful features. They allow you to show only the packets you're interested in.</p>
        
        <p><strong>Common display filter examples:</strong></p>
        <ul>
          <li><code>ip.addr == ***********</code> - Show only packets with this IP address as source or destination</li>
          <li><code>http</code> - Show only HTTP packets</li>
          <li><code>tcp.port == 80</code> - Show only TCP packets using port 80</li>
          <li><code>dns</code> - Show only DNS packets</li>
          <li><code>icmp</code> - Show only ICMP packets (like ping)</li>
        </ul>
        
        <p>You can combine filters using operators:</p>
        <ul>
          <li><code>&&</code> or <code>and</code> - Both conditions must be true</li>
          <li><code>||</code> or <code>or</code> - Either condition can be true</li>
          <li><code>!</code> or <code>not</code> - Negate a condition</li>
        </ul>
        
        <p><strong>Example:</strong> <code>http && ip.addr == ***********</code> - Show HTTP traffic to/from ***********</p>
      `
    }
  ]
};
