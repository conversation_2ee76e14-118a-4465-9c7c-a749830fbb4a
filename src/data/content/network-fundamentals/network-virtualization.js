/**
 * Network Virtualization Module
 *
 * This file contains the content for the Network Virtualization module
 * in the Network Fundamentals learning path.
 */

export const networkVirtualizationContent = {
  id: "nf-24",
  pathId: "networking-fundamentals",
  title: "Network Virtualization",
  description: "Learn about network virtualization technologies and their implementation.",
  objectives: [
    "Understand what network virtualization is and why it's important",
    "Learn about different types of network virtualization technologies",
    "Explore virtual network components and how they work",
    "Understand the benefits and challenges of implementing network virtualization"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to Network Virtualization",
      type: "text",
      content: `
        <h2>What is Network Virtualization?</h2>
        <p>Imagine if you had one big playground, but you wanted to create separate areas for different games - one area for basketball, another for soccer, and a third for tag - all without building actual walls. Network virtualization works in a similar way for computer networks!</p>
        
        <p>Network virtualization is the process of combining hardware and software network resources into a single, software-based virtual network. It allows you to create multiple isolated virtual networks on top of a single physical network infrastructure.</p>

        <div class="interactive-diagram">
          <svg id="network-virtualization-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Physical Network -->
            <rect x="100" y="300" width="600" height="50" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="330" text-anchor="middle" font-size="16" fill="#ffffff">Physical Network Infrastructure</text>
            
            <!-- Virtualization Layer -->
            <rect x="100" y="250" width="600" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="400" y="275" text-anchor="middle" font-size="14" fill="#ffffff">Network Virtualization Layer</text>
            
            <!-- Virtual Networks -->
            <rect x="120" y="150" width="150" height="80" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="195" y="180" text-anchor="middle" font-size="14" fill="#ffffff">Virtual Network A</text>
            <text x="195" y="200" text-anchor="middle" font-size="12" fill="#ffffff">Marketing Department</text>
            
            <rect x="325" y="150" width="150" height="80" rx="10" fill="#2c5282" stroke="#63b3ed" />
            <text x="400" y="180" text-anchor="middle" font-size="14" fill="#ffffff">Virtual Network B</text>
            <text x="400" y="200" text-anchor="middle" font-size="12" fill="#ffffff">Engineering Department</text>
            
            <rect x="530" y="150" width="150" height="80" rx="10" fill="#c53030" stroke="#fc8181" />
            <text x="605" y="180" text-anchor="middle" font-size="14" fill="#ffffff">Virtual Network C</text>
            <text x="605" y="200" text-anchor="middle" font-size="12" fill="#ffffff">Finance Department</text>
            
            <!-- Connections -->
            <path d="M 195 230 L 195 250" stroke="#38b2ac" fill="none" stroke-width="2" />
            <path d="M 400 230 L 400 250" stroke="#63b3ed" fill="none" stroke-width="2" />
            <path d="M 605 230 L 605 250" stroke="#fc8181" fill="none" stroke-width="2" />
            
            <!-- Virtual Devices -->
            <circle cx="150" y="180" r="10" fill="#1e3a5f" stroke="#38b2ac" />
            <circle cx="180" y="180" r="10" fill="#1e3a5f" stroke="#38b2ac" />
            <circle cx="210" y="180" r="10" fill="#1e3a5f" stroke="#38b2ac" />
            
            <circle cx="355" y="180" r="10" fill="#2c5282" stroke="#63b3ed" />
            <circle cx="385" y="180" r="10" fill="#2c5282" stroke="#63b3ed" />
            <circle cx="415" y="180" r="10" fill="#2c5282" stroke="#63b3ed" />
            <circle cx="445" y="180" r="10" fill="#2c5282" stroke="#63b3ed" />
            
            <circle cx="560" y="180" r="10" fill="#c53030" stroke="#fc8181" />
            <circle cx="590" y="180" r="10" fill="#c53030" stroke="#fc8181" />
            <circle cx="620" y="180" r="10" fill="#c53030" stroke="#fc8181" />
            
            <!-- Users -->
            <rect x="100" y="50" width="600" height="50" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="80" text-anchor="middle" font-size="16" fill="#ffffff">Users and Applications</text>
            
            <!-- User Connections -->
            <path d="M 195 100 L 195 150" stroke="#ffffff" fill="none" stroke-width="2" />
            <path d="M 400 100 L 400 150" stroke="#ffffff" fill="none" stroke-width="2" />
            <path d="M 605 100 L 605 150" stroke="#ffffff" fill="none" stroke-width="2" />
          </svg>
        </div>

        <h3>Why Use Network Virtualization?</h3>
        <p>Network virtualization offers several important benefits:</p>
        <ul>
          <li><strong>Isolation:</strong> Keep different networks separate for security and performance</li>
          <li><strong>Flexibility:</strong> Create, modify, and delete virtual networks without changing physical hardware</li>
          <li><strong>Resource Efficiency:</strong> Make better use of existing network infrastructure</li>
          <li><strong>Simplified Management:</strong> Manage multiple networks from a central location</li>
          <li><strong>Cost Savings:</strong> Reduce hardware costs and operational expenses</li>
          <li><strong>Faster Deployment:</strong> Set up new networks in minutes instead of days or weeks</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Magic Art Room</h3>
          <p>Think of network virtualization like a magic art room in a school:</p>
          <ul>
            <li>The school has one large art room with tables, chairs, and art supplies (the physical network)</li>
            <li>The art teacher uses magic to create invisible walls that divide the room into separate spaces (virtualization layer)</li>
            <li>One group of students works on clay sculptures, another on paintings, and a third on collages (virtual networks)</li>
            <li>Each group has its own set of tools and materials that can't be accessed by other groups (isolation)</li>
            <li>The teacher can easily rearrange the invisible walls to make spaces larger or smaller as needed (flexibility)</li>
            <li>The school saves money by having one room instead of three separate art rooms (resource efficiency)</li>
          </ul>
          <p>Just like the magic art room allows different art projects to happen in the same physical space, network virtualization allows different virtual networks to operate on the same physical infrastructure!</p>
        </div>

        <h3>Physical vs. Virtual Networks</h3>
        <p>Let's compare physical and virtual networks:</p>
        
        <h4>Physical Networks:</h4>
        <ul>
          <li>Consist of actual hardware devices (switches, routers, cables)</li>
          <li>Changes require physical access to devices</li>
          <li>Limited by hardware capabilities</li>
          <li>Expensive to scale and modify</li>
          <li>Each device has a fixed purpose</li>
        </ul>
        
        <h4>Virtual Networks:</h4>
        <ul>
          <li>Created using software on top of physical infrastructure</li>
          <li>Changes can be made remotely through software</li>
          <li>Can be easily reconfigured and scaled</li>
          <li>More cost-effective to deploy and manage</li>
          <li>Devices can serve multiple purposes</li>
        </ul>
      `
    },
    {
      title: "Network Virtualization Technologies",
      type: "text",
      content: `
        <h2>Types of Network Virtualization</h2>
        <p>There are several different approaches to network virtualization. Let's explore the main ones:</p>

        <div class="interactive-diagram">
          <svg id="virtualization-types-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- VLAN -->
            <rect x="100" y="50" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">Virtual LANs (VLANs)</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">Basic network segmentation within a switch</text>
            
            <rect x="500" y="70" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="95" text-anchor="middle" font-size="10" fill="#ffffff">Switch</text>
            <rect x="510" y="105" width="40" height="10" rx="2" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="530" y="113" text-anchor="middle" font-size="8" fill="#ffffff">VLAN 10</text>
            <rect x="560" y="105" width="40" height="10" rx="2" fill="#2c5282" stroke="#63b3ed" />
            <text x="580" y="113" text-anchor="middle" font-size="8" fill="#ffffff">VLAN 20</text>
            <rect x="610" y="105" width="40" height="10" rx="2" fill="#c53030" stroke="#fc8181" />
            <text x="630" y="113" text-anchor="middle" font-size="8" fill="#ffffff">VLAN 30</text>
            
            <!-- VRF -->
            <rect x="100" y="170" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="200" text-anchor="start" font-size="16" fill="#ffffff">Virtual Routing and Forwarding (VRF)</text>
            <text x="150" y="220" text-anchor="start" font-size="12" fill="#ffffff">Multiple routing tables in a single router</text>
            
            <rect x="500" y="190" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="215" text-anchor="middle" font-size="10" fill="#ffffff">Router</text>
            <rect x="510" y="225" width="40" height="10" rx="2" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="530" y="233" text-anchor="middle" font-size="8" fill="#ffffff">VRF Red</text>
            <rect x="560" y="225" width="40" height="10" rx="2" fill="#2c5282" stroke="#63b3ed" />
            <text x="580" y="233" text-anchor="middle" font-size="8" fill="#ffffff">VRF Blue</text>
            <rect x="610" y="225" width="40" height="10" rx="2" fill="#c53030" stroke="#fc8181" />
            <text x="630" y="233" text-anchor="middle" font-size="8" fill="#ffffff">VRF Green</text>
            
            <!-- VXLAN -->
            <rect x="100" y="290" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="320" text-anchor="start" font-size="16" fill="#ffffff">VXLAN (Virtual Extensible LAN)</text>
            <text x="150" y="340" text-anchor="start" font-size="12" fill="#ffffff">Overlay network that extends Layer 2 networks over Layer 3</text>
            
            <rect x="500" y="310" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="330" text-anchor="middle" font-size="10" fill="#ffffff">VXLAN Tunnel</text>
            <path d="M 510 350 L 640 350" stroke="#38b2ac" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <text x="575" y="365" text-anchor="middle" font-size="8" fill="#ffffff">Encapsulated Traffic</text>
            
            <!-- NFV -->
            <rect x="100" y="410" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="440" text-anchor="start" font-size="16" fill="#ffffff">Network Function Virtualization (NFV)</text>
            <text x="150" y="460" text-anchor="start" font-size="12" fill="#ffffff">Virtualizing network services that traditionally run on dedicated hardware</text>
            
            <rect x="500" y="430" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="450" text-anchor="middle" font-size="10" fill="#ffffff">Standard Server</text>
            <rect x="510" y="460" width="40" height="10" rx="2" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="530" y="468" text-anchor="middle" font-size="8" fill="#ffffff">vRouter</text>
            <rect x="560" y="460" width="40" height="10" rx="2" fill="#2c5282" stroke="#63b3ed" />
            <text x="580" y="468" text-anchor="middle" font-size="8" fill="#ffffff">vFirewall</text>
            <rect x="610" y="460" width="40" height="10" rx="2" fill="#c53030" stroke="#fc8181" />
            <text x="630" y="468" text-anchor="middle" font-size="8" fill="#ffffff">vLB</text>
            
            <!-- Overlay Networks -->
            <rect x="100" y="530" width="600" height="50" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="560" text-anchor="start" font-size="16" fill="#ffffff">Overlay Networks</text>
            <text x="350" y="560" text-anchor="start" font-size="12" fill="#ffffff">Virtual networks built on top of physical networks</text>
          </svg>
        </div>

        <h3>Virtual LANs (VLANs)</h3>
        <p>VLANs are one of the simplest forms of network virtualization. They allow you to divide a single physical switch into multiple virtual networks.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>Operates at Layer 2 (Data Link Layer) of the OSI model</li>
          <li>Uses VLAN tags to identify which virtual network a packet belongs to</li>
          <li>Limited to a single physical location or connected switches</li>
          <li>Typically supports up to 4,094 VLANs</li>
        </ul>
        <p><strong>Use cases:</strong> Separating departments within an organization, isolating guest networks, creating test environments</p>

        <h3>Virtual Routing and Forwarding (VRF)</h3>
        <p>VRF allows a single physical router to maintain multiple separate routing tables, creating multiple virtual routers within one device.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>Operates at Layer 3 (Network Layer) of the OSI model</li>
          <li>Each VRF has its own routing table, independent of others</li>
          <li>Provides strong isolation between different routing domains</li>
          <li>Can use overlapping IP addresses in different VRFs</li>
        </ul>
        <p><strong>Use cases:</strong> Multi-tenant environments, separating different customer networks, creating secure network segments</p>

        <h3>VXLAN (Virtual Extensible LAN)</h3>
        <p>VXLAN is an overlay technology that extends Layer 2 networks across Layer 3 boundaries, allowing virtual networks to span multiple physical locations.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>Encapsulates Layer 2 frames in UDP packets</li>
          <li>Supports up to 16 million virtual networks (compared to 4,094 for VLANs)</li>
          <li>Enables network virtualization across data centers</li>
          <li>Works well with east-west traffic in data centers</li>
        </ul>
        <p><strong>Use cases:</strong> Cloud computing environments, data center interconnection, large-scale multi-tenant networks</p>

        <h3>Network Function Virtualization (NFV)</h3>
        <p>NFV replaces dedicated network appliances (like firewalls, routers, and load balancers) with software running on standard servers.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>Decouples network functions from proprietary hardware</li>
          <li>Runs network services as virtual machines or containers</li>
          <li>Enables rapid deployment and scaling of network services</li>
          <li>Reduces hardware costs and power consumption</li>
        </ul>
        <p><strong>Common virtualized network functions:</strong></p>
        <ul>
          <li>Virtual routers</li>
          <li>Virtual firewalls</li>
          <li>Virtual load balancers</li>
          <li>Virtual WAN optimizers</li>
          <li>Virtual IDS/IPS (Intrusion Detection/Prevention Systems)</li>
        </ul>
      `
    }
  ]
};
