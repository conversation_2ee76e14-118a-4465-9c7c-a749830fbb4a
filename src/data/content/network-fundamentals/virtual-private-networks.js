/**
 * Virtual Private Networks (VPN) Module
 *
 * This file contains the content for the Virtual Private Networks module
 * in the Network Fundamentals learning path.
 */

export const virtualPrivateNetworksContent = {
  id: "nf-16",
  pathId: "networking-fundamentals",
  title: "Virtual Private Networks",
  description: "Understand how VPNs provide secure remote access and site-to-site connectivity.",
  objectives: [
    "Understand what Virtual Private Networks (VPNs) are and why they're important",
    "Learn about different VPN technologies and their use cases",
    "Explore common VPN protocols and their security features",
    "Understand VPN implementation considerations and best practices"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to VPNs",
      type: "text",
      content: `
        <h2>What are Virtual Private Networks (VPNs)?</h2>
        <p>Imagine you're sending a letter with private information through the mail. Anyone who handles that letter could potentially open it and read your private information. But what if you could put that letter inside a special locked box that only you and the recipient have the key to? That's similar to how a VPN works!</p>
        
        <p>A Virtual Private Network (VPN) creates a secure, encrypted connection (often called a "tunnel") between your device and another network over the internet. This tunnel protects your data from being seen or tampered with as it travels across the public internet.</p>

        <div class="interactive-diagram">
          <svg id="vpn-intro-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Internet Cloud -->
            <ellipse cx="400" cy="100" rx="350" ry="70" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="100" text-anchor="middle" font-size="16" fill="#ffffff">Public Internet</text>
            <text x="400" y="125" text-anchor="middle" font-size="12" fill="#ffffff">(Unsecured Network)</text>
            
            <!-- VPN Tunnel -->
            <path d="M 100 200 C 100 150, 700 150, 700 200" stroke="#38b2ac" fill="none" stroke-width="15" stroke-opacity="0.3" />
            <path d="M 100 200 C 100 250, 700 250, 700 200" stroke="#38b2ac" fill="none" stroke-width="15" stroke-opacity="0.3" />
            <text x="400" y="200" text-anchor="middle" font-size="16" fill="#ffffff">Encrypted VPN Tunnel</text>
            
            <!-- User Device -->
            <rect x="50" y="300" width="100" height="60" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="100" y="330" text-anchor="middle" font-size="14" fill="#ffffff">Your Device</text>
            <text x="100" y="350" text-anchor="middle" font-size="12" fill="#ffffff">VPN Client</text>
            
            <!-- VPN Server -->
            <rect x="650" y="300" width="100" height="60" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="700" y="330" text-anchor="middle" font-size="14" fill="#ffffff">VPN Server</text>
            
            <!-- Connecting lines -->
            <path d="M 100 300 L 100 200" stroke="#ffffff" fill="none" stroke-width="2" />
            <path d="M 700 300 L 700 200" stroke="#ffffff" fill="none" stroke-width="2" />
            
            <!-- Hackers/Snoopers -->
            <g transform="translate(250, 50)">
              <rect x="0" y="0" width="30" height="30" rx="15" fill="#c53030" stroke="#fc8181" />
              <text x="15" y="20" text-anchor="middle" font-size="20" fill="#ffffff">🕵️</text>
            </g>
            
            <g transform="translate(500, 70)">
              <rect x="0" y="0" width="30" height="30" rx="15" fill="#c53030" stroke="#fc8181" />
              <text x="15" y="20" text-anchor="middle" font-size="20" fill="#ffffff">🕵️</text>
            </g>
            
            <g transform="translate(350, 130)">
              <rect x="0" y="0" width="30" height="30" rx="15" fill="#c53030" stroke="#fc8181" />
              <text x="15" y="20" text-anchor="middle" font-size="20" fill="#ffffff">🕵️</text>
            </g>
          </svg>
        </div>

        <h3>Why Use VPNs?</h3>
        <p>VPNs are essential tools for several reasons:</p>
        <ul>
          <li><strong>Privacy:</strong> Protect your browsing activity from being monitored by your ISP, government, or malicious actors</li>
          <li><strong>Security:</strong> Encrypt your data to prevent it from being intercepted on public Wi-Fi networks</li>
          <li><strong>Remote Access:</strong> Securely connect to your organization's network from anywhere</li>
          <li><strong>Bypass Geo-restrictions:</strong> Access content that might be restricted in your location</li>
          <li><strong>Network Extension:</strong> Connect multiple office networks together securely</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Secret Tunnel</h3>
          <p>Imagine you need to transport valuable jewels across a dangerous town. Instead of walking through the open streets where thieves might see you, you discover there's a secret underground tunnel that goes directly from your house to the bank vault.</p>
          <p>The tunnel has special guards at both ends who check your ID, and the tunnel itself is completely secure from outsiders. No one can see you're carrying jewels, and no one can get into the tunnel without permission.</p>
          <p>This is exactly how a VPN works - it creates a secure "tunnel" through the public internet, encrypting your data so others can't see or access it, and it verifies who can enter the tunnel at both ends.</p>
        </div>

        <h3>How VPNs Work</h3>
        <p>VPNs use a combination of technologies to create secure connections:</p>
        
        <ol>
          <li><strong>Tunneling:</strong> Creates a private pathway for your data through the public internet</li>
          <li><strong>Encryption:</strong> Scrambles your data so only authorized parties can read it</li>
          <li><strong>Authentication:</strong> Verifies the identity of users and devices connecting to the VPN</li>
          <li><strong>Key Exchange:</strong> Securely shares encryption keys between the VPN client and server</li>
        </ol>
      `
    },
    {
      title: "VPN Technologies",
      type: "text",
      content: `
        <h2>Types of VPN Technologies</h2>
        <p>There are several different types of VPNs, each designed for specific use cases:</p>

        <div class="interactive-diagram">
          <svg id="vpn-types-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- Remote Access VPN -->
            <rect x="100" y="50" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">Remote Access VPN</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">Connects individual users to a remote network</text>
            
            <rect x="150" y="120" width="80" height="50" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="190" y="145" text-anchor="middle" font-size="10" fill="#ffffff">Remote User</text>
            
            <path d="M 230 145 L 400 145" stroke="#38b2ac" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <text x="315" y="135" text-anchor="middle" font-size="10" fill="#ffffff">VPN Tunnel</text>
            
            <rect x="400" y="120" width="80" height="50" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="440" y="145" text-anchor="middle" font-size="10" fill="#ffffff">VPN Server</text>
            
            <rect x="500" y="120" width="150" height="50" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="575" y="145" text-anchor="middle" font-size="10" fill="#ffffff">Corporate Network</text>
            
            <path d="M 480 145 L 500 145" stroke="#ffffff" fill="none" stroke-width="2" />
            
            <!-- Site-to-Site VPN -->
            <rect x="100" y="220" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="250" text-anchor="start" font-size="16" fill="#ffffff">Site-to-Site VPN</text>
            <text x="150" y="270" text-anchor="start" font-size="12" fill="#ffffff">Connects entire networks to each other</text>
            
            <rect x="150" y="290" width="150" height="50" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="225" y="315" text-anchor="middle" font-size="10" fill="#ffffff">Office A Network</text>
            
            <rect x="500" y="290" width="150" height="50" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="575" y="315" text-anchor="middle" font-size="10" fill="#ffffff">Office B Network</text>
            
            <path d="M 300 315 L 500 315" stroke="#9f7aea" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <text x="400" y="305" text-anchor="middle" font-size="10" fill="#ffffff">VPN Tunnel</text>
            
            <!-- SSL VPN -->
            <rect x="100" y="390" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="420" text-anchor="start" font-size="16" fill="#ffffff">SSL VPN</text>
            <text x="150" y="440" text-anchor="start" font-size="12" fill="#ffffff">Uses web browsers for VPN access</text>
            
            <rect x="150" y="460" width="80" height="50" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="190" y="485" text-anchor="middle" font-size="10" fill="#ffffff">Web Browser</text>
            
            <path d="M 230 485 L 400 485" stroke="#63b3ed" fill="none" stroke-width="2" stroke-dasharray="5,5" />
            <text x="315" y="475" text-anchor="middle" font-size="10" fill="#ffffff">HTTPS</text>
            
            <rect x="400" y="460" width="80" height="50" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="440" y="485" text-anchor="middle" font-size="10" fill="#ffffff">SSL Gateway</text>
            
            <rect x="500" y="460" width="150" height="50" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="575" y="485" text-anchor="middle" font-size="10" fill="#ffffff">Internal Resources</text>
            
            <path d="M 480 485 L 500 485" stroke="#ffffff" fill="none" stroke-width="2" />
          </svg>
        </div>

        <h3>Remote Access VPN</h3>
        <p>Remote Access VPNs allow individual users to connect to a private network from a remote location. This is the most common type of VPN for remote workers.</p>
        <p><strong>Use cases:</strong></p>
        <ul>
          <li>Employees working from home or traveling</li>
          <li>Contractors needing temporary access to company resources</li>
          <li>Personal users wanting to protect their privacy online</li>
        </ul>

        <h3>Site-to-Site VPN</h3>
        <p>Site-to-Site VPNs connect entire networks to each other. They're typically used to connect branch offices to a main office network.</p>
        <p><strong>Use cases:</strong></p>
        <ul>
          <li>Connecting multiple office locations</li>
          <li>Linking a company network to a partner organization</li>
          <li>Connecting to cloud infrastructure securely</li>
        </ul>

        <h3>SSL VPN</h3>
        <p>SSL VPNs use the Secure Sockets Layer protocol to provide VPN access through a web browser. No special client software is needed.</p>
        <p><strong>Use cases:</strong></p>
        <ul>
          <li>Providing access from devices where VPN clients can't be installed</li>
          <li>Giving limited access to specific web applications</li>
          <li>Supporting a wide range of devices and operating systems</li>
        </ul>
      `
    }
  ]
};
