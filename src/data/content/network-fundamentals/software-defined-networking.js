/**
 * Software-Defined Networking Module
 *
 * This file contains the content for the Software-Defined Networking module
 * in the Network Fundamentals learning path.
 */

export const softwareDefinedNetworkingContent = {
  id: "nf-23",
  pathId: "networking-fundamentals",
  title: "Software-Defined Networking",
  description: "Explore the concepts and benefits of software-defined networking.",
  objectives: [
    "Understand what Software-Defined Networking (SDN) is and how it differs from traditional networking",
    "Learn about the SDN architecture and its key components",
    "Explore SDN controllers and their role in network management",
    "Understand the benefits and challenges of implementing SDN"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to Software-Defined Networking",
      type: "text",
      content: `
        <h2>What is Software-Defined Networking?</h2>
        <p>Imagine if instead of having to walk to each light switch in your house to turn lights on and off, you had a special tablet that could control all the lights from one place. Software-Defined Networking (SDN) is like that tablet, but for computer networks!</p>
        
        <p>Software-Defined Networking (SDN) is an approach to networking that separates the control plane (the brains that decide where traffic should go) from the data plane (the muscles that move the traffic). This separation allows network administrators to manage network services through software rather than having to manually configure individual network devices.</p>

        <div class="interactive-diagram">
          <svg id="sdn-intro-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Traditional Networking -->
            <rect x="100" y="50" width="250" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="225" y="70" text-anchor="middle" font-size="14" fill="#ffffff">Traditional Networking</text>
            
            <!-- Traditional Network Devices -->
            <rect x="130" y="90" width="60" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="160" y="115" text-anchor="middle" font-size="10" fill="#ffffff">Router</text>
            <text x="160" y="130" text-anchor="middle" font-size="8" fill="#ffffff">Control + Data</text>
            
            <rect x="210" y="90" width="60" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="240" y="115" text-anchor="middle" font-size="10" fill="#ffffff">Switch</text>
            <text x="240" y="130" text-anchor="middle" font-size="8" fill="#ffffff">Control + Data</text>
            
            <rect x="130" y="150" width="60" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="160" y="175" text-anchor="middle" font-size="10" fill="#ffffff">Firewall</text>
            <text x="160" y="190" text-anchor="middle" font-size="8" fill="#ffffff">Control + Data</text>
            
            <rect x="210" y="150" width="60" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="240" y="175" text-anchor="middle" font-size="10" fill="#ffffff">Load Balancer</text>
            <text x="240" y="190" text-anchor="middle" font-size="8" fill="#ffffff">Control + Data</text>
            
            <!-- SDN -->
            <rect x="450" y="50" width="250" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="575" y="70" text-anchor="middle" font-size="14" fill="#ffffff">Software-Defined Networking</text>
            
            <!-- SDN Controller -->
            <rect x="500" y="90" width="150" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="575" y="115" text-anchor="middle" font-size="12" fill="#ffffff">SDN Controller</text>
            <text x="575" y="130" text-anchor="middle" font-size="8" fill="#ffffff">Centralized Control Plane</text>
            
            <!-- SDN Network Devices -->
            <rect x="480" y="150" width="60" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="510" y="175" text-anchor="middle" font-size="10" fill="#ffffff">Switch</text>
            <text x="510" y="190" text-anchor="middle" font-size="8" fill="#ffffff">Data Plane Only</text>
            
            <rect x="550" y="150" width="60" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="580" y="175" text-anchor="middle" font-size="10" fill="#ffffff">Switch</text>
            <text x="580" y="190" text-anchor="middle" font-size="8" fill="#ffffff">Data Plane Only</text>
            
            <rect x="620" y="150" width="60" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="650" y="175" text-anchor="middle" font-size="10" fill="#ffffff">Switch</text>
            <text x="650" y="190" text-anchor="middle" font-size="8" fill="#ffffff">Data Plane Only</text>
            
            <!-- Control Connections -->
            <path d="M 575 130 L 510 150" stroke="#9f7aea" fill="none" stroke-width="2" />
            <path d="M 575 130 L 580 150" stroke="#9f7aea" fill="none" stroke-width="2" />
            <path d="M 575 130 L 650 150" stroke="#9f7aea" fill="none" stroke-width="2" />
            
            <!-- Comparison -->
            <rect x="100" y="250" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="270" text-anchor="middle" font-size="14" fill="#ffffff">Key Differences</text>
            
            <rect x="120" y="290" width="250" height="50" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="245" y="310" text-anchor="middle" font-size="12" fill="#ffffff">Traditional Networking</text>
            <text x="245" y="330" text-anchor="middle" font-size="10" fill="#ffffff">Device-by-Device Configuration</text>
            
            <rect x="430" y="290" width="250" height="50" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="555" y="310" text-anchor="middle" font-size="12" fill="#ffffff">Software-Defined Networking</text>
            <text x="555" y="330" text-anchor="middle" font-size="10" fill="#ffffff">Centralized Programmable Control</text>
            
            <path d="M 380 315 L 420 315" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            
            <!-- Arrow Definitions -->
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#ffffff" />
              </marker>
            </defs>
          </svg>
        </div>

        <h3>Why Use Software-Defined Networking?</h3>
        <p>SDN offers several important benefits over traditional networking:</p>
        <ul>
          <li><strong>Centralized Management:</strong> Control your entire network from a single point</li>
          <li><strong>Programmability:</strong> Automate network configuration and changes</li>
          <li><strong>Flexibility:</strong> Quickly adapt the network to changing requirements</li>
          <li><strong>Vendor Independence:</strong> Use hardware from different vendors with the same controller</li>
          <li><strong>Innovation:</strong> Easily implement new network services and applications</li>
          <li><strong>Cost Efficiency:</strong> Reduce operational expenses through automation</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Traffic Control Center</h3>
          <p>Think of SDN like a modern traffic control center for a city:</p>
          <ul>
            <li>In old cities, each traffic light had its own timer and worked independently (like traditional networking)</li>
            <li>In modern cities, all traffic lights are connected to a central control center (like the SDN controller)</li>
            <li>Traffic engineers can see the entire city's traffic patterns at once (centralized view)</li>
            <li>They can change light patterns during rush hour or for special events (programmability)</li>
            <li>If there's an accident, they can quickly reroute traffic (adaptability)</li>
            <li>The control center works with different types of traffic lights from various manufacturers (vendor independence)</li>
          </ul>
          <p>Just as a traffic control center makes a city's transportation more efficient, SDN makes computer networks more efficient and easier to manage!</p>
        </div>

        <h3>Traditional Networking vs. SDN</h3>
        <p>Let's compare traditional networking with Software-Defined Networking:</p>
        
        <h4>Traditional Networking:</h4>
        <ul>
          <li>Control and data planes are combined in each network device</li>
          <li>Each device must be configured individually</li>
          <li>Configuration is often manual and device-specific</li>
          <li>Changes can be time-consuming and error-prone</li>
          <li>Limited automation capabilities</li>
        </ul>
        
        <h4>Software-Defined Networking:</h4>
        <ul>
          <li>Control plane is separated from the data plane</li>
          <li>Centralized controller manages all network devices</li>
          <li>Configuration is programmable and consistent</li>
          <li>Changes can be implemented quickly across the entire network</li>
          <li>Extensive automation capabilities</li>
        </ul>
      `
    },
    {
      title: "SDN Architecture",
      type: "text",
      content: `
        <h2>The Three Layers of SDN Architecture</h2>
        <p>Software-Defined Networking has a three-layer architecture that separates different network functions. Let's explore each layer:</p>

        <div class="interactive-diagram">
          <svg id="sdn-architecture-svg" viewBox="0 0 800 500" width="100%" height="500">
            <!-- Application Layer -->
            <rect x="100" y="50" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">Application Layer</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">Network applications and services</text>
            
            <rect x="500" y="70" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="95" text-anchor="middle" font-size="10" fill="#ffffff">Business Applications</text>
            <text x="575" y="115" text-anchor="middle" font-size="10" fill="#ffffff">(Security, Load Balancing, etc.)</text>
            
            <!-- Northbound API -->
            <rect x="100" y="160" width="600" height="30" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="400" y="180" text-anchor="middle" font-size="12" fill="#ffffff">Northbound API</text>
            
            <!-- Control Layer -->
            <rect x="100" y="200" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="230" text-anchor="start" font-size="16" fill="#ffffff">Control Layer</text>
            <text x="150" y="250" text-anchor="start" font-size="12" fill="#ffffff">Network intelligence and control logic</text>
            
            <rect x="500" y="220" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="245" text-anchor="middle" font-size="10" fill="#ffffff">SDN Controller</text>
            <text x="575" y="265" text-anchor="middle" font-size="10" fill="#ffffff">(Network Operating System)</text>
            
            <!-- Southbound API -->
            <rect x="100" y="310" width="600" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="400" y="330" text-anchor="middle" font-size="12" fill="#ffffff">Southbound API (OpenFlow, NETCONF, etc.)</text>
            
            <!-- Infrastructure Layer -->
            <rect x="100" y="350" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="380" text-anchor="start" font-size="16" fill="#ffffff">Infrastructure Layer</text>
            <text x="150" y="400" text-anchor="start" font-size="12" fill="#ffffff">Physical and virtual network devices</text>
            
            <!-- Network Devices -->
            <rect x="200" y="380" width="60" height="40" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="230" y="405" text-anchor="middle" font-size="10" fill="#ffffff">Switch</text>
            
            <rect x="300" y="380" width="60" height="40" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="330" y="405" text-anchor="middle" font-size="10" fill="#ffffff">Switch</text>
            
            <rect x="400" y="380" width="60" height="40" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="430" y="405" text-anchor="middle" font-size="10" fill="#ffffff">Switch</text>
            
            <rect x="500" y="380" width="60" height="40" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="530" y="405" text-anchor="middle" font-size="10" fill="#ffffff">Router</text>
            
            <rect x="600" y="380" width="60" height="40" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="630" y="405" text-anchor="middle" font-size="10" fill="#ffffff">Firewall</text>
            
            <!-- Data Flow -->
            <path d="M 575 130 L 575 220" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 575 280 L 575 350" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 575 280 L 230 380" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
            <path d="M 575 280 L 330 380" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
            <path d="M 575 280 L 430 380" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
            <path d="M 575 280 L 530 380" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
            <path d="M 575 280 L 630 380" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
            
            <!-- Arrow Definitions -->
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#ffffff" />
              </marker>
            </defs>
          </svg>
        </div>

        <h3>1. Application Layer</h3>
        <p>The Application Layer is where network applications and services that use the SDN network reside.</p>
        <p><strong>Key components:</strong></p>
        <ul>
          <li>Business applications (security, load balancing, traffic engineering)</li>
          <li>Network management applications</li>
          <li>Analytics and monitoring tools</li>
        </ul>
        <p>These applications communicate with the Control Layer through the Northbound API.</p>

        <h3>2. Control Layer</h3>
        <p>The Control Layer is the brain of the SDN architecture. It contains the SDN controller that manages the network.</p>
        <p><strong>Key functions:</strong></p>
        <ul>
          <li>Maintains a global view of the network</li>
          <li>Makes decisions about traffic routing</li>
          <li>Translates application requirements into network configurations</li>
          <li>Communicates with network devices through the Southbound API</li>
        </ul>
        <p><strong>Popular SDN controllers:</strong> OpenDaylight, ONOS, Floodlight, VMware NSX</p>

        <h3>3. Infrastructure Layer</h3>
        <p>The Infrastructure Layer consists of the physical and virtual network devices that forward packets.</p>
        <p><strong>Key components:</strong></p>
        <ul>
          <li>Physical switches and routers</li>
          <li>Virtual switches</li>
          <li>Other network devices (firewalls, load balancers)</li>
        </ul>
        <p>These devices focus solely on the data plane functions (forwarding packets) and receive instructions from the Control Layer.</p>

        <h3>SDN APIs</h3>
        <p>APIs (Application Programming Interfaces) are crucial in SDN as they enable communication between the different layers:</p>
        
        <h4>Northbound API:</h4>
        <ul>
          <li>Connects the Application Layer to the Control Layer</li>
          <li>Allows applications to communicate their requirements to the controller</li>
          <li>Examples: REST API, Java API</li>
        </ul>
        
        <h4>Southbound API:</h4>
        <ul>
          <li>Connects the Control Layer to the Infrastructure Layer</li>
          <li>Allows the controller to program the network devices</li>
          <li>Examples: OpenFlow, NETCONF, OVSDB</li>
        </ul>
        
        <h4>East-West API:</h4>
        <ul>
          <li>Enables communication between multiple SDN controllers</li>
          <li>Important for large networks that require multiple controllers</li>
          <li>Examples: SDNi (SDN interface), ALTO (Application-Layer Traffic Optimization)</li>
        </ul>
      `
    }
  ]
};
