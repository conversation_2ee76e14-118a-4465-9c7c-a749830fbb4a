/**
 * Cloud Networking Module
 *
 * This file contains the content for the Cloud Networking module
 * in the Network Fundamentals learning path.
 */

export const cloudNetworkingContent = {
  id: "nf-22",
  pathId: "networking-fundamentals",
  title: "Cloud Networking",
  description: "Explore networking concepts and technologies in cloud computing environments.",
  objectives: [
    "Understand what cloud networking is and its key components",
    "Learn about different cloud networking models and services",
    "Explore cloud connectivity options and their use cases",
    "Understand security considerations for cloud networks"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to Cloud Networking",
      type: "text",
      content: `
        <h2>What is Cloud Networking?</h2>
        <p>Imagine if instead of having your own toy box at home, there was a giant toy box in your neighborhood that everyone could share. You could play with any toy you wanted, and you'd only pay for the time you used it. Cloud networking works in a similar way for computer networks!</p>
        
        <p>Cloud networking is the use of network resources in cloud computing environments. Instead of building and managing your own physical network infrastructure, you can use network services provided by cloud providers like Amazon Web Services (AWS), Microsoft Azure, or Google Cloud Platform (GCP).</p>

        <div class="interactive-diagram">
          <svg id="cloud-networking-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Cloud -->
            <ellipse cx="400" cy="100" rx="250" ry="70" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="100" text-anchor="middle" font-size="18" fill="#ffffff">Cloud Provider</text>
            
            <!-- Cloud Services -->
            <rect x="250" y="80" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="290" y="105" text-anchor="middle" font-size="10" fill="#ffffff">Virtual Networks</text>
            
            <rect x="350" y="80" width="80" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="390" y="105" text-anchor="middle" font-size="10" fill="#ffffff">Load Balancers</text>
            
            <rect x="450" y="80" width="80" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="490" y="105" text-anchor="middle" font-size="10" fill="#ffffff">Firewalls</text>
            
            <!-- Company Office -->
            <rect x="100" y="250" width="200" height="100" rx="10" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="200" y="280" text-anchor="middle" font-size="14" fill="#ffffff">Company Office</text>
            <text x="200" y="300" text-anchor="middle" font-size="12" fill="#ffffff">On-premises Network</text>
            
            <!-- Remote Workers -->
            <rect x="500" y="250" width="200" height="100" rx="10" fill="#2c5282" stroke="#63b3ed" />
            <text x="600" y="280" text-anchor="middle" font-size="14" fill="#ffffff">Remote Workers</text>
            <text x="600" y="300" text-anchor="middle" font-size="12" fill="#ffffff">Home Networks</text>
            
            <!-- Connections -->
            <path d="M 200 250 L 350 150" stroke="#38b2ac" fill="none" stroke-width="2" />
            <text x="250" y="190" text-anchor="middle" font-size="12" fill="#ffffff">VPN / Direct Connect</text>
            
            <path d="M 600 250 L 450 150" stroke="#63b3ed" fill="none" stroke-width="2" />
            <text x="550" y="190" text-anchor="middle" font-size="12" fill="#ffffff">Internet</text>
            
            <!-- Devices in Office -->
            <circle cx="150" cy="320" r="10" fill="#1e3a5f" stroke="#38b2ac" />
            <circle cx="180" cy="320" r="10" fill="#1e3a5f" stroke="#38b2ac" />
            <circle cx="210" cy="320" r="10" fill="#1e3a5f" stroke="#38b2ac" />
            <circle cx="240" cy="320" r="10" fill="#1e3a5f" stroke="#38b2ac" />
            
            <!-- Remote Workers -->
            <circle cx="550" cy="320" r="10" fill="#2c5282" stroke="#63b3ed" />
            <circle cx="580" cy="320" r="10" fill="#2c5282" stroke="#63b3ed" />
            <circle cx="610" cy="320" r="10" fill="#2c5282" stroke="#63b3ed" />
            <circle cx="640" cy="320" r="10" fill="#2c5282" stroke="#63b3ed" />
          </svg>
        </div>

        <h3>Why Use Cloud Networking?</h3>
        <p>Cloud networking offers several important benefits:</p>
        <ul>
          <li><strong>Scalability:</strong> Easily grow or shrink your network as needed</li>
          <li><strong>Cost Efficiency:</strong> Pay only for what you use, with no large upfront investments</li>
          <li><strong>Flexibility:</strong> Deploy network resources quickly and change them as needed</li>
          <li><strong>Global Reach:</strong> Access network resources from anywhere in the world</li>
          <li><strong>Reliability:</strong> Benefit from the redundancy and high availability of cloud providers</li>
          <li><strong>Managed Services:</strong> Let the cloud provider handle maintenance and updates</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Community Swimming Pool</h3>
          <p>Think of cloud networking like a community swimming pool:</p>
          <ul>
            <li>Instead of building your own pool (traditional networking), you use a shared pool (cloud networking)</li>
            <li>You don't have to worry about cleaning the pool, maintaining the filters, or adding chemicals - the pool management handles all that (managed services)</li>
            <li>On busy days, the pool can accommodate more people, and on quiet days, it uses fewer resources (scalability)</li>
            <li>You only pay for when you use the pool, not for all the times when you're not using it (cost efficiency)</li>
            <li>The pool has lifeguards, security cameras, and safety equipment to keep everyone safe (security)</li>
          </ul>
          <p>Just like a community pool lets you enjoy swimming without the hassle of owning your own pool, cloud networking lets you use network resources without the complexity of building and maintaining your own infrastructure!</p>
        </div>

        <h3>Key Components of Cloud Networking</h3>
        <p>Cloud networking includes several important components:</p>
        
        <ol>
          <li><strong>Virtual Networks:</strong> Software-defined networks that behave like physical networks</li>
          <li><strong>Subnets:</strong> Subdivisions of virtual networks for organizing resources</li>
          <li><strong>Virtual Routers:</strong> Route traffic between subnets and to the internet</li>
          <li><strong>Virtual Firewalls:</strong> Control traffic flow based on security rules</li>
          <li><strong>Load Balancers:</strong> Distribute traffic across multiple servers</li>
          <li><strong>VPN Gateways:</strong> Provide secure connections to cloud resources</li>
          <li><strong>Content Delivery Networks (CDNs):</strong> Distribute content to users from nearby locations</li>
        </ol>
      `
    },
    {
      title: "Cloud Networking Models",
      type: "text",
      content: `
        <h2>Different Cloud Networking Models</h2>
        <p>Cloud providers offer different networking models to meet various needs. Let's explore the main ones:</p>

        <div class="interactive-diagram">
          <svg id="cloud-models-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- IaaS -->
            <rect x="100" y="50" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">Infrastructure as a Service (IaaS)</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">You manage most of the networking components</text>
            
            <rect x="150" y="120" width="500" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            
            <!-- IaaS Components -->
            <rect x="170" y="130" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="210" y="155" text-anchor="middle" font-size="10" fill="#ffffff">Virtual Networks</text>
            
            <rect x="270" y="130" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="310" y="155" text-anchor="middle" font-size="10" fill="#ffffff">Subnets</text>
            
            <rect x="370" y="130" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="410" y="155" text-anchor="middle" font-size="10" fill="#ffffff">Firewalls</text>
            
            <rect x="470" y="130" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="510" y="155" text-anchor="middle" font-size="10" fill="#ffffff">Load Balancers</text>
            
            <text x="400" y="190" text-anchor="middle" font-size="10" fill="#ffffff">Examples: AWS EC2, Azure VMs, Google Compute Engine</text>
            
            <!-- PaaS -->
            <rect x="100" y="220" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="250" text-anchor="start" font-size="16" fill="#ffffff">Platform as a Service (PaaS)</text>
            <text x="150" y="270" text-anchor="start" font-size="12" fill="#ffffff">Provider manages most networking, you focus on applications</text>
            
            <rect x="150" y="290" width="500" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            
            <!-- PaaS Components -->
            <rect x="170" y="300" width="220" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="280" y="325" text-anchor="middle" font-size="10" fill="#ffffff">Application Platform</text>
            
            <rect x="410" y="300" width="220" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="520" y="325" text-anchor="middle" font-size="10" fill="#ffffff">Managed Networking</text>
            
            <text x="400" y="360" text-anchor="middle" font-size="10" fill="#ffffff">Examples: AWS Elastic Beanstalk, Azure App Service, Google App Engine</text>
            
            <!-- SaaS -->
            <rect x="100" y="390" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="420" text-anchor="start" font-size="16" fill="#ffffff">Software as a Service (SaaS)</text>
            <text x="150" y="440" text-anchor="start" font-size="12" fill="#ffffff">Provider manages everything, you just use the application</text>
            
            <rect x="150" y="460" width="500" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            
            <!-- SaaS Components -->
            <rect x="170" y="470" width="460" height="40" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="400" y="495" text-anchor="middle" font-size="10" fill="#ffffff">Complete Application with Built-in Networking</text>
            
            <text x="400" y="530" text-anchor="middle" font-size="10" fill="#ffffff">Examples: Microsoft 365, Google Workspace, Salesforce</text>
          </svg>
        </div>

        <h3>Infrastructure as a Service (IaaS)</h3>
        <p>In the IaaS model, the cloud provider offers virtual networking components that you configure and manage yourself.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>You create and manage virtual networks, subnets, and routing</li>
          <li>You configure firewalls and security groups</li>
          <li>You set up load balancers and VPN connections</li>
          <li>You have the most control but also the most responsibility</li>
        </ul>
        <p><strong>Examples:</strong> Amazon VPC (Virtual Private Cloud), Azure Virtual Network, Google Cloud VPC</p>

        <h3>Platform as a Service (PaaS)</h3>
        <p>In the PaaS model, the cloud provider handles most of the networking for you, allowing you to focus on your applications.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>The provider manages the underlying network infrastructure</li>
          <li>You can configure some networking aspects through service settings</li>
          <li>Networking is often simplified and abstracted</li>
          <li>Less control but also less management overhead</li>
        </ul>
        <p><strong>Examples:</strong> AWS Elastic Beanstalk, Azure App Service, Google App Engine</p>

        <h3>Software as a Service (SaaS)</h3>
        <p>In the SaaS model, the cloud provider delivers a complete application with all networking handled behind the scenes.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>All networking is managed by the provider</li>
          <li>You simply use the application</li>
          <li>Minimal configuration options for networking</li>
          <li>No management responsibility but also minimal control</li>
        </ul>
        <p><strong>Examples:</strong> Microsoft 365, Google Workspace, Salesforce</p>

        <h3>Cloud Connectivity Options</h3>
        <p>There are several ways to connect to cloud networks:</p>
        <ul>
          <li><strong>Internet Connection:</strong> Standard connection through the public internet</li>
          <li><strong>VPN (Virtual Private Network):</strong> Secure encrypted connection over the internet</li>
          <li><strong>Direct Connect / ExpressRoute:</strong> Private dedicated connection to the cloud provider</li>
          <li><strong>Transit Gateway:</strong> Central hub to connect multiple VPCs and on-premises networks</li>
        </ul>
      `
    }
  ]
};
