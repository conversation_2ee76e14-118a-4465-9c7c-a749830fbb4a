/**
 * Firewalls and Access Control Lists Module
 *
 * This file contains the content for the Firewalls and Access Control Lists module
 * in the Network Fundamentals learning path.
 */

export const firewallsAndAclsContent = {
  id: "nf-15",
  pathId: "networking-fundamentals",
  title: "Firewalls and Access Control Lists",
  description: "Learn how firewalls and ACLs protect networks by controlling traffic flow.",
  objectives: [
    "Understand what firewalls are and how they protect networks",
    "Learn about different types of firewalls and their capabilities",
    "Understand Access Control Lists (ACLs) and how they filter traffic",
    "Explore Next-Generation Firewall features and benefits"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to Firewalls",
      type: "text",
      content: `
        <h2>What are Firewalls?</h2>
        <p>Imagine your home has doors and windows that you can lock to keep unwanted visitors out while letting family members in. A firewall works the same way for computer networks!</p>
        
        <p>A firewall is a security device (hardware or software) that monitors and filters incoming and outgoing network traffic based on predetermined security rules. It acts as a barrier between a trusted network (like your home or business network) and an untrusted network (like the internet).</p>

        <div class="interactive-diagram">
          <svg id="firewall-intro-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Internet Cloud -->
            <ellipse cx="150" cy="200" rx="120" ry="70" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="200" text-anchor="middle" font-size="16" fill="#ffffff">Internet</text>
            <text x="150" y="225" text-anchor="middle" font-size="12" fill="#ffffff">(Untrusted Network)</text>
            
            <!-- Firewall -->
            <rect x="350" y="150" width="100" height="100" rx="10" fill="#c53030" stroke="#fc8181" />
            <text x="400" y="180" text-anchor="middle" font-size="16" fill="#ffffff">Firewall</text>
            <text x="400" y="200" text-anchor="middle" font-size="12" fill="#ffffff">Inspects</text>
            <text x="400" y="220" text-anchor="middle" font-size="12" fill="#ffffff">Traffic</text>
            
            <!-- Protected Network -->
            <ellipse cx="650" cy="200" rx="120" ry="70" fill="#2c5282" stroke="#63b3ed" />
            <text x="650" y="200" text-anchor="middle" font-size="16" fill="#ffffff">Protected</text>
            <text x="650" y="225" text-anchor="middle" font-size="12" fill="#ffffff">Network</text>
            
            <!-- Traffic Arrows -->
            <path d="M 270 180 L 350 180" stroke="#38b2ac" fill="none" stroke-width="2" marker-end="url(#greenarrow)" />
            <text x="310" y="170" text-anchor="middle" font-size="10" fill="#ffffff">Allowed Traffic</text>
            
            <path d="M 270 220 L 350 220" stroke="#fc8181" fill="none" stroke-width="2" marker-end="url(#redarrow)" stroke-dasharray="5,5" />
            <text x="310" y="240" text-anchor="middle" font-size="10" fill="#ffffff">Blocked Traffic</text>
            
            <path d="M 450 180 L 530 180" stroke="#38b2ac" fill="none" stroke-width="2" marker-end="url(#greenarrow)" />
            <text x="490" y="170" text-anchor="middle" font-size="10" fill="#ffffff">Safe Traffic</text>
            
            <!-- Devices -->
            <rect x="600" y="150" width="40" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="620" y="170" text-anchor="middle" font-size="10" fill="#ffffff">PC</text>
            
            <rect x="650" y="150" width="40" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="670" y="170" text-anchor="middle" font-size="10" fill="#ffffff">PC</text>
            
            <rect x="700" y="150" width="40" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="720" y="170" text-anchor="middle" font-size="10" fill="#ffffff">PC</text>
            
            <rect x="625" y="250" width="50" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="650" y="270" text-anchor="middle" font-size="10" fill="#ffffff">Server</text>
            
            <!-- Definitions -->
            <defs>
              <marker id="greenarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#38b2ac" />
              </marker>
              <marker id="redarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#fc8181" />
              </marker>
            </defs>
          </svg>
        </div>

        <h3>Why Do We Need Firewalls?</h3>
        <p>Firewalls are essential for network security because they:</p>
        <ul>
          <li><strong>Block Unauthorized Access:</strong> Prevent hackers from accessing your network</li>
          <li><strong>Control Traffic Flow:</strong> Determine what traffic can enter or leave your network</li>
          <li><strong>Prevent Malware:</strong> Help stop viruses, worms, and other malicious software</li>
          <li><strong>Protect Sensitive Data:</strong> Keep private information from leaving your network</li>
          <li><strong>Create Security Zones:</strong> Separate different parts of your network based on security needs</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Security Guard</h3>
          <p>Think of a firewall like a security guard at the entrance of a building:</p>
          <ul>
            <li>The guard checks everyone's ID before letting them in (authentication)</li>
            <li>The guard only allows people to enter specific areas they have permission for (authorization)</li>
            <li>The guard keeps a log of who enters and leaves (logging)</li>
            <li>The guard watches for suspicious behavior (monitoring)</li>
            <li>The guard follows specific rules about who can enter (security policy)</li>
          </ul>
          <p>Just like a good security guard protects a building, a good firewall protects your network!</p>
        </div>
      `
    },
    {
      title: "Types of Firewalls",
      type: "text",
      content: `
        <h2>Different Types of Firewalls</h2>
        <p>Firewalls have evolved over time, becoming more sophisticated and offering better protection. Let's explore the main types of firewalls:</p>

        <div class="interactive-diagram">
          <svg id="firewall-types-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- Packet Filtering Firewall -->
            <rect x="100" y="50" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">Packet Filtering Firewall</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">Examines packet headers (IP addresses, ports, protocols)</text>
            
            <rect x="150" y="120" width="500" height="20" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <rect x="150" y="120" width="100" height="20" rx="5" fill="#c53030" stroke="#fc8181" />
            <text x="200" y="135" text-anchor="middle" font-size="10" fill="#ffffff">Source IP</text>
            
            <rect x="250" y="120" width="100" height="20" rx="0" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="300" y="135" text-anchor="middle" font-size="10" fill="#ffffff">Dest IP</text>
            
            <rect x="350" y="120" width="100" height="20" rx="0" fill="#c53030" stroke="#fc8181" />
            <text x="400" y="135" text-anchor="middle" font-size="10" fill="#ffffff">Port</text>
            
            <rect x="450" y="120" width="100" height="20" rx="0" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="500" y="135" text-anchor="middle" font-size="10" fill="#ffffff">Protocol</text>
            
            <rect x="550" y="120" width="100" height="20" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="600" y="135" text-anchor="middle" font-size="10" fill="#ffffff">Data</text>
            
            <!-- Stateful Inspection Firewall -->
            <rect x="100" y="170" width="600" height="120" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="200" text-anchor="start" font-size="16" fill="#ffffff">Stateful Inspection Firewall</text>
            <text x="150" y="220" text-anchor="start" font-size="12" fill="#ffffff">Tracks the state of active connections</text>
            
            <rect x="150" y="240" width="500" height="20" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <rect x="150" y="240" width="400" height="20" rx="0" fill="#c53030" stroke="#fc8181" />
            <text x="350" y="255" text-anchor="middle" font-size="10" fill="#ffffff">Connection State Table</text>
            
            <rect x="550" y="240" width="100" height="20" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="600" y="255" text-anchor="middle" font-size="10" fill="#ffffff">Data</text>
            
            <!-- Application Layer Firewall -->
            <rect x="100" y="310" width="600" height="120" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="340" text-anchor="start" font-size="16" fill="#ffffff">Application Layer Firewall</text>
            <text x="150" y="360" text-anchor="start" font-size="12" fill="#ffffff">Inspects the actual content of the traffic</text>
            
            <rect x="150" y="380" width="500" height="20" rx="5" fill="#c53030" stroke="#fc8181" />
            <text x="400" y="395" text-anchor="middle" font-size="10" fill="#ffffff">Deep Packet Inspection (Headers + Data Content)</text>
            
            <!-- Next-Generation Firewall -->
            <rect x="100" y="450" width="600" height="130" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="480" text-anchor="start" font-size="16" fill="#ffffff">Next-Generation Firewall (NGFW)</text>
            <text x="150" y="500" text-anchor="start" font-size="12" fill="#ffffff">Combines traditional firewall with advanced features</text>
            
            <rect x="150" y="520" width="100" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="200" y="540" text-anchor="middle" font-size="10" fill="#ffffff">Traditional Firewall</text>
            
            <rect x="260" y="520" width="100" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="310" y="540" text-anchor="middle" font-size="10" fill="#ffffff">Intrusion Prevention</text>
            
            <rect x="370" y="520" width="100" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="420" y="540" text-anchor="middle" font-size="10" fill="#ffffff">Application Control</text>
            
            <rect x="480" y="520" width="100" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="530" y="540" text-anchor="middle" font-size="10" fill="#ffffff">User Identity</text>
            
            <rect x="590" y="520" width="100" height="40" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="640" y="540" text-anchor="middle" font-size="10" fill="#ffffff">Threat Intelligence</text>
          </svg>
        </div>

        <h3>Packet Filtering Firewall</h3>
        <p>This is the most basic type of firewall. It examines packet headers and makes decisions based on:</p>
        <ul>
          <li>Source and destination IP addresses</li>
          <li>Source and destination port numbers</li>
          <li>Protocol type (TCP, UDP, ICMP, etc.)</li>
        </ul>
        <p><strong>Limitations:</strong> Can't understand the context of connections and doesn't inspect the actual data content.</p>

        <h3>Stateful Inspection Firewall</h3>
        <p>This type of firewall keeps track of the state of active connections. It understands the context of traffic and can determine if a packet is part of an established connection.</p>
        <p><strong>Advantages:</strong> More secure than packet filtering because it can tell the difference between new connections and responses to existing connections.</p>

        <h3>Application Layer Firewall</h3>
        <p>Also called a proxy firewall, this type inspects the actual content of the traffic, not just the headers. It can understand specific applications and protocols.</p>
        <p><strong>Advantages:</strong> Can detect and block specific application-level attacks and malicious content.</p>

        <h3>Next-Generation Firewall (NGFW)</h3>
        <p>This is the most advanced type of firewall, combining traditional firewall capabilities with additional features:</p>
        <ul>
          <li>Intrusion Prevention System (IPS)</li>
          <li>Application awareness and control</li>
          <li>User identity integration</li>
          <li>Advanced malware protection</li>
          <li>Threat intelligence integration</li>
        </ul>
        <p><strong>Advantages:</strong> Provides comprehensive protection against modern threats and gives administrators more control over network traffic.</p>
      `
    }
  ]
};
