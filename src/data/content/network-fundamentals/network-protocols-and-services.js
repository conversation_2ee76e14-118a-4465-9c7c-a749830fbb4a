/**
 * Network Protocols and Services Module
 *
 * This file contains the content for the Network Protocols and Services module
 * in the Network Fundamentals learning path.
 */

export const networkProtocolsAndServicesContent = {
  id: "nf-20",
  pathId: "networking-fundamentals",
  title: "Network Protocols and Services",
  description: "Explore the key protocols and services that enable network communication and applications.",
  objectives: [
    "Understand the role of protocols in network communication",
    "Learn about common application layer protocols and their functions",
    "Explore key network services and how they support applications",
    "Understand protocol security considerations and best practices"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to Network Protocols",
      type: "text",
      content: `
        <h2>What are Network Protocols?</h2>
        <p>Imagine if you visited a country where you don't speak the language. You'd need a set of rules to communicate - maybe using hand gestures or a translation book. Network protocols are similar - they're the rules that allow devices to communicate with each other!</p>
        
        <p>Network protocols are standardized rules that determine how data is transmitted, received, and processed across networks. They define everything from how connections are established to how data is formatted, addressed, transmitted, routed, and received.</p>

        <div class="interactive-diagram">
          <svg id="protocol-layers-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Protocol Layers -->
            <rect x="100" y="50" width="600" height="300" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="80" text-anchor="middle" font-size="18" fill="#ffffff">Network Protocol Layers</text>
            
            <!-- Application Layer -->
            <rect x="150" y="120" width="500" height="50" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="400" y="150" text-anchor="middle" font-size="16" fill="#ffffff">Application Layer</text>
            <text x="200" y="150" text-anchor="middle" font-size="12" fill="#ffffff">HTTP</text>
            <text x="250" y="150" text-anchor="middle" font-size="12" fill="#ffffff">DNS</text>
            <text x="300" y="150" text-anchor="middle" font-size="12" fill="#ffffff">FTP</text>
            <text x="350" y="150" text-anchor="middle" font-size="12" fill="#ffffff">SMTP</text>
            <text x="400" y="150" text-anchor="middle" font-size="12" fill="#ffffff">SSH</text>
            <text x="450" y="150" text-anchor="middle" font-size="12" fill="#ffffff">DHCP</text>
            <text x="500" y="150" text-anchor="middle" font-size="12" fill="#ffffff">SNMP</text>
            <text x="550" y="150" text-anchor="middle" font-size="12" fill="#ffffff">RDP</text>
            
            <!-- Transport Layer -->
            <rect x="150" y="180" width="500" height="50" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="400" y="210" text-anchor="middle" font-size="16" fill="#ffffff">Transport Layer</text>
            <text x="300" y="210" text-anchor="middle" font-size="12" fill="#ffffff">TCP</text>
            <text x="500" y="210" text-anchor="middle" font-size="12" fill="#ffffff">UDP</text>
            
            <!-- Internet Layer -->
            <rect x="150" y="240" width="500" height="50" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="400" y="270" text-anchor="middle" font-size="16" fill="#ffffff">Internet Layer</text>
            <text x="250" y="270" text-anchor="middle" font-size="12" fill="#ffffff">IPv4</text>
            <text x="300" y="270" text-anchor="middle" font-size="12" fill="#ffffff">IPv6</text>
            <text x="350" y="270" text-anchor="middle" font-size="12" fill="#ffffff">ICMP</text>
            <text x="400" y="270" text-anchor="middle" font-size="12" fill="#ffffff">ARP</text>
            <text x="450" y="270" text-anchor="middle" font-size="12" fill="#ffffff">IGMP</text>
            <text x="500" y="270" text-anchor="middle" font-size="12" fill="#ffffff">IPsec</text>
            
            <!-- Link Layer -->
            <rect x="150" y="300" width="500" height="40" rx="5" fill="#c53030" stroke="#fc8181" />
            <text x="400" y="325" text-anchor="middle" font-size="16" fill="#ffffff">Link Layer</text>
            <text x="250" y="325" text-anchor="middle" font-size="12" fill="#ffffff">Ethernet</text>
            <text x="350" y="325" text-anchor="middle" font-size="12" fill="#ffffff">Wi-Fi</text>
            <text x="450" y="325" text-anchor="middle" font-size="12" fill="#ffffff">PPP</text>
            <text x="550" y="325" text-anchor="middle" font-size="12" fill="#ffffff">MAC</text>
          </svg>
        </div>

        <h3>Why Do We Need Protocols?</h3>
        <p>Network protocols serve several important purposes:</p>
        <ul>
          <li><strong>Standardization:</strong> They ensure different devices from different manufacturers can communicate</li>
          <li><strong>Error Handling:</strong> They define how to detect and recover from transmission errors</li>
          <li><strong>Flow Control:</strong> They manage the rate of data transmission to prevent overwhelming receivers</li>
          <li><strong>Routing:</strong> They determine how data finds its way from source to destination</li>
          <li><strong>Security:</strong> They can include mechanisms for authentication, encryption, and integrity checking</li>
        </ul>

        <div class="learning-module-story">
          <h3>The International Mail System</h3>
          <p>Think of network protocols like the international mail system:</p>
          <ul>
            <li>When you send a letter internationally, you follow specific rules (protocols)</li>
            <li>You must write the address in a standard format (data formatting)</li>
            <li>You need to use the correct postage (addressing)</li>
            <li>The postal service routes your letter through various sorting facilities (routing)</li>
            <li>The recipient's local post office delivers it to the final destination (delivery)</li>
            <li>If your letter is damaged, there are procedures to handle it (error recovery)</li>
          </ul>
          <p>Without these standardized rules, your mail might never reach its destination - just like network data wouldn't reach its destination without protocols!</p>
        </div>

        <h3>Protocol Layers</h3>
        <p>Network protocols are organized into layers, with each layer responsible for specific aspects of communication:</p>
        
        <ol>
          <li><strong>Application Layer:</strong> Protocols that applications use directly (HTTP, DNS, FTP, etc.)</li>
          <li><strong>Transport Layer:</strong> Protocols that manage end-to-end communication (TCP, UDP)</li>
          <li><strong>Internet Layer:</strong> Protocols that handle routing between networks (IP, ICMP)</li>
          <li><strong>Link Layer:</strong> Protocols that deal with the physical connection (Ethernet, Wi-Fi)</li>
        </ol>
        
        <p>This layered approach allows each protocol to focus on its specific job, making the overall system more modular and easier to develop and maintain.</p>
      `
    },
    {
      title: "Application Layer Protocols",
      type: "text",
      content: `
        <h2>Common Application Layer Protocols</h2>
        <p>The application layer is where most user-facing services operate. Let's explore some of the most important application layer protocols:</p>

        <div class="interactive-diagram">
          <svg id="application-protocols-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- HTTP/HTTPS -->
            <rect x="100" y="50" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">HTTP/HTTPS</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">HyperText Transfer Protocol (Secure)</text>
            
            <rect x="500" y="70" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="95" text-anchor="middle" font-size="10" fill="#ffffff">Web Browsing</text>
            <text x="575" y="115" text-anchor="middle" font-size="10" fill="#ffffff">Ports: 80/443</text>
            
            <!-- DNS -->
            <rect x="100" y="160" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="190" text-anchor="start" font-size="16" fill="#ffffff">DNS</text>
            <text x="150" y="210" text-anchor="start" font-size="12" fill="#ffffff">Domain Name System</text>
            
            <rect x="500" y="180" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="205" text-anchor="middle" font-size="10" fill="#ffffff">Name Resolution</text>
            <text x="575" y="225" text-anchor="middle" font-size="10" fill="#ffffff">Port: 53</text>
            
            <!-- SMTP/POP3/IMAP -->
            <rect x="100" y="270" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="300" text-anchor="start" font-size="16" fill="#ffffff">SMTP/POP3/IMAP</text>
            <text x="150" y="320" text-anchor="start" font-size="12" fill="#ffffff">Email Protocols</text>
            
            <rect x="500" y="290" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="315" text-anchor="middle" font-size="10" fill="#ffffff">Email Services</text>
            <text x="575" y="335" text-anchor="middle" font-size="10" fill="#ffffff">Ports: 25, 110, 143</text>
            
            <!-- FTP/SFTP -->
            <rect x="100" y="380" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="410" text-anchor="start" font-size="16" fill="#ffffff">FTP/SFTP</text>
            <text x="150" y="430" text-anchor="start" font-size="12" fill="#ffffff">File Transfer Protocol (Secure)</text>
            
            <rect x="500" y="400" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="425" text-anchor="middle" font-size="10" fill="#ffffff">File Transfers</text>
            <text x="575" y="445" text-anchor="middle" font-size="10" fill="#ffffff">Ports: 20/21, 22</text>
            
            <!-- SSH -->
            <rect x="100" y="490" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="520" text-anchor="start" font-size="16" fill="#ffffff">SSH</text>
            <text x="150" y="540" text-anchor="start" font-size="12" fill="#ffffff">Secure Shell</text>
            
            <rect x="500" y="510" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="535" text-anchor="middle" font-size="10" fill="#ffffff">Secure Remote Access</text>
            <text x="575" y="555" text-anchor="middle" font-size="10" fill="#ffffff">Port: 22</text>
          </svg>
        </div>

        <h3>HTTP/HTTPS (HyperText Transfer Protocol)</h3>
        <p>HTTP is the foundation of data communication on the World Wide Web. HTTPS is the secure version that adds encryption.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>Used for loading web pages in browsers</li>
          <li>Client-server model (request-response)</li>
          <li>Stateless protocol (each request is independent)</li>
          <li>HTTPS uses TLS/SSL for encryption</li>
          <li>Default ports: 80 (HTTP) and 443 (HTTPS)</li>
        </ul>

        <h3>DNS (Domain Name System)</h3>
        <p>DNS translates human-readable domain names (like example.com) into IP addresses that computers use to identify each other.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>Hierarchical, distributed database</li>
          <li>Uses both UDP and TCP on port 53</li>
          <li>Caches results to improve performance</li>
          <li>Includes various record types (A, AAAA, MX, CNAME, etc.)</li>
          <li>Critical for almost all internet services</li>
        </ul>

        <h3>Email Protocols (SMTP, POP3, IMAP)</h3>
        <p>These protocols handle different aspects of email delivery and retrieval:</p>
        <ul>
          <li><strong>SMTP (Simple Mail Transfer Protocol):</strong> Sends and routes email between mail servers (port 25)</li>
          <li><strong>POP3 (Post Office Protocol):</strong> Downloads email from a server to a client (port 110)</li>
          <li><strong>IMAP (Internet Message Access Protocol):</strong> Manages email on the server with client access (port 143)</li>
        </ul>

        <h3>FTP/SFTP (File Transfer Protocol)</h3>
        <p>FTP is used to transfer files between computers on a network. SFTP is the secure version that runs over SSH.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>Separate control and data connections</li>
          <li>Supports authentication</li>
          <li>Can list, add, delete, and modify files</li>
          <li>FTP uses ports 20 and 21</li>
          <li>SFTP uses port 22 (same as SSH)</li>
        </ul>

        <h3>SSH (Secure Shell)</h3>
        <p>SSH provides a secure channel over an unsecured network, commonly used for remote login and command execution.</p>
        <p><strong>Key features:</strong></p>
        <ul>
          <li>Strong encryption and authentication</li>
          <li>Replaces insecure protocols like Telnet</li>
          <li>Supports public key authentication</li>
          <li>Can tunnel other protocols</li>
          <li>Uses port 22</li>
        </ul>
      `
    }
  ]
};
