/**
 * Network Security Assessment Module
 *
 * This file contains the content for the Network Security Assessment module
 * in the Network Fundamentals learning path.
 */

export const networkSecurityAssessmentContent = {
  id: "nf-25",
  pathId: "networking-fundamentals",
  title: "Network Security Assessment",
  description: "Learn how to evaluate and improve network security through systematic assessment.",
  objectives: [
    "Understand what network security assessment is and why it's important",
    "Learn about different types of security assessments and when to use them",
    "Explore common tools and methodologies for network security testing",
    "Understand how to interpret assessment results and implement improvements"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Introduction to Network Security Assessment",
      type: "text",
      content: `
        <h2>What is Network Security Assessment?</h2>
        <p>Imagine if you wanted to make sure your house was safe from burglars. You might check if all the doors and windows lock properly, if the alarm system works, and if there are any other ways someone could break in. Network security assessment is like doing a safety check on your computer network!</p>
        
        <p>Network security assessment is the process of systematically evaluating the security of a network by identifying vulnerabilities, threats, and risks. It helps organizations understand their security posture and take steps to protect their networks from attacks.</p>

        <div class="interactive-diagram">
          <svg id="security-assessment-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Network -->
            <rect x="100" y="200" width="600" height="150" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="225" text-anchor="middle" font-size="16" fill="#ffffff">Organization Network</text>
            
            <!-- Network Devices -->
            <rect x="150" y="250" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="190" y="275" text-anchor="middle" font-size="10" fill="#ffffff">Firewall</text>
            
            <rect x="250" y="250" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="290" y="275" text-anchor="middle" font-size="10" fill="#ffffff">Router</text>
            
            <rect x="350" y="250" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="390" y="275" text-anchor="middle" font-size="10" fill="#ffffff">Switch</text>
            
            <rect x="450" y="250" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="490" y="275" text-anchor="middle" font-size="10" fill="#ffffff">Server</text>
            
            <rect x="550" y="250" width="80" height="40" rx="5" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="590" y="275" text-anchor="middle" font-size="10" fill="#ffffff">Workstation</text>
            
            <!-- Security Assessment -->
            <rect x="100" y="50" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="80" text-anchor="middle" font-size="16" fill="#ffffff">Security Assessment</text>
            
            <!-- Assessment Types -->
            <rect x="150" y="100" width="100" height="30" rx="5" fill="#553c9a" stroke="#9f7aea" />
            <text x="200" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Vulnerability Scan</text>
            
            <rect x="275" y="100" width="100" height="30" rx="5" fill="#2c5282" stroke="#63b3ed" />
            <text x="325" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Penetration Test</text>
            
            <rect x="400" y="100" width="100" height="30" rx="5" fill="#c53030" stroke="#fc8181" />
            <text x="450" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Security Audit</text>
            
            <rect x="525" y="100" width="100" height="30" rx="5" fill="#38a169" stroke="#9ae6b4" />
            <text x="575" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Risk Assessment</text>
            
            <!-- Assessment Connections -->
            <path d="M 200 130 L 200 200" stroke="#9f7aea" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 325 130 L 325 200" stroke="#63b3ed" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 450 130 L 450 200" stroke="#fc8181" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 575 130 L 575 200" stroke="#9ae6b4" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            
            <!-- Vulnerabilities -->
            <circle cx="190" cy="300" r="10" fill="#c53030" stroke="#fc8181" />
            <text x="190" y="320" text-anchor="middle" font-size="8" fill="#ffffff">Outdated</text>
            <text x="190" y="330" text-anchor="middle" font-size="8" fill="#ffffff">Firmware</text>
            
            <circle cx="290" cy="300" r="10" fill="#c53030" stroke="#fc8181" />
            <text x="290" y="320" text-anchor="middle" font-size="8" fill="#ffffff">Weak</text>
            <text x="290" y="330" text-anchor="middle" font-size="8" fill="#ffffff">Password</text>
            
            <circle cx="490" cy="300" r="10" fill="#c53030" stroke="#fc8181" />
            <text x="490" y="320" text-anchor="middle" font-size="8" fill="#ffffff">Missing</text>
            <text x="490" y="330" text-anchor="middle" font-size="8" fill="#ffffff">Patches</text>
            
            <!-- Arrow Definitions -->
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#ffffff" />
              </marker>
            </defs>
          </svg>
        </div>

        <h3>Why Conduct Network Security Assessments?</h3>
        <p>Regular security assessments are crucial for several reasons:</p>
        <ul>
          <li><strong>Identify Vulnerabilities:</strong> Discover weaknesses before attackers do</li>
          <li><strong>Verify Security Controls:</strong> Ensure that security measures are working properly</li>
          <li><strong>Meet Compliance Requirements:</strong> Satisfy regulatory and industry standards</li>
          <li><strong>Reduce Risk:</strong> Lower the likelihood and impact of security incidents</li>
          <li><strong>Prioritize Security Investments:</strong> Focus resources on the most critical issues</li>
          <li><strong>Improve Security Awareness:</strong> Help staff understand security risks and best practices</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Castle Inspector</h3>
          <p>Think of network security assessment like a medieval castle inspector:</p>
          <ul>
            <li>The castle inspector regularly checks all the castle's defenses (like a security assessment checks network defenses)</li>
            <li>They look for weak spots in the walls and gates (vulnerabilities in the network)</li>
            <li>They test if the guards are following proper procedures (security policies and controls)</li>
            <li>They check if the moat is deep enough and the drawbridge works properly (perimeter security)</li>
            <li>They make sure the castle has enough supplies to withstand an attack (incident response capabilities)</li>
            <li>After the inspection, they give the castle lord a report with recommendations for improvements (assessment report)</li>
          </ul>
          <p>Just as the castle inspector helps keep the castle safe from invaders, network security assessments help keep your network safe from cyber attackers!</p>
        </div>

        <h3>Types of Network Security Assessments</h3>
        <p>There are several different types of security assessments, each with its own focus and approach:</p>
        
        <h4>Vulnerability Assessment</h4>
        <ul>
          <li>Identifies and catalogs security vulnerabilities in systems and networks</li>
          <li>Usually automated using scanning tools</li>
          <li>Provides a list of vulnerabilities with severity ratings</li>
          <li>Does not actively exploit vulnerabilities</li>
          <li>Example tools: Nessus, OpenVAS, Qualys</li>
        </ul>
        
        <h4>Penetration Testing</h4>
        <ul>
          <li>Simulates real-world attacks to test security defenses</li>
          <li>Actively attempts to exploit vulnerabilities</li>
          <li>Conducted by skilled security professionals</li>
          <li>Shows what an attacker could actually accomplish</li>
          <li>Example tools: Metasploit, Burp Suite, Kali Linux</li>
        </ul>
        
        <h4>Security Audit</h4>
        <ul>
          <li>Systematic evaluation of security against a set of criteria or standards</li>
          <li>Reviews configurations, policies, and procedures</li>
          <li>Often required for compliance with regulations</li>
          <li>Typically more formal and comprehensive</li>
          <li>Example standards: ISO 27001, NIST Cybersecurity Framework, CIS Controls</li>
        </ul>
        
        <h4>Risk Assessment</h4>
        <ul>
          <li>Identifies and evaluates potential risks to the organization</li>
          <li>Considers both likelihood and impact of security events</li>
          <li>Helps prioritize security efforts based on risk level</li>
          <li>Takes a broader view beyond just technical vulnerabilities</li>
          <li>Example methodologies: NIST SP 800-30, OCTAVE, FAIR</li>
        </ul>
      `
    },
    {
      title: "Security Assessment Methodology",
      type: "text",
      content: `
        <h2>How to Conduct a Network Security Assessment</h2>
        <p>A thorough network security assessment follows a structured methodology. Let's explore the key steps:</p>

        <div class="interactive-diagram">
          <svg id="assessment-methodology-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- Planning Phase -->
            <rect x="100" y="50" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">1. Planning and Preparation</text>
            
            <rect x="150" y="100" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="200" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Define Scope</text>
            
            <rect x="275" y="100" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="325" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Set Objectives</text>
            
            <rect x="400" y="100" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="450" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Get Approvals</text>
            
            <rect x="525" y="100" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="120" text-anchor="middle" font-size="10" fill="#ffffff">Gather Information</text>
            
            <!-- Information Gathering -->
            <rect x="100" y="170" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="200" text-anchor="start" font-size="16" fill="#ffffff">2. Information Gathering</text>
            
            <rect x="150" y="220" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="200" y="240" text-anchor="middle" font-size="10" fill="#ffffff">Network Discovery</text>
            
            <rect x="275" y="220" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="325" y="240" text-anchor="middle" font-size="10" fill="#ffffff">Service Enumeration</text>
            
            <rect x="400" y="220" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="450" y="240" text-anchor="middle" font-size="10" fill="#ffffff">OS Fingerprinting</text>
            
            <rect x="525" y="220" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="240" text-anchor="middle" font-size="10" fill="#ffffff">Network Mapping</text>
            
            <!-- Vulnerability Assessment -->
            <rect x="100" y="290" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="320" text-anchor="start" font-size="16" fill="#ffffff">3. Vulnerability Assessment</text>
            
            <rect x="150" y="340" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="200" y="360" text-anchor="middle" font-size="10" fill="#ffffff">Automated Scans</text>
            
            <rect x="275" y="340" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="325" y="360" text-anchor="middle" font-size="10" fill="#ffffff">Manual Testing</text>
            
            <rect x="400" y="340" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="450" y="360" text-anchor="middle" font-size="10" fill="#ffffff">Configuration Review</text>
            
            <rect x="525" y="340" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="360" text-anchor="middle" font-size="10" fill="#ffffff">Vulnerability Analysis</text>
            
            <!-- Analysis and Reporting -->
            <rect x="100" y="410" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="440" text-anchor="start" font-size="16" fill="#ffffff">4. Analysis and Reporting</text>
            
            <rect x="150" y="460" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="200" y="480" text-anchor="middle" font-size="10" fill="#ffffff">Risk Assessment</text>
            
            <rect x="275" y="460" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="325" y="480" text-anchor="middle" font-size="10" fill="#ffffff">Prioritize Findings</text>
            
            <rect x="400" y="460" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="450" y="480" text-anchor="middle" font-size="10" fill="#ffffff">Create Report</text>
            
            <rect x="525" y="460" width="100" height="30" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="575" y="480" text-anchor="middle" font-size="10" fill="#ffffff">Recommend Solutions</text>
            
            <!-- Remediation -->
            <rect x="100" y="530" width="600" height="50" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="560" text-anchor="start" font-size="16" fill="#ffffff">5. Remediation and Verification</text>
            <text x="450" y="560" text-anchor="start" font-size="12" fill="#ffffff">Fix issues and verify the fixes work</text>
            
            <!-- Flow Arrows -->
            <path d="M 400 150 L 400 170" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 400 270 L 400 290" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 400 390 L 400 410" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 400 510 L 400 530" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            
            <!-- Arrow Definitions -->
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#ffffff" />
              </marker>
            </defs>
          </svg>
        </div>

        <h3>1. Planning and Preparation</h3>
        <p>The first step is to plan the assessment carefully:</p>
        <ul>
          <li><strong>Define Scope:</strong> Determine which systems and networks will be assessed</li>
          <li><strong>Set Objectives:</strong> Establish clear goals for the assessment</li>
          <li><strong>Get Approvals:</strong> Obtain proper authorization before starting</li>
          <li><strong>Gather Information:</strong> Collect documentation about the network</li>
          <li><strong>Select Tools:</strong> Choose appropriate assessment tools</li>
          <li><strong>Create Schedule:</strong> Plan when testing will occur to minimize disruption</li>
        </ul>

        <h3>2. Information Gathering</h3>
        <p>Next, collect detailed information about the network:</p>
        <ul>
          <li><strong>Network Discovery:</strong> Identify all devices on the network</li>
          <li><strong>Service Enumeration:</strong> Determine what services are running on each device</li>
          <li><strong>OS Fingerprinting:</strong> Identify operating systems in use</li>
          <li><strong>Network Mapping:</strong> Create a map of the network topology</li>
          <li><strong>Port Scanning:</strong> Identify open ports on devices</li>
        </ul>
        <p><strong>Common tools:</strong> Nmap, Wireshark, Shodan, Maltego</p>

        <h3>3. Vulnerability Assessment</h3>
        <p>Now, identify vulnerabilities in the network:</p>
        <ul>
          <li><strong>Automated Scanning:</strong> Use tools to scan for known vulnerabilities</li>
          <li><strong>Manual Testing:</strong> Perform hands-on testing for issues that automated tools might miss</li>
          <li><strong>Configuration Review:</strong> Check device and service configurations for security issues</li>
          <li><strong>Password Analysis:</strong> Test for weak or default passwords</li>
          <li><strong>Wireless Security Testing:</strong> Assess the security of wireless networks</li>
        </ul>
        <p><strong>Common tools:</strong> Nessus, OpenVAS, Qualys, Nexpose</p>

        <h3>4. Analysis and Reporting</h3>
        <p>After gathering data, analyze the findings and create a report:</p>
        <ul>
          <li><strong>Risk Assessment:</strong> Evaluate the severity of each vulnerability</li>
          <li><strong>Prioritize Findings:</strong> Rank issues based on risk level</li>
          <li><strong>Create Report:</strong> Document all findings and recommendations</li>
          <li><strong>Recommend Solutions:</strong> Suggest specific fixes for each issue</li>
          <li><strong>Present Results:</strong> Share findings with stakeholders</li>
        </ul>
        <p>A good security report should include:</p>
        <ul>
          <li>Executive summary for management</li>
          <li>Detailed technical findings</li>
          <li>Risk ratings for each vulnerability</li>
          <li>Clear recommendations for remediation</li>
          <li>Supporting evidence (screenshots, logs, etc.)</li>
        </ul>

        <h3>5. Remediation and Verification</h3>
        <p>The final step is to fix the identified issues and verify the fixes:</p>
        <ul>
          <li><strong>Develop Remediation Plan:</strong> Create a plan to address vulnerabilities</li>
          <li><strong>Implement Fixes:</strong> Apply patches, change configurations, etc.</li>
          <li><strong>Verify Remediation:</strong> Re-test to ensure issues are fixed</li>
          <li><strong>Document Changes:</strong> Update documentation with changes made</li>
          <li><strong>Plan Follow-up:</strong> Schedule the next assessment</li>
        </ul>
      `
    }
  ]
};
