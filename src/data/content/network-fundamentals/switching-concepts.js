/**
 * Switching Concepts Module
 *
 * This module covers switch operation, basic configuration, and functionality
 * at a 5th-6th grade reading level (age 10-12).
 */

export const switchingConceptsContent = {
  id: "nf-10",
  pathId: "networking-fundamentals",
  title: "Switching Concepts",
  description: "Learn about network switches, how they work, and their role in creating efficient local area networks.",
  estimatedTime: 60, // minutes
  difficulty: "Beginner",
  objectives: [
    "Understand what a network switch is and how it differs from other network devices",
    "Learn how switches make forwarding decisions using MAC addresses",
    "Explore basic switch features and functions",
    "Practice basic switch configuration concepts"
  ],
  sections: [
    {
      title: "Introduction to Switches",
      type: "text",
      content: `
        <h2>What is a Network Switch?</h2>
        <p>Imagine you're in a big classroom where everyone needs to pass notes to each other. Instead of shouting across the room or having everyone hear every conversation, there's a helper in the middle who knows exactly who should get each note. This helper takes notes from one person and delivers them directly to the right recipient without bothering everyone else.</p>
        <p>A network switch works just like this helper! It's a special device that connects computers, printers, servers, and other devices on a local network. The switch makes sure that information from one device goes directly to the right destination device, without bothering all the other devices.</p>

        <div class="learning-module-story">
          <h3>The Smart Traffic Director</h3>
          <p>Think of a network switch like a really smart traffic director at a busy intersection. This traffic director knows exactly which car needs to go where, and can create temporary direct paths between cars and their destinations. This way, many cars can travel at the same time without crashing into each other!</p>
          <p>Similarly, a network switch creates direct connections between devices that need to communicate, allowing many conversations to happen at the same time without interfering with each other.</p>
        </div>

        <div class="interactive-diagram">
          <svg id="switch-intro-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- SVG content will be added via JavaScript -->
            <rect x="300" y="150" width="200" height="100" rx="10" fill="#f0f0f0" stroke="#333" />
            <text x="400" y="200" text-anchor="middle" font-size="16">Network Switch</text>

            <!-- Connected devices -->
            <rect x="100" y="50" width="120" height="80" rx="10" fill="#e6f7ff" stroke="#0066cc" />
            <text x="160" y="90" text-anchor="middle" font-size="14">Computer A</text>
            <text x="160" y="110" text-anchor="middle" font-size="12">MAC: AA:AA:AA</text>

            <rect x="100" y="270" width="120" height="80" rx="10" fill="#e6f7ff" stroke="#0066cc" />
            <text x="160" y="310" text-anchor="middle" font-size="14">Computer B</text>
            <text x="160" y="330" text-anchor="middle" font-size="12">MAC: BB:BB:BB</text>

            <rect x="580" y="50" width="120" height="80" rx="10" fill="#e6f7ff" stroke="#0066cc" />
            <text x="640" y="90" text-anchor="middle" font-size="14">Printer</text>
            <text x="640" y="110" text-anchor="middle" font-size="12">MAC: CC:CC:CC</text>

            <rect x="580" y="270" width="120" height="80" rx="10" fill="#e6f7ff" stroke="#0066cc" />
            <text x="640" y="310" text-anchor="middle" font-size="14">Server</text>
            <text x="640" y="330" text-anchor="middle" font-size="12">MAC: DD:DD:DD</text>

            <!-- Connection lines -->
            <line x1="220" y1="90" x2="300" y2="170" stroke="#0066cc" stroke-width="2" />
            <line x1="220" y1="310" x2="300" y2="230" stroke="#0066cc" stroke-width="2" />
            <line x1="500" y1="170" x2="580" y2="90" stroke="#0066cc" stroke-width="2" />
            <line x1="500" y1="230" x2="580" y2="310" stroke="#0066cc" stroke-width="2" />

            <!-- Data flow example -->
            <path id="data-path" d="M 160 120 C 160 150, 200 180, 300 180 C 400 180, 500 180, 580 180 C 600 180, 620 150, 640 120" stroke="#00cc00" stroke-width="3" fill="none" stroke-dasharray="10,5" />
            <circle id="data-packet" cx="160" cy="120" r="8" fill="#00cc00" />

            <text x="400" y="350" text-anchor="middle" font-size="14">Direct communication path from Computer A to Printer</text>
            <text x="400" y="370" text-anchor="middle" font-size="12">(Other devices aren't interrupted)</text>
          </svg>
        </div>

        <h3>How Switches Are Different from Other Network Devices</h3>
        <table class="comparison-table">
          <tr>
            <th>Device</th>
            <th>What It Does</th>
            <th>How It's Different from a Switch</th>
          </tr>
          <tr>
            <td><strong>Hub</strong></td>
            <td>Connects multiple devices together</td>
            <td>A hub sends data to ALL connected devices, even if it's only meant for one device. This creates a lot of unnecessary traffic and can slow down the network.</td>
          </tr>
          <tr>
            <td><strong>Router</strong></td>
            <td>Connects different networks together</td>
            <td>A router works between different networks (like connecting your home network to the internet), while a switch connects devices within the same network.</td>
          </tr>
          <tr>
            <td><strong>Access Point</strong></td>
            <td>Provides wireless network access</td>
            <td>An access point connects wireless devices to a network, while a switch typically connects wired devices.</td>
          </tr>
        </table>

        <h3>Why Switches Are Important</h3>
        <ul>
          <li><strong>Efficiency:</strong> Switches create direct connections between devices, allowing multiple conversations to happen at the same time.</li>
          <li><strong>Speed:</strong> By sending data only to the intended recipient, switches reduce unnecessary network traffic and improve speed.</li>
          <li><strong>Security:</strong> Since data is sent only to the specific destination device, it's harder for other devices to see information that isn't meant for them.</li>
          <li><strong>Reliability:</strong> Switches can detect and manage network problems, making the network more reliable.</li>
        </ul>
      `
    },
    {
      title: "How Switches Work",
      type: "text",
      content: `
        <h2>How Switches Make Decisions</h2>
        <p>Have you ever wondered how a switch knows where to send information? It's like magic - you plug in a bunch of computers, and somehow the switch knows exactly which computer should get which message. Let's learn how this works!</p>

        <div class="learning-module-story">
          <h3>The School Mailroom</h3>
          <p>Imagine a school mailroom with hundreds of mailboxes. When a letter arrives, the mail clerk needs to know which mailbox to put it in. To do this, the clerk keeps a list that matches each student's name to their mailbox number.</p>
          <p>A network switch works the same way! It keeps a special list called a "MAC address table" that matches each device's unique address (called a MAC address) to the specific port where that device is connected.</p>
        </div>

        <h3>MAC Addresses: The Special ID for Every Device</h3>
        <p>Every device that can connect to a network has a unique identifier called a MAC address (Media Access Control address). Think of it like a serial number that's built into your device when it's manufactured.</p>
        <p>A MAC address looks like this: <code>00:1A:2B:3C:4D:5E</code></p>
        <p>This address is used by the switch to know exactly where to send information.</p>

        <div class="interactive-diagram">
          <svg id="mac-table-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- SVG content will be added via JavaScript -->
            <rect x="300" y="50" width="200" height="100" rx="10" fill="#f0f0f0" stroke="#333" />
            <text x="400" y="100" text-anchor="middle" font-size="16">Network Switch</text>

            <!-- MAC Address Table -->
            <rect x="250" y="170" width="300" height="180" rx="5" fill="#f9f9f9" stroke="#333" />
            <text x="400" y="190" text-anchor="middle" font-size="14">MAC Address Table</text>

            <line x1="250" y1="200" x2="550" y2="200" stroke="#333" stroke-width="1" />
            <text x="300" y="220" text-anchor="middle" font-size="12">MAC Address</text>
            <text x="500" y="220" text-anchor="middle" font-size="12">Port</text>
            <line x1="250" y1="230" x2="550" y2="230" stroke="#333" stroke-width="1" />

            <text x="300" y="250" text-anchor="middle" font-size="12">AA:AA:AA</text>
            <text x="500" y="250" text-anchor="middle" font-size="12">Port 1</text>
            <line x1="250" y1="260" x2="550" y2="260" stroke="#333" stroke-width="1" />

            <text x="300" y="280" text-anchor="middle" font-size="12">BB:BB:BB</text>
            <text x="500" y="280" text-anchor="middle" font-size="12">Port 2</text>
            <line x1="250" y1="290" x2="550" y2="290" stroke="#333" stroke-width="1" />

            <text x="300" y="310" text-anchor="middle" font-size="12">CC:CC:CC</text>
            <text x="500" y="310" text-anchor="middle" font-size="12">Port 3</text>
            <line x1="250" y1="320" x2="550" y2="320" stroke="#333" stroke-width="1" />

            <text x="300" y="340" text-anchor="middle" font-size="12">DD:DD:DD</text>
            <text x="500" y="340" text-anchor="middle" font-size="12">Port 4</text>

            <!-- Connected devices -->
            <rect x="50" y="50" width="120" height="60" rx="5" fill="#e6f7ff" stroke="#0066cc" />
            <text x="110" y="80" text-anchor="middle" font-size="12">Computer A</text>
            <text x="110" y="100" text-anchor="middle" font-size="10">MAC: AA:AA:AA</text>

            <rect x="50" y="170" width="120" height="60" rx="5" fill="#e6f7ff" stroke="#0066cc" />
            <text x="110" y="200" text-anchor="middle" font-size="12">Computer B</text>
            <text x="110" y="220" text-anchor="middle" font-size="10">MAC: BB:BB:BB</text>

            <rect x="50" y="290" width="120" height="60" rx="5" fill="#e6f7ff" stroke="#0066cc" />
            <text x="110" y="320" text-anchor="middle" font-size="12">Printer</text>
            <text x="110" y="340" text-anchor="middle" font-size="10">MAC: CC:CC:CC</text>

            <rect x="630" y="170" width="120" height="60" rx="5" fill="#e6f7ff" stroke="#0066cc" />
            <text x="690" y="200" text-anchor="middle" font-size="12">Server</text>
            <text x="690" y="220" text-anchor="middle" font-size="10">MAC: DD:DD:DD</text>

            <!-- Connection lines -->
            <line x1="170" y1="80" x2="300" y2="80" stroke="#0066cc" stroke-width="2" />
            <text x="200" y="70" text-anchor="middle" font-size="10">Port 1</text>

            <line x1="170" y1="200" x2="300" y2="100" stroke="#0066cc" stroke-width="2" />
            <text x="200" y="160" text-anchor="middle" font-size="10">Port 2</text>

            <line x1="170" y1="320" x2="300" y2="120" stroke="#0066cc" stroke-width="2" />
            <text x="200" y="230" text-anchor="middle" font-size="10">Port 3</text>

            <line x1="630" y1="200" x2="500" y2="100" stroke="#0066cc" stroke-width="2" />
            <text x="580" y="160" text-anchor="middle" font-size="10">Port 4</text>
          </svg>
        </div>

        <h3>How a Switch Learns MAC Addresses</h3>
        <p>When you first set up a switch, its MAC address table is empty. It learns where devices are connected through a simple process:</p>
        <ol>
          <li><strong>Listen and Learn:</strong> When a device sends data through the switch, the switch looks at the source MAC address and remembers which port it came from.</li>
          <li><strong>Update the Table:</strong> The switch adds this information to its MAC address table.</li>
          <li><strong>Make Better Decisions:</strong> Next time data needs to go to that device, the switch knows exactly which port to use.</li>
        </ol>

        <h3>The Three Ways a Switch Handles Data</h3>
        <ol>
          <li><strong>Unicast:</strong> When data is sent from one device to one specific device. The switch looks up the destination MAC address in its table and forwards the data only to the correct port.</li>
          <li><strong>Broadcast:</strong> When data is sent from one device to ALL devices on the network. The switch forwards this data to all ports except the one it came from.</li>
          <li><strong>Multicast:</strong> When data is sent from one device to a specific GROUP of devices. The switch forwards this data only to the ports where devices in that group are connected.</li>
        </ol>
      `
    },
    {
      title: "Switch Features and Functions",
      type: "text",
      content: `
        <h2>Cool Things Switches Can Do</h2>
        <p>Switches aren't just simple devices that forward data. Modern switches have many special features that make networks faster, more secure, and easier to manage. Let's explore some of these cool features!</p>

        <div class="feature-cards">
          <div class="feature-card">
            <h3>Port Speed and Duplex</h3>
            <p><strong>What it is:</strong> Switches can have different speeds for different ports (like 10 Mbps, 100 Mbps, 1 Gbps, or even 10 Gbps). Duplex refers to whether data can flow in both directions at the same time (full-duplex) or only one direction at a time (half-duplex).</p>
            <p><strong>Why it matters:</strong> Faster ports mean data transfers more quickly. Full-duplex means devices can send and receive data at the same time, making the network even faster.</p>
          </div>

          <div class="feature-card">
            <h3>VLANs (Virtual Local Area Networks)</h3>
            <p><strong>What it is:</strong> VLANs let you create separate virtual networks within a single physical switch. It's like having multiple switches in one device!</p>
            <p><strong>Why it matters:</strong> VLANs help organize networks by department, function, or security level. For example, you could have one VLAN for teachers and another for students in a school.</p>
          </div>

          <div class="feature-card">
            <h3>Power over Ethernet (PoE)</h3>
            <p><strong>What it is:</strong> Some switches can provide electrical power through the same cable that carries data.</p>
            <p><strong>Why it matters:</strong> This lets you power devices like security cameras, wireless access points, or IP phones without needing a separate power cord.</p>
          </div>

          <div class="feature-card">
            <h3>Spanning Tree Protocol (STP)</h3>
            <p><strong>What it is:</strong> A protocol that prevents loops in networks with multiple switches connected together.</p>
            <p><strong>Why it matters:</strong> Network loops can cause broadcast storms that crash the entire network. STP automatically detects and prevents these loops.</p>
          </div>

          <div class="feature-card">
            <h3>Port Security</h3>
            <p><strong>What it is:</strong> Features that control which devices can connect to which ports on the switch.</p>
            <p><strong>Why it matters:</strong> This prevents unauthorized devices from connecting to your network and helps protect against security threats.</p>
          </div>

          <div class="feature-card">
            <h3>Quality of Service (QoS)</h3>
            <p><strong>What it is:</strong> The ability to prioritize certain types of network traffic over others.</p>
            <p><strong>Why it matters:</strong> This ensures important applications like video calls or online games get priority over less time-sensitive traffic like email or file downloads.</p>
          </div>
        </div>

        <div class="learning-module-story">
          <h3>The School Bus System</h3>
          <p>Think of a switch with all these features like a smart school bus system:</p>
          <ul>
            <li>Some buses are regular speed, while others are express buses (port speeds)</li>
            <li>There are separate buses for elementary, middle, and high school students (VLANs)</li>
            <li>Some buses have snacks and drinks available on board (PoE providing extra services)</li>
            <li>The bus system has maps to avoid traffic jams and road closures (STP avoiding loops)</li>
            <li>Students need ID cards to board their assigned bus (port security)</li>
            <li>Emergency vehicles and school buses get priority at intersections (QoS)</li>
          </ul>
        </div>

        <div class="interactive-diagram">
          <svg id="switch-features-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- SVG content will be added via JavaScript -->
            <rect x="300" y="150" width="200" height="100" rx="10" fill="#f0f0f0" stroke="#333" />
            <text x="400" y="180" text-anchor="middle" font-size="16">Smart Switch</text>
            <text x="400" y="200" text-anchor="middle" font-size="12">with Advanced Features</text>

            <!-- Feature icons -->
            <circle cx="250" y="100" r="30" fill="#e6f7ff" stroke="#0066cc" />
            <text x="250" y="105" text-anchor="middle" font-size="12">PoE</text>
            <line x1="250" y1="130" x2="300" y2="150" stroke="#0066cc" stroke-width="2" />

            <circle cx="200" y="180" r="30" fill="#e6f7ff" stroke="#0066cc" />
            <text x="200" y="185" text-anchor="middle" font-size="12">VLANs</text>
            <line x1="230" y1="180" x2="300" y2="180" stroke="#0066cc" stroke-width="2" />

            <circle cx="250" y="260" r="30" fill="#e6f7ff" stroke="#0066cc" />
            <text x="250" y="265" text-anchor="middle" font-size="12">QoS</text>
            <line x1="250" y1="230" x2="300" y2="210" stroke="#0066cc" stroke-width="2" />

            <circle cx="550" y="100" r="30" fill="#e6f7ff" stroke="#0066cc" />
            <text x="550" y="105" text-anchor="middle" font-size="12">STP</text>
            <line x1="550" y1="130" x2="500" y2="150" stroke="#0066cc" stroke-width="2" />

            <circle cx="600" y="180" r="30" fill="#e6f7ff" stroke="#0066cc" />
            <text x="600" y="175" text-anchor="middle" font-size="12">Port</text>
            <text x="600" y="190" text-anchor="middle" font-size="12">Security</text>
            <line x1="570" y1="180" x2="500" y2="180" stroke="#0066cc" stroke-width="2" />

            <circle cx="550" y="260" r="30" fill="#e6f7ff" stroke="#0066cc" />
            <text x="550" y="255" text-anchor="middle" font-size="10">Gigabit</text>
            <text x="550" y="270" text-anchor="middle" font-size="10">Speeds</text>
            <line x1="550" y1="230" x2="500" y2="210" stroke="#0066cc" stroke-width="2" />

            <!-- Connected devices -->
            <rect x="380" y="300" width="40" height="60" rx="5" fill="#e6f7ff" stroke="#0066cc" />
            <text x="400" y="340" text-anchor="middle" font-size="10" transform="rotate(-90, 400, 340)">Security Camera</text>
            <line x1="400" y1="300" x2="400" y2="250" stroke="#0066cc" stroke-width="2" />
            <text x="410" y="280" text-anchor="middle" font-size="10">PoE</text>
          </svg>
        </div>

        <h3>Types of Switches</h3>
        <p>Switches come in different types for different needs:</p>
        <ul>
          <li><strong>Unmanaged Switches:</strong> Simple, plug-and-play switches with no special settings. Good for home networks or small offices.</li>
          <li><strong>Managed Switches:</strong> Advanced switches that can be configured with special settings. These have all the cool features we talked about and are used in larger networks.</li>
          <li><strong>Layer 2 Switches:</strong> Standard switches that work with MAC addresses to forward data within a single network.</li>
          <li><strong>Layer 3 Switches:</strong> Advanced switches that can also route data between different networks, like a combination of a switch and a router.</li>
        </ul>
      `
    },
    {
      title: "Basic Switch Configuration",
      type: "text",
      content: `
        <h2>Setting Up a Switch</h2>
        <p>Configuring a switch means setting it up with the right settings so it works properly on your network. Let's learn about some basic switch configuration concepts!</p>

        <div class="learning-module-story">
          <h3>Setting Up a New Game Console</h3>
          <p>Configuring a switch is a bit like setting up a new game console. You need to:</p>
          <ol>
            <li>Give it a name so you know which one it is</li>
            <li>Set up user accounts and passwords to control who can change settings</li>
            <li>Configure the display settings for the best experience</li>
            <li>Connect it to your home network</li>
            <li>Update its software to get the latest features</li>
          </ol>
          <p>Switch configuration follows similar steps, just with networking terms!</p>
        </div>

        <h3>Basic Switch Configuration Steps</h3>
        <ol>
          <li><strong>Naming the Switch:</strong> Giving the switch a hostname makes it easier to identify in the network.</li>
          <li><strong>Setting Up Access Security:</strong> Creating usernames, passwords, and privilege levels to control who can make changes to the switch.</li>
          <li><strong>Configuring Management Access:</strong> Setting up ways to connect to the switch for management, like console, SSH, or web interface.</li>
          <li><strong>Setting Up IP Information:</strong> Giving the switch an IP address so it can be managed over the network.</li>
          <li><strong>Configuring Ports:</strong> Setting speed, duplex, and other features for each port.</li>
          <li><strong>Updating Firmware:</strong> Installing the latest software to get new features and security fixes.</li>
        </ol>

        <div class="configuration-example">
          <h3>Example: Basic Switch Configuration Commands</h3>
          <p>Here's what some basic switch configuration commands might look like on a Cisco switch:</p>
          <pre class="code-block">
# Enter configuration mode
Switch> enable
Switch# configure terminal

# Give the switch a name
Switch(config)# hostname ClassroomSwitch

# Set up a password for privileged access
ClassroomSwitch(config)# enable secret MySecurePassword123

# Configure a management IP address
ClassroomSwitch(config)# interface vlan 1
ClassroomSwitch(config-if)# ip address ************ *************
ClassroomSwitch(config-if)# no shutdown
ClassroomSwitch(config-if)# exit

# Configure a specific port for a server
ClassroomSwitch(config)# interface gigabitethernet 0/1
ClassroomSwitch(config-if)# description Connection to Main Server
ClassroomSwitch(config-if)# speed 1000
ClassroomSwitch(config-if)# duplex full
ClassroomSwitch(config-if)# no shutdown
ClassroomSwitch(config-if)# exit

# Save the configuration
ClassroomSwitch(config)# exit
ClassroomSwitch# copy running-config startup-config
          </pre>
          <p>Don't worry about memorizing these commands! The important thing is to understand the concepts of what needs to be configured.</p>
        </div>

        <h3>Switch Configuration Methods</h3>
        <p>There are several ways to configure a switch:</p>
        <ul>
          <li><strong>Command Line Interface (CLI):</strong> Typing commands directly into the switch through a console connection or SSH.</li>
          <li><strong>Web Interface:</strong> Using a web browser to access the switch's configuration pages.</li>
          <li><strong>Network Management Software:</strong> Using special software to configure and monitor multiple switches from one place.</li>
        </ul>

        <div class="interactive-diagram">
          <svg id="switch-config-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- SVG content will be added via JavaScript -->
            <rect x="300" y="150" width="200" height="100" rx="10" fill="#f0f0f0" stroke="#333" />
            <text x="400" y="180" text-anchor="middle" font-size="16">Network Switch</text>
            <text x="400" y="200" text-anchor="middle" font-size="12">"ClassroomSwitch"</text>

            <!-- Configuration methods -->
            <rect x="50" y="150" width="150" height="100" rx="5" fill="#e6f7ff" stroke="#0066cc" />
            <text x="125" y="180" text-anchor="middle" font-size="14">Command Line</text>
            <text x="125" y="200" text-anchor="middle" font-size="12">Interface (CLI)</text>
            <line x1="200" y1="200" x2="300" y2="200" stroke="#0066cc" stroke-width="2" />

            <rect x="300" y="300" width="200" height="80" rx="5" fill="#e6f7ff" stroke="#0066cc" />
            <text x="400" y="340" text-anchor="middle" font-size="14">Web Interface</text>
            <line x1="400" y1="300" x2="400" y2="250" stroke="#0066cc" stroke-width="2" />

            <rect x="600" y="150" width="150" height="100" rx="5" fill="#e6f7ff" stroke="#0066cc" />
            <text x="675" y="180" text-anchor="middle" font-size="14">Network</text>
            <text x="675" y="200" text-anchor="middle" font-size="14">Management</text>
            <text x="675" y="220" text-anchor="middle" font-size="14">Software</text>
            <line x1="600" y1="200" x2="500" y2="200" stroke="#0066cc" stroke-width="2" />

            <!-- Configuration settings -->
            <rect x="300" y="30" width="200" height="80" rx="5" fill="#f9f9f9" stroke="#333" />
            <text x="400" y="50" text-anchor="middle" font-size="14">Configuration Settings</text>
            <text x="400" y="70" text-anchor="middle" font-size="12">Hostname: ClassroomSwitch</text>
            <text x="400" y="90" text-anchor="middle" font-size="12">IP: ************</text>
            <text x="400" y="110" text-anchor="middle" font-size="12">Port 1: Server Connection</text>
            <line x1="400" y1="110" x2="400" y2="150" stroke="#333" stroke-width="2" />
          </svg>
        </div>
      `
    },
    {
      title: "Switch Practice and Knowledge Check",
      type: "interactive",
      content: `
        <h2>Switch Configuration Practice</h2>
        <p>Let's practice what we've learned about switches by doing some hands-on exercises!</p>

        <div id="switch-simulator" class="interactive-exercise">
          <h3>Switch Configuration Simulator</h3>
          <p>In this exercise, you'll help configure a network switch for a classroom.</p>

          <div class="exercise-container">
            <div class="exercise-controls">
              <button id="start-switch-button">Start Configuration</button>
              <button id="reset-switch-button">Reset</button>
            </div>

            <div class="exercise-visualization">
              <!-- This will be populated by JavaScript -->
            </div>

            <div class="exercise-steps">
              <div class="step" data-step="1">
                <h4>Step 1: Name the Switch</h4>
                <p>What hostname should we give to the classroom switch?</p>
                <select id="hostname-select">
                  <option value="">Select a hostname...</option>
                  <option value="correct">ClassroomSwitch</option>
                  <option value="wrong1">Switch1</option>
                  <option value="wrong2">MySwitch</option>
                </select>
                <button class="step-button" data-action="hostname">Set Hostname</button>
              </div>

              <div class="step" data-step="2" style="display: none;">
                <h4>Step 2: Configure Management IP</h4>
                <p>What IP address should we assign to the switch for management?</p>
                <select id="ip-select">
                  <option value="">Select an IP address...</option>
                  <option value="correct">************</option>
                  <option value="wrong1">********</option>
                  <option value="wrong2">**********</option>
                </select>
                <button class="step-button" data-action="ip">Set IP Address</button>
              </div>

              <div class="step" data-step="3" style="display: none;">
                <h4>Step 3: Configure a Port for the Teacher's Computer</h4>
                <p>How should we configure port 1 for the teacher's computer?</p>
                <select id="port-select">
                  <option value="">Select port configuration...</option>
                  <option value="correct">Speed: 1000 Mbps, Full-Duplex</option>
                  <option value="wrong1">Speed: 10 Mbps, Half-Duplex</option>
                  <option value="wrong2">Speed: Auto, Duplex: Auto</option>
                </select>
                <button class="step-button" data-action="port">Configure Port</button>
              </div>

              <div class="step" data-step="4" style="display: none;">
                <h4>Success!</h4>
                <p>Congratulations! You've successfully configured the classroom switch with:</p>
                <ul>
                  <li>Hostname: ClassroomSwitch</li>
                  <li>Management IP: ************</li>
                  <li>Port 1 (Teacher's Computer): 1000 Mbps, Full-Duplex</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <h2>Knowledge Check</h2>
        <p>Let's see how much you've learned about network switches!</p>

        <div class="quiz-container" id="switch-quiz">
          <div class="quiz-question" data-question-id="1">
            <h3>Question 1:</h3>
            <p>What is the main difference between a hub and a switch?</p>
            <div class="quiz-options">
              <label><input type="radio" name="q1" value="a"> A hub is wireless while a switch is wired</label>
              <label><input type="radio" name="q1" value="b"> A switch creates direct connections between devices, while a hub sends data to all connected devices</label>
              <label><input type="radio" name="q1" value="c"> A switch connects different networks, while a hub connects devices in the same network</label>
              <label><input type="radio" name="q1" value="d"> A hub is faster than a switch</label>
            </div>
            <div class="quiz-feedback" data-for="q1-b">
              <p>Correct! A switch is smarter than a hub because it creates direct connections between devices that need to communicate, while a hub sends all data to every connected device.</p>
            </div>
            <div class="quiz-feedback" data-for="q1-wrong">
              <p>Not quite. A switch creates direct connections between devices that need to communicate, while a hub sends all data to every connected device.</p>
            </div>
          </div>

          <div class="quiz-question" data-question-id="2">
            <h3>Question 2:</h3>
            <p>What information does a switch use to decide where to send data?</p>
            <div class="quiz-options">
              <label><input type="radio" name="q2" value="a"> IP addresses</label>
              <label><input type="radio" name="q2" value="b"> MAC addresses</label>
              <label><input type="radio" name="q2" value="c"> Domain names</label>
              <label><input type="radio" name="q2" value="d"> Email addresses</label>
            </div>
            <div class="quiz-feedback" data-for="q2-b">
              <p>Correct! Switches use MAC addresses (Media Access Control addresses) to determine which port to send data to.</p>
            </div>
            <div class="quiz-feedback" data-for="q2-wrong">
              <p>Not quite. Switches use MAC addresses (Media Access Control addresses) to determine which port to send data to.</p>
            </div>
          </div>

          <div class="quiz-question" data-question-id="3">
            <h3>Question 3:</h3>
            <p>What is Power over Ethernet (PoE)?</p>
            <div class="quiz-options">
              <label><input type="radio" name="q3" value="a"> A way to make switches run faster</label>
              <label><input type="radio" name="q3" value="b"> A feature that allows switches to provide electrical power through network cables</label>
              <label><input type="radio" name="q3" value="c"> A method for connecting switches to power outlets</label>
              <label><input type="radio" name="q3" value="d"> A protocol for managing power consumption on networks</label>
            </div>
            <div class="quiz-feedback" data-for="q3-b">
              <p>Correct! Power over Ethernet (PoE) allows switches to provide electrical power to devices like IP cameras or wireless access points through the same cable that carries data.</p>
            </div>
            <div class="quiz-feedback" data-for="q3-wrong">
              <p>Not quite. Power over Ethernet (PoE) allows switches to provide electrical power to devices like IP cameras or wireless access points through the same cable that carries data.</p>
            </div>
          </div>

          <div class="quiz-question" data-question-id="4">
            <h3>Question 4:</h3>
            <p>What is a VLAN?</p>
            <div class="quiz-options">
              <label><input type="radio" name="q4" value="a"> A type of network cable</label>
              <label><input type="radio" name="q4" value="b"> A virtual local area network that divides a physical switch into multiple logical networks</label>
              <label><input type="radio" name="q4" value="c"> A very large area network that connects multiple cities</label>
              <label><input type="radio" name="q4" value="d"> A visual display that shows network traffic</label>
            </div>
            <div class="quiz-feedback" data-for="q4-b">
              <p>Correct! A VLAN (Virtual Local Area Network) divides a physical switch into multiple logical networks, allowing you to group devices together even if they're connected to different ports.</p>
            </div>
            <div class="quiz-feedback" data-for="q4-wrong">
              <p>Not quite. A VLAN (Virtual Local Area Network) divides a physical switch into multiple logical networks, allowing you to group devices together even if they're connected to different ports.</p>
            </div>
          </div>

          <div class="quiz-question" data-question-id="5">
            <h3>Question 5:</h3>
            <p>What is the purpose of the Spanning Tree Protocol (STP)?</p>
            <div class="quiz-options">
              <label><input type="radio" name="q5" value="a"> To increase network speed</label>
              <label><input type="radio" name="q5" value="b"> To connect switches to routers</label>
              <label><input type="radio" name="q5" value="c"> To prevent network loops in networks with multiple switches</label>
              <label><input type="radio" name="q5" value="d"> To manage wireless connections</label>
            </div>
            <div class="quiz-feedback" data-for="q5-c">
              <p>Correct! The Spanning Tree Protocol (STP) prevents network loops in networks with multiple switches, which could otherwise cause broadcast storms and crash the network.</p>
            </div>
            <div class="quiz-feedback" data-for="q5-wrong">
              <p>Not quite. The Spanning Tree Protocol (STP) prevents network loops in networks with multiple switches, which could otherwise cause broadcast storms and crash the network.</p>
            </div>
          </div>

          <button id="submit-quiz" class="quiz-submit-button">Submit Answers</button>

          <div id="quiz-results" class="quiz-results">
            <h3>Quiz Results</h3>
            <p>You scored: <span id="quiz-score">0</span> out of 5</p>
            <div id="quiz-feedback"></div>
          </div>
        </div>
      `
    }
  ]
};
