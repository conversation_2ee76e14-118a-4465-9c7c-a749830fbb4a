/**
 * Network Troubleshooting Module
 *
 * This file contains the content for the Network Troubleshooting module
 * in the Network Fundamentals learning path.
 */

export const networkTroubleshootingContent = {
  id: "nf-18",
  pathId: "networking-fundamentals",
  title: "Network Troubleshooting",
  description: "Master the methodologies and tools for diagnosing and resolving network issues.",
  objectives: [
    "Understand structured troubleshooting methodologies",
    "Learn to use common network diagnostic tools",
    "Identify and resolve common network issues",
    "Develop effective documentation practices for network problems and solutions"
  ],
  difficulty: "Intermediate",
  estimatedTime: 60, // minutes
  sections: [
    {
      title: "Troubleshooting Methodology",
      type: "text",
      content: `
        <h2>Structured Network Troubleshooting</h2>
        <p>Imagine you're a detective trying to solve a mystery. You wouldn't just guess who did it - you'd follow clues, gather evidence, and use a systematic approach. Network troubleshooting works the same way!</p>
        
        <p>Network troubleshooting is the process of identifying, diagnosing, and resolving problems in computer networks. Using a structured methodology helps you solve problems efficiently and effectively.</p>

        <div class="interactive-diagram">
          <svg id="troubleshooting-methodology-svg" viewBox="0 0 800 400" width="100%" height="400">
            <!-- Troubleshooting Steps -->
            <rect x="100" y="50" width="600" height="300" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="400" y="80" text-anchor="middle" font-size="18" fill="#ffffff">Network Troubleshooting Methodology</text>
            
            <!-- Step 1 -->
            <circle cx="150" cy="150" r="30" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="150" y="155" text-anchor="middle" font-size="16" fill="#ffffff">1</text>
            <text x="250" y="155" text-anchor="start" font-size="14" fill="#ffffff">Identify the Problem</text>
            
            <!-- Step 2 -->
            <circle cx="150" cy="220" r="30" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="150" y="225" text-anchor="middle" font-size="16" fill="#ffffff">2</text>
            <text x="250" y="225" text-anchor="start" font-size="14" fill="#ffffff">Establish a Theory of Probable Cause</text>
            
            <!-- Step 3 -->
            <circle cx="150" cy="290" r="30" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="150" y="295" text-anchor="middle" font-size="16" fill="#ffffff">3</text>
            <text x="250" y="295" text-anchor="start" font-size="14" fill="#ffffff">Test the Theory</text>
            
            <!-- Step 4 -->
            <circle cx="450" cy="150" r="30" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="450" y="155" text-anchor="middle" font-size="16" fill="#ffffff">4</text>
            <text x="550" y="155" text-anchor="start" font-size="14" fill="#ffffff">Establish a Plan of Action</text>
            
            <!-- Step 5 -->
            <circle cx="450" cy="220" r="30" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="450" y="225" text-anchor="middle" font-size="16" fill="#ffffff">5</text>
            <text x="550" y="225" text-anchor="start" font-size="14" fill="#ffffff">Implement the Solution</text>
            
            <!-- Step 6 -->
            <circle cx="450" cy="290" r="30" fill="#1e3a5f" stroke="#38b2ac" />
            <text x="450" y="295" text-anchor="middle" font-size="16" fill="#ffffff">6</text>
            <text x="550" y="295" text-anchor="start" font-size="14" fill="#ffffff">Verify and Document</text>
            
            <!-- Connecting Arrows -->
            <path d="M 150 180 L 150 190" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 150 250 L 150 260" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 180 290 L 420 290" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 450 260 L 450 250" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            <path d="M 450 190 L 450 180" stroke="#ffffff" fill="none" stroke-width="2" marker-end="url(#arrowhead)" />
            
            <!-- Loop back arrow -->
            <path d="M 420 150 C 350 120, 250 120, 180 150" stroke="#ffffff" fill="none" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
            <text x="300" y="120" text-anchor="middle" font-size="12" fill="#ffffff">If solution doesn't work</text>
            
            <!-- Arrow Definitions -->
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#ffffff" />
              </marker>
            </defs>
          </svg>
        </div>

        <h3>Step 1: Identify the Problem</h3>
        <p>The first step is to clearly define what the problem is. This involves:</p>
        <ul>
          <li>Gathering information from users about what they're experiencing</li>
          <li>Determining the scope of the problem (one user, a department, everyone?)</li>
          <li>Identifying when the problem started</li>
          <li>Documenting any recent changes to the network</li>
        </ul>
        <p><strong>Key questions to ask:</strong></p>
        <ul>
          <li>"What exactly is happening or not happening?"</li>
          <li>"When did you first notice the problem?"</li>
          <li>"Has anything changed recently in the network?"</li>
          <li>"Who is affected by this issue?"</li>
        </ul>

        <h3>Step 2: Establish a Theory of Probable Cause</h3>
        <p>Based on the information gathered, develop theories about what might be causing the problem:</p>
        <ul>
          <li>Start with the simplest, most likely causes</li>
          <li>Consider the OSI model layers (start from physical and work up)</li>
          <li>Think about similar problems you've encountered before</li>
        </ul>

        <h3>Step 3: Test the Theory</h3>
        <p>Once you have theories, test them to determine the actual cause:</p>
        <ul>
          <li>Use appropriate diagnostic tools</li>
          <li>Make one change at a time</li>
          <li>Document what you test and the results</li>
        </ul>

        <div class="learning-module-story">
          <h3>The Network Doctor</h3>
          <p>Think of network troubleshooting like a doctor diagnosing a patient:</p>
          <ul>
            <li>First, the doctor asks about symptoms (identifying the problem)</li>
            <li>Then, the doctor forms theories about possible illnesses (establishing probable causes)</li>
            <li>Next, the doctor orders tests to confirm or rule out theories (testing the theory)</li>
            <li>Once the diagnosis is confirmed, the doctor creates a treatment plan (plan of action)</li>
            <li>The doctor administers treatment (implementing the solution)</li>
            <li>Finally, the doctor checks if the patient is getting better and updates medical records (verify and document)</li>
          </ul>
          <p>Just like a good doctor follows a systematic approach to diagnose and treat patients, a good network troubleshooter follows a systematic approach to diagnose and fix network problems!</p>
        </div>

        <h3>Step 4: Establish a Plan of Action</h3>
        <p>Once you've identified the cause, develop a plan to fix it:</p>
        <ul>
          <li>Consider the potential impact of your solution</li>
          <li>Determine if you need to schedule downtime</li>
          <li>Create a backup plan in case something goes wrong</li>
          <li>Get necessary approvals if required</li>
        </ul>

        <h3>Step 5: Implement the Solution</h3>
        <p>Execute your plan to resolve the issue:</p>
        <ul>
          <li>Make changes carefully and methodically</li>
          <li>Follow change management procedures</li>
          <li>Document each step as you implement it</li>
        </ul>

        <h3>Step 6: Verify and Document</h3>
        <p>Confirm that the problem is resolved and document what you did:</p>
        <ul>
          <li>Test to ensure the issue is fixed</li>
          <li>Verify with users that everything is working properly</li>
          <li>Document the problem, cause, and solution</li>
          <li>Update network documentation if necessary</li>
          <li>Share knowledge with the team</li>
        </ul>
      `
    },
    {
      title: "Network Diagnostic Tools",
      type: "text",
      content: `
        <h2>Essential Network Troubleshooting Tools</h2>
        <p>Having the right tools is crucial for effective network troubleshooting. Let's explore some of the most important ones:</p>

        <div class="interactive-diagram">
          <svg id="diagnostic-tools-svg" viewBox="0 0 800 600" width="100%" height="600">
            <!-- Ping -->
            <rect x="100" y="50" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="80" text-anchor="start" font-size="16" fill="#ffffff">Ping</text>
            <text x="150" y="100" text-anchor="start" font-size="12" fill="#ffffff">Tests basic connectivity using ICMP echo requests</text>
            
            <rect x="500" y="70" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="510" y="90" text-anchor="start" font-size="10" fill="#38b2ac">$ ping google.com</text>
            <text x="510" y="105" text-anchor="start" font-size="10" fill="#38b2ac">64 bytes from *************: icmp_seq=1 ttl=57 time=14.5 ms</text>
            <text x="510" y="120" text-anchor="start" font-size="10" fill="#38b2ac">64 bytes from *************: icmp_seq=2 ttl=57 time=15.2 ms</text>
            
            <!-- Traceroute -->
            <rect x="100" y="170" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="200" text-anchor="start" font-size="16" fill="#ffffff">Traceroute / Tracert</text>
            <text x="150" y="220" text-anchor="start" font-size="12" fill="#ffffff">Shows the path packets take to reach a destination</text>
            
            <rect x="500" y="190" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="510" y="210" text-anchor="start" font-size="10" fill="#38b2ac">$ traceroute google.com</text>
            <text x="510" y="225" text-anchor="start" font-size="10" fill="#38b2ac">1  ***********  1.2 ms</text>
            <text x="510" y="240" text-anchor="start" font-size="10" fill="#38b2ac">2  ********  5.4 ms</text>
            
            <!-- ipconfig/ifconfig -->
            <rect x="100" y="290" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="320" text-anchor="start" font-size="16" fill="#ffffff">ipconfig / ifconfig</text>
            <text x="150" y="340" text-anchor="start" font-size="12" fill="#ffffff">Displays network interface configuration</text>
            
            <rect x="500" y="310" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="510" y="330" text-anchor="start" font-size="10" fill="#38b2ac">$ ipconfig</text>
            <text x="510" y="345" text-anchor="start" font-size="10" fill="#38b2ac">IPv4 Address: ***********</text>
            <text x="510" y="360" text-anchor="start" font-size="10" fill="#38b2ac">Subnet Mask: *************</text>
            
            <!-- nslookup/dig -->
            <rect x="100" y="410" width="600" height="100" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="440" text-anchor="start" font-size="16" fill="#ffffff">nslookup / dig</text>
            <text x="150" y="460" text-anchor="start" font-size="12" fill="#ffffff">Queries DNS servers for domain information</text>
            
            <rect x="500" y="430" width="150" height="60" rx="5" fill="#1a202c" stroke="#4a5568" />
            <text x="510" y="450" text-anchor="start" font-size="10" fill="#38b2ac">$ nslookup google.com</text>
            <text x="510" y="465" text-anchor="start" font-size="10" fill="#38b2ac">Server: ***********</text>
            <text x="510" y="480" text-anchor="start" font-size="10" fill="#38b2ac">Address: *************</text>
            
            <!-- Wireshark -->
            <rect x="100" y="530" width="600" height="50" rx="10" fill="#2a3548" stroke="#ffffff" />
            <text x="150" y="560" text-anchor="start" font-size="16" fill="#ffffff">Wireshark</text>
            <text x="350" y="560" text-anchor="start" font-size="12" fill="#ffffff">Captures and analyzes network packets in detail</text>
          </svg>
        </div>

        <h3>Ping</h3>
        <p>Ping is one of the most basic and useful network troubleshooting tools. It sends ICMP echo request packets to a target and waits for a response.</p>
        <p><strong>What ping tells you:</strong></p>
        <ul>
          <li>If a device is reachable on the network</li>
          <li>How long it takes for packets to reach the destination and return (latency)</li>
          <li>If there's packet loss</li>
        </ul>
        <p><strong>Example command:</strong> <code>ping google.com</code></p>

        <h3>Traceroute / Tracert</h3>
        <p>Traceroute (Linux/Mac) or Tracert (Windows) shows the path that packets take to reach a destination, including all the routers (hops) along the way.</p>
        <p><strong>What traceroute tells you:</strong></p>
        <ul>
          <li>The number of hops between you and the destination</li>
          <li>Where delays or packet loss might be occurring</li>
          <li>If routing problems exist</li>
        </ul>
        <p><strong>Example command:</strong> <code>traceroute google.com</code> or <code>tracert google.com</code></p>

        <h3>ipconfig / ifconfig</h3>
        <p>These commands display the network configuration of your computer.</p>
        <p><strong>What ipconfig/ifconfig tells you:</strong></p>
        <ul>
          <li>IP address</li>
          <li>Subnet mask</li>
          <li>Default gateway</li>
          <li>DNS servers</li>
          <li>MAC address</li>
        </ul>
        <p><strong>Example command:</strong> <code>ipconfig</code> (Windows) or <code>ifconfig</code> (Linux/Mac)</p>

        <h3>nslookup / dig</h3>
        <p>These tools query DNS servers to get information about domain names and IP addresses.</p>
        <p><strong>What nslookup/dig tells you:</strong></p>
        <ul>
          <li>IP addresses associated with a domain name</li>
          <li>Which DNS server is responding</li>
          <li>DNS record information (A, MX, CNAME, etc.)</li>
        </ul>
        <p><strong>Example command:</strong> <code>nslookup google.com</code> or <code>dig google.com</code></p>

        <h3>Wireshark</h3>
        <p>Wireshark is a powerful network protocol analyzer that captures and inspects packets in detail.</p>
        <p><strong>What Wireshark tells you:</strong></p>
        <ul>
          <li>Detailed packet information</li>
          <li>Protocol-specific analysis</li>
          <li>Traffic patterns</li>
          <li>Network errors</li>
          <li>Security issues</li>
        </ul>
      `
    }
  ]
};
