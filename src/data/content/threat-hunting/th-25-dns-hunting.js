/**
 * TH-25: DNS Threat Hunting
 * Master DNS-based threat detection and analysis techniques
 */

export const dnsHuntingContent = {
  id: 'th-25',
  title: 'DNS Threat Hunting',
  description: 'Master comprehensive DNS threat hunting including DGA detection, DNS tunneling, and advanced DNS-based attack analysis.',
  duration: '36 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Master DNS protocol analysis and threat detection',
    'Implement Domain Generation Algorithm (DGA) detection',
    'Build DNS tunneling and exfiltration detection systems',
    'Develop DNS-based threat intelligence integration',
    'Create automated DNS anomaly detection workflows',
    'Implement DNS forensics and investigation techniques',
    'Build comprehensive DNS security monitoring platforms'
  ],

  sections: [
    {
      id: 'dns-threat-analysis',
      title: 'DNS Protocol Threat Analysis',
      content: `
## DNS Threat Landscape and Attack Vectors

### **DNS Attack Categories**
\`\`\`yaml
DNS-Based Attacks:
  Malicious Domain Usage:
    - Command and Control (C2) communication
    - Malware distribution sites
    - Phishing and fraud domains
    - Cryptocurrency mining pools
    
  Domain Generation Algorithms (DGA):
    - Automated domain generation
    - Evasion of domain blocking
    - Resilient C2 infrastructure
    - Fast-flux networks
    
  DNS Tunneling:
    - Data exfiltration via DNS queries
    - Command injection through DNS
    - Covert communication channels
    - Bypass network restrictions
    
  DNS Hijacking:
    - DNS cache poisoning
    - Authoritative server compromise
    - BGP hijacking affecting DNS
    - Registrar account takeover
    
  DNS Amplification:
    - DDoS amplification attacks
    - Reflection attacks
    - Open resolver abuse
    - Bandwidth exhaustion
    
  DNS Reconnaissance:
    - Zone transfers
    - Subdomain enumeration
    - DNS walking
    - Information gathering

DNS Security Challenges:
  Protocol Limitations:
    - Lack of built-in encryption (pre-DoH/DoT)
    - Limited authentication mechanisms
    - Caching behavior exploitation
    - Recursive resolver vulnerabilities
    
  Infrastructure Complexity:
    - Multiple resolver layers
    - Caching hierarchies
    - Authoritative server distribution
    - Third-party DNS services
\`\`\`

### **DNS Hunting Framework**
\`\`\`python
import re
import math
import socket
import struct
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import dns.resolver
import dns.query
import dns.zone

class DNSThreatHunter:
    def __init__(self):
        self.dga_models = {}
        self.known_malicious_domains = set()
        self.legitimate_domains = set()
        self.dns_baselines = {}
        
    def analyze_dns_traffic(self, dns_logs):
        """Comprehensive DNS traffic analysis"""
        analysis_results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_queries': len(dns_logs),
            'threat_indicators': [],
            'dga_detections': [],
            'tunneling_indicators': [],
            'anomaly_scores': {}
        }
        
        # Detect DGA domains
        dga_detections = self._detect_dga_domains(dns_logs)
        analysis_results['dga_detections'] = dga_detections
        
        # Detect DNS tunneling
        tunneling_indicators = self._detect_dns_tunneling(dns_logs)
        analysis_results['tunneling_indicators'] = tunneling_indicators
        
        # Detect malicious domains
        malicious_domains = self._detect_malicious_domains(dns_logs)
        analysis_results['threat_indicators'].extend(malicious_domains)
        
        # Detect DNS anomalies
        anomalies = self._detect_dns_anomalies(dns_logs)
        analysis_results['threat_indicators'].extend(anomalies)
        
        # Calculate overall risk scores
        analysis_results['anomaly_scores'] = self._calculate_dns_risk_scores(
            analysis_results
        )
        
        return analysis_results
    
    def _detect_dga_domains(self, dns_logs):
        """Detect Domain Generation Algorithm (DGA) domains"""
        dga_detections = []
        
        for log_entry in dns_logs:
            domain = log_entry.get('query_name', '').lower()
            
            if domain and self._is_potential_dga_domain(domain):
                dga_score = self._calculate_dga_score(domain)
                
                if dga_score > 0.7:  # High confidence threshold
                    dga_detections.append({
                        'domain': domain,
                        'dga_score': dga_score,
                        'timestamp': log_entry.get('timestamp'),
                        'source_ip': log_entry.get('source_ip'),
                        'query_type': log_entry.get('query_type'),
                        'indicators': self._get_dga_indicators(domain)
                    })
        
        return dga_detections
    
    def _is_potential_dga_domain(self, domain):
        """Check if domain shows DGA characteristics"""
        if not domain or '.' not in domain:
            return False
        
        domain_parts = domain.split('.')
        if len(domain_parts) < 2:
            return False
        
        subdomain = domain_parts[0]
        
        # Basic DGA indicators
        if len(subdomain) < 6 or len(subdomain) > 50:
            return False
        
        # Check for high entropy
        entropy = self._calculate_entropy(subdomain)
        if entropy < 3.0:
            return False
        
        # Check for consonant/vowel ratio
        consonant_ratio = self._calculate_consonant_ratio(subdomain)
        if consonant_ratio < 0.3 or consonant_ratio > 0.9:
            return True
        
        # Check for dictionary words
        if self._contains_dictionary_words(subdomain):
            return False
        
        return True
    
    def _calculate_dga_score(self, domain):
        """Calculate DGA probability score"""
        if not domain:
            return 0
        
        domain_parts = domain.split('.')
        subdomain = domain_parts[0]
        
        score = 0
        
        # Entropy score (0-0.3)
        entropy = self._calculate_entropy(subdomain)
        entropy_score = min(entropy / 5.0, 0.3)
        score += entropy_score
        
        # Length score (0-0.2)
        length_score = 0
        if 8 <= len(subdomain) <= 15:
            length_score = 0.2
        elif 6 <= len(subdomain) <= 20:
            length_score = 0.1
        score += length_score
        
        # Character distribution score (0-0.2)
        char_dist_score = self._calculate_character_distribution_score(subdomain)
        score += char_dist_score
        
        # Consonant/vowel ratio score (0-0.15)
        consonant_ratio = self._calculate_consonant_ratio(subdomain)
        if consonant_ratio > 0.8 or consonant_ratio < 0.2:
            score += 0.15
        elif consonant_ratio > 0.7 or consonant_ratio < 0.3:
            score += 0.1
        
        # Dictionary word penalty (-0.3 to 0)
        if self._contains_dictionary_words(subdomain):
            score -= 0.3
        
        # TLD reputation score (0-0.15)
        tld = domain_parts[-1] if len(domain_parts) > 1 else ''
        if tld in ['tk', 'ml', 'ga', 'cf', 'top']:
            score += 0.15
        elif tld in ['com', 'org', 'net', 'edu', 'gov']:
            score += 0.05
        
        return max(0, min(1, score))
    
    def _calculate_entropy(self, string):
        """Calculate Shannon entropy of string"""
        if not string:
            return 0
        
        # Count character frequencies
        char_counts = Counter(string.lower())
        string_length = len(string)
        
        # Calculate entropy
        entropy = 0
        for count in char_counts.values():
            probability = count / string_length
            entropy -= probability * math.log2(probability)
        
        return entropy
    
    def _calculate_consonant_ratio(self, string):
        """Calculate ratio of consonants to total letters"""
        if not string:
            return 0
        
        vowels = set('aeiou')
        letters = [c for c in string.lower() if c.isalpha()]
        
        if not letters:
            return 0
        
        consonants = [c for c in letters if c not in vowels]
        return len(consonants) / len(letters)
    
    def _detect_dns_tunneling(self, dns_logs):
        """Detect DNS tunneling activities"""
        tunneling_indicators = []
        
        # Group queries by source IP and domain
        queries_by_source = defaultdict(list)
        
        for log_entry in dns_logs:
            source_ip = log_entry.get('source_ip')
            queries_by_source[source_ip].append(log_entry)
        
        # Analyze each source for tunneling patterns
        for source_ip, queries in queries_by_source.items():
            tunneling_score = self._calculate_tunneling_score(queries)
            
            if tunneling_score > 0.6:
                tunneling_indicators.append({
                    'source_ip': source_ip,
                    'tunneling_score': tunneling_score,
                    'query_count': len(queries),
                    'unique_domains': len(set(q.get('query_name') for q in queries)),
                    'indicators': self._get_tunneling_indicators(queries),
                    'time_span': self._calculate_time_span(queries)
                })
        
        return tunneling_indicators
    
    def _calculate_tunneling_score(self, queries):
        """Calculate DNS tunneling probability score"""
        if len(queries) < 10:
            return 0
        
        score = 0
        
        # High query frequency score (0-0.3)
        time_span = self._calculate_time_span_seconds(queries)
        if time_span > 0:
            query_rate = len(queries) / time_span
            if query_rate > 10:  # More than 10 queries per second
                score += 0.3
            elif query_rate > 5:
                score += 0.2
            elif query_rate > 2:
                score += 0.1
        
        # Unusual query types score (0-0.2)
        query_types = [q.get('query_type') for q in queries]
        unusual_types = ['TXT', 'NULL', 'CNAME', 'MX']
        unusual_count = sum(1 for qt in query_types if qt in unusual_types)
        if unusual_count > len(queries) * 0.5:
            score += 0.2
        elif unusual_count > len(queries) * 0.3:
            score += 0.1
        
        # Large response size score (0-0.2)
        large_responses = [q for q in queries if q.get('response_size', 0) > 512]
        if len(large_responses) > len(queries) * 0.3:
            score += 0.2
        elif len(large_responses) > len(queries) * 0.1:
            score += 0.1
        
        # Subdomain pattern score (0-0.3)
        domains = [q.get('query_name', '') for q in queries]
        if self._has_tunneling_subdomain_pattern(domains):
            score += 0.3
        
        return min(1, score)
    
    def _has_tunneling_subdomain_pattern(self, domains):
        """Check for DNS tunneling subdomain patterns"""
        if not domains:
            return False
        
        # Look for base64-like patterns in subdomains
        base64_pattern = re.compile(r'^[A-Za-z0-9+/=]+$')
        hex_pattern = re.compile(r'^[A-Fa-f0-9]+$')
        
        suspicious_count = 0
        
        for domain in domains:
            if '.' in domain:
                subdomain = domain.split('.')[0]
                
                # Check for base64-like encoding
                if len(subdomain) > 10 and base64_pattern.match(subdomain):
                    suspicious_count += 1
                
                # Check for hex encoding
                elif len(subdomain) > 8 and len(subdomain) % 2 == 0 and hex_pattern.match(subdomain):
                    suspicious_count += 1
                
                # Check for unusual length
                elif len(subdomain) > 30:
                    suspicious_count += 1
        
        # If more than 30% of domains are suspicious
        return suspicious_count > len(domains) * 0.3

### **Advanced DNS Analysis**
\`\`\`python
class AdvancedDNSAnalyzer:
    def __init__(self):
        self.passive_dns_sources = {}
        self.threat_intelligence_feeds = {}
        
    def perform_passive_dns_analysis(self, domain):
        """Perform passive DNS analysis for domain"""
        analysis_result = {
            'domain': domain,
            'analysis_timestamp': datetime.now().isoformat(),
            'historical_resolutions': [],
            'associated_domains': [],
            'infrastructure_analysis': {},
            'threat_assessment': {}
        }
        
        # Get historical DNS resolutions
        historical_data = self._get_historical_dns_data(domain)
        analysis_result['historical_resolutions'] = historical_data
        
        # Find associated domains
        associated = self._find_associated_domains(domain, historical_data)
        analysis_result['associated_domains'] = associated
        
        # Analyze infrastructure
        infrastructure = self._analyze_dns_infrastructure(domain, historical_data)
        analysis_result['infrastructure_analysis'] = infrastructure
        
        # Assess threat level
        threat_assessment = self._assess_domain_threat_level(
            domain, historical_data, associated, infrastructure
        )
        analysis_result['threat_assessment'] = threat_assessment
        
        return analysis_result
    
    def _get_historical_dns_data(self, domain):
        """Get historical DNS resolution data"""
        historical_data = []
        
        try:
            # Simulate passive DNS lookup
            # In real implementation, this would query passive DNS services
            current_resolution = socket.gethostbyname(domain)
            
            historical_data.append({
                'ip_address': current_resolution,
                'first_seen': datetime.now().isoformat(),
                'last_seen': datetime.now().isoformat(),
                'record_type': 'A'
            })
            
        except socket.gaierror:
            # Domain doesn't resolve
            pass
        
        return historical_data
    
    def build_dns_threat_intelligence(self, dns_logs, threat_feeds):
        """Build DNS-based threat intelligence"""
        threat_intel = {
            'creation_timestamp': datetime.now().isoformat(),
            'malicious_domains': set(),
            'suspicious_patterns': [],
            'c2_infrastructure': {},
            'threat_campaigns': []
        }
        
        # Analyze DNS logs for threat patterns
        for log_entry in dns_logs:
            domain = log_entry.get('query_name', '').lower()
            
            # Check against threat feeds
            if self._is_known_malicious_domain(domain, threat_feeds):
                threat_intel['malicious_domains'].add(domain)
            
            # Identify suspicious patterns
            if self._is_suspicious_dns_pattern(log_entry):
                threat_intel['suspicious_patterns'].append({
                    'pattern': self._extract_pattern(log_entry),
                    'domain': domain,
                    'timestamp': log_entry.get('timestamp'),
                    'source_ip': log_entry.get('source_ip')
                })
        
        # Correlate with known threat campaigns
        campaigns = self._correlate_threat_campaigns(threat_intel)
        threat_intel['threat_campaigns'] = campaigns
        
        return threat_intel
\`\`\`
      `,
      activities: [
        'Analyze DNS protocol threats and attack vectors',
        'Build DGA detection algorithms and models',
        'Implement DNS tunneling detection systems',
        'Create passive DNS analysis framework'
      ]
    },

    {
      id: 'dns-monitoring-response',
      title: 'DNS Monitoring and Automated Response',
      content: `
## DNS Security Monitoring and Response

### **Real-time DNS Monitoring System**
\`\`\`python
class DNSMonitoringSystem:
    def __init__(self):
        self.monitoring_rules = {}
        self.alert_thresholds = {}
        self.response_actions = {}
        self.dns_cache = {}

    def setup_dns_monitoring(self, monitoring_config):
        """Setup comprehensive DNS monitoring"""
        monitoring_system = {
            'config_timestamp': datetime.now().isoformat(),
            'monitoring_rules': [],
            'alert_channels': [],
            'response_workflows': []
        }

        # Configure DGA detection rules
        dga_rules = self._create_dga_monitoring_rules()
        monitoring_system['monitoring_rules'].extend(dga_rules)

        # Configure tunneling detection rules
        tunneling_rules = self._create_tunneling_monitoring_rules()
        monitoring_system['monitoring_rules'].extend(tunneling_rules)

        # Configure malicious domain detection
        malicious_domain_rules = self._create_malicious_domain_rules()
        monitoring_system['monitoring_rules'].extend(malicious_domain_rules)

        # Setup alert channels
        alert_channels = self._setup_alert_channels(monitoring_config)
        monitoring_system['alert_channels'] = alert_channels

        # Configure automated responses
        response_workflows = self._setup_response_workflows(monitoring_config)
        monitoring_system['response_workflows'] = response_workflows

        return monitoring_system

    def _create_dga_monitoring_rules(self):
        """Create DGA detection monitoring rules"""
        rules = [
            {
                'rule_id': 'dga_high_entropy',
                'name': 'High Entropy Domain Detection',
                'description': 'Detect domains with high entropy indicating DGA',
                'condition': 'entropy > 4.0 AND length > 8',
                'severity': 'medium',
                'action': 'alert_and_log'
            },
            {
                'rule_id': 'dga_consonant_ratio',
                'name': 'Unusual Consonant Ratio',
                'description': 'Detect domains with unusual consonant/vowel ratios',
                'condition': 'consonant_ratio > 0.8 OR consonant_ratio < 0.2',
                'severity': 'low',
                'action': 'log_only'
            },
            {
                'rule_id': 'dga_suspicious_tld',
                'name': 'Suspicious TLD Usage',
                'description': 'Detect usage of suspicious top-level domains',
                'condition': 'tld IN ["tk", "ml", "ga", "cf"] AND entropy > 3.5',
                'severity': 'high',
                'action': 'block_and_alert'
            }
        ]

        return rules

    def _create_tunneling_monitoring_rules(self):
        """Create DNS tunneling detection rules"""
        rules = [
            {
                'rule_id': 'dns_high_frequency',
                'name': 'High Frequency DNS Queries',
                'description': 'Detect unusually high frequency of DNS queries',
                'condition': 'query_rate > 10 per_second FROM same_source',
                'severity': 'high',
                'action': 'alert_and_investigate'
            },
            {
                'rule_id': 'dns_large_responses',
                'name': 'Large DNS Response Sizes',
                'description': 'Detect unusually large DNS responses',
                'condition': 'response_size > 1024 bytes',
                'severity': 'medium',
                'action': 'alert_and_log'
            },
            {
                'rule_id': 'dns_unusual_types',
                'name': 'Unusual Query Types',
                'description': 'Detect unusual DNS query types',
                'condition': 'query_type IN ["TXT", "NULL"] AND frequency > 50%',
                'severity': 'high',
                'action': 'block_and_alert'
            }
        ]

        return rules

    def process_dns_event(self, dns_event):
        """Process single DNS event through monitoring rules"""
        event_results = {
            'event_id': dns_event.get('event_id'),
            'processing_timestamp': datetime.now().isoformat(),
            'rule_matches': [],
            'alerts_generated': [],
            'actions_taken': []
        }

        # Check event against all monitoring rules
        for rule in self.monitoring_rules.values():
            if self._evaluate_rule_condition(rule, dns_event):
                match_result = {
                    'rule_id': rule['rule_id'],
                    'rule_name': rule['name'],
                    'severity': rule['severity'],
                    'match_timestamp': datetime.now().isoformat()
                }
                event_results['rule_matches'].append(match_result)

                # Generate alert if required
                if rule['action'] in ['alert_and_log', 'alert_and_investigate', 'block_and_alert']:
                    alert = self._generate_dns_alert(rule, dns_event, match_result)
                    event_results['alerts_generated'].append(alert)

                # Take automated action if required
                if rule['action'] in ['block_and_alert', 'auto_remediate']:
                    action_result = self._execute_automated_action(rule, dns_event)
                    event_results['actions_taken'].append(action_result)

        return event_results

    def _evaluate_rule_condition(self, rule, dns_event):
        """Evaluate if DNS event matches rule condition"""
        condition = rule['condition']

        # Extract event properties
        domain = dns_event.get('query_name', '').lower()
        query_type = dns_event.get('query_type', '')
        response_size = dns_event.get('response_size', 0)
        source_ip = dns_event.get('source_ip', '')

        # Calculate domain properties
        if domain:
            entropy = self._calculate_entropy(domain.split('.')[0])
            consonant_ratio = self._calculate_consonant_ratio(domain.split('.')[0])
            tld = domain.split('.')[-1] if '.' in domain else ''
            length = len(domain.split('.')[0])
        else:
            entropy = consonant_ratio = length = 0
            tld = ''

        # Simple condition evaluation (in production, use proper parser)
        try:
            # Replace condition variables with actual values
            condition = condition.replace('entropy', str(entropy))
            condition = condition.replace('consonant_ratio', str(consonant_ratio))
            condition = condition.replace('length', str(length))
            condition = condition.replace('response_size', str(response_size))
            condition = condition.replace('query_type', f'"{query_type}"')
            condition = condition.replace('tld', f'"{tld}"')

            # Handle IN operator
            condition = re.sub(r'"([^"]+)" IN \\[([^\\]]+)\\]',
                             lambda m: str(m.group(1) in [x.strip(' "') for x in m.group(2).split(',')]),
                             condition)

            # Evaluate condition (simplified - use proper expression parser in production)
            return eval(condition.replace('AND', ' and ').replace('OR', ' or '))

        except:
            return False

    def _generate_dns_alert(self, rule, dns_event, match_result):
        """Generate DNS security alert"""
        alert = {
            'alert_id': f"dns_alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'alert_timestamp': datetime.now().isoformat(),
            'rule_id': rule['rule_id'],
            'rule_name': rule['name'],
            'severity': rule['severity'],
            'event_details': {
                'domain': dns_event.get('query_name'),
                'source_ip': dns_event.get('source_ip'),
                'query_type': dns_event.get('query_type'),
                'response_size': dns_event.get('response_size'),
                'timestamp': dns_event.get('timestamp')
            },
            'threat_indicators': self._extract_threat_indicators(dns_event),
            'recommended_actions': self._get_recommended_actions(rule, dns_event)
        }

        return alert

    def implement_dns_sinkhole(self, malicious_domains):
        """Implement DNS sinkhole for malicious domains"""
        sinkhole_config = {
            'implementation_timestamp': datetime.now().isoformat(),
            'sinkhole_ip': '127.0.0.1',  # Sinkhole IP address
            'blocked_domains': [],
            'sinkhole_rules': []
        }

        for domain in malicious_domains:
            # Create sinkhole rule
            rule = {
                'domain': domain,
                'action': 'redirect',
                'target_ip': sinkhole_config['sinkhole_ip'],
                'log_requests': True,
                'alert_on_access': True
            }

            sinkhole_config['sinkhole_rules'].append(rule)
            sinkhole_config['blocked_domains'].append(domain)

        return sinkhole_config

    def create_dns_hunting_dashboard(self, dns_metrics):
        """Create DNS threat hunting dashboard"""
        dashboard_config = {
            'dashboard_id': 'dns_threat_hunting',
            'title': 'DNS Threat Hunting Dashboard',
            'refresh_interval': 60,  # seconds
            'panels': []
        }

        # DGA Detection Panel
        dga_panel = {
            'panel_id': 'dga_detection',
            'title': 'DGA Domain Detection',
            'type': 'time_series',
            'metrics': [
                'dga_domains_detected',
                'dga_confidence_scores',
                'dga_families_identified'
            ],
            'visualization': 'line_chart'
        }
        dashboard_config['panels'].append(dga_panel)

        # DNS Tunneling Panel
        tunneling_panel = {
            'panel_id': 'dns_tunneling',
            'title': 'DNS Tunneling Detection',
            'type': 'time_series',
            'metrics': [
                'tunneling_sessions_detected',
                'data_volume_tunneled',
                'tunneling_sources'
            ],
            'visualization': 'area_chart'
        }
        dashboard_config['panels'].append(tunneling_panel)

        # Top Malicious Domains Panel
        top_domains_panel = {
            'panel_id': 'top_malicious_domains',
            'title': 'Top Malicious Domains',
            'type': 'table',
            'metrics': [
                'domain_name',
                'threat_score',
                'query_count',
                'unique_sources'
            ],
            'sort_by': 'threat_score',
            'limit': 20
        }
        dashboard_config['panels'].append(top_domains_panel)

        # DNS Query Volume Panel
        volume_panel = {
            'panel_id': 'dns_query_volume',
            'title': 'DNS Query Volume Analysis',
            'type': 'histogram',
            'metrics': [
                'total_queries',
                'unique_domains',
                'query_types_distribution'
            ],
            'visualization': 'bar_chart'
        }
        dashboard_config['panels'].append(volume_panel)

        return dashboard_config
\`\`\`
      `,
      activities: [
        'Build real-time DNS monitoring system',
        'Implement automated DNS threat response',
        'Create DNS sinkhole and blocking mechanisms',
        'Develop comprehensive DNS hunting dashboard'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'DGA Detection Laboratory',
      description: 'Build and test Domain Generation Algorithm detection system',
      tasks: [
        'Collect DGA domain samples from multiple malware families',
        'Build machine learning DGA detection model',
        'Test detection accuracy against known DGA families',
        'Implement real-time DGA monitoring system'
      ]
    },
    {
      title: 'DNS Tunneling Investigation',
      description: 'Investigate and detect DNS tunneling activities',
      tasks: [
        'Set up DNS tunneling tools and techniques',
        'Analyze DNS tunneling traffic patterns',
        'Build detection rules for various tunneling methods',
        'Create automated tunneling response workflows'
      ]
    },
    {
      title: 'Enterprise DNS Security Platform',
      description: 'Design comprehensive DNS security monitoring solution',
      tasks: [
        'Deploy DNS monitoring infrastructure',
        'Implement threat intelligence integration',
        'Create automated response and blocking systems',
        'Build executive DNS security dashboard'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'DNS Threat Hunting Mastery',
      description: 'Demonstrate advanced DNS threat detection and analysis capabilities'
    },
    {
      type: 'project',
      title: 'Enterprise DNS Security Solution',
      description: 'Design and implement comprehensive DNS security monitoring and response system'
    }
  ],

  resources: [
    'DNS Protocol Security Analysis',
    'DGA Detection Algorithms and Models',
    'DNS Tunneling Detection Techniques',
    'Passive DNS Analysis Tools',
    'DNS Threat Intelligence Sources',
    'DNS Security Monitoring Best Practices'
  ]
};
