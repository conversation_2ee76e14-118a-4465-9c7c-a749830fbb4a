/**
 * TH-19: Insider Threat Hunting
 * Master detection and analysis of insider threats and malicious user behavior
 */

export const insiderThreatHuntingContent = {
  id: 'th-19',
  title: 'Insider Threat Hunting',
  description: 'Master comprehensive insider threat detection using behavioral analytics, privilege abuse detection, and advanced user monitoring techniques.',
  duration: '46 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Master User and Entity Behavior Analytics (UEBA) techniques',
    'Implement privilege abuse and escalation detection',
    'Build data theft and exfiltration detection systems',
    'Develop psychological and behavioral indicator analysis',
    'Create automated insider threat detection workflows',
    'Implement investigation and case management for insider threats',
    'Build comprehensive insider threat prevention programs'
  ],

  sections: [
    {
      id: 'ueba-fundamentals',
      title: 'User and Entity Behavior Analytics (UEBA)',
      content: `
## Advanced UEBA Implementation

### **Behavioral Baseline Establishment**
\`\`\`python
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from collections import defaultdict
import json

class UEBAEngine:
    def __init__(self):
        self.user_baselines = {}
        self.entity_baselines = {}
        self.anomaly_detectors = {}
        self.risk_scores = {}
        
    def establish_user_baseline(self, user_id, historical_data, days=30):
        """Establish behavioral baseline for user"""
        
        # Filter data for the user and time period
        cutoff_date = datetime.now() - timedelta(days=days)
        user_data = [
            event for event in historical_data 
            if event.get('user_id') == user_id and 
            datetime.fromisoformat(event.get('timestamp', '')) >= cutoff_date
        ]
        
        if len(user_data) < 100:  # Minimum data points required
            return None
        
        # Extract behavioral features
        features = self._extract_user_features(user_data)
        
        # Calculate baseline statistics
        baseline = {
            'user_id': user_id,
            'baseline_period': f"{days} days",
            'data_points': len(user_data),
            'features': {
                'login_patterns': self._analyze_login_patterns(user_data),
                'access_patterns': self._analyze_access_patterns(user_data),
                'data_usage': self._analyze_data_usage(user_data),
                'application_usage': self._analyze_application_usage(user_data),
                'network_behavior': self._analyze_network_behavior(user_data)
            },
            'risk_factors': self._identify_baseline_risk_factors(features),
            'baseline_timestamp': datetime.now().isoformat()
        }
        
        self.user_baselines[user_id] = baseline
        
        # Train anomaly detection model
        self._train_user_anomaly_detector(user_id, features)
        
        return baseline
    
    def _extract_user_features(self, user_data):
        """Extract behavioral features from user data"""
        features = []
        
        # Group events by day
        daily_events = defaultdict(list)
        for event in user_data:
            date = datetime.fromisoformat(event['timestamp']).date()
            daily_events[date].append(event)
        
        # Calculate daily features
        for date, events in daily_events.items():
            daily_features = {
                'date': date.isoformat(),
                'login_count': len([e for e in events if e.get('event_type') == 'login']),
                'file_access_count': len([e for e in events if e.get('event_type') == 'file_access']),
                'data_downloaded_mb': sum(e.get('bytes_downloaded', 0) for e in events) / (1024*1024),
                'unique_applications': len(set(e.get('application') for e in events if e.get('application'))),
                'after_hours_activity': len([
                    e for e in events 
                    if datetime.fromisoformat(e['timestamp']).hour > 18 or 
                       datetime.fromisoformat(e['timestamp']).hour < 8
                ]),
                'weekend_activity': len([
                    e for e in events 
                    if datetime.fromisoformat(e['timestamp']).weekday() >= 5
                ]),
                'failed_access_attempts': len([
                    e for e in events 
                    if e.get('event_type') == 'access_denied'
                ]),
                'privilege_escalation_attempts': len([
                    e for e in events 
                    if e.get('event_type') == 'privilege_escalation'
                ])
            }
            features.append(daily_features)
        
        return features
    
    def _analyze_login_patterns(self, user_data):
        """Analyze user login patterns"""
        login_events = [e for e in user_data if e.get('event_type') == 'login']
        
        if not login_events:
            return {}
        
        # Extract login times
        login_hours = [
            datetime.fromisoformat(event['timestamp']).hour 
            for event in login_events
        ]
        
        login_days = [
            datetime.fromisoformat(event['timestamp']).weekday() 
            for event in login_events
        ]
        
        # Calculate patterns
        return {
            'avg_daily_logins': len(login_events) / 30,  # Assuming 30-day baseline
            'common_login_hours': self._get_common_values(login_hours),
            'common_login_days': self._get_common_values(login_days),
            'login_locations': self._get_common_values([
                event.get('source_ip') for event in login_events
            ]),
            'after_hours_login_percentage': len([
                h for h in login_hours if h > 18 or h < 8
            ]) / len(login_hours) * 100
        }
    
    def detect_user_anomalies(self, user_id, current_data):
        """Detect anomalies in current user behavior"""
        if user_id not in self.user_baselines:
            return {'error': 'No baseline established for user'}
        
        baseline = self.user_baselines[user_id]
        anomalies = []
        
        # Extract current features
        current_features = self._extract_user_features(current_data)
        
        if not current_features:
            return {'anomalies': []}
        
        # Compare with baseline
        for feature_set in current_features:
            # Login anomalies
            if feature_set['login_count'] > baseline['features']['login_patterns']['avg_daily_logins'] * 3:
                anomalies.append({
                    'type': 'Excessive Login Activity',
                    'current_value': feature_set['login_count'],
                    'baseline_avg': baseline['features']['login_patterns']['avg_daily_logins'],
                    'severity': 'medium',
                    'date': feature_set['date']
                })
            
            # After-hours activity anomalies
            baseline_after_hours = baseline['features']['login_patterns']['after_hours_login_percentage']
            if feature_set['after_hours_activity'] > 0 and baseline_after_hours < 10:
                anomalies.append({
                    'type': 'Unusual After-Hours Activity',
                    'current_value': feature_set['after_hours_activity'],
                    'baseline_percentage': baseline_after_hours,
                    'severity': 'high',
                    'date': feature_set['date']
                })
            
            # Data download anomalies
            if feature_set['data_downloaded_mb'] > 1000:  # 1GB threshold
                anomalies.append({
                    'type': 'Large Data Download',
                    'current_value': feature_set['data_downloaded_mb'],
                    'severity': 'high',
                    'date': feature_set['date']
                })
            
            # Failed access attempts
            if feature_set['failed_access_attempts'] > 10:
                anomalies.append({
                    'type': 'Excessive Failed Access Attempts',
                    'current_value': feature_set['failed_access_attempts'],
                    'severity': 'medium',
                    'date': feature_set['date']
                })
        
        # Use ML model for additional anomaly detection
        if user_id in self.anomaly_detectors:
            ml_anomalies = self._detect_ml_anomalies(user_id, current_features)
            anomalies.extend(ml_anomalies)
        
        return {
            'user_id': user_id,
            'analysis_timestamp': datetime.now().isoformat(),
            'anomalies': anomalies,
            'risk_score': self._calculate_user_risk_score(anomalies)
        }
    
    def _train_user_anomaly_detector(self, user_id, features):
        """Train ML model for user anomaly detection"""
        if len(features) < 20:  # Minimum samples for training
            return
        
        # Prepare feature matrix
        feature_matrix = []
        for feature_set in features:
            feature_vector = [
                feature_set['login_count'],
                feature_set['file_access_count'],
                feature_set['data_downloaded_mb'],
                feature_set['unique_applications'],
                feature_set['after_hours_activity'],
                feature_set['weekend_activity'],
                feature_set['failed_access_attempts'],
                feature_set['privilege_escalation_attempts']
            ]
            feature_matrix.append(feature_vector)
        
        # Normalize features
        scaler = StandardScaler()
        normalized_features = scaler.fit_transform(feature_matrix)
        
        # Train Isolation Forest
        detector = IsolationForest(contamination=0.1, random_state=42)
        detector.fit(normalized_features)
        
        self.anomaly_detectors[user_id] = {
            'model': detector,
            'scaler': scaler,
            'feature_names': [
                'login_count', 'file_access_count', 'data_downloaded_mb',
                'unique_applications', 'after_hours_activity', 'weekend_activity',
                'failed_access_attempts', 'privilege_escalation_attempts'
            ]
        }
    
    def _detect_ml_anomalies(self, user_id, current_features):
        """Detect anomalies using ML model"""
        detector_info = self.anomaly_detectors[user_id]
        model = detector_info['model']
        scaler = detector_info['scaler']
        
        anomalies = []
        
        for feature_set in current_features:
            feature_vector = [
                feature_set['login_count'],
                feature_set['file_access_count'],
                feature_set['data_downloaded_mb'],
                feature_set['unique_applications'],
                feature_set['after_hours_activity'],
                feature_set['weekend_activity'],
                feature_set['failed_access_attempts'],
                feature_set['privilege_escalation_attempts']
            ]
            
            # Normalize and predict
            normalized_vector = scaler.transform([feature_vector])
            anomaly_score = model.decision_function(normalized_vector)[0]
            is_anomaly = model.predict(normalized_vector)[0] == -1
            
            if is_anomaly:
                anomalies.append({
                    'type': 'ML-Detected Behavioral Anomaly',
                    'anomaly_score': anomaly_score,
                    'feature_vector': feature_vector,
                    'feature_names': detector_info['feature_names'],
                    'severity': 'high' if anomaly_score < -0.5 else 'medium',
                    'date': feature_set['date']
                })
        
        return anomalies
    
    def _calculate_user_risk_score(self, anomalies):
        """Calculate overall risk score for user"""
        if not anomalies:
            return 0
        
        severity_weights = {'low': 10, 'medium': 25, 'high': 50, 'critical': 100}
        
        total_score = sum(
            severity_weights.get(anomaly.get('severity', 'low'), 10)
            for anomaly in anomalies
        )
        
        return min(total_score, 100)  # Cap at 100

### **Privilege Abuse Detection**
\`\`\`python
class PrivilegeAbuseDetector:
    def __init__(self):
        self.privilege_baselines = {}
        self.abuse_patterns = {
            'privilege_escalation': [
                'runas', 'sudo', 'su', 'psexec', 'powershell -verb runas'
            ],
            'credential_access': [
                'mimikatz', 'lsass', 'sam', 'ntds.dit', 'secretsdump'
            ],
            'lateral_movement': [
                'wmic', 'psexec', 'winrm', 'schtasks', 'at.exe'
            ]
        }
    
    def detect_privilege_abuse(self, user_events, admin_events):
        """Detect privilege abuse patterns"""
        abuse_indicators = []
        
        # Analyze privilege escalation attempts
        escalation_indicators = self._detect_privilege_escalation(user_events)
        abuse_indicators.extend(escalation_indicators)
        
        # Analyze administrative action patterns
        admin_indicators = self._analyze_admin_actions(admin_events)
        abuse_indicators.extend(admin_indicators)
        
        # Analyze credential access attempts
        credential_indicators = self._detect_credential_access(user_events)
        abuse_indicators.extend(credential_indicators)
        
        return {
            'detection_timestamp': datetime.now().isoformat(),
            'abuse_indicators': abuse_indicators,
            'risk_assessment': self._assess_privilege_abuse_risk(abuse_indicators)
        }
    
    def _detect_privilege_escalation(self, user_events):
        """Detect privilege escalation attempts"""
        indicators = []
        
        for event in user_events:
            command_line = event.get('command_line', '').lower()
            process_name = event.get('process_name', '').lower()
            
            # Check for privilege escalation tools/commands
            for pattern in self.abuse_patterns['privilege_escalation']:
                if pattern in command_line or pattern in process_name:
                    indicators.append({
                        'type': 'Privilege Escalation Attempt',
                        'user': event.get('user'),
                        'process': event.get('process_name'),
                        'command_line': event.get('command_line'),
                        'timestamp': event.get('timestamp'),
                        'pattern_matched': pattern,
                        'severity': 'high'
                    })
            
            # Check for unusual privilege changes
            if event.get('event_type') == 'privilege_change':
                old_privileges = set(event.get('old_privileges', []))
                new_privileges = set(event.get('new_privileges', []))
                added_privileges = new_privileges - old_privileges
                
                if added_privileges:
                    indicators.append({
                        'type': 'Privilege Addition',
                        'user': event.get('user'),
                        'added_privileges': list(added_privileges),
                        'timestamp': event.get('timestamp'),
                        'severity': 'medium'
                    })
        
        return indicators
    
    def _analyze_admin_actions(self, admin_events):
        """Analyze administrative actions for abuse"""
        indicators = []
        
        # Group admin actions by user
        user_admin_actions = defaultdict(list)
        
        for event in admin_events:
            user = event.get('user')
            if user:
                user_admin_actions[user].append(event)
        
        # Analyze each user's admin activity
        for user, events in user_admin_actions.items():
            # Check for excessive admin activity
            if len(events) > 100:  # Threshold for excessive activity
                indicators.append({
                    'type': 'Excessive Administrative Activity',
                    'user': user,
                    'action_count': len(events),
                    'time_span': self._calculate_time_span(events),
                    'severity': 'medium'
                })
            
            # Check for unusual admin actions
            unusual_actions = [
                'user_creation', 'group_modification', 'policy_change',
                'audit_log_clearing', 'security_setting_change'
            ]
            
            for event in events:
                action_type = event.get('action_type')
                if action_type in unusual_actions:
                    indicators.append({
                        'type': 'Sensitive Administrative Action',
                        'user': user,
                        'action': action_type,
                        'target': event.get('target'),
                        'timestamp': event.get('timestamp'),
                        'severity': 'high'
                    })
        
        return indicators
\`\`\`
      `,
      activities: [
        'Implement UEBA baseline establishment',
        'Build behavioral anomaly detection algorithms',
        'Create privilege abuse detection system',
        'Develop user risk scoring framework'
      ]
    },

    {
      id: 'data-theft-detection',
      title: 'Data Theft and Exfiltration Detection',
      content: `
## Advanced Data Loss Prevention and Detection

### **Data Classification and Monitoring**
\`\`\`python
import re
import hashlib
from pathlib import Path

class DataTheftDetector:
    def __init__(self):
        self.data_classifiers = {
            'pii': {
                'patterns': [
                    r'\\b\\d{3}-\\d{2}-\\d{4}\\b',  # SSN
                    r'\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b',  # Credit card
                    r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b'  # Email
                ],
                'sensitivity': 'high'
            },
            'financial': {
                'patterns': [
                    r'\\b\\d{9,18}\\b',  # Account numbers
                    r'\\$[\\d,]+\\.\\d{2}',  # Currency amounts
                    r'\\b(SWIFT|IBAN|ABA)\\b'  # Financial codes
                ],
                'sensitivity': 'high'
            },
            'intellectual_property': {
                'patterns': [
                    r'\\b(confidential|proprietary|trade secret)\\b',
                    r'\\b(patent|copyright|trademark)\\b',
                    r'\\.(dwg|cad|psd|ai)$'  # Design files
                ],
                'sensitivity': 'medium'
            }
        }

        self.exfiltration_indicators = []

    def monitor_file_access(self, file_events):
        """Monitor file access for potential data theft"""
        theft_indicators = []

        # Group events by user
        user_file_access = defaultdict(list)

        for event in file_events:
            user = event.get('user')
            if user:
                user_file_access[user].append(event)

        # Analyze each user's file access patterns
        for user, events in user_file_access.items():
            # Check for bulk file access
            bulk_access = self._detect_bulk_file_access(user, events)
            if bulk_access:
                theft_indicators.extend(bulk_access)

            # Check for sensitive data access
            sensitive_access = self._detect_sensitive_data_access(user, events)
            if sensitive_access:
                theft_indicators.extend(sensitive_access)

            # Check for unusual file operations
            unusual_ops = self._detect_unusual_file_operations(user, events)
            if unusual_ops:
                theft_indicators.extend(unusual_ops)

        return theft_indicators

    def _detect_bulk_file_access(self, user, events):
        """Detect bulk file access patterns"""
        indicators = []

        # Count file accesses in time windows
        time_windows = defaultdict(int)

        for event in events:
            timestamp = datetime.fromisoformat(event['timestamp'])
            # 1-hour time windows
            window = timestamp.replace(minute=0, second=0, microsecond=0)
            time_windows[window] += 1

        # Check for excessive access in any window
        for window, count in time_windows.items():
            if count > 100:  # Threshold for bulk access
                indicators.append({
                    'type': 'Bulk File Access',
                    'user': user,
                    'time_window': window.isoformat(),
                    'file_count': count,
                    'severity': 'high',
                    'description': f'User accessed {count} files in 1-hour window'
                })

        return indicators

    def _detect_sensitive_data_access(self, user, events):
        """Detect access to sensitive data"""
        indicators = []

        for event in events:
            file_path = event.get('file_path', '')
            file_content = event.get('file_content', '')

            # Classify file content
            classification = self._classify_data(file_content)

            if classification['sensitivity'] == 'high':
                indicators.append({
                    'type': 'Sensitive Data Access',
                    'user': user,
                    'file_path': file_path,
                    'data_types': classification['types'],
                    'timestamp': event.get('timestamp'),
                    'severity': 'high',
                    'operation': event.get('operation')
                })

        return indicators

    def _classify_data(self, content):
        """Classify data content for sensitivity"""
        if not content:
            return {'sensitivity': 'low', 'types': []}

        detected_types = []
        max_sensitivity = 'low'

        for data_type, classifier in self.data_classifiers.items():
            for pattern in classifier['patterns']:
                if re.search(pattern, content, re.IGNORECASE):
                    detected_types.append(data_type)
                    if classifier['sensitivity'] == 'high':
                        max_sensitivity = 'high'
                    elif classifier['sensitivity'] == 'medium' and max_sensitivity != 'high':
                        max_sensitivity = 'medium'

        return {
            'sensitivity': max_sensitivity,
            'types': detected_types
        }

    def detect_data_exfiltration(self, network_events, file_events):
        """Detect data exfiltration activities"""
        exfiltration_indicators = []

        # Correlate file access with network activity
        correlations = self._correlate_file_and_network_activity(file_events, network_events)

        for correlation in correlations:
            if correlation['risk_score'] > 70:
                exfiltration_indicators.append({
                    'type': 'Potential Data Exfiltration',
                    'user': correlation['user'],
                    'files_accessed': correlation['file_count'],
                    'data_transferred': correlation['bytes_transferred'],
                    'destination': correlation['destination'],
                    'time_correlation': correlation['time_correlation'],
                    'risk_score': correlation['risk_score'],
                    'severity': 'critical'
                })

        return exfiltration_indicators

    def _correlate_file_and_network_activity(self, file_events, network_events):
        """Correlate file access with network transfers"""
        correlations = []

        # Group events by user and time
        user_activities = defaultdict(lambda: {'files': [], 'network': []})

        for event in file_events:
            user = event.get('user')
            if user:
                user_activities[user]['files'].append(event)

        for event in network_events:
            user = event.get('user')
            if user:
                user_activities[user]['network'].append(event)

        # Analyze correlations for each user
        for user, activities in user_activities.items():
            file_events = activities['files']
            network_events = activities['network']

            # Look for temporal correlations
            for net_event in network_events:
                net_time = datetime.fromisoformat(net_event['timestamp'])

                # Find file events within 1 hour before network event
                correlated_files = [
                    f_event for f_event in file_events
                    if abs((datetime.fromisoformat(f_event['timestamp']) - net_time).total_seconds()) < 3600
                ]

                if correlated_files and net_event.get('bytes_transferred', 0) > 10 * 1024 * 1024:  # 10MB
                    risk_score = self._calculate_exfiltration_risk(correlated_files, net_event)

                    correlations.append({
                        'user': user,
                        'file_count': len(correlated_files),
                        'bytes_transferred': net_event.get('bytes_transferred'),
                        'destination': net_event.get('destination_ip'),
                        'time_correlation': len(correlated_files),
                        'risk_score': risk_score
                    })

        return correlations

### **Psychological and Behavioral Indicators**
\`\`\`python
class PsychologicalIndicatorAnalyzer:
    def __init__(self):
        self.risk_indicators = {
            'access_patterns': {
                'after_hours_increase': 30,
                'weekend_activity_increase': 25,
                'holiday_activity': 40,
                'unusual_location_access': 35
            },
            'behavioral_changes': {
                'sudden_privilege_requests': 45,
                'policy_violation_increase': 30,
                'security_tool_evasion': 50,
                'unusual_application_usage': 25
            },
            'data_patterns': {
                'bulk_data_access': 40,
                'sensitive_data_exploration': 45,
                'data_staging_activities': 50,
                'unusual_download_patterns': 35
            }
        }

    def analyze_psychological_indicators(self, user_profile, recent_activities):
        """Analyze psychological and behavioral risk indicators"""
        indicators = []

        # Analyze access pattern changes
        access_indicators = self._analyze_access_pattern_changes(user_profile, recent_activities)
        indicators.extend(access_indicators)

        # Analyze behavioral changes
        behavior_indicators = self._analyze_behavioral_changes(user_profile, recent_activities)
        indicators.extend(behavior_indicators)

        # Analyze data access patterns
        data_indicators = self._analyze_data_access_patterns(recent_activities)
        indicators.extend(data_indicators)

        # Calculate overall psychological risk score
        risk_score = self._calculate_psychological_risk_score(indicators)

        return {
            'user_id': user_profile.get('user_id'),
            'analysis_timestamp': datetime.now().isoformat(),
            'psychological_indicators': indicators,
            'risk_score': risk_score,
            'risk_level': self._get_risk_level(risk_score),
            'recommendations': self._generate_recommendations(indicators, risk_score)
        }

    def _analyze_access_pattern_changes(self, user_profile, recent_activities):
        """Analyze changes in access patterns"""
        indicators = []

        baseline = user_profile.get('baseline_behavior', {})

        # After-hours activity analysis
        recent_after_hours = len([
            activity for activity in recent_activities
            if datetime.fromisoformat(activity['timestamp']).hour > 18 or
               datetime.fromisoformat(activity['timestamp']).hour < 8
        ])

        baseline_after_hours = baseline.get('avg_after_hours_activity', 0)

        if recent_after_hours > baseline_after_hours * 2:
            indicators.append({
                'type': 'Increased After-Hours Activity',
                'current_value': recent_after_hours,
                'baseline_value': baseline_after_hours,
                'risk_score': self.risk_indicators['access_patterns']['after_hours_increase'],
                'description': 'Significant increase in after-hours system access'
            })

        # Location-based access analysis
        recent_locations = set(activity.get('source_ip') for activity in recent_activities)
        baseline_locations = set(baseline.get('common_locations', []))

        new_locations = recent_locations - baseline_locations
        if len(new_locations) > 2:
            indicators.append({
                'type': 'Access from New Locations',
                'new_locations': list(new_locations),
                'risk_score': self.risk_indicators['access_patterns']['unusual_location_access'],
                'description': f'Access from {len(new_locations)} new IP addresses'
            })

        return indicators

    def _generate_recommendations(self, indicators, risk_score):
        """Generate recommendations based on analysis"""
        recommendations = []

        if risk_score > 70:
            recommendations.extend([
                'Immediate investigation required',
                'Consider temporary access restrictions',
                'Enhanced monitoring and logging',
                'Manager notification recommended'
            ])
        elif risk_score > 50:
            recommendations.extend([
                'Increased monitoring recommended',
                'Review recent access patterns',
                'Consider security awareness training'
            ])
        else:
            recommendations.append('Continue normal monitoring')

        # Specific recommendations based on indicators
        indicator_types = set(indicator['type'] for indicator in indicators)

        if 'Increased After-Hours Activity' in indicator_types:
            recommendations.append('Review business justification for after-hours access')

        if 'Access from New Locations' in indicator_types:
            recommendations.append('Verify legitimacy of new access locations')

        if 'Bulk Data Access' in indicator_types:
            recommendations.append('Review data access permissions and business need')

        return recommendations
\`\`\`
      `,
      activities: [
        'Build data classification and monitoring system',
        'Implement data exfiltration detection algorithms',
        'Create psychological indicator analysis framework',
        'Develop insider threat investigation workflows'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'UEBA Implementation Laboratory',
      description: 'Build comprehensive User and Entity Behavior Analytics system',
      tasks: [
        'Establish behavioral baselines for user population',
        'Implement machine learning anomaly detection',
        'Create real-time behavioral monitoring',
        'Build user risk scoring and alerting system'
      ]
    },
    {
      title: 'Insider Threat Investigation Simulation',
      description: 'Investigate simulated insider threat scenarios',
      tasks: [
        'Analyze suspicious user behavior patterns',
        'Correlate data access with exfiltration activities',
        'Build evidence timeline and case documentation',
        'Develop mitigation and response strategies'
      ]
    },
    {
      title: 'Enterprise Insider Threat Program',
      description: 'Design comprehensive insider threat prevention and detection program',
      tasks: [
        'Create insider threat policy and procedures',
        'Implement technical detection capabilities',
        'Build investigation and response workflows',
        'Develop training and awareness programs'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Insider Threat Detection Mastery',
      description: 'Demonstrate advanced insider threat detection and investigation capabilities'
    },
    {
      type: 'project',
      title: 'Enterprise Insider Threat Solution',
      description: 'Design and implement comprehensive insider threat detection and prevention system'
    }
  ],

  resources: [
    'UEBA Implementation Best Practices',
    'Insider Threat Psychology and Indicators',
    'Data Loss Prevention Technologies',
    'Behavioral Analytics Frameworks',
    'Insider Threat Case Studies',
    'Legal and Compliance Considerations'
  ]
};
