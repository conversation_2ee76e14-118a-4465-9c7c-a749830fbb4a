/**
 * TH-30: Sigma Rules Creation
 * Master Sigma rule development for universal SIEM detection
 */

export const sigmaRulesContent = {
  id: 'th-30',
  title: 'Sigma Rules Creation',
  description: 'Master Sigma rule development for universal SIEM detection, including rule optimization, testing, and multi-platform deployment.',
  duration: '34 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Master Sigma rule syntax and development methodology',
    'Implement advanced Sigma rule optimization techniques',
    'Build automated Sigma rule generation and testing systems',
    'Develop multi-platform SIEM deployment strategies',
    'Create comprehensive Sigma rule validation frameworks',
    'Implement Sigma rule management and versioning systems',
    'Build threat intelligence integration with Sigma rules'
  ],

  sections: [
    {
      id: 'sigma-rule-development',
      title: 'Advanced Sigma Rule Development',
      content: `
## Sigma Rule Creation and Optimization

### **Sigma Rule Structure and Best Practices**
\`\`\`yaml
# Advanced Sigma Rule Template with Best Practices

title: Advanced PowerShell Obfuscation Detection
id: 12345678-1234-5678-9012-123456789012
status: experimental
description: Detects advanced PowerShell obfuscation techniques used by attackers
references:
    - https://attack.mitre.org/techniques/T1059/001/
    - https://example.com/powershell-obfuscation-analysis
author: Threat <PERSON>
date: 2024/01/15
modified: 2024/01/15
tags:
    - attack.execution
    - attack.t1059.001
    - attack.defense_evasion
    - attack.t1027
logsource:
    category: process_creation
    product: windows
detection:
    selection_base:
        Image|endswith: '\\\\powershell.exe'
        CommandLine|contains:
            - '-EncodedCommand'
            - '-enc'
            - 'FromBase64String'
            - 'Convert::FromBase64String'
    selection_obfuscation:
        CommandLine|contains:
            - 'invoke-expression'
            - 'iex'
            - '[char]'
            - '-join'
            - 'replace'
    selection_suspicious:
        CommandLine|contains:
            - 'bypass'
            - 'unrestricted'
            - 'hidden'
            - 'windowstyle'
            - 'noprofile'
    filter_legitimate:
        CommandLine|contains:
            - 'Microsoft.PowerShell.Management'
            - 'Get-Help'
            - 'Update-Help'
    condition: selection_base and (selection_obfuscation or selection_suspicious) and not filter_legitimate
falsepositives:
    - Legitimate PowerShell scripts with base64 encoding
    - Administrative automation scripts
    - Software deployment tools
level: high
---

# Multi-Platform Sigma Rule Example
title: Suspicious Network Connection to Known Malicious IPs
id: 87654321-4321-8765-2109-876543210987
status: stable
description: Detects network connections to known malicious IP addresses
references:
    - https://attack.mitre.org/techniques/T1071/
author: Threat Hunter
date: 2024/01/15
tags:
    - attack.command_and_control
    - attack.t1071
logsource:
    category: network_connection
    product: windows
detection:
    selection:
        Initiated: 'true'
        DestinationIp|cidr:
            - '***********/24'  # Example malicious subnet
            - '10.0.0.0/8'      # Example internal network
    filter_internal:
        DestinationIp|cidr:
            - '10.0.0.0/8'
            - '**********/12'
            - '***********/16'
    condition: selection and not filter_internal
falsepositives:
    - Legitimate business connections
    - VPN connections
    - CDN services
level: medium
---

# Advanced Detection with Aggregation
title: Multiple Failed Login Attempts - Brute Force Attack
id: 11111111-2222-3333-4444-555555555555
status: stable
description: Detects potential brute force attacks through multiple failed login attempts
references:
    - https://attack.mitre.org/techniques/T1110/
author: Threat Hunter
date: 2024/01/15
tags:
    - attack.credential_access
    - attack.t1110
logsource:
    category: authentication
    product: windows
detection:
    selection:
        EventID: 4625  # Failed logon
        LogonType: 3   # Network logon
    timeframe: 5m
    condition: selection | count(TargetUserName) by SourceNetworkAddress > 10
falsepositives:
    - Password expiration scenarios
    - Service account misconfigurations
    - Network connectivity issues
level: high
\`\`\`

### **Automated Sigma Rule Generation**
\`\`\`python
import yaml
import json
import re
from datetime import datetime
from collections import defaultdict
import uuid

class SigmaRuleGenerator:
    def __init__(self):
        self.rule_templates = {}
        self.field_mappings = {}
        self.attack_mappings = {}
        
    def generate_sigma_rule(self, detection_logic, metadata):
        """Generate Sigma rule from detection logic and metadata"""
        rule_data = {
            'title': metadata.get('title', 'Generated Detection Rule'),
            'id': str(uuid.uuid4()),
            'status': metadata.get('status', 'experimental'),
            'description': metadata.get('description', 'Auto-generated detection rule'),
            'references': metadata.get('references', []),
            'author': metadata.get('author', 'Auto-Generated'),
            'date': datetime.now().strftime('%Y/%m/%d'),
            'modified': datetime.now().strftime('%Y/%m/%d'),
            'tags': self._generate_attack_tags(detection_logic),
            'logsource': self._determine_logsource(detection_logic),
            'detection': self._build_detection_logic(detection_logic),
            'falsepositives': metadata.get('falsepositives', ['Unknown']),
            'level': self._calculate_severity_level(detection_logic)
        }
        
        return self._format_sigma_rule(rule_data)
    
    def _generate_attack_tags(self, detection_logic):
        """Generate MITRE ATT&CK tags based on detection logic"""
        tags = []
        
        # Map detection patterns to ATT&CK techniques
        attack_patterns = {
            'powershell': ['attack.execution', 'attack.t1059.001'],
            'cmd': ['attack.execution', 'attack.t1059.003'],
            'wmi': ['attack.execution', 'attack.t1047'],
            'registry': ['attack.persistence', 'attack.t1547'],
            'scheduled_task': ['attack.persistence', 'attack.t1053'],
            'network_connection': ['attack.command_and_control', 'attack.t1071'],
            'file_creation': ['attack.defense_evasion', 'attack.t1027'],
            'process_injection': ['attack.defense_evasion', 'attack.t1055'],
            'credential_access': ['attack.credential_access', 'attack.t1003']
        }
        
        detection_text = str(detection_logic).lower()
        
        for pattern, attack_tags in attack_patterns.items():
            if pattern in detection_text:
                tags.extend(attack_tags)
        
        return list(set(tags))  # Remove duplicates
    
    def _determine_logsource(self, detection_logic):
        """Determine appropriate log source based on detection logic"""
        logsource_patterns = {
            'process_creation': ['image', 'commandline', 'parentimage', 'processid'],
            'network_connection': ['destinationip', 'destinationport', 'sourceip'],
            'file_event': ['targetfilename', 'creationutctime'],
            'registry_event': ['targetobject', 'details'],
            'authentication': ['logontype', 'targetusername', 'workstationname'],
            'dns_query': ['queryname', 'querytype'],
            'image_load': ['imagepath', 'signature'],
            'pipe_event': ['pipename']
        }
        
        detection_fields = self._extract_fields_from_detection(detection_logic)
        
        for category, fields in logsource_patterns.items():
            if any(field.lower() in [f.lower() for f in detection_fields] for field in fields):
                return {
                    'category': category,
                    'product': 'windows'  # Default to Windows, can be enhanced
                }
        
        # Default logsource
        return {
            'category': 'process_creation',
            'product': 'windows'
        }
    
    def _build_detection_logic(self, detection_logic):
        """Build Sigma detection logic from input"""
        detection = {}
        
        if isinstance(detection_logic, dict):
            # Direct detection logic provided
            detection = detection_logic
        elif isinstance(detection_logic, str):
            # Parse string-based detection logic
            detection = self._parse_string_detection(detection_logic)
        else:
            # Build from structured input
            detection = self._build_structured_detection(detection_logic)
        
        # Ensure condition is present
        if 'condition' not in detection:
            selection_keys = [key for key in detection.keys() if key.startswith('selection')]
            if selection_keys:
                detection['condition'] = ' and '.join(selection_keys)
            else:
                detection['condition'] = 'selection'
        
        return detection
    
    def _parse_string_detection(self, detection_string):
        """Parse string-based detection logic into Sigma format"""
        detection = {'selection': {}}
        
        # Simple parsing for common patterns
        if 'CommandLine' in detection_string:
            detection['selection']['CommandLine|contains'] = [detection_string]
        elif 'Image' in detection_string:
            detection['selection']['Image|endswith'] = [detection_string]
        else:
            detection['selection']['keywords'] = [detection_string]
        
        detection['condition'] = 'selection'
        return detection
    
    def optimize_sigma_rule(self, rule_content):
        """Optimize Sigma rule for performance and accuracy"""
        optimization_result = {
            'original_rule': rule_content,
            'optimized_rule': rule_content,
            'optimizations_applied': []
        }
        
        try:
            # Parse YAML rule
            rule_data = yaml.safe_load(rule_content)
            
            # Apply optimizations
            optimized_rule = rule_data.copy()
            
            # 1. Optimize field modifiers
            detection = optimized_rule.get('detection', {})
            for key, value in detection.items():
                if isinstance(value, dict):
                    optimized_value = self._optimize_field_modifiers(value)
                    if optimized_value != value:
                        detection[key] = optimized_value
                        optimization_result['optimizations_applied'].append(
                            f'Optimized field modifiers in {key}'
                        )
            
            # 2. Add performance filters
            if 'filter_performance' not in detection:
                performance_filter = self._generate_performance_filter(detection)
                if performance_filter:
                    detection['filter_performance'] = performance_filter
                    # Update condition
                    condition = detection.get('condition', 'selection')
                    detection['condition'] = f"{condition} and not filter_performance"
                    optimization_result['optimizations_applied'].append(
                        'Added performance filter'
                    )
            
            # 3. Optimize condition logic
            original_condition = detection.get('condition', '')
            optimized_condition = self._optimize_condition_logic(original_condition)
            if optimized_condition != original_condition:
                detection['condition'] = optimized_condition
                optimization_result['optimizations_applied'].append(
                    'Optimized condition logic'
                )
            
            # Convert back to YAML
            optimization_result['optimized_rule'] = yaml.dump(
                optimized_rule, default_flow_style=False, sort_keys=False
            )
            
        except Exception as e:
            optimization_result['error'] = str(e)
        
        return optimization_result
    
    def _optimize_field_modifiers(self, field_dict):
        """Optimize field modifiers for better performance"""
        optimized = field_dict.copy()
        
        for field, value in field_dict.items():
            # Convert contains to more specific modifiers when possible
            if field.endswith('|contains') and isinstance(value, list):
                base_field = field.replace('|contains', '')
                
                # Check if all values are file extensions
                if all(v.startswith('.') for v in value):
                    optimized[f"{base_field}|endswith"] = value
                    del optimized[field]
                
                # Check if all values are file paths
                elif all('\\\\' in v for v in value):
                    optimized[f"{base_field}|contains"] = value  # Keep as is
                
                # Check if values can use startswith
                elif all(len(v) > 10 and not any(c in v for c in ['*', '?']) for v in value):
                    # Use exact match for long, specific strings
                    optimized[base_field] = value
                    del optimized[field]
        
        return optimized
    
    def validate_sigma_rule(self, rule_content):
        """Validate Sigma rule syntax and structure"""
        validation_result = {
            'valid': False,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        try:
            # Parse YAML
            rule_data = yaml.safe_load(rule_content)
            
            # Check required fields
            required_fields = ['title', 'logsource', 'detection']
            for field in required_fields:
                if field not in rule_data:
                    validation_result['errors'].append(f"Missing required field: {field}")
            
            # Check detection structure
            if 'detection' in rule_data:
                detection = rule_data['detection']
                
                if 'condition' not in detection:
                    validation_result['errors'].append("Missing condition in detection")
                
                # Check for at least one selection
                selections = [key for key in detection.keys() if key.startswith('selection')]
                if not selections:
                    validation_result['warnings'].append("No selection blocks found")
            
            # Check metadata quality
            if 'description' not in rule_data:
                validation_result['warnings'].append("Missing description")
            
            if 'author' not in rule_data:
                validation_result['warnings'].append("Missing author")
            
            if 'tags' not in rule_data:
                validation_result['suggestions'].append("Consider adding MITRE ATT&CK tags")
            
            if 'falsepositives' not in rule_data:
                validation_result['suggestions'].append("Consider documenting false positives")
            
            # If no errors, mark as valid
            if not validation_result['errors']:
                validation_result['valid'] = True
            
        except yaml.YAMLError as e:
            validation_result['errors'].append(f"YAML syntax error: {str(e)}")
        except Exception as e:
            validation_result['errors'].append(f"Validation error: {str(e)}")
        
        return validation_result

### **Sigma Rule Testing Framework**
\`\`\`python
class SigmaRuleTester:
    def __init__(self):
        self.test_datasets = {}
        self.conversion_targets = ['splunk', 'elasticsearch', 'qradar', 'arcsight']
        
    def comprehensive_rule_testing(self, rule_file):
        """Comprehensive testing of Sigma rules"""
        test_results = {
            'rule_file': rule_file,
            'test_timestamp': datetime.now().isoformat(),
            'syntax_validation': {},
            'conversion_testing': {},
            'logic_validation': {},
            'performance_estimation': {},
            'overall_score': 0
        }
        
        # Load rule
        with open(rule_file, 'r') as f:
            rule_content = f.read()
        
        # 1. Syntax validation
        syntax_result = self._validate_syntax(rule_content)
        test_results['syntax_validation'] = syntax_result
        
        # 2. Conversion testing
        conversion_result = self._test_conversions(rule_content)
        test_results['conversion_testing'] = conversion_result
        
        # 3. Logic validation
        logic_result = self._validate_logic(rule_content)
        test_results['logic_validation'] = logic_result
        
        # 4. Performance estimation
        performance_result = self._estimate_performance(rule_content)
        test_results['performance_estimation'] = performance_result
        
        # 5. Calculate overall score
        test_results['overall_score'] = self._calculate_test_score(test_results)
        
        return test_results
    
    def _test_conversions(self, rule_content):
        """Test Sigma rule conversion to different SIEM formats"""
        conversion_results = {}
        
        for target in self.conversion_targets:
            try:
                # Simulate conversion (in real implementation, use sigmac)
                conversion_result = {
                    'target': target,
                    'success': True,
                    'converted_query': f"# Converted to {target}\\n# {rule_content[:100]}...",
                    'warnings': [],
                    'errors': []
                }
                
                # Add target-specific validation
                if target == 'splunk':
                    conversion_result = self._validate_splunk_conversion(conversion_result)
                elif target == 'elasticsearch':
                    conversion_result = self._validate_elasticsearch_conversion(conversion_result)
                
                conversion_results[target] = conversion_result
                
            except Exception as e:
                conversion_results[target] = {
                    'target': target,
                    'success': False,
                    'error': str(e)
                }
        
        return conversion_results
\`\`\`
      `,
      activities: [
        'Master Sigma rule syntax and development methodology',
        'Build automated Sigma rule generation system',
        'Implement Sigma rule optimization techniques',
        'Create comprehensive rule validation framework'
      ]
    },

    {
      id: 'sigma-deployment-management',
      title: 'Sigma Rule Deployment and Management',
      content: `
## Sigma Rule Deployment and Management

### **Multi-Platform SIEM Deployment**
\`\`\`python
import subprocess
import json
import yaml
from pathlib import Path

class SigmaDeploymentManager:
    def __init__(self):
        self.deployment_targets = {}
        self.conversion_configs = {}
        self.deployment_history = []

    def setup_deployment_pipeline(self, config):
        """Setup automated Sigma rule deployment pipeline"""
        pipeline_config = {
            'pipeline_id': f"sigma_deploy_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'creation_timestamp': datetime.now().isoformat(),
            'stages': [],
            'targets': config.get('targets', []),
            'validation_rules': config.get('validation_rules', {}),
            'rollback_strategy': config.get('rollback_strategy', 'automatic')
        }

        # Define deployment stages
        stages = [
            {
                'stage': 'validation',
                'description': 'Validate Sigma rules syntax and logic',
                'actions': ['syntax_check', 'logic_validation', 'conversion_test']
            },
            {
                'stage': 'conversion',
                'description': 'Convert Sigma rules to target SIEM formats',
                'actions': ['convert_to_splunk', 'convert_to_elasticsearch', 'convert_to_qradar']
            },
            {
                'stage': 'testing',
                'description': 'Test converted rules in staging environment',
                'actions': ['deploy_to_staging', 'run_test_cases', 'validate_results']
            },
            {
                'stage': 'production_deployment',
                'description': 'Deploy to production SIEM systems',
                'actions': ['backup_existing_rules', 'deploy_new_rules', 'verify_deployment']
            },
            {
                'stage': 'monitoring',
                'description': 'Monitor rule performance and effectiveness',
                'actions': ['track_alerts', 'measure_performance', 'collect_feedback']
            }
        ]

        pipeline_config['stages'] = stages
        return pipeline_config

    def convert_sigma_to_siem(self, sigma_rule_file, target_siem):
        """Convert Sigma rule to specific SIEM format"""
        conversion_result = {
            'source_file': sigma_rule_file,
            'target_siem': target_siem,
            'conversion_timestamp': datetime.now().isoformat(),
            'success': False,
            'converted_query': '',
            'warnings': [],
            'errors': []
        }

        try:
            # Load Sigma rule
            with open(sigma_rule_file, 'r') as f:
                sigma_rule = yaml.safe_load(f)

            # Convert based on target SIEM
            if target_siem == 'splunk':
                converted_query = self._convert_to_splunk(sigma_rule)
            elif target_siem == 'elasticsearch':
                converted_query = self._convert_to_elasticsearch(sigma_rule)
            elif target_siem == 'qradar':
                converted_query = self._convert_to_qradar(sigma_rule)
            elif target_siem == 'arcsight':
                converted_query = self._convert_to_arcsight(sigma_rule)
            else:
                raise ValueError(f"Unsupported SIEM target: {target_siem}")

            conversion_result['converted_query'] = converted_query
            conversion_result['success'] = True

        except Exception as e:
            conversion_result['errors'].append(str(e))

        return conversion_result

    def _convert_to_splunk(self, sigma_rule):
        """Convert Sigma rule to Splunk SPL"""
        detection = sigma_rule.get('detection', {})
        logsource = sigma_rule.get('logsource', {})

        # Build base search
        base_search = self._build_splunk_base_search(logsource)

        # Build search conditions
        conditions = []

        for key, value in detection.items():
            if key == 'condition':
                continue
            elif key.startswith('selection'):
                splunk_condition = self._build_splunk_condition(value)
                conditions.append(f"({splunk_condition})")
            elif key.startswith('filter'):
                splunk_filter = self._build_splunk_condition(value)
                conditions.append(f"NOT ({splunk_filter})")

        # Combine conditions
        if conditions:
            search_query = f"{base_search} | where {' AND '.join(conditions)}"
        else:
            search_query = base_search

        # Add metadata as comments
        metadata_comments = [
            f"# Title: {sigma_rule.get('title', 'Unknown')}",
            f"# Description: {sigma_rule.get('description', 'No description')}",
            f"# Author: {sigma_rule.get('author', 'Unknown')}",
            f"# Level: {sigma_rule.get('level', 'medium')}"
        ]

        return "\\n".join(metadata_comments) + "\\n" + search_query

    def _convert_to_elasticsearch(self, sigma_rule):
        """Convert Sigma rule to Elasticsearch query"""
        detection = sigma_rule.get('detection', {})

        query_body = {
            "query": {
                "bool": {
                    "must": [],
                    "must_not": []
                }
            }
        }

        # Process detection logic
        for key, value in detection.items():
            if key == 'condition':
                continue
            elif key.startswith('selection'):
                es_query = self._build_elasticsearch_query(value)
                query_body["query"]["bool"]["must"].append(es_query)
            elif key.startswith('filter'):
                es_filter = self._build_elasticsearch_query(value)
                query_body["query"]["bool"]["must_not"].append(es_filter)

        return json.dumps(query_body, indent=2)

    def _build_splunk_condition(self, condition_dict):
        """Build Splunk condition from Sigma detection"""
        conditions = []

        for field, value in condition_dict.items():
            if '|' in field:
                base_field, modifier = field.split('|', 1)
                splunk_condition = self._apply_splunk_modifier(base_field, modifier, value)
            else:
                if isinstance(value, list):
                    value_conditions = [f'{field}="{v}"' for v in value]
                    splunk_condition = f"({' OR '.join(value_conditions)})"
                else:
                    splunk_condition = f'{field}="{value}"'

            conditions.append(splunk_condition)

        return ' AND '.join(conditions)

    def _apply_splunk_modifier(self, field, modifier, value):
        """Apply Sigma field modifier to Splunk query"""
        if modifier == 'contains':
            if isinstance(value, list):
                conditions = [f'match({field}, ".*{v}.*")' for v in value]
                return f"({' OR '.join(conditions)})"
            else:
                return f'match({field}, ".*{value}.*")'

        elif modifier == 'endswith':
            if isinstance(value, list):
                conditions = [f'match({field}, ".*{v}$")' for v in value]
                return f"({' OR '.join(conditions)})"
            else:
                return f'match({field}, ".*{value}$")'

        elif modifier == 'startswith':
            if isinstance(value, list):
                conditions = [f'match({field}, "^{v}.*")' for v in value]
                return f"({' OR '.join(conditions)})"
            else:
                return f'match({field}, "^{value}.*")'

        else:
            # Default to exact match
            if isinstance(value, list):
                conditions = [f'{field}="{v}"' for v in value]
                return f"({' OR '.join(conditions)})"
            else:
                return f'{field}="{value}"'

    def deploy_rules_to_production(self, rule_files, deployment_config):
        """Deploy Sigma rules to production SIEM systems"""
        deployment_result = {
            'deployment_id': f"prod_deploy_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'deployment_timestamp': datetime.now().isoformat(),
            'rule_files': rule_files,
            'targets': deployment_config.get('targets', []),
            'deployment_status': {},
            'rollback_info': {}
        }

        # Deploy to each target
        for target in deployment_config['targets']:
            target_result = self._deploy_to_target(rule_files, target)
            deployment_result['deployment_status'][target['name']] = target_result

        # Create rollback information
        deployment_result['rollback_info'] = self._create_rollback_info(deployment_result)

        return deployment_result

    def _deploy_to_target(self, rule_files, target):
        """Deploy rules to specific SIEM target"""
        target_result = {
            'target_name': target['name'],
            'target_type': target['type'],
            'deployment_success': False,
            'deployed_rules': [],
            'failed_rules': [],
            'warnings': []
        }

        try:
            for rule_file in rule_files:
                # Convert rule to target format
                conversion_result = self.convert_sigma_to_siem(rule_file, target['type'])

                if conversion_result['success']:
                    # Deploy converted rule
                    deploy_success = self._deploy_converted_rule(
                        conversion_result['converted_query'],
                        target
                    )

                    if deploy_success:
                        target_result['deployed_rules'].append(rule_file)
                    else:
                        target_result['failed_rules'].append(rule_file)
                else:
                    target_result['failed_rules'].append(rule_file)
                    target_result['warnings'].extend(conversion_result['errors'])

            # Check overall success
            if len(target_result['deployed_rules']) > 0:
                target_result['deployment_success'] = True

        except Exception as e:
            target_result['warnings'].append(str(e))

        return target_result

### **Sigma Rule Management System**
\`\`\`python
class SigmaRuleManager:
    def __init__(self):
        self.rule_repository = {}
        self.rule_versions = {}
        self.rule_metadata = {}

    def manage_rule_lifecycle(self, rule_file):
        """Manage complete Sigma rule lifecycle"""
        lifecycle_data = {
            'rule_file': rule_file,
            'lifecycle_stages': [
                'development',
                'testing',
                'validation',
                'staging',
                'production',
                'maintenance',
                'retirement'
            ],
            'current_stage': 'development',
            'stage_history': [],
            'version_info': {},
            'deployment_targets': []
        }

        return lifecycle_data

    def create_rule_documentation(self, rule_file):
        """Generate comprehensive documentation for Sigma rule"""
        with open(rule_file, 'r') as f:
            rule_content = f.read()
            rule_data = yaml.safe_load(rule_content)

        documentation = {
            'rule_file': rule_file,
            'generation_timestamp': datetime.now().isoformat(),
            'rule_metadata': rule_data,
            'technical_details': self._analyze_rule_technical_details(rule_data),
            'usage_examples': self._generate_usage_examples(rule_data),
            'deployment_notes': self._generate_deployment_notes(rule_data),
            'maintenance_info': self._generate_maintenance_info(rule_data)
        }

        return documentation

    def _analyze_rule_technical_details(self, rule_data):
        """Analyze technical details of Sigma rule"""
        details = {
            'logsource_analysis': rule_data.get('logsource', {}),
            'detection_complexity': 'simple',
            'field_count': 0,
            'condition_complexity': 'basic',
            'performance_impact': 'low'
        }

        # Analyze detection complexity
        detection = rule_data.get('detection', {})
        field_count = 0

        for key, value in detection.items():
            if key != 'condition' and isinstance(value, dict):
                field_count += len(value)

        details['field_count'] = field_count

        if field_count > 10:
            details['detection_complexity'] = 'complex'
            details['performance_impact'] = 'high'
        elif field_count > 5:
            details['detection_complexity'] = 'moderate'
            details['performance_impact'] = 'medium'

        return details

    def create_rule_test_cases(self, rule_file):
        """Create test cases for Sigma rule"""
        with open(rule_file, 'r') as f:
            rule_data = yaml.safe_load(f.read())

        test_cases = {
            'rule_file': rule_file,
            'test_cases': [],
            'negative_test_cases': [],
            'performance_test_cases': []
        }

        # Generate positive test cases
        detection = rule_data.get('detection', {})
        for key, value in detection.items():
            if key.startswith('selection'):
                test_case = self._generate_positive_test_case(value)
                test_cases['test_cases'].append(test_case)

        # Generate negative test cases based on filters
        for key, value in detection.items():
            if key.startswith('filter'):
                negative_test_case = self._generate_negative_test_case(value)
                test_cases['negative_test_cases'].append(negative_test_case)

        return test_cases
\`\`\`
      `,
      activities: [
        'Build multi-platform SIEM deployment system',
        'Implement automated Sigma rule conversion',
        'Create comprehensive rule management framework',
        'Develop rule testing and validation workflows'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Advanced Sigma Rule Development',
      description: 'Create sophisticated Sigma rules for complex attack scenarios',
      tasks: [
        'Develop multi-stage attack detection rules',
        'Create rules with advanced aggregation and correlation',
        'Optimize rules for multiple SIEM platforms',
        'Test rules against real-world attack scenarios'
      ]
    },
    {
      title: 'Sigma Rule Automation Pipeline',
      description: 'Build automated Sigma rule development and deployment pipeline',
      tasks: [
        'Implement automated rule generation from threat intelligence',
        'Create comprehensive rule testing framework',
        'Build multi-platform deployment automation',
        'Develop rule performance monitoring system'
      ]
    },
    {
      title: 'Enterprise Sigma Management',
      description: 'Deploy enterprise-wide Sigma rule management system',
      tasks: [
        'Design centralized rule repository and versioning',
        'Implement rule lifecycle management workflows',
        'Create rule effectiveness metrics and reporting',
        'Build collaborative rule development platform'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Sigma Rules Development Mastery',
      description: 'Demonstrate advanced Sigma rule development and deployment capabilities'
    },
    {
      type: 'project',
      title: 'Enterprise Sigma Management Solution',
      description: 'Design and implement comprehensive Sigma rule management and deployment system'
    }
  ],

  resources: [
    'Sigma Rule Specification and Documentation',
    'Advanced Sigma Rule Development Techniques',
    'Multi-Platform SIEM Integration Guide',
    'Sigma Rule Testing and Validation Frameworks',
    'Enterprise Sigma Deployment Strategies',
    'Sigma Rule Performance Optimization'
  ]
};
