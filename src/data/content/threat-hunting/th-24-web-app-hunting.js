/**
 * TH-24: Web Application Threat Hunting
 * Master web application security hunting and API threat detection
 */

export const webAppHuntingContent = {
  id: 'th-24',
  title: 'Web Application Threat Hunting',
  description: 'Master comprehensive web application threat hunting including API security, injection attacks, and modern web threat detection techniques.',
  duration: '38 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Master web application attack vector identification and hunting',
    'Implement API security monitoring and threat detection',
    'Build injection attack detection and prevention systems',
    'Develop web application behavioral analysis techniques',
    'Create automated web threat hunting workflows',
    'Implement web application forensics and investigation',
    'Build comprehensive web security monitoring platforms'
  ],

  sections: [
    {
      id: 'web-attack-hunting',
      title: 'Web Application Attack Detection',
      content: `
## Web Application Threat Landscape

### **Common Web Attack Vectors**
\`\`\`yaml
OWASP Top 10 Attack Categories:
  A01 - Broken Access Control:
    - Privilege escalation
    - IDOR (Insecure Direct Object References)
    - Missing function level access control
    - CORS misconfiguration
    
  A02 - Cryptographic Failures:
    - Sensitive data exposure
    - Weak encryption algorithms
    - Missing encryption in transit
    - Improper key management
    
  A03 - Injection:
    - SQL injection
    - NoSQL injection
    - LDAP injection
    - Command injection
    - XSS (Cross-Site Scripting)
    
  A04 - Insecure Design:
    - Missing security controls
    - Insecure design patterns
    - Threat modeling failures
    
  A05 - Security Misconfiguration:
    - Default configurations
    - Incomplete configurations
    - Open cloud storage
    - Verbose error messages
    
  A06 - Vulnerable Components:
    - Outdated libraries
    - Unsupported components
    - Unknown vulnerabilities
    
  A07 - Authentication Failures:
    - Broken authentication
    - Session management flaws
    - Credential stuffing
    - Brute force attacks
    
  A08 - Software Integrity Failures:
    - Unsigned updates
    - Insecure CI/CD pipelines
    - Auto-update vulnerabilities
    
  A09 - Logging Failures:
    - Insufficient logging
    - Missing monitoring
    - Inadequate incident response
    
  A10 - Server-Side Request Forgery:
    - SSRF attacks
    - Internal network access
    - Cloud metadata access
\`\`\`

### **Web Application Hunting Framework**
\`\`\`python
import re
import json
import urllib.parse
from datetime import datetime, timedelta
from collections import defaultdict
import base64

class WebApplicationHunter:
    def __init__(self):
        self.attack_patterns = {
            'sql_injection': [
                "union select",
                "or 1=1",
                "' or '1'='1",
                "exec xp_",
                "information_schema"
            ],
            'xss': [
                "<script>",
                "javascript:",
                "onload=",
                "<iframe",
                "<object"
            ],
            'command_injection': [
                "; cat",
                "& whoami",
                "$(id)",
                "/etc/passwd",
                "/proc/version"
            ],
            'path_traversal': [
                "../",
                "..\\\\",
                "%2e%2e%2f",
                "/etc/",
                "C:\\\\Windows"
            ]
        }
        
        self.suspicious_headers = [
            'x-forwarded-for', 'x-real-ip', 'x-originating-ip',
            'x-remote-ip', 'x-remote-addr', 'x-cluster-client-ip'
        ]
        
    def analyze_web_logs(self, log_entries):
        """Analyze web server logs for attack patterns"""
        analysis_results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_requests': len(log_entries),
            'attack_indicators': [],
            'suspicious_patterns': {},
            'risk_assessment': {}
        }
        
        # Analyze each log entry
        for entry in log_entries:
            indicators = self._analyze_single_request(entry)
            if indicators:
                analysis_results['attack_indicators'].extend(indicators)
        
        # Aggregate suspicious patterns
        analysis_results['suspicious_patterns'] = self._aggregate_attack_patterns(
            analysis_results['attack_indicators']
        )
        
        # Calculate risk assessment
        analysis_results['risk_assessment'] = self._calculate_web_risk_assessment(
            analysis_results['attack_indicators']
        )
        
        return analysis_results
    
    def _analyze_single_request(self, log_entry):
        """Analyze single web request for attack indicators"""
        indicators = []
        
        url = log_entry.get('url', '')
        user_agent = log_entry.get('user_agent', '')
        method = log_entry.get('method', '')
        status_code = log_entry.get('status_code', 0)
        payload = log_entry.get('post_data', '')
        headers = log_entry.get('headers', {})
        
        # Check for injection attacks
        for attack_type, patterns in self.attack_patterns.items():
            for pattern in patterns:
                if re.search(pattern, url, re.IGNORECASE) or re.search(pattern, payload, re.IGNORECASE):
                    indicators.append({
                        'type': attack_type,
                        'pattern': pattern,
                        'location': 'url' if pattern in url else 'payload',
                        'timestamp': log_entry.get('timestamp'),
                        'source_ip': log_entry.get('source_ip'),
                        'severity': self._calculate_attack_severity(attack_type, pattern)
                    })
        
        # Check for suspicious user agents
        if self._is_suspicious_user_agent(user_agent):
            indicators.append({
                'type': 'suspicious_user_agent',
                'user_agent': user_agent,
                'timestamp': log_entry.get('timestamp'),
                'source_ip': log_entry.get('source_ip'),
                'severity': 'medium'
            })
        
        # Check for brute force patterns
        if status_code in [401, 403] and method == 'POST':
            indicators.append({
                'type': 'potential_brute_force',
                'method': method,
                'status_code': status_code,
                'timestamp': log_entry.get('timestamp'),
                'source_ip': log_entry.get('source_ip'),
                'severity': 'medium'
            })
        
        # Check for suspicious headers
        for header_name, header_value in headers.items():
            if header_name.lower() in self.suspicious_headers:
                if self._is_suspicious_header_value(header_value):
                    indicators.append({
                        'type': 'header_manipulation',
                        'header': header_name,
                        'value': header_value,
                        'timestamp': log_entry.get('timestamp'),
                        'source_ip': log_entry.get('source_ip'),
                        'severity': 'low'
                    })
        
        return indicators
    
    def _is_suspicious_user_agent(self, user_agent):
        """Check if user agent is suspicious"""
        suspicious_agents = [
            'sqlmap', 'nikto', 'burp', 'nessus', 'openvas',
            'nmap', 'masscan', 'zap', 'w3af', 'skipfish'
        ]
        
        return any(agent in user_agent.lower() for agent in suspicious_agents)
    
    def detect_api_attacks(self, api_logs):
        """Detect attacks against REST APIs"""
        api_analysis = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_api_calls': len(api_logs),
            'attack_indicators': [],
            'rate_limiting_violations': [],
            'authentication_anomalies': []
        }
        
        # Group requests by source IP and endpoint
        requests_by_source = defaultdict(list)
        requests_by_endpoint = defaultdict(list)
        
        for log_entry in api_logs:
            source_ip = log_entry.get('source_ip')
            endpoint = log_entry.get('endpoint')
            
            requests_by_source[source_ip].append(log_entry)
            requests_by_endpoint[endpoint].append(log_entry)
        
        # Detect rate limiting violations
        for source_ip, requests in requests_by_source.items():
            rate_violations = self._detect_rate_limiting_violations(source_ip, requests)
            api_analysis['rate_limiting_violations'].extend(rate_violations)
        
        # Detect API enumeration
        for source_ip, requests in requests_by_source.items():
            enumeration = self._detect_api_enumeration(source_ip, requests)
            if enumeration:
                api_analysis['attack_indicators'].append(enumeration)
        
        # Detect authentication anomalies
        auth_anomalies = self._detect_authentication_anomalies(api_logs)
        api_analysis['authentication_anomalies'].extend(auth_anomalies)
        
        return api_analysis
    
    def _detect_rate_limiting_violations(self, source_ip, requests):
        """Detect rate limiting violations"""
        violations = []
        
        # Group requests by time windows (1 minute)
        time_windows = defaultdict(int)
        
        for request in requests:
            timestamp = datetime.fromisoformat(request['timestamp'])
            window = timestamp.replace(second=0, microsecond=0)
            time_windows[window] += 1
        
        # Check for excessive requests
        for window, count in time_windows.items():
            if count > 100:  # Threshold: 100 requests per minute
                violations.append({
                    'source_ip': source_ip,
                    'time_window': window.isoformat(),
                    'request_count': count,
                    'severity': 'high' if count > 500 else 'medium'
                })
        
        return violations
    
    def _detect_api_enumeration(self, source_ip, requests):
        """Detect API endpoint enumeration"""
        unique_endpoints = set(request.get('endpoint') for request in requests)
        status_404_count = len([r for r in requests if r.get('status_code') == 404])
        
        # Check for enumeration patterns
        if len(unique_endpoints) > 50 and status_404_count > 20:
            return {
                'type': 'api_enumeration',
                'source_ip': source_ip,
                'unique_endpoints': len(unique_endpoints),
                'not_found_requests': status_404_count,
                'total_requests': len(requests),
                'severity': 'high'
            }
        
        return None

### **Advanced Web Attack Detection**
\`\`\`python
class AdvancedWebAttackDetector:
    def __init__(self):
        self.ml_models = {}
        self.behavioral_baselines = {}
        
    def detect_advanced_attacks(self, web_traffic):
        """Detect advanced web attacks using ML and behavioral analysis"""
        detection_results = {
            'detection_timestamp': datetime.now().isoformat(),
            'advanced_attacks': [],
            'behavioral_anomalies': [],
            'zero_day_indicators': []
        }
        
        # Detect advanced persistent web threats
        apt_web_attacks = self._detect_apt_web_attacks(web_traffic)
        detection_results['advanced_attacks'].extend(apt_web_attacks)
        
        # Detect behavioral anomalies
        behavioral_anomalies = self._detect_behavioral_anomalies(web_traffic)
        detection_results['behavioral_anomalies'].extend(behavioral_anomalies)
        
        # Detect potential zero-day exploits
        zero_day_indicators = self._detect_zero_day_indicators(web_traffic)
        detection_results['zero_day_indicators'].extend(zero_day_indicators)
        
        return detection_results
    
    def _detect_apt_web_attacks(self, web_traffic):
        """Detect APT-style web attacks"""
        apt_indicators = []
        
        # Group traffic by source IP
        traffic_by_source = defaultdict(list)
        for request in web_traffic:
            source_ip = request.get('source_ip')
            traffic_by_source[source_ip].append(request)
        
        # Analyze each source for APT patterns
        for source_ip, requests in traffic_by_source.items():
            # Check for low-and-slow attacks
            if self._is_low_and_slow_attack(requests):
                apt_indicators.append({
                    'type': 'low_and_slow_attack',
                    'source_ip': source_ip,
                    'request_count': len(requests),
                    'time_span': self._calculate_time_span(requests),
                    'severity': 'high'
                })
            
            # Check for reconnaissance patterns
            if self._is_reconnaissance_pattern(requests):
                apt_indicators.append({
                    'type': 'web_reconnaissance',
                    'source_ip': source_ip,
                    'endpoints_probed': len(set(r.get('endpoint') for r in requests)),
                    'severity': 'medium'
                })
        
        return apt_indicators
    
    def _is_low_and_slow_attack(self, requests):
        """Check if requests indicate low-and-slow attack"""
        if len(requests) < 10:
            return False
        
        # Calculate time span
        timestamps = [datetime.fromisoformat(r['timestamp']) for r in requests]
        time_span = (max(timestamps) - min(timestamps)).total_seconds()
        
        # Low and slow: many requests over long period with consistent intervals
        if time_span > 3600 and len(requests) > 50:  # Over 1 hour, 50+ requests
            # Check for consistent intervals
            intervals = []
            for i in range(1, len(timestamps)):
                interval = (timestamps[i] - timestamps[i-1]).total_seconds()
                intervals.append(interval)
            
            # Calculate coefficient of variation
            if intervals:
                mean_interval = sum(intervals) / len(intervals)
                variance = sum((x - mean_interval) ** 2 for x in intervals) / len(intervals)
                std_dev = variance ** 0.5
                cv = std_dev / mean_interval if mean_interval > 0 else 0
                
                # Low coefficient of variation indicates regular intervals
                return cv < 0.5
        
        return False
    
    def build_web_application_baseline(self, historical_traffic):
        """Build behavioral baseline for web application"""
        baseline = {
            'baseline_timestamp': datetime.now().isoformat(),
            'traffic_patterns': {},
            'endpoint_usage': {},
            'user_behavior': {},
            'error_rates': {}
        }
        
        # Analyze traffic patterns
        baseline['traffic_patterns'] = self._analyze_traffic_patterns(historical_traffic)
        
        # Analyze endpoint usage
        baseline['endpoint_usage'] = self._analyze_endpoint_usage(historical_traffic)
        
        # Analyze user behavior
        baseline['user_behavior'] = self._analyze_user_behavior(historical_traffic)
        
        # Analyze error rates
        baseline['error_rates'] = self._analyze_error_rates(historical_traffic)
        
        return baseline
\`\`\`
      `,
      activities: [
        'Analyze web application attack vectors and patterns',
        'Build web log analysis and attack detection systems',
        'Implement API security monitoring and threat detection',
        'Create advanced web attack detection algorithms'
      ]
    },

    {
      id: 'web-forensics',
      title: 'Web Application Forensics and Investigation',
      content: `
## Web Application Incident Response and Forensics

### **Web Application Forensics Framework**
\`\`\`python
class WebApplicationForensics:
    def __init__(self):
        self.evidence_collectors = {}
        self.timeline_builders = {}
        self.artifact_analyzers = {}

    def conduct_web_incident_investigation(self, incident_data):
        """Conduct comprehensive web application incident investigation"""
        investigation_results = {
            'incident_id': incident_data.get('incident_id'),
            'investigation_timestamp': datetime.now().isoformat(),
            'evidence_collected': {},
            'timeline_analysis': {},
            'attack_reconstruction': {},
            'impact_assessment': {}
        }

        # Collect digital evidence
        evidence = self._collect_web_evidence(incident_data)
        investigation_results['evidence_collected'] = evidence

        # Build attack timeline
        timeline = self._build_attack_timeline(evidence)
        investigation_results['timeline_analysis'] = timeline

        # Reconstruct attack chain
        attack_chain = self._reconstruct_attack_chain(evidence, timeline)
        investigation_results['attack_reconstruction'] = attack_chain

        # Assess impact
        impact = self._assess_incident_impact(evidence, attack_chain)
        investigation_results['impact_assessment'] = impact

        return investigation_results

    def _collect_web_evidence(self, incident_data):
        """Collect web application evidence"""
        evidence = {
            'web_logs': [],
            'database_logs': [],
            'application_logs': [],
            'system_artifacts': [],
            'network_captures': []
        }

        # Collect web server logs
        web_logs = self._collect_web_server_logs(incident_data)
        evidence['web_logs'] = web_logs

        # Collect database logs
        db_logs = self._collect_database_logs(incident_data)
        evidence['database_logs'] = db_logs

        # Collect application logs
        app_logs = self._collect_application_logs(incident_data)
        evidence['application_logs'] = app_logs

        # Collect system artifacts
        system_artifacts = self._collect_system_artifacts(incident_data)
        evidence['system_artifacts'] = system_artifacts

        return evidence

    def _build_attack_timeline(self, evidence):
        """Build comprehensive attack timeline"""
        timeline_events = []

        # Process web logs
        for log_entry in evidence.get('web_logs', []):
            if self._is_suspicious_request(log_entry):
                timeline_events.append({
                    'timestamp': log_entry.get('timestamp'),
                    'event_type': 'suspicious_web_request',
                    'source': 'web_logs',
                    'details': log_entry,
                    'severity': self._calculate_event_severity(log_entry)
                })

        # Process database logs
        for db_entry in evidence.get('database_logs', []):
            if self._is_suspicious_db_activity(db_entry):
                timeline_events.append({
                    'timestamp': db_entry.get('timestamp'),
                    'event_type': 'suspicious_database_activity',
                    'source': 'database_logs',
                    'details': db_entry,
                    'severity': self._calculate_db_severity(db_entry)
                })

        # Sort timeline by timestamp
        timeline_events.sort(key=lambda x: x['timestamp'])

        return {
            'total_events': len(timeline_events),
            'events': timeline_events,
            'attack_phases': self._identify_attack_phases(timeline_events)
        }

    def _reconstruct_attack_chain(self, evidence, timeline):
        """Reconstruct the complete attack chain"""
        attack_chain = {
            'initial_access': None,
            'privilege_escalation': [],
            'persistence': [],
            'data_access': [],
            'exfiltration': [],
            'impact': []
        }

        timeline_events = timeline.get('events', [])

        # Identify initial access
        for event in timeline_events:
            if event['event_type'] == 'suspicious_web_request':
                details = event['details']
                if self._indicates_initial_access(details):
                    attack_chain['initial_access'] = {
                        'timestamp': event['timestamp'],
                        'method': self._identify_access_method(details),
                        'source_ip': details.get('source_ip'),
                        'payload': details.get('payload')
                    }
                    break

        # Identify subsequent attack phases
        for event in timeline_events:
            event_type = event['event_type']
            details = event['details']

            if self._indicates_privilege_escalation(details):
                attack_chain['privilege_escalation'].append(event)
            elif self._indicates_persistence(details):
                attack_chain['persistence'].append(event)
            elif self._indicates_data_access(details):
                attack_chain['data_access'].append(event)
            elif self._indicates_exfiltration(details):
                attack_chain['exfiltration'].append(event)
            elif self._indicates_impact(details):
                attack_chain['impact'].append(event)

        return attack_chain

### **Automated Web Threat Response**
\`\`\`python
class WebThreatResponseSystem:
    def __init__(self):
        self.response_playbooks = {}
        self.mitigation_strategies = {}
        self.blocking_rules = {}

    def execute_automated_response(self, threat_detection):
        """Execute automated response to web threats"""
        response_result = {
            'threat_id': threat_detection.get('threat_id'),
            'response_timestamp': datetime.now().isoformat(),
            'actions_taken': [],
            'mitigation_effectiveness': 0
        }

        threat_type = threat_detection.get('threat_type')
        severity = threat_detection.get('severity', 'medium')

        # Determine response actions based on threat type and severity
        response_actions = self._determine_response_actions(threat_type, severity)

        # Execute response actions
        for action in response_actions:
            action_result = self._execute_response_action(action, threat_detection)
            response_result['actions_taken'].append(action_result)

        # Evaluate mitigation effectiveness
        effectiveness = self._evaluate_mitigation_effectiveness(
            threat_detection, response_result['actions_taken']
        )
        response_result['mitigation_effectiveness'] = effectiveness

        return response_result

    def _determine_response_actions(self, threat_type, severity):
        """Determine appropriate response actions"""
        actions = []

        if threat_type == 'sql_injection':
            actions.extend([
                {'type': 'block_source_ip', 'duration': 3600},
                {'type': 'enable_waf_rule', 'rule_type': 'sql_injection'},
                {'type': 'alert_security_team', 'urgency': 'high'}
            ])

        elif threat_type == 'xss':
            actions.extend([
                {'type': 'sanitize_input', 'scope': 'affected_parameters'},
                {'type': 'enable_csp_headers', 'policy': 'strict'},
                {'type': 'alert_development_team', 'urgency': 'medium'}
            ])

        elif threat_type == 'brute_force':
            actions.extend([
                {'type': 'implement_rate_limiting', 'threshold': 10},
                {'type': 'enable_account_lockout', 'duration': 1800},
                {'type': 'require_captcha', 'scope': 'login_forms'}
            ])

        elif threat_type == 'api_abuse':
            actions.extend([
                {'type': 'throttle_api_requests', 'limit': 100},
                {'type': 'require_api_authentication', 'method': 'token'},
                {'type': 'log_api_usage', 'detail_level': 'verbose'}
            ])

        # Add severity-based actions
        if severity == 'critical':
            actions.extend([
                {'type': 'isolate_affected_systems', 'scope': 'web_tier'},
                {'type': 'activate_incident_response', 'level': 'critical'},
                {'type': 'notify_executives', 'urgency': 'immediate'}
            ])

        return actions

    def create_waf_rules(self, threat_patterns):
        """Create Web Application Firewall rules"""
        waf_rules = []

        for pattern in threat_patterns:
            threat_type = pattern.get('type')
            pattern_regex = pattern.get('regex')

            if threat_type == 'sql_injection':
                waf_rule = {
                    'rule_id': f"sql_injection_{len(waf_rules) + 1}",
                    'name': 'SQL Injection Protection',
                    'pattern': pattern_regex,
                    'action': 'block',
                    'log_level': 'warning',
                    'enabled': True
                }
                waf_rules.append(waf_rule)

            elif threat_type == 'xss':
                waf_rule = {
                    'rule_id': f"xss_protection_{len(waf_rules) + 1}",
                    'name': 'XSS Protection',
                    'pattern': pattern_regex,
                    'action': 'sanitize',
                    'log_level': 'info',
                    'enabled': True
                }
                waf_rules.append(waf_rule)

        return waf_rules

    def implement_security_headers(self, application_config):
        """Implement security headers for web application"""
        security_headers = {
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            'X-Frame-Options': 'DENY',
            'X-Content-Type-Options': 'nosniff',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
        }

        # Customize headers based on application requirements
        if application_config.get('uses_frames'):
            security_headers['X-Frame-Options'] = 'SAMEORIGIN'

        if application_config.get('api_endpoints'):
            security_headers['Access-Control-Allow-Origin'] = application_config.get('allowed_origins', '*')
            security_headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
            security_headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'

        return security_headers
\`\`\`
      `,
      activities: [
        'Build web application forensics framework',
        'Implement automated web threat response',
        'Create WAF rule generation system',
        'Develop web security monitoring dashboard'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Web Application Attack Simulation',
      description: 'Simulate and detect various web application attacks',
      tasks: [
        'Set up vulnerable web application environment',
        'Execute OWASP Top 10 attack scenarios',
        'Build detection rules for each attack type',
        'Create automated response workflows'
      ]
    },
    {
      title: 'API Security Assessment',
      description: 'Comprehensive API security testing and monitoring',
      tasks: [
        'Analyze REST API security posture',
        'Implement API rate limiting and authentication',
        'Build API abuse detection system',
        'Create API security monitoring dashboard'
      ]
    },
    {
      title: 'Web Application Incident Response',
      description: 'Conduct full web application incident investigation',
      tasks: [
        'Investigate simulated web application breach',
        'Collect and analyze digital evidence',
        'Reconstruct attack timeline and methods',
        'Develop remediation and prevention strategies'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Web Application Hunting Mastery',
      description: 'Demonstrate advanced web application threat hunting capabilities'
    },
    {
      type: 'project',
      title: 'Enterprise Web Security Platform',
      description: 'Design and implement comprehensive web application security monitoring solution'
    }
  ],

  resources: [
    'OWASP Testing Guide and Top 10',
    'Web Application Security Scanner Tools',
    'API Security Best Practices',
    'Web Application Firewall Configuration',
    'Modern Web Threat Landscape Analysis',
    'Web Application Forensics Techniques'
  ]
};
