/**
 * TH-11: Hunting Platforms Overview
 * Comprehensive comparison and mastery of threat hunting platforms
 */

export const huntingPlatformsContent = {
  id: 'th-11',
  title: 'Hunting Platforms Overview',
  description: 'Master the selection, deployment, and optimization of threat hunting platforms across enterprise environments.',
  duration: '35 hours',
  difficulty: 'Intermediate',
  
  objectives: [
    'Compare and evaluate major threat hunting platforms',
    'Design platform selection criteria for enterprise environments',
    'Implement multi-platform hunting strategies',
    'Optimize platform performance and capabilities',
    'Integrate platforms with existing security infrastructure',
    'Build unified hunting workflows across platforms',
    'Assess platform ROI and effectiveness metrics'
  ],

  sections: [
    {
      id: 'platform-landscape',
      title: 'Threat Hunting Platform Landscape',
      content: `
## Major Hunting Platform Categories

### **Enterprise SIEM Platforms**
\`\`\`yaml
Splunk Enterprise Security:
  Strengths:
    - Advanced SPL query language
    - Extensive app ecosystem
    - Powerful visualization capabilities
    - Strong community support
  Weaknesses:
    - High licensing costs
    - Resource intensive
    - Complex administration
  Best For: Large enterprises, complex environments

IBM QRadar:
  Strengths:
    - Built-in threat intelligence
    - Advanced correlation rules
    - Compliance reporting
    - Integrated incident response
  Weaknesses:
    - Steep learning curve
    - Limited customization
    - Expensive scaling
  Best For: Regulated industries, compliance-focused

ArcSight ESM:
  Strengths:
    - Real-time correlation
    - Scalable architecture
    - Advanced analytics
    - Mature platform
  Weaknesses:
    - Complex deployment
    - High maintenance overhead
    - Legacy interface
  Best For: Large enterprises, high-volume environments
\`\`\`

### **Cloud-Native Platforms**
\`\`\`yaml
Microsoft Azure Sentinel:
  Strengths:
    - Cloud-native scalability
    - KQL query language
    - AI/ML integration
    - Office 365 integration
  Weaknesses:
    - Microsoft ecosystem dependency
    - Limited on-premises support
    - Newer platform (less mature)
  Best For: Microsoft-centric environments, cloud-first

Google Chronicle:
  Strengths:
    - Massive scale capabilities
    - Fast search performance
    - Built-in threat intelligence
    - Predictable pricing
  Weaknesses:
    - Limited customization
    - Google ecosystem focus
    - Fewer integrations
  Best For: Large-scale environments, Google Cloud users

AWS Security Lake:
  Strengths:
    - AWS native integration
    - Cost-effective storage
    - Open data formats
    - Serverless architecture
  Weaknesses:
    - Limited analysis tools
    - AWS ecosystem dependency
    - Requires additional tools
  Best For: AWS-heavy environments, data lake approach
\`\`\`

### **Open Source Solutions**
\`\`\`yaml
ELK Stack (Elasticsearch, Logstash, Kibana):
  Strengths:
    - Open source and free
    - Highly customizable
    - Strong community
    - Flexible architecture
  Weaknesses:
    - Requires expertise to manage
    - No built-in security features
    - Scaling complexity
  Best For: Budget-conscious, technical teams

Wazuh:
  Strengths:
    - Free and open source
    - Built-in HIDS/NIDS
    - Compliance frameworks
    - Active community
  Weaknesses:
    - Limited advanced analytics
    - Requires technical expertise
    - Scaling challenges
  Best For: Small to medium organizations, compliance

OSSIM/AlienVault:
  Strengths:
    - Integrated security tools
    - Threat intelligence feeds
    - Asset discovery
    - Vulnerability assessment
  Weaknesses:
    - Performance limitations
    - Limited scalability
    - Complex configuration
  Best For: Small organizations, all-in-one solution
\`\`\`

## Platform Selection Matrix

### **Evaluation Criteria**
\`\`\`python
# Platform evaluation framework
class PlatformEvaluator:
    def __init__(self):
        self.criteria = {
            'technical_capabilities': {
                'query_language_power': 0.2,
                'data_ingestion_rate': 0.15,
                'search_performance': 0.15,
                'visualization_quality': 0.1,
                'api_capabilities': 0.1,
                'integration_options': 0.15,
                'scalability': 0.15
            },
            'operational_factors': {
                'ease_of_use': 0.2,
                'administration_complexity': 0.15,
                'skill_requirements': 0.15,
                'documentation_quality': 0.1,
                'community_support': 0.1,
                'vendor_support': 0.15,
                'training_availability': 0.15
            },
            'business_considerations': {
                'total_cost_ownership': 0.3,
                'licensing_model': 0.2,
                'implementation_time': 0.15,
                'roi_timeline': 0.15,
                'vendor_stability': 0.1,
                'compliance_support': 0.1
            }
        }
    
    def evaluate_platform(self, platform_scores):
        total_score = 0
        for category, weights in self.criteria.items():
            category_score = 0
            for criterion, weight in weights.items():
                score = platform_scores.get(category, {}).get(criterion, 0)
                category_score += score * weight
            total_score += category_score
        return total_score

# Example evaluation
splunk_scores = {
    'technical_capabilities': {
        'query_language_power': 9,
        'data_ingestion_rate': 8,
        'search_performance': 7,
        'visualization_quality': 9,
        'api_capabilities': 8,
        'integration_options': 9,
        'scalability': 8
    },
    'operational_factors': {
        'ease_of_use': 6,
        'administration_complexity': 4,
        'skill_requirements': 3,
        'documentation_quality': 9,
        'community_support': 9,
        'vendor_support': 8,
        'training_availability': 9
    },
    'business_considerations': {
        'total_cost_ownership': 3,
        'licensing_model': 4,
        'implementation_time': 5,
        'roi_timeline': 6,
        'vendor_stability': 9,
        'compliance_support': 8
    }
}
\`\`\`
      `,
      activities: [
        'Evaluate platforms using selection matrix',
        'Create platform comparison spreadsheet',
        'Conduct hands-on platform trials',
        'Interview platform users and administrators'
      ]
    },

    {
      id: 'multi-platform-strategies',
      title: 'Multi-Platform Hunting Strategies',
      content: `
## Unified Hunting Across Platforms

### **Data Normalization Framework**
\`\`\`python
import json
from datetime import datetime
from typing import Dict, Any, List

class UnifiedEventFormat:
    """Common event format for cross-platform hunting"""
    
    def __init__(self):
        self.schema = {
            'timestamp': 'ISO 8601 format',
            'source_platform': 'Platform identifier',
            'event_type': 'Normalized event category',
            'source_ip': 'Source IP address',
            'dest_ip': 'Destination IP address',
            'user': 'Username or user identifier',
            'process': 'Process name or identifier',
            'command_line': 'Command line arguments',
            'file_path': 'File path or name',
            'registry_key': 'Registry key path',
            'network_protocol': 'Network protocol',
            'port': 'Network port',
            'severity': 'Event severity (1-10)',
            'raw_event': 'Original event data'
        }
    
    def normalize_splunk_event(self, splunk_event):
        """Convert Splunk event to unified format"""
        return {
            'timestamp': splunk_event.get('_time'),
            'source_platform': 'splunk',
            'event_type': self._map_splunk_sourcetype(splunk_event.get('sourcetype')),
            'source_ip': splunk_event.get('src_ip', splunk_event.get('src')),
            'dest_ip': splunk_event.get('dest_ip', splunk_event.get('dest')),
            'user': splunk_event.get('user', splunk_event.get('User')),
            'process': splunk_event.get('process', splunk_event.get('Image')),
            'command_line': splunk_event.get('CommandLine'),
            'severity': self._calculate_severity(splunk_event),
            'raw_event': splunk_event
        }
    
    def normalize_elk_event(self, elk_event):
        """Convert ELK event to unified format"""
        return {
            'timestamp': elk_event.get('@timestamp'),
            'source_platform': 'elk',
            'event_type': self._map_elk_type(elk_event.get('type')),
            'source_ip': elk_event.get('source', {}).get('ip'),
            'dest_ip': elk_event.get('destination', {}).get('ip'),
            'user': elk_event.get('user', {}).get('name'),
            'process': elk_event.get('process', {}).get('name'),
            'command_line': elk_event.get('process', {}).get('command_line'),
            'severity': self._calculate_severity(elk_event),
            'raw_event': elk_event
        }
    
    def normalize_sentinel_event(self, sentinel_event):
        """Convert Azure Sentinel event to unified format"""
        return {
            'timestamp': sentinel_event.get('TimeGenerated'),
            'source_platform': 'sentinel',
            'event_type': self._map_sentinel_type(sentinel_event.get('Type')),
            'source_ip': sentinel_event.get('SourceIP'),
            'dest_ip': sentinel_event.get('DestinationIP'),
            'user': sentinel_event.get('UserPrincipalName'),
            'process': sentinel_event.get('ProcessName'),
            'command_line': sentinel_event.get('CommandLine'),
            'severity': self._calculate_severity(sentinel_event),
            'raw_event': sentinel_event
        }

### Cross-Platform Correlation Engine
class CrossPlatformHunter:
    def __init__(self, platforms):
        self.platforms = platforms
        self.normalizer = UnifiedEventFormat()
        self.correlation_rules = []
    
    def hunt_across_platforms(self, hunt_query):
        """Execute hunt across multiple platforms"""
        results = {}
        
        for platform_name, platform_client in self.platforms.items():
            try:
                # Translate query to platform-specific format
                platform_query = self._translate_query(hunt_query, platform_name)
                
                # Execute query
                raw_results = platform_client.search(platform_query)
                
                # Normalize results
                normalized_results = []
                for event in raw_results:
                    if platform_name == 'splunk':
                        normalized_event = self.normalizer.normalize_splunk_event(event)
                    elif platform_name == 'elk':
                        normalized_event = self.normalizer.normalize_elk_event(event)
                    elif platform_name == 'sentinel':
                        normalized_event = self.normalizer.normalize_sentinel_event(event)
                    
                    normalized_results.append(normalized_event)
                
                results[platform_name] = normalized_results
                
            except Exception as e:
                results[platform_name] = {'error': str(e)}
        
        return self._correlate_results(results)
    
    def _correlate_results(self, platform_results):
        """Correlate events across platforms"""
        correlated_events = []
        
        # Simple correlation by IP address and time window
        all_events = []
        for platform, events in platform_results.items():
            if isinstance(events, list):
                all_events.extend(events)
        
        # Group events by source IP and time window (5 minutes)
        time_window = 300  # 5 minutes in seconds
        correlation_groups = {}
        
        for event in all_events:
            if event.get('source_ip'):
                timestamp = datetime.fromisoformat(event['timestamp'])
                time_bucket = int(timestamp.timestamp() // time_window)
                key = f"{event['source_ip']}_{time_bucket}"
                
                if key not in correlation_groups:
                    correlation_groups[key] = []
                correlation_groups[key].append(event)
        
        # Identify multi-platform correlations
        for group_key, events in correlation_groups.items():
            platforms_involved = set(event['source_platform'] for event in events)
            if len(platforms_involved) > 1:
                correlated_events.append({
                    'correlation_key': group_key,
                    'platforms': list(platforms_involved),
                    'event_count': len(events),
                    'events': events,
                    'risk_score': self._calculate_correlation_risk(events)
                })
        
        return correlated_events
\`\`\`

### **Query Translation Framework**
\`\`\`python
class QueryTranslator:
    """Translate hunting queries between platforms"""
    
    def __init__(self):
        self.translation_rules = {
            'ip_search': {
                'splunk': 'src_ip="{ip}" OR dest_ip="{ip}"',
                'elk': 'source.ip:"{ip}" OR destination.ip:"{ip}"',
                'sentinel': 'SourceIP == "{ip}" or DestinationIP == "{ip}"'
            },
            'process_search': {
                'splunk': 'process="{process}" OR Image="*{process}*"',
                'elk': 'process.name:"{process}"',
                'sentinel': 'ProcessName contains "{process}"'
            },
            'time_range': {
                'splunk': 'earliest={start} latest={end}',
                'elk': '@timestamp:[{start} TO {end}]',
                'sentinel': 'TimeGenerated between (datetime({start}) .. datetime({end}))'
            }
        }
    
    def translate_hunt_query(self, generic_query, target_platform):
        """Translate generic hunt query to platform-specific syntax"""
        translated_query = generic_query
        
        for rule_type, translations in self.translation_rules.items():
            if rule_type in generic_query:
                platform_syntax = translations.get(target_platform, '')
                translated_query = translated_query.replace(
                    f"{{{rule_type}}}", 
                    platform_syntax
                )
        
        return translated_query

# Example usage
translator = QueryTranslator()
generic_query = "search for {ip_search} in {time_range} where {process_search}"

# Translate to different platforms
splunk_query = translator.translate_hunt_query(generic_query, 'splunk')
elk_query = translator.translate_hunt_query(generic_query, 'elk')
sentinel_query = translator.translate_hunt_query(generic_query, 'sentinel')
\`\`\`
      `,
      activities: [
        'Build cross-platform correlation engine',
        'Create query translation framework',
        'Implement unified event format',
        'Test multi-platform hunting scenarios'
      ]
    },

    {
      id: 'platform-optimization',
      title: 'Platform Performance Optimization',
      content: `
## Performance Tuning Strategies

### **Splunk Optimization**
\`\`\`bash
# Splunk performance tuning checklist

# 1. Index optimization
[index_settings]
maxDataSize = auto_high_volume
maxHotBuckets = 10
maxWarmDBCount = 300

# 2. Search optimization
# Use time ranges effectively
earliest=-24h@h latest=@h

# Limit search scope
index=security sourcetype=windows:security

# Use statistical commands efficiently
| stats count by user | sort -count | head 100

# 3. Hardware optimization
# Indexer specifications
CPU: 16+ cores
RAM: 32GB+ (12GB for Splunk, rest for OS cache)
Storage: SSD for hot/warm buckets, spinning disk for cold

# Search head specifications  
CPU: 8+ cores
RAM: 16GB+
Storage: SSD for search artifacts
\`\`\`

### **ELK Stack Optimization**
\`\`\`yaml
# Elasticsearch optimization
elasticsearch.yml:
  cluster.name: hunting-cluster
  node.name: hunting-node-1
  
  # Memory settings
  bootstrap.memory_lock: true
  
  # Index settings
  index.number_of_shards: 3
  index.number_of_replicas: 1
  index.refresh_interval: 30s
  
  # Search settings
  search.max_buckets: 65536
  indices.query.bool.max_clause_count: 10000

# Logstash optimization
logstash.yml:
  pipeline.workers: 8
  pipeline.batch.size: 1000
  pipeline.batch.delay: 50

# Kibana optimization
kibana.yml:
  elasticsearch.requestTimeout: 300000
  elasticsearch.shardTimeout: 120000
\`\`\`

### **Performance Monitoring Framework**
\`\`\`python
import time
import psutil
import requests
from datetime import datetime

class PlatformMonitor:
    def __init__(self, platforms):
        self.platforms = platforms
        self.metrics = {}
    
    def monitor_splunk_performance(self, splunk_client):
        """Monitor Splunk performance metrics"""
        try:
            # Search performance
            start_time = time.time()
            search_job = splunk_client.jobs.create('search index=* | head 1000')
            
            while not search_job.is_done():
                time.sleep(1)
            
            search_duration = time.time() - start_time
            
            # System metrics
            system_metrics = {
                'cpu_usage': psutil.cpu_percent(),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent
            }
            
            # Splunk-specific metrics
            splunk_metrics = {
                'search_duration': search_duration,
                'indexing_rate': self._get_splunk_indexing_rate(splunk_client),
                'search_concurrency': self._get_splunk_search_concurrency(splunk_client)
            }
            
            return {
                'timestamp': datetime.now().isoformat(),
                'platform': 'splunk',
                'system_metrics': system_metrics,
                'platform_metrics': splunk_metrics
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def monitor_elk_performance(self, elk_client):
        """Monitor ELK stack performance"""
        try:
            # Elasticsearch cluster health
            cluster_health = elk_client.cluster.health()
            
            # Search performance test
            start_time = time.time()
            search_result = elk_client.search(
                index='*',
                body={'query': {'match_all': {}}, 'size': 1000}
            )
            search_duration = time.time() - start_time
            
            # Node statistics
            node_stats = elk_client.nodes.stats()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'platform': 'elk',
                'cluster_health': cluster_health,
                'search_duration': search_duration,
                'node_stats': node_stats
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        report = {
            'report_timestamp': datetime.now().isoformat(),
            'platforms': {}
        }
        
        for platform_name, client in self.platforms.items():
            if platform_name == 'splunk':
                metrics = self.monitor_splunk_performance(client)
            elif platform_name == 'elk':
                metrics = self.monitor_elk_performance(client)
            
            report['platforms'][platform_name] = metrics
        
        # Calculate performance scores
        report['performance_scores'] = self._calculate_performance_scores(report)
        
        return report
    
    def _calculate_performance_scores(self, report):
        """Calculate normalized performance scores"""
        scores = {}
        
        for platform, metrics in report['platforms'].items():
            if 'error' not in metrics:
                # Normalize metrics to 0-100 scale
                search_score = max(0, 100 - (metrics.get('search_duration', 0) * 10))
                cpu_score = max(0, 100 - metrics.get('system_metrics', {}).get('cpu_usage', 0))
                memory_score = max(0, 100 - metrics.get('system_metrics', {}).get('memory_usage', 0))
                
                overall_score = (search_score + cpu_score + memory_score) / 3
                scores[platform] = {
                    'search_performance': search_score,
                    'cpu_performance': cpu_score,
                    'memory_performance': memory_score,
                    'overall_score': overall_score
                }
        
        return scores
\`\`\`
      `,
      activities: [
        'Implement platform performance monitoring',
        'Optimize search queries for each platform',
        'Tune hardware and software configurations',
        'Create performance benchmarking framework'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Platform Selection Workshop',
      description: 'Evaluate and select optimal hunting platform for given enterprise scenario',
      tasks: [
        'Analyze enterprise requirements and constraints',
        'Evaluate platforms using selection matrix',
        'Create detailed comparison report',
        'Present recommendation with justification'
      ]
    },
    {
      title: 'Multi-Platform Hunting Lab',
      description: 'Implement unified hunting across Splunk, ELK, and Sentinel',
      tasks: [
        'Set up data normalization framework',
        'Build cross-platform correlation engine',
        'Create query translation system',
        'Execute coordinated hunting campaign'
      ]
    },
    {
      title: 'Platform Optimization Challenge',
      description: 'Optimize hunting platform performance for high-volume environment',
      tasks: [
        'Baseline current performance metrics',
        'Identify performance bottlenecks',
        'Implement optimization strategies',
        'Measure and validate improvements'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Platform Architecture Design',
      description: 'Design comprehensive hunting platform architecture for enterprise environment'
    },
    {
      type: 'project',
      title: 'Multi-Platform Integration',
      description: 'Build working multi-platform hunting solution with unified interface'
    }
  ],

  resources: [
    'SIEM Platform Comparison Studies',
    'Cloud Security Platform Evaluations',
    'Open Source SIEM Deployment Guides',
    'Platform Performance Benchmarking Tools',
    'Enterprise Architecture Best Practices'
  ]
};
