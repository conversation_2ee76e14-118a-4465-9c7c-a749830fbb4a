/**
 * TH-12: Splunk for Threat Hunting
 * Master Splunk for comprehensive threat hunting operations
 */

export const splunkHuntingContent = {
  id: 'th-12',
  title: 'Splunk for Threat Hunting',
  description: 'Master Splunk platform for comprehensive threat hunting operations, from basic searches to advanced hunting techniques and automation.',
  duration: '40 hours',
  difficulty: 'Intermediate',
  
  learningObjectives: [
    'Master Splunk Search Processing Language (SPL) for threat hunting',
    'Build advanced hunting queries and correlation searches',
    'Create custom dashboards and visualizations for threat detection',
    'Implement automated hunting workflows and alerts',
    'Develop threat hunting playbooks in Splunk',
    'Integrate threat intelligence feeds with Splunk hunting',
    'Perform behavioral analysis using Splunk analytics',
    'Create custom hunting apps and add-ons'
  ],

  sections: [
    {
      id: 'splunk-fundamentals',
      title: 'Splunk Fundamentals for Hunters',
      content: `
## Splunk Architecture for Threat Hunting

### Core Components
- **Indexers**: Store and index security data
- **Search Heads**: Execute hunting queries and analytics
- **Forwarders**: Collect data from various sources
- **Deployment Server**: Manage configurations

### Data Models for Security
- **Authentication Data Model**: Login activities and authentication events
- **Network Traffic Data Model**: Network communications and flows
- **Endpoint Data Model**: Host-based security events
- **Malware Data Model**: Malware detection and analysis
- **Web Data Model**: Web application security events

### Essential SPL Commands for Hunting
\`\`\`spl
# Basic search structure
index=security sourcetype=windows:security
| search EventCode=4624
| stats count by user, src_ip
| sort -count

# Time-based analysis
index=network earliest=-24h@h latest=@h
| bucket _time span=1h
| stats count by _time, dest_port
| timechart span=1h count by dest_port
\`\`\`
      `,
      activities: [
        'Set up Splunk hunting environment',
        'Configure security data sources',
        'Practice basic SPL commands',
        'Explore security data models'
      ]
    },

    {
      id: 'hunting-queries',
      title: 'Advanced Hunting Queries',
      content: `
## Behavioral Analysis Queries

### Detecting Lateral Movement
\`\`\`spl
# Unusual authentication patterns
index=security sourcetype=windows:security EventCode=4624
| eval hour=strftime(_time, "%H")
| stats dc(dest) as unique_hosts by user, hour
| where unique_hosts > 5
| sort -unique_hosts
\`\`\`

### Command and Control Detection
\`\`\`spl
# Beaconing detection
index=network sourcetype=firewall
| bucket _time span=1m
| stats count by _time, src_ip, dest_ip, dest_port
| eventstats avg(count) as avg_count, stdev(count) as stdev_count by src_ip, dest_ip, dest_port
| eval threshold=avg_count+2*stdev_count
| where count > threshold
\`\`\`

### Data Exfiltration Hunting
\`\`\`spl
# Large data transfers
index=network sourcetype=netflow
| eval bytes_mb=bytes/1024/1024
| stats sum(bytes_mb) as total_mb by src_ip, dest_ip
| where total_mb > 100
| sort -total_mb
\`\`\`

### Process Execution Analysis
\`\`\`spl
# Suspicious process chains
index=endpoint sourcetype=sysmon EventCode=1
| eval process_chain=ParentImage + " -> " + Image
| stats count by process_chain, Computer
| where count < 5
| sort count
\`\`\`
      `,
      activities: [
        'Build lateral movement detection queries',
        'Create C2 beaconing detection logic',
        'Develop data exfiltration hunting searches',
        'Analyze process execution patterns'
      ]
    },

    {
      id: 'threat-intelligence',
      title: 'Threat Intelligence Integration',
      content: `
## IOC Hunting with Threat Intelligence

### IP Reputation Hunting
\`\`\`spl
# Create threat intel lookup
| inputlookup threat_intel_ips.csv
| eval threat_level=case(
    reputation="malicious", 5,
    reputation="suspicious", 3,
    reputation="unknown", 1,
    1==1, 0
)

# Hunt for IOCs in network traffic
index=network
| lookup threat_intel_ips.csv ip as dest_ip OUTPUT reputation, threat_level
| where threat_level >= 3
| stats count by src_ip, dest_ip, reputation
\`\`\`

### Domain Reputation Analysis
\`\`\`spl
# DNS hunting with threat intelligence
index=dns
| rex field=query "(?<domain>[^.]+\.[^.]+)$"
| lookup threat_domains.csv domain OUTPUT category, risk_score
| where risk_score > 7
| stats count by src_ip, domain, category
\`\`\`

### Hash-based Malware Hunting
\`\`\`spl
# File hash reputation checking
index=endpoint sourcetype=sysmon EventCode=15
| lookup malware_hashes.csv hash as Hash OUTPUT malware_family, severity
| where isnotnull(malware_family)
| stats count by Computer, Image, malware_family, severity
\`\`\`
      `,
      activities: [
        'Set up threat intelligence lookups',
        'Create IOC hunting dashboards',
        'Build automated threat intel correlation',
        'Develop hash-based hunting queries'
      ]
    },

    {
      id: 'automation-playbooks',
      title: 'Hunting Automation & Playbooks',
      content: `
## Automated Hunting Workflows

### Correlation Searches
\`\`\`spl
# Automated lateral movement detection
index=security sourcetype=windows:security EventCode=4624
| eval hour=strftime(_time, "%H")
| stats dc(dest) as unique_hosts by user, hour
| where unique_hosts > 5
| eval severity=case(
    unique_hosts > 20, "critical",
    unique_hosts > 10, "high",
    unique_hosts > 5, "medium",
    1==1, "low"
)
| outputlookup lateral_movement_alerts.csv
\`\`\`

### Scheduled Hunting Searches
\`\`\`spl
# Daily APT hunting search
index=* earliest=-24h@h latest=@h
[| inputlookup apt_indicators.csv | fields indicator_value | rename indicator_value as search]
| stats count by index, sourcetype, host
| where count > 0
| eval hunt_date=strftime(now(), "%Y-%m-%d")
| outputlookup append=true apt_hunting_results.csv
\`\`\`

### Alert Actions and Responses
\`\`\`python
# Custom alert action script
import splunk.Intersplunk as si
import requests

def send_to_soar(results):
    for result in results:
        payload = {
            "alert_type": "threat_hunting",
            "severity": result.get("severity", "medium"),
            "indicators": result.get("indicators", ""),
            "timestamp": result.get("_time", "")
        }
        requests.post("https://soar-platform/api/alerts", json=payload)

results, dummyresults, settings = si.getOrganizedResults()
send_to_soar(results)
si.outputResults(results)
\`\`\`
      `,
      activities: [
        'Create correlation searches for automated hunting',
        'Build scheduled hunting workflows',
        'Develop custom alert actions',
        'Integrate with SOAR platforms'
      ]
    },

    {
      id: 'advanced-analytics',
      title: 'Advanced Analytics & Machine Learning',
      content: `
## Statistical Analysis for Threat Hunting

### Outlier Detection
\`\`\`spl
# Statistical outlier detection
index=network sourcetype=netflow
| stats sum(bytes) as total_bytes by src_ip
| eventstats avg(total_bytes) as avg_bytes, stdev(total_bytes) as stdev_bytes
| eval z_score=abs(total_bytes-avg_bytes)/stdev_bytes
| where z_score > 3
| sort -z_score
\`\`\`

### Time Series Analysis
\`\`\`spl
# Anomalous time patterns
index=authentication
| bucket _time span=1h
| stats count by _time, user
| predict count as predicted_count
| eval anomaly_score=abs(count-predicted_count)/predicted_count
| where anomaly_score > 0.5
\`\`\`

### Clustering Analysis
\`\`\`spl
# Behavioral clustering
index=endpoint sourcetype=sysmon EventCode=1
| stats count by Image, CommandLine, ParentImage
| cluster field=CommandLine k=10
| stats values(Image) as processes, values(CommandLine) as commands by cluster
\`\`\`

### Machine Learning Models
\`\`\`spl
# Train anomaly detection model
| inputlookup baseline_network_traffic.csv
| fit DensityFunction bytes_per_second into network_anomaly_model

# Apply model for hunting
index=network
| apply network_anomaly_model
| where "IsOutlier(bytes_per_second)"="True"
| stats count by src_ip, dest_ip, dest_port
\`\`\`
      `,
      activities: [
        'Implement statistical outlier detection',
        'Build time series anomaly detection',
        'Create behavioral clustering models',
        'Deploy machine learning for hunting'
      ]
    },

    {
      id: 'hunting-dashboards',
      title: 'Hunting Dashboards & Visualizations',
      content: `
## Executive Hunting Dashboard

### Key Metrics Panel
\`\`\`xml
<dashboard>
  <label>Threat Hunting Operations Center</label>
  <row>
    <panel>
      <title>Hunting Metrics</title>
      <single>
        <search>
          <query>
            | rest /services/saved/searches
            | search title="TH-*"
            | stats count as active_hunts
          </query>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">none</option>
      </single>
    </panel>
  </row>
</dashboard>
\`\`\`

### Threat Landscape Visualization
\`\`\`spl
# Geographic threat distribution
index=security
| iplocation src_ip
| geostats count by Country
| eval threat_level=case(
    count > 1000, "critical",
    count > 500, "high",
    count > 100, "medium",
    1==1, "low"
)
\`\`\`

### Hunt Progress Tracking
\`\`\`spl
# Hunting campaign progress
| inputlookup hunting_campaigns.csv
| eval days_active=round((now()-start_time)/86400,0)
| eval completion_rate=findings/total_indicators*100
| table campaign_name, days_active, completion_rate, status
\`\`\`
      `,
      activities: [
        'Build executive hunting dashboards',
        'Create threat landscape visualizations',
        'Develop hunt progress tracking',
        'Design operational hunting interfaces'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'APT Hunting Campaign',
      description: 'Conduct a comprehensive APT hunting campaign using Splunk',
      tasks: [
        'Set up APT indicator feeds in Splunk',
        'Create hunting queries for each MITRE ATT&CK technique',
        'Build correlation searches for APT behavior patterns',
        'Develop automated alerting and response workflows',
        'Create executive reporting dashboard'
      ],
      timeEstimate: '8 hours',
      difficulty: 'Advanced'
    },
    {
      title: 'Insider Threat Detection',
      description: 'Build comprehensive insider threat hunting capabilities',
      tasks: [
        'Analyze user behavior patterns',
        'Create anomaly detection for data access',
        'Build privilege escalation detection',
        'Develop data exfiltration hunting queries',
        'Create insider threat risk scoring'
      ],
      timeEstimate: '6 hours',
      difficulty: 'Intermediate'
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Splunk Hunting Mastery Assessment',
      description: 'Demonstrate comprehensive Splunk hunting capabilities',
      tasks: [
        'Build 10 different hunting queries covering various attack techniques',
        'Create automated hunting workflow with correlation searches',
        'Develop custom hunting dashboard with key metrics',
        'Integrate threat intelligence feeds',
        'Present findings and recommendations'
      ]
    }
  ],

  resources: [
    {
      type: 'documentation',
      title: 'Splunk Security Essentials',
      url: 'https://splunkbase.splunk.com/app/3435/',
      description: 'Official Splunk security use cases and searches'
    },
    {
      type: 'tool',
      title: 'Splunk Attack Range',
      url: 'https://github.com/splunk/attack_range',
      description: 'Tool for building instrumented environments for security research'
    },
    {
      type: 'reference',
      title: 'SPL Reference Guide',
      url: 'https://docs.splunk.com/Documentation/Splunk/latest/SearchReference',
      description: 'Comprehensive SPL command reference'
    }
  ],

  tools: [
    'Splunk Enterprise Security',
    'Splunk SOAR',
    'Splunk Machine Learning Toolkit',
    'Splunk Security Essentials',
    'Splunk Attack Range',
    'Custom hunting apps and add-ons'
  ],

  prerequisites: [
    'Basic Splunk administration knowledge',
    'Understanding of SPL fundamentals',
    'Network security concepts',
    'Log analysis experience'
  ]
};
