/**
 * TH-23: Mobile Threat Hunting
 * Master mobile device security and threat hunting for iOS and Android platforms
 */

export const mobileHuntingContent = {
  id: 'th-23',
  title: 'Mobile Threat Hunting',
  description: 'Master comprehensive mobile threat hunting techniques for iOS and Android platforms, including malware detection, privacy violations, and enterprise mobile security.',
  duration: '42 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Master iOS and Android security architectures and attack vectors',
    'Implement mobile malware detection and analysis techniques',
    'Build mobile device forensics and investigation capabilities',
    'Develop mobile application security assessment skills',
    'Create mobile threat intelligence and IOC management',
    'Implement enterprise mobile device management (MDM) security',
    'Build automated mobile threat detection and response systems'
  ],

  sections: [
    {
      id: 'mobile-security-fundamentals',
      title: 'Mobile Security Architecture and Threats',
      content: `
## Mobile Platform Security Analysis

### **iOS Security Architecture**
\`\`\`yaml
iOS Security Layers:
  Hardware Security:
    - Secure Enclave
    - Hardware encryption
    - Secure boot chain
    - Touch ID/Face ID biometrics
    
  Kernel and OS Security:
    - XNU kernel with KASLR
    - System Integrity Protection (SIP)
    - Mandatory Access Control (MAC)
    - Code signing enforcement
    
  Application Security:
    - App Store review process
    - Code signing requirements
    - Sandboxing and entitlements
    - Runtime protections
    
  Data Protection:
    - File system encryption
    - Keychain services
    - App Transport Security (ATS)
    - Certificate pinning

iOS Attack Vectors:
  Jailbreaking:
    - Kernel exploits
    - Bootchain vulnerabilities
    - Privilege escalation
    
  Malicious Apps:
    - Enterprise certificate abuse
    - Side-loading attacks
    - Configuration profile abuse
    
  Network Attacks:
    - Man-in-the-middle
    - Certificate pinning bypass
    - Wi-Fi attacks
    
  Physical Attacks:
    - Device extraction
    - Passcode bypass
    - Hardware tampering
\`\`\`

### **Android Security Architecture**
\`\`\`yaml
Android Security Layers:
  Hardware Security:
    - Trusted Execution Environment (TEE)
    - Hardware-backed keystore
    - Verified boot
    - Hardware security module (HSM)
    
  Linux Kernel Security:
    - SELinux mandatory access control
    - Kernel hardening features
    - Control Flow Integrity (CFI)
    - Kernel Address Space Layout Randomization
    
  Android Framework Security:
    - Application sandboxing
    - Permission model
    - Inter-process communication (IPC) security
    - Runtime application self-protection
    
  Application Security:
    - APK signing
    - Google Play Protect
    - Runtime permissions
    - Network security config

Android Attack Vectors:
  Rooting:
    - Kernel exploits
    - Bootloader unlocking
    - Custom ROM installation
    
  Malicious Apps:
    - Side-loading from unknown sources
    - Repackaged legitimate apps
    - Privilege escalation exploits
    
  Network Attacks:
    - SSL/TLS interception
    - Rogue access points
    - SMS/MMS attacks
    
  Physical Attacks:
    - ADB debugging abuse
    - Bootloader exploitation
    - Hardware modification
\`\`\`

### **Mobile Threat Hunting Framework**
\`\`\`python
import json
import hashlib
import subprocess
from datetime import datetime
from pathlib import Path

class MobileThreatHunter:
    def __init__(self, platform='android'):
        self.platform = platform
        self.threat_indicators = {
            'malware_signatures': [],
            'suspicious_permissions': [],
            'network_indicators': [],
            'behavioral_patterns': []
        }
        
    def analyze_mobile_device(self, device_data):
        """Comprehensive mobile device threat analysis"""
        analysis_results = {
            'device_info': device_data.get('device_info', {}),
            'analysis_timestamp': datetime.now().isoformat(),
            'platform': self.platform,
            'threats_detected': [],
            'risk_score': 0
        }
        
        # Analyze installed applications
        app_threats = self._analyze_applications(device_data.get('applications', []))
        analysis_results['threats_detected'].extend(app_threats)
        
        # Analyze network connections
        network_threats = self._analyze_network_activity(device_data.get('network_activity', []))
        analysis_results['threats_detected'].extend(network_threats)
        
        # Analyze system configuration
        config_threats = self._analyze_system_configuration(device_data.get('system_config', {}))
        analysis_results['threats_detected'].extend(config_threats)
        
        # Analyze file system
        filesystem_threats = self._analyze_filesystem(device_data.get('filesystem', []))
        analysis_results['threats_detected'].extend(filesystem_threats)
        
        # Calculate overall risk score
        analysis_results['risk_score'] = self._calculate_mobile_risk_score(
            analysis_results['threats_detected']
        )
        
        return analysis_results
    
    def _analyze_applications(self, applications):
        """Analyze installed applications for threats"""
        threats = []
        
        for app in applications:
            app_threats = []
            
            # Check for suspicious permissions
            permissions = app.get('permissions', [])
            suspicious_perms = self._check_suspicious_permissions(permissions)
            if suspicious_perms:
                app_threats.append({
                    'type': 'Suspicious Permissions',
                    'permissions': suspicious_perms,
                    'severity': 'medium'
                })
            
            # Check for malware signatures
            app_hash = app.get('hash')
            if app_hash and self._check_malware_signature(app_hash):
                app_threats.append({
                    'type': 'Known Malware',
                    'hash': app_hash,
                    'severity': 'critical'
                })
            
            # Check for repackaged apps
            if self._detect_repackaged_app(app):
                app_threats.append({
                    'type': 'Repackaged Application',
                    'original_app': app.get('original_signature'),
                    'severity': 'high'
                })
            
            # Check for code obfuscation
            if app.get('obfuscated', False):
                app_threats.append({
                    'type': 'Code Obfuscation',
                    'obfuscation_level': app.get('obfuscation_level'),
                    'severity': 'medium'
                })
            
            if app_threats:
                threats.append({
                    'app_name': app.get('name'),
                    'package_name': app.get('package_name'),
                    'version': app.get('version'),
                    'threats': app_threats
                })
        
        return threats
    
    def _check_suspicious_permissions(self, permissions):
        """Check for suspicious permission combinations"""
        high_risk_permissions = {
            'android': [
                'android.permission.SEND_SMS',
                'android.permission.READ_SMS',
                'android.permission.RECEIVE_SMS',
                'android.permission.READ_CONTACTS',
                'android.permission.ACCESS_FINE_LOCATION',
                'android.permission.RECORD_AUDIO',
                'android.permission.CAMERA',
                'android.permission.READ_CALL_LOG',
                'android.permission.WRITE_EXTERNAL_STORAGE',
                'android.permission.SYSTEM_ALERT_WINDOW',
                'android.permission.DEVICE_ADMIN'
            ],
            'ios': [
                'NSLocationAlwaysUsageDescription',
                'NSContactsUsageDescription',
                'NSMicrophoneUsageDescription',
                'NSCameraUsageDescription',
                'NSPhotoLibraryUsageDescription',
                'NSCalendarsUsageDescription'
            ]
        }
        
        platform_permissions = high_risk_permissions.get(self.platform, [])
        suspicious = [perm for perm in permissions if perm in platform_permissions]
        
        # Check for dangerous permission combinations
        if 'android.permission.SEND_SMS' in permissions and 'android.permission.RECEIVE_SMS' in permissions:
            suspicious.append('SMS_FULL_ACCESS_COMBINATION')
        
        if 'android.permission.RECORD_AUDIO' in permissions and 'android.permission.ACCESS_FINE_LOCATION' in permissions:
            suspicious.append('AUDIO_LOCATION_COMBINATION')
        
        return suspicious
    
    def _analyze_network_activity(self, network_activity):
        """Analyze network activity for threats"""
        threats = []
        
        # Check for connections to known malicious domains
        malicious_domains = self._load_malicious_domains()
        
        for connection in network_activity:
            destination = connection.get('destination')
            
            if destination in malicious_domains:
                threats.append({
                    'type': 'Malicious Domain Connection',
                    'destination': destination,
                    'timestamp': connection.get('timestamp'),
                    'bytes_transferred': connection.get('bytes_transferred'),
                    'severity': 'high'
                })
            
            # Check for suspicious data exfiltration
            bytes_transferred = connection.get('bytes_transferred', 0)
            if bytes_transferred > 100 * 1024 * 1024:  # 100MB threshold
                threats.append({
                    'type': 'Large Data Transfer',
                    'destination': destination,
                    'bytes_transferred': bytes_transferred,
                    'severity': 'medium'
                })
            
            # Check for unusual protocols
            protocol = connection.get('protocol', '').lower()
            if protocol in ['tor', 'i2p', 'freenet']:
                threats.append({
                    'type': 'Anonymous Network Usage',
                    'protocol': protocol,
                    'destination': destination,
                    'severity': 'medium'
                })
        
        return threats
    
    def _analyze_system_configuration(self, system_config):
        """Analyze system configuration for security issues"""
        threats = []
        
        if self.platform == 'android':
            # Check for rooting indicators
            if system_config.get('rooted', False):
                threats.append({
                    'type': 'Device Rooted',
                    'root_method': system_config.get('root_method'),
                    'severity': 'high'
                })
            
            # Check for unknown sources enabled
            if system_config.get('unknown_sources_enabled', False):
                threats.append({
                    'type': 'Unknown Sources Enabled',
                    'severity': 'medium'
                })
            
            # Check for USB debugging enabled
            if system_config.get('usb_debugging_enabled', False):
                threats.append({
                    'type': 'USB Debugging Enabled',
                    'severity': 'medium'
                })
        
        elif self.platform == 'ios':
            # Check for jailbreak indicators
            if system_config.get('jailbroken', False):
                threats.append({
                    'type': 'Device Jailbroken',
                    'jailbreak_method': system_config.get('jailbreak_method'),
                    'severity': 'high'
                })
            
            # Check for enterprise certificates
            enterprise_certs = system_config.get('enterprise_certificates', [])
            if enterprise_certs:
                threats.append({
                    'type': 'Enterprise Certificates Installed',
                    'certificates': enterprise_certs,
                    'severity': 'medium'
                })
        
        # Check for outdated OS version
        os_version = system_config.get('os_version')
        if self._is_outdated_os(os_version):
            threats.append({
                'type': 'Outdated Operating System',
                'current_version': os_version,
                'severity': 'medium'
            })
        
        return threats

### **Mobile Malware Analysis**
\`\`\`python
class MobileMalwareAnalyzer:
    def __init__(self):
        self.malware_families = {
            'android': {
                'banking_trojans': ['Anubis', 'Cerberus', 'Flubot', 'Medusa'],
                'spyware': ['Pegasus', 'Chrysaor', 'Skygofree', 'RedDrop'],
                'ransomware': ['Android/Filecoder', 'DoubleLocker', 'Koler'],
                'adware': ['HiddenAds', 'AdDisplay', 'MobiDash']
            },
            'ios': {
                'spyware': ['Pegasus', 'Chrysaor', 'Trident'],
                'adware': ['AdThief', 'YiSpecter'],
                'trojans': ['XcodeGhost', 'AceDeceiver']
            }
        }
        
    def analyze_mobile_malware(self, app_data):
        """Analyze mobile application for malware characteristics"""
        analysis_result = {
            'app_info': {
                'name': app_data.get('name'),
                'package': app_data.get('package_name'),
                'version': app_data.get('version'),
                'hash': app_data.get('hash')
            },
            'malware_indicators': [],
            'family_classification': None,
            'risk_score': 0
        }
        
        # Static analysis
        static_indicators = self._static_analysis(app_data)
        analysis_result['malware_indicators'].extend(static_indicators)
        
        # Dynamic analysis indicators
        dynamic_indicators = self._dynamic_analysis(app_data)
        analysis_result['malware_indicators'].extend(dynamic_indicators)
        
        # Classify malware family
        analysis_result['family_classification'] = self._classify_malware_family(
            analysis_result['malware_indicators']
        )
        
        # Calculate risk score
        analysis_result['risk_score'] = self._calculate_malware_risk_score(
            analysis_result['malware_indicators']
        )
        
        return analysis_result
    
    def _static_analysis(self, app_data):
        """Perform static analysis of mobile application"""
        indicators = []
        
        # Check for suspicious API calls
        api_calls = app_data.get('api_calls', [])
        suspicious_apis = [
            'sendTextMessage', 'getDeviceId', 'getLocation',
            'startService', 'bindService', 'registerReceiver'
        ]
        
        for api in api_calls:
            if api in suspicious_apis:
                indicators.append({
                    'type': 'Suspicious API Call',
                    'api': api,
                    'category': 'static_analysis'
                })
        
        # Check for suspicious strings
        strings = app_data.get('strings', [])
        suspicious_strings = [
            'su', 'busybox', '/system/bin', '/system/xbin',
            'root', 'superuser', 'cydia', 'jailbreak'
        ]
        
        for string in strings:
            if any(sus_str in string.lower() for sus_str in suspicious_strings):
                indicators.append({
                    'type': 'Suspicious String',
                    'string': string,
                    'category': 'static_analysis'
                })
        
        # Check for code obfuscation
        if app_data.get('obfuscated', False):
            indicators.append({
                'type': 'Code Obfuscation',
                'level': app_data.get('obfuscation_level'),
                'category': 'static_analysis'
            })
        
        return indicators
    
    def _dynamic_analysis(self, app_data):
        """Analyze dynamic behavior indicators"""
        indicators = []
        
        # Check runtime behavior
        runtime_behavior = app_data.get('runtime_behavior', {})
        
        # Network connections
        network_connections = runtime_behavior.get('network_connections', [])
        for connection in network_connections:
            if self._is_suspicious_connection(connection):
                indicators.append({
                    'type': 'Suspicious Network Connection',
                    'destination': connection.get('destination'),
                    'category': 'dynamic_analysis'
                })
        
        # File system operations
        file_operations = runtime_behavior.get('file_operations', [])
        for operation in file_operations:
            if self._is_suspicious_file_operation(operation):
                indicators.append({
                    'type': 'Suspicious File Operation',
                    'operation': operation.get('type'),
                    'path': operation.get('path'),
                    'category': 'dynamic_analysis'
                })
        
        return indicators
\`\`\`
      `,
      activities: [
        'Analyze iOS and Android security architectures',
        'Build mobile threat detection framework',
        'Implement mobile malware analysis techniques',
        'Create mobile device forensics capabilities'
      ]
    },

    {
      id: 'mobile-forensics',
      title: 'Mobile Device Forensics and Investigation',
      content: `
## Advanced Mobile Forensics Techniques

### **Mobile Device Acquisition**
\`\`\`python
import subprocess
import json
import os
from pathlib import Path

class MobileForensicsToolkit:
    def __init__(self):
        self.acquisition_methods = {
            'android': ['adb', 'fastboot', 'odin', 'sp_flash_tool'],
            'ios': ['libimobiledevice', 'idevice', 'checkra1n', '3utools']
        }

    def acquire_android_device(self, device_id, output_dir):
        """Acquire Android device data"""
        acquisition_results = {
            'device_id': device_id,
            'acquisition_timestamp': datetime.now().isoformat(),
            'methods_used': [],
            'data_acquired': {},
            'errors': []
        }

        try:
            # Check device connection
            if not self._check_adb_connection(device_id):
                raise Exception("Device not connected or ADB not enabled")

            # Get device information
            device_info = self._get_android_device_info(device_id)
            acquisition_results['device_info'] = device_info

            # Acquire system information
            system_data = self._acquire_android_system_data(device_id)
            acquisition_results['data_acquired']['system'] = system_data

            # Acquire application data
            app_data = self._acquire_android_app_data(device_id)
            acquisition_results['data_acquired']['applications'] = app_data

            # Acquire user data
            user_data = self._acquire_android_user_data(device_id, output_dir)
            acquisition_results['data_acquired']['user_data'] = user_data

            # Acquire network data
            network_data = self._acquire_android_network_data(device_id)
            acquisition_results['data_acquired']['network'] = network_data

        except Exception as e:
            acquisition_results['errors'].append(str(e))

        return acquisition_results

    def _get_android_device_info(self, device_id):
        """Get Android device information"""
        device_info = {}

        try:
            # Get device properties
            props_cmd = f"adb -s {device_id} shell getprop"
            result = subprocess.run(props_cmd.split(), capture_output=True, text=True)

            if result.returncode == 0:
                props = {}
                for line in result.stdout.split('\\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        props[key.strip('[] ')] = value.strip('[] ')

                device_info = {
                    'model': props.get('ro.product.model', 'Unknown'),
                    'manufacturer': props.get('ro.product.manufacturer', 'Unknown'),
                    'android_version': props.get('ro.build.version.release', 'Unknown'),
                    'api_level': props.get('ro.build.version.sdk', 'Unknown'),
                    'build_id': props.get('ro.build.id', 'Unknown'),
                    'serial': props.get('ro.serialno', 'Unknown'),
                    'bootloader': props.get('ro.bootloader', 'Unknown'),
                    'security_patch': props.get('ro.build.version.security_patch', 'Unknown')
                }

        except Exception as e:
            device_info['error'] = str(e)

        return device_info

    def _acquire_android_app_data(self, device_id):
        """Acquire Android application data"""
        app_data = []

        try:
            # Get installed packages
            packages_cmd = f"adb -s {device_id} shell pm list packages -f"
            result = subprocess.run(packages_cmd.split(), capture_output=True, text=True)

            if result.returncode == 0:
                for line in result.stdout.split('\\n'):
                    if line.startswith('package:'):
                        parts = line.split('=')
                        if len(parts) == 2:
                            apk_path = parts[0].replace('package:', '')
                            package_name = parts[1]

                            # Get package information
                            app_info = self._get_android_package_info(device_id, package_name)
                            app_info['apk_path'] = apk_path
                            app_data.append(app_info)

        except Exception as e:
            app_data.append({'error': str(e)})

        return app_data

    def _get_android_package_info(self, device_id, package_name):
        """Get detailed Android package information"""
        package_info = {'package_name': package_name}

        try:
            # Get package details
            info_cmd = f"adb -s {device_id} shell dumpsys package {package_name}"
            result = subprocess.run(info_cmd.split(), capture_output=True, text=True)

            if result.returncode == 0:
                output = result.stdout

                # Parse version information
                if 'versionName=' in output:
                    version_line = [line for line in output.split('\\n') if 'versionName=' in line][0]
                    package_info['version'] = version_line.split('versionName=')[1].split()[0]

                # Parse permissions
                permissions = []
                in_permissions = False
                for line in output.split('\\n'):
                    if 'requested permissions:' in line:
                        in_permissions = True
                        continue
                    elif in_permissions and line.startswith('    '):
                        perm = line.strip()
                        if perm.startswith('android.permission.'):
                            permissions.append(perm)
                    elif in_permissions and not line.startswith('    '):
                        break

                package_info['permissions'] = permissions

                # Check if app is system app
                package_info['is_system_app'] = '/system/' in output

        except Exception as e:
            package_info['error'] = str(e)

        return package_info

    def acquire_ios_device(self, device_udid, output_dir):
        """Acquire iOS device data"""
        acquisition_results = {
            'device_udid': device_udid,
            'acquisition_timestamp': datetime.now().isoformat(),
            'methods_used': [],
            'data_acquired': {},
            'errors': []
        }

        try:
            # Check device connection
            if not self._check_ios_connection(device_udid):
                raise Exception("Device not connected or not trusted")

            # Get device information
            device_info = self._get_ios_device_info(device_udid)
            acquisition_results['device_info'] = device_info

            # Acquire application data
            app_data = self._acquire_ios_app_data(device_udid)
            acquisition_results['data_acquired']['applications'] = app_data

            # Acquire backup data (if available)
            backup_data = self._acquire_ios_backup_data(device_udid, output_dir)
            acquisition_results['data_acquired']['backup'] = backup_data

        except Exception as e:
            acquisition_results['errors'].append(str(e))

        return acquisition_results

    def _get_ios_device_info(self, device_udid):
        """Get iOS device information"""
        device_info = {}

        try:
            # Use ideviceinfo to get device details
            info_cmd = f"ideviceinfo -u {device_udid}"
            result = subprocess.run(info_cmd.split(), capture_output=True, text=True)

            if result.returncode == 0:
                for line in result.stdout.split('\\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        device_info[key.strip()] = value.strip()

        except Exception as e:
            device_info['error'] = str(e)

        return device_info

### **Mobile Network Analysis**
\`\`\`python
class MobileNetworkAnalyzer:
    def __init__(self):
        self.suspicious_domains = set()
        self.malware_c2_servers = set()
        self.load_threat_intelligence()

    def analyze_mobile_network_traffic(self, traffic_data):
        """Analyze mobile network traffic for threats"""
        analysis_results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_connections': len(traffic_data),
            'threats_detected': [],
            'risk_assessment': {}
        }

        # Analyze DNS queries
        dns_threats = self._analyze_dns_queries(traffic_data)
        analysis_results['threats_detected'].extend(dns_threats)

        # Analyze HTTP/HTTPS traffic
        http_threats = self._analyze_http_traffic(traffic_data)
        analysis_results['threats_detected'].extend(http_threats)

        # Analyze data exfiltration patterns
        exfiltration_threats = self._analyze_data_exfiltration(traffic_data)
        analysis_results['threats_detected'].extend(exfiltration_threats)

        # Analyze certificate anomalies
        cert_threats = self._analyze_certificate_anomalies(traffic_data)
        analysis_results['threats_detected'].extend(cert_threats)

        # Calculate risk assessment
        analysis_results['risk_assessment'] = self._calculate_network_risk_assessment(
            analysis_results['threats_detected']
        )

        return analysis_results

    def _analyze_dns_queries(self, traffic_data):
        """Analyze DNS queries for malicious domains"""
        threats = []

        dns_queries = [conn for conn in traffic_data if conn.get('protocol') == 'DNS']

        for query in dns_queries:
            domain = query.get('query_name', '').lower()

            # Check against known malicious domains
            if domain in self.suspicious_domains:
                threats.append({
                    'type': 'Malicious Domain Query',
                    'domain': domain,
                    'timestamp': query.get('timestamp'),
                    'source_app': query.get('source_app'),
                    'severity': 'high'
                })

            # Check for DGA (Domain Generation Algorithm) patterns
            if self._is_dga_domain(domain):
                threats.append({
                    'type': 'Potential DGA Domain',
                    'domain': domain,
                    'dga_score': self._calculate_dga_score(domain),
                    'timestamp': query.get('timestamp'),
                    'severity': 'medium'
                })

            # Check for DNS tunneling
            if self._is_dns_tunneling(query):
                threats.append({
                    'type': 'DNS Tunneling',
                    'domain': domain,
                    'query_size': query.get('query_size'),
                    'response_size': query.get('response_size'),
                    'severity': 'high'
                })

        return threats

    def _is_dga_domain(self, domain):
        """Check if domain matches DGA patterns"""
        # Simple DGA detection based on entropy and patterns
        if len(domain) < 6:
            return False

        # Calculate character entropy
        char_counts = {}
        for char in domain:
            char_counts[char] = char_counts.get(char, 0) + 1

        entropy = 0
        domain_length = len(domain)
        for count in char_counts.values():
            probability = count / domain_length
            entropy -= probability * (probability.bit_length() - 1)

        # High entropy suggests DGA
        return entropy > 3.5

    def _analyze_http_traffic(self, traffic_data):
        """Analyze HTTP/HTTPS traffic for threats"""
        threats = []

        http_connections = [
            conn for conn in traffic_data
            if conn.get('protocol') in ['HTTP', 'HTTPS']
        ]

        for connection in http_connections:
            # Check for suspicious user agents
            user_agent = connection.get('user_agent', '')
            if self._is_suspicious_user_agent(user_agent):
                threats.append({
                    'type': 'Suspicious User Agent',
                    'user_agent': user_agent,
                    'destination': connection.get('destination'),
                    'severity': 'medium'
                })

            # Check for data exfiltration patterns
            if connection.get('method') == 'POST':
                payload_size = connection.get('payload_size', 0)
                if payload_size > 1024 * 1024:  # 1MB threshold
                    threats.append({
                        'type': 'Large POST Request',
                        'destination': connection.get('destination'),
                        'payload_size': payload_size,
                        'severity': 'medium'
                    })

        return threats
\`\`\`
      `,
      activities: [
        'Implement mobile device acquisition techniques',
        'Build mobile forensics analysis framework',
        'Create mobile network traffic analysis',
        'Develop mobile threat intelligence integration'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Mobile Malware Analysis Laboratory',
      description: 'Analyze sophisticated mobile malware samples',
      tasks: [
        'Set up mobile malware analysis environment',
        'Analyze Android banking trojan samples',
        'Investigate iOS spyware capabilities',
        'Build mobile malware detection signatures'
      ]
    },
    {
      title: 'Mobile Device Forensics Investigation',
      description: 'Conduct comprehensive mobile device forensics investigation',
      tasks: [
        'Acquire mobile device data using multiple methods',
        'Analyze application data and user artifacts',
        'Reconstruct mobile device timeline',
        'Generate forensics investigation report'
      ]
    },
    {
      title: 'Enterprise Mobile Security Assessment',
      description: 'Assess and improve enterprise mobile security posture',
      tasks: [
        'Evaluate mobile device management (MDM) security',
        'Assess mobile application security policies',
        'Implement mobile threat detection system',
        'Create mobile security awareness program'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Mobile Threat Hunting Mastery',
      description: 'Demonstrate advanced mobile threat hunting and forensics capabilities'
    },
    {
      type: 'project',
      title: 'Enterprise Mobile Security Solution',
      description: 'Design and implement comprehensive mobile security and threat hunting solution'
    }
  ],

  resources: [
    'Mobile Security Testing Guide (MSTG)',
    'iOS and Android Security Architecture Documentation',
    'Mobile Malware Analysis Tools and Techniques',
    'Mobile Device Forensics Best Practices',
    'Enterprise Mobile Device Management Security',
    'Mobile Threat Intelligence Sources'
  ]
};
