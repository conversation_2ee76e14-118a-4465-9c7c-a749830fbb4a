/**
 * TH-13: <PERSON><PERSON><PERSON>ack for Threat Hunting
 * Master Elasticsearch, Logstash, and Kibana for comprehensive threat hunting
 */

export const elkStackHuntingContent = {
  id: 'th-13',
  title: 'ELK Stack for Threat Hunting',
  description: 'Master Elasticsearch, Logstash, and <PERSON><PERSON> for comprehensive threat hunting operations, from data ingestion to advanced analytics.',
  duration: '42 hours',
  difficulty: 'Intermediate to Advanced',
  
  objectives: [
    'Master Elasticsearch Query DSL for threat hunting',
    'Build advanced Kibana dashboards and visualizations',
    'Configure Logstash for security data processing',
    'Implement Beats for comprehensive data collection',
    'Create automated hunting workflows with <PERSON><PERSON>',
    'Develop custom ELK hunting tools and integrations',
    'Build scalable ELK hunting architecture'
  ],

  sections: [
    {
      id: 'elasticsearch-hunting',
      title: 'Elasticsearch for Threat Hunting',
      content: `
## Elasticsearch Query DSL Mastery

### **Basic Hunting Queries**
\`\`\`json
# Search for suspicious PowerShell activity
GET /winlogbeat-*/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "match": {
            "event.code": "4688"
          }
        },
        {
          "wildcard": {
            "process.command_line": "*powershell*"
          }
        }
      ],
      "filter": [
        {
          "range": {
            "@timestamp": {
              "gte": "now-24h"
            }
          }
        }
      ]
    }
  },
  "sort": [
    {
      "@timestamp": {
        "order": "desc"
      }
    }
  ]
}

# Hunt for lateral movement via RDP
GET /winlogbeat-*/_search
{
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "event.code": ["4624", "4625"]
          }
        },
        {
          "match": {
            "winlog.event_data.LogonType": "10"
          }
        }
      ]
    }
  },
  "aggs": {
    "users_by_source": {
      "terms": {
        "field": "user.name.keyword",
        "size": 100
      },
      "aggs": {
        "source_ips": {
          "terms": {
            "field": "source.ip",
            "size": 50
          }
        }
      }
    }
  }
}
\`\`\`

### **Advanced Behavioral Analysis**
\`\`\`json
# Detect beaconing behavior using aggregations
GET /packetbeat-*/_search
{
  "size": 0,
  "query": {
    "range": {
      "@timestamp": {
        "gte": "now-1h"
      }
    }
  },
  "aggs": {
    "connections": {
      "composite": {
        "sources": [
          {
            "src_ip": {
              "terms": {
                "field": "source.ip"
              }
            }
          },
          {
            "dst_ip": {
              "terms": {
                "field": "destination.ip"
              }
            }
          },
          {
            "dst_port": {
              "terms": {
                "field": "destination.port"
              }
            }
          }
        ]
      },
      "aggs": {
        "connection_times": {
          "date_histogram": {
            "field": "@timestamp",
            "fixed_interval": "1m"
          }
        },
        "bytes_stats": {
          "stats": {
            "field": "network.bytes"
          }
        },
        "regularity_score": {
          "bucket_script": {
            "buckets_path": {
              "count": "connection_times>_count",
              "bytes_avg": "bytes_stats>avg",
              "bytes_std": "bytes_stats>std_deviation"
            },
            "script": "params.count > 10 && params.bytes_std < (params.bytes_avg * 0.1) ? 1 : 0"
          }
        }
      }
    }
  }
}

# Hunt for data exfiltration patterns
GET /filebeat-*/_search
{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "range": {
            "@timestamp": {
              "gte": "now-24h"
            }
          }
        },
        {
          "exists": {
            "field": "network.bytes"
          }
        }
      ]
    }
  },
  "aggs": {
    "users": {
      "terms": {
        "field": "user.name.keyword",
        "size": 100
      },
      "aggs": {
        "total_bytes": {
          "sum": {
            "field": "network.bytes"
          }
        },
        "unique_destinations": {
          "cardinality": {
            "field": "destination.ip"
          }
        },
        "time_distribution": {
          "date_histogram": {
            "field": "@timestamp",
            "fixed_interval": "1h"
          }
        },
        "anomaly_score": {
          "bucket_script": {
            "buckets_path": {
              "bytes": "total_bytes",
              "destinations": "unique_destinations"
            },
            "script": "params.bytes > 1000000000 && params.destinations > 10 ? 10 : 0"
          }
        }
      }
    }
  }
}
\`\`\`

### **Machine Learning Integration**
\`\`\`json
# Create ML job for anomaly detection
PUT _ml/anomaly_detectors/network_anomaly_detection
{
  "description": "Detect network traffic anomalies",
  "analysis_config": {
    "bucket_span": "15m",
    "detectors": [
      {
        "function": "high_count",
        "field_name": "network.bytes",
        "by_field_name": "source.ip"
      },
      {
        "function": "rare",
        "field_name": "destination.port",
        "by_field_name": "source.ip"
      }
    ],
    "influencers": ["source.ip", "destination.ip", "user.name"]
  },
  "data_description": {
    "time_field": "@timestamp",
    "time_format": "epoch_ms"
  }
}

# Start ML job
POST _ml/anomaly_detectors/network_anomaly_detection/_open

# Create datafeed
PUT _ml/datafeeds/datafeed-network_anomaly_detection
{
  "job_id": "network_anomaly_detection",
  "indices": ["packetbeat-*"],
  "query": {
    "match_all": {}
  }
}

# Start datafeed
POST _ml/datafeeds/datafeed-network_anomaly_detection/_start
\`\`\`
      `,
      activities: [
        'Build complex Elasticsearch hunting queries',
        'Implement behavioral analysis with aggregations',
        'Set up machine learning anomaly detection',
        'Create custom search templates for hunting'
      ]
    },

    {
      id: 'kibana-dashboards',
      title: 'Kibana Hunting Dashboards',
      content: `
## Advanced Kibana Visualizations

### **Threat Hunting Dashboard Design**
\`\`\`json
{
  "version": "7.15.0",
  "objects": [
    {
      "id": "threat-hunting-overview",
      "type": "dashboard",
      "attributes": {
        "title": "Threat Hunting Overview",
        "description": "Comprehensive threat hunting dashboard",
        "panelsJSON": "[
          {
            \"version\":\"7.15.0\",
            \"panelIndex\":\"1\",
            \"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15},
            \"panelRefName\":\"panel_1\"
          },
          {
            \"version\":\"7.15.0\",
            \"panelIndex\":\"2\",
            \"gridData\":{\"x\":24,\"y\":0,\"w\":24,\"h\":15},
            \"panelRefName\":\"panel_2\"
          }
        ]",
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"match_all\":{}},\"filter\":[]}"
        }
      },
      "references": [
        {
          "name": "panel_1",
          "type": "visualization",
          "id": "suspicious-processes-timeline"
        },
        {
          "name": "panel_2",
          "type": "visualization",
          "id": "network-connections-heatmap"
        }
      ]
    }
  ]
}

# Suspicious Processes Timeline Visualization
{
  "id": "suspicious-processes-timeline",
  "type": "visualization",
  "attributes": {
    "title": "Suspicious Processes Timeline",
    "visState": "{
      \"title\":\"Suspicious Processes Timeline\",
      \"type\":\"histogram\",
      \"params\":{
        \"grid\":{\"categoryLines\":false,\"style\":{\"color\":\"#eee\"}},
        \"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"truncate\":100},\"title\":{}}],
        \"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Count\"}}],
        \"seriesParams\":[{\"show\":true,\"type\":\"histogram\",\"mode\":\"stacked\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"showCircles\":true}],
        \"addTooltip\":true,
        \"addLegend\":true,
        \"legendPosition\":\"right\",
        \"times\":[],
        \"addTimeMarker\":false
      },
      \"aggs\":[
        {\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},
        {\"id\":\"2\",\"enabled\":true,\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"customInterval\":\"2h\",\"min_doc_count\":1,\"extended_bounds\":{}}},
        {\"id\":\"3\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"group\",\"params\":{\"field\":\"process.name.keyword\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}}
      ]
    }",
    "kibanaSavedObjectMeta": {
      "searchSourceJSON": "{
        \"index\":\"winlogbeat-*\",
        \"query\":{
          \"bool\":{
            \"must\":[
              {\"match\":{\"event.code\":\"4688\"}},
              {\"bool\":{
                \"should\":[
                  {\"wildcard\":{\"process.command_line\":\"*powershell*\"}},
                  {\"wildcard\":{\"process.command_line\":\"*cmd*\"}},
                  {\"wildcard\":{\"process.name\":\"*rundll32*\"}},
                  {\"wildcard\":{\"process.name\":\"*regsvr32*\"}}
                ]
              }}
            ]
          }
        },
        \"filter\":[]
      }"
    }
  }
}
\`\`\`

### **Custom Kibana Plugins for Hunting**
\`\`\`javascript
// Custom Kibana plugin for threat hunting
import { Plugin, CoreSetup, CoreStart } from 'kibana/public';

export class ThreatHuntingPlugin implements Plugin {
  public setup(core: CoreSetup) {
    // Register hunting application
    core.application.register({
      id: 'threatHunting',
      title: 'Threat Hunting',
      async mount(params) {
        const { renderApp } = await import('./application');
        return renderApp(params);
      },
    });
  }

  public start(core: CoreStart) {
    return {};
  }

  public stop() {}
}

// Threat hunting application component
import React, { useState, useEffect } from 'react';
import { EuiPage, EuiPageBody, EuiPageContent, EuiTitle } from '@elastic/eui';

export const ThreatHuntingApp = () => {
  const [huntingResults, setHuntingResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const executeHunt = async (huntQuery) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/threat-hunting/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: huntQuery }),
      });
      
      const results = await response.json();
      setHuntingResults(results);
    } catch (error) {
      console.error('Hunt execution failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <EuiPage>
      <EuiPageBody>
        <EuiPageContent>
          <EuiTitle>
            <h1>Threat Hunting Console</h1>
          </EuiTitle>
          
          <HuntingQueryBuilder onExecute={executeHunt} />
          <HuntingResults results={huntingResults} loading={isLoading} />
        </EuiPageContent>
      </EuiPageBody>
    </EuiPage>
  );
};
\`\`\`

### **Watcher for Automated Hunting**
\`\`\`json
# Create automated hunting watcher
PUT _watcher/watch/suspicious_powershell_activity
{
  "trigger": {
    "schedule": {
      "interval": "5m"
    }
  },
  "input": {
    "search": {
      "request": {
        "search_type": "query_then_fetch",
        "indices": ["winlogbeat-*"],
        "body": {
          "query": {
            "bool": {
              "must": [
                {
                  "match": {
                    "event.code": "4688"
                  }
                },
                {
                  "bool": {
                    "should": [
                      {
                        "wildcard": {
                          "process.command_line": "*-enc*"
                        }
                      },
                      {
                        "wildcard": {
                          "process.command_line": "*bypass*"
                        }
                      },
                      {
                        "wildcard": {
                          "process.command_line": "*downloadstring*"
                        }
                      }
                    ]
                  }
                }
              ],
              "filter": [
                {
                  "range": {
                    "@timestamp": {
                      "gte": "now-5m"
                    }
                  }
                }
              ]
            }
          }
        }
      }
    }
  },
  "condition": {
    "compare": {
      "ctx.payload.hits.total": {
        "gt": 0
      }
    }
  },
  "actions": {
    "send_alert": {
      "webhook": {
        "scheme": "https",
        "host": "your-soar-platform.com",
        "port": 443,
        "method": "post",
        "path": "/api/alerts",
        "params": {},
        "headers": {
          "Content-Type": "application/json"
        },
        "body": "{\\"alert_type\\": \\"suspicious_powershell\\", \\"count\\": {{ctx.payload.hits.total}}, \\"timestamp\\": \\"{{ctx.execution_time}}\\"}"
      }
    },
    "create_case": {
      "index": {
        "index": "hunting-cases",
        "body": {
          "case_id": "{{ctx.watch_id}}-{{ctx.execution_time}}",
          "alert_type": "suspicious_powershell",
          "severity": "high",
          "event_count": "{{ctx.payload.hits.total}}",
          "timestamp": "{{ctx.execution_time}}",
          "events": "{{ctx.payload.hits.hits}}"
        }
      }
    }
  }
}
\`\`\`
      `,
      activities: [
        'Build comprehensive hunting dashboards',
        'Create custom Kibana visualizations',
        'Develop automated hunting watchers',
        'Build custom Kibana plugins for hunting'
      ]
    },

    {
      id: 'logstash-processing',
      title: 'Logstash for Security Data Processing',
      content: `
## Advanced Logstash Configurations

### **Security Log Processing Pipeline**
\`\`\`ruby
# Logstash configuration for Windows Security Events
input {
  beats {
    port => 5044
  }
}

filter {
  # Parse Windows Event Logs
  if [agent][type] == "winlogbeat" {
    
    # Extract user domain and name
    if [winlog][event_data][TargetUserName] {
      grok {
        match => { "[winlog][event_data][TargetUserName]" => "(?<user_domain>[^\\\\]+)\\\\(?<user_name>.+)" }
        tag_on_failure => ["_grokparsefailure_user"]
      }
    }
    
    # Enrich with threat intelligence
    if [source][ip] {
      translate {
        field => "[source][ip]"
        destination => "[threat_intel][reputation]"
        dictionary_path => "/etc/logstash/threat_intel/ip_reputation.yml"
        fallback => "unknown"
      }
    }
    
    # Calculate risk score
    ruby {
      code => "
        risk_score = 0
        
        # High-risk event codes
        high_risk_events = ['4625', '4648', '4672', '4720', '4728']
        if high_risk_events.include?(event.get('[event][code]'))
          risk_score += 30
        end
        
        # Suspicious processes
        command_line = event.get('[process][command_line]')
        if command_line
          risk_score += 40 if command_line.include?('-enc')
          risk_score += 35 if command_line.include?('bypass')
          risk_score += 30 if command_line.include?('downloadstring')
        end
        
        # Threat intelligence reputation
        reputation = event.get('[threat_intel][reputation]')
        case reputation
        when 'malicious'
          risk_score += 50
        when 'suspicious'
          risk_score += 25
        end
        
        event.set('[risk_score]', risk_score)
        
        # Set severity based on risk score
        if risk_score >= 80
          event.set('[severity]', 'critical')
        elsif risk_score >= 60
          event.set('[severity]', 'high')
        elsif risk_score >= 40
          event.set('[severity]', 'medium')
        else
          event.set('[severity]', 'low')
        end
      "
    }
    
    # GeoIP enrichment
    if [source][ip] {
      geoip {
        source => "[source][ip]"
        target => "[source][geo]"
      }
    }
    
    # Add hunting tags
    if [risk_score] and [risk_score] > 50 {
      mutate {
        add_tag => ["hunting_priority"]
      }
    }
  }
  
  # Parse network logs
  if [agent][type] == "packetbeat" {
    
    # Detect potential beaconing
    if [network][bytes] {
      ruby {
        code => "
          # Simple beaconing detection logic
          bytes = event.get('[network][bytes]')
          
          # Store connection patterns in a cache (simplified)
          # In production, use Redis or similar
          
          if bytes && bytes > 0 && bytes < 1000
            event.set('[potential_beaconing]', true)
            event.add_tag('beaconing_candidate')
          end
        "
      }
    }
    
    # Protocol analysis
    if [network][protocol] {
      if [network][protocol] == "dns" {
        # DNS analysis
        if [dns][question][name] {
          ruby {
            code => "
              domain = event.get('[dns][question][name]')
              
              # Check for DGA domains
              if domain && domain.length > 20
                # Simple entropy calculation
                chars = domain.chars
                entropy = chars.uniq.length.to_f / chars.length
                
                if entropy > 0.8
                  event.set('[dns][dga_score]', entropy)
                  event.add_tag('potential_dga')
                end
              end
              
              # Check for DNS tunneling
              if domain && domain.split('.').any? { |label| label.length > 63 }
                event.add_tag('dns_tunneling_candidate')
              end
            "
          }
        }
      }
    }
  }
  
  # Common enrichments for all events
  
  # Add timestamp parsing
  date {
    match => [ "@timestamp", "ISO8601" ]
  }
  
  # Add hunting metadata
  mutate {
    add_field => {
      "[hunting][processed_at]" => "%{+YYYY-MM-dd HH:mm:ss}"
      "[hunting][pipeline_version]" => "1.0"
    }
  }
}

output {
  # Send to Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "%{[@metadata][beat]}-%{[@metadata][version]}-%{+YYYY.MM.dd}"
  }
  
  # Send high-risk events to SOAR
  if "hunting_priority" in [tags] {
    http {
      url => "https://soar-platform.com/api/events"
      http_method => "post"
      format => "json"
      headers => {
        "Authorization" => "Bearer YOUR_API_TOKEN"
        "Content-Type" => "application/json"
      }
    }
  }
  
  # Debug output
  if [severity] == "critical" {
    stdout {
      codec => rubydebug
    }
  }
}
\`\`\`

### **Custom Logstash Plugins**
\`\`\`ruby
# Custom filter plugin for YARA scanning
require "logstash/filters/base"
require "logstash/namespace"

class LogStash::Filters::YaraScan < LogStash::Filters::Base
  config_name "yara_scan"
  
  config :rules_path, :validate => :string, :required => true
  config :field, :validate => :string, :default => "message"
  config :target, :validate => :string, :default => "yara"
  
  def register
    require "yara"
    @rules = Yara::Rules.load_file(@rules_path)
  end
  
  def filter(event)
    content = event.get(@field)
    return unless content
    
    matches = @rules.scan_string(content)
    
    if matches.any?
      event.set("#{@target}.matches", matches.map(&:rule))
      event.set("#{@target}.match_count", matches.length)
      event.add_tag("yara_match")
    end
    
    filter_matched(event)
  end
end

# Custom output plugin for threat intelligence platforms
require "logstash/outputs/base"
require "logstash/namespace"

class LogStash::Outputs::ThreatIntel < LogStash::Outputs::Base
  config_name "threat_intel"
  
  config :api_url, :validate => :string, :required => true
  config :api_key, :validate => :string, :required => true
  config :ioc_fields, :validate => :array, :default => ["source.ip", "destination.ip", "dns.question.name"]
  
  def register
    require "net/http"
    require "json"
  end
  
  def receive(event)
    iocs = extract_iocs(event)
    
    iocs.each do |ioc|
      enrichment = lookup_threat_intel(ioc)
      if enrichment
        event.set("[threat_intel][#{ioc}]", enrichment)
      end
    end
  end
  
  private
  
  def extract_iocs(event)
    iocs = []
    @ioc_fields.each do |field|
      value = event.get(field)
      iocs << value if value
    end
    iocs
  end
  
  def lookup_threat_intel(ioc)
    uri = URI("#{@api_url}/lookup")
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    
    request = Net::HTTP::Post.new(uri)
    request["Authorization"] = "Bearer #{@api_key}"
    request["Content-Type"] = "application/json"
    request.body = { "indicator" => ioc }.to_json
    
    response = http.request(request)
    
    if response.code == "200"
      JSON.parse(response.body)
    else
      nil
    end
  rescue => e
    @logger.error("Threat intel lookup failed", :error => e.message)
    nil
  end
end
\`\`\`
      `,
      activities: [
        'Configure advanced Logstash pipelines',
        'Build custom Logstash plugins',
        'Implement real-time threat intelligence enrichment',
        'Create automated IOC extraction and analysis'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'ELK Hunting Platform Deployment',
      description: 'Deploy and configure complete ELK stack for threat hunting',
      tasks: [
        'Set up Elasticsearch cluster with security',
        'Configure Logstash for security log processing',
        'Build comprehensive Kibana hunting dashboards',
        'Implement automated hunting with Watcher'
      ]
    },
    {
      title: 'Advanced Query Development Lab',
      description: 'Develop sophisticated hunting queries using Elasticsearch DSL',
      tasks: [
        'Build behavioral analysis queries',
        'Implement statistical anomaly detection',
        'Create machine learning jobs for hunting',
        'Develop custom aggregation pipelines'
      ]
    },
    {
      title: 'Custom ELK Extensions Project',
      description: 'Build custom plugins and extensions for specialized hunting',
      tasks: [
        'Develop custom Logstash filter plugins',
        'Create specialized Kibana visualizations',
        'Build threat intelligence integration',
        'Implement automated response mechanisms'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'ELK Hunting Mastery Assessment',
      description: 'Demonstrate advanced ELK stack hunting capabilities through practical scenarios'
    },
    {
      type: 'project',
      title: 'Enterprise ELK Hunting Solution',
      description: 'Design and implement complete ELK-based hunting solution for enterprise'
    }
  ],

  resources: [
    'Elasticsearch Query DSL Reference',
    'Kibana Visualization Best Practices',
    'Logstash Configuration Patterns',
    'Elastic Security Detection Rules',
    'ELK Stack Performance Tuning Guide',
    'Custom Plugin Development Documentation'
  ]
};
