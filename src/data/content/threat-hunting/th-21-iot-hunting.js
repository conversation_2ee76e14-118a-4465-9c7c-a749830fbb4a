/**
 * TH-21: IoT Threat Hunting
 * Master threat hunting in Internet of Things environments
 */

export const iotHuntingContent = {
  id: 'th-21',
  title: 'IoT Threat Hunting: Securing Connected Devices',
  description: 'Master comprehensive threat hunting in IoT environments, from smart homes to industrial IoT deployments.',
  duration: '35 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Understand IoT threat landscape and attack vectors',
    'Master IoT network traffic analysis and monitoring',
    'Implement IoT device behavior analysis and anomaly detection',
    'Hunt for IoT botnets and malware infections',
    'Secure IoT communication protocols and data flows',
    'Develop IoT-specific hunting methodologies and tools',
    'Build comprehensive IoT security monitoring systems'
  ],

  sections: [
    {
      id: 'iot-fundamentals',
      title: 'IoT Security Fundamentals',
      content: `
## IoT Threat Landscape

### Common IoT Attack Vectors
- **Device Compromise**
  - Default credential exploitation
  - Firmware vulnerabilities and backdoors
  - Physical device tampering
  - Supply chain attacks

- **Network-Based Attacks**
  - Man-in-the-middle attacks on IoT communications
  - Protocol exploitation (MQTT, CoAP, Zigbee)
  - Network segmentation bypass
  - Lateral movement through IoT networks

- **Botnet Recruitment**
  - Mirai and variants targeting IoT devices
  - DDoS amplification using IoT devices
  - Cryptomining on compromised IoT infrastructure
  - Command and control through IoT networks

### IoT Device Categories and Risks
\`\`\`yaml
Smart Home Devices:
  - Security cameras and doorbells
  - Smart locks and access controls
  - Voice assistants and smart speakers
  - Smart thermostats and appliances
  - Risks: Privacy invasion, physical security bypass

Industrial IoT (IIoT):
  - Sensors and monitoring devices
  - Actuators and control systems
  - Edge computing devices
  - Risks: Production disruption, safety hazards

Healthcare IoT:
  - Medical devices and monitors
  - Wearable health trackers
  - Connected medical equipment
  - Risks: Patient safety, data privacy

Smart City Infrastructure:
  - Traffic management systems
  - Environmental monitoring
  - Public safety systems
  - Risks: Public safety, infrastructure disruption
\`\`\`

### IoT Communication Protocols
\`\`\`
Application Layer:
  - MQTT (Message Queuing Telemetry Transport)
  - CoAP (Constrained Application Protocol)
  - HTTP/HTTPS for web-based IoT
  - AMQP (Advanced Message Queuing Protocol)

Network Layer:
  - 6LoWPAN (IPv6 over Low-Power Wireless)
  - RPL (Routing Protocol for Low-Power and Lossy Networks)
  - Thread networking protocol

Physical/Link Layer:
  - WiFi (802.11)
  - Bluetooth/BLE
  - Zigbee (802.15.4)
  - LoRaWAN
  - Cellular (4G/5G)
\`\`\`
      `,
      activities: [
        'Map IoT devices in network environment',
        'Analyze IoT communication protocols',
        'Identify IoT security vulnerabilities',
        'Set up IoT monitoring infrastructure'
      ]
    },

    {
      id: 'iot-traffic-analysis',
      title: 'IoT Network Traffic Analysis',
      content: `
## IoT Traffic Hunting Techniques

### 1. Protocol-Specific Analysis

#### MQTT Traffic Analysis
\`\`\`python
import paho.mqtt.client as mqtt
import json
from datetime import datetime

class MQTTHunter:
    def __init__(self, broker_host, broker_port=1883):
        self.client = mqtt.Client()
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.suspicious_patterns = []
        
    def on_connect(self, client, userdata, flags, rc):
        print(f"Connected to MQTT broker with result code {rc}")
        # Subscribe to all topics for hunting
        client.subscribe("#")
        
    def on_message(self, client, userdata, msg):
        try:
            payload = json.loads(msg.payload.decode())
            self.analyze_mqtt_message(msg.topic, payload)
        except:
            # Handle non-JSON payloads
            self.analyze_raw_payload(msg.topic, msg.payload)
    
    def analyze_mqtt_message(self, topic, payload):
        # Hunt for suspicious patterns
        if self.detect_anomalous_frequency(topic):
            self.log_suspicious_activity("high_frequency", topic, payload)
        
        if self.detect_unusual_payload_size(payload):
            self.log_suspicious_activity("large_payload", topic, payload)
        
        if self.detect_command_injection(payload):
            self.log_suspicious_activity("command_injection", topic, payload)

# Wireshark filter for IoT protocols
wireshark_filters = {
    'mqtt': 'mqtt',
    'coap': 'coap',
    'zigbee': 'zbee_nwk',
    'bluetooth': 'bthci_cmd or bthci_evt',
    'modbus': 'modbus'
}
\`\`\`

#### CoAP Traffic Hunting
\`\`\`python
from scapy.all import *
from scapy.contrib.coap import CoAP

def hunt_coap_anomalies(pcap_file):
    packets = rdpcap(pcap_file)
    coap_packets = [pkt for pkt in packets if CoAP in pkt]
    
    # Analyze CoAP request patterns
    request_patterns = {}
    for pkt in coap_packets:
        if pkt[CoAP].code in [1, 2, 3, 4]:  # GET, POST, PUT, DELETE
            src_ip = pkt[IP].src
            uri_path = pkt[CoAP].options[0].value if pkt[CoAP].options else "unknown"
            
            key = f"{src_ip}:{uri_path}"
            request_patterns[key] = request_patterns.get(key, 0) + 1
    
    # Identify anomalous patterns
    suspicious_requests = {k: v for k, v in request_patterns.items() if v > 100}
    return suspicious_requests
\`\`\`

### 2. Device Behavior Analysis

#### IoT Device Fingerprinting
\`\`\`python
import numpy as np
from sklearn.cluster import DBSCAN

class IoTDeviceProfiler:
    def __init__(self):
        self.device_profiles = {}
        
    def create_device_profile(self, mac_address, traffic_data):
        # Extract behavioral features
        features = {
            'packet_intervals': self.calculate_packet_intervals(traffic_data),
            'payload_sizes': [len(pkt.payload) for pkt in traffic_data],
            'protocol_distribution': self.get_protocol_distribution(traffic_data),
            'communication_patterns': self.analyze_communication_patterns(traffic_data)
        }
        
        self.device_profiles[mac_address] = features
        return features
    
    def detect_behavioral_anomalies(self, mac_address, current_traffic):
        if mac_address not in self.device_profiles:
            return "Unknown device"
        
        baseline = self.device_profiles[mac_address]
        current_features = self.create_device_profile(mac_address, current_traffic)
        
        # Compare current behavior with baseline
        anomaly_score = self.calculate_anomaly_score(baseline, current_features)
        
        if anomaly_score > 0.8:
            return "High anomaly - potential compromise"
        elif anomaly_score > 0.6:
            return "Medium anomaly - investigate"
        else:
            return "Normal behavior"
\`\`\`

### 3. IoT Botnet Detection

#### Mirai Botnet Hunting
\`\`\`bash
# Zeek/Bro script for Mirai detection
@load base/protocols/conn
@load base/protocols/dns

module MiraiHunter;

export {
    # Mirai C2 communication patterns
    const mirai_ports: set[port] = {23/tcp, 2323/tcp, 80/tcp, 8080/tcp};
    const mirai_user_agents: pattern = /wget|curl|busybox/;
    
    # Suspicious DNS queries
    const mirai_domains: pattern = /\.tk$|\.ml$|\.ga$/;
}

event connection_established(c: connection) {
    if (c$id$resp_p in mirai_ports && 
        c$conn$duration > 60secs &&
        c$conn$resp_bytes > 1000) {
        
        print fmt("Potential Mirai C2: %s -> %s:%s", 
                  c$id$orig_h, c$id$resp_h, c$id$resp_p);
    }
}

event http_request(c: connection, method: string, original_URI: string,
                   unescaped_URI: string, version: string) {
    if (mirai_user_agents in c$http$user_agent) {
        print fmt("Mirai-like HTTP request: %s from %s", 
                  original_URI, c$id$orig_h);
    }
}
\`\`\`
      `,
      activities: [
        'Analyze MQTT and CoAP traffic patterns',
        'Build IoT device behavioral profiles',
        'Hunt for IoT botnet communications',
        'Detect anomalous IoT device behavior'
      ]
    },

    {
      id: 'iot-malware-hunting',
      title: 'IoT Malware and Firmware Analysis',
      content: `
## IoT Malware Hunting Techniques

### 1. Firmware Analysis for Threats

#### Automated Firmware Analysis
\`\`\`python
import binwalk
import yara
import hashlib

class IoTFirmwareHunter:
    def __init__(self):
        self.yara_rules = yara.compile(filepath='iot_malware_rules.yar')
        
    def analyze_firmware(self, firmware_path):
        results = {
            'file_hash': self.calculate_hash(firmware_path),
            'extracted_files': self.extract_filesystem(firmware_path),
            'malware_signatures': self.scan_for_malware(firmware_path),
            'suspicious_strings': self.find_suspicious_strings(firmware_path),
            'crypto_keys': self.extract_crypto_material(firmware_path)
        }
        return results
    
    def extract_filesystem(self, firmware_path):
        # Use binwalk to extract filesystem
        binwalk.scan(firmware_path, signature=True, quiet=True, extract=True)
        return "Filesystem extracted"
    
    def scan_for_malware(self, firmware_path):
        with open(firmware_path, 'rb') as f:
            matches = self.yara_rules.match(data=f.read())
        return [match.rule for match in matches]

# YARA rules for IoT malware detection
iot_malware_rules = '''
rule Mirai_IoT_Malware {
    meta:
        description = "Detects Mirai IoT malware variants"
        author = "IoT Threat Hunter"
    
    strings:
        $s1 = "busybox wget" ascii
        $s2 = "/bin/busybox" ascii
        $s3 = "HTTPFLOOD" ascii
        $s4 = "UDPFLOOD" ascii
        $s5 = { 22 2F 62 69 6E 2F 73 68 22 }  // "/bin/sh"
    
    condition:
        3 of them
}

rule IoT_Backdoor {
    meta:
        description = "Generic IoT backdoor detection"
    
    strings:
        $telnet = "telnetd" ascii
        $ssh = "dropbear" ascii
        $shell = "/bin/sh" ascii
        $passwd = "/etc/passwd" ascii
    
    condition:
        2 of them and filesize < 1MB
}
'''
\`\`\`

### 2. Runtime Malware Detection

#### IoT Honeypot for Malware Collection
\`\`\`python
import socket
import threading
import logging

class IoTHoneypot:
    def __init__(self, port=23):  # Telnet port commonly targeted
        self.port = port
        self.running = False
        self.connections = []
        
    def start_honeypot(self):
        self.running = True
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.bind(('0.0.0.0', self.port))
        server_socket.listen(5)
        
        print(f"IoT Honeypot listening on port {self.port}")
        
        while self.running:
            client_socket, address = server_socket.accept()
            print(f"Connection from {address}")
            
            # Handle connection in separate thread
            thread = threading.Thread(
                target=self.handle_connection,
                args=(client_socket, address)
            )
            thread.start()
    
    def handle_connection(self, client_socket, address):
        try:
            # Simulate vulnerable IoT device
            client_socket.send(b"BusyBox v1.01 (2023.01.01-00:00+0000) Built-in shell\\n")
            client_socket.send(b"login: ")
            
            while True:
                data = client_socket.recv(1024)
                if not data:
                    break
                
                # Log all commands for analysis
                command = data.decode('utf-8', errors='ignore').strip()
                self.log_attack_attempt(address, command)
                
                # Simulate command execution
                if command in ['admin', 'root', 'user']:
                    client_socket.send(b"Password: ")
                elif command in ['admin', '123456', 'password']:
                    client_socket.send(b"# ")
                else:
                    client_socket.send(b"$ ")
                    
        except Exception as e:
            print(f"Error handling connection: {e}")
        finally:
            client_socket.close()
    
    def log_attack_attempt(self, address, command):
        logging.info(f"Attack from {address}: {command}")
        
        # Analyze for known attack patterns
        if any(pattern in command.lower() for pattern in ['wget', 'curl', 'tftp']):
            self.alert_malware_download(address, command)
\`\`\`

### 3. IoT Network Segmentation Monitoring

#### Network Boundary Violation Detection
\`\`\`python
import ipaddress

class IoTNetworkMonitor:
    def __init__(self):
        # Define IoT network segments
        self.iot_networks = [
            ipaddress.IPv4Network('*************/24'),  # Smart home devices
            ipaddress.IPv4Network('*********/24'),      # Industrial IoT
            ipaddress.IPv4Network('***********/24')     # Healthcare IoT
        ]
        
        self.corporate_networks = [
            ipaddress.IPv4Network('10.0.0.0/16'),
            ipaddress.IPv4Network('**********/12')
        ]
    
    def analyze_traffic_flow(self, src_ip, dst_ip, protocol, port):
        src_addr = ipaddress.IPv4Address(src_ip)
        dst_addr = ipaddress.IPv4Address(dst_ip)
        
        src_is_iot = any(src_addr in network for network in self.iot_networks)
        dst_is_corporate = any(dst_addr in network for network in self.corporate_networks)
        
        # Detect IoT to corporate network communication
        if src_is_iot and dst_is_corporate:
            risk_level = self.assess_communication_risk(protocol, port)
            if risk_level > 7:
                return {
                    'alert': 'High-risk IoT to corporate communication',
                    'src_ip': src_ip,
                    'dst_ip': dst_ip,
                    'protocol': protocol,
                    'port': port,
                    'risk_level': risk_level
                }
        
        return None
    
    def assess_communication_risk(self, protocol, port):
        # Risk scoring based on protocol and port
        high_risk_ports = [22, 23, 80, 443, 3389, 5900]  # SSH, Telnet, HTTP, HTTPS, RDP, VNC
        
        if port in high_risk_ports:
            return 9
        elif protocol.upper() == 'TCP':
            return 6
        else:
            return 3
\`\`\`
      `,
      activities: [
        'Analyze IoT firmware for malware',
        'Set up IoT honeypots for threat collection',
        'Monitor IoT network segmentation violations',
        'Hunt for IoT-specific malware families'
      ]
    },

    {
      id: 'iot-hunting-automation',
      title: 'Automated IoT Threat Hunting',
      content: `
## IoT Hunting Automation and Orchestration

### 1. Automated IoT Device Discovery

#### Network Scanning for IoT Devices
\`\`\`python
import nmap
import requests
import json

class IoTDeviceDiscovery:
    def __init__(self):
        self.nm = nmap.PortScanner()
        self.known_iot_signatures = {
            'cameras': ['axis', 'hikvision', 'dahua'],
            'routers': ['linksys', 'netgear', 'dlink'],
            'smart_home': ['philips hue', 'nest', 'ring']
        }
    
    def discover_iot_devices(self, network_range):
        # Scan for common IoT ports
        iot_ports = '22,23,80,443,554,8080,8081,9000'
        scan_results = self.nm.scan(network_range, iot_ports)
        
        iot_devices = []
        for host in scan_results['scan']:
            device_info = self.fingerprint_device(host)
            if device_info['is_iot']:
                iot_devices.append(device_info)
        
        return iot_devices
    
    def fingerprint_device(self, ip):
        device_info = {
            'ip': ip,
            'is_iot': False,
            'device_type': 'unknown',
            'vendor': 'unknown',
            'vulnerabilities': []
        }
        
        # HTTP banner grabbing
        try:
            response = requests.get(f'http://{ip}', timeout=5)
            server_header = response.headers.get('Server', '').lower()
            
            for device_type, signatures in self.known_iot_signatures.items():
                if any(sig in server_header for sig in signatures):
                    device_info['is_iot'] = True
                    device_info['device_type'] = device_type
                    break
        except:
            pass
        
        # Check for default credentials
        if self.check_default_credentials(ip):
            device_info['vulnerabilities'].append('default_credentials')
        
        return device_info

### 2. Continuous IoT Monitoring

#### Real-time IoT Threat Detection
\`\`\`python
import asyncio
import websockets
import json

class IoTThreatMonitor:
    def __init__(self):
        self.active_threats = {}
        self.baseline_behavior = {}
        
    async def monitor_iot_traffic(self, websocket, path):
        async for message in websocket:
            traffic_data = json.loads(message)
            threat_analysis = await self.analyze_traffic(traffic_data)
            
            if threat_analysis['threat_level'] > 7:
                await self.trigger_response(threat_analysis)
    
    async def analyze_traffic(self, traffic_data):
        device_id = traffic_data['device_id']
        
        # Behavioral analysis
        if device_id not in self.baseline_behavior:
            self.baseline_behavior[device_id] = self.create_baseline(traffic_data)
            return {'threat_level': 0}
        
        deviation = self.calculate_behavioral_deviation(
            traffic_data, 
            self.baseline_behavior[device_id]
        )
        
        threat_level = min(deviation * 10, 10)  # Scale to 0-10
        
        return {
            'device_id': device_id,
            'threat_level': threat_level,
            'anomalies': self.identify_anomalies(traffic_data),
            'timestamp': traffic_data['timestamp']
        }
    
    async def trigger_response(self, threat_analysis):
        # Automated response actions
        if threat_analysis['threat_level'] > 9:
            await self.isolate_device(threat_analysis['device_id'])
        elif threat_analysis['threat_level'] > 7:
            await self.alert_security_team(threat_analysis)

### 3. IoT Threat Intelligence Integration

#### Automated IoT IOC Enrichment
\`\`\`python
class IoTThreatIntelligence:
    def __init__(self):
        self.ti_sources = {
            'mirai_tracker': 'https://mirai-tracker.abuse.ch/api/',
            'iot_malware_db': 'https://iot-malware-database.com/api/',
            'shodan': 'https://api.shodan.io/'
        }
    
    def enrich_iot_indicators(self, indicators):
        enriched_data = {}
        
        for indicator in indicators:
            enrichment = {
                'mirai_family': self.check_mirai_tracker(indicator),
                'known_malware': self.check_malware_database(indicator),
                'exposed_services': self.check_shodan(indicator),
                'reputation_score': self.calculate_reputation(indicator)
            }
            
            enriched_data[indicator] = enrichment
        
        return enriched_data
    
    def generate_hunting_hypotheses(self, enriched_data):
        hypotheses = []
        
        for indicator, data in enriched_data.items():
            if data['mirai_family']:
                hypotheses.append({
                    'hypothesis': f'Hunt for Mirai {data["mirai_family"]} infections',
                    'ioc': indicator,
                    'priority': 'high',
                    'hunting_queries': self.generate_mirai_queries(data['mirai_family'])
                })
        
        return hypotheses
\`\`\`
      `,
      activities: [
        'Build automated IoT device discovery',
        'Implement continuous IoT monitoring',
        'Create IoT threat intelligence feeds',
        'Develop automated response mechanisms'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'IoT Botnet Investigation',
      description: 'Investigate a simulated IoT botnet infection across smart home devices',
      tasks: [
        'Identify compromised IoT devices',
        'Analyze botnet communication patterns',
        'Track command and control infrastructure',
        'Develop containment strategies'
      ]
    },
    {
      title: 'Smart City Security Assessment',
      description: 'Conduct threat hunting in a simulated smart city environment',
      tasks: [
        'Map IoT infrastructure and communications',
        'Hunt for unauthorized access attempts',
        'Analyze traffic management system security',
        'Assess environmental monitoring integrity'
      ]
    },
    {
      title: 'Industrial IoT Security Monitoring',
      description: 'Build comprehensive security monitoring for IIoT environment',
      tasks: [
        'Design IIoT network segmentation monitoring',
        'Implement behavioral analysis for industrial devices',
        'Create automated threat detection rules',
        'Develop incident response procedures'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'IoT Threat Hunting Certification',
      description: 'Demonstrate mastery of IoT threat hunting techniques and tools'
    },
    {
      type: 'project',
      title: 'Enterprise IoT Security Architecture',
      description: 'Design and implement comprehensive IoT security monitoring solution'
    }
  ],

  resources: [
    'IoT Security Foundation Guidelines',
    'NIST Cybersecurity for IoT Program',
    'OWASP IoT Security Testing Guide',
    'Industrial Internet Consortium Security Working Group',
    'Mirai Source Code Analysis',
    'IoT Malware Analysis Tools and Techniques'
  ]
};
