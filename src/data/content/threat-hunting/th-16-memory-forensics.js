/**
 * TH-16: Memory Forensics for Threat Hunting
 * Master memory analysis and forensics for advanced threat detection
 */

export const memoryForensicsContent = {
  id: 'th-16',
  title: 'Memory Forensics for Threat Hunting',
  description: 'Master memory analysis and forensics techniques using Volatility and other tools for advanced threat detection and malware analysis.',
  duration: '45 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Master Volatility 3 for comprehensive memory analysis',
    'Hunt for advanced malware and rootkits in memory',
    'Analyze process injection and code injection techniques',
    'Extract and analyze network artifacts from memory',
    'Investigate credential theft and privilege escalation',
    'Build automated memory analysis workflows',
    'Develop custom memory analysis plugins and tools'
  ],

  sections: [
    {
      id: 'volatility-mastery',
      title: 'Volatility 3 Advanced Techniques',
      content: `
## Volatility 3 Fundamentals

### **Basic Memory Analysis Workflow**
\`\`\`bash
# Memory dump acquisition
# Using WinPmem for Windows
winpmem_mini_x64.exe -o memory.raw

# Using LiME for Linux
sudo insmod lime-$(uname -r).ko "path=/tmp/memory.lime format=lime"

# Basic Volatility 3 commands
python3 vol.py -f memory.raw windows.info
python3 vol.py -f memory.raw windows.pslist
python3 vol.py -f memory.raw windows.pstree
python3 vol.py -f memory.raw windows.netstat
python3 vol.py -f memory.raw windows.filescan
\`\`\`

### **Advanced Process Analysis**
\`\`\`python
#!/usr/bin/env python3
"""
Advanced process analysis with Volatility 3
"""

import volatility3.framework.contexts
import volatility3.framework.plugins
from volatility3.framework import interfaces, constants
from volatility3.plugins.windows import pslist, handles, vadinfo

class AdvancedProcessHunter:
    def __init__(self, memory_file):
        self.memory_file = memory_file
        self.context = self._build_context()
    
    def _build_context(self):
        """Build Volatility context"""
        ctx = volatility3.framework.contexts.Context()
        constants.BANG_PATH = volatility3.framework.constants.BANG_PATH
        
        # Add the memory file as a layer
        ctx.config['automagic.LayerStacker.single_location'] = f"file://{self.memory_file}"
        
        return ctx
    
    def hunt_suspicious_processes(self):
        """Hunt for suspicious processes"""
        suspicious_processes = []
        
        # Get process list
        plugin = pslist.PsList(self.context, config_path="plugins.PsList")
        
        for process in plugin.list_processes():
            suspicion_score = self._calculate_process_suspicion(process)
            
            if suspicion_score > 50:
                process_info = {
                    'pid': process.UniqueProcessId,
                    'name': process.ImageFileName.cast("string", max_length=process.ImageFileName.vol.count, errors='replace'),
                    'ppid': process.InheritedFromUniqueProcessId,
                    'create_time': process.CreateTime,
                    'suspicion_score': suspicion_score,
                    'indicators': self._get_process_indicators(process)
                }
                suspicious_processes.append(process_info)
        
        return suspicious_processes
    
    def _calculate_process_suspicion(self, process):
        """Calculate process suspicion score"""
        score = 0
        
        process_name = str(process.ImageFileName).lower()
        
        # Suspicious process names
        suspicious_names = [
            'powershell.exe', 'cmd.exe', 'rundll32.exe', 'regsvr32.exe',
            'mshta.exe', 'cscript.exe', 'wscript.exe', 'certutil.exe'
        ]
        
        if any(name in process_name for name in suspicious_names):
            score += 30
        
        # Process running from suspicious locations
        try:
            peb = process.get_peb()
            if peb:
                image_path = str(peb.ProcessParameters.ImagePathName).lower()
                if any(path in image_path for path in ['temp', 'appdata', 'downloads']):
                    score += 40
        except:
            pass
        
        # Unusual parent-child relationships
        if process.InheritedFromUniqueProcessId == 0 and process.UniqueProcessId != 4:
            score += 25  # Orphaned process
        
        return score
    
    def hunt_process_injection(self):
        """Hunt for process injection techniques"""
        injection_indicators = []
        
        plugin = vadinfo.VadInfo(self.context, config_path="plugins.VadInfo")
        
        for task in plugin.list_processes():
            for vad in task.get_vad_root().traverse():
                # Look for executable memory regions not backed by files
                if (vad.get_protection() & 0x20) and not vad.get_file_name():  # PAGE_EXECUTE_READ
                    injection_indicators.append({
                        'pid': task.UniqueProcessId,
                        'process_name': str(task.ImageFileName),
                        'vad_start': hex(vad.get_start()),
                        'vad_end': hex(vad.get_end()),
                        'protection': hex(vad.get_protection()),
                        'type': 'Executable memory without file backing'
                    })
        
        return injection_indicators
    
    def extract_network_artifacts(self):
        """Extract network artifacts from memory"""
        network_artifacts = []
        
        # Get network connections
        netstat_plugin = volatility3.plugins.windows.netstat.NetStat(
            self.context, config_path="plugins.NetStat"
        )
        
        for connection in netstat_plugin.list_network_connections():
            artifact = {
                'local_addr': connection.LocalAddr,
                'local_port': connection.LocalPort,
                'remote_addr': connection.RemoteAddr,
                'remote_port': connection.RemotePort,
                'state': connection.State,
                'pid': connection.Owner.UniqueProcessId if connection.Owner else None,
                'process_name': str(connection.Owner.ImageFileName) if connection.Owner else None
            }
            network_artifacts.append(artifact)
        
        return network_artifacts
    
    def hunt_malware_signatures(self):
        """Hunt for malware signatures in memory"""
        malware_indicators = []
        
        # YARA scanning
        yara_plugin = volatility3.plugins.yarascan.YaraScan(
            self.context, config_path="plugins.YaraScan"
        )
        
        # Define YARA rules for common malware families
        yara_rules = """
        rule Mimikatz {
            strings:
                $s1 = "sekurlsa::logonpasswords" ascii
                $s2 = "privilege::debug" ascii
                $s3 = "lsadump::sam" ascii
            condition:
                any of them
        }
        
        rule Cobalt_Strike {
            strings:
                $s1 = "beacon.dll" ascii
                $s2 = "malleable_c2" ascii
                $s3 = "cobaltstrike" ascii
            condition:
                any of them
        }
        
        rule Metasploit {
            strings:
                $s1 = "meterpreter" ascii
                $s2 = "metasploit" ascii
                $s3 = "payload/windows" ascii
            condition:
                any of them
        }
        """
        
        # Scan memory with YARA rules
        for hit in yara_plugin.scan(yara_rules):
            malware_indicators.append({
                'rule_name': hit.rule,
                'offset': hex(hit.offset),
                'process_name': hit.process_name if hasattr(hit, 'process_name') else 'Unknown',
                'strings': [str(s) for s in hit.strings]
            })
        
        return malware_indicators

### **Memory-Based IOC Extraction**
\`\`\`python
import re
import struct
from volatility3.plugins.windows import filescan, handles

class MemoryIOCExtractor:
    def __init__(self, context):
        self.context = context
        self.ioc_patterns = {
            'ip_addresses': r'\\\\b(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}\\\\b',
            'domains': r'\\\\b[a-zA-Z0-9]([a-zA-Z0-9\\\\-]{0,61}[a-zA-Z0-9])?\\\\.[a-zA-Z]{2,}\\\\b',
            'urls': r'https?://[^\\\\s<>"{}|\\\\\\\\^\\\\[\\\\]]+',
            'file_paths': r'[A-Za-z]:\\\\\\\\[^\\\\s<>"|?*]+',
            'registry_keys': r'HKEY_[A-Z_]+\\\\\\\\[^\\\\s<>"|?*]+',
            'email_addresses': r'\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\.[A-Z|a-z]{2,}\\\\b'
        }
    
    def extract_iocs_from_memory(self):
        """Extract IOCs from memory dump"""
        iocs = {ioc_type: set() for ioc_type in self.ioc_patterns.keys()}
        
        # Extract from process memory
        pslist_plugin = volatility3.plugins.windows.pslist.PsList(
            self.context, config_path="plugins.PsList"
        )
        
        for process in pslist_plugin.list_processes():
            try:
                # Read process memory
                process_space = process.get_process_address_space()
                if process_space:
                    # Read memory chunks and extract IOCs
                    for vad in process.get_vad_root().traverse():
                        try:
                            data = process_space.read(vad.get_start(), min(vad.get_size(), 1024*1024))  # Read up to 1MB
                            if data:
                                text_data = data.decode('utf-8', errors='ignore')
                                self._extract_iocs_from_text(text_data, iocs)
                        except:
                            continue
            except:
                continue
        
        # Convert sets to lists for JSON serialization
        return {ioc_type: list(ioc_set) for ioc_type, ioc_set in iocs.items()}
    
    def _extract_iocs_from_text(self, text, iocs):
        """Extract IOCs from text using regex patterns"""
        for ioc_type, pattern in self.ioc_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                # Filter out common false positives
                if self._is_valid_ioc(ioc_type, match):
                    iocs[ioc_type].add(match)
    
    def _is_valid_ioc(self, ioc_type, value):
        """Validate IOC to reduce false positives"""
        if ioc_type == 'ip_addresses':
            # Filter out private IP ranges and invalid IPs
            parts = value.split('.')
            if len(parts) != 4:
                return False
            try:
                nums = [int(part) for part in parts]
                if any(num > 255 for num in nums):
                    return False
                # Skip private ranges
                if (nums[0] == 10 or 
                    (nums[0] == 172 and 16 <= nums[1] <= 31) or
                    (nums[0] == 192 and nums[1] == 168) or
                    nums[0] == 127):
                    return False
                return True
            except ValueError:
                return False
        
        elif ioc_type == 'domains':
            # Filter out common system domains
            system_domains = [
                'microsoft.com', 'windows.com', 'msftncsi.com',
                'google.com', 'googleapis.com', 'gstatic.com'
            ]
            return not any(sys_domain in value.lower() for sys_domain in system_domains)
        
        return True

### **Automated Memory Analysis Pipeline**
\`\`\`python
import json
import os
from datetime import datetime

class MemoryAnalysisPipeline:
    def __init__(self, memory_file, output_dir):
        self.memory_file = memory_file
        self.output_dir = output_dir
        self.hunter = AdvancedProcessHunter(memory_file)
        self.ioc_extractor = MemoryIOCExtractor(self.hunter.context)
    
    def run_full_analysis(self):
        """Run comprehensive memory analysis"""
        analysis_results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'memory_file': self.memory_file,
            'results': {}
        }
        
        print("Starting memory analysis...")
        
        # Process analysis
        print("Analyzing processes...")
        analysis_results['results']['suspicious_processes'] = self.hunter.hunt_suspicious_processes()
        
        # Process injection hunting
        print("Hunting for process injection...")
        analysis_results['results']['injection_indicators'] = self.hunter.hunt_process_injection()
        
        # Network artifacts
        print("Extracting network artifacts...")
        analysis_results['results']['network_artifacts'] = self.hunter.extract_network_artifacts()
        
        # Malware signatures
        print("Scanning for malware signatures...")
        analysis_results['results']['malware_indicators'] = self.hunter.hunt_malware_signatures()
        
        # IOC extraction
        print("Extracting IOCs...")
        analysis_results['results']['extracted_iocs'] = self.ioc_extractor.extract_iocs_from_memory()
        
        # Generate summary
        analysis_results['summary'] = self._generate_summary(analysis_results['results'])
        
        # Save results
        output_file = os.path.join(self.output_dir, f"memory_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(output_file, 'w') as f:
            json.dump(analysis_results, f, indent=2, default=str)
        
        print(f"Analysis complete. Results saved to {output_file}")
        return analysis_results
    
    def _generate_summary(self, results):
        """Generate analysis summary"""
        return {
            'total_suspicious_processes': len(results.get('suspicious_processes', [])),
            'high_risk_processes': len([p for p in results.get('suspicious_processes', []) if p.get('suspicion_score', 0) > 70]),
            'injection_indicators': len(results.get('injection_indicators', [])),
            'network_connections': len(results.get('network_artifacts', [])),
            'malware_detections': len(results.get('malware_indicators', [])),
            'unique_ips': len(results.get('extracted_iocs', {}).get('ip_addresses', [])),
            'unique_domains': len(results.get('extracted_iocs', {}).get('domains', []))
        }

# Example usage
if __name__ == "__main__":
    pipeline = MemoryAnalysisPipeline("memory.raw", "/tmp/analysis_output")
    results = pipeline.run_full_analysis()
    
    # Print summary
    print("\\nAnalysis Summary:")
    for key, value in results['summary'].items():
        print(f"  {key}: {value}")
\`\`\`
      `,
      activities: [
        'Acquire and analyze memory dumps using Volatility 3',
        'Hunt for process injection and code injection techniques',
        'Extract network artifacts and IOCs from memory',
        'Build automated memory analysis pipelines'
      ]
    },

    {
      id: 'malware-memory-analysis',
      title: 'Malware Analysis in Memory',
      content: `
## Advanced Malware Memory Analysis

### **Rootkit Detection Techniques**
\`\`\`python
class RootkitHunter:
    def __init__(self, context):
        self.context = context
    
    def hunt_hidden_processes(self):
        """Hunt for hidden processes using multiple techniques"""
        hidden_processes = []
        
        # Compare different process enumeration methods
        pslist_pids = set(self._get_pslist_pids())
        psscan_pids = set(self._get_psscan_pids())
        thrdlist_pids = set(self._get_thread_owner_pids())
        
        # Find processes visible in some methods but not others
        hidden_in_pslist = (psscan_pids | thrdlist_pids) - pslist_pids
        
        for pid in hidden_in_pslist:
            hidden_processes.append({
                'pid': pid,
                'detection_method': 'Process list manipulation',
                'visibility': {
                    'pslist': pid in pslist_pids,
                    'psscan': pid in psscan_pids,
                    'threads': pid in thrdlist_pids
                }
            })
        
        return hidden_processes
    
    def hunt_ssdt_hooks(self):
        """Hunt for System Service Descriptor Table hooks"""
        ssdt_hooks = []
        
        # This would require custom Volatility plugin
        # Simplified example of concept
        
        return ssdt_hooks
    
    def hunt_inline_hooks(self):
        """Hunt for inline API hooks"""
        inline_hooks = []
        
        # Analyze system DLLs for modifications
        system_dlls = [
            'ntdll.dll', 'kernel32.dll', 'kernelbase.dll',
            'user32.dll', 'advapi32.dll', 'ws2_32.dll'
        ]
        
        for dll_name in system_dlls:
            hooks = self._analyze_dll_for_hooks(dll_name)
            inline_hooks.extend(hooks)
        
        return inline_hooks
    
    def _analyze_dll_for_hooks(self, dll_name):
        """Analyze DLL for inline hooks"""
        hooks = []
        
        # Get all processes
        pslist_plugin = volatility3.plugins.windows.pslist.PsList(
            self.context, config_path="plugins.PsList"
        )
        
        for process in pslist_plugin.list_processes():
            try:
                # Get process address space
                proc_space = process.get_process_address_space()
                if not proc_space:
                    continue
                
                # Find the DLL in process memory
                for vad in process.get_vad_root().traverse():
                    file_name = vad.get_file_name()
                    if file_name and dll_name.lower() in file_name.lower():
                        # Analyze DLL for hooks
                        dll_base = vad.get_start()
                        dll_size = vad.get_size()
                        
                        # Read DLL header
                        dll_data = proc_space.read(dll_base, min(dll_size, 4096))
                        if dll_data:
                            hook_indicators = self._check_for_hook_patterns(dll_data, dll_base)
                            if hook_indicators:
                                hooks.append({
                                    'process_pid': process.UniqueProcessId,
                                    'process_name': str(process.ImageFileName),
                                    'dll_name': dll_name,
                                    'dll_base': hex(dll_base),
                                    'hook_indicators': hook_indicators
                                })
            except:
                continue
        
        return hooks
    
    def _check_for_hook_patterns(self, dll_data, base_address):
        """Check for common hook patterns in DLL"""
        indicators = []
        
        # Look for jump instructions at function entry points
        # This is a simplified example
        for i in range(0, len(dll_data) - 5, 4):
            # Check for JMP instruction (0xE9)
            if dll_data[i] == 0xE9:
                # Extract jump target
                jump_offset = struct.unpack('<I', dll_data[i+1:i+5])[0]
                jump_target = base_address + i + 5 + jump_offset
                
                # Check if jump target is outside normal DLL range
                if jump_target < base_address or jump_target > base_address + len(dll_data):
                    indicators.append({
                        'type': 'Suspicious jump instruction',
                        'offset': hex(i),
                        'target': hex(jump_target)
                    })
        
        return indicators

### **Credential Extraction from Memory**
\`\`\`python
class CredentialHunter:
    def __init__(self, context):
        self.context = context
    
    def hunt_lsass_credentials(self):
        """Hunt for credentials in LSASS memory"""
        credentials = []
        
        # Find LSASS process
        pslist_plugin = volatility3.plugins.windows.pslist.PsList(
            self.context, config_path="plugins.PsList"
        )
        
        lsass_process = None
        for process in pslist_plugin.list_processes():
            if str(process.ImageFileName).lower() == 'lsass.exe':
                lsass_process = process
                break
        
        if not lsass_process:
            return credentials
        
        # Extract credentials using mimikatz-like techniques
        # This is a simplified example - real implementation would be much more complex
        
        try:
            proc_space = lsass_process.get_process_address_space()
            
            # Look for credential structures in LSASS memory
            for vad in lsass_process.get_vad_root().traverse():
                if vad.get_protection() & 0x04:  # PAGE_READWRITE
                    data = proc_space.read(vad.get_start(), min(vad.get_size(), 1024*1024))
                    if data:
                        creds = self._extract_credentials_from_data(data)
                        credentials.extend(creds)
        except:
            pass
        
        return credentials
    
    def _extract_credentials_from_data(self, data):
        """Extract credential patterns from memory data"""
        credentials = []
        
        # Look for common credential patterns
        # This is highly simplified - real credential extraction is much more complex
        
        # Look for NTLM hashes (32 hex characters)
        ntlm_pattern = re.compile(rb'[a-fA-F0-9]{32}')
        for match in ntlm_pattern.finditer(data):
            credentials.append({
                'type': 'NTLM Hash',
                'value': match.group().decode('ascii'),
                'offset': hex(match.start())
            })
        
        # Look for Kerberos tickets
        kerberos_pattern = re.compile(rb'krbtgt|kerberos|ticket')
        for match in kerberos_pattern.finditer(data):
            # Extract surrounding context
            start = max(0, match.start() - 100)
            end = min(len(data), match.end() + 100)
            context = data[start:end]
            
            credentials.append({
                'type': 'Kerberos Artifact',
                'context': context.hex(),
                'offset': hex(match.start())
            })
        
        return credentials

### **Memory-Based Persistence Detection**
\`\`\`python
class PersistenceHunter:
    def __init__(self, context):
        self.context = context
    
    def hunt_memory_persistence(self):
        """Hunt for memory-based persistence mechanisms"""
        persistence_indicators = []
        
        # Hunt for DLL hijacking
        dll_hijacking = self._hunt_dll_hijacking()
        persistence_indicators.extend(dll_hijacking)
        
        # Hunt for process hollowing
        process_hollowing = self._hunt_process_hollowing()
        persistence_indicators.extend(process_hollowing)
        
        # Hunt for reflective DLL loading
        reflective_dlls = self._hunt_reflective_dlls()
        persistence_indicators.extend(reflective_dlls)
        
        return persistence_indicators
    
    def _hunt_dll_hijacking(self):
        """Hunt for DLL hijacking indicators"""
        indicators = []
        
        # Check for DLLs loaded from unusual locations
        pslist_plugin = volatility3.plugins.windows.pslist.PsList(
            self.context, config_path="plugins.PsList"
        )
        
        for process in pslist_plugin.list_processes():
            try:
                for vad in process.get_vad_root().traverse():
                    file_name = vad.get_file_name()
                    if file_name and file_name.endswith('.dll'):
                        # Check if DLL is in suspicious location
                        suspicious_paths = [
                            '\\\\temp\\\\', '\\\\appdata\\\\', '\\\\downloads\\\\',
                            '\\\\users\\\\public\\\\', '\\\\programdata\\\\'
                        ]
                        
                        if any(path in file_name.lower() for path in suspicious_paths):
                            indicators.append({
                                'type': 'Suspicious DLL Location',
                                'process_pid': process.UniqueProcessId,
                                'process_name': str(process.ImageFileName),
                                'dll_path': file_name,
                                'base_address': hex(vad.get_start())
                            })
            except:
                continue
        
        return indicators
    
    def _hunt_process_hollowing(self):
        """Hunt for process hollowing indicators"""
        indicators = []
        
        pslist_plugin = volatility3.plugins.windows.pslist.PsList(
            self.context, config_path="plugins.PsList"
        )
        
        for process in pslist_plugin.list_processes():
            try:
                # Check if main executable region has been modified
                peb = process.get_peb()
                if peb:
                    image_base = peb.ImageBaseAddress
                    
                    # Find the main executable VAD
                    for vad in process.get_vad_root().traverse():
                        if vad.get_start() == image_base:
                            # Check if VAD properties suggest hollowing
                            if (vad.get_protection() & 0x40 and  # PAGE_EXECUTE_READWRITE
                                not vad.get_file_name()):        # No file backing
                                indicators.append({
                                    'type': 'Process Hollowing',
                                    'process_pid': process.UniqueProcessId,
                                    'process_name': str(process.ImageFileName),
                                    'image_base': hex(image_base),
                                    'protection': hex(vad.get_protection())
                                })
                            break
            except:
                continue
        
        return indicators
\`\`\`
      `,
      activities: [
        'Hunt for rootkits and advanced malware in memory',
        'Extract credentials and sensitive data from memory',
        'Detect memory-based persistence mechanisms',
        'Analyze advanced evasion techniques'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Advanced Malware Memory Analysis',
      description: 'Analyze sophisticated malware samples using memory forensics',
      tasks: [
        'Acquire memory dumps from infected systems',
        'Hunt for advanced persistence mechanisms',
        'Extract IOCs and network artifacts',
        'Reconstruct attack timeline from memory'
      ]
    },
    {
      title: 'Rootkit Detection Laboratory',
      description: 'Detect and analyze rootkits using memory analysis techniques',
      tasks: [
        'Deploy rootkit samples in controlled environment',
        'Use multiple detection techniques to identify hiding mechanisms',
        'Analyze SSDT and inline hooks',
        'Develop custom detection signatures'
      ]
    },
    {
      title: 'Memory Analysis Automation Project',
      description: 'Build automated memory analysis pipeline for enterprise use',
      tasks: [
        'Design scalable memory analysis architecture',
        'Implement automated analysis workflows',
        'Create custom Volatility plugins',
        'Build reporting and alerting system'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Memory Forensics Mastery Assessment',
      description: 'Demonstrate advanced memory analysis and malware hunting capabilities'
    },
    {
      type: 'project',
      title: 'Custom Memory Analysis Tool Development',
      description: 'Develop specialized memory analysis tool for specific threat hunting use case'
    }
  ],

  resources: [
    'Volatility 3 Documentation and Plugins',
    'Memory Forensics Best Practices',
    'Malware Analysis in Memory Techniques',
    'Rootkit Detection Methodologies',
    'Custom Plugin Development Guides',
    'Memory Acquisition Tools and Techniques'
  ]
};
