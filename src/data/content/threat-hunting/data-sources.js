/**
 * Threat Hunting Data Sources Module
 * Understanding and leveraging data sources for effective threat hunting
 */

export const dataSourcesContent = {
  id: "th-3",
  title: "Data Sources for Threat Hunting",
  description: "Learn about various data sources available for threat hunting and how to effectively leverage them for proactive threat detection.",
  difficulty: "Intermediate",
  estimatedTime: 90,
  objectives: [
    "Understand different types of threat hunting data sources",
    "Learn to identify high-value data sources",
    "Master data collection and aggregation techniques",
    "Develop effective data source strategies"
  ],
  sections: [
    {
      title: "Introduction to Threat Hunting Data Sources",
      content: `
        <h2>Threat Hunting Data Sources</h2>
        <p>Effective threat hunting relies on comprehensive data collection from various sources across the IT environment. Understanding what data is available and how to leverage it is crucial for successful hunting operations.</p>

        <h3>Types of Data Sources</h3>
        <ul>
          <li><strong>Network Data:</strong> Traffic flows, DNS queries, firewall logs</li>
          <li><strong>Endpoint Data:</strong> Process execution, file changes, registry modifications</li>
          <li><strong>Security Tools:</strong> SIEM alerts, IDS/IPS events, antivirus logs</li>
          <li><strong>Infrastructure Data:</strong> Server logs, database activity, cloud events</li>
          <li><strong>External Intelligence:</strong> Threat feeds, IOCs, vulnerability data</li>
        </ul>

        <h3>Data Quality Considerations</h3>
        <ul>
          <li>Completeness and coverage</li>
          <li>Accuracy and reliability</li>
          <li>Timeliness and freshness</li>
          <li>Context and enrichment</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Network Data Sources",
      content: `
        <h2>Network-Based Data Sources</h2>
        <p>Network data provides visibility into communication patterns and can reveal indicators of compromise and malicious activity.</p>

        <h3>Network Flow Data</h3>
        <ul>
          <li><strong>NetFlow/sFlow:</strong> Network traffic metadata</li>
          <li><strong>Connection Logs:</strong> Source/destination pairs with timing</li>
          <li><strong>Bandwidth Analysis:</strong> Data volume and transfer patterns</li>
          <li><strong>Protocol Distribution:</strong> Application layer protocols in use</li>
        </ul>

        <h3>DNS Data</h3>
        <ul>
          <li><strong>DNS Query Logs:</strong> Domain resolution requests</li>
          <li><strong>DNS Response Analysis:</strong> Resolution patterns and anomalies</li>
          <li><strong>Domain Generation Algorithms:</strong> DGA detection</li>
          <li><strong>DNS Tunneling:</strong> Covert channel detection</li>
        </ul>

        <h3>Firewall and Proxy Logs</h3>
        <ul>
          <li><strong>Allow/Deny Events:</strong> Traffic filtering decisions</li>
          <li><strong>URL Categories:</strong> Web content classification</li>
          <li><strong>File Downloads:</strong> Executable and document transfers</li>
          <li><strong>Geographic Analysis:</strong> Source and destination countries</li>
        </ul>

        <h3>Network Security Monitoring</h3>
        <ul>
          <li><strong>IDS/IPS Alerts:</strong> Signature-based detections</li>
          <li><strong>Network Behavior Analysis:</strong> Anomaly detection</li>
          <li><strong>Threat Intelligence Matching:</strong> IOC correlation</li>
          <li><strong>Protocol Analysis:</strong> Deep packet inspection results</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Endpoint Data Sources",
      content: `
        <h2>Endpoint-Based Data Sources</h2>
        <p>Endpoint data provides detailed visibility into system-level activities and is essential for detecting advanced threats.</p>

        <h3>Process Monitoring</h3>
        <ul>
          <li><strong>Process Creation:</strong> New process execution events</li>
          <li><strong>Process Relationships:</strong> Parent-child relationships</li>
          <li><strong>Command Line Arguments:</strong> Process parameters and flags</li>
          <li><strong>Process Termination:</strong> Exit codes and timing</li>
        </ul>

        <h3>File System Activity</h3>
        <ul>
          <li><strong>File Creation/Modification:</strong> File system changes</li>
          <li><strong>File Access Patterns:</strong> Read/write operations</li>
          <li><strong>File Hash Analysis:</strong> Cryptographic signatures</li>
          <li><strong>Executable Analysis:</strong> PE headers and metadata</li>
        </ul>

        <h3>Registry Monitoring (Windows)</h3>
        <ul>
          <li><strong>Registry Key Changes:</strong> Modifications to registry</li>
          <li><strong>Persistence Mechanisms:</strong> Startup and service entries</li>
          <li><strong>Configuration Changes:</strong> System and application settings</li>
          <li><strong>Malware Artifacts:</strong> Persistence and configuration</li>
        </ul>

        <h3>System Events</h3>
        <ul>
          <li><strong>Authentication Events:</strong> Login/logout activities</li>
          <li><strong>Privilege Changes:</strong> User account modifications</li>
          <li><strong>Service Activities:</strong> Windows services and daemons</li>
          <li><strong>Network Connections:</strong> Endpoint network activity</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Security Tool Data Sources",
      content: `
        <h2>Security Tool Integration</h2>
        <p>Leveraging data from existing security tools provides enriched context and accelerates threat hunting activities.</p>

        <h3>SIEM and Log Management</h3>
        <ul>
          <li><strong>Aggregated Logs:</strong> Centralized log collection</li>
          <li><strong>Correlation Rules:</strong> Automated event correlation</li>
          <li><strong>Alert Data:</strong> Security incidents and notifications</li>
          <li><strong>Dashboard Metrics:</strong> Security posture indicators</li>
        </ul>

        <h3>Endpoint Detection and Response (EDR)</h3>
        <ul>
          <li><strong>Behavioral Analysis:</strong> Endpoint behavior monitoring</li>
          <li><strong>IOC Scanning:</strong> Indicator matching and alerts</li>
          <li><strong>Threat Timeline:</strong> Attack progression tracking</li>
          <li><strong>Response Actions:</strong> Containment and remediation</li>
        </ul>

        <h3>Vulnerability Management</h3>
        <ul>
          <li><strong>Vulnerability Scans:</strong> Known security weaknesses</li>
          <li><strong>Patch Status:</strong> System update information</li>
          <li><strong>Risk Assessments:</strong> Vulnerability prioritization</li>
          <li><strong>Compliance Data:</strong> Regulatory requirements</li>
        </ul>

        <h3>Threat Intelligence Platforms</h3>
        <ul>
          <li><strong>IOC Feeds:</strong> Indicators of compromise</li>
          <li><strong>Threat Actor Profiles:</strong> Attribution and TTPs</li>
          <li><strong>Campaign Information:</strong> Ongoing threat activities</li>
          <li><strong>Contextual Analysis:</strong> Threat landscape insights</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Data Collection and Management",
      content: `
        <h2>Data Collection Strategies</h2>
        <p>Effective data collection requires strategic planning and proper implementation to ensure comprehensive coverage.</p>

        <h3>Collection Architecture</h3>
        <ul>
          <li><strong>Centralized Collection:</strong> Single aggregation point</li>
          <li><strong>Distributed Collection:</strong> Multiple collection nodes</li>
          <li><strong>Hybrid Approaches:</strong> Combination of methods</li>
          <li><strong>Cloud Integration:</strong> Cloud-native data sources</li>
        </ul>

        <h3>Data Retention and Storage</h3>
        <ul>
          <li><strong>Retention Policies:</strong> How long to keep data</li>
          <li><strong>Storage Optimization:</strong> Compression and archiving</li>
          <li><strong>Access Controls:</strong> Who can access what data</li>
          <li><strong>Compliance Requirements:</strong> Regulatory obligations</li>
        </ul>

        <h3>Data Enrichment</h3>
        <ul>
          <li><strong>Geolocation Data:</strong> IP address to location mapping</li>
          <li><strong>Asset Information:</strong> System and user context</li>
          <li><strong>Threat Intelligence:</strong> IOC and reputation data</li>
          <li><strong>Business Context:</strong> Criticality and ownership</li>
        </ul>

        <h3>Quality Assurance</h3>
        <ul>
          <li><strong>Data Validation:</strong> Accuracy and completeness checks</li>
          <li><strong>Normalization:</strong> Standardizing data formats</li>
          <li><strong>Deduplication:</strong> Removing duplicate entries</li>
          <li><strong>Performance Monitoring:</strong> Collection system health</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which type of data source is most valuable for detecting lateral movement?",
            options: [
              "Antivirus logs",
              "Network flow data",
              "Email logs",
              "Web proxy logs"
            ],
            correctAnswer: 1,
            explanation: "Network flow data is most valuable for detecting lateral movement as it shows communication patterns between systems, which is a key indicator of attackers moving through the network."
          },
          {
            question: "What is the primary benefit of endpoint process monitoring for threat hunting?",
            options: [
              "Network traffic analysis",
              "Detailed system-level activity visibility",
              "Email security monitoring",
              "Web browsing patterns"
            ],
            correctAnswer: 1,
            explanation: "Endpoint process monitoring provides detailed system-level activity visibility, allowing hunters to see exactly what processes are running and how they interact with the system."
          }
        ]
      },
      type: "quiz"
    }
  ]
}; 