/**
 * Threat Hunting Learning Path - 40 Comprehensive Modules
 * Master proactive threat detection from fundamentals to advanced operations
 * Complete curriculum for professional threat hunters
 */

// Foundation Phase (TH-1 to TH-10) - Core Fundamentals
import { threatHuntingIntroContent } from './threat-hunting-intro.js';
import { huntingMethodologyContent } from './hunting-methodology.js';
import { dataSourcesContent } from './data-sources.js';
import { hypothesisGenerationContent } from './hypothesis-generation.js';
import { huntingTechniquesContent } from './hunting-techniques.js';
import { behavioralAnalysisContent } from './behavioral-analysis.js';
import { anomalyDetectionContent } from './anomaly-detection.js';
import { networkHuntingContent } from './network-hunting.js';
import { endpointHuntingContent } from './endpoint-hunting.js';
import { logAnalysisHuntingContent } from './log-analysis-hunting.js';

// Intermediate Phase (TH-11 to TH-20) - Platform & Tool Mastery
import { huntingPlatformsContent } from './th-11-hunting-platforms.js';
import { splunkHuntingContent } from './th-12-splunk-hunting.js';
import { ********************** } from './th-13-elk-hunting.js';
import { siemIntegrationContent } from './th-14-siem-integration.js';
import { huntingToolsContent } from './th-15-hunting-tools.js';
import { memoryForensicsContent } from './th-16-memory-forensics.js';
import { malwareHuntingContent } from './th-17-malware-hunting.js';
import { aptHuntingContent } from './th-18-apt-hunting.js';
import { insiderThreatHuntingContent } from './th-19-insider-threat.js';
import { cloudHuntingContent } from './th-20-cloud-hunting.js';

// Advanced Phase (TH-21 to TH-30) - Specialized Environments
import { iotHuntingContent } from './th-21-iot-hunting.js';
import { otHuntingContent } from './th-22-ot-hunting.js';
import { mobileHuntingContent } from './th-23-mobile-hunting.js';
import { webAppHuntingContent } from './th-24-web-app-hunting.js';
import { dnsHuntingContent } from './th-25-dns-hunting.js';
import { huntingAutomationContent } from './th-26-hunting-automation.js';
import { pythonHuntingContent } from './th-27-python-hunting.js';
import { powershellHuntingContent } from './th-28-powershell-hunting.js';
import { yaraRulesContent } from './th-29-yara-rules.js';
import { sigmaRulesContent } from './th-30-sigma-rules.js';

// Expert Phase (TH-31 to TH-40) - Advanced Operations & Leadership
import { threatIntelIntegrationContent } from './th-31-threat-intel.js';
import { mitreAttackContent } from './th-32-mitre-attack.js';
import { huntingMetricsContent } from './th-33-hunting-metrics.js';
import { caseManagementContent } from './th-34-case-management.js';
import { huntingReportingContent } from './th-35-hunting-reporting.js';
import { huntingProgramManagementContent } from './th-36-program-management.js';
import { huntingTeamLeadershipContent } from './th-37-team-leadership.js';
import { huntingMaturityModelContent } from './th-38-maturity-model.js';
import { huntingInnovationContent } from './th-39-innovation.js';
import { capstoneProjectContent } from './th-40-capstone-project.js';

export const threatHuntingLearningPath = {
  id: "threat-hunting",
  title: "Threat Hunting",
  description: "Master comprehensive threat hunting from fundamentals to advanced operations. Learn to detect sophisticated threats across all environments - traditional IT, cloud, IoT, OT, and mobile. Build expertise in tools, techniques, automation, and program leadership.",
  category: "Proactive Defense & Detection",
  difficulty: "Beginner to Expert",
  estimatedTime: "1600+ hours",
  prerequisites: [
    "Basic cybersecurity fundamentals",
    "Understanding of networking concepts",
    "Familiarity with operating systems (Windows/Linux)",
    "Basic command-line experience",
    "Willingness to learn scripting and automation"
  ],
  outcomes: [
    "Lead enterprise threat hunting programs across all environments",
    "Develop sophisticated hunting hypotheses and methodologies",
    "Master behavioral analysis and anomaly detection techniques",
    "Implement automated hunting workflows and custom tools",
    "Integrate threat intelligence into proactive hunting operations",
    "Hunt threats in cloud, IoT, OT, mobile, and traditional environments",
    "Build and manage high-performing threat hunting teams",
    "Create comprehensive hunting metrics and reporting frameworks",
    "Develop custom detection rules (YARA, Sigma, SIEM queries)",
    "Perform advanced memory forensics and malware hunting",
    "Lead incident response and threat attribution efforts",
    "Design and implement threat hunting platforms and architectures"
  ],
  modules: [
    // Foundation Phase (TH-1 to TH-10) - Core Fundamentals
    threatHuntingIntroContent,
    huntingMethodologyContent,
    dataSourcesContent,
    hypothesisGenerationContent,
    huntingTechniquesContent,
    behavioralAnalysisContent,
    anomalyDetectionContent,
    networkHuntingContent,
    endpointHuntingContent,
    logAnalysisHuntingContent,

    // Intermediate Phase (TH-11 to TH-20) - Platform & Tool Mastery
    huntingPlatformsContent,
    splunkHuntingContent,
    **********************,
    siemIntegrationContent,
    huntingToolsContent,
    memoryForensicsContent,
    malwareHuntingContent,
    aptHuntingContent,
    insiderThreatHuntingContent,
    cloudHuntingContent,

    // Advanced Phase (TH-21 to TH-30) - Specialized Environments
    iotHuntingContent,
    otHuntingContent,
    mobileHuntingContent,
    webAppHuntingContent,
    dnsHuntingContent,
    huntingAutomationContent,
    pythonHuntingContent,
    powershellHuntingContent,
    yaraRulesContent,
    sigmaRulesContent,

    // Expert Phase (TH-31 to TH-40) - Advanced Operations & Leadership
    threatIntelIntegrationContent,
    mitreAttackContent,
    huntingMetricsContent,
    caseManagementContent,
    huntingReportingContent,
    huntingProgramManagementContent,
    huntingTeamLeadershipContent,
    huntingMaturityModelContent,
    huntingInnovationContent,
    capstoneProjectContent
  ]
};

export const getAllThreatHuntingModules = () => {
  return threatHuntingLearningPath.modules;
};

export const getThreatHuntingModuleById = (id) => {
  return threatHuntingLearningPath.modules.find(module => module.id === id);
}; 