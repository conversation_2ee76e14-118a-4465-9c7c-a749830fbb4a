/**
 * TH-18: APT Hunting
 * Master Advanced Persistent Threat detection and campaign tracking
 */

export const aptHuntingContent = {
  id: 'th-18',
  title: 'APT Hunting: Advanced Persistent Threats',
  description: 'Master the detection, analysis, and attribution of Advanced Persistent Threat campaigns using sophisticated hunting techniques.',
  duration: '48 hours',
  difficulty: 'Expert',

  objectives: [
    'Master APT campaign detection and tracking methodologies',
    'Implement long-term persistence hunting techniques',
    'Build threat actor attribution frameworks',
    'Develop APT-specific hunting hypotheses and playbooks',
    'Create advanced lateral movement detection systems',
    'Implement data exfiltration hunting techniques',
    'Build comprehensive APT intelligence and reporting systems'
  ],

  sections: [
    {
      id: 'apt-fundamentals',
      title: 'APT Campaign Analysis Fundamentals',
      content: `
## Advanced Persistent Threat Characteristics

### **APT Lifecycle and Hunting Strategies**
\`\`\`yaml
APT Attack Lifecycle:
  Initial Compromise:
    Techniques:
      - Spear phishing with custom malware
      - Watering hole attacks
      - Supply chain compromises
      - Zero-day exploits
    Hunting Focus:
      - Email attachment analysis
      - Web traffic anomalies
      - Software update mechanisms
      - Unusual process execution

  Establish Foothold:
    Techniques:
      - Custom backdoors and implants
      - Living-off-the-land techniques
      - Registry persistence
      - Service creation
    Hunting Focus:
      - Persistence mechanism analysis
      - Unusual service creation
      - Registry modifications
      - Scheduled task creation

  Escalate Privileges:
    Techniques:
      - Credential dumping (Mimikatz)
      - Token manipulation
      - Exploit local vulnerabilities
      - Pass-the-hash attacks
    Hunting Focus:
      - LSASS access patterns
      - Unusual privilege escalation
      - Token manipulation detection
      - Credential reuse patterns

  Internal Reconnaissance:
    Techniques:
      - Network scanning
      - Active Directory enumeration
      - File share discovery
      - System information gathering
    Hunting Focus:
      - Network scanning patterns
      - AD query anomalies
      - File share access patterns
      - System enumeration activities

  Lateral Movement:
    Techniques:
      - WMI/PowerShell remoting
      - PsExec and variants
      - RDP/SSH abuse
      - SMB exploitation
    Hunting Focus:
      - Remote execution patterns
      - Unusual authentication events
      - Cross-system process creation
      - Network share access

  Maintain Presence:
    Techniques:
      - Multiple persistence mechanisms
      - Backup communication channels
      - Dormant implants
      - Infrastructure rotation
    Hunting Focus:
      - Long-term behavioral patterns
      - Communication channel analysis
      - Infrastructure correlation
      - Dormant malware detection

  Complete Mission:
    Techniques:
      - Data collection and staging
      - Exfiltration via multiple channels
      - Evidence destruction
      - Infrastructure teardown
    Hunting Focus:
      - Data staging activities
      - Unusual data transfers
      - Log deletion patterns
      - Infrastructure changes
\`\`\`

### **APT Attribution Framework**
\`\`\`python
import json
import hashlib
from datetime import datetime, timedelta
from collections import defaultdict

class APTAttributionEngine:
    def __init__(self):
        self.threat_actors = {}
        self.campaigns = {}
        self.attribution_factors = {
            'infrastructure': 0.25,
            'malware_families': 0.20,
            'ttps': 0.20,
            'targeting': 0.15,
            'timing_patterns': 0.10,
            'linguistic_indicators': 0.10
        }

    def load_threat_actor_profiles(self, profiles_file):
        """Load known threat actor profiles"""
        with open(profiles_file, 'r') as f:
            self.threat_actors = json.load(f)

    def analyze_campaign_attribution(self, campaign_data):
        """Analyze campaign for threat actor attribution"""
        attribution_scores = {}

        for actor_name, actor_profile in self.threat_actors.items():
            score = self._calculate_attribution_score(campaign_data, actor_profile)
            attribution_scores[actor_name] = score

        # Sort by confidence score
        sorted_attributions = sorted(
            attribution_scores.items(),
            key=lambda x: x[1]['total_score'],
            reverse=True
        )

        return {
            'campaign_id': campaign_data['campaign_id'],
            'attribution_analysis': sorted_attributions[:5],  # Top 5 candidates
            'analysis_timestamp': datetime.now().isoformat(),
            'confidence_threshold': 0.7
        }

    def _calculate_attribution_score(self, campaign_data, actor_profile):
        """Calculate attribution confidence score"""
        scores = {}

        # Infrastructure overlap
        campaign_infrastructure = set(campaign_data.get('infrastructure', []))
        actor_infrastructure = set(actor_profile.get('known_infrastructure', []))

        if campaign_infrastructure and actor_infrastructure:
            overlap = len(campaign_infrastructure.intersection(actor_infrastructure))
            total = len(campaign_infrastructure.union(actor_infrastructure))
            scores['infrastructure'] = overlap / total if total > 0 else 0
        else:
            scores['infrastructure'] = 0

        # Malware family overlap
        campaign_malware = set(campaign_data.get('malware_families', []))
        actor_malware = set(actor_profile.get('malware_families', []))

        if campaign_malware and actor_malware:
            overlap = len(campaign_malware.intersection(actor_malware))
            scores['malware_families'] = overlap / len(campaign_malware) if campaign_malware else 0
        else:
            scores['malware_families'] = 0

        # TTP similarity
        campaign_ttps = set(campaign_data.get('ttps', []))
        actor_ttps = set(actor_profile.get('ttps', []))

        if campaign_ttps and actor_ttps:
            overlap = len(campaign_ttps.intersection(actor_ttps))
            scores['ttps'] = overlap / len(campaign_ttps) if campaign_ttps else 0
        else:
            scores['ttps'] = 0

        # Targeting similarity
        campaign_targets = set(campaign_data.get('target_sectors', []))
        actor_targets = set(actor_profile.get('target_sectors', []))

        if campaign_targets and actor_targets:
            overlap = len(campaign_targets.intersection(actor_targets))
            scores['targeting'] = overlap / len(campaign_targets) if campaign_targets else 0
        else:
            scores['targeting'] = 0

        # Timing patterns
        scores['timing_patterns'] = self._analyze_timing_patterns(
            campaign_data.get('activity_timeline', []),
            actor_profile.get('activity_patterns', {})
        )

        # Linguistic indicators
        scores['linguistic_indicators'] = self._analyze_linguistic_indicators(
            campaign_data.get('artifacts', []),
            actor_profile.get('linguistic_markers', [])
        )

        # Calculate weighted total score
        total_score = sum(
            scores.get(factor, 0) * weight
            for factor, weight in self.attribution_factors.items()
        )

        return {
            'total_score': total_score,
            'factor_scores': scores,
            'confidence_level': self._get_confidence_level(total_score)
        }

    def _analyze_timing_patterns(self, campaign_timeline, actor_patterns):
        """Analyze timing patterns for attribution"""
        if not campaign_timeline or not actor_patterns:
            return 0

        # Analyze working hours, days of week, etc.
        campaign_hours = [
            datetime.fromisoformat(event['timestamp']).hour
            for event in campaign_timeline
        ]

        actor_preferred_hours = actor_patterns.get('preferred_hours', [])

        if actor_preferred_hours:
            overlap = len(set(campaign_hours).intersection(set(actor_preferred_hours)))
            return overlap / len(set(campaign_hours)) if campaign_hours else 0

        return 0

    def _analyze_linguistic_indicators(self, campaign_artifacts, actor_markers):
        """Analyze linguistic indicators for attribution"""
        if not campaign_artifacts or not actor_markers:
            return 0

        # Simple text analysis for linguistic markers
        campaign_text = " ".join([
            artifact.get('text', '')
            for artifact in campaign_artifacts
        ]).lower()

        matches = sum(1 for marker in actor_markers if marker.lower() in campaign_text)
        return matches / len(actor_markers) if actor_markers else 0

    def _get_confidence_level(self, score):
        """Convert score to confidence level"""
        if score >= 0.8:
            return 'High'
        elif score >= 0.6:
            return 'Medium'
        elif score >= 0.4:
            return 'Low'
        else:
            return 'Very Low'

### **Long-term Persistence Detection**
\`\`\`python
class PersistenceHunter:
    def __init__(self):
        self.persistence_mechanisms = {
            'registry_run_keys': [
                'HKLM\\\\Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Run',
                'HKCU\\\\Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Run',
                'HKLM\\\\Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\RunOnce',
                'HKCU\\\\Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\RunOnce'
            ],
            'services': 'Windows Services',
            'scheduled_tasks': 'Task Scheduler',
            'startup_folders': 'Startup Folders',
            'wmi_events': 'WMI Event Subscriptions',
            'dll_hijacking': 'DLL Search Order Hijacking',
            'com_hijacking': 'COM Object Hijacking'
        }

    def hunt_advanced_persistence(self, system_data):
        """Hunt for advanced persistence mechanisms"""
        persistence_findings = []

        # Registry-based persistence
        registry_findings = self._hunt_registry_persistence(system_data.get('registry', []))
        persistence_findings.extend(registry_findings)

        # Service-based persistence
        service_findings = self._hunt_service_persistence(system_data.get('services', []))
        persistence_findings.extend(service_findings)

        # Scheduled task persistence
        task_findings = self._hunt_scheduled_task_persistence(system_data.get('tasks', []))
        persistence_findings.extend(task_findings)

        # WMI persistence
        wmi_findings = self._hunt_wmi_persistence(system_data.get('wmi_events', []))
        persistence_findings.extend(wmi_findings)

        # DLL hijacking
        dll_findings = self._hunt_dll_hijacking(system_data.get('loaded_dlls', []))
        persistence_findings.extend(dll_findings)

        return persistence_findings

    def _hunt_registry_persistence(self, registry_data):
        """Hunt for registry-based persistence"""
        findings = []

        for reg_entry in registry_data:
            key_path = reg_entry.get('key_path', '')
            value_name = reg_entry.get('value_name', '')
            value_data = reg_entry.get('value_data', '')

            # Check for suspicious run key entries
            for run_key in self.persistence_mechanisms['registry_run_keys']:
                if run_key.lower() in key_path.lower():
                    suspicion_score = self._calculate_registry_suspicion(reg_entry)

                    if suspicion_score > 50:
                        findings.append({
                            'type': 'Registry Persistence',
                            'mechanism': 'Run Key',
                            'key_path': key_path,
                            'value_name': value_name,
                            'value_data': value_data,
                            'suspicion_score': suspicion_score,
                            'indicators': self._get_registry_indicators(reg_entry)
                        })

        return findings

    def _calculate_registry_suspicion(self, reg_entry):
        """Calculate suspicion score for registry entry"""
        score = 0
        value_data = reg_entry.get('value_data', '').lower()

        # Suspicious file locations
        suspicious_paths = [
            'temp', 'appdata', 'programdata', 'users\\\\public',
            'downloads', 'documents', 'desktop'
        ]

        if any(path in value_data for path in suspicious_paths):
            score += 40

        # Suspicious file extensions
        suspicious_extensions = ['.bat', '.cmd', '.ps1', '.vbs', '.js']
        if any(ext in value_data for ext in suspicious_extensions):
            score += 30

        # Command line obfuscation
        if any(indicator in value_data for indicator in ['-enc', 'bypass', 'hidden']):
            score += 50

        # Unsigned executables
        if not reg_entry.get('signed', True):
            score += 20

        return min(score, 100)

    def _hunt_wmi_persistence(self, wmi_events):
        """Hunt for WMI-based persistence"""
        findings = []

        for event in wmi_events:
            event_type = event.get('event_type', '')

            # Look for suspicious WMI event subscriptions
            if event_type in ['EventFilter', 'EventConsumer', 'FilterToConsumerBinding']:
                suspicion_indicators = []

                # Check for suspicious queries
                query = event.get('query', '').lower()
                if any(suspicious in query for suspicious in [
                    'select * from', 'win32_process', 'create', 'commandline'
                ]):
                    suspicion_indicators.append('Suspicious WQL query')

                # Check for suspicious consumers
                consumer_type = event.get('consumer_type', '').lower()
                if consumer_type in ['commandlineeventconsumer', 'activescripteventconsumer']:
                    suspicion_indicators.append('High-risk event consumer')

                if suspicion_indicators:
                    findings.append({
                        'type': 'WMI Persistence',
                        'mechanism': 'Event Subscription',
                        'event_type': event_type,
                        'query': event.get('query'),
                        'consumer': event.get('consumer'),
                        'indicators': suspicion_indicators,
                        'suspicion_score': len(suspicion_indicators) * 30
                    })

        return findings
\`\`\`
      `,
      activities: [
        'Analyze APT campaign lifecycles and characteristics',
        'Build threat actor attribution framework',
        'Implement advanced persistence detection',
        'Create APT-specific hunting playbooks'
      ]
    },

    {
      id: 'lateral-movement-hunting',
      title: 'Advanced Lateral Movement Detection',
      content: `
## APT Lateral Movement Hunting

### **Cross-System Activity Correlation**
\`\`\`python
class LateralMovementHunter:
    def __init__(self):
        self.movement_patterns = {}
        self.authentication_timeline = []
        self.process_execution_timeline = []

    def detect_lateral_movement(self, network_data, timeframe_hours=24):
        """Detect lateral movement patterns across the network"""
        movement_indicators = []

        # Analyze authentication patterns
        auth_patterns = self._analyze_authentication_patterns(
            network_data.get('authentication_events', []),
            timeframe_hours
        )
        movement_indicators.extend(auth_patterns)

        # Analyze remote execution patterns
        remote_exec = self._analyze_remote_execution(
            network_data.get('process_events', []),
            timeframe_hours
        )
        movement_indicators.extend(remote_exec)

        # Analyze network connections
        network_patterns = self._analyze_network_patterns(
            network_data.get('network_connections', []),
            timeframe_hours
        )
        movement_indicators.extend(network_patterns)

        # Correlate findings
        correlated_movements = self._correlate_movement_indicators(movement_indicators)

        return {
            'detection_timestamp': datetime.now().isoformat(),
            'timeframe_analyzed': f"{timeframe_hours} hours",
            'individual_indicators': movement_indicators,
            'correlated_movements': correlated_movements,
            'risk_assessment': self._assess_movement_risk(correlated_movements)
        }

    def _analyze_authentication_patterns(self, auth_events, timeframe_hours):
        """Analyze authentication patterns for lateral movement"""
        indicators = []

        # Group authentication events by user
        user_auth_patterns = defaultdict(list)

        cutoff_time = datetime.now() - timedelta(hours=timeframe_hours)

        for event in auth_events:
            event_time = datetime.fromisoformat(event['timestamp'])
            if event_time >= cutoff_time:
                user = event.get('user')
                if user:
                    user_auth_patterns[user].append(event)

        # Analyze each user's authentication pattern
        for user, events in user_auth_patterns.items():
            # Sort events by time
            events.sort(key=lambda x: x['timestamp'])

            # Look for rapid authentication across multiple systems
            unique_systems = set(event.get('source_system') for event in events)

            if len(unique_systems) > 3:  # User authenticated to more than 3 systems
                time_span = (
                    datetime.fromisoformat(events[-1]['timestamp']) -
                    datetime.fromisoformat(events[0]['timestamp'])
                ).total_seconds() / 3600  # Convert to hours

                if time_span < 2:  # Within 2 hours
                    indicators.append({
                        'type': 'Rapid Multi-System Authentication',
                        'user': user,
                        'systems_accessed': list(unique_systems),
                        'time_span_hours': time_span,
                        'event_count': len(events),
                        'risk_score': min(len(unique_systems) * 20, 100),
                        'first_auth': events[0]['timestamp'],
                        'last_auth': events[-1]['timestamp']
                    })

            # Look for unusual authentication times
            unusual_times = []
            for event in events:
                hour = datetime.fromisoformat(event['timestamp']).hour
                if hour < 6 or hour > 22:  # Outside normal business hours
                    unusual_times.append(event)

            if len(unusual_times) > 2:
                indicators.append({
                    'type': 'Off-Hours Authentication Pattern',
                    'user': user,
                    'unusual_auth_count': len(unusual_times),
                    'systems': list(set(event.get('source_system') for event in unusual_times)),
                    'risk_score': min(len(unusual_times) * 15, 80)
                })

        return indicators

    def _analyze_remote_execution(self, process_events, timeframe_hours):
        """Analyze remote process execution patterns"""
        indicators = []

        cutoff_time = datetime.now() - timedelta(hours=timeframe_hours)

        # Filter recent events
        recent_events = [
            event for event in process_events
            if datetime.fromisoformat(event['timestamp']) >= cutoff_time
        ]

        # Group by parent process and source
        execution_chains = defaultdict(list)

        for event in recent_events:
            parent_process = event.get('parent_process', '')
            source_ip = event.get('source_ip', '')

            # Look for remote execution tools
            remote_tools = [
                'psexec', 'wmic', 'powershell', 'winrm', 'schtasks',
                'at.exe', 'sc.exe', 'net.exe', 'reg.exe'
            ]

            process_name = event.get('process_name', '').lower()
            command_line = event.get('command_line', '').lower()

            if any(tool in process_name or tool in command_line for tool in remote_tools):
                key = f"{source_ip}_{parent_process}"
                execution_chains[key].append(event)

        # Analyze execution chains
        for chain_key, events in execution_chains.items():
            if len(events) > 2:  # Multiple remote executions from same source
                source_ip, parent_process = chain_key.split('_', 1)

                unique_targets = set(event.get('target_system') for event in events)

                indicators.append({
                    'type': 'Remote Execution Chain',
                    'source_ip': source_ip,
                    'parent_process': parent_process,
                    'execution_count': len(events),
                    'target_systems': list(unique_targets),
                    'tools_used': list(set(
                        event.get('process_name') for event in events
                    )),
                    'risk_score': min(len(events) * 25, 100),
                    'time_span': self._calculate_time_span(events)
                })

        return indicators

    def _correlate_movement_indicators(self, indicators):
        """Correlate individual indicators into movement campaigns"""
        correlated_movements = []

        # Group indicators by user, IP, or time proximity
        correlation_groups = defaultdict(list)

        for indicator in indicators:
            # Create correlation keys
            correlation_keys = []

            if 'user' in indicator:
                correlation_keys.append(f"user_{indicator['user']}")

            if 'source_ip' in indicator:
                correlation_keys.append(f"ip_{indicator['source_ip']}")

            # Time-based correlation (within 1 hour)
            if 'first_auth' in indicator:
                time_bucket = datetime.fromisoformat(indicator['first_auth']).replace(
                    minute=0, second=0, microsecond=0
                )
                correlation_keys.append(f"time_{time_bucket.isoformat()}")

            # Add to correlation groups
            for key in correlation_keys:
                correlation_groups[key].append(indicator)

        # Find groups with multiple indicators
        for group_key, group_indicators in correlation_groups.items():
            if len(group_indicators) > 1:
                # Calculate combined risk score
                combined_risk = min(
                    sum(indicator.get('risk_score', 0) for indicator in group_indicators),
                    100
                )

                correlated_movements.append({
                    'correlation_key': group_key,
                    'indicator_count': len(group_indicators),
                    'indicators': group_indicators,
                    'combined_risk_score': combined_risk,
                    'movement_confidence': self._calculate_movement_confidence(group_indicators)
                })

        return correlated_movements

    def _calculate_movement_confidence(self, indicators):
        """Calculate confidence level for lateral movement"""
        # Multiple indicator types increase confidence
        indicator_types = set(indicator['type'] for indicator in indicators)

        if len(indicator_types) >= 3:
            return 'High'
        elif len(indicator_types) == 2:
            return 'Medium'
        else:
            return 'Low'

### **Data Exfiltration Detection**
\`\`\`python
class DataExfiltrationHunter:
    def __init__(self):
        self.baseline_transfer_patterns = {}
        self.suspicious_transfer_threshold = 1024 * 1024 * 100  # 100MB

    def hunt_data_exfiltration(self, network_data, file_access_data):
        """Hunt for data exfiltration activities"""
        exfiltration_indicators = []

        # Analyze unusual data transfers
        transfer_anomalies = self._analyze_data_transfers(network_data)
        exfiltration_indicators.extend(transfer_anomalies)

        # Analyze file access patterns
        file_access_anomalies = self._analyze_file_access_patterns(file_access_data)
        exfiltration_indicators.extend(file_access_anomalies)

        # Analyze data staging activities
        staging_activities = self._detect_data_staging(file_access_data)
        exfiltration_indicators.extend(staging_activities)

        return {
            'detection_timestamp': datetime.now().isoformat(),
            'exfiltration_indicators': exfiltration_indicators,
            'risk_assessment': self._assess_exfiltration_risk(exfiltration_indicators)
        }

    def _analyze_data_transfers(self, network_data):
        """Analyze network data transfers for exfiltration"""
        indicators = []

        # Group transfers by source IP and destination
        transfer_patterns = defaultdict(lambda: defaultdict(list))

        for connection in network_data.get('connections', []):
            src_ip = connection.get('source_ip')
            dst_ip = connection.get('destination_ip')
            bytes_out = connection.get('bytes_out', 0)

            if bytes_out > self.suspicious_transfer_threshold:
                transfer_patterns[src_ip][dst_ip].append(connection)

        # Analyze patterns
        for src_ip, destinations in transfer_patterns.items():
            for dst_ip, connections in destinations.items():
                total_bytes = sum(conn.get('bytes_out', 0) for conn in connections)

                # Check if destination is external
                if self._is_external_ip(dst_ip):
                    indicators.append({
                        'type': 'Large External Data Transfer',
                        'source_ip': src_ip,
                        'destination_ip': dst_ip,
                        'total_bytes': total_bytes,
                        'connection_count': len(connections),
                        'risk_score': min(total_bytes // (1024 * 1024 * 10), 100),  # 10MB = 1 point
                        'time_span': self._calculate_connection_time_span(connections)
                    })

        return indicators

    def _detect_data_staging(self, file_access_data):
        """Detect data staging activities"""
        indicators = []

        # Look for large file operations in temporary locations
        staging_locations = [
            'temp', 'tmp', 'appdata\\\\local\\\\temp', 'programdata',
            'users\\\\public', 'windows\\\\temp'
        ]

        for file_event in file_access_data:
            file_path = file_event.get('file_path', '').lower()
            operation = file_event.get('operation', '')
            file_size = file_event.get('file_size', 0)

            # Check for large files in staging locations
            if (any(location in file_path for location in staging_locations) and
                operation in ['create', 'write'] and
                file_size > 10 * 1024 * 1024):  # 10MB threshold

                indicators.append({
                    'type': 'Data Staging Activity',
                    'file_path': file_event.get('file_path'),
                    'operation': operation,
                    'file_size': file_size,
                    'user': file_event.get('user'),
                    'process': file_event.get('process'),
                    'timestamp': file_event.get('timestamp'),
                    'risk_score': min(file_size // (1024 * 1024), 100)
                })

        return indicators
\`\`\`
      `,
      activities: [
        'Build lateral movement detection algorithms',
        'Implement cross-system activity correlation',
        'Create data exfiltration hunting techniques',
        'Develop APT campaign timeline reconstruction'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'APT Campaign Investigation',
      description: 'Investigate sophisticated APT campaign using real-world techniques',
      tasks: [
        'Analyze APT29 (Cozy Bear) campaign artifacts',
        'Build threat actor attribution framework',
        'Track lateral movement across enterprise network',
        'Identify data exfiltration activities and methods'
      ]
    },
    {
      title: 'Advanced Persistence Hunting Lab',
      description: 'Hunt for sophisticated persistence mechanisms used by APT groups',
      tasks: [
        'Detect WMI-based persistence techniques',
        'Hunt for COM object hijacking',
        'Identify supply chain persistence methods',
        'Build automated persistence detection system'
      ]
    },
    {
      title: 'APT Intelligence Platform',
      description: 'Build comprehensive APT intelligence and tracking platform',
      tasks: [
        'Design APT campaign tracking architecture',
        'Implement threat actor attribution engine',
        'Create automated APT detection workflows',
        'Build executive APT threat reporting system'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'APT Hunting Mastery Assessment',
      description: 'Demonstrate advanced APT detection and attribution capabilities'
    },
    {
      type: 'project',
      title: 'Enterprise APT Defense System',
      description: 'Design and implement comprehensive APT detection and response system'
    }
  ],

  resources: [
    'MITRE ATT&CK APT Group Profiles',
    'APT Campaign Case Studies and Analysis',
    'Threat Actor Attribution Methodologies',
    'Advanced Persistence Techniques Research',
    'Lateral Movement Detection Frameworks',
    'Data Exfiltration Analysis Techniques'
  ]
};