/**
 * TH-28: PowerShell Threat Hunting
 * Master PowerShell-based attack detection and analysis
 */

export const powershellHuntingContent = {
  id: 'th-28',
  title: 'PowerShell Threat Hunting',
  description: 'Master comprehensive PowerShell threat hunting including obfuscation detection, malicious script analysis, and PowerShell-based attack prevention.',
  duration: '40 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Master PowerShell attack vector identification and analysis',
    'Implement PowerShell obfuscation detection and deobfuscation',
    'Build malicious PowerShell script detection systems',
    'Develop PowerShell behavioral analysis techniques',
    'Create automated PowerShell threat hunting workflows',
    'Implement PowerShell logging and monitoring strategies',
    'Build comprehensive PowerShell security frameworks'
  ],

  sections: [
    {
      id: 'powershell-attack-analysis',
      title: 'PowerShell Attack Vector Analysis',
      content: `
## PowerShell Threat Landscape

### **PowerShell Attack Categories**
\`\`\`yaml
PowerShell Attack Vectors:
  Execution Techniques:
    - Direct command execution
    - Script block execution
    - Encoded command execution
    - Remote script execution
    - Fileless execution
    
  Obfuscation Methods:
    - Base64 encoding
    - String concatenation
    - Variable substitution
    - Character replacement
    - Compression techniques
    - Invoke-Expression abuse
    
  Malicious Activities:
    - Credential harvesting (Mimikatz)
    - Lateral movement (PSRemoting)
    - Data exfiltration
    - Persistence mechanisms
    - Defense evasion
    - Reconnaissance
    
  Living-off-the-Land:
    - Built-in cmdlets abuse
    - WMI manipulation
    - Registry modifications
    - Service management
    - Process manipulation
    
  Advanced Techniques:
    - PowerShell Empire
    - Cobalt Strike beacons
    - Metasploit payloads
    - Custom frameworks
    - Reflective DLL loading

PowerShell Security Challenges:
  Default Configuration:
    - Execution policy bypass
    - Logging disabled by default
    - Script block logging optional
    - Module logging limited
    
  Legitimate Usage:
    - Administrative tasks
    - Automation scripts
    - System management
    - Application deployment
\`\`\`

### **PowerShell Hunting Framework**
\`\`\`python
import re
import base64
import zlib
import json
from datetime import datetime
from collections import defaultdict, Counter

class PowerShellHunter:
    def __init__(self):
        self.suspicious_cmdlets = [
            'Invoke-Expression', 'IEX', 'Invoke-Command', 'ICM',
            'New-Object', 'Start-Process', 'Invoke-WmiMethod',
            'Get-WmiObject', 'Invoke-RestMethod', 'Invoke-WebRequest',
            'DownloadString', 'DownloadFile', 'BitsTransfer',
            'Copy-Item', 'Move-Item', 'Remove-Item'
        ]
        
        self.obfuscation_patterns = [
            r'[A-Za-z0-9+/]{20,}={0,2}',  # Base64
            r'\\$\\([^)]+\\)',  # Variable substitution
            r'\\[char\\]\\d+',  # Character codes
            r'\\[convert\\]::\\w+',  # Type conversion
            r'-join\\s*\\(',  # String joining
            r'\\[string\\]::join',  # String join method
        ]
        
        self.malicious_indicators = [
            'mimikatz', 'sekurlsa', 'kerberos', 'lsadump',
            'invoke-mimikatz', 'dump-creds', 'get-password',
            'bypass', 'unrestricted', 'hidden', 'windowstyle',
            'encodedcommand', 'enc', 'noprofile', 'noninteractive'
        ]
        
    def analyze_powershell_logs(self, ps_logs):
        """Comprehensive PowerShell log analysis"""
        analysis_results = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_events': len(ps_logs),
            'suspicious_scripts': [],
            'obfuscated_commands': [],
            'malicious_indicators': [],
            'behavioral_anomalies': []
        }
        
        # Analyze each PowerShell event
        for log_entry in ps_logs:
            # Detect suspicious scripts
            if self._is_suspicious_script(log_entry):
                suspicious_script = self._analyze_suspicious_script(log_entry)
                analysis_results['suspicious_scripts'].append(suspicious_script)
            
            # Detect obfuscation
            if self._is_obfuscated_command(log_entry):
                obfuscated_cmd = self._analyze_obfuscated_command(log_entry)
                analysis_results['obfuscated_commands'].append(obfuscated_cmd)
            
            # Detect malicious indicators
            malicious_indicators = self._detect_malicious_indicators(log_entry)
            if malicious_indicators:
                analysis_results['malicious_indicators'].extend(malicious_indicators)
        
        # Detect behavioral anomalies
        behavioral_anomalies = self._detect_behavioral_anomalies(ps_logs)
        analysis_results['behavioral_anomalies'] = behavioral_anomalies
        
        return analysis_results
    
    def _is_suspicious_script(self, log_entry):
        """Check if PowerShell script is suspicious"""
        script_content = log_entry.get('script_block', '') or log_entry.get('command_line', '')
        
        if not script_content:
            return False
        
        script_lower = script_content.lower()
        
        # Check for suspicious cmdlets
        suspicious_cmdlet_count = sum(
            1 for cmdlet in self.suspicious_cmdlets 
            if cmdlet.lower() in script_lower
        )
        
        # Check for malicious indicators
        malicious_indicator_count = sum(
            1 for indicator in self.malicious_indicators 
            if indicator in script_lower
        )
        
        # Check for obfuscation patterns
        obfuscation_matches = sum(
            1 for pattern in self.obfuscation_patterns 
            if re.search(pattern, script_content, re.IGNORECASE)
        )
        
        # Determine suspicion level
        suspicion_score = (
            suspicious_cmdlet_count * 10 +
            malicious_indicator_count * 20 +
            obfuscation_matches * 15
        )
        
        return suspicion_score > 30
    
    def _analyze_suspicious_script(self, log_entry):
        """Analyze suspicious PowerShell script"""
        script_content = log_entry.get('script_block', '') or log_entry.get('command_line', '')
        
        analysis = {
            'timestamp': log_entry.get('timestamp'),
            'user': log_entry.get('user'),
            'process_id': log_entry.get('process_id'),
            'script_content': script_content[:1000],  # Truncate for storage
            'suspicious_elements': [],
            'threat_score': 0,
            'deobfuscated_content': None
        }
        
        # Identify suspicious elements
        script_lower = script_content.lower()
        
        for cmdlet in self.suspicious_cmdlets:
            if cmdlet.lower() in script_lower:
                analysis['suspicious_elements'].append({
                    'type': 'suspicious_cmdlet',
                    'value': cmdlet,
                    'risk_level': 'medium'
                })
                analysis['threat_score'] += 10
        
        for indicator in self.malicious_indicators:
            if indicator in script_lower:
                analysis['suspicious_elements'].append({
                    'type': 'malicious_indicator',
                    'value': indicator,
                    'risk_level': 'high'
                })
                analysis['threat_score'] += 20
        
        # Check for obfuscation
        if self._is_obfuscated_command({'script_block': script_content}):
            analysis['suspicious_elements'].append({
                'type': 'obfuscation_detected',
                'value': 'Multiple obfuscation techniques',
                'risk_level': 'high'
            })
            analysis['threat_score'] += 25
            
            # Attempt deobfuscation
            deobfuscated = self._deobfuscate_powershell(script_content)
            if deobfuscated != script_content:
                analysis['deobfuscated_content'] = deobfuscated[:1000]
        
        return analysis
    
    def _is_obfuscated_command(self, log_entry):
        """Check if PowerShell command is obfuscated"""
        script_content = log_entry.get('script_block', '') or log_entry.get('command_line', '')
        
        if not script_content:
            return False
        
        obfuscation_indicators = 0
        
        # Check for base64 encoding
        if re.search(r'[A-Za-z0-9+/]{50,}={0,2}', script_content):
            obfuscation_indicators += 1
        
        # Check for character code usage
        if re.search(r'\\[char\\]\\d+', script_content, re.IGNORECASE):
            obfuscation_indicators += 1
        
        # Check for string concatenation
        if script_content.count('+') > 10:
            obfuscation_indicators += 1
        
        # Check for variable substitution
        if re.search(r'\\$\\([^)]+\\)', script_content):
            obfuscation_indicators += 1
        
        # Check for invoke-expression usage
        if re.search(r'(invoke-expression|iex)\\s*\\(', script_content, re.IGNORECASE):
            obfuscation_indicators += 1
        
        return obfuscation_indicators >= 2
    
    def _deobfuscate_powershell(self, script_content):
        """Attempt to deobfuscate PowerShell script"""
        deobfuscated = script_content
        
        try:
            # Decode base64 strings
            base64_matches = re.findall(r'[A-Za-z0-9+/]{20,}={0,2}', script_content)
            for match in base64_matches:
                try:
                    decoded = base64.b64decode(match).decode('utf-8', errors='ignore')
                    if len(decoded) > 10 and decoded.isprintable():
                        deobfuscated = deobfuscated.replace(match, f"[DECODED: {decoded}]")
                except:
                    continue
            
            # Replace character codes
            char_pattern = r'\\[char\\](\\d+)'
            def replace_char(match):
                try:
                    char_code = int(match.group(1))
                    if 32 <= char_code <= 126:  # Printable ASCII
                        return chr(char_code)
                except:
                    pass
                return match.group(0)
            
            deobfuscated = re.sub(char_pattern, replace_char, deobfuscated, flags=re.IGNORECASE)
            
            # Simplify string concatenations
            deobfuscated = re.sub(r"'([^']*)'\\s*\\+\\s*'([^']*)'", r"'\\1\\2'", deobfuscated)
            deobfuscated = re.sub(r'"([^"]*)"\\s*\\+\\s*"([^"]*)"', r'"\\1\\2"', deobfuscated)
            
        except Exception as e:
            # If deobfuscation fails, return original
            pass
        
        return deobfuscated
    
    def _detect_malicious_indicators(self, log_entry):
        """Detect malicious indicators in PowerShell event"""
        indicators = []
        script_content = log_entry.get('script_block', '') or log_entry.get('command_line', '')
        
        if not script_content:
            return indicators
        
        script_lower = script_content.lower()
        
        # Credential harvesting indicators
        cred_indicators = ['mimikatz', 'sekurlsa', 'lsadump', 'dump-creds', 'get-password']
        for indicator in cred_indicators:
            if indicator in script_lower:
                indicators.append({
                    'type': 'credential_harvesting',
                    'indicator': indicator,
                    'timestamp': log_entry.get('timestamp'),
                    'user': log_entry.get('user'),
                    'severity': 'critical'
                })
        
        # Lateral movement indicators
        lateral_indicators = ['invoke-command', 'enter-pssession', 'new-pssession', 'winrm']
        for indicator in lateral_indicators:
            if indicator in script_lower:
                indicators.append({
                    'type': 'lateral_movement',
                    'indicator': indicator,
                    'timestamp': log_entry.get('timestamp'),
                    'user': log_entry.get('user'),
                    'severity': 'high'
                })
        
        # Data exfiltration indicators
        exfil_indicators = ['invoke-webrequest', 'downloadstring', 'uploadfile', 'send-mailmessage']
        for indicator in exfil_indicators:
            if indicator in script_lower:
                indicators.append({
                    'type': 'data_exfiltration',
                    'indicator': indicator,
                    'timestamp': log_entry.get('timestamp'),
                    'user': log_entry.get('user'),
                    'severity': 'high'
                })
        
        # Persistence indicators
        persistence_indicators = ['new-service', 'set-service', 'schtasks', 'register-scheduledjob']
        for indicator in persistence_indicators:
            if indicator in script_lower:
                indicators.append({
                    'type': 'persistence',
                    'indicator': indicator,
                    'timestamp': log_entry.get('timestamp'),
                    'user': log_entry.get('user'),
                    'severity': 'medium'
                })
        
        return indicators

### **Advanced PowerShell Analysis**
\`\`\`python
class AdvancedPowerShellAnalyzer:
    def __init__(self):
        self.behavioral_baselines = {}
        self.script_families = {}
        
    def build_powershell_behavioral_baseline(self, historical_logs):
        """Build behavioral baseline for PowerShell usage"""
        baseline = {
            'baseline_timestamp': datetime.now().isoformat(),
            'user_patterns': {},
            'cmdlet_usage': {},
            'execution_patterns': {},
            'script_characteristics': {}
        }
        
        # Analyze user patterns
        user_activity = defaultdict(list)
        for log_entry in historical_logs:
            user = log_entry.get('user')
            if user:
                user_activity[user].append(log_entry)
        
        for user, activities in user_activity.items():
            baseline['user_patterns'][user] = {
                'avg_daily_executions': len(activities) / 30,  # Assuming 30-day baseline
                'common_cmdlets': self._get_common_cmdlets(activities),
                'execution_times': self._analyze_execution_times(activities),
                'script_complexity': self._analyze_script_complexity(activities)
            }
        
        # Analyze overall cmdlet usage
        all_cmdlets = []
        for log_entry in historical_logs:
            cmdlets = self._extract_cmdlets(log_entry.get('script_block', ''))
            all_cmdlets.extend(cmdlets)
        
        cmdlet_counts = Counter(all_cmdlets)
        baseline['cmdlet_usage'] = {
            'most_common': cmdlet_counts.most_common(20),
            'total_unique': len(cmdlet_counts),
            'usage_distribution': dict(cmdlet_counts)
        }
        
        return baseline
    
    def detect_powershell_anomalies(self, current_logs, baseline):
        """Detect PowerShell behavioral anomalies"""
        anomalies = []
        
        # Analyze current activity against baseline
        current_user_activity = defaultdict(list)
        for log_entry in current_logs:
            user = log_entry.get('user')
            if user:
                current_user_activity[user].append(log_entry)
        
        # Check for user behavior anomalies
        for user, activities in current_user_activity.items():
            user_baseline = baseline['user_patterns'].get(user, {})
            
            if user_baseline:
                # Check execution frequency anomaly
                current_executions = len(activities)
                baseline_avg = user_baseline.get('avg_daily_executions', 0)
                
                if current_executions > baseline_avg * 5:  # 5x normal activity
                    anomalies.append({
                        'type': 'execution_frequency_anomaly',
                        'user': user,
                        'current_executions': current_executions,
                        'baseline_average': baseline_avg,
                        'severity': 'medium'
                    })
                
                # Check for unusual cmdlets
                current_cmdlets = set()
                for activity in activities:
                    cmdlets = self._extract_cmdlets(activity.get('script_block', ''))
                    current_cmdlets.update(cmdlets)
                
                baseline_cmdlets = set(user_baseline.get('common_cmdlets', []))
                unusual_cmdlets = current_cmdlets - baseline_cmdlets
                
                if len(unusual_cmdlets) > 5:
                    anomalies.append({
                        'type': 'unusual_cmdlet_usage',
                        'user': user,
                        'unusual_cmdlets': list(unusual_cmdlets),
                        'severity': 'low'
                    })
        
        return anomalies
    
    def classify_powershell_script_family(self, script_content):
        """Classify PowerShell script into known families"""
        classification = {
            'family': 'unknown',
            'confidence': 0,
            'characteristics': [],
            'similar_scripts': []
        }
        
        # Check for known PowerShell frameworks
        frameworks = {
            'PowerShell Empire': ['invoke-empire', 'empire', 'get-system'],
            'Cobalt Strike': ['beacon', 'cobaltstrike', 'malleable'],
            'Metasploit': ['meterpreter', 'metasploit', 'payload'],
            'PowerSploit': ['powersploit', 'invoke-mimikatz', 'get-gpppassword'],
            'Nishang': ['nishang', 'invoke-powershelltcp', 'invoke-shellcode']
        }
        
        script_lower = script_content.lower()
        
        for framework, indicators in frameworks.items():
            matches = sum(1 for indicator in indicators if indicator in script_lower)
            if matches > 0:
                confidence = min(matches / len(indicators), 1.0)
                if confidence > classification['confidence']:
                    classification['family'] = framework
                    classification['confidence'] = confidence
        
        # Analyze script characteristics
        characteristics = []
        
        if 'base64' in script_lower or re.search(r'[A-Za-z0-9+/]{50,}', script_content):
            characteristics.append('base64_encoded')
        
        if 'invoke-expression' in script_lower or 'iex' in script_lower:
            characteristics.append('dynamic_execution')
        
        if 'downloadstring' in script_lower or 'invoke-webrequest' in script_lower:
            characteristics.append('network_download')
        
        if len(script_content) > 10000:
            characteristics.append('large_script')
        
        if script_content.count('$') > 20:
            characteristics.append('variable_heavy')
        
        classification['characteristics'] = characteristics
        
        return classification
\`\`\`
      `,
      activities: [
        'Analyze PowerShell attack vectors and techniques',
        'Build PowerShell obfuscation detection systems',
        'Implement malicious PowerShell script analysis',
        'Create PowerShell behavioral baseline and anomaly detection'
      ]
    },

    {
      id: 'powershell-monitoring-response',
      title: 'PowerShell Monitoring and Response',
      content: `
## PowerShell Security Monitoring and Response

### **PowerShell Logging Configuration**
\`\`\`powershell
# Enhanced PowerShell Logging Configuration

# Enable Script Block Logging
$regPath = "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\PowerShell\\ScriptBlockLogging"
if (!(Test-Path $regPath)) {
    New-Item -Path $regPath -Force
}
Set-ItemProperty -Path $regPath -Name "EnableScriptBlockLogging" -Value 1
Set-ItemProperty -Path $regPath -Name "EnableScriptBlockInvocationLogging" -Value 1

# Enable Module Logging
$regPath = "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\PowerShell\\ModuleLogging"
if (!(Test-Path $regPath)) {
    New-Item -Path $regPath -Force
}
Set-ItemProperty -Path $regPath -Name "EnableModuleLogging" -Value 1

# Configure Module Names to Log
$regPath = "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\PowerShell\\ModuleLogging\\ModuleNames"
if (!(Test-Path $regPath)) {
    New-Item -Path $regPath -Force
}
Set-ItemProperty -Path $regPath -Name "*" -Value "*"

# Enable Transcription
$regPath = "HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\PowerShell\\Transcription"
if (!(Test-Path $regPath)) {
    New-Item -Path $regPath -Force
}
Set-ItemProperty -Path $regPath -Name "EnableTranscripting" -Value 1
Set-ItemProperty -Path $regPath -Name "EnableInvocationHeader" -Value 1
Set-ItemProperty -Path $regPath -Name "OutputDirectory" -Value "C:\\PSTranscripts"

# Configure Execution Policy
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine -Force
\`\`\`

### **Real-time PowerShell Monitoring**
\`\`\`python
import win32evtlog
import win32evtlogutil
import win32con
import json
from datetime import datetime

class PowerShellMonitor:
    def __init__(self):
        self.event_log_name = 'Microsoft-Windows-PowerShell/Operational'
        self.monitoring_rules = {}
        self.alert_handlers = []

    def setup_realtime_monitoring(self):
        """Setup real-time PowerShell event monitoring"""
        monitoring_config = {
            'log_sources': [
                'Microsoft-Windows-PowerShell/Operational',
                'Windows PowerShell',
                'Microsoft-Windows-WinRM/Operational'
            ],
            'event_ids': [
                4103,  # Module logging
                4104,  # Script block logging
                4105,  # Script block logging (start)
                4106,  # Script block logging (stop)
                53504  # PowerShell console startup
            ],
            'monitoring_rules': self._create_monitoring_rules()
        }

        return monitoring_config

    def _create_monitoring_rules(self):
        """Create PowerShell monitoring rules"""
        rules = [
            {
                'rule_id': 'ps_obfuscation_detection',
                'name': 'PowerShell Obfuscation Detection',
                'event_ids': [4104],
                'conditions': [
                    'base64_encoded_content',
                    'character_substitution',
                    'invoke_expression_usage'
                ],
                'severity': 'high',
                'action': 'alert_and_block'
            },
            {
                'rule_id': 'ps_credential_harvesting',
                'name': 'PowerShell Credential Harvesting',
                'event_ids': [4104],
                'conditions': [
                    'mimikatz_indicators',
                    'lsass_access',
                    'credential_dumping'
                ],
                'severity': 'critical',
                'action': 'immediate_alert'
            },
            {
                'rule_id': 'ps_lateral_movement',
                'name': 'PowerShell Lateral Movement',
                'event_ids': [4104, 4103],
                'conditions': [
                    'invoke_command_usage',
                    'remote_session_creation',
                    'winrm_usage'
                ],
                'severity': 'high',
                'action': 'alert_and_investigate'
            },
            {
                'rule_id': 'ps_download_execution',
                'name': 'PowerShell Download and Execute',
                'event_ids': [4104],
                'conditions': [
                    'downloadstring_usage',
                    'invoke_webrequest',
                    'remote_script_execution'
                ],
                'severity': 'high',
                'action': 'alert_and_block'
            }
        ]

        return rules

    def monitor_powershell_events(self):
        """Monitor PowerShell events in real-time"""
        try:
            # Open event log
            hand = win32evtlog.OpenEventLog(None, self.event_log_name)

            # Read events
            events = win32evtlog.ReadEventLog(
                hand,
                win32evtlog.EVENTLOG_BACKWARDS_READ | win32evtlog.EVENTLOG_SEQUENTIAL_READ,
                0
            )

            for event in events:
                if event.EventID in [4103, 4104, 4105, 4106]:
                    processed_event = self._process_powershell_event(event)

                    # Check against monitoring rules
                    alerts = self._check_monitoring_rules(processed_event)

                    # Handle alerts
                    for alert in alerts:
                        self._handle_alert(alert)

            win32evtlog.CloseEventLog(hand)

        except Exception as e:
            print(f"Error monitoring PowerShell events: {e}")

    def _process_powershell_event(self, event):
        """Process PowerShell event log entry"""
        processed_event = {
            'event_id': event.EventID,
            'timestamp': event.TimeGenerated.isoformat(),
            'computer': event.ComputerName,
            'user': event.Sid,
            'process_id': None,
            'script_content': '',
            'cmdlets_used': [],
            'parameters': {}
        }

        # Extract event data
        if event.StringInserts:
            for i, insert in enumerate(event.StringInserts):
                if i == 0:  # Usually contains script content
                    processed_event['script_content'] = insert
                elif 'ProcessId' in insert:
                    processed_event['process_id'] = insert

        # Extract cmdlets from script content
        if processed_event['script_content']:
            cmdlets = self._extract_cmdlets(processed_event['script_content'])
            processed_event['cmdlets_used'] = cmdlets

        return processed_event

    def _check_monitoring_rules(self, event):
        """Check event against monitoring rules"""
        alerts = []

        for rule in self.monitoring_rules.values():
            if event['event_id'] in rule['event_ids']:
                if self._evaluate_rule_conditions(rule, event):
                    alert = {
                        'alert_id': f"ps_alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        'rule_id': rule['rule_id'],
                        'rule_name': rule['name'],
                        'severity': rule['severity'],
                        'timestamp': datetime.now().isoformat(),
                        'event_details': event,
                        'recommended_action': rule['action']
                    }
                    alerts.append(alert)

        return alerts

    def create_powershell_hunting_queries(self):
        """Create PowerShell hunting queries for various platforms"""
        queries = {
            'splunk': {
                'obfuscated_powershell': '''
                index=windows EventCode=4104
                | eval ScriptBlockText=coalesce(ScriptBlockText, Message)
                | where match(ScriptBlockText, "(?i)(bypass|unrestricted|hidden|encodedcommand|invoke-expression|[A-Za-z0-9+/]{50,})")
                | eval ObfuscationScore=case(
                    match(ScriptBlockText, "[A-Za-z0-9+/]{100,}"), 30,
                    match(ScriptBlockText, "(?i)(bypass|unrestricted)"), 20,
                    match(ScriptBlockText, "(?i)(invoke-expression|iex)"), 25,
                    match(ScriptBlockText, "(?i)encodedcommand"), 35,
                    1=1, 10
                )
                | where ObfuscationScore > 25
                | table _time, Computer, User, ScriptBlockText, ObfuscationScore
                ''',

                'credential_harvesting': '''
                index=windows EventCode=4104
                | eval ScriptBlockText=coalesce(ScriptBlockText, Message)
                | where match(ScriptBlockText, "(?i)(mimikatz|sekurlsa|lsadump|dump-creds|get-password|invoke-mimikatz)")
                | eval ThreatLevel="Critical"
                | table _time, Computer, User, ScriptBlockText, ThreatLevel
                ''',

                'lateral_movement': '''
                index=windows EventCode=4104 OR EventCode=4103
                | eval ScriptBlockText=coalesce(ScriptBlockText, Message)
                | where match(ScriptBlockText, "(?i)(invoke-command|enter-pssession|new-pssession|winrm|psremoting)")
                | eval MovementType=case(
                    match(ScriptBlockText, "(?i)invoke-command"), "Remote Command Execution",
                    match(ScriptBlockText, "(?i)(enter-pssession|new-pssession)"), "Interactive Session",
                    match(ScriptBlockText, "(?i)winrm"), "WinRM Usage",
                    1=1, "Unknown"
                )
                | table _time, Computer, User, ScriptBlockText, MovementType
                '''
            },

            'elasticsearch': {
                'obfuscated_powershell': {
                    "query": {
                        "bool": {
                            "must": [
                                {"term": {"event.code": "4104"}},
                                {
                                    "regexp": {
                                        "powershell.script_block.text": ".*(?i)(bypass|unrestricted|hidden|encodedcommand|invoke-expression|[A-Za-z0-9+/]{50,}).*"
                                    }
                                }
                            ]
                        }
                    },
                    "aggs": {
                        "users": {"terms": {"field": "user.name.keyword"}},
                        "computers": {"terms": {"field": "host.name.keyword"}}
                    }
                },

                'download_execute': {
                    "query": {
                        "bool": {
                            "must": [
                                {"term": {"event.code": "4104"}},
                                {
                                    "regexp": {
                                        "powershell.script_block.text": ".*(?i)(downloadstring|invoke-webrequest|webclient|bitsadmin).*"
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }

        return queries

    def implement_powershell_response_actions(self, alert):
        """Implement automated response actions for PowerShell threats"""
        response_actions = []

        severity = alert.get('severity', 'medium')
        rule_id = alert.get('rule_id', '')

        # Critical severity responses
        if severity == 'critical':
            response_actions.extend([
                {'action': 'isolate_host', 'target': alert['event_details']['computer']},
                {'action': 'disable_user_account', 'target': alert['event_details']['user']},
                {'action': 'kill_powershell_processes', 'target': alert['event_details']['process_id']},
                {'action': 'collect_memory_dump', 'target': alert['event_details']['computer']},
                {'action': 'notify_security_team', 'urgency': 'immediate'}
            ])

        # High severity responses
        elif severity == 'high':
            response_actions.extend([
                {'action': 'block_powershell_execution', 'target': alert['event_details']['user']},
                {'action': 'increase_logging_level', 'target': alert['event_details']['computer']},
                {'action': 'collect_process_artifacts', 'target': alert['event_details']['process_id']},
                {'action': 'notify_security_team', 'urgency': 'high'}
            ])

        # Rule-specific responses
        if 'credential_harvesting' in rule_id:
            response_actions.extend([
                {'action': 'force_password_reset', 'target': 'all_domain_users'},
                {'action': 'revoke_kerberos_tickets', 'target': 'domain_controllers'},
                {'action': 'enable_credential_guard', 'target': 'all_endpoints'}
            ])

        elif 'lateral_movement' in rule_id:
            response_actions.extend([
                {'action': 'block_psremoting', 'target': alert['event_details']['computer']},
                {'action': 'monitor_network_connections', 'target': alert['event_details']['computer']},
                {'action': 'audit_privileged_accounts', 'target': 'domain'}
            ])

        return response_actions
\`\`\`
      `,
      activities: [
        'Configure comprehensive PowerShell logging',
        'Build real-time PowerShell monitoring system',
        'Create PowerShell hunting queries for multiple platforms',
        'Implement automated PowerShell threat response'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'PowerShell Attack Simulation',
      description: 'Simulate and detect various PowerShell-based attacks',
      tasks: [
        'Set up PowerShell attack simulation environment',
        'Execute obfuscated PowerShell attacks',
        'Build detection rules for PowerShell threats',
        'Test deobfuscation and analysis techniques'
      ]
    },
    {
      title: 'PowerShell Behavioral Analysis',
      description: 'Build PowerShell behavioral analysis and anomaly detection',
      tasks: [
        'Collect baseline PowerShell usage data',
        'Build behavioral analysis models',
        'Implement anomaly detection algorithms',
        'Create PowerShell user behavior profiles'
      ]
    },
    {
      title: 'Enterprise PowerShell Security',
      description: 'Implement enterprise-wide PowerShell security monitoring',
      tasks: [
        'Deploy PowerShell logging across enterprise',
        'Build centralized PowerShell monitoring system',
        'Create automated response workflows',
        'Develop PowerShell security policies'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'PowerShell Hunting Mastery',
      description: 'Demonstrate advanced PowerShell threat hunting and analysis capabilities'
    },
    {
      type: 'project',
      title: 'Enterprise PowerShell Security Solution',
      description: 'Design and implement comprehensive PowerShell security monitoring and response system'
    }
  ],

  resources: [
    'PowerShell Security Best Practices',
    'PowerShell Obfuscation Techniques and Detection',
    'PowerShell Attack Frameworks Analysis',
    'PowerShell Logging and Monitoring Configuration',
    'PowerShell Forensics and Investigation',
    'Enterprise PowerShell Security Policies'
  ]
};
