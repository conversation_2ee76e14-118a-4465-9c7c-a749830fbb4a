export const networkHuntingContent = {
  title: 'Network Hunting',
  description: 'Advanced techniques for hunting threats across network infrastructure.',
  labs: [
    {
      title: 'Network Traffic Analysis Lab',
      description: 'Analyze network traffic patterns to identify potential threats',
      difficulty: 'Advanced',
      duration: '2 hours',
      objectives: [
        'Learn to capture and analyze network traffic',
        'Identify suspicious network patterns',
        'Use tools like Wireshark and Zeek for analysis'
      ]
    },
    {
      title: 'Network Flow Analysis',
      description: 'Investigate network flows to detect anomalies',
      difficulty: 'Intermediate',
      duration: '1.5 hours',
      objectives: [
        'Understand NetFlow and IPFIX data',
        'Analyze flow records for suspicious activity',
        'Correlate flow data with other security events'
      ]
    }
  ],
  useCases: [
    {
      title: 'Detecting Lateral Movement',
      description: 'Identify unauthorized lateral movement across the network',
      scenario: 'Investigate unusual network connections between internal systems',
      mitreTactics: ['Lateral Movement', 'Discovery'],
      tools: ['Wireshark', 'Zeek', 'Network Flow Analyzers']
    },
    {
      title: 'Command & Control Detection',
      description: 'Uncover malicious C2 communications',
      scenario: 'Analyze network traffic for suspicious outbound connections',
      mitreTactics: ['Command and Control'],
      tools: ['Network IDS', 'Traffic Analysis Tools']
    }
  ],
  mitreMapping: [
    {
      tactic: 'Discovery',
      techniques: [
        'Network Service Discovery',
        'Network Share Discovery',
        'Remote System Discovery'
      ]
    },
    {
      tactic: 'Lateral Movement',
      techniques: [
        'Internal Spearphishing',
        'Remote Services',
        'Replication Through Removable Media'
      ]
    }
  ]
}; 