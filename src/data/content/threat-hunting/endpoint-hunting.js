export const endpointHuntingContent = {
  title: 'Endpoint Hunting',
  description: 'Advanced techniques for hunting threats on endpoints and workstations.',
  labs: [
    {
      title: 'Endpoint Detection Lab',
      description: 'Learn to detect and analyze suspicious activities on endpoints',
      difficulty: 'Advanced',
      duration: '2 hours',
      objectives: [
        'Master endpoint monitoring tools and techniques',
        'Analyze process behavior and system changes',
        'Investigate suspicious activities in real-time'
      ]
    },
    {
      title: 'Memory Analysis Workshop',
      description: 'Deep dive into memory forensics and analysis',
      difficulty: 'Expert',
      duration: '3 hours',
      objectives: [
        'Learn memory acquisition techniques',
        'Analyze memory dumps for malicious artifacts',
        'Identify advanced persistence mechanisms'
      ]
    }
  ],
  useCases: [
    {
      title: 'Malware Persistence Detection',
      description: 'Identify and analyze malware persistence mechanisms',
      scenario: 'Investigate suspicious startup entries and scheduled tasks',
      mitreTactics: ['Persistence', 'Privilege Escalation'],
      tools: ['Autoruns', 'Process Explorer', 'Volatility']
    },
    {
      title: 'Lateral Movement Detection',
      description: 'Detect unauthorized lateral movement attempts',
      scenario: 'Analyze suspicious network connections and process creation',
      mitreTactics: ['Lateral Movement', 'Execution'],
      tools: ['Sysmon', 'Windows Event Logs', 'EDR Solutions']
    }
  ],
  mitreMapping: [
    {
      tactic: 'Persistence',
      techniques: [
        'Registry Run Keys',
        'Scheduled Tasks',
        'Service Installation',
        'WMI Event Subscription'
      ]
    },
    {
      tactic: 'Privilege Escalation',
      techniques: [
        'Process Injection',
        'Access Token Manipulation',
        'Bypass User Account Control'
      ]
    }
  ],
  tools: [
    {
      name: 'Sysmon',
      description: 'System Monitor for Windows',
      useCase: 'Process and network connection monitoring'
    },
    {
      name: 'Volatility',
      description: 'Memory Forensics Framework',
      useCase: 'Memory analysis and malware detection'
    },
    {
      name: 'Process Explorer',
      description: 'Advanced Process Viewer',
      useCase: 'Process analysis and DLL inspection'
    }
  ],
  prerequisites: [
    'Basic understanding of Windows internals',
    'Familiarity with process monitoring',
    'Knowledge of common persistence mechanisms'
  ]
}; 