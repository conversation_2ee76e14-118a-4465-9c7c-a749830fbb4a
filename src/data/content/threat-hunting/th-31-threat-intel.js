/**
 * TH-31: Threat Intelligence Integration
 * Master threat intelligence platforms and integration for enhanced hunting
 */

export const threatIntelIntegrationContent = {
  id: 'th-31',
  title: 'Threat Intelligence Integration',
  description: 'Master threat intelligence platforms, IOC management, and intelligence-driven hunting methodologies.',
  duration: '42 hours',
  difficulty: 'Advanced',

  objectives: [
    'Master MISP and OpenCTI threat intelligence platforms',
    'Implement automated IOC collection and management',
    'Build intelligence-driven hunting workflows',
    'Create threat actor attribution and campaign tracking',
    'Develop custom threat intelligence feeds and sources',
    'Integrate threat intelligence with hunting platforms',
    'Build threat intelligence sharing and collaboration frameworks'
  ],

  sections: [
    {
      id: 'threat-intel-fundamentals',
      title: 'Threat Intelligence Fundamentals',
      content: `
## Threat Intelligence Framework

### **Intelligence Types and Sources**
\`\`\`yaml
Strategic Intelligence:
  Description: High-level threat landscape analysis
  Sources:
    - Government reports and advisories
    - Industry threat reports
    - Security vendor research
    - Academic research
  Use Cases:
    - Executive briefings
    - Risk assessment
    - Security strategy planning

Tactical Intelligence:
  Description: TTPs and attack methodologies
  Sources:
    - Malware analysis reports
    - Incident response findings
    - Security research blogs
    - Conference presentations
  Use Cases:
    - Detection rule development
    - Hunting hypothesis generation
    - Security control tuning

Operational Intelligence:
  Description: Specific campaigns and threat actors
  Sources:
    - Threat actor profiles
    - Campaign tracking reports
    - Attribution analysis
    - Victim notifications
  Use Cases:
    - Threat hunting campaigns
    - Incident attribution
    - Proactive defense

Technical Intelligence:
  Description: IOCs and technical artifacts
  Sources:
    - IOC feeds
    - Malware samples
    - Network signatures
    - YARA rules
  Use Cases:
    - Automated detection
    - IOC blocking
    - Signature development
\`\`\`

### **Intelligence Lifecycle**
\`\`\`python
class ThreatIntelligenceLifecycle:
    def __init__(self):
        self.phases = [
            'Direction', 'Collection', 'Processing',
            'Analysis', 'Dissemination', 'Feedback'
        ]
        self.current_phase = 'Direction'

    def direction_phase(self, requirements):
        """Define intelligence requirements and priorities"""
        intel_requirements = {
            'priority_threats': requirements.get('priority_threats', []),
            'geographic_focus': requirements.get('geographic_focus', []),
            'industry_focus': requirements.get('industry_focus', []),
            'technical_focus': requirements.get('technical_focus', []),
            'timeline': requirements.get('timeline', '30 days')
        }

        return {
            'phase': 'Direction',
            'requirements': intel_requirements,
            'collection_plan': self._create_collection_plan(intel_requirements)
        }

    def collection_phase(self, collection_plan):
        """Collect raw intelligence from various sources"""
        collected_data = {
            'open_sources': self._collect_osint(),
            'commercial_feeds': self._collect_commercial_intel(),
            'government_sources': self._collect_government_intel(),
            'industry_sharing': self._collect_industry_intel(),
            'internal_sources': self._collect_internal_intel()
        }

        return {
            'phase': 'Collection',
            'raw_data': collected_data,
            'collection_timestamp': datetime.now().isoformat()
        }

    def processing_phase(self, raw_data):
        """Process and normalize collected intelligence"""
        processed_intel = {
            'normalized_iocs': self._normalize_iocs(raw_data),
            'structured_reports': self._structure_reports(raw_data),
            'deduplicated_data': self._deduplicate_intelligence(raw_data),
            'quality_scored': self._score_intelligence_quality(raw_data)
        }

        return {
            'phase': 'Processing',
            'processed_data': processed_intel
        }

    def analysis_phase(self, processed_data):
        """Analyze processed intelligence for actionable insights"""
        analysis_results = {
            'threat_assessment': self._assess_threats(processed_data),
            'attribution_analysis': self._analyze_attribution(processed_data),
            'campaign_tracking': self._track_campaigns(processed_data),
            'hunting_hypotheses': self._generate_hunting_hypotheses(processed_data)
        }

        return {
            'phase': 'Analysis',
            'analysis': analysis_results
        }
\`\`\`
      `,
      activities: [
        'Design threat intelligence requirements',
        'Map intelligence sources and feeds',
        'Implement intelligence lifecycle processes',
        'Create intelligence quality scoring framework'
      ]
    },

    {
      id: 'misp-platform',
      title: 'MISP Platform Mastery',
      content: `
## MISP (Malware Information Sharing Platform)

### **MISP Configuration and Setup**
\`\`\`bash
# MISP Installation (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install apache2 mysql-server php php-mysql php-xml php-mbstring

# Download and install MISP
cd /var/www/html
sudo git clone https://github.com/MISP/MISP.git
cd MISP
sudo git submodule update --init --recursive

# Configure MISP
sudo cp app/Config/bootstrap.default.php app/Config/bootstrap.php
sudo cp app/Config/database.default.php app/Config/database.php
sudo cp app/Config/core.default.php app/Config/core.php
sudo cp app/Config/config.default.php app/Config/config.php

# Set permissions
sudo chown -R www-data:www-data /var/www/html/MISP
sudo chmod -R 755 /var/www/html/MISP
\`\`\`

### **MISP API Integration**
\`\`\`python
import requests
import json
from datetime import datetime, timedelta

class MISPConnector:
    def __init__(self, misp_url, api_key, verify_ssl=True):
        self.misp_url = misp_url.rstrip('/')
        self.api_key = api_key
        self.verify_ssl = verify_ssl
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': api_key,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })

    def search_attributes(self, search_params):
        """Search for attributes in MISP"""
        url = f"{self.misp_url}/attributes/restSearch"

        response = self.session.post(
            url,
            data=json.dumps(search_params),
            verify=self.verify_ssl
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"MISP search failed: {response.status_code} - {response.text}")

    def get_iocs_by_type(self, ioc_type, days_back=30):
        """Get IOCs by type from recent events"""
        search_params = {
            'returnFormat': 'json',
            'type': ioc_type,
            'timestamp': (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d'),
            'to_ids': True,
            'published': True
        }

        return self.search_attributes(search_params)

    def create_event(self, event_data):
        """Create new event in MISP"""
        url = f"{self.misp_url}/events"

        response = self.session.post(
            url,
            data=json.dumps(event_data),
            verify=self.verify_ssl
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Event creation failed: {response.status_code} - {response.text}")

    def add_attributes_to_event(self, event_id, attributes):
        """Add attributes to existing event"""
        url = f"{self.misp_url}/attributes/add/{event_id}"

        results = []
        for attribute in attributes:
            response = self.session.post(
                url,
                data=json.dumps(attribute),
                verify=self.verify_ssl
            )
            results.append(response.json() if response.status_code == 200 else response.text)

        return results

    def get_threat_actor_events(self, threat_actor_name):
        """Get events related to specific threat actor"""
        search_params = {
            'returnFormat': 'json',
            'eventinfo': threat_actor_name,
            'published': True
        }

        url = f"{self.misp_url}/events/restSearch"
        response = self.session.post(
            url,
            data=json.dumps(search_params),
            verify=self.verify_ssl
        )

        return response.json() if response.status_code == 200 else []

### **Automated IOC Enrichment**
\`\`\`python
class IOCEnrichmentEngine:
    def __init__(self, misp_connector):
        self.misp = misp_connector
        self.enrichment_sources = {
            'virustotal': self._enrich_with_virustotal,
            'shodan': self._enrich_with_shodan,
            'passive_dns': self._enrich_with_passive_dns,
            'whois': self._enrich_with_whois
        }

    def enrich_ioc(self, ioc_value, ioc_type):
        """Enrich IOC with additional intelligence"""
        enrichment_data = {
            'ioc_value': ioc_value,
            'ioc_type': ioc_type,
            'enrichment_timestamp': datetime.now().isoformat(),
            'sources': {}
        }

        # Check MISP for existing intelligence
        misp_data = self._get_misp_intelligence(ioc_value, ioc_type)
        if misp_data:
            enrichment_data['sources']['misp'] = misp_data

        # Enrich with external sources
        for source_name, enrichment_func in self.enrichment_sources.items():
            try:
                source_data = enrichment_func(ioc_value, ioc_type)
                if source_data:
                    enrichment_data['sources'][source_name] = source_data
            except Exception as e:
                enrichment_data['sources'][source_name] = {'error': str(e)}

        # Calculate composite risk score
        enrichment_data['risk_score'] = self._calculate_risk_score(enrichment_data['sources'])

        return enrichment_data

    def _get_misp_intelligence(self, ioc_value, ioc_type):
        """Get existing intelligence from MISP"""
        try:
            search_params = {
                'returnFormat': 'json',
                'value': ioc_value,
                'type': ioc_type
            }

            results = self.misp.search_attributes(search_params)

            if results and 'response' in results:
                return {
                    'found': True,
                    'events': len(results['response'].get('Attribute', [])),
                    'last_seen': self._get_latest_timestamp(results['response']),
                    'threat_actors': self._extract_threat_actors(results['response'])
                }
        except:
            pass

        return {'found': False}

    def _enrich_with_virustotal(self, ioc_value, ioc_type):
        """Enrich IOC with VirusTotal data"""
        # Implementation would call VirusTotal API
        return {
            'reputation': 'malicious',
            'detections': 45,
            'total_engines': 70,
            'first_seen': '2024-01-01',
            'last_analysis': '2024-01-15'
        }

    def _calculate_risk_score(self, sources):
        """Calculate composite risk score from all sources"""
        score = 0

        # MISP intelligence
        if sources.get('misp', {}).get('found'):
            score += 30

        # VirusTotal detections
        vt_data = sources.get('virustotal', {})
        if vt_data.get('detections', 0) > 0:
            detection_ratio = vt_data['detections'] / vt_data.get('total_engines', 1)
            score += int(detection_ratio * 40)

        # Shodan exposure
        if sources.get('shodan', {}).get('exposed_services'):
            score += 20

        # Passive DNS suspicious domains
        if sources.get('passive_dns', {}).get('suspicious_domains'):
            score += 15

        return min(score, 100)  # Cap at 100
\`\`\`
      `,
      activities: [
        'Deploy and configure MISP platform',
        'Build MISP API integrations',
        'Create automated IOC enrichment workflows',
        'Implement threat actor tracking system'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Threat Intelligence Platform Deployment',
      description: 'Deploy and configure comprehensive threat intelligence platform',
      tasks: [
        'Install and configure MISP platform',
        'Set up threat intelligence feeds',
        'Build automated IOC collection system',
        'Create intelligence sharing workflows'
      ]
    },
    {
      title: 'Intelligence-Driven Hunting Campaign',
      description: 'Execute comprehensive hunting campaign using threat intelligence',
      tasks: [
        'Analyze threat actor TTPs',
        'Generate hunting hypotheses',
        'Execute multi-platform hunting',
        'Attribute findings to threat campaigns'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Threat Intelligence Integration Mastery',
      description: 'Demonstrate advanced threat intelligence integration capabilities'
    }
  ],

  resources: [
    'MISP Documentation and Best Practices',
    'OpenCTI Platform Guide',
    'Threat Intelligence Sharing Standards',
    'IOC Management Frameworks'
  ]
};