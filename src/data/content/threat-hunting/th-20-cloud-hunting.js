/**
 * TH-20: Cloud Threat Hunting
 * Master threat hunting across AWS, Azure, GCP, and multi-cloud environments
 */

export const cloudHuntingContent = {
  id: 'th-20',
  title: 'Cloud Threat Hunting: AWS, Azure & GCP',
  description: 'Master comprehensive threat hunting across all major cloud platforms, from basic cloud security to advanced multi-cloud threat detection and response.',
  duration: '45 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Master cloud-native threat hunting methodologies',
    'Hunt threats across AWS, Azure, and Google Cloud Platform',
    'Implement cloud security monitoring and detection',
    'Develop cloud-specific hunting hypotheses and techniques',
    'Build automated cloud threat detection systems',
    'Investigate cloud-based attacks and incidents',
    'Secure containerized and serverless environments'
  ],

  sections: [
    {
      id: 'cloud-fundamentals',
      title: 'Cloud Security Fundamentals for Hunters',
      content: `
## Cloud Threat Landscape

### Common Cloud Attack Vectors
- **Identity and Access Management (IAM) Abuse**
  - Privilege escalation through misconfigurations
  - Credential stuffing and password spraying
  - Service account compromise
  - Cross-account access abuse

- **Data Exposure and Exfiltration**
  - Misconfigured storage buckets (S3, Blob, Cloud Storage)
  - Database exposure through security groups
  - API key leakage and abuse
  - Insider threats and data theft

- **Resource Hijacking**
  - Cryptomining on compromised instances
  - Compute resource abuse for botnets
  - Storage abuse for illegal content
  - Network bandwidth theft

### Cloud-Specific Threat Hunting Challenges
- **Ephemeral Infrastructure**: Resources that appear and disappear rapidly
- **Shared Responsibility Model**: Understanding what you vs. cloud provider monitors
- **API-Driven Operations**: Most activities happen through APIs, not traditional logs
- **Multi-Tenancy**: Distinguishing legitimate from malicious cross-tenant activities
- **Scale and Velocity**: Massive log volumes and rapid change rates

### Essential Cloud Hunting Data Sources
\`\`\`yaml
AWS:
  - CloudTrail: API calls and management events
  - VPC Flow Logs: Network traffic metadata
  - GuardDuty: ML-based threat detection
  - Config: Resource configuration changes
  - CloudWatch: Metrics and application logs

Azure:
  - Activity Log: Subscription-level events
  - Resource Logs: Service-specific logs
  - Security Center: Security posture and alerts
  - Sentinel: SIEM and threat hunting platform
  - Network Security Group Flow Logs

GCP:
  - Cloud Audit Logs: Admin and data access logs
  - VPC Flow Logs: Network traffic data
  - Security Command Center: Security findings
  - Cloud Logging: Centralized logging service
  - Chronicle: Security analytics platform
\`\`\`
      `,
      activities: [
        'Set up cloud logging and monitoring',
        'Configure threat detection services',
        'Practice cloud CLI and API usage',
        'Explore cloud security dashboards'
      ]
    },

    {
      id: 'aws-hunting',
      title: 'AWS Threat Hunting Techniques',
      content: `
## AWS-Specific Hunting Techniques

### 1. CloudTrail Analysis for Threat Hunting

#### Suspicious IAM Activities
\`\`\`json
# CloudTrail query for privilege escalation
{
  "eventName": ["AttachUserPolicy", "AttachRolePolicy", "PutUserPolicy", "PutRolePolicy"],
  "errorCode": {"exists": false},
  "userIdentity.type": "IAMUser",
  "sourceIPAddress": {"not": ["AWS Internal"]}
}
\`\`\`

#### Unusual API Call Patterns
\`\`\`sql
-- Athena query for anomalous API usage
SELECT 
    useridentity.username,
    sourceipaddress,
    eventname,
    COUNT(*) as call_count,
    COUNT(DISTINCT eventname) as unique_events
FROM cloudtrail_logs
WHERE eventtime >= current_timestamp - interval '24' hour
GROUP BY useridentity.username, sourceipaddress, eventname
HAVING call_count > 100 OR unique_events > 50
ORDER BY call_count DESC;
\`\`\`

### 2. VPC Flow Logs Hunting

#### Data Exfiltration Detection
\`\`\`python
# Python script for analyzing VPC Flow Logs
import pandas as pd
import numpy as np

def detect_data_exfiltration(flow_logs):
    # Calculate bytes out per source IP
    outbound_traffic = flow_logs[flow_logs['direction'] == 'egress']
    traffic_summary = outbound_traffic.groupby(['srcaddr', 'dstaddr']).agg({
        'bytes': 'sum',
        'packets': 'sum',
        'start': 'min',
        'end': 'max'
    }).reset_index()
    
    # Identify statistical outliers
    Q1 = traffic_summary['bytes'].quantile(0.25)
    Q3 = traffic_summary['bytes'].quantile(0.75)
    IQR = Q3 - Q1
    outlier_threshold = Q3 + 1.5 * IQR
    
    suspicious_flows = traffic_summary[traffic_summary['bytes'] > outlier_threshold]
    return suspicious_flows
\`\`\`

### 3. GuardDuty Integration

#### Custom Threat Intelligence Integration
\`\`\`python
import boto3

def enrich_guardduty_findings():
    guardduty = boto3.client('guardduty')
    detector_id = 'your-detector-id'
    
    findings = guardduty.get_findings(
        DetectorId=detector_id,
        FindingCriteria={
            'Criterion': {
                'severity': {'Gte': 7.0},
                'updatedAt': {'Gte': '2024-01-01T00:00:00Z'}
            }
        }
    )
    
    for finding in findings['Findings']:
        # Enrich with custom threat intelligence
        iocs = extract_iocs(finding)
        threat_context = lookup_threat_intelligence(iocs)
        
        # Create custom hunting hypothesis
        hypothesis = generate_hunting_hypothesis(finding, threat_context)
        print(f"Hunting Hypothesis: {hypothesis}")
\`\`\`

### 4. S3 Security Hunting

#### Bucket Enumeration Detection
\`\`\`sql
-- Detect S3 bucket enumeration attempts
SELECT 
    sourceipaddress,
    useragent,
    eventname,
    errorcode,
    COUNT(*) as attempt_count
FROM s3_access_logs
WHERE eventname IN ('ListBucket', 'GetBucketAcl', 'GetBucketPolicy')
    AND errorcode IN ('NoSuchBucket', 'AccessDenied')
    AND eventtime >= current_timestamp - interval '1' hour
GROUP BY sourceipaddress, useragent, eventname, errorcode
HAVING attempt_count > 10
ORDER BY attempt_count DESC;
\`\`\`
      `,
      activities: [
        'Analyze CloudTrail logs for suspicious activities',
        'Hunt for data exfiltration in VPC Flow Logs',
        'Integrate GuardDuty with custom threat intelligence',
        'Detect S3 bucket enumeration and abuse'
      ]
    },

    {
      id: 'azure-hunting',
      title: 'Azure Threat Hunting with Sentinel',
      content: `
## Azure Sentinel Hunting Techniques

### 1. KQL Queries for Threat Hunting

#### Suspicious Azure AD Sign-ins
\`\`\`kql
SigninLogs
| where TimeGenerated > ago(24h)
| where RiskLevelDuringSignIn == "high" or RiskLevelAggregated == "high"
| extend GeoInfo = strcat(LocationDetails.countryOrRegion, " - ", LocationDetails.city)
| summarize 
    SigninCount = count(),
    Countries = make_set(LocationDetails.countryOrRegion),
    IPs = make_set(IPAddress),
    Apps = make_set(AppDisplayName)
    by UserPrincipalName, bin(TimeGenerated, 1h)
| where array_length(Countries) > 1  // Multiple countries
| project TimeGenerated, UserPrincipalName, SigninCount, Countries, IPs, Apps
\`\`\`

#### Privilege Escalation Detection
\`\`\`kql
AuditLogs
| where TimeGenerated > ago(7d)
| where OperationName in ("Add member to role", "Add app role assignment to user")
| extend InitiatedBy = tostring(InitiatedBy.user.userPrincipalName)
| extend TargetUser = tostring(TargetResources[0].userPrincipalName)
| extend RoleName = tostring(TargetResources[0].displayName)
| where RoleName contains "Admin" or RoleName contains "Global"
| project TimeGenerated, InitiatedBy, TargetUser, RoleName, OperationName
| order by TimeGenerated desc
\`\`\`

### 2. Advanced Hunting Workbooks

#### Custom Threat Hunting Dashboard
\`\`\`json
{
  "version": "Notebook/1.0",
  "items": [
    {
      "type": 9,
      "content": {
        "version": "KqlParameterItem/1.0",
        "parameters": [
          {
            "id": "time-range",
            "version": "KqlParameterItem/1.0",
            "name": "TimeRange",
            "type": 4,
            "value": {
              "durationMs": ********
            }
          }
        ]
      }
    },
    {
      "type": 3,
      "content": {
        "version": "KqlItem/1.0",
        "query": "SecurityEvent\\n| where TimeGenerated {TimeRange}\\n| where EventID == 4625\\n| summarize FailedLogins = count() by Account, Computer\\n| top 10 by FailedLogins",
        "size": 0,
        "title": "Top Failed Login Attempts"
      }
    }
  ]
}
\`\`\`

### 3. Automated Hunting Rules

#### Custom Detection Rule
\`\`\`kql
// Rule: Detect potential Azure resource abuse
AzureActivity
| where TimeGenerated > ago(1h)
| where OperationNameValue contains "Microsoft.Compute/virtualMachines"
| where ActivityStatusValue == "Success"
| summarize 
    VMOperations = count(),
    Operations = make_set(OperationNameValue),
    ResourceGroups = make_set(ResourceGroup)
    by CallerIpAddress, Caller, bin(TimeGenerated, 5m)
| where VMOperations > 10  // Threshold for suspicious activity
| extend ThreatLevel = case(
    VMOperations > 50, "High",
    VMOperations > 25, "Medium",
    "Low"
)
\`\`\`
      `,
      activities: [
        'Build KQL hunting queries in Azure Sentinel',
        'Create custom hunting workbooks',
        'Develop automated detection rules',
        'Investigate Azure AD security events'
      ]
    },

    {
      id: 'gcp-hunting',
      title: 'Google Cloud Platform Threat Hunting',
      content: `
## GCP Threat Hunting with Chronicle and Cloud Logging

### 1. Cloud Audit Logs Analysis

#### Suspicious IAM Changes
\`\`\`sql
-- BigQuery query for GCP audit logs
SELECT
  timestamp,
  protoPayload.authenticationInfo.principalEmail as actor,
  protoPayload.methodName as method,
  protoPayload.resourceName as resource,
  protoPayload.request.policy.bindings as bindings
FROM \`project.dataset.cloudaudit_googleapis_com_activity\`
WHERE protoPayload.methodName LIKE "%setIamPolicy%"
  AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
  AND JSON_EXTRACT_SCALAR(protoPayload.request.policy.bindings, '$[0].role') LIKE "%admin%"
ORDER BY timestamp DESC;
\`\`\`

### 2. Chronicle YARA-L Rules

#### Detect GCP Resource Abuse
\`\`\`yara-l
rule gcp_compute_abuse {
  meta:
    author = "Threat Hunter"
    description = "Detect potential GCP compute resource abuse"
    
  events:
    $compute_create = $gcp.audit.method_name = "v1.compute.instances.insert"
    $compute_delete = $gcp.audit.method_name = "v1.compute.instances.delete"
    
  match:
    $compute_create and $compute_delete over 1h
    
  condition:
    #compute_create > 10 and #compute_delete > 10
}
\`\`\`

### 3. Container and Kubernetes Hunting

#### Suspicious Container Activities
\`\`\`yaml
# GKE audit log query
apiVersion: v1
kind: ConfigMap
metadata:
  name: hunting-queries
data:
  suspicious-pods.sql: |
    SELECT
      timestamp,
      protoPayload.request.metadata.name as pod_name,
      protoPayload.request.spec.containers[0].image as image,
      protoPayload.authenticationInfo.username as user
    FROM \`project.dataset.gke_audit_logs\`
    WHERE protoPayload.methodName = "create"
      AND protoPayload.request.kind = "Pod"
      AND (
        protoPayload.request.spec.containers[0].image LIKE "%:latest%"
        OR protoPayload.request.spec.containers[0].image NOT LIKE "gcr.io/%"
      )
\`\`\`
      `,
      activities: [
        'Analyze GCP audit logs with BigQuery',
        'Create Chronicle YARA-L detection rules',
        'Hunt threats in GKE environments',
        'Monitor container security events'
      ]
    },

    {
      id: 'multi-cloud-hunting',
      title: 'Multi-Cloud Hunting Strategies',
      content: `
## Cross-Cloud Threat Hunting

### 1. Unified Logging Strategy

#### SIEM Integration Architecture
\`\`\`python
# Multi-cloud log aggregation
import boto3
import azure.identity
import google.cloud.logging

class MultiCloudHunter:
    def __init__(self):
        self.aws_session = boto3.Session()
        self.azure_credential = azure.identity.DefaultAzureCredential()
        self.gcp_client = google.cloud.logging.Client()
    
    def hunt_across_clouds(self, ioc_list):
        results = {
            'aws': self.hunt_aws(ioc_list),
            'azure': self.hunt_azure(ioc_list),
            'gcp': self.hunt_gcp(ioc_list)
        }
        return self.correlate_findings(results)
    
    def correlate_findings(self, results):
        # Cross-cloud correlation logic
        correlated = []
        for aws_finding in results['aws']:
            for azure_finding in results['azure']:
                if self.is_related(aws_finding, azure_finding):
                    correlated.append({
                        'type': 'cross_cloud_activity',
                        'aws': aws_finding,
                        'azure': azure_finding,
                        'confidence': self.calculate_confidence(aws_finding, azure_finding)
                    })
        return correlated
\`\`\`

### 2. Cloud Attack Chain Analysis

#### Multi-Stage Cloud Attack Detection
\`\`\`sql
-- Unified query across cloud platforms
WITH cloud_events AS (
  SELECT 'aws' as cloud, event_time, user_identity, event_name, source_ip
  FROM aws_cloudtrail
  UNION ALL
  SELECT 'azure' as cloud, event_time, user_identity, operation_name, source_ip
  FROM azure_activity_log
  UNION ALL
  SELECT 'gcp' as cloud, event_time, user_identity, method_name, source_ip
  FROM gcp_audit_log
)
SELECT 
  source_ip,
  COUNT(DISTINCT cloud) as clouds_accessed,
  COUNT(DISTINCT user_identity) as identities_used,
  MIN(event_time) as first_seen,
  MAX(event_time) as last_seen,
  ARRAY_AGG(DISTINCT cloud) as clouds,
  ARRAY_AGG(DISTINCT user_identity) as identities
FROM cloud_events
WHERE event_time >= CURRENT_TIMESTAMP - INTERVAL 24 HOUR
GROUP BY source_ip
HAVING clouds_accessed > 1
ORDER BY clouds_accessed DESC, identities_used DESC;
\`\`\`

### 3. Cloud-Native Threat Intelligence

#### Automated IOC Enrichment
\`\`\`python
def enrich_cloud_iocs(ioc_list):
    enriched_data = {}
    
    for ioc in ioc_list:
        enrichment = {
            'aws_reputation': check_aws_guardduty_reputation(ioc),
            'azure_ti': check_azure_sentinel_ti(ioc),
            'gcp_reputation': check_gcp_security_center(ioc),
            'external_feeds': check_external_threat_feeds(ioc)
        }
        
        # Calculate composite risk score
        risk_score = calculate_composite_risk(enrichment)
        enriched_data[ioc] = {
            'enrichment': enrichment,
            'risk_score': risk_score,
            'hunting_priority': get_hunting_priority(risk_score)
        }
    
    return enriched_data
\`\`\`
      `,
      activities: [
        'Design multi-cloud logging architecture',
        'Build cross-cloud correlation queries',
        'Implement unified threat intelligence',
        'Create cloud attack chain detection'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'AWS Security Incident Investigation',
      description: 'Investigate a simulated AWS security incident using CloudTrail and VPC Flow Logs',
      tasks: [
        'Analyze suspicious IAM activities',
        'Track lateral movement through AWS services',
        'Identify data exfiltration attempts',
        'Build timeline of attacker activities'
      ]
    },
    {
      title: 'Azure Sentinel Hunting Lab',
      description: 'Build comprehensive hunting queries and workbooks in Azure Sentinel',
      tasks: [
        'Create KQL hunting queries',
        'Build custom hunting workbooks',
        'Develop automated detection rules',
        'Investigate Azure AD compromise scenarios'
      ]
    },
    {
      title: 'Multi-Cloud Threat Hunting Platform',
      description: 'Design and implement a unified threat hunting platform across all cloud providers',
      tasks: [
        'Architect multi-cloud log aggregation',
        'Build cross-cloud correlation engine',
        'Implement unified threat intelligence',
        'Create cloud-agnostic hunting playbooks'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Cloud Threat Hunting Certification',
      description: 'Demonstrate mastery of cloud threat hunting across AWS, Azure, and GCP'
    },
    {
      type: 'project',
      title: 'Enterprise Cloud Security Architecture',
      description: 'Design and implement a complete cloud security monitoring and hunting solution'
    }
  ],

  resources: [
    'AWS Security Best Practices',
    'Azure Sentinel Hunting Queries Repository',
    'GCP Security Command Center Documentation',
    'Cloud Security Alliance (CSA) Guidelines',
    'NIST Cloud Security Framework',
    'Multi-Cloud Security Architecture Patterns'
  ]
};
