/**
 * TH-27: Python for Threat Hunting
 * Master Python programming for automated threat hunting and analysis
 */

export const pythonHuntingContent = {
  id: 'th-27',
  title: 'Python for Threat Hunting',
  description: 'Master Python programming for automated threat hunting, data analysis, and custom tool development.',
  duration: '45 hours',
  difficulty: 'Intermediate to Advanced',
  
  learningObjectives: [
    'Develop Python scripts for automated threat hunting',
    'Build custom hunting tools and frameworks',
    'Perform large-scale data analysis with pandas and numpy',
    'Create threat intelligence processing pipelines',
    'Implement machine learning for anomaly detection',
    'Develop API integrations for hunting platforms',
    'Build automated reporting and visualization tools',
    'Create custom IOC extraction and analysis tools'
  ],

  sections: [
    {
      id: 'python-hunting-fundamentals',
      title: 'Python Fundamentals for Hunters',
      content: `
## Essential Python Libraries for Threat Hunting

### Core Libraries
\`\`\`python
import pandas as pd          # Data analysis and manipulation
import numpy as np           # Numerical computing
import matplotlib.pyplot as plt  # Data visualization
import seaborn as sns        # Statistical visualization
import requests             # HTTP requests for APIs
import json                 # JSON data handling
import re                   # Regular expressions
import datetime             # Date/time handling
import ipaddress           # IP address manipulation
import hashlib             # Hash calculations
\`\`\`

### Security-Specific Libraries
\`\`\`python
import yara                 # YARA rule engine
import pefile              # PE file analysis
import dnspython           # DNS analysis
import scapy               # Network packet analysis
import volatility3         # Memory analysis
import shodan              # Shodan API integration
import virustotal_python   # VirusTotal API
import misp_python_api     # MISP threat intelligence
\`\`\`

### Basic Hunting Script Structure
\`\`\`python
#!/usr/bin/env python3
"""
Threat Hunting Script Template
"""

import argparse
import logging
import sys
from datetime import datetime, timedelta

class ThreatHunter:
    def __init__(self, config_file=None):
        self.setup_logging()
        self.load_config(config_file)
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('threat_hunt.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def hunt(self, indicators):
        """Main hunting logic"""
        results = []
        for indicator in indicators:
            result = self.analyze_indicator(indicator)
            results.append(result)
        return results
        
    def analyze_indicator(self, indicator):
        """Analyze individual indicator"""
        # Implementation specific to indicator type
        pass
        
    def generate_report(self, results):
        """Generate hunting report"""
        # Report generation logic
        pass

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Threat Hunting Tool')
    parser.add_argument('--indicators', required=True, help='File containing indicators')
    parser.add_argument('--output', default='hunt_results.json', help='Output file')
    
    args = parser.parse_args()
    
    hunter = ThreatHunter()
    results = hunter.hunt(args.indicators)
    hunter.generate_report(results)
\`\`\`
      `,
      activities: [
        'Set up Python hunting environment',
        'Install essential security libraries',
        'Create basic hunting script template',
        'Practice with core Python concepts'
      ]
    },

    {
      id: 'data-analysis',
      title: 'Data Analysis for Threat Hunting',
      content: `
## Log Analysis with Pandas

### Processing Security Logs
\`\`\`python
import pandas as pd
import numpy as np
from datetime import datetime

# Load and parse security logs
def load_security_logs(file_path):
    df = pd.read_csv(file_path)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    return df

# Detect authentication anomalies
def detect_auth_anomalies(df):
    # Group by user and calculate statistics
    user_stats = df.groupby('username').agg({
        'source_ip': 'nunique',
        'login_time': 'count',
        'failed_attempts': 'sum'
    }).rename(columns={
        'source_ip': 'unique_ips',
        'login_time': 'total_logins'
    })
    
    # Calculate z-scores for anomaly detection
    user_stats['ip_zscore'] = np.abs(
        (user_stats['unique_ips'] - user_stats['unique_ips'].mean()) / 
        user_stats['unique_ips'].std()
    )
    
    # Flag anomalous users
    anomalous_users = user_stats[
        (user_stats['ip_zscore'] > 2) | 
        (user_stats['failed_attempts'] > 10)
    ]
    
    return anomalous_users

# Network traffic analysis
def analyze_network_traffic(df):
    # Detect beaconing behavior
    traffic_stats = df.groupby(['src_ip', 'dst_ip', 'dst_port']).agg({
        'bytes': ['count', 'mean', 'std'],
        'timestamp': lambda x: (x.max() - x.min()).total_seconds()
    }).round(2)
    
    # Flatten column names
    traffic_stats.columns = ['_'.join(col).strip() for col in traffic_stats.columns]
    
    # Calculate regularity score (lower = more regular/suspicious)
    traffic_stats['regularity_score'] = (
        traffic_stats['bytes_std'] / traffic_stats['bytes_mean']
    ).fillna(0)
    
    # Flag potential beaconing
    beaconing = traffic_stats[
        (traffic_stats['bytes_count'] > 10) &
        (traffic_stats['regularity_score'] < 0.1) &
        (traffic_stats['timestamp_<lambda>'] > 3600)  # Active for > 1 hour
    ]
    
    return beaconing
\`\`\`

### Statistical Analysis for Hunting
\`\`\`python
import scipy.stats as stats
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler

# Outlier detection using statistical methods
def detect_statistical_outliers(df, column, method='iqr'):
    if method == 'iqr':
        Q1 = df[column].quantile(0.25)
        Q3 = df[column].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = df[(df[column] < lower_bound) | (df[column] > upper_bound)]
    
    elif method == 'zscore':
        z_scores = np.abs(stats.zscore(df[column]))
        outliers = df[z_scores > 3]
    
    return outliers

# Behavioral clustering
def cluster_behavior(df, features):
    # Prepare data for clustering
    X = df[features].fillna(0)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Apply DBSCAN clustering
    clustering = DBSCAN(eps=0.5, min_samples=5)
    clusters = clustering.fit_predict(X_scaled)
    
    df['cluster'] = clusters
    
    # Analyze clusters
    cluster_analysis = df.groupby('cluster')[features].agg(['mean', 'std', 'count'])
    
    return df, cluster_analysis
\`\`\`
      `,
      activities: [
        'Analyze authentication logs for anomalies',
        'Detect network beaconing patterns',
        'Implement statistical outlier detection',
        'Perform behavioral clustering analysis'
      ]
    },

    {
      id: 'threat-intelligence',
      title: 'Threat Intelligence Automation',
      content: `
## IOC Processing and Enrichment

### VirusTotal Integration
\`\`\`python
import requests
import time
import json

class VirusTotalHunter:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://www.virustotal.com/vtapi/v2"
        
    def check_file_hash(self, hash_value):
        url = f"{self.base_url}/file/report"
        params = {
            'apikey': self.api_key,
            'resource': hash_value
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            return response.json()
        return None
    
    def check_ip_reputation(self, ip_address):
        url = f"{self.base_url}/ip-address/report"
        params = {
            'apikey': self.api_key,
            'ip': ip_address
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            return response.json()
        return None
    
    def bulk_hash_check(self, hash_list, delay=15):
        results = []
        for hash_value in hash_list:
            result = self.check_file_hash(hash_value)
            if result:
                results.append({
                    'hash': hash_value,
                    'positives': result.get('positives', 0),
                    'total': result.get('total', 0),
                    'scan_date': result.get('scan_date', ''),
                    'malicious': result.get('positives', 0) > 0
                })
            time.sleep(delay)  # Rate limiting
        return results

# MISP Integration
class MISPHunter:
    def __init__(self, misp_url, misp_key):
        from pymisp import PyMISP
        self.misp = PyMISP(misp_url, misp_key, ssl=False)
        
    def search_attributes(self, value, category=None):
        search_result = self.misp.search(
            controller='attributes',
            value=value,
            category=category,
            pythonify=True
        )
        return search_result
    
    def get_threat_actors(self, tag_filter=None):
        events = self.misp.search(
            controller='events',
            tags=tag_filter,
            pythonify=True
        )
        return events
    
    def create_hunting_event(self, indicators, threat_actor=None):
        event = self.misp.new_event(
            distribution=0,
            threat_level_id=2,
            analysis=1,
            info=f"Threat Hunting Campaign - {datetime.now().strftime('%Y-%m-%d')}"
        )
        
        for indicator in indicators:
            self.misp.add_attribute(
                event,
                type=indicator['type'],
                value=indicator['value'],
                category='Network activity'
            )
        
        return self.misp.add_event(event)
\`\`\`

### Custom IOC Extraction
\`\`\`python
import re
import ipaddress
from urllib.parse import urlparse

class IOCExtractor:
    def __init__(self):
        # Regex patterns for different IOC types
        self.patterns = {
            'ip': r'\\\\b(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}\\\\b',
            'domain': r'\\\\b[a-zA-Z0-9]([a-zA-Z0-9\\\\-]{0,61}[a-zA-Z0-9])?\\\\.[a-zA-Z]{2,}\\\\b',
            'url': r'https?://[^\\\\s<>"{}|\\\\\\\\^\\\\[\\\\]]+',
            'email': r'\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\.[A-Z|a-z]{2,}\\\\b',
            'md5': r'\\\\b[a-fA-F0-9]{32}\\\\b',
            'sha1': r'\\\\b[a-fA-F0-9]{40}\\\\b',
            'sha256': r'\\\\b[a-fA-F0-9]{64}\\\\b'
        }
    
    def extract_iocs(self, text):
        iocs = {}
        for ioc_type, pattern in self.patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                iocs[ioc_type] = list(set(matches))  # Remove duplicates
        return iocs
    
    def validate_ip(self, ip_string):
        try:
            ip = ipaddress.ip_address(ip_string)
            return {
                'valid': True,
                'type': 'ipv4' if ip.version == 4 else 'ipv6',
                'private': ip.is_private,
                'reserved': ip.is_reserved
            }
        except ValueError:
            return {'valid': False}
    
    def extract_from_file(self, file_path):
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        return self.extract_iocs(content)
\`\`\`
      `,
      activities: [
        'Build VirusTotal integration for hash checking',
        'Create MISP integration for threat intelligence',
        'Develop custom IOC extraction tools',
        'Build automated enrichment pipelines'
      ]
    },

    {
      id: 'automation-frameworks',
      title: 'Hunting Automation Frameworks',
      content: `
## Building Hunting Orchestration

### Hunting Workflow Engine
\`\`\`python
import asyncio
import aiohttp
from dataclasses import dataclass
from typing import List, Dict, Any
from enum import Enum

class HuntStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class HuntTask:
    id: str
    name: str
    indicators: List[str]
    hunt_type: str
    priority: int
    status: HuntStatus = HuntStatus.PENDING
    results: Dict[str, Any] = None

class HuntingOrchestrator:
    def __init__(self, max_concurrent=5):
        self.max_concurrent = max_concurrent
        self.active_hunts = {}
        self.hunt_queue = asyncio.Queue()
        self.results_store = {}
        
    async def submit_hunt(self, hunt_task: HuntTask):
        await self.hunt_queue.put(hunt_task)
        
    async def process_hunts(self):
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        while True:
            hunt_task = await self.hunt_queue.get()
            asyncio.create_task(self._execute_hunt(hunt_task, semaphore))
            
    async def _execute_hunt(self, hunt_task: HuntTask, semaphore):
        async with semaphore:
            hunt_task.status = HuntStatus.RUNNING
            self.active_hunts[hunt_task.id] = hunt_task
            
            try:
                # Execute hunting logic based on hunt type
                if hunt_task.hunt_type == "ioc_search":
                    results = await self._hunt_iocs(hunt_task.indicators)
                elif hunt_task.hunt_type == "behavioral_analysis":
                    results = await self._hunt_behavior(hunt_task.indicators)
                else:
                    results = {"error": "Unknown hunt type"}
                
                hunt_task.results = results
                hunt_task.status = HuntStatus.COMPLETED
                
            except Exception as e:
                hunt_task.status = HuntStatus.FAILED
                hunt_task.results = {"error": str(e)}
            
            finally:
                self.results_store[hunt_task.id] = hunt_task
                del self.active_hunts[hunt_task.id]
    
    async def _hunt_iocs(self, indicators):
        # IOC hunting implementation
        results = []
        async with aiohttp.ClientSession() as session:
            for indicator in indicators:
                # Search across multiple data sources
                result = await self._search_indicator(session, indicator)
                results.append(result)
        return results
    
    async def _search_indicator(self, session, indicator):
        # Implementation for searching indicator across data sources
        # This would integrate with SIEM, logs, threat intel, etc.
        pass

# Hunting Playbook System
class HuntingPlaybook:
    def __init__(self, name, description):
        self.name = name
        self.description = description
        self.steps = []
        
    def add_step(self, step_name, step_function, **kwargs):
        self.steps.append({
            'name': step_name,
            'function': step_function,
            'kwargs': kwargs
        })
    
    async def execute(self, context):
        results = {}
        for step in self.steps:
            step_result = await step['function'](context, **step['kwargs'])
            results[step['name']] = step_result
            context.update(step_result)  # Pass results to next step
        return results

# Example playbook for APT hunting
async def create_apt_hunting_playbook():
    playbook = HuntingPlaybook(
        "APT Hunting Campaign",
        "Comprehensive APT detection and analysis"
    )
    
    playbook.add_step("collect_indicators", collect_apt_indicators)
    playbook.add_step("enrich_indicators", enrich_with_threat_intel)
    playbook.add_step("search_logs", search_security_logs)
    playbook.add_step("analyze_behavior", analyze_behavioral_patterns)
    playbook.add_step("generate_report", generate_hunting_report)
    
    return playbook
\`\`\`

### Automated Reporting System
\`\`\`python
import matplotlib.pyplot as plt
import seaborn as sns
from jinja2 import Template
import pdfkit

class HuntingReporter:
    def __init__(self, template_dir="templates"):
        self.template_dir = template_dir
        
    def generate_executive_summary(self, hunt_results):
        summary = {
            'total_indicators': len(hunt_results.get('indicators', [])),
            'threats_found': len([r for r in hunt_results.get('results', []) if r.get('threat_detected')]),
            'high_priority': len([r for r in hunt_results.get('results', []) if r.get('priority') == 'high']),
            'hunt_duration': hunt_results.get('duration', 0),
            'data_sources': hunt_results.get('data_sources', [])
        }
        return summary
    
    def create_threat_timeline(self, events):
        plt.figure(figsize=(12, 6))
        df = pd.DataFrame(events)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Create timeline plot
        plt.scatter(df['timestamp'], df['severity'], 
                   c=df['threat_type'].astype('category').cat.codes, 
                   alpha=0.7, s=100)
        plt.xlabel('Time')
        plt.ylabel('Severity')
        plt.title('Threat Detection Timeline')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('threat_timeline.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_html_report(self, hunt_results):
        template_str = '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Threat Hunting Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { background-color: #f0f0f0; padding: 20px; }
                .summary { background-color: #e8f4fd; padding: 15px; margin: 20px 0; }
                .threat { background-color: #ffe6e6; padding: 10px; margin: 10px 0; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Threat Hunting Report</h1>
                <p>Generated: {{ report_date }}</p>
            </div>
            
            <div class="summary">
                <h2>Executive Summary</h2>
                <ul>
                    <li>Total Indicators Analyzed: {{ summary.total_indicators }}</li>
                    <li>Threats Detected: {{ summary.threats_found }}</li>
                    <li>High Priority Threats: {{ summary.high_priority }}</li>
                    <li>Hunt Duration: {{ summary.hunt_duration }} hours</li>
                </ul>
            </div>
            
            <h2>Detailed Findings</h2>
            {% for finding in findings %}
            <div class="threat">
                <h3>{{ finding.title }}</h3>
                <p><strong>Severity:</strong> {{ finding.severity }}</p>
                <p><strong>Description:</strong> {{ finding.description }}</p>
                <p><strong>Indicators:</strong> {{ finding.indicators|join(', ') }}</p>
                <p><strong>Recommendations:</strong> {{ finding.recommendations }}</p>
            </div>
            {% endfor %}
        </body>
        </html>
        '''
        
        template = Template(template_str)
        return template.render(
            report_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            summary=self.generate_executive_summary(hunt_results),
            findings=hunt_results.get('findings', [])
        )
\`\`\`
      `,
      activities: [
        'Build hunting workflow orchestration system',
        'Create automated playbook execution engine',
        'Develop comprehensive reporting framework',
        'Implement hunt result visualization tools'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Automated APT Hunting System',
      description: 'Build a complete automated APT hunting system using Python',
      tasks: [
        'Create IOC extraction and enrichment pipeline',
        'Build multi-source data correlation engine',
        'Implement behavioral analysis algorithms',
        'Develop automated reporting system',
        'Create hunting orchestration framework'
      ],
      timeEstimate: '12 hours',
      difficulty: 'Advanced'
    },
    {
      title: 'Machine Learning Anomaly Detection',
      description: 'Implement ML-based anomaly detection for threat hunting',
      tasks: [
        'Collect and prepare training data',
        'Build unsupervised anomaly detection models',
        'Create real-time scoring pipeline',
        'Develop model evaluation metrics',
        'Implement automated model retraining'
      ],
      timeEstimate: '10 hours',
      difficulty: 'Expert'
    }
  ],

  assessments: [
    {
      type: 'project',
      title: 'Python Hunting Framework Development',
      description: 'Develop a comprehensive Python-based threat hunting framework',
      requirements: [
        'Multi-source data ingestion capabilities',
        'Automated IOC processing and enrichment',
        'Behavioral analysis and anomaly detection',
        'Hunting workflow orchestration',
        'Comprehensive reporting and visualization',
        'API integrations with security tools',
        'Documentation and testing'
      ]
    }
  ],

  tools: [
    'Python 3.8+',
    'Pandas, NumPy, Matplotlib',
    'Scikit-learn, TensorFlow',
    'Requests, AsyncIO',
    'YARA-Python',
    'PyMISP',
    'Jupyter Notebooks',
    'Docker for deployment'
  ]
};
