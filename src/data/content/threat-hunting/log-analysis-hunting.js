export const logAnalysisHuntingContent = {
  title: 'Log Analysis for Threat Hunting',
  description: 'Advanced techniques for analyzing logs to detect and investigate security threats.',
  
  // Core concepts and learning objectives
  concepts: [
    'Log collection and normalization',
    'Pattern recognition in logs',
    'Correlation analysis',
    'Timeline analysis',
    'Anomaly detection in logs'
  ],

  // Practical labs with clear objectives
  labs: [
    {
      title: 'SIEM Log Analysis Lab',
      description: 'Learn to analyze and correlate logs in a SIEM environment',
      difficulty: 'Advanced',
      duration: '2 hours',
      objectives: [
        'Configure log sources and parsers',
        'Create correlation rules',
        'Investigate security incidents using logs',
        'Generate and analyze reports'
      ],
      tools: ['Splunk', 'ELK Stack', 'QRadar'],
      prerequisites: ['Basic SIEM knowledge', 'Understanding of log formats']
    },
    {
      title: 'Windows Event Log Analysis',
      description: 'Deep dive into Windows event logs for threat detection',
      difficulty: 'Intermediate',
      duration: '1.5 hours',
      objectives: [
        'Analyze Security event logs',
        'Investigate authentication events',
        'Track process creation and execution',
        'Monitor network connections'
      ],
      tools: ['Windows Event Viewer', 'PowerShell', 'Log Parser'],
      prerequisites: ['Windows security concepts', 'Basic PowerShell knowledge']
    }
  ],

  // Real-world use cases with detailed scenarios
  useCases: [
    {
      title: 'Detecting Lateral Movement',
      description: 'Identify unauthorized lateral movement through log analysis',
      scenario: 'Investigate suspicious authentication attempts and network connections across systems',
      mitreTactics: ['Lateral Movement', 'Discovery'],
      tools: ['SIEM', 'Network Logs', 'Authentication Logs'],
      steps: [
        'Collect and normalize relevant logs',
        'Identify unusual authentication patterns',
        'Correlate with network connection logs',
        'Investigate suspicious activities'
      ]
    },
    {
      title: 'Data Exfiltration Detection',
      description: 'Detect and investigate data exfiltration attempts',
      scenario: 'Analyze network and system logs for unusual data transfer patterns',
      mitreTactics: ['Exfiltration'],
      tools: ['Network Monitoring Tools', 'DLP Solutions', 'SIEM'],
      steps: [
        'Monitor outbound network traffic',
        'Analyze file transfer logs',
        'Investigate unusual data transfer patterns',
        'Correlate with user activity logs'
      ]
    }
  ],

  // MITRE ATT&CK mapping
  mitreMapping: [
    {
      tactic: 'Discovery',
      techniques: [
        {
          name: 'Network Service Discovery',
          description: 'Analyze logs for network scanning activities',
          detection: 'Look for port scanning patterns in network logs'
        },
        {
          name: 'System Information Discovery',
          description: 'Monitor for system information gathering',
          detection: 'Track unusual system information queries'
        }
      ]
    },
    {
      tactic: 'Lateral Movement',
      techniques: [
        {
          name: 'Remote Services',
          description: 'Monitor remote service access',
          detection: 'Analyze authentication and connection logs'
        },
        {
          name: 'Internal Spearphishing',
          description: 'Track internal email-based attacks',
          detection: 'Monitor email server logs for suspicious patterns'
        }
      ]
    }
  ],

  // Required tools and technologies
  tools: [
    {
      name: 'SIEM Solutions',
      description: 'Security Information and Event Management',
      useCases: ['Log aggregation', 'Correlation analysis', 'Alert generation'],
      examples: ['Splunk', 'QRadar', 'ELK Stack']
    },
    {
      name: 'Log Analysis Tools',
      description: 'Specialized tools for log analysis',
      useCases: ['Pattern matching', 'Timeline analysis', 'Anomaly detection'],
      examples: ['Log Parser', 'Graylog', 'LogRhythm']
    }
  ],

  // Prerequisites and dependencies
  prerequisites: [
    'Understanding of common log formats',
    'Basic knowledge of SIEM concepts',
    'Familiarity with network protocols',
    'Understanding of security events and alerts'
  ],

  // Additional resources
  resources: [
    {
      type: 'Documentation',
      title: 'SIEM Best Practices',
      url: 'https://example.com/siem-best-practices'
    },
    {
      type: 'Cheat Sheet',
      title: 'Common Log Analysis Commands',
      url: 'https://example.com/log-analysis-cheatsheet'
    }
  ]
}; 