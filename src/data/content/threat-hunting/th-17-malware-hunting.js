/**
 * TH-17: Malware Hunting
 * Master malware detection, analysis, and family classification techniques
 */

export const malwareHuntingContent = {
  id: 'th-17',
  title: 'Malware Hunting: Detection & Analysis',
  description: 'Master comprehensive malware hunting techniques including behavioral analysis, family classification, and advanced evasion detection.',
  duration: '44 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Master YARA rule development for malware detection',
    'Implement behavioral analysis for unknown malware',
    'Build malware family classification systems',
    'Hunt for fileless and living-off-the-land attacks',
    'Develop automated malware analysis pipelines',
    'Create custom malware detection signatures',
    'Integrate malware intelligence with hunting platforms'
  ],

  sections: [
    {
      id: 'yara-mastery',
      title: 'YARA Rules for Malware Hunting',
      content: `
## Advanced YARA Rule Development

### **YARA Rule Structure and Optimization**
\`\`\`yara
rule Advanced_Ransomware_Detection {
    meta:
        author = "Threat Hunter"
        description = "Detects advanced ransomware families"
        date = "2024-01-15"
        version = "1.0"
        tlp = "white"
        confidence = "high"
        
    strings:
        // Encryption-related strings
        $crypto1 = "CryptGenRandom" ascii
        $crypto2 = "CryptAcquireContext" ascii
        $crypto3 = "CryptCreateHash" ascii
        
        // Ransomware behavior indicators
        $ransom1 = "Your files have been encrypted" ascii nocase
        $ransom2 = "pay the ransom" ascii nocase
        $ransom3 = "bitcoin" ascii nocase
        
        // File extension patterns
        $ext1 = ".locked" ascii
        $ext2 = ".encrypted" ascii
        $ext3 = ".crypto" ascii
        
        // Registry modifications
        $reg1 = "HKEY_CURRENT_USER\\\\Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Run" ascii
        $reg2 = "DisableTaskMgr" ascii
        
        // Network communication
        $net1 = "tor2web" ascii
        $net2 = ".onion" ascii
        
        // Anti-analysis techniques
        $anti1 = "IsDebuggerPresent" ascii
        $anti2 = "CheckRemoteDebuggerPresent" ascii
        $anti3 = "VirtualAlloc" ascii
        
    condition:
        uint16(0) == 0x5A4D and  // PE header
        filesize < 10MB and
        (
            (2 of ($crypto*) and 1 of ($ransom*)) or
            (3 of ($crypto*) and 1 of ($ext*)) or
            (1 of ($crypto*) and 1 of ($reg*) and 1 of ($net*))
        ) and
        1 of ($anti*)
}

rule APT_Backdoor_Generic {
    meta:
        description = "Generic APT backdoor detection"
        author = "Threat Hunter"
        
    strings:
        // Command execution
        $cmd1 = "cmd.exe /c" ascii
        $cmd2 = "powershell.exe -exec bypass" ascii
        $cmd3 = "wmic process call create" ascii
        
        // Network communication
        $net1 = "User-Agent:" ascii
        $net2 = "POST" ascii
        $net3 = "GET" ascii
        
        // Persistence mechanisms
        $persist1 = "schtasks /create" ascii
        $persist2 = "sc create" ascii
        $persist3 = "reg add" ascii
        
        // Data exfiltration
        $exfil1 = "WinHttpOpen" ascii
        $exfil2 = "InternetOpen" ascii
        $exfil3 = "send" ascii
        
    condition:
        uint16(0) == 0x5A4D and
        2 of ($cmd*) and
        2 of ($net*) and
        1 of ($persist*) and
        1 of ($exfil*)
}

rule Fileless_Malware_Indicators {
    meta:
        description = "Detects fileless malware techniques"
        
    strings:
        // PowerShell obfuscation
        $ps1 = "powershell" ascii nocase
        $ps2 = "-encodedcommand" ascii nocase
        $ps3 = "-enc" ascii nocase
        $ps4 = "invoke-expression" ascii nocase
        $ps5 = "iex" ascii nocase
        
        // WMI abuse
        $wmi1 = "winmgmts:" ascii
        $wmi2 = "Win32_Process" ascii
        $wmi3 = "Create" ascii
        
        // Registry abuse
        $reg1 = "HKLM\\\\SOFTWARE\\\\Classes" ascii
        $reg2 = "InprocServer32" ascii
        
        // Living off the land
        $lol1 = "regsvr32" ascii nocase
        $lol2 = "rundll32" ascii nocase
        $lol3 = "mshta" ascii nocase
        $lol4 = "certutil" ascii nocase
        
    condition:
        (2 of ($ps*) or 2 of ($wmi*) or 2 of ($reg*)) and
        1 of ($lol*)
}
\`\`\`

### **Automated YARA Scanning Framework**
\`\`\`python
import yara
import os
import hashlib
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

class MalwareHunter:
    def __init__(self, rules_directory):
        self.rules_directory = rules_directory
        self.compiled_rules = self._compile_rules()
        self.scan_results = []
        
    def _compile_rules(self):
        """Compile all YARA rules from directory"""
        rule_files = {}
        
        for root, dirs, files in os.walk(self.rules_directory):
            for file in files:
                if file.endswith('.yar') or file.endswith('.yara'):
                    rule_path = os.path.join(root, file)
                    rule_name = os.path.splitext(file)[0]
                    rule_files[rule_name] = rule_path
        
        try:
            return yara.compile(filepaths=rule_files)
        except yara.Error as e:
            print(f"YARA compilation error: {e}")
            return None
    
    def scan_file(self, file_path):
        """Scan single file with YARA rules"""
        if not self.compiled_rules:
            return None
            
        try:
            matches = self.compiled_rules.match(file_path)
            
            if matches:
                file_hash = self._calculate_file_hash(file_path)
                
                scan_result = {
                    'file_path': file_path,
                    'file_hash': file_hash,
                    'file_size': os.path.getsize(file_path),
                    'scan_timestamp': datetime.now().isoformat(),
                    'matches': []
                }
                
                for match in matches:
                    match_info = {
                        'rule_name': match.rule,
                        'namespace': match.namespace,
                        'tags': match.tags,
                        'meta': match.meta,
                        'strings': []
                    }
                    
                    for string_match in match.strings:
                        match_info['strings'].append({
                            'identifier': string_match.identifier,
                            'instances': [
                                {
                                    'offset': instance.offset,
                                    'matched_data': instance.matched_data.hex(),
                                    'matched_length': instance.matched_length
                                }
                                for instance in string_match.instances
                            ]
                        })
                    
                    scan_result['matches'].append(match_info)
                
                return scan_result
                
        except Exception as e:
            return {
                'file_path': file_path,
                'error': str(e),
                'scan_timestamp': datetime.now().isoformat()
            }
        
        return None
    
    def scan_directory(self, directory_path, max_workers=4):
        """Scan directory recursively with threading"""
        file_paths = []
        
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                file_path = os.path.join(root, file)
                # Skip very large files (>100MB)
                try:
                    if os.path.getsize(file_path) < 100 * 1024 * 1024:
                        file_paths.append(file_path)
                except OSError:
                    continue
        
        results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(self.scan_file, file_path): file_path 
                for file_path in file_paths
            }
            
            for future in as_completed(future_to_file):
                result = future.result()
                if result:
                    results.append(result)
                    print(f"Scanned: {future_to_file[future]}")
        
        return results
    
    def _calculate_file_hash(self, file_path):
        """Calculate SHA256 hash of file"""
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception:
            return None
    
    def generate_hunting_report(self, scan_results):
        """Generate comprehensive hunting report"""
        report = {
            'scan_summary': {
                'total_files_scanned': len(scan_results),
                'malware_detected': len([r for r in scan_results if 'matches' in r and r['matches']]),
                'scan_timestamp': datetime.now().isoformat()
            },
            'detections': [],
            'rule_statistics': {},
            'file_type_analysis': {}
        }
        
        # Process detections
        for result in scan_results:
            if 'matches' in result and result['matches']:
                report['detections'].append(result)
                
                # Count rule matches
                for match in result['matches']:
                    rule_name = match['rule_name']
                    if rule_name not in report['rule_statistics']:
                        report['rule_statistics'][rule_name] = 0
                    report['rule_statistics'][rule_name] += 1
        
        # Sort rules by frequency
        report['rule_statistics'] = dict(
            sorted(report['rule_statistics'].items(), 
                   key=lambda x: x[1], reverse=True)
        )
        
        return report

### **Behavioral Analysis Engine**
\`\`\`python
import psutil
import time
import json
from collections import defaultdict

class BehavioralAnalyzer:
    def __init__(self):
        self.baseline_behavior = {}
        self.suspicious_behaviors = []
        self.monitoring_duration = 300  # 5 minutes
        
    def establish_baseline(self, duration=3600):
        """Establish baseline system behavior"""
        print(f"Establishing baseline for {duration} seconds...")
        
        baseline_data = {
            'process_creation_rate': [],
            'network_connections': [],
            'file_modifications': [],
            'registry_changes': [],
            'cpu_usage_patterns': [],
            'memory_usage_patterns': []
        }
        
        start_time = time.time()
        while time.time() - start_time < duration:
            # Collect system metrics
            baseline_data['cpu_usage_patterns'].append(psutil.cpu_percent())
            baseline_data['memory_usage_patterns'].append(psutil.virtual_memory().percent)
            
            # Monitor process creation (simplified)
            current_processes = set(p.pid for p in psutil.process_iter())
            baseline_data['process_creation_rate'].append(len(current_processes))
            
            time.sleep(60)  # Sample every minute
        
        # Calculate baseline statistics
        self.baseline_behavior = {
            'avg_cpu_usage': sum(baseline_data['cpu_usage_patterns']) / len(baseline_data['cpu_usage_patterns']),
            'avg_memory_usage': sum(baseline_data['memory_usage_patterns']) / len(baseline_data['memory_usage_patterns']),
            'avg_process_count': sum(baseline_data['process_creation_rate']) / len(baseline_data['process_creation_rate']),
            'baseline_timestamp': datetime.now().isoformat()
        }
        
        print("Baseline established successfully")
        return self.baseline_behavior
    
    def monitor_for_anomalies(self):
        """Monitor system for behavioral anomalies"""
        print(f"Monitoring for anomalies for {self.monitoring_duration} seconds...")
        
        anomalies = []
        start_time = time.time()
        
        while time.time() - start_time < self.monitoring_duration:
            current_metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu_usage': psutil.cpu_percent(),
                'memory_usage': psutil.virtual_memory().percent,
                'process_count': len(list(psutil.process_iter())),
                'network_connections': len(psutil.net_connections())
            }
            
            # Check for anomalies
            anomaly = self._detect_anomaly(current_metrics)
            if anomaly:
                anomalies.append(anomaly)
                print(f"Anomaly detected: {anomaly['type']}")
            
            time.sleep(10)  # Check every 10 seconds
        
        return anomalies
    
    def _detect_anomaly(self, current_metrics):
        """Detect anomalies based on baseline"""
        if not self.baseline_behavior:
            return None
        
        anomaly_threshold = 2.0  # 2x baseline
        
        # CPU usage anomaly
        if current_metrics['cpu_usage'] > self.baseline_behavior['avg_cpu_usage'] * anomaly_threshold:
            return {
                'type': 'High CPU Usage',
                'current_value': current_metrics['cpu_usage'],
                'baseline_value': self.baseline_behavior['avg_cpu_usage'],
                'timestamp': current_metrics['timestamp'],
                'severity': 'medium'
            }
        
        # Memory usage anomaly
        if current_metrics['memory_usage'] > self.baseline_behavior['avg_memory_usage'] * anomaly_threshold:
            return {
                'type': 'High Memory Usage',
                'current_value': current_metrics['memory_usage'],
                'baseline_value': self.baseline_behavior['avg_memory_usage'],
                'timestamp': current_metrics['timestamp'],
                'severity': 'medium'
            }
        
        # Process creation anomaly
        if current_metrics['process_count'] > self.baseline_behavior['avg_process_count'] * anomaly_threshold:
            return {
                'type': 'Unusual Process Activity',
                'current_value': current_metrics['process_count'],
                'baseline_value': self.baseline_behavior['avg_process_count'],
                'timestamp': current_metrics['timestamp'],
                'severity': 'high'
            }
        
        return None
\`\`\`
      `,
      activities: [
        'Develop advanced YARA rules for malware families',
        'Build automated malware scanning framework',
        'Implement behavioral analysis for unknown threats',
        'Create malware classification system'
      ]
    },

    {
      id: 'malware-families',
      title: 'Malware Family Classification',
      content: `
## Advanced Malware Family Analysis

### **Machine Learning for Malware Classification**
\`\`\`python
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import pickle

class MalwareFamilyClassifier:
    def __init__(self):
        self.feature_extractor = TfidfVectorizer(max_features=10000, ngram_range=(1, 3))
        self.classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        self.family_labels = {}
        
    def extract_features(self, file_path):
        """Extract features from malware sample"""
        features = {
            'file_size': 0,
            'entropy': 0,
            'pe_characteristics': [],
            'imported_functions': [],
            'strings': [],
            'opcodes': []
        }
        
        try:
            # File size
            features['file_size'] = os.path.getsize(file_path)
            
            # Calculate entropy
            with open(file_path, 'rb') as f:
                data = f.read()
                features['entropy'] = self._calculate_entropy(data)
            
            # PE analysis (simplified)
            if self._is_pe_file(file_path):
                features['pe_characteristics'] = self._extract_pe_features(file_path)
            
            # String extraction
            features['strings'] = self._extract_strings(file_path)
            
        except Exception as e:
            print(f"Feature extraction error for {file_path}: {e}")
        
        return features
    
    def _calculate_entropy(self, data):
        """Calculate Shannon entropy of data"""
        if not data:
            return 0
        
        # Count byte frequencies
        byte_counts = [0] * 256
        for byte in data:
            byte_counts[byte] += 1
        
        # Calculate entropy
        entropy = 0
        data_len = len(data)
        for count in byte_counts:
            if count > 0:
                probability = count / data_len
                entropy -= probability * np.log2(probability)
        
        return entropy
    
    def _extract_strings(self, file_path, min_length=4):
        """Extract printable strings from file"""
        strings = []
        
        try:
            with open(file_path, 'rb') as f:
                data = f.read()
                current_string = ""
                
                for byte in data:
                    if 32 <= byte <= 126:  # Printable ASCII
                        current_string += chr(byte)
                    else:
                        if len(current_string) >= min_length:
                            strings.append(current_string)
                        current_string = ""
                
                # Don't forget the last string
                if len(current_string) >= min_length:
                    strings.append(current_string)
                    
        except Exception:
            pass
        
        return strings[:1000]  # Limit to first 1000 strings
    
    def train_classifier(self, training_data):
        """Train malware family classifier"""
        features_list = []
        labels = []
        
        for sample in training_data:
            # Combine all textual features
            text_features = " ".join(
                sample['features']['strings'] + 
                sample['features']['imported_functions'] +
                [str(sample['features']['file_size']), 
                 str(sample['features']['entropy'])]
            )
            
            features_list.append(text_features)
            labels.append(sample['family'])
        
        # Convert text features to numerical
        X = self.feature_extractor.fit_transform(features_list)
        y = np.array(labels)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Train classifier
        self.classifier.fit(X_train, y_train)
        
        # Evaluate
        y_pred = self.classifier.predict(X_test)
        print("Classification Report:")
        print(classification_report(y_test, y_pred))
        
        return {
            'accuracy': self.classifier.score(X_test, y_test),
            'classification_report': classification_report(y_test, y_pred, output_dict=True)
        }
    
    def classify_sample(self, file_path):
        """Classify malware sample"""
        features = self.extract_features(file_path)
        
        # Prepare features for classification
        text_features = " ".join(
            features['strings'] + 
            features['imported_functions'] +
            [str(features['file_size']), 
             str(features['entropy'])]
        )
        
        # Transform and predict
        X = self.feature_extractor.transform([text_features])
        prediction = self.classifier.predict(X)[0]
        confidence = max(self.classifier.predict_proba(X)[0])
        
        return {
            'predicted_family': prediction,
            'confidence': confidence,
            'features_summary': {
                'file_size': features['file_size'],
                'entropy': features['entropy'],
                'string_count': len(features['strings'])
            }
        }
    
    def save_model(self, model_path):
        """Save trained model"""
        model_data = {
            'feature_extractor': self.feature_extractor,
            'classifier': self.classifier,
            'family_labels': self.family_labels
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
    
    def load_model(self, model_path):
        """Load trained model"""
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        self.feature_extractor = model_data['feature_extractor']
        self.classifier = model_data['classifier']
        self.family_labels = model_data['family_labels']

### **Malware Campaign Tracking**
\`\`\`python
class MalwareCampaignTracker:
    def __init__(self):
        self.campaigns = {}
        self.ioc_database = {}
        
    def track_campaign(self, campaign_name, initial_iocs):
        """Start tracking a malware campaign"""
        self.campaigns[campaign_name] = {
            'start_date': datetime.now().isoformat(),
            'iocs': set(initial_iocs),
            'samples': [],
            'victims': [],
            'infrastructure': {
                'domains': set(),
                'ips': set(),
                'urls': set()
            },
            'ttps': [],
            'attribution': {
                'threat_actor': None,
                'confidence': 0
            }
        }
    
    def add_sample_to_campaign(self, campaign_name, sample_info):
        """Add malware sample to campaign tracking"""
        if campaign_name not in self.campaigns:
            return False
        
        sample_data = {
            'hash': sample_info['hash'],
            'file_path': sample_info['file_path'],
            'discovery_date': datetime.now().isoformat(),
            'family': sample_info.get('family'),
            'yara_matches': sample_info.get('yara_matches', []),
            'network_iocs': sample_info.get('network_iocs', []),
            'behavioral_indicators': sample_info.get('behavioral_indicators', [])
        }
        
        self.campaigns[campaign_name]['samples'].append(sample_data)
        
        # Extract and add new IOCs
        new_iocs = self._extract_iocs_from_sample(sample_data)
        self.campaigns[campaign_name]['iocs'].update(new_iocs)
        
        return True
    
    def _extract_iocs_from_sample(self, sample_data):
        """Extract IOCs from malware sample"""
        iocs = set()
        
        # Add network IOCs
        for ioc in sample_data.get('network_iocs', []):
            iocs.add(ioc)
            
            # Categorize IOC
            if self._is_ip_address(ioc):
                self.campaigns[campaign_name]['infrastructure']['ips'].add(ioc)
            elif self._is_domain(ioc):
                self.campaigns[campaign_name]['infrastructure']['domains'].add(ioc)
            elif ioc.startswith('http'):
                self.campaigns[campaign_name]['infrastructure']['urls'].add(ioc)
        
        return iocs
    
    def correlate_campaigns(self):
        """Find correlations between different campaigns"""
        correlations = []
        
        campaign_names = list(self.campaigns.keys())
        
        for i in range(len(campaign_names)):
            for j in range(i + 1, len(campaign_names)):
                campaign1 = campaign_names[i]
                campaign2 = campaign_names[j]
                
                correlation = self._calculate_campaign_correlation(campaign1, campaign2)
                
                if correlation['score'] > 0.3:  # Threshold for significant correlation
                    correlations.append(correlation)
        
        return correlations
    
    def _calculate_campaign_correlation(self, campaign1, campaign2):
        """Calculate correlation score between two campaigns"""
        c1 = self.campaigns[campaign1]
        c2 = self.campaigns[campaign2]
        
        # IOC overlap
        ioc_overlap = len(c1['iocs'].intersection(c2['iocs']))
        total_iocs = len(c1['iocs'].union(c2['iocs']))
        ioc_score = ioc_overlap / total_iocs if total_iocs > 0 else 0
        
        # Infrastructure overlap
        ip_overlap = len(c1['infrastructure']['ips'].intersection(c2['infrastructure']['ips']))
        domain_overlap = len(c1['infrastructure']['domains'].intersection(c2['infrastructure']['domains']))
        
        total_infrastructure = len(
            c1['infrastructure']['ips'].union(c2['infrastructure']['ips']) |
            c1['infrastructure']['domains'].union(c2['infrastructure']['domains'])
        )
        
        infra_score = (ip_overlap + domain_overlap) / total_infrastructure if total_infrastructure > 0 else 0
        
        # Overall correlation score
        correlation_score = (ioc_score * 0.6) + (infra_score * 0.4)
        
        return {
            'campaign1': campaign1,
            'campaign2': campaign2,
            'score': correlation_score,
            'shared_iocs': ioc_overlap,
            'shared_infrastructure': ip_overlap + domain_overlap,
            'correlation_details': {
                'ioc_score': ioc_score,
                'infrastructure_score': infra_score
            }
        }
\`\`\`
      `,
      activities: [
        'Build machine learning malware classifier',
        'Implement malware campaign tracking system',
        'Create family attribution framework',
        'Develop correlation analysis tools'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Advanced Malware Analysis Laboratory',
      description: 'Analyze sophisticated malware samples using multiple techniques',
      tasks: [
        'Develop custom YARA rules for new malware family',
        'Build behavioral analysis profiles',
        'Create machine learning classification model',
        'Track malware campaign evolution'
      ]
    },
    {
      title: 'Fileless Malware Detection Challenge',
      description: 'Detect and analyze fileless and living-off-the-land attacks',
      tasks: [
        'Hunt for PowerShell-based attacks',
        'Detect WMI abuse and registry persistence',
        'Analyze memory-only malware',
        'Build detection rules for fileless techniques'
      ]
    },
    {
      title: 'Enterprise Malware Hunting Platform',
      description: 'Build comprehensive malware hunting platform for enterprise',
      tasks: [
        'Design scalable malware analysis architecture',
        'Implement automated analysis pipelines',
        'Create threat intelligence integration',
        'Build executive reporting dashboards'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Malware Hunting Mastery Assessment',
      description: 'Demonstrate advanced malware hunting and analysis capabilities'
    },
    {
      type: 'project',
      title: 'Custom Malware Detection System',
      description: 'Build complete malware detection and classification system'
    }
  ],

  resources: [
    'YARA Rule Writing Best Practices',
    'Malware Analysis Techniques and Tools',
    'Machine Learning for Cybersecurity',
    'Behavioral Analysis Frameworks',
    'Threat Intelligence Integration Guides',
    'Malware Family Classification Research'
  ]
};
