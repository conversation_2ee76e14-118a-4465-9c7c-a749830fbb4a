/**
 * TH-22: OT/ICS Threat Hunting
 * Master threat hunting in Operational Technology and Industrial Control Systems
 */

export const otHuntingContent = {
  id: 'th-22',
  title: 'OT/ICS Threat Hunting: Industrial Security',
  description: 'Master comprehensive threat hunting in Operational Technology (OT) and Industrial Control Systems (ICS) environments.',
  duration: '40 hours',
  difficulty: 'Expert',
  
  objectives: [
    'Understand OT/ICS threat landscape and attack vectors',
    'Master industrial protocol analysis and monitoring',
    'Implement OT network segmentation and monitoring',
    'Hunt for advanced persistent threats in industrial environments',
    'Secure SCADA, PLC, and HMI systems',
    'Develop OT-specific hunting methodologies and playbooks',
    'Build comprehensive industrial cybersecurity programs'
  ],

  sections: [
    {
      id: 'ot-fundamentals',
      title: 'OT/ICS Security Fundamentals',
      content: `
## Industrial Control Systems Threat Landscape

### OT/ICS Architecture Components
- **SCADA (Supervisory Control and Data Acquisition)**
  - Central monitoring and control systems
  - Human-Machine Interfaces (HMI)
  - Data historians and trending systems
  - Communication networks and protocols

- **PLC (Programmable Logic Controllers)**
  - Field-level control devices
  - Input/Output modules
  - Programming and configuration tools
  - Real-time control logic execution

- **DCS (Distributed Control Systems)**
  - Process control and automation
  - Operator workstations
  - Engineering workstations
  - Control networks and field devices

- **Safety Instrumented Systems (SIS)**
  - Emergency shutdown systems
  - Fire and gas detection systems
  - Safety PLCs and logic solvers
  - Critical safety functions

### Common OT Attack Vectors
\`\`\`yaml
Network-Based Attacks:
  - Protocol exploitation (Modbus, DNP3, EtherNet/IP)
  - Network reconnaissance and scanning
  - Man-in-the-middle attacks
  - Network segmentation bypass

Device-Level Attacks:
  - PLC programming and logic modification
  - Firmware manipulation and backdoors
  - Configuration file tampering
  - Physical device access

Application Attacks:
  - HMI and SCADA software vulnerabilities
  - Engineering workstation compromise
  - Database and historian attacks
  - Remote access tool abuse

Supply Chain Attacks:
  - Compromised vendor software
  - Malicious hardware components
  - Third-party service provider risks
  - Update and patch manipulation
\`\`\`

### Industrial Communication Protocols
\`\`\`
Serial Protocols:
  - Modbus RTU/ASCII
  - DNP3 (Distributed Network Protocol)
  - IEC 61850 (Power systems)
  - Hart Protocol

Ethernet-Based Protocols:
  - Modbus TCP
  - EtherNet/IP (Common Industrial Protocol)
  - Profinet (Process Field Network)
  - OPC UA (Open Platform Communications)
  - BACnet (Building Automation)

Wireless Protocols:
  - WirelessHART
  - ISA100.11a
  - Zigbee Pro
  - WiFi and cellular for remote monitoring
\`\`\`
      `,
      activities: [
        'Map OT network architecture and assets',
        'Identify industrial protocols in use',
        'Assess OT security posture and risks',
        'Set up OT monitoring infrastructure'
      ]
    },

    {
      id: 'ot-protocol-analysis',
      title: 'Industrial Protocol Analysis',
      content: `
## OT Protocol Hunting Techniques

### 1. Modbus Protocol Analysis

#### Modbus Traffic Monitoring
\`\`\`python
from scapy.all import *
from scapy.contrib.modbus import *

class ModbusHunter:
    def __init__(self):
        self.baseline_registers = {}
        self.suspicious_activities = []
        
    def analyze_modbus_traffic(self, pcap_file):
        packets = rdpcap(pcap_file)
        modbus_packets = [pkt for pkt in packets if ModbusPDU in pkt]
        
        for pkt in modbus_packets:
            self.analyze_modbus_packet(pkt)
        
        return self.suspicious_activities
    
    def analyze_modbus_packet(self, packet):
        if packet[ModbusPDU].funcCode == 0x06:  # Write Single Register
            self.detect_unauthorized_writes(packet)
        elif packet[ModbusPDU].funcCode == 0x10:  # Write Multiple Registers
            self.detect_bulk_modifications(packet)
        elif packet[ModbusPDU].funcCode == 0x03:  # Read Holding Registers
            self.detect_reconnaissance(packet)
    
    def detect_unauthorized_writes(self, packet):
        src_ip = packet[IP].src
        register_addr = packet[ModbusPDU].registerAddr
        
        # Check if source is authorized for writes
        if not self.is_authorized_writer(src_ip):
            self.log_suspicious_activity(
                "unauthorized_modbus_write",
                src_ip,
                f"Register {register_addr}"
            )
    
    def detect_reconnaissance(self, packet):
        src_ip = packet[IP].src
        
        # Track read patterns for reconnaissance detection
        if src_ip not in self.read_patterns:
            self.read_patterns[src_ip] = []
        
        self.read_patterns[src_ip].append(packet[ModbusPDU].registerAddr)
        
        # Detect systematic scanning
        if len(self.read_patterns[src_ip]) > 100:
            self.log_suspicious_activity(
                "modbus_reconnaissance",
                src_ip,
                "Systematic register scanning detected"
            )

# Zeek script for Modbus monitoring
zeek_modbus_script = '''
@load base/protocols/conn

module ModbusHunter;

export {
    redef enum Log::ID += { LOG };
    
    type Info: record {
        ts: time &log;
        uid: string &log;
        id: conn_id &log;
        function_code: count &log;
        register_addr: count &optional &log;
        register_value: count &optional &log;
        is_suspicious: bool &log &default=F;
    };
}

event modbus_message(c: connection, headers: modbus_headers, is_orig: bool) {
    local info: ModbusHunter::Info;
    info$ts = network_time();
    info$uid = c$uid;
    info$id = c$id;
    info$function_code = headers$function_code;
    
    # Detect suspicious function codes
    if (headers$function_code in {0x06, 0x10, 0x16}) {
        info$is_suspicious = T;
    }
    
    Log::write(ModbusHunter::LOG, info);
}
'''
\`\`\`

### 2. DNP3 Protocol Hunting

#### DNP3 Anomaly Detection
\`\`\`python
import struct

class DNP3Hunter:
    def __init__(self):
        self.normal_patterns = {}
        self.attack_signatures = {
            'unauthorized_control': [0x05, 0x06],  # Select/Operate
            'data_manipulation': [0x02, 0x04],     # Write/Read
            'time_sync_attack': [0x17],            # Time sync
        }
    
    def analyze_dnp3_packet(self, packet_data):
        # Parse DNP3 header
        start_bytes = packet_data[:2]
        if start_bytes != b'\x05\x64':  # DNP3 start bytes
            return None
        
        length = struct.unpack('<H', packet_data[2:4])[0]
        control = packet_data[4]
        dest_addr = struct.unpack('<H', packet_data[5:7])[0]
        src_addr = struct.unpack('<H', packet_data[7:9])[0]
        
        # Extract application layer function code
        if len(packet_data) > 10:
            func_code = packet_data[11]
            
            # Check for suspicious function codes
            for attack_type, codes in self.attack_signatures.items():
                if func_code in codes:
                    return {
                        'alert_type': attack_type,
                        'src_addr': src_addr,
                        'dest_addr': dest_addr,
                        'function_code': func_code,
                        'timestamp': time.time()
                    }
        
        return None
    
    def detect_dnp3_flooding(self, traffic_log):
        # Analyze DNP3 traffic volume for DoS detection
        traffic_by_minute = {}
        
        for entry in traffic_log:
            minute = entry['timestamp'] // 60
            if minute not in traffic_by_minute:
                traffic_by_minute[minute] = 0
            traffic_by_minute[minute] += 1
        
        # Detect abnormal traffic spikes
        avg_traffic = sum(traffic_by_minute.values()) / len(traffic_by_minute)
        threshold = avg_traffic * 5  # 5x normal traffic
        
        for minute, count in traffic_by_minute.items():
            if count > threshold:
                yield {
                    'alert': 'DNP3 traffic spike detected',
                    'minute': minute,
                    'packet_count': count,
                    'threshold': threshold
                }
\`\`\`

### 3. EtherNet/IP and CIP Analysis

#### CIP Protocol Hunting
\`\`\`python
class CIPHunter:
    def __init__(self):
        self.device_profiles = {}
        self.suspicious_services = [
            0x4C,  # Get Attribute List
            0x4D,  # Set Attribute List
            0x52,  # Multiple Service Packet
        ]
    
    def analyze_cip_packet(self, packet):
        # Parse Common Industrial Protocol packet
        if len(packet) < 4:
            return None
        
        service_code = packet[0]
        class_id = struct.unpack('<H', packet[2:4])[0] if len(packet) >= 4 else 0
        
        # Detect suspicious CIP services
        if service_code in self.suspicious_services:
            return {
                'alert': 'Suspicious CIP service detected',
                'service_code': hex(service_code),
                'class_id': hex(class_id),
                'risk_level': self.calculate_risk_level(service_code)
            }
        
        # Monitor for device enumeration
        if service_code == 0x01:  # Get Attributes All
            return self.detect_device_enumeration(packet)
        
        return None
    
    def detect_device_enumeration(self, packet):
        # Detect systematic device discovery attempts
        # Implementation would track enumeration patterns
        pass
    
    def calculate_risk_level(self, service_code):
        risk_levels = {
            0x4C: 8,  # Get Attribute List - High risk
            0x4D: 9,  # Set Attribute List - Critical risk
            0x52: 7,  # Multiple Service Packet - Medium-high risk
        }
        return risk_levels.get(service_code, 5)
\`\`\`
      `,
      activities: [
        'Analyze Modbus traffic for anomalies',
        'Hunt for DNP3 protocol attacks',
        'Monitor EtherNet/IP and CIP communications',
        'Build protocol-specific detection rules'
      ]
    },

    {
      id: 'ot-advanced-threats',
      title: 'Advanced OT Threat Hunting',
      content: `
## Hunting Advanced OT Threats

### 1. APT Detection in OT Environments

#### Stuxnet-Style Attack Detection
\`\`\`python
class OTAPTHunter:
    def __init__(self):
        self.stuxnet_indicators = {
            'plc_modifications': [
                'unexpected_program_changes',
                'unauthorized_logic_uploads',
                'frequency_converter_manipulation'
            ],
            'network_patterns': [
                'peer_to_peer_propagation',
                'usb_autorun_activity',
                'command_control_beaconing'
            ]
        }
    
    def hunt_for_apt_activity(self, ot_logs):
        apt_indicators = []
        
        # Hunt for PLC program modifications
        plc_changes = self.detect_plc_modifications(ot_logs)
        if plc_changes:
            apt_indicators.extend(plc_changes)
        
        # Hunt for lateral movement
        lateral_movement = self.detect_lateral_movement(ot_logs)
        if lateral_movement:
            apt_indicators.extend(lateral_movement)
        
        # Hunt for data exfiltration
        data_theft = self.detect_data_exfiltration(ot_logs)
        if data_theft:
            apt_indicators.extend(data_theft)
        
        return apt_indicators
    
    def detect_plc_modifications(self, logs):
        suspicious_changes = []
        
        for log_entry in logs:
            if log_entry.get('event_type') == 'plc_program_change':
                # Check if change was authorized
                if not self.is_authorized_change(log_entry):
                    suspicious_changes.append({
                        'alert': 'Unauthorized PLC program modification',
                        'plc_id': log_entry['plc_id'],
                        'timestamp': log_entry['timestamp'],
                        'change_type': log_entry['change_type'],
                        'risk_level': 'critical'
                    })
        
        return suspicious_changes
    
    def detect_lateral_movement(self, logs):
        # Track network connections between OT devices
        connections = {}
        
        for log_entry in logs:
            if log_entry.get('event_type') == 'network_connection':
                src = log_entry['src_ip']
                dst = log_entry['dst_ip']
                
                if src not in connections:
                    connections[src] = set()
                connections[src].add(dst)
        
        # Identify devices with unusual connectivity patterns
        suspicious_movement = []
        for src, destinations in connections.items():
            if len(destinations) > 10:  # Threshold for suspicious connectivity
                suspicious_movement.append({
                    'alert': 'Potential lateral movement detected',
                    'source_device': src,
                    'target_count': len(destinations),
                    'targets': list(destinations)
                })
        
        return suspicious_movement

### 2. OT-Specific Malware Hunting

#### Industrial Malware Detection
\`\`\`python
import yara

class OTMalwareHunter:
    def __init__(self):
        self.ot_malware_rules = yara.compile(source='''
            rule Stuxnet_PLC_Payload {
                meta:
                    description = "Detects Stuxnet-like PLC manipulation"
                    author = "OT Security Team"
                
                strings:
                    $s1 = { 7A 36 00 00 }  // Siemens PLC signature
                    $s2 = "s7otbxdx.dll" ascii
                    $s3 = { FC 1A 84 }     // Frequency converter codes
                
                condition:
                    2 of them
            }
            
            rule TRITON_Safety_System {
                meta:
                    description = "Detects TRITON/TRISIS safety system malware"
                
                strings:
                    $triton1 = "TriStation" ascii
                    $triton2 = "inject.bin" ascii
                    $triton3 = { 41 4D 20 }  // Safety system commands
                
                condition:
                    any of them
            }
            
            rule Industroyer_Protocol_Attack {
                meta:
                    description = "Detects Industroyer/CrashOverride components"
                
                strings:
                    $proto1 = "IEC 61850" ascii
                    $proto2 = "IEC 104" ascii
                    $proto3 = { 68 04 07 00 00 00 }  // IEC 104 APDU
                
                condition:
                    any of them
            }
        ''')
    
    def scan_ot_systems(self, file_paths):
        malware_detections = []
        
        for file_path in file_paths:
            try:
                matches = self.ot_malware_rules.match(file_path)
                if matches:
                    malware_detections.append({
                        'file': file_path,
                        'malware_family': [match.rule for match in matches],
                        'risk_level': 'critical'
                    })
            except Exception as e:
                print(f"Error scanning {file_path}: {e}")
        
        return malware_detections

### 3. Safety System Monitoring

#### Safety Instrumented System (SIS) Monitoring
\`\`\`python
class SISMonitor:
    def __init__(self):
        self.safety_functions = {}
        self.alarm_thresholds = {
            'spurious_trips': 5,
            'failed_demands': 2,
            'bypass_duration': 3600  # 1 hour in seconds
        }
    
    def monitor_safety_systems(self, sis_logs):
        safety_alerts = []
        
        for log_entry in sis_logs:
            # Monitor for safety system bypasses
            if log_entry.get('event_type') == 'safety_bypass':
                alert = self.check_bypass_duration(log_entry)
                if alert:
                    safety_alerts.append(alert)
            
            # Monitor for spurious trips
            elif log_entry.get('event_type') == 'safety_trip':
                alert = self.check_spurious_trips(log_entry)
                if alert:
                    safety_alerts.append(alert)
            
            # Monitor for failed safety demands
            elif log_entry.get('event_type') == 'safety_demand_failed':
                safety_alerts.append({
                    'alert': 'Safety system failed to respond to demand',
                    'sis_tag': log_entry['sis_tag'],
                    'timestamp': log_entry['timestamp'],
                    'risk_level': 'critical'
                })
        
        return safety_alerts
    
    def check_bypass_duration(self, log_entry):
        bypass_duration = log_entry.get('duration', 0)
        
        if bypass_duration > self.alarm_thresholds['bypass_duration']:
            return {
                'alert': 'Extended safety system bypass detected',
                'sis_tag': log_entry['sis_tag'],
                'duration': bypass_duration,
                'risk_level': 'high'
            }
        
        return None
\`\`\`
      `,
      activities: [
        'Hunt for APT activities in OT networks',
        'Scan for OT-specific malware families',
        'Monitor safety system integrity',
        'Analyze industrial process anomalies'
      ]
    },

    {
      id: 'ot-network-segmentation',
      title: 'OT Network Security Monitoring',
      content: `
## OT Network Segmentation and Monitoring

### 1. Network Boundary Monitoring

#### OT/IT Network Boundary Detection
\`\`\`python
import ipaddress

class OTNetworkMonitor:
    def __init__(self):
        # Define network zones
        self.network_zones = {
            'corporate': [
                ipaddress.IPv4Network('10.0.0.0/16'),
                ipaddress.IPv4Network('**********/12')
            ],
            'dmz': [
                ipaddress.IPv4Network('*************/24')
            ],
            'ot_level_3': [  # Manufacturing operations
                ipaddress.IPv4Network('************/24')
            ],
            'ot_level_2': [  # Supervisory control
                ipaddress.IPv4Network('************/24')
            ],
            'ot_level_1': [  # Basic control
                ipaddress.IPv4Network('************/24')
            ],
            'ot_level_0': [  # Field devices
                ipaddress.IPv4Network('************/24')
            ]
        }
        
        self.allowed_communications = {
            ('ot_level_3', 'ot_level_2'): ['modbus_tcp', 'ethernet_ip'],
            ('ot_level_2', 'ot_level_1'): ['modbus_tcp', 'profinet'],
            ('ot_level_1', 'ot_level_0'): ['modbus_rtu', 'hart']
        }
    
    def analyze_cross_zone_traffic(self, network_flows):
        violations = []
        
        for flow in network_flows:
            src_zone = self.identify_zone(flow['src_ip'])
            dst_zone = self.identify_zone(flow['dst_ip'])
            
            if src_zone != dst_zone:
                violation = self.check_zone_communication(
                    src_zone, dst_zone, flow
                )
                if violation:
                    violations.append(violation)
        
        return violations
    
    def identify_zone(self, ip_address):
        ip_addr = ipaddress.IPv4Address(ip_address)
        
        for zone_name, networks in self.network_zones.items():
            for network in networks:
                if ip_addr in network:
                    return zone_name
        
        return 'unknown'
    
    def check_zone_communication(self, src_zone, dst_zone, flow):
        # Check if communication is allowed between zones
        zone_pair = (src_zone, dst_zone)
        reverse_pair = (dst_zone, src_zone)
        
        allowed_protocols = (
            self.allowed_communications.get(zone_pair, []) +
            self.allowed_communications.get(reverse_pair, [])
        )
        
        if not allowed_protocols:
            return {
                'violation_type': 'unauthorized_zone_communication',
                'src_zone': src_zone,
                'dst_zone': dst_zone,
                'src_ip': flow['src_ip'],
                'dst_ip': flow['dst_ip'],
                'protocol': flow['protocol'],
                'risk_level': 'high'
            }
        
        # Check if protocol is allowed
        if flow['protocol'] not in allowed_protocols:
            return {
                'violation_type': 'unauthorized_protocol',
                'src_zone': src_zone,
                'dst_zone': dst_zone,
                'protocol': flow['protocol'],
                'allowed_protocols': allowed_protocols,
                'risk_level': 'medium'
            }
        
        return None

### 2. OT Asset Discovery and Monitoring

#### Industrial Asset Discovery
\`\`\`python
import socket
import struct

class OTAssetDiscovery:
    def __init__(self):
        self.discovered_assets = {}
        self.protocol_scanners = {
            'modbus': self.scan_modbus_device,
            'ethernet_ip': self.scan_ethernet_ip_device,
            'bacnet': self.scan_bacnet_device
        }
    
    def discover_ot_assets(self, ip_range):
        discovered_devices = []
        
        for ip in self.generate_ip_range(ip_range):
            device_info = self.probe_device(ip)
            if device_info:
                discovered_devices.append(device_info)
        
        return discovered_devices
    
    def probe_device(self, ip):
        device_info = {
            'ip': ip,
            'protocols': [],
            'device_type': 'unknown',
            'vendor': 'unknown',
            'model': 'unknown'
        }
        
        # Test for common OT protocols
        for protocol, scanner in self.protocol_scanners.items():
            if scanner(ip):
                device_info['protocols'].append(protocol)
                device_details = self.get_device_details(ip, protocol)
                device_info.update(device_details)
        
        return device_info if device_info['protocols'] else None
    
    def scan_modbus_device(self, ip, port=502):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def get_device_details(self, ip, protocol):
        if protocol == 'modbus':
            return self.get_modbus_device_info(ip)
        elif protocol == 'ethernet_ip':
            return self.get_ethernet_ip_device_info(ip)
        else:
            return {}
    
    def get_modbus_device_info(self, ip):
        # Query Modbus device identification
        try:
            # Implementation would send Modbus device identification request
            # and parse response for vendor, model, version info
            return {
                'device_type': 'plc',
                'vendor': 'detected_vendor',
                'model': 'detected_model'
            }
        except:
            return {}

### 3. Continuous OT Monitoring

#### Real-time OT Threat Detection
\`\`\`python
import asyncio
import json

class OTThreatDetector:
    def __init__(self):
        self.detection_rules = {
            'unauthorized_plc_access': {
                'protocols': ['modbus_tcp', 'ethernet_ip'],
                'functions': ['write_coils', 'write_registers'],
                'threshold': 5
            },
            'process_manipulation': {
                'parameters': ['setpoint_changes', 'alarm_suppression'],
                'threshold': 3
            },
            'safety_system_interference': {
                'systems': ['sis', 'fire_gas', 'emergency_shutdown'],
                'actions': ['bypass', 'disable', 'modify']
            }
        }
    
    async def monitor_ot_traffic(self, traffic_stream):
        async for packet in traffic_stream:
            threat_analysis = await self.analyze_packet(packet)
            
            if threat_analysis['threat_level'] > 7:
                await self.trigger_incident_response(threat_analysis)
            elif threat_analysis['threat_level'] > 5:
                await self.alert_security_team(threat_analysis)
    
    async def analyze_packet(self, packet):
        threat_indicators = []
        
        # Protocol-specific analysis
        if packet['protocol'] == 'modbus':
            indicators = self.analyze_modbus_packet(packet)
            threat_indicators.extend(indicators)
        
        # Behavioral analysis
        behavioral_indicators = self.analyze_device_behavior(packet)
        threat_indicators.extend(behavioral_indicators)
        
        # Calculate overall threat level
        threat_level = self.calculate_threat_level(threat_indicators)
        
        return {
            'packet_id': packet['id'],
            'threat_level': threat_level,
            'indicators': threat_indicators,
            'timestamp': packet['timestamp']
        }
\`\`\`
      `,
      activities: [
        'Monitor OT network zone boundaries',
        'Discover and profile OT assets',
        'Implement continuous OT monitoring',
        'Build OT-specific detection rules'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Industrial Control System Security Assessment',
      description: 'Conduct comprehensive security assessment of simulated ICS environment',
      tasks: [
        'Map OT network architecture and assets',
        'Identify protocol vulnerabilities',
        'Hunt for unauthorized access attempts',
        'Assess safety system integrity'
      ]
    },
    {
      title: 'APT Investigation in Manufacturing Environment',
      description: 'Investigate advanced persistent threat targeting manufacturing systems',
      tasks: [
        'Analyze PLC program modifications',
        'Track lateral movement through OT networks',
        'Identify data exfiltration attempts',
        'Assess impact on production systems'
      ]
    },
    {
      title: 'OT Security Monitoring Platform',
      description: 'Build comprehensive OT security monitoring and detection platform',
      tasks: [
        'Design OT network monitoring architecture',
        'Implement protocol-specific detection rules',
        'Create safety system monitoring',
        'Develop incident response procedures'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'OT/ICS Threat Hunting Certification',
      description: 'Demonstrate mastery of OT threat hunting techniques and industrial security'
    },
    {
      type: 'project',
      title: 'Industrial Cybersecurity Program',
      description: 'Design and implement comprehensive industrial cybersecurity program'
    }
  ],

  resources: [
    'NIST Cybersecurity Framework for Manufacturing',
    'ICS-CERT Security Guidelines',
    'SANS ICS Security Training Materials',
    'Purdue Model for Industrial Network Architecture',
    'IEC 62443 Industrial Security Standards',
    'NERC CIP Critical Infrastructure Protection Standards'
  ]
};
