/**
 * TH-14: SIEM Integration for Threat Hunting
 * Master multi-platform SIEM integration and unified hunting workflows
 */

export const siemIntegrationContent = {
  id: 'th-14',
  title: 'SIEM Integration for Threat Hunting',
  description: 'Master the integration of multiple SIEM platforms for unified threat hunting operations and cross-platform correlation.',
  duration: '38 hours',
  difficulty: 'Advanced',
  
  objectives: [
    'Design unified hunting architectures across multiple SIEMs',
    'Implement cross-platform data correlation and analysis',
    'Build standardized hunting workflows and playbooks',
    'Create unified threat intelligence integration',
    'Develop automated hunting orchestration systems',
    'Establish consistent hunting metrics and reporting',
    'Optimize multi-SIEM performance and scalability'
  ],

  sections: [
    {
      id: 'multi-siem-architecture',
      title: 'Multi-SIEM Hunting Architecture',
      content: `
## Unified SIEM Architecture Design

### **Architecture Patterns**
\`\`\`yaml
Centralized Hub Model:
  Description: Single hunting platform aggregates data from multiple SIEMs
  Components:
    - Central hunting platform (Splunk/ELK)
    - Data connectors to each SIEM
    - Unified data model
    - Central correlation engine
  Pros:
    - Single pane of glass
    - Consistent hunting experience
    - Centralized correlation
  Cons:
    - Data duplication
    - Potential bottleneck
    - Complex data synchronization

Federated Search Model:
  Description: Hunting queries executed across multiple SIEMs simultaneously
  Components:
    - Federated search engine
    - SIEM API connectors
    - Query translation layer
    - Result aggregation service
  Pros:
    - No data duplication
    - Real-time access
    - Preserves SIEM investments
  Cons:
    - Complex query translation
    - Performance dependencies
    - Limited correlation capabilities

Hybrid Model:
  Description: Combination of centralized and federated approaches
  Components:
    - Core hunting platform
    - Selective data replication
    - Federated search capabilities
    - Smart routing logic
  Pros:
    - Balanced approach
    - Flexible data strategy
    - Optimized performance
  Cons:
    - Increased complexity
    - Higher maintenance overhead
\`\`\`

### **Integration Framework**
\`\`\`python
import asyncio
import aiohttp
import json
from abc import ABC, abstractmethod
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

class SIEMType(Enum):
    SPLUNK = "splunk"
    ELK = "elk"
    QRADAR = "qradar"
    ARCSIGHT = "arcsight"
    SENTINEL = "sentinel"

@dataclass
class HuntingQuery:
    query_id: str
    description: str
    generic_query: str
    time_range: Dict[str, str]
    expected_fields: List[str]
    severity: str

@dataclass
class HuntingResult:
    siem_type: SIEMType
    query_id: str
    event_count: int
    events: List[Dict[str, Any]]
    execution_time: float
    errors: List[str] = None

class SIEMConnector(ABC):
    """Abstract base class for SIEM connectors"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.siem_type = None
    
    @abstractmethod
    async def authenticate(self) -> bool:
        """Authenticate with SIEM platform"""
        pass
    
    @abstractmethod
    async def execute_query(self, query: HuntingQuery) -> HuntingResult:
        """Execute hunting query on SIEM platform"""
        pass
    
    @abstractmethod
    def translate_query(self, generic_query: str) -> str:
        """Translate generic query to SIEM-specific syntax"""
        pass
    
    @abstractmethod
    def normalize_results(self, raw_results: Any) -> List[Dict[str, Any]]:
        """Normalize SIEM results to common format"""
        pass

class SplunkConnector(SIEMConnector):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.siem_type = SIEMType.SPLUNK
        self.session = None
    
    async def authenticate(self) -> bool:
        try:
            auth_url = f"{self.config['base_url']}/services/auth/login"
            auth_data = {
                'username': self.config['username'],
                'password': self.config['password']
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(auth_url, data=auth_data) as response:
                    if response.status == 200:
                        self.session_key = await response.text()
                        return True
            return False
        except Exception as e:
            print(f"Splunk authentication failed: {e}")
            return False
    
    def translate_query(self, generic_query: str) -> str:
        """Translate generic query to SPL"""
        translations = {
            'SELECT': 'search',
            'WHERE source_ip =': 'src_ip=',
            'WHERE dest_ip =': 'dest_ip=',
            'WHERE user =': 'user=',
            'GROUP BY': '| stats count by',
            'ORDER BY': '| sort'
        }
        
        spl_query = generic_query
        for generic, spl in translations.items():
            spl_query = spl_query.replace(generic, spl)
        
        return spl_query
    
    async def execute_query(self, query: HuntingQuery) -> HuntingResult:
        start_time = time.time()
        
        try:
            spl_query = self.translate_query(query.generic_query)
            
            # Add time range
            if query.time_range:
                spl_query += f" earliest={query.time_range['start']} latest={query.time_range['end']}"
            
            search_url = f"{self.config['base_url']}/services/search/jobs"
            search_data = {
                'search': spl_query,
                'output_mode': 'json'
            }
            
            async with aiohttp.ClientSession() as session:
                # Submit search
                async with session.post(search_url, data=search_data) as response:
                    search_id = await response.text()
                
                # Poll for results
                results_url = f"{search_url}/{search_id}/results"
                while True:
                    async with session.get(results_url) as response:
                        if response.status == 200:
                            results = await response.json()
                            break
                    await asyncio.sleep(1)
            
            normalized_events = self.normalize_results(results)
            execution_time = time.time() - start_time
            
            return HuntingResult(
                siem_type=self.siem_type,
                query_id=query.query_id,
                event_count=len(normalized_events),
                events=normalized_events,
                execution_time=execution_time
            )
            
        except Exception as e:
            return HuntingResult(
                siem_type=self.siem_type,
                query_id=query.query_id,
                event_count=0,
                events=[],
                execution_time=time.time() - start_time,
                errors=[str(e)]
            )

class ELKConnector(SIEMConnector):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.siem_type = SIEMType.ELK
    
    async def authenticate(self) -> bool:
        # ELK authentication logic
        return True
    
    def translate_query(self, generic_query: str) -> Dict[str, Any]:
        """Translate generic query to Elasticsearch DSL"""
        # Simplified translation logic
        return {
            "query": {
                "bool": {
                    "must": [
                        {"match_all": {}}
                    ]
                }
            }
        }
    
    async def execute_query(self, query: HuntingQuery) -> HuntingResult:
        # ELK query execution logic
        pass

class MultiSIEMHunter:
    def __init__(self):
        self.connectors: Dict[SIEMType, SIEMConnector] = {}
        self.correlation_engine = CorrelationEngine()
    
    def add_siem(self, siem_type: SIEMType, connector: SIEMConnector):
        self.connectors[siem_type] = connector
    
    async def execute_unified_hunt(self, query: HuntingQuery) -> Dict[SIEMType, HuntingResult]:
        """Execute hunting query across all connected SIEMs"""
        tasks = []
        
        for siem_type, connector in self.connectors.items():
            task = asyncio.create_task(connector.execute_query(query))
            tasks.append((siem_type, task))
        
        results = {}
        for siem_type, task in tasks:
            try:
                result = await task
                results[siem_type] = result
            except Exception as e:
                results[siem_type] = HuntingResult(
                    siem_type=siem_type,
                    query_id=query.query_id,
                    event_count=0,
                    events=[],
                    execution_time=0,
                    errors=[str(e)]
                )
        
        return results
    
    async def correlate_results(self, results: Dict[SIEMType, HuntingResult]) -> List[Dict[str, Any]]:
        """Correlate results across SIEMs"""
        all_events = []
        
        for siem_type, result in results.items():
            for event in result.events:
                event['_source_siem'] = siem_type.value
                all_events.append(event)
        
        return self.correlation_engine.correlate_events(all_events)
\`\`\`

### **Data Normalization Framework**
\`\`\`python
class UnifiedEventSchema:
    """Common event schema for cross-SIEM hunting"""
    
    REQUIRED_FIELDS = [
        'timestamp',
        'event_type',
        'source_siem',
        'raw_event'
    ]
    
    OPTIONAL_FIELDS = [
        'source_ip',
        'destination_ip',
        'source_port',
        'destination_port',
        'protocol',
        'user_name',
        'process_name',
        'command_line',
        'file_path',
        'registry_key',
        'dns_query',
        'http_method',
        'url',
        'user_agent',
        'severity',
        'confidence'
    ]
    
    @staticmethod
    def normalize_event(raw_event: Dict[str, Any], siem_type: SIEMType) -> Dict[str, Any]:
        """Normalize event to unified schema"""
        normalized = {
            'timestamp': None,
            'event_type': 'unknown',
            'source_siem': siem_type.value,
            'raw_event': raw_event
        }
        
        if siem_type == SIEMType.SPLUNK:
            normalized.update({
                'timestamp': raw_event.get('_time'),
                'source_ip': raw_event.get('src_ip', raw_event.get('src')),
                'destination_ip': raw_event.get('dest_ip', raw_event.get('dest')),
                'user_name': raw_event.get('user'),
                'process_name': raw_event.get('process', raw_event.get('Image')),
                'command_line': raw_event.get('CommandLine')
            })
        
        elif siem_type == SIEMType.ELK:
            normalized.update({
                'timestamp': raw_event.get('@timestamp'),
                'source_ip': raw_event.get('source', {}).get('ip'),
                'destination_ip': raw_event.get('destination', {}).get('ip'),
                'user_name': raw_event.get('user', {}).get('name'),
                'process_name': raw_event.get('process', {}).get('name'),
                'command_line': raw_event.get('process', {}).get('command_line')
            })
        
        elif siem_type == SIEMType.SENTINEL:
            normalized.update({
                'timestamp': raw_event.get('TimeGenerated'),
                'source_ip': raw_event.get('SourceIP'),
                'destination_ip': raw_event.get('DestinationIP'),
                'user_name': raw_event.get('UserPrincipalName'),
                'process_name': raw_event.get('ProcessName'),
                'command_line': raw_event.get('CommandLine')
            })
        
        # Remove None values
        return {k: v for k, v in normalized.items() if v is not None}
\`\`\`
      `,
      activities: [
        'Design multi-SIEM architecture',
        'Build SIEM connector framework',
        'Implement data normalization system',
        'Create unified hunting interface'
      ]
    },

    {
      id: 'cross-platform-correlation',
      title: 'Cross-Platform Correlation Engine',
      content: `
## Advanced Correlation Techniques

### **Event Correlation Framework**
\`\`\`python
import networkx as nx
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class CorrelationRule:
    rule_id: str
    name: str
    description: str
    time_window: int  # seconds
    required_events: List[str]
    correlation_fields: List[str]
    severity: str
    confidence_threshold: float

class CorrelationEngine:
    def __init__(self):
        self.rules = []
        self.event_cache = defaultdict(list)
        self.correlation_graph = nx.Graph()
    
    def add_rule(self, rule: CorrelationRule):
        self.rules.append(rule)
    
    def correlate_events(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Correlate events using defined rules"""
        correlations = []
        
        # Group events by time windows
        time_buckets = self._group_events_by_time(events)
        
        for time_bucket, bucket_events in time_buckets.items():
            # Apply each correlation rule
            for rule in self.rules:
                rule_correlations = self._apply_correlation_rule(rule, bucket_events)
                correlations.extend(rule_correlations)
        
        return correlations
    
    def _group_events_by_time(self, events: List[Dict[str, Any]], window_size: int = 300) -> Dict[int, List[Dict[str, Any]]]:
        """Group events into time buckets"""
        time_buckets = defaultdict(list)
        
        for event in events:
            timestamp = datetime.fromisoformat(event['timestamp'])
            bucket = int(timestamp.timestamp() // window_size)
            time_buckets[bucket].append(event)
        
        return time_buckets
    
    def _apply_correlation_rule(self, rule: CorrelationRule, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply correlation rule to events"""
        correlations = []
        
        # Group events by correlation fields
        correlation_groups = defaultdict(list)
        
        for event in events:
            # Create correlation key from specified fields
            key_parts = []
            for field in rule.correlation_fields:
                value = event.get(field)
                if value:
                    key_parts.append(str(value))
            
            if key_parts:
                correlation_key = "|".join(key_parts)
                correlation_groups[correlation_key].append(event)
        
        # Check each group for rule matches
        for correlation_key, group_events in correlation_groups.items():
            correlation = self._check_rule_match(rule, group_events)
            if correlation:
                correlations.append(correlation)
        
        return correlations
    
    def _check_rule_match(self, rule: CorrelationRule, events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check if events match correlation rule"""
        event_types = set(event.get('event_type') for event in events)
        
        # Check if all required event types are present
        required_set = set(rule.required_events)
        if not required_set.issubset(event_types):
            return None
        
        # Calculate confidence score
        confidence = len(event_types.intersection(required_set)) / len(required_set)
        
        if confidence >= rule.confidence_threshold:
            return {
                'correlation_id': f"{rule.rule_id}_{int(datetime.now().timestamp())}",
                'rule_name': rule.name,
                'description': rule.description,
                'confidence': confidence,
                'severity': rule.severity,
                'event_count': len(events),
                'events': events,
                'correlation_fields': rule.correlation_fields,
                'time_span': self._calculate_time_span(events)
            }
        
        return None
    
    def _calculate_time_span(self, events: List[Dict[str, Any]]) -> Dict[str, str]:
        """Calculate time span of correlated events"""
        timestamps = [datetime.fromisoformat(event['timestamp']) for event in events]
        
        return {
            'start_time': min(timestamps).isoformat(),
            'end_time': max(timestamps).isoformat(),
            'duration_seconds': (max(timestamps) - min(timestamps)).total_seconds()
        }

# Example correlation rules
lateral_movement_rule = CorrelationRule(
    rule_id="lateral_movement_001",
    name="Lateral Movement Detection",
    description="Detect lateral movement across multiple systems",
    time_window=3600,  # 1 hour
    required_events=["authentication", "process_execution", "network_connection"],
    correlation_fields=["user_name", "source_ip"],
    severity="high",
    confidence_threshold=0.8
)

data_exfiltration_rule = CorrelationRule(
    rule_id="data_exfiltration_001",
    name="Data Exfiltration Pattern",
    description="Detect potential data exfiltration activities",
    time_window=1800,  # 30 minutes
    required_events=["file_access", "network_connection", "large_data_transfer"],
    correlation_fields=["user_name", "destination_ip"],
    severity="critical",
    confidence_threshold=0.7
)

privilege_escalation_rule = CorrelationRule(
    rule_id="privilege_escalation_001",
    name="Privilege Escalation Chain",
    description="Detect privilege escalation attack chains",
    time_window=900,  # 15 minutes
    required_events=["authentication_failure", "privilege_change", "admin_access"],
    correlation_fields=["user_name", "source_ip"],
    severity="high",
    confidence_threshold=0.9
)
\`\`\`

### **Attack Chain Reconstruction**
\`\`\`python
class AttackChainAnalyzer:
    def __init__(self):
        self.attack_graph = nx.DiGraph()
        self.mitre_mapping = self._load_mitre_mapping()
    
    def build_attack_chain(self, correlated_events: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build attack chain from correlated events"""
        
        # Sort events by timestamp
        sorted_events = sorted(
            correlated_events, 
            key=lambda x: datetime.fromisoformat(x['timestamp'])
        )
        
        # Map events to MITRE ATT&CK techniques
        attack_techniques = []
        for event in sorted_events:
            technique = self._map_to_mitre_technique(event)
            if technique:
                attack_techniques.append(technique)
        
        # Build attack graph
        attack_chain = {
            'chain_id': f"chain_{int(datetime.now().timestamp())}",
            'start_time': sorted_events[0]['timestamp'],
            'end_time': sorted_events[-1]['timestamp'],
            'event_count': len(sorted_events),
            'techniques': attack_techniques,
            'attack_phases': self._identify_attack_phases(attack_techniques),
            'severity': self._calculate_chain_severity(attack_techniques),
            'events': sorted_events
        }
        
        return attack_chain
    
    def _map_to_mitre_technique(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Map event to MITRE ATT&CK technique"""
        event_type = event.get('event_type')
        process_name = event.get('process_name', '').lower()
        command_line = event.get('command_line', '').lower()
        
        # Simple mapping logic (expand based on requirements)
        if 'powershell' in process_name and 'bypass' in command_line:
            return {
                'technique_id': 'T1059.001',
                'technique_name': 'PowerShell',
                'tactic': 'Execution',
                'confidence': 0.8
            }
        
        elif event_type == 'authentication' and event.get('authentication_result') == 'failure':
            return {
                'technique_id': 'T1110',
                'technique_name': 'Brute Force',
                'tactic': 'Credential Access',
                'confidence': 0.6
            }
        
        elif 'rundll32' in process_name:
            return {
                'technique_id': 'T1055',
                'technique_name': 'Process Injection',
                'tactic': 'Defense Evasion',
                'confidence': 0.7
            }
        
        return None
    
    def _identify_attack_phases(self, techniques: List[Dict[str, Any]]) -> List[str]:
        """Identify attack phases based on techniques"""
        tactics = [technique['tactic'] for technique in techniques]
        
        # Map tactics to attack phases
        phase_mapping = {
            'Reconnaissance': 'Initial Reconnaissance',
            'Initial Access': 'Initial Access',
            'Execution': 'Execution',
            'Persistence': 'Persistence',
            'Privilege Escalation': 'Privilege Escalation',
            'Defense Evasion': 'Defense Evasion',
            'Credential Access': 'Credential Access',
            'Discovery': 'Discovery',
            'Lateral Movement': 'Lateral Movement',
            'Collection': 'Collection',
            'Exfiltration': 'Exfiltration',
            'Impact': 'Impact'
        }
        
        phases = []
        for tactic in tactics:
            phase = phase_mapping.get(tactic)
            if phase and phase not in phases:
                phases.append(phase)
        
        return phases
    
    def _calculate_chain_severity(self, techniques: List[Dict[str, Any]]) -> str:
        """Calculate overall attack chain severity"""
        if not techniques:
            return 'low'
        
        avg_confidence = sum(t['confidence'] for t in techniques) / len(techniques)
        technique_count = len(techniques)
        
        if avg_confidence > 0.8 and technique_count >= 5:
            return 'critical'
        elif avg_confidence > 0.6 and technique_count >= 3:
            return 'high'
        elif avg_confidence > 0.4 and technique_count >= 2:
            return 'medium'
        else:
            return 'low'
\`\`\`
      `,
      activities: [
        'Build cross-platform correlation engine',
        'Create attack chain reconstruction system',
        'Implement MITRE ATT&CK mapping',
        'Develop correlation rule framework'
      ]
    }
  ],

  practicalExercises: [
    {
      title: 'Multi-SIEM Integration Project',
      description: 'Integrate multiple SIEM platforms for unified threat hunting',
      tasks: [
        'Design multi-SIEM architecture',
        'Build SIEM connectors and APIs',
        'Implement data normalization',
        'Create unified hunting interface'
      ]
    },
    {
      title: 'Cross-Platform Correlation Lab',
      description: 'Build advanced correlation engine for multi-SIEM environments',
      tasks: [
        'Develop correlation rules and logic',
        'Implement attack chain reconstruction',
        'Create MITRE ATT&CK mapping',
        'Build correlation visualization'
      ]
    },
    {
      title: 'Enterprise SIEM Strategy Workshop',
      description: 'Design comprehensive SIEM strategy for large enterprise',
      tasks: [
        'Assess current SIEM landscape',
        'Design integration architecture',
        'Create migration and implementation plan',
        'Develop governance and operational procedures'
      ]
    }
  ],

  assessments: [
    {
      type: 'practical',
      title: 'Multi-SIEM Hunting Mastery',
      description: 'Demonstrate advanced multi-SIEM hunting capabilities'
    },
    {
      type: 'project',
      title: 'Enterprise SIEM Integration Solution',
      description: 'Design and implement complete multi-SIEM integration solution'
    }
  ],

  resources: [
    'SIEM Integration Best Practices',
    'Cross-Platform Correlation Techniques',
    'Enterprise SIEM Architecture Patterns',
    'API Integration Documentation',
    'Data Normalization Standards',
    'Attack Chain Analysis Methodologies'
  ]
};
