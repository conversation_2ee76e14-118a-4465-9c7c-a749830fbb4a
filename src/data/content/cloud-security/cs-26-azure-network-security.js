/**
 * Azure Network Security Module
 */

export const azureNetworkSecurityContent = {
  id: "cs-26",
  pathId: "cloud-security",
  title: "Azure Network Security",
  description: "Master Azure network security including Virtual Networks, Network Security Groups, Azure Firewall, and advanced network protection services for comprehensive network defense.",
  objectives: [
    "Understand Azure network security fundamentals",
    "Learn Virtual Network security and segmentation",
    "Master Network Security Groups and Azure Firewall",
    "Develop skills in network monitoring and protection",
    "Learn advanced network security services",
    "Implement comprehensive network security architectures"
  ],
  difficulty: "Advanced",
  estimatedTime: 145,
  sections: [
    {
      title: "Azure Network Security Fundamentals",
      content: `
        <h2>Azure Network Security Overview</h2>
        <p>Azure network security provides multiple layers of protection for network traffic, including virtual network isolation, traffic filtering, threat protection, and connectivity security.</p>
        
        <h3>Azure Network Security Components</h3>
        <ul>
          <li><strong>Virtual Network (VNet) Security:</strong>
            <ul>
              <li>Network isolation and segmentation</li>
              <li>Private IP address spaces</li>
              <li>Subnet-based micro-segmentation</li>
              <li>Service endpoints and private endpoints</li>
            </ul>
          </li>
          <li><strong>Network Access Control:</strong>
            <ul>
              <li>Network Security Groups (NSGs)</li>
              <li>Application Security Groups (ASGs)</li>
              <li>Azure Firewall and third-party NVAs</li>
              <li>Route tables and user-defined routes</li>
            </ul>
          </li>
          <li><strong>Connectivity Security:</strong>
            <ul>
              <li>VPN Gateway and ExpressRoute</li>
              <li>Virtual WAN and hub-spoke architecture</li>
              <li>Private Link and service endpoints</li>
              <li>Cross-premises and hybrid connectivity</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Network Architecture Patterns</h3>
        <ul>
          <li><strong>Hub-and-Spoke Architecture:</strong>
            <ul>
              <li>Centralized connectivity and security</li>
              <li>Shared services in hub VNet</li>
              <li>Spoke VNets for workload isolation</li>
              <li>Transit routing and traffic inspection</li>
            </ul>
          </li>
          <li><strong>Perimeter Network (DMZ):</strong>
            <ul>
              <li>Internet-facing service isolation</li>
              <li>Multi-tier application architecture</li>
              <li>Ingress and egress traffic control</li>
              <li>Web application firewall integration</li>
            </ul>
          </li>
          <li><strong>Zero Trust Network Architecture:</strong>
            <ul>
              <li>Never trust, always verify</li>
              <li>Micro-segmentation and least privilege</li>
              <li>Identity-based network access</li>
              <li>Continuous monitoring and validation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Security Design Principles</h3>
        <ul>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple security layers</li>
              <li>Perimeter, network, and host-based controls</li>
              <li>Redundant security mechanisms</li>
              <li>Fail-safe security posture</li>
            </ul>
          </li>
          <li><strong>Least Privilege Access:</strong>
            <ul>
              <li>Minimal required network access</li>
              <li>Specific port and protocol restrictions</li>
              <li>Source and destination limitations</li>
              <li>Time-based access controls</li>
            </ul>
          </li>
          <li><strong>Network Segmentation:</strong>
            <ul>
              <li>Workload-based isolation</li>
              <li>Environment separation (dev, test, prod)</li>
              <li>Compliance zone segregation</li>
              <li>Threat containment boundaries</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Network Security Groups and Azure Firewall",
      content: `
        <h2>Network Security Groups and Azure Firewall Configuration</h2>
        <p>Network Security Groups and Azure Firewall provide comprehensive traffic filtering and network protection capabilities for Azure virtual networks and resources.</p>
        
        <h3>Network Security Groups (NSGs)</h3>
        <ul>
          <li><strong>NSG Rule Configuration:</strong>
            <ul>
              <li>Inbound and outbound security rules</li>
              <li>Priority-based rule evaluation</li>
              <li>Allow and deny rule actions</li>
              <li>Protocol, port, and address filtering</li>
            </ul>
          </li>
          <li><strong>NSG Assignment and Scope:</strong>
            <ul>
              <li>Subnet-level NSG assignment</li>
              <li>Network interface-level assignment</li>
              <li>Rule evaluation order and precedence</li>
              <li>Default security rules and behavior</li>
            </ul>
          </li>
          <li><strong>Application Security Groups (ASGs):</strong>
            <ul>
              <li>Application-centric security grouping</li>
              <li>Simplified rule management</li>
              <li>Dynamic membership and scaling</li>
              <li>Micro-segmentation implementation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Firewall</h3>
        <ul>
          <li><strong>Firewall Capabilities:</strong>
            <ul>
              <li>Stateful packet inspection</li>
              <li>Application and network-level filtering</li>
              <li>Threat intelligence integration</li>
              <li>High availability and scalability</li>
            </ul>
          </li>
          <li><strong>Rule Types and Collections:</strong>
            <ul>
              <li>Network rules for IP-based filtering</li>
              <li>Application rules for FQDN filtering</li>
              <li>NAT rules for inbound connections</li>
              <li>Rule collection groups and priorities</li>
            </ul>
          </li>
          <li><strong>Azure Firewall Premium:</strong>
            <ul>
              <li>TLS inspection and decryption</li>
              <li>Intrusion Detection and Prevention (IDPS)</li>
              <li>URL filtering and web categories</li>
              <li>Advanced threat protection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Virtual Appliances (NVAs)</h3>
        <ul>
          <li><strong>Third-Party Security Solutions:</strong>
            <ul>
              <li>Next-generation firewalls (NGFWs)</li>
              <li>Intrusion detection/prevention systems</li>
              <li>Web application firewalls</li>
              <li>SD-WAN and networking appliances</li>
            </ul>
          </li>
          <li><strong>NVA Deployment Patterns:</strong>
            <ul>
              <li>Hub-and-spoke with centralized NVA</li>
              <li>Distributed NVA deployment</li>
              <li>High availability and load balancing</li>
              <li>Traffic routing and inspection</li>
            </ul>
          </li>
          <li><strong>Integration and Management:</strong>
            <ul>
              <li>Azure Marketplace deployment</li>
              <li>Partner solution integration</li>
              <li>Centralized management and monitoring</li>
              <li>Automation and orchestration</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced Network Security Services",
      content: `
        <h2>Advanced Azure Network Security Services</h2>
        <p>Advanced network security services in Azure provide comprehensive protection against sophisticated threats, including DDoS attacks, web application vulnerabilities, and network-based intrusions.</p>
        
        <h3>Azure DDoS Protection</h3>
        <ul>
          <li><strong>DDoS Protection Basic:</strong>
            <ul>
              <li>Automatic protection for all Azure services</li>
              <li>Always-on traffic monitoring</li>
              <li>Real-time attack mitigation</li>
              <li>No additional cost</li>
            </ul>
          </li>
          <li><strong>DDoS Protection Standard:</strong>
            <ul>
              <li>Enhanced mitigation capabilities</li>
              <li>Attack analytics and reporting</li>
              <li>Application-specific tuning</li>
              <li>Cost protection and SLA</li>
            </ul>
          </li>
          <li><strong>DDoS Response and Monitoring:</strong>
            <ul>
              <li>Real-time attack notifications</li>
              <li>Attack metrics and telemetry</li>
              <li>Post-attack analysis reports</li>
              <li>Rapid response team support</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Web Application Firewall (WAF)</h3>
        <ul>
          <li><strong>WAF Deployment Options:</strong>
            <ul>
              <li>Application Gateway WAF</li>
              <li>Azure Front Door WAF</li>
              <li>Azure CDN WAF</li>
              <li>Third-party WAF solutions</li>
            </ul>
          </li>
          <li><strong>Protection Capabilities:</strong>
            <ul>
              <li>OWASP Top 10 protection</li>
              <li>SQL injection and XSS prevention</li>
              <li>Bot protection and rate limiting</li>
              <li>Custom rule creation</li>
            </ul>
          </li>
          <li><strong>WAF Management:</strong>
            <ul>
              <li>Managed rule sets and updates</li>
              <li>Custom policy configuration</li>
              <li>Exclusion and allowlist management</li>
              <li>Monitoring and alerting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Monitoring and Analytics</h3>
        <ul>
          <li><strong>Network Watcher:</strong>
            <ul>
              <li>Network topology visualization</li>
              <li>Connection troubleshooting</li>
              <li>Packet capture and analysis</li>
              <li>Flow log analysis</li>
            </ul>
          </li>
          <li><strong>Traffic Analytics:</strong>
            <ul>
              <li>Network traffic insights</li>
              <li>Security threat identification</li>
              <li>Bandwidth utilization analysis</li>
              <li>Geo-location traffic mapping</li>
            </ul>
          </li>
          <li><strong>Security Monitoring Integration:</strong>
            <ul>
              <li>Azure Sentinel integration</li>
              <li>Microsoft Defender for Cloud</li>
              <li>Custom monitoring solutions</li>
              <li>Third-party SIEM connectivity</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary difference between Network Security Groups (NSGs) and Azure Firewall?",
            options: [
              "NSGs are more expensive than Azure Firewall",
              "NSGs provide basic layer 3/4 filtering while Azure Firewall offers advanced stateful inspection and application-level filtering",
              "NSGs work only with VMs while Azure Firewall works with all resources",
              "There is no difference between NSGs and Azure Firewall"
            ],
            correctAnswer: 1,
            explanation: "NSGs provide basic layer 3/4 network filtering with simple allow/deny rules, while Azure Firewall offers advanced stateful packet inspection, application-level filtering, threat intelligence, and centralized management capabilities."
          },
          {
            question: "Which Azure service provides automatic DDoS protection for all Azure services at no additional cost?",
            options: [
              "Azure Firewall",
              "DDoS Protection Standard",
              "DDoS Protection Basic",
              "Network Security Groups"
            ],
            correctAnswer: 2,
            explanation: "DDoS Protection Basic provides automatic protection for all Azure services with always-on traffic monitoring and real-time attack mitigation at no additional cost, while DDoS Protection Standard offers enhanced features for a fee."
          },
          {
            question: "What is the recommended network architecture pattern for centralized security and connectivity in Azure?",
            options: [
              "Flat network architecture",
              "Hub-and-spoke architecture",
              "Mesh network architecture",
              "Point-to-point architecture"
            ],
            correctAnswer: 1,
            explanation: "Hub-and-spoke architecture is recommended for centralized security and connectivity, with shared services in the hub VNet and workload isolation in spoke VNets, enabling centralized traffic inspection and security policy enforcement."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
