/**
 * AWS Security Fundamentals Module
 */

export const awsSecurityFundamentalsContent = {
  id: "cs-19",
  pathId: "cloud-security",
  title: "AWS Security Fundamentals",
  description: "Master Amazon Web Services security fundamentals, including the AWS shared responsibility model, security services, and best practices for securing AWS environments.",
  objectives: [
    "Understand AWS security architecture and shared responsibility",
    "Learn AWS security services and tools",
    "Master AWS account security and organization",
    "Develop skills in AWS security monitoring",
    "Learn AWS compliance and governance",
    "Implement comprehensive AWS security strategies"
  ],
  difficulty: "Intermediate",
  estimatedTime: 130,
  sections: [
    {
      title: "AWS Security Architecture",
      content: `
        <h2>AWS Security Architecture Overview</h2>
        <p>AWS security architecture is built on a shared responsibility model where AWS secures the cloud infrastructure while customers secure their data and applications in the cloud.</p>
        
        <h3>AWS Shared Responsibility Model</h3>
        <ul>
          <li><strong>AWS Responsibilities (Security OF the Cloud):</strong>
            <ul>
              <li>Physical security of data centers</li>
              <li>Hardware and software infrastructure</li>
              <li>Network controls and host operating system patching</li>
              <li>Hypervisor and service infrastructure</li>
            </ul>
          </li>
          <li><strong>Customer Responsibilities (Security IN the Cloud):</strong>
            <ul>
              <li>Guest operating system and application patching</li>
              <li>Identity and access management</li>
              <li>Network and firewall configuration</li>
              <li>Data encryption and protection</li>
            </ul>
          </li>
          <li><strong>Shared Controls:</strong>
            <ul>
              <li>Patch management (infrastructure vs. guest OS)</li>
              <li>Configuration management</li>
              <li>Awareness and training</li>
              <li>Physical and environmental controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Security Foundations</h3>
        <ul>
          <li><strong>AWS Global Infrastructure Security:</strong>
            <ul>
              <li>Regions and Availability Zones</li>
              <li>Edge locations and CloudFront</li>
              <li>Physical security and compliance</li>
              <li>Network architecture and isolation</li>
            </ul>
          </li>
          <li><strong>AWS Security Design Principles:</strong>
            <ul>
              <li>Implement a strong identity foundation</li>
              <li>Apply security at all layers</li>
              <li>Enable traceability and monitoring</li>
              <li>Automate security best practices</li>
            </ul>
          </li>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple security layers</li>
              <li>Network and application security</li>
              <li>Data protection and encryption</li>
              <li>Identity and access controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Security Services Overview</h3>
        <ul>
          <li><strong>Identity and Access Management:</strong>
            <ul>
              <li>AWS IAM (Identity and Access Management)</li>
              <li>AWS SSO (Single Sign-On)</li>
              <li>AWS Directory Service</li>
              <li>AWS Cognito for user authentication</li>
            </ul>
          </li>
          <li><strong>Data Protection Services:</strong>
            <ul>
              <li>AWS KMS (Key Management Service)</li>
              <li>AWS CloudHSM</li>
              <li>AWS Certificate Manager</li>
              <li>AWS Secrets Manager</li>
            </ul>
          </li>
          <li><strong>Infrastructure Protection:</strong>
            <ul>
              <li>AWS WAF (Web Application Firewall)</li>
              <li>AWS Shield (DDoS protection)</li>
              <li>AWS Firewall Manager</li>
              <li>VPC security features</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "AWS Account Security and Organization",
      content: `
        <h2>AWS Account Security and Organization</h2>
        <p>Proper AWS account security and organization structure is fundamental to maintaining a secure and well-governed AWS environment at scale.</p>
        
        <h3>AWS Account Security Best Practices</h3>
        <ul>
          <li><strong>Root Account Security:</strong>
            <ul>
              <li>Enable MFA on root account</li>
              <li>Avoid using root account for daily tasks</li>
              <li>Secure root account credentials</li>
              <li>Monitor root account usage</li>
            </ul>
          </li>
          <li><strong>Account Access Management:</strong>
            <ul>
              <li>Create individual IAM users</li>
              <li>Use IAM roles for applications</li>
              <li>Implement least privilege access</li>
              <li>Regular access reviews and cleanup</li>
            </ul>
          </li>
          <li><strong>Account Monitoring:</strong>
            <ul>
              <li>Enable CloudTrail in all regions</li>
              <li>Configure AWS Config</li>
              <li>Set up billing alerts</li>
              <li>Monitor unusual account activity</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Organizations and Multi-Account Strategy</h3>
        <ul>
          <li><strong>AWS Organizations Benefits:</strong>
            <ul>
              <li>Centralized account management</li>
              <li>Consolidated billing and cost management</li>
              <li>Service Control Policies (SCPs)</li>
              <li>Organizational unit structure</li>
            </ul>
          </li>
          <li><strong>Multi-Account Architecture:</strong>
            <ul>
              <li>Security account for centralized security</li>
              <li>Logging account for audit trails</li>
              <li>Shared services account</li>
              <li>Environment-specific accounts (dev, test, prod)</li>
            </ul>
          </li>
          <li><strong>Cross-Account Access:</strong>
            <ul>
              <li>Cross-account IAM roles</li>
              <li>Resource sharing strategies</li>
              <li>Centralized identity management</li>
              <li>Federated access patterns</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Control Tower and Landing Zone</h3>
        <ul>
          <li><strong>AWS Control Tower Features:</strong>
            <ul>
              <li>Automated multi-account setup</li>
              <li>Pre-configured security guardrails</li>
              <li>Centralized logging and monitoring</li>
              <li>Account factory for new accounts</li>
            </ul>
          </li>
          <li><strong>Landing Zone Components:</strong>
            <ul>
              <li>Core organizational units</li>
              <li>Shared services infrastructure</li>
              <li>Security and compliance baselines</li>
              <li>Network architecture foundation</li>
            </ul>
          </li>
          <li><strong>Guardrails and Policies:</strong>
            <ul>
              <li>Preventive guardrails (SCPs)</li>
              <li>Detective guardrails (Config rules)</li>
              <li>Mandatory and strongly recommended</li>
              <li>Custom guardrail development</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "AWS Security Monitoring and Compliance",
      content: `
        <h2>AWS Security Monitoring and Compliance</h2>
        <p>Comprehensive security monitoring and compliance management in AWS requires leveraging multiple services and implementing continuous monitoring strategies.</p>
        
        <h3>AWS Security Monitoring Services</h3>
        <ul>
          <li><strong>AWS CloudTrail:</strong>
            <ul>
              <li>API call logging and auditing</li>
              <li>Multi-region and multi-account trails</li>
              <li>Event history and analysis</li>
              <li>Integration with CloudWatch and S3</li>
            </ul>
          </li>
          <li><strong>AWS Config:</strong>
            <ul>
              <li>Configuration change tracking</li>
              <li>Compliance rule evaluation</li>
              <li>Resource relationship mapping</li>
              <li>Configuration history and snapshots</li>
            </ul>
          </li>
          <li><strong>AWS Security Hub:</strong>
            <ul>
              <li>Centralized security findings</li>
              <li>Multi-service integration</li>
              <li>Security standards compliance</li>
              <li>Custom insight creation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Threat Detection and Response</h3>
        <ul>
          <li><strong>Amazon GuardDuty:</strong>
            <ul>
              <li>Intelligent threat detection</li>
              <li>Machine learning-based analysis</li>
              <li>DNS, VPC Flow, and CloudTrail analysis</li>
              <li>Threat intelligence integration</li>
            </ul>
          </li>
          <li><strong>AWS Detective:</strong>
            <ul>
              <li>Security investigation and analysis</li>
              <li>Behavior graph creation</li>
              <li>Root cause analysis</li>
              <li>GuardDuty finding investigation</li>
            </ul>
          </li>
          <li><strong>Amazon Macie:</strong>
            <ul>
              <li>Data discovery and classification</li>
              <li>Sensitive data protection</li>
              <li>S3 bucket security assessment</li>
              <li>Data privacy monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Compliance and Governance</h3>
        <ul>
          <li><strong>Compliance Programs:</strong>
            <ul>
              <li>SOC 1, 2, and 3 compliance</li>
              <li>PCI DSS certification</li>
              <li>HIPAA and HITECH compliance</li>
              <li>FedRAMP authorization</li>
            </ul>
          </li>
          <li><strong>AWS Artifact:</strong>
            <ul>
              <li>Compliance report access</li>
              <li>Security and compliance documentation</li>
              <li>Agreement management</li>
              <li>Audit artifact repository</li>
            </ul>
          </li>
          <li><strong>Governance Tools:</strong>
            <ul>
              <li>AWS Well-Architected Framework</li>
              <li>AWS Trusted Advisor</li>
              <li>AWS Systems Manager</li>
              <li>AWS Service Catalog</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "In the AWS Shared Responsibility Model, who is responsible for patching the guest operating system?",
            options: [
              "AWS is responsible",
              "The customer is responsible",
              "Both AWS and customer share responsibility",
              "It depends on the service type"
            ],
            correctAnswer: 1,
            explanation: "The customer is responsible for patching the guest operating system as part of their security responsibilities 'in the cloud', while AWS handles the underlying infrastructure patching."
          },
          {
            question: "Which AWS service provides centralized security findings from multiple AWS security services?",
            options: [
              "AWS CloudTrail",
              "AWS Config",
              "AWS Security Hub",
              "Amazon GuardDuty"
            ],
            correctAnswer: 2,
            explanation: "AWS Security Hub provides centralized security findings by aggregating alerts and findings from multiple AWS security services like GuardDuty, Config, Macie, and third-party tools."
          },
          {
            question: "What is the primary benefit of using AWS Organizations for security?",
            options: [
              "Reduced costs only",
              "Centralized account management and Service Control Policies (SCPs)",
              "Improved application performance",
              "Automatic security patching"
            ],
            correctAnswer: 1,
            explanation: "AWS Organizations provides centralized account management and enables Service Control Policies (SCPs) for governance, allowing organizations to set guardrails and manage security policies across multiple AWS accounts."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
