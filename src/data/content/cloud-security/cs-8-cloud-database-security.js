/**
 * Cloud Database Security Module
 */

export const cloudDatabaseSecurityContent = {
  id: "cs-8",
  pathId: "cloud-security",
  title: "Cloud Database Security",
  description: "Master cloud database security across different platforms, including encryption, access controls, monitoring, and compliance for cloud-based database services.",
  objectives: [
    "Understand cloud database security fundamentals and threats",
    "Learn database encryption and key management strategies",
    "Master access controls and authentication for cloud databases",
    "Develop skills in database monitoring and auditing",
    "Learn compliance requirements for cloud databases",
    "Implement comprehensive database security strategies"
  ],
  difficulty: "Intermediate",
  estimatedTime: 120,
  sections: [
    {
      title: "Cloud Database Security Fundamentals",
      content: `
        <h2>Cloud Database Security Overview</h2>
        <p>Cloud database security involves protecting data at rest, in transit, and in use across various cloud database services while maintaining performance and accessibility.</p>
        
        <h3>Cloud Database Types and Services</h3>
        <ul>
          <li><strong>Relational Database Services:</strong>
            <ul>
              <li>Amazon RDS (MySQL, PostgreSQL, Oracle, SQL Server)</li>
              <li>Azure SQL Database and Managed Instance</li>
              <li>Google Cloud SQL and AlloyDB</li>
              <li>Aurora (AWS) and Cosmos DB (Azure)</li>
            </ul>
          </li>
          <li><strong>NoSQL Database Services:</strong>
            <ul>
              <li>Amazon DynamoDB and DocumentDB</li>
              <li>Azure Cosmos DB and Table Storage</li>
              <li>Google Firestore and Bigtable</li>
              <li>MongoDB Atlas and Redis Cloud</li>
            </ul>
          </li>
          <li><strong>Data Warehouse Services:</strong>
            <ul>
              <li>Amazon Redshift and Redshift Serverless</li>
              <li>Azure Synapse Analytics</li>
              <li>Google BigQuery</li>
              <li>Snowflake on cloud platforms</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Database Security Threats</h3>
        <ul>
          <li><strong>Data Breach and Unauthorized Access:</strong>
            <ul>
              <li>Credential compromise and privilege escalation</li>
              <li>SQL injection and NoSQL injection attacks</li>
              <li>Misconfigured access controls</li>
              <li>Insider threats and malicious users</li>
            </ul>
          </li>
          <li><strong>Data Loss and Corruption:</strong>
            <ul>
              <li>Accidental deletion and modification</li>
              <li>Ransomware and malware attacks</li>
              <li>System failures and outages</li>
              <li>Backup and recovery failures</li>
            </ul>
          </li>
          <li><strong>Compliance and Regulatory Risks:</strong>
            <ul>
              <li>Data residency and sovereignty issues</li>
              <li>Privacy regulation violations (GDPR, CCPA)</li>
              <li>Industry compliance failures (PCI DSS, HIPAA)</li>
              <li>Audit and reporting deficiencies</li>
            </ul>
          </li>
        </ul>
        
        <h3>Shared Responsibility in Database Security</h3>
        <ul>
          <li><strong>Cloud Provider Responsibilities:</strong>
            <ul>
              <li>Infrastructure and platform security</li>
              <li>Physical security and hardware protection</li>
              <li>Network security and isolation</li>
              <li>Service availability and redundancy</li>
            </ul>
          </li>
          <li><strong>Customer Responsibilities:</strong>
            <ul>
              <li>Data classification and protection</li>
              <li>Access control and authentication</li>
              <li>Application-level security</li>
              <li>Backup and disaster recovery planning</li>
            </ul>
          </li>
          <li><strong>Shared Responsibilities:</strong>
            <ul>
              <li>Encryption key management</li>
              <li>Network access controls</li>
              <li>Monitoring and logging</li>
              <li>Patch management (varies by service)</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Database Encryption and Key Management",
      content: `
        <h2>Database Encryption and Key Management</h2>
        <p>Encryption is fundamental to cloud database security, protecting data at rest, in transit, and during processing through comprehensive encryption strategies.</p>
        
        <h3>Encryption at Rest</h3>
        <ul>
          <li><strong>Database-Level Encryption:</strong>
            <ul>
              <li>Transparent Data Encryption (TDE)</li>
              <li>Column-level and field-level encryption</li>
              <li>Tablespace and file-level encryption</li>
              <li>Application-layer encryption</li>
            </ul>
          </li>
          <li><strong>Storage-Level Encryption:</strong>
            <ul>
              <li>Block storage encryption (EBS, Azure Disk)</li>
              <li>File system encryption</li>
              <li>Backup and snapshot encryption</li>
              <li>Log file encryption</li>
            </ul>
          </li>
          <li><strong>Platform-Specific Implementation:</strong>
            <ul>
              <li>AWS RDS encryption with KMS</li>
              <li>Azure SQL Database Always Encrypted</li>
              <li>Google Cloud SQL encryption options</li>
              <li>Third-party encryption solutions</li>
            </ul>
          </li>
        </ul>
        
        <h3>Encryption in Transit</h3>
        <ul>
          <li><strong>Connection Encryption:</strong>
            <ul>
              <li>SSL/TLS for database connections</li>
              <li>Certificate management and validation</li>
              <li>Protocol version and cipher selection</li>
              <li>Perfect Forward Secrecy (PFS)</li>
            </ul>
          </li>
          <li><strong>Application-to-Database Security:</strong>
            <ul>
              <li>Connection string security</li>
              <li>Connection pooling security</li>
              <li>API gateway encryption</li>
              <li>Microservices communication security</li>
            </ul>
          </li>
          <li><strong>Replication and Backup Encryption:</strong>
            <ul>
              <li>Cross-region replication encryption</li>
              <li>Backup transmission security</li>
              <li>Log shipping encryption</li>
              <li>Disaster recovery site communication</li>
            </ul>
          </li>
        </ul>
        
        <h3>Key Management Strategies</h3>
        <ul>
          <li><strong>Cloud Key Management Services:</strong>
            <ul>
              <li>AWS Key Management Service (KMS)</li>
              <li>Azure Key Vault</li>
              <li>Google Cloud KMS</li>
              <li>Multi-cloud key management</li>
            </ul>
          </li>
          <li><strong>Key Lifecycle Management:</strong>
            <ul>
              <li>Key generation and distribution</li>
              <li>Key rotation and versioning</li>
              <li>Key escrow and recovery</li>
              <li>Key destruction and disposal</li>
            </ul>
          </li>
          <li><strong>Hardware Security Modules (HSM):</strong>
            <ul>
              <li>Cloud HSM services</li>
              <li>Dedicated HSM instances</li>
              <li>FIPS 140-2 compliance</li>
              <li>Custom key management solutions</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Access Controls and Monitoring",
      content: `
        <h2>Database Access Controls and Security Monitoring</h2>
        <p>Implementing robust access controls and comprehensive monitoring ensures that only authorized users can access databases while maintaining visibility into all database activities.</p>
        
        <h3>Identity and Access Management</h3>
        <ul>
          <li><strong>Authentication Mechanisms:</strong>
            <ul>
              <li>Multi-factor authentication (MFA)</li>
              <li>Single sign-on (SSO) integration</li>
              <li>Certificate-based authentication</li>
              <li>Service account management</li>
            </ul>
          </li>
          <li><strong>Authorization and Permissions:</strong>
            <ul>
              <li>Role-based access control (RBAC)</li>
              <li>Attribute-based access control (ABAC)</li>
              <li>Principle of least privilege</li>
              <li>Dynamic permission assignment</li>
            </ul>
          </li>
          <li><strong>Database-Specific Access Controls:</strong>
            <ul>
              <li>Database user management</li>
              <li>Schema and object-level permissions</li>
              <li>Row-level and column-level security</li>
              <li>Stored procedure and function access</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Security and Isolation</h3>
        <ul>
          <li><strong>Network Segmentation:</strong>
            <ul>
              <li>Virtual Private Cloud (VPC) isolation</li>
              <li>Subnet and security group configuration</li>
              <li>Private endpoint and service links</li>
              <li>Network access control lists (NACLs)</li>
            </ul>
          </li>
          <li><strong>Firewall and Traffic Control:</strong>
            <ul>
              <li>Database firewall rules</li>
              <li>IP whitelisting and blacklisting</li>
              <li>Port and protocol restrictions</li>
              <li>Geographic access controls</li>
            </ul>
          </li>
          <li><strong>VPN and Private Connectivity:</strong>
            <ul>
              <li>Site-to-site VPN connections</li>
              <li>ExpressRoute and Direct Connect</li>
              <li>Private peering and transit gateways</li>
              <li>Bastion hosts and jump servers</li>
            </ul>
          </li>
        </ul>
        
        <h3>Database Activity Monitoring</h3>
        <ul>
          <li><strong>Audit Logging and Compliance:</strong>
            <ul>
              <li>Database audit trail configuration</li>
              <li>Query and transaction logging</li>
              <li>Administrative action tracking</li>
              <li>Compliance reporting automation</li>
            </ul>
          </li>
          <li><strong>Real-Time Monitoring:</strong>
            <ul>
              <li>Performance and security metrics</li>
              <li>Anomaly detection and alerting</li>
              <li>Threat detection and response</li>
              <li>Capacity and resource monitoring</li>
            </ul>
          </li>
          <li><strong>Security Information and Event Management:</strong>
            <ul>
              <li>SIEM integration and correlation</li>
              <li>Log aggregation and analysis</li>
              <li>Incident response automation</li>
              <li>Forensic investigation support</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which encryption approach provides the strongest protection for sensitive data in cloud databases?",
            options: [
              "Storage-level encryption only",
              "Transport encryption only",
              "Application-layer encryption combined with database encryption",
              "Default cloud provider encryption"
            ],
            correctAnswer: 2,
            explanation: "Application-layer encryption combined with database encryption provides the strongest protection because it creates multiple layers of security, ensuring data is protected even if one layer is compromised."
          },
          {
            question: "What is the primary benefit of using cloud-native key management services?",
            options: [
              "Lower cost only",
              "Integrated security, compliance, and automated key lifecycle management",
              "Faster performance",
              "Unlimited key storage"
            ],
            correctAnswer: 1,
            explanation: "Cloud-native key management services provide integrated security, compliance features, and automated key lifecycle management, including rotation, backup, and audit capabilities that are difficult to implement manually."
          },
          {
            question: "Which access control principle is most important for cloud database security?",
            options: [
              "Maximum privilege assignment",
              "Principle of least privilege",
              "Open access by default",
              "Single-factor authentication"
            ],
            correctAnswer: 1,
            explanation: "The principle of least privilege is most important because it ensures users and applications have only the minimum access necessary to perform their functions, reducing the attack surface and potential impact of compromised accounts."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
