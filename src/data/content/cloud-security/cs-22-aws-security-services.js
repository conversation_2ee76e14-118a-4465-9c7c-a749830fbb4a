/**
 * AWS Security Services Module
 */

export const awsSecurityServicesContent = {
  id: "cs-22",
  pathId: "cloud-security",
  title: "AWS Security Services",
  description: "Master AWS security services including GuardDuty, Security Hub, WAF, Shield, and other security tools for comprehensive threat detection and protection.",
  objectives: [
    "Understand AWS security services ecosystem",
    "Learn threat detection with GuardDuty and Detective",
    "Master web application protection with WAF and Shield",
    "Develop skills in security monitoring and response",
    "Learn data protection and compliance services",
    "Implement integrated security service strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 145,
  sections: [
    {
      title: "AWS Security Services Overview",
      content: `
        <h2>AWS Security Services Ecosystem</h2>
        <p>AWS provides a comprehensive suite of security services that work together to provide threat detection, incident response, compliance monitoring, and data protection across your AWS environment.</p>
        
        <h3>Security Service Categories</h3>
        <ul>
          <li><strong>Threat Detection and Response:</strong>
            <ul>
              <li>Amazon GuardDuty - Intelligent threat detection</li>
              <li>AWS Security Hub - Centralized security findings</li>
              <li>Amazon Detective - Security investigation</li>
              <li>AWS Systems Manager Incident Manager</li>
            </ul>
          </li>
          <li><strong>Infrastructure Protection:</strong>
            <ul>
              <li>AWS WAF - Web Application Firewall</li>
              <li>AWS Shield - DDoS protection</li>
              <li>AWS Firewall Manager - Centralized firewall management</li>
              <li>AWS Network Firewall - VPC-level protection</li>
            </ul>
          </li>
          <li><strong>Data Protection:</strong>
            <ul>
              <li>Amazon Macie - Data discovery and protection</li>
              <li>AWS KMS - Key management</li>
              <li>AWS CloudHSM - Hardware security modules</li>
              <li>AWS Certificate Manager - SSL/TLS certificates</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Service Integration</h3>
        <ul>
          <li><strong>Centralized Management:</strong>
            <ul>
              <li>AWS Security Hub as central dashboard</li>
              <li>Cross-service finding correlation</li>
              <li>Unified security posture view</li>
              <li>Automated response workflows</li>
            </ul>
          </li>
          <li><strong>Event-Driven Architecture:</strong>
            <ul>
              <li>CloudWatch Events integration</li>
              <li>Lambda-based automated responses</li>
              <li>SNS notifications and alerts</li>
              <li>Third-party SIEM integration</li>
            </ul>
          </li>
          <li><strong>API and Automation:</strong>
            <ul>
              <li>Programmatic service management</li>
              <li>Infrastructure as Code integration</li>
              <li>Custom security orchestration</li>
              <li>DevSecOps pipeline integration</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Service Deployment Strategies</h3>
        <ul>
          <li><strong>Multi-Account Architecture:</strong>
            <ul>
              <li>Centralized security account</li>
              <li>Cross-account service delegation</li>
              <li>Organizational-wide protection</li>
              <li>Consolidated security monitoring</li>
            </ul>
          </li>
          <li><strong>Regional Considerations:</strong>
            <ul>
              <li>Service availability by region</li>
              <li>Data residency requirements</li>
              <li>Cross-region replication</li>
              <li>Disaster recovery planning</li>
            </ul>
          </li>
          <li><strong>Cost Optimization:</strong>
            <ul>
              <li>Service tier selection</li>
              <li>Usage-based pricing models</li>
              <li>Reserved capacity options</li>
              <li>Cost monitoring and alerts</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Threat Detection and Investigation Services",
      content: `
        <h2>AWS Threat Detection and Investigation Services</h2>
        <p>AWS threat detection services use machine learning and threat intelligence to identify malicious activity and provide tools for security investigation and response.</p>
        
        <h3>Amazon GuardDuty</h3>
        <ul>
          <li><strong>Threat Detection Capabilities:</strong>
            <ul>
              <li>DNS log analysis for malicious domains</li>
              <li>VPC Flow Log analysis for network anomalies</li>
              <li>CloudTrail event analysis for suspicious API calls</li>
              <li>Machine learning-based behavioral analysis</li>
            </ul>
          </li>
          <li><strong>Finding Types:</strong>
            <ul>
              <li>Reconnaissance attacks and port scanning</li>
              <li>Instance compromise indicators</li>
              <li>Cryptocurrency mining detection</li>
              <li>Data exfiltration attempts</li>
            </ul>
          </li>
          <li><strong>GuardDuty Configuration:</strong>
            <ul>
              <li>Master and member account setup</li>
              <li>Trusted IP and threat lists</li>
              <li>Finding suppression rules</li>
              <li>Custom threat intelligence feeds</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Security Hub</h3>
        <ul>
          <li><strong>Security Standards:</strong>
            <ul>
              <li>AWS Foundational Security Standard</li>
              <li>CIS AWS Foundations Benchmark</li>
              <li>PCI DSS compliance checks</li>
              <li>Custom security standards</li>
            </ul>
          </li>
          <li><strong>Finding Aggregation:</strong>
            <ul>
              <li>Multi-service finding collection</li>
              <li>Third-party security tool integration</li>
              <li>Finding normalization and scoring</li>
              <li>Workflow status management</li>
            </ul>
          </li>
          <li><strong>Custom Insights:</strong>
            <ul>
              <li>Finding filtering and grouping</li>
              <li>Trend analysis and reporting</li>
              <li>Compliance dashboard creation</li>
              <li>Executive summary views</li>
            </ul>
          </li>
        </ul>
        
        <h3>Amazon Detective</h3>
        <ul>
          <li><strong>Investigation Capabilities:</strong>
            <ul>
              <li>Behavior graph analysis</li>
              <li>Root cause investigation</li>
              <li>Timeline reconstruction</li>
              <li>Entity relationship mapping</li>
            </ul>
          </li>
          <li><strong>Data Sources:</strong>
            <ul>
              <li>GuardDuty findings integration</li>
              <li>VPC Flow Logs analysis</li>
              <li>CloudTrail event correlation</li>
              <li>DNS query analysis</li>
            </ul>
          </li>
          <li><strong>Investigation Workflows:</strong>
            <ul>
              <li>Guided investigation paths</li>
              <li>Visual data exploration</li>
              <li>Evidence collection</li>
              <li>Finding validation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Web Application and Infrastructure Protection",
      content: `
        <h2>AWS Web Application and Infrastructure Protection</h2>
        <p>AWS provides comprehensive protection services for web applications and infrastructure, including DDoS protection, web application firewalls, and network security services.</p>
        
        <h3>AWS WAF (Web Application Firewall)</h3>
        <ul>
          <li><strong>WAF Rule Types:</strong>
            <ul>
              <li>Rate-based rules for DDoS protection</li>
              <li>IP reputation and geographic blocking</li>
              <li>SQL injection and XSS protection</li>
              <li>Custom rule logic and conditions</li>
            </ul>
          </li>
          <li><strong>Managed Rule Groups:</strong>
            <ul>
              <li>AWS Managed Rules for common threats</li>
              <li>Third-party managed rule sets</li>
              <li>OWASP Top 10 protection</li>
              <li>Application-specific rule groups</li>
            </ul>
          </li>
          <li><strong>WAF Integration:</strong>
            <ul>
              <li>CloudFront distribution protection</li>
              <li>Application Load Balancer integration</li>
              <li>API Gateway protection</li>
              <li>AWS AppSync GraphQL APIs</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Shield</h3>
        <ul>
          <li><strong>Shield Standard:</strong>
            <ul>
              <li>Automatic DDoS protection</li>
              <li>Network and transport layer protection</li>
              <li>No additional cost</li>
              <li>Always-on detection and mitigation</li>
            </ul>
          </li>
          <li><strong>Shield Advanced:</strong>
            <ul>
              <li>Enhanced DDoS protection</li>
              <li>24/7 DDoS Response Team (DRT) access</li>
              <li>Advanced attack diagnostics</li>
              <li>DDoS cost protection</li>
            </ul>
          </li>
          <li><strong>DDoS Response:</strong>
            <ul>
              <li>Real-time attack notifications</li>
              <li>Automated mitigation responses</li>
              <li>Custom mitigation strategies</li>
              <li>Post-attack analysis and reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Firewall Manager</h3>
        <ul>
          <li><strong>Centralized Management:</strong>
            <ul>
              <li>Organization-wide firewall policies</li>
              <li>WAF rule deployment automation</li>
              <li>Security group management</li>
              <li>Shield Advanced protection</li>
            </ul>
          </li>
          <li><strong>Policy Types:</strong>
            <ul>
              <li>WAF policies for web applications</li>
              <li>Shield Advanced policies</li>
              <li>Security group policies</li>
              <li>Network Firewall policies</li>
            </ul>
          </li>
          <li><strong>Compliance Monitoring:</strong>
            <ul>
              <li>Policy compliance tracking</li>
              <li>Non-compliant resource identification</li>
              <li>Automated remediation actions</li>
              <li>Compliance reporting</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which AWS service provides centralized security findings from multiple AWS security services?",
            options: [
              "Amazon GuardDuty",
              "AWS Security Hub",
              "Amazon Detective",
              "AWS Config"
            ],
            correctAnswer: 1,
            explanation: "AWS Security Hub provides centralized security findings by aggregating and normalizing findings from multiple AWS security services like GuardDuty, Config, Macie, and third-party security tools."
          },
          {
            question: "What type of analysis does Amazon GuardDuty primarily use for threat detection?",
            options: [
              "Static code analysis",
              "Machine learning and behavioral analysis",
              "Manual security reviews",
              "Signature-based detection only"
            ],
            correctAnswer: 1,
            explanation: "Amazon GuardDuty uses machine learning and behavioral analysis to analyze DNS logs, VPC Flow Logs, and CloudTrail events to detect threats and anomalous behavior patterns."
          },
          {
            question: "Which AWS Shield tier provides access to the DDoS Response Team (DRT)?",
            options: [
              "Shield Standard",
              "Shield Advanced",
              "Both Standard and Advanced",
              "Neither tier provides DRT access"
            ],
            correctAnswer: 1,
            explanation: "AWS Shield Advanced provides access to the DDoS Response Team (DRT) for 24/7 support during DDoS attacks, along with enhanced protection and cost protection features."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
