/**
 * GCP Network Security Module
 */

export const gcpNetworkSecurityContent = {
  id: "cs-31",
  pathId: "cloud-security",
  title: "GCP Network Security",
  description: "Master Google Cloud Platform network security including VPC security, firewall rules, Cloud Armor, and advanced network protection services for comprehensive network defense.",
  objectives: [
    "Understand GCP network security fundamentals",
    "Learn VPC security and network segmentation",
    "Master firewall rules and Cloud Armor",
    "Develop skills in network monitoring and protection",
    "Learn advanced network security services",
    "Implement comprehensive GCP network security architectures"
  ],
  difficulty: "Advanced",
  estimatedTime: 145,
  sections: [
    {
      title: "GCP Network Security Fundamentals",
      content: `
        <h2>Google Cloud Network Security Overview</h2>
        <p>GCP network security provides comprehensive protection through Virtual Private Cloud (VPC) isolation, firewall rules, load balancer security, and advanced threat protection services.</p>
        
        <h3>GCP Network Architecture</h3>
        <ul>
          <li><strong>Virtual Private Cloud (VPC):</strong>
            <ul>
              <li>Global VPC with regional subnets</li>
              <li>Private IP address spaces</li>
              <li>Network isolation and segmentation</li>
              <li>Shared VPC for multi-project connectivity</li>
            </ul>
          </li>
          <li><strong>Network Connectivity:</strong>
            <ul>
              <li>VPC peering for inter-VPC communication</li>
              <li>Cloud VPN for hybrid connectivity</li>
              <li>Cloud Interconnect for dedicated connections</li>
              <li>Private Google Access for service connectivity</li>
            </ul>
          </li>
          <li><strong>Network Security Controls:</strong>
            <ul>
              <li>VPC firewall rules</li>
              <li>Identity-Aware Proxy (IAP)</li>
              <li>Private Service Connect</li>
              <li>VPC Service Controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>VPC Security Features</h3>
        <ul>
          <li><strong>Network Isolation:</strong>
            <ul>
              <li>Project-level network isolation</li>
              <li>Subnet-based micro-segmentation</li>
              <li>Private IP addressing</li>
              <li>Network tags for resource grouping</li>
            </ul>
          </li>
          <li><strong>Private Connectivity:</strong>
            <ul>
              <li>Private Google Access</li>
              <li>Private Service Connect</li>
              <li>VPC Service Controls perimeter</li>
              <li>Private endpoints for services</li>
            </ul>
          </li>
          <li><strong>Network Monitoring:</strong>
            <ul>
              <li>VPC Flow Logs</li>
              <li>Firewall Rules Logging</li>
              <li>Network Intelligence Center</li>
              <li>Packet Mirroring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Security Design Principles</h3>
        <ul>
          <li><strong>Zero Trust Networking:</strong>
            <ul>
              <li>Never trust, always verify</li>
              <li>Identity-based access control</li>
              <li>Micro-segmentation</li>
              <li>Continuous monitoring and validation</li>
            </ul>
          </li>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple security layers</li>
              <li>Perimeter and internal controls</li>
              <li>Application and network security</li>
              <li>Data protection and encryption</li>
            </ul>
          </li>
          <li><strong>Least Privilege Access:</strong>
            <ul>
              <li>Minimal required network access</li>
              <li>Specific port and protocol restrictions</li>
              <li>Source and destination limitations</li>
              <li>Time-based access controls</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "VPC Firewall Rules and Cloud Armor",
      content: `
        <h2>VPC Firewall Rules and Cloud Armor Protection</h2>
        <p>VPC firewall rules and Cloud Armor provide comprehensive traffic filtering and application-layer protection for GCP resources and applications.</p>
        
        <h3>VPC Firewall Rules</h3>
        <ul>
          <li><strong>Firewall Rule Components:</strong>
            <ul>
              <li>Direction (ingress or egress)</li>
              <li>Priority and rule evaluation order</li>
              <li>Action (allow or deny)</li>
              <li>Targets (tags, service accounts, IP ranges)</li>
            </ul>
          </li>
          <li><strong>Traffic Filtering:</strong>
            <ul>
              <li>Protocol and port specifications</li>
              <li>Source and destination filters</li>
              <li>Network tag-based targeting</li>
              <li>Service account-based rules</li>
            </ul>
          </li>
          <li><strong>Firewall Rule Best Practices:</strong>
            <ul>
              <li>Deny-by-default approach</li>
              <li>Specific port and protocol rules</li>
              <li>Network tag organization</li>
              <li>Regular rule auditing and cleanup</li>
            </ul>
          </li>
        </ul>
        
        <h3>Google Cloud Armor</h3>
        <ul>
          <li><strong>DDoS Protection:</strong>
            <ul>
              <li>Automatic DDoS mitigation</li>
              <li>Volumetric attack protection</li>
              <li>Protocol-based attack defense</li>
              <li>Application-layer protection</li>
            </ul>
          </li>
          <li><strong>Web Application Firewall (WAF):</strong>
            <ul>
              <li>OWASP Top 10 protection</li>
              <li>SQL injection and XSS prevention</li>
              <li>Custom security rules</li>
              <li>Geo-based access control</li>
            </ul>
          </li>
          <li><strong>Security Policies:</strong>
            <ul>
              <li>Rate limiting and throttling</li>
              <li>IP allowlisting and blocklisting</li>
              <li>Custom rule expressions</li>
              <li>Adaptive protection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Network Security Services</h3>
        <ul>
          <li><strong>VPC Service Controls:</strong>
            <ul>
              <li>Service perimeter creation</li>
              <li>Data exfiltration protection</li>
              <li>API access controls</li>
              <li>Context-aware access policies</li>
            </ul>
          </li>
          <li><strong>Private Service Connect:</strong>
            <ul>
              <li>Private connectivity to services</li>
              <li>Service producer and consumer model</li>
              <li>Network endpoint groups</li>
              <li>Cross-project service sharing</li>
            </ul>
          </li>
          <li><strong>Binary Authorization:</strong>
            <ul>
              <li>Container image attestation</li>
              <li>Policy-based deployment control</li>
              <li>Supply chain security</li>
              <li>Continuous verification</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Network Monitoring and Advanced Protection",
      content: `
        <h2>GCP Network Monitoring and Advanced Protection Services</h2>
        <p>Advanced network monitoring and protection services in GCP provide comprehensive visibility, threat detection, and automated response capabilities for network security.</p>
        
        <h3>Network Monitoring and Visibility</h3>
        <ul>
          <li><strong>VPC Flow Logs:</strong>
            <ul>
              <li>Network traffic sampling and logging</li>
              <li>Source and destination analysis</li>
              <li>Protocol and port visibility</li>
              <li>Security investigation support</li>
            </ul>
          </li>
          <li><strong>Firewall Rules Logging:</strong>
            <ul>
              <li>Rule hit tracking and analysis</li>
              <li>Allow and deny action logging</li>
              <li>Rule effectiveness assessment</li>
              <li>Compliance and audit support</li>
            </ul>
          </li>
          <li><strong>Network Intelligence Center:</strong>
            <ul>
              <li>Network topology visualization</li>
              <li>Performance insights and analysis</li>
              <li>Connectivity testing and troubleshooting</li>
              <li>Security posture assessment</li>
            </ul>
          </li>
        </ul>
        
        <h3>Threat Detection and Response</h3>
        <ul>
          <li><strong>Security Command Center Integration:</strong>
            <ul>
              <li>Network security findings</li>
              <li>Threat detection and alerting</li>
              <li>Asset inventory and discovery</li>
              <li>Security posture monitoring</li>
            </ul>
          </li>
          <li><strong>Cloud IDS (Intrusion Detection System):</strong>
            <ul>
              <li>Network-based threat detection</li>
              <li>Signature and anomaly-based detection</li>
              <li>Real-time traffic analysis</li>
              <li>Threat intelligence integration</li>
            </ul>
          </li>
          <li><strong>Automated Response:</strong>
            <ul>
              <li>Cloud Functions-based automation</li>
              <li>Firewall rule updates</li>
              <li>Incident response workflows</li>
              <li>Threat containment actions</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Security Architecture Patterns</h3>
        <ul>
          <li><strong>Hub-and-Spoke Architecture:</strong>
            <ul>
              <li>Centralized security controls</li>
              <li>Shared services in hub VPC</li>
              <li>Spoke VPC isolation</li>
              <li>Transit routing and inspection</li>
            </ul>
          </li>
          <li><strong>Multi-Tier Architecture:</strong>
            <ul>
              <li>Web, application, and database tiers</li>
              <li>Tier-based security controls</li>
              <li>Load balancer integration</li>
              <li>Database security and isolation</li>
            </ul>
          </li>
          <li><strong>Zero Trust Network Architecture:</strong>
            <ul>
              <li>Identity-based access control</li>
              <li>Micro-segmentation implementation</li>
              <li>Continuous verification</li>
              <li>Context-aware access policies</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary advantage of GCP's global VPC with regional subnets?",
            options: [
              "Lower costs",
              "Better performance only",
              "Global connectivity with regional resource placement and simplified network management",
              "Automatic security configuration"
            ],
            correctAnswer: 2,
            explanation: "GCP's global VPC with regional subnets provides global connectivity while allowing regional resource placement, simplifying network management and enabling seamless cross-region communication within the same VPC."
          },
          {
            question: "Which GCP service provides application-layer DDoS protection and Web Application Firewall capabilities?",
            options: [
              "VPC Firewall Rules",
              "Cloud Armor",
              "Identity-Aware Proxy",
              "VPC Service Controls"
            ],
            correctAnswer: 1,
            explanation: "Cloud Armor provides application-layer DDoS protection and Web Application Firewall (WAF) capabilities, including protection against OWASP Top 10 vulnerabilities and custom security rules."
          },
          {
            question: "What is the purpose of VPC Service Controls in GCP network security?",
            options: [
              "Load balancing traffic",
              "Creating service perimeters to prevent data exfiltration",
              "Managing firewall rules",
              "Monitoring network performance"
            ],
            correctAnswer: 1,
            explanation: "VPC Service Controls create service perimeters around GCP resources to prevent data exfiltration and provide additional context-aware access controls for sensitive data and services."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
