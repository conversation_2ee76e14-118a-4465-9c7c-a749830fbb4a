/**
 * Container Security in Cloud Module
 */

export const containerSecurityContent = {
  id: "cs-11",
  pathId: "cloud-security",
  title: "Container Security in Cloud",
  description: "Master container security fundamentals, Docker security, Kubernetes security, and cloud-native container protection strategies across different cloud platforms.",
  objectives: [
    "Understand container security fundamentals and threat model",
    "Learn Docker security best practices and hardening",
    "Master Kubernetes security architecture and controls",
    "Develop skills in container image security and scanning",
    "Learn runtime security and monitoring for containers",
    "Implement comprehensive container security strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "Container Security Fundamentals",
      content: `
        <h2>Container Security Overview</h2>
        <p>Container security involves protecting containerized applications throughout their lifecycle, from development and build to deployment and runtime in cloud environments.</p>
        
        <h3>Container Technology Stack</h3>
        <ul>
          <li><strong>Container Runtime Layer:</strong>
            <ul>
              <li>Docker Engine and containerd</li>
              <li>CRI-O and other OCI-compliant runtimes</li>
              <li>Runtime security and isolation</li>
              <li>Resource constraints and limits</li>
            </ul>
          </li>
          <li><strong>Container Orchestration:</strong>
            <ul>
              <li>Kubernetes cluster architecture</li>
              <li>Docker Swarm and other orchestrators</li>
              <li>Service mesh and networking</li>
              <li>Load balancing and service discovery</li>
            </ul>
          </li>
          <li><strong>Cloud Container Services:</strong>
            <ul>
              <li>Amazon ECS, EKS, and Fargate</li>
              <li>Azure Container Instances and AKS</li>
              <li>Google Cloud Run and GKE</li>
              <li>Managed vs. self-managed containers</li>
            </ul>
          </li>
        </ul>
        
        <h3>Container Security Threat Model</h3>
        <ul>
          <li><strong>Image-Based Threats:</strong>
            <ul>
              <li>Vulnerable base images and dependencies</li>
              <li>Malicious images and supply chain attacks</li>
              <li>Secrets and credentials in images</li>
              <li>Backdoors and malware injection</li>
            </ul>
          </li>
          <li><strong>Runtime Threats:</strong>
            <ul>
              <li>Container escape and privilege escalation</li>
              <li>Resource exhaustion and DoS attacks</li>
              <li>Network-based attacks and lateral movement</li>
              <li>Data exfiltration and unauthorized access</li>
            </ul>
          </li>
          <li><strong>Orchestration Threats:</strong>
            <ul>
              <li>Kubernetes API server attacks</li>
              <li>RBAC misconfigurations</li>
              <li>Pod security policy violations</li>
              <li>Service account token abuse</li>
            </ul>
          </li>
        </ul>
        
        <h3>Container Security Principles</h3>
        <ul>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple security layers and controls</li>
              <li>Image, runtime, and network security</li>
              <li>Host and infrastructure protection</li>
              <li>Application-level security measures</li>
            </ul>
          </li>
          <li><strong>Least Privilege Access:</strong>
            <ul>
              <li>Minimal container permissions</li>
              <li>Non-root user execution</li>
              <li>Resource limits and constraints</li>
              <li>Network segmentation and policies</li>
            </ul>
          </li>
          <li><strong>Immutable Infrastructure:</strong>
            <ul>
              <li>Read-only container filesystems</li>
              <li>Immutable image deployments</li>
              <li>Configuration as code</li>
              <li>Stateless application design</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Docker Security and Image Protection",
      content: `
        <h2>Docker Security and Container Image Protection</h2>
        <p>Docker security involves securing the container runtime, images, and deployment configurations to prevent vulnerabilities and ensure safe container operations.</p>
        
        <h3>Docker Security Best Practices</h3>
        <ul>
          <li><strong>Docker Daemon Security:</strong>
            <ul>
              <li>TLS encryption for Docker API</li>
              <li>User namespace remapping</li>
              <li>Seccomp and AppArmor profiles</li>
              <li>Docker daemon configuration hardening</li>
            </ul>
          </li>
          <li><strong>Container Runtime Security:</strong>
            <ul>
              <li>Non-root user execution</li>
              <li>Read-only root filesystem</li>
              <li>Capability dropping and privilege reduction</li>
              <li>Resource limits and cgroups</li>
            </ul>
          </li>
          <li><strong>Network Security:</strong>
            <ul>
              <li>Custom bridge networks</li>
              <li>Network segmentation and isolation</li>
              <li>Port exposure minimization</li>
              <li>Container-to-container communication controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>Container Image Security</h3>
        <ul>
          <li><strong>Secure Image Building:</strong>
            <ul>
              <li>Minimal base images (Alpine, distroless)</li>
              <li>Multi-stage builds for size reduction</li>
              <li>Package manager security updates</li>
              <li>Dockerfile security best practices</li>
            </ul>
          </li>
          <li><strong>Image Vulnerability Scanning:</strong>
            <ul>
              <li>Static analysis and CVE detection</li>
              <li>Dependency vulnerability assessment</li>
              <li>License compliance checking</li>
              <li>Continuous scanning in CI/CD pipelines</li>
            </ul>
          </li>
          <li><strong>Image Registry Security:</strong>
            <ul>
              <li>Private registry implementation</li>
              <li>Image signing and verification</li>
              <li>Access controls and authentication</li>
              <li>Registry vulnerability scanning</li>
            </ul>
          </li>
        </ul>
        
        <h3>Secrets and Configuration Management</h3>
        <ul>
          <li><strong>Secrets Management:</strong>
            <ul>
              <li>External secrets management systems</li>
              <li>Runtime secrets injection</li>
              <li>Avoiding secrets in images</li>
              <li>Secrets rotation and lifecycle</li>
            </ul>
          </li>
          <li><strong>Configuration Security:</strong>
            <ul>
              <li>Environment variable security</li>
              <li>Configuration file protection</li>
              <li>Runtime configuration validation</li>
              <li>Configuration drift detection</li>
            </ul>
          </li>
          <li><strong>Supply Chain Security:</strong>
            <ul>
              <li>Base image provenance verification</li>
              <li>Dependency integrity checking</li>
              <li>Build process security</li>
              <li>Software bill of materials (SBOM)</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Kubernetes Security Architecture",
      content: `
        <h2>Kubernetes Security Architecture and Controls</h2>
        <p>Kubernetes security requires comprehensive understanding of cluster architecture, security controls, and best practices for securing containerized workloads at scale.</p>
        
        <h3>Kubernetes Security Components</h3>
        <ul>
          <li><strong>API Server Security:</strong>
            <ul>
              <li>Authentication mechanisms (certificates, tokens, OIDC)</li>
              <li>Authorization policies (RBAC, ABAC, Webhook)</li>
              <li>Admission controllers and policies</li>
              <li>API server audit logging</li>
            </ul>
          </li>
          <li><strong>etcd Security:</strong>
            <ul>
              <li>Encryption at rest and in transit</li>
              <li>Access controls and authentication</li>
              <li>Backup encryption and security</li>
              <li>Network isolation and firewalling</li>
            </ul>
          </li>
          <li><strong>Node Security:</strong>
            <ul>
              <li>Kubelet security configuration</li>
              <li>Container runtime security</li>
              <li>Host OS hardening</li>
              <li>Node-to-node communication security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Pod Security and Policies</h3>
        <ul>
          <li><strong>Pod Security Standards:</strong>
            <ul>
              <li>Privileged, Baseline, and Restricted profiles</li>
              <li>Security context configuration</li>
              <li>Pod Security Policy (deprecated) migration</li>
              <li>Pod Security Admission controller</li>
            </ul>
          </li>
          <li><strong>Network Policies:</strong>
            <ul>
              <li>Ingress and egress traffic control</li>
              <li>Namespace isolation</li>
              <li>Service-to-service communication</li>
              <li>Network policy enforcement</li>
            </ul>
          </li>
          <li><strong>Resource Management:</strong>
            <ul>
              <li>Resource quotas and limits</li>
              <li>Quality of Service (QoS) classes</li>
              <li>Horizontal and vertical pod autoscaling</li>
              <li>Resource monitoring and alerting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Runtime Security and Monitoring</h3>
        <ul>
          <li><strong>Runtime Threat Detection:</strong>
            <ul>
              <li>Behavioral analysis and anomaly detection</li>
              <li>Process and system call monitoring</li>
              <li>Network traffic analysis</li>
              <li>File integrity monitoring</li>
            </ul>
          </li>
          <li><strong>Security Monitoring Tools:</strong>
            <ul>
              <li>Falco for runtime security</li>
              <li>Twistlock/Prisma Cloud</li>
              <li>Aqua Security platform</li>
              <li>Sysdig Secure</li>
            </ul>
          </li>
          <li><strong>Incident Response:</strong>
            <ul>
              <li>Automated response and remediation</li>
              <li>Container isolation and quarantine</li>
              <li>Forensic data collection</li>
              <li>Security event correlation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which Docker security practice provides the strongest protection against container escape attacks?",
            options: [
              "Using official base images only",
              "Running containers as non-root users with user namespace remapping",
              "Limiting container memory usage",
              "Using private registries only"
            ],
            correctAnswer: 1,
            explanation: "Running containers as non-root users with user namespace remapping provides the strongest protection against container escape attacks by ensuring that even if an attacker escapes the container, they have limited privileges on the host system."
          },
          {
            question: "What is the primary purpose of Kubernetes Network Policies?",
            options: [
              "Load balancing traffic",
              "Controlling ingress and egress traffic between pods and services",
              "Managing DNS resolution",
              "Monitoring network performance"
            ],
            correctAnswer: 1,
            explanation: "Kubernetes Network Policies control ingress and egress traffic between pods and services, providing micro-segmentation and zero-trust networking capabilities within the cluster."
          },
          {
            question: "Which approach is most effective for preventing secrets from being exposed in container images?",
            options: [
              "Encrypting the entire image",
              "Using environment variables in Dockerfile",
              "Runtime secrets injection from external secrets management systems",
              "Storing secrets in image metadata"
            ],
            correctAnswer: 2,
            explanation: "Runtime secrets injection from external secrets management systems is most effective because it keeps secrets out of images entirely and provides them only when needed at runtime, with proper access controls and rotation capabilities."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
