/**
 * Cloud Identity Management Module
 */

export const cloudIdentityContent = {
  id: "cs-4",
  pathId: "cloud-security",
  title: "Cloud Identity Management",
  description: "Master cloud identity and access management (IAM) principles, including authentication, authorization, federation, and identity governance across cloud platforms.",
  objectives: [
    "Understand cloud identity management fundamentals",
    "Learn authentication and authorization mechanisms",
    "Master identity federation and single sign-on",
    "Implement multi-factor authentication strategies",
    "Understand privileged access management in cloud",
    "Learn identity governance and lifecycle management"
  ],
  difficulty: "Intermediate",
  estimatedTime: 100,
  sections: [
    {
      title: "Cloud Identity Fundamentals",
      content: `
        <h2>Cloud Identity Management Fundamentals</h2>
        <p>Cloud identity management is the foundation of cloud security, controlling who can access what resources and under what conditions.</p>
        
        <h3>Core Identity Concepts</h3>
        <ul>
          <li><strong>Identity:</strong> A unique representation of a user, service, or device</li>
          <li><strong>Authentication:</strong> Verifying the identity of a user or service</li>
          <li><strong>Authorization:</strong> Determining what an authenticated identity can access</li>
          <li><strong>Accountability:</strong> Tracking and auditing identity activities</li>
        </ul>
        
        <h3>Cloud Identity Challenges</h3>
        <ul>
          <li><strong>Scale and Complexity:</strong>
            <ul>
              <li>Managing thousands of users and services</li>
              <li>Multiple cloud platforms and services</li>
              <li>Dynamic and ephemeral resources</li>
              <li>Distributed global infrastructure</li>
            </ul>
          </li>
          <li><strong>Security Requirements:</strong>
            <ul>
              <li>Zero trust architecture principles</li>
              <li>Least privilege access</li>
              <li>Continuous authentication and authorization</li>
              <li>Real-time threat detection and response</li>
            </ul>
          </li>
          <li><strong>Compliance and Governance:</strong>
            <ul>
              <li>Regulatory compliance requirements</li>
              <li>Data sovereignty and residency</li>
              <li>Audit trails and reporting</li>
              <li>Identity lifecycle management</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Identity Models</h3>
        <ul>
          <li><strong>Cloud-Native Identity:</strong>
            <ul>
              <li>Identity services provided by cloud platforms</li>
              <li>Integrated with cloud services and APIs</li>
              <li>Examples: AWS IAM, Azure AD, Google Cloud Identity</li>
              <li>Optimized for cloud-native applications</li>
            </ul>
          </li>
          <li><strong>Federated Identity:</strong>
            <ul>
              <li>Integration with on-premises identity systems</li>
              <li>Single sign-on across hybrid environments</li>
              <li>Standards-based federation (SAML, OAuth, OpenID Connect)</li>
              <li>Centralized identity management</li>
            </ul>
          </li>
          <li><strong>Hybrid Identity:</strong>
            <ul>
              <li>Combination of cloud and on-premises identity</li>
              <li>Synchronized identity stores</li>
              <li>Consistent policies across environments</li>
              <li>Seamless user experience</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Authentication and Authorization",
      content: `
        <h2>Cloud Authentication and Authorization</h2>
        <p>Robust authentication and authorization mechanisms are critical for securing cloud resources and ensuring appropriate access controls.</p>
        
        <h3>Authentication Methods</h3>
        <ul>
          <li><strong>Multi-Factor Authentication (MFA):</strong>
            <ul>
              <li>Something you know (password, PIN)</li>
              <li>Something you have (token, smartphone)</li>
              <li>Something you are (biometrics)</li>
              <li>Adaptive authentication based on risk</li>
            </ul>
          </li>
          <li><strong>Certificate-Based Authentication:</strong>
            <ul>
              <li>Digital certificates for user and device authentication</li>
              <li>Public key infrastructure (PKI) integration</li>
              <li>Mutual TLS authentication</li>
              <li>Certificate lifecycle management</li>
            </ul>
          </li>
          <li><strong>Token-Based Authentication:</strong>
            <ul>
              <li>JSON Web Tokens (JWT)</li>
              <li>OAuth 2.0 and OpenID Connect</li>
              <li>API keys and access tokens</li>
              <li>Token validation and refresh</li>
            </ul>
          </li>
        </ul>
        
        <h3>Authorization Models</h3>
        <ul>
          <li><strong>Role-Based Access Control (RBAC):</strong>
            <ul>
              <li>Users assigned to roles with specific permissions</li>
              <li>Hierarchical role structures</li>
              <li>Role inheritance and delegation</li>
              <li>Separation of duties enforcement</li>
            </ul>
          </li>
          <li><strong>Attribute-Based Access Control (ABAC):</strong>
            <ul>
              <li>Fine-grained access control based on attributes</li>
              <li>Dynamic policy evaluation</li>
              <li>Context-aware access decisions</li>
              <li>Complex policy expressions</li>
            </ul>
          </li>
          <li><strong>Policy-Based Access Control:</strong>
            <ul>
              <li>Centralized policy management</li>
              <li>Declarative access policies</li>
              <li>Policy as code implementation</li>
              <li>Automated policy enforcement</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud-Specific Authorization</h3>
        <ul>
          <li><strong>Resource-Based Policies:</strong>
            <ul>
              <li>Policies attached to cloud resources</li>
              <li>Cross-account access control</li>
              <li>Service-specific permissions</li>
              <li>Conditional access based on context</li>
            </ul>
          </li>
          <li><strong>Service Accounts and Roles:</strong>
            <ul>
              <li>Non-human identities for services</li>
              <li>Temporary credentials and role assumption</li>
              <li>Cross-service authentication</li>
              <li>Automated credential rotation</li>
            </ul>
          </li>
          <li><strong>Just-in-Time Access:</strong>
            <ul>
              <li>Temporary privilege elevation</li>
              <li>Time-bound access grants</li>
              <li>Approval workflows</li>
              <li>Audit trails for privileged access</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Identity Federation and SSO",
      content: `
        <h2>Identity Federation and Single Sign-On</h2>
        <p>Identity federation enables seamless and secure access across multiple systems and organizations while maintaining centralized identity management.</p>
        
        <h3>Federation Standards and Protocols</h3>
        <ul>
          <li><strong>SAML 2.0 (Security Assertion Markup Language):</strong>
            <ul>
              <li>XML-based standard for exchanging authentication data</li>
              <li>Identity Provider (IdP) and Service Provider (SP) model</li>
              <li>Assertion-based authentication</li>
              <li>Enterprise SSO and B2B federation</li>
            </ul>
          </li>
          <li><strong>OAuth 2.0:</strong>
            <ul>
              <li>Authorization framework for API access</li>
              <li>Token-based authorization</li>
              <li>Delegated access without sharing credentials</li>
              <li>Mobile and web application integration</li>
            </ul>
          </li>
          <li><strong>OpenID Connect:</strong>
            <ul>
              <li>Identity layer on top of OAuth 2.0</li>
              <li>Standardized authentication protocol</li>
              <li>JSON Web Token (JWT) based</li>
              <li>Modern web and mobile applications</li>
            </ul>
          </li>
        </ul>
        
        <h3>Federation Architecture</h3>
        <ul>
          <li><strong>Identity Provider (IdP):</strong>
            <ul>
              <li>Authenticates users and issues assertions</li>
              <li>Maintains user identity information</li>
              <li>Provides authentication services</li>
              <li>Examples: Active Directory, Okta, Azure AD</li>
            </ul>
          </li>
          <li><strong>Service Provider (SP):</strong>
            <ul>
              <li>Relies on IdP for authentication</li>
              <li>Consumes identity assertions</li>
              <li>Provides services to authenticated users</li>
              <li>Cloud applications and services</li>
            </ul>
          </li>
          <li><strong>Trust Relationships:</strong>
            <ul>
              <li>Cryptographic trust between IdP and SP</li>
              <li>Certificate-based validation</li>
              <li>Metadata exchange</li>
              <li>Security policy alignment</li>
            </ul>
          </li>
        </ul>
        
        <h3>SSO Implementation Best Practices</h3>
        <ul>
          <li><strong>Security Considerations:</strong>
            <ul>
              <li>Strong authentication at the IdP</li>
              <li>Secure token transmission</li>
              <li>Token validation and expiration</li>
              <li>Session management and logout</li>
            </ul>
          </li>
          <li><strong>User Experience:</strong>
            <ul>
              <li>Seamless authentication flow</li>
              <li>Minimal user interaction</li>
              <li>Clear error messages</li>
              <li>Mobile-friendly interfaces</li>
            </ul>
          </li>
          <li><strong>Operational Management:</strong>
            <ul>
              <li>Centralized user provisioning</li>
              <li>Automated account lifecycle</li>
              <li>Monitoring and alerting</li>
              <li>Disaster recovery planning</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which authentication factor represents 'something you are' in multi-factor authentication?",
            options: [
              "Password",
              "SMS token",
              "Biometric scan",
              "Hardware token"
            ],
            correctAnswer: 2,
            explanation: "Biometric scans (fingerprint, facial recognition, etc.) represent 'something you are' - an inherent characteristic of the user."
          },
          {
            question: "In SAML federation, what is the role of the Identity Provider (IdP)?",
            options: [
              "Consumes authentication assertions",
              "Provides services to users",
              "Authenticates users and issues assertions",
              "Manages network security"
            ],
            correctAnswer: 2,
            explanation: "The Identity Provider (IdP) is responsible for authenticating users and issuing SAML assertions that Service Providers can trust."
          },
          {
            question: "What is the primary benefit of implementing Just-in-Time (JIT) access in cloud environments?",
            options: [
              "Faster authentication",
              "Reduced administrative overhead",
              "Minimized exposure window for privileged access",
              "Better user experience"
            ],
            correctAnswer: 2,
            explanation: "JIT access minimizes the exposure window for privileged access by granting elevated permissions only when needed and for a limited time."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
