/**
 * AWS VPC Security Module
 */

export const awsVpcSecurityContent = {
  id: "cs-21",
  pathId: "cloud-security",
  title: "AWS VPC Security",
  description: "Master AWS Virtual Private Cloud security, including network segmentation, security groups, NACLs, and advanced VPC security features for comprehensive network protection.",
  objectives: [
    "Understand AWS VPC security fundamentals",
    "Learn network segmentation and isolation strategies",
    "Master security groups and NACLs configuration",
    "Develop skills in VPC monitoring and logging",
    "Learn advanced VPC security features",
    "Implement comprehensive VPC security architectures"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "AWS VPC Security Fundamentals",
      content: `
        <h2>AWS VPC Security Overview</h2>
        <p>AWS Virtual Private Cloud (VPC) provides a logically isolated network environment where you can launch AWS resources with complete control over network configuration and security.</p>
        
        <h3>VPC Security Components</h3>
        <ul>
          <li><strong>Network Isolation:</strong>
            <ul>
              <li>Logically isolated network environment</li>
              <li>Private IP address ranges (RFC 1918)</li>
              <li>Subnet-based segmentation</li>
              <li>Availability Zone distribution</li>
            </ul>
          </li>
          <li><strong>Security Groups:</strong>
            <ul>
              <li>Instance-level virtual firewalls</li>
              <li>Stateful traffic filtering</li>
              <li>Allow rules only (default deny)</li>
              <li>Protocol, port, and source/destination control</li>
            </ul>
          </li>
          <li><strong>Network Access Control Lists (NACLs):</strong>
            <ul>
              <li>Subnet-level traffic filtering</li>
              <li>Stateless rule evaluation</li>
              <li>Allow and deny rules</li>
              <li>Numbered rule priority</li>
            </ul>
          </li>
        </ul>
        
        <h3>VPC Network Architecture</h3>
        <ul>
          <li><strong>Subnet Design:</strong>
            <ul>
              <li>Public subnets with internet gateway access</li>
              <li>Private subnets for internal resources</li>
              <li>Database subnets for data tier isolation</li>
              <li>Multi-AZ deployment for high availability</li>
            </ul>
          </li>
          <li><strong>Routing and Gateways:</strong>
            <ul>
              <li>Route tables and routing decisions</li>
              <li>Internet Gateway for public access</li>
              <li>NAT Gateway/Instance for outbound access</li>
              <li>VPC Endpoints for AWS service access</li>
            </ul>
          </li>
          <li><strong>Connectivity Options:</strong>
            <ul>
              <li>VPC Peering for inter-VPC communication</li>
              <li>Transit Gateway for hub-and-spoke architecture</li>
              <li>VPN connections for hybrid connectivity</li>
              <li>Direct Connect for dedicated connections</li>
            </ul>
          </li>
        </ul>
        
        <h3>Defense in Depth Strategy</h3>
        <ul>
          <li><strong>Multiple Security Layers:</strong>
            <ul>
              <li>Network-level controls (NACLs)</li>
              <li>Instance-level controls (Security Groups)</li>
              <li>Application-level security</li>
              <li>Data encryption and protection</li>
            </ul>
          </li>
          <li><strong>Principle of Least Privilege:</strong>
            <ul>
              <li>Minimal required network access</li>
              <li>Specific port and protocol restrictions</li>
              <li>Source and destination limitations</li>
              <li>Regular access review and cleanup</li>
            </ul>
          </li>
          <li><strong>Network Segmentation:</strong>
            <ul>
              <li>Tier-based architecture (web, app, database)</li>
              <li>Environment separation (dev, test, prod)</li>
              <li>Compliance zone isolation</li>
              <li>Micro-segmentation strategies</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Security Groups and NACLs Configuration",
      content: `
        <h2>Security Groups and Network ACLs Configuration</h2>
        <p>Proper configuration of security groups and NACLs is essential for implementing effective network security controls and traffic filtering in AWS VPC environments.</p>
        
        <h3>Security Groups Best Practices</h3>
        <ul>
          <li><strong>Rule Configuration:</strong>
            <ul>
              <li>Specific port ranges instead of broad access</li>
              <li>Source-specific rules (avoid 0.0.0.0/0)</li>
              <li>Protocol-specific restrictions</li>
              <li>Reference other security groups for internal access</li>
            </ul>
          </li>
          <li><strong>Security Group Management:</strong>
            <ul>
              <li>Descriptive naming conventions</li>
              <li>Purpose-based group organization</li>
              <li>Regular rule auditing and cleanup</li>
              <li>Change management and documentation</li>
            </ul>
          </li>
          <li><strong>Common Security Group Patterns:</strong>
            <ul>
              <li>Web tier: HTTP/HTTPS from internet</li>
              <li>Application tier: specific ports from web tier</li>
              <li>Database tier: database ports from app tier</li>
              <li>Management: SSH/RDP from bastion hosts</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network ACLs Implementation</h3>
        <ul>
          <li><strong>NACL Rule Design:</strong>
            <ul>
              <li>Numbered rules with priority order</li>
              <li>Explicit allow and deny rules</li>
              <li>Ephemeral port considerations</li>
              <li>Stateless nature requiring bidirectional rules</li>
            </ul>
          </li>
          <li><strong>NACL Use Cases:</strong>
            <ul>
              <li>Subnet-level traffic blocking</li>
              <li>Compliance requirement enforcement</li>
              <li>Additional security layer</li>
              <li>Broad traffic pattern control</li>
            </ul>
          </li>
          <li><strong>NACL vs Security Groups:</strong>
            <ul>
              <li>Subnet vs instance level protection</li>
              <li>Stateless vs stateful operation</li>
              <li>Allow/deny vs allow-only rules</li>
              <li>Complementary security controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Network Security Features</h3>
        <ul>
          <li><strong>VPC Endpoints:</strong>
            <ul>
              <li>Gateway endpoints for S3 and DynamoDB</li>
              <li>Interface endpoints for other AWS services</li>
              <li>Private connectivity without internet gateway</li>
              <li>Endpoint policies for access control</li>
            </ul>
          </li>
          <li><strong>AWS PrivateLink:</strong>
            <ul>
              <li>Private connectivity to third-party services</li>
              <li>Service provider and consumer model</li>
              <li>Network Load Balancer integration</li>
              <li>Cross-account service sharing</li>
            </ul>
          </li>
          <li><strong>VPC Security Groups for Lambda:</strong>
            <ul>
              <li>Lambda function VPC configuration</li>
              <li>ENI-based network access</li>
              <li>Cold start performance considerations</li>
              <li>VPC endpoint optimization</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "VPC Monitoring and Advanced Security",
      content: `
        <h2>VPC Monitoring and Advanced Security Features</h2>
        <p>Comprehensive VPC monitoring and advanced security features provide visibility into network traffic and enable proactive threat detection and response.</p>
        
        <h3>VPC Flow Logs</h3>
        <ul>
          <li><strong>Flow Log Configuration:</strong>
            <ul>
              <li>VPC, subnet, or ENI level logging</li>
              <li>Accepted, rejected, or all traffic</li>
              <li>CloudWatch Logs or S3 destinations</li>
              <li>Custom log format and fields</li>
            </ul>
          </li>
          <li><strong>Flow Log Analysis:</strong>
            <ul>
              <li>Traffic pattern identification</li>
              <li>Security incident investigation</li>
              <li>Network troubleshooting</li>
              <li>Compliance and auditing</li>
            </ul>
          </li>
          <li><strong>Automated Analysis Tools:</strong>
            <ul>
              <li>Amazon Athena for querying</li>
              <li>Amazon Elasticsearch for visualization</li>
              <li>Third-party SIEM integration</li>
              <li>Custom analytics and alerting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Security Monitoring</h3>
        <ul>
          <li><strong>Traffic Analysis:</strong>
            <ul>
              <li>Baseline traffic pattern establishment</li>
              <li>Anomaly detection and alerting</li>
              <li>DDoS attack identification</li>
              <li>Data exfiltration detection</li>
            </ul>
          </li>
          <li><strong>Security Group Monitoring:</strong>
            <ul>
              <li>Rule change tracking</li>
              <li>Overly permissive rule detection</li>
              <li>Unused security group identification</li>
              <li>Compliance violation alerts</li>
            </ul>
          </li>
          <li><strong>Network Performance Monitoring:</strong>
            <ul>
              <li>Bandwidth utilization tracking</li>
              <li>Latency and packet loss monitoring</li>
              <li>Connection failure analysis</li>
              <li>Capacity planning insights</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced VPC Security Architecture</h3>
        <ul>
          <li><strong>Transit Gateway Security:</strong>
            <ul>
              <li>Centralized connectivity hub</li>
              <li>Route table-based segmentation</li>
              <li>Cross-account and cross-region connectivity</li>
              <li>Network firewall integration</li>
            </ul>
          </li>
          <li><strong>AWS Network Firewall:</strong>
            <ul>
              <li>Stateful and stateless rule engines</li>
              <li>Intrusion detection and prevention</li>
              <li>Domain name filtering</li>
              <li>Custom rule group creation</li>
            </ul>
          </li>
          <li><strong>VPC Security Automation:</strong>
            <ul>
              <li>Infrastructure as Code (CloudFormation/Terraform)</li>
              <li>Automated security group management</li>
              <li>Dynamic rule updates based on threats</li>
              <li>Compliance remediation automation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary difference between Security Groups and Network ACLs in AWS VPC?",
            options: [
              "Security Groups are more expensive than NACLs",
              "Security Groups are stateful while NACLs are stateless",
              "Security Groups work at the VPC level while NACLs work at the instance level",
              "Security Groups support deny rules while NACLs only support allow rules"
            ],
            correctAnswer: 1,
            explanation: "Security Groups are stateful (return traffic is automatically allowed) while NACLs are stateless (require explicit rules for both inbound and outbound traffic). Security Groups work at the instance level while NACLs work at the subnet level."
          },
          {
            question: "Which AWS service provides the most comprehensive network traffic visibility in a VPC?",
            options: [
              "CloudTrail",
              "VPC Flow Logs",
              "CloudWatch Metrics",
              "AWS Config"
            ],
            correctAnswer: 1,
            explanation: "VPC Flow Logs provide the most comprehensive network traffic visibility by capturing information about IP traffic going to and from network interfaces in your VPC, including source, destination, ports, and protocols."
          },
          {
            question: "What is the recommended approach for database tier security in a multi-tier VPC architecture?",
            options: [
              "Place databases in public subnets with security groups",
              "Place databases in private subnets with security groups allowing access only from application tier",
              "Use only NACLs for database protection",
              "Allow all traffic to databases for flexibility"
            ],
            correctAnswer: 1,
            explanation: "The recommended approach is to place databases in private subnets with security groups that allow access only from the application tier, implementing the principle of least privilege and defense in depth."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
