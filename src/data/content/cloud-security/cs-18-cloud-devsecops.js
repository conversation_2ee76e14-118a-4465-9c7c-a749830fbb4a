/**
 * Cloud DevSecOps Module
 */

export const cloudDevSecOpsContent = {
  id: "cs-18",
  pathId: "cloud-security",
  title: "Cloud DevSecOps",
  description: "Master DevSecOps practices in cloud environments, including security automation, CI/CD pipeline security, and infrastructure as code security.",
  objectives: [
    "Understand DevSecOps fundamentals and cloud integration",
    "Learn CI/CD pipeline security and automation",
    "Master Infrastructure as Code (IaC) security",
    "Develop skills in security testing automation",
    "Learn container and deployment security",
    "Implement comprehensive cloud DevSecOps programs"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "Cloud DevSecOps Fundamentals",
      content: `
        <h2>Cloud DevSecOps Overview</h2>
        <p>Cloud DevSecOps integrates security practices into cloud development and operations workflows, enabling secure, rapid, and reliable software delivery at scale.</p>
        
        <h3>DevSecOps Principles</h3>
        <ul>
          <li><strong>Shift Left Security:</strong>
            <ul>
              <li>Early security integration in development</li>
              <li>Security by design principles</li>
              <li>Developer security training</li>
              <li>Automated security testing</li>
            </ul>
          </li>
          <li><strong>Continuous Security:</strong>
            <ul>
              <li>Security throughout the lifecycle</li>
              <li>Continuous monitoring and feedback</li>
              <li>Automated security validation</li>
              <li>Real-time threat detection</li>
            </ul>
          </li>
          <li><strong>Collaboration and Culture:</strong>
            <ul>
              <li>Cross-functional team integration</li>
              <li>Shared security responsibility</li>
              <li>Security as enabler, not blocker</li>
              <li>Continuous learning and improvement</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud DevSecOps Benefits</h3>
        <ul>
          <li><strong>Security Benefits:</strong>
            <ul>
              <li>Reduced security vulnerabilities</li>
              <li>Faster security issue resolution</li>
              <li>Improved compliance posture</li>
              <li>Enhanced threat detection</li>
            </ul>
          </li>
          <li><strong>Operational Benefits:</strong>
            <ul>
              <li>Faster time to market</li>
              <li>Reduced manual security tasks</li>
              <li>Improved deployment reliability</li>
              <li>Lower operational costs</li>
            </ul>
          </li>
          <li><strong>Business Benefits:</strong>
            <ul>
              <li>Increased customer trust</li>
              <li>Regulatory compliance</li>
              <li>Competitive advantage</li>
              <li>Risk reduction</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud DevSecOps Challenges</h3>
        <ul>
          <li><strong>Technical Challenges:</strong>
            <ul>
              <li>Tool integration complexity</li>
              <li>Scalability and performance</li>
              <li>Multi-cloud environments</li>
              <li>Legacy system integration</li>
            </ul>
          </li>
          <li><strong>Organizational Challenges:</strong>
            <ul>
              <li>Cultural transformation</li>
              <li>Skill gaps and training</li>
              <li>Process standardization</li>
              <li>Governance and compliance</li>
            </ul>
          </li>
          <li><strong>Security Challenges:</strong>
            <ul>
              <li>Rapid deployment security</li>
              <li>Container and microservices security</li>
              <li>Infrastructure as Code security</li>
              <li>Supply chain security</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "CI/CD Pipeline Security",
      content: `
        <h2>CI/CD Pipeline Security and Automation</h2>
        <p>Securing CI/CD pipelines is critical for DevSecOps, requiring integration of security controls, automated testing, and continuous monitoring throughout the software delivery process.</p>
        
        <h3>Pipeline Security Architecture</h3>
        <ul>
          <li><strong>Source Code Security:</strong>
            <ul>
              <li>Git repository security and access controls</li>
              <li>Branch protection and code review</li>
              <li>Secrets scanning and management</li>
              <li>Static Application Security Testing (SAST)</li>
            </ul>
          </li>
          <li><strong>Build Security:</strong>
            <ul>
              <li>Secure build environments</li>
              <li>Dependency scanning and management</li>
              <li>Container image security scanning</li>
              <li>Software composition analysis (SCA)</li>
            </ul>
          </li>
          <li><strong>Deployment Security:</strong>
            <ul>
              <li>Infrastructure security validation</li>
              <li>Configuration security testing</li>
              <li>Dynamic Application Security Testing (DAST)</li>
              <li>Runtime security monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Testing Automation</h3>
        <ul>
          <li><strong>Static Analysis Security Testing (SAST):</strong>
            <ul>
              <li>Source code vulnerability scanning</li>
              <li>Security rule enforcement</li>
              <li>Code quality and security metrics</li>
              <li>IDE and pipeline integration</li>
            </ul>
          </li>
          <li><strong>Dynamic Analysis Security Testing (DAST):</strong>
            <ul>
              <li>Runtime vulnerability assessment</li>
              <li>Web application security testing</li>
              <li>API security validation</li>
              <li>Penetration testing automation</li>
            </ul>
          </li>
          <li><strong>Interactive Application Security Testing (IAST):</strong>
            <ul>
              <li>Real-time vulnerability detection</li>
              <li>Application instrumentation</li>
              <li>Runtime security monitoring</li>
              <li>Accurate vulnerability identification</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud CI/CD Security Tools</h3>
        <ul>
          <li><strong>AWS DevSecOps Tools:</strong>
            <ul>
              <li>AWS CodePipeline security integration</li>
              <li>Amazon CodeGuru security reviews</li>
              <li>AWS Security Hub findings</li>
              <li>Amazon Inspector vulnerability assessment</li>
            </ul>
          </li>
          <li><strong>Azure DevSecOps Tools:</strong>
            <ul>
              <li>Azure DevOps security extensions</li>
              <li>Azure Security Center integration</li>
              <li>Microsoft Defender for DevOps</li>
              <li>Azure Key Vault integration</li>
            </ul>
          </li>
          <li><strong>Google Cloud DevSecOps Tools:</strong>
            <ul>
              <li>Cloud Build security scanning</li>
              <li>Binary Authorization</li>
              <li>Container Analysis API</li>
              <li>Cloud Security Command Center</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Infrastructure as Code Security",
      content: `
        <h2>Infrastructure as Code (IaC) Security</h2>
        <p>IaC security involves securing infrastructure definitions, templates, and deployment processes to ensure secure and compliant cloud infrastructure provisioning.</p>
        
        <h3>IaC Security Fundamentals</h3>
        <ul>
          <li><strong>IaC Security Challenges:</strong>
            <ul>
              <li>Configuration drift and compliance</li>
              <li>Template security vulnerabilities</li>
              <li>Secrets and credential management</li>
              <li>Access control and permissions</li>
            </ul>
          </li>
          <li><strong>Security Benefits of IaC:</strong>
            <ul>
              <li>Consistent security configurations</li>
              <li>Version control and audit trails</li>
              <li>Automated compliance checking</li>
              <li>Repeatable secure deployments</li>
            </ul>
          </li>
          <li><strong>IaC Security Best Practices:</strong>
            <ul>
              <li>Security by default configurations</li>
              <li>Least privilege access principles</li>
              <li>Encryption and data protection</li>
              <li>Network security and segmentation</li>
            </ul>
          </li>
        </ul>
        
        <h3>IaC Security Scanning and Validation</h3>
        <ul>
          <li><strong>Static IaC Analysis:</strong>
            <ul>
              <li>Template security scanning</li>
              <li>Policy as Code validation</li>
              <li>Compliance rule checking</li>
              <li>Security misconfiguration detection</li>
            </ul>
          </li>
          <li><strong>IaC Security Tools:</strong>
            <ul>
              <li>Checkov for Terraform and CloudFormation</li>
              <li>Terrascan for multi-cloud IaC</li>
              <li>Bridgecrew platform integration</li>
              <li>Cloud provider native tools</li>
            </ul>
          </li>
          <li><strong>Policy as Code:</strong>
            <ul>
              <li>Open Policy Agent (OPA) integration</li>
              <li>Rego policy language</li>
              <li>Gatekeeper for Kubernetes</li>
              <li>Custom policy development</li>
            </ul>
          </li>
        </ul>
        
        <h3>Container and Deployment Security</h3>
        <ul>
          <li><strong>Container Security in DevSecOps:</strong>
            <ul>
              <li>Base image security scanning</li>
              <li>Dockerfile security best practices</li>
              <li>Container runtime security</li>
              <li>Registry security and signing</li>
            </ul>
          </li>
          <li><strong>Kubernetes Security:</strong>
            <ul>
              <li>Pod Security Standards</li>
              <li>Network policy enforcement</li>
              <li>RBAC and service accounts</li>
              <li>Admission controller policies</li>
            </ul>
          </li>
          <li><strong>Deployment Security Automation:</strong>
            <ul>
              <li>Blue-green deployment security</li>
              <li>Canary deployment monitoring</li>
              <li>Rollback and recovery procedures</li>
              <li>Production security validation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary goal of 'Shift Left' security in DevSecOps?",
            options: [
              "Moving security teams to the left side of the office",
              "Integrating security early in the development lifecycle",
              "Reducing security tool costs",
              "Improving application performance"
            ],
            correctAnswer: 1,
            explanation: "'Shift Left' security means integrating security practices early in the development lifecycle, allowing security issues to be identified and resolved during development rather than after deployment, reducing costs and risks."
          },
          {
            question: "Which type of security testing provides the most accurate results with the fewest false positives?",
            options: [
              "Static Application Security Testing (SAST)",
              "Dynamic Application Security Testing (DAST)",
              "Interactive Application Security Testing (IAST)",
              "Software Composition Analysis (SCA)"
            ],
            correctAnswer: 2,
            explanation: "Interactive Application Security Testing (IAST) provides the most accurate results with fewer false positives because it combines static and dynamic analysis techniques, analyzing applications during runtime with instrumentation."
          },
          {
            question: "What is the main security benefit of Infrastructure as Code (IaC)?",
            options: [
              "Faster deployment speeds",
              "Lower infrastructure costs",
              "Consistent and repeatable secure configurations",
              "Better user interface design"
            ],
            correctAnswer: 2,
            explanation: "The main security benefit of IaC is consistent and repeatable secure configurations, ensuring that infrastructure is deployed with the same security settings every time, reducing configuration drift and human errors."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
