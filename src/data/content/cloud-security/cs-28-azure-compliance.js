/**
 * Azure Compliance Module
 */

export const azureComplianceContent = {
  id: "cs-28",
  pathId: "cloud-security",
  title: "Azure Compliance",
  description: "Master Azure compliance frameworks, regulatory requirements, and governance tools to ensure adherence to industry standards and legal obligations in Azure environments.",
  objectives: [
    "Understand Azure compliance programs and certifications",
    "Learn regulatory compliance implementation in Azure",
    "Master Azure governance and policy management",
    "Develop skills in compliance automation and monitoring",
    "Learn industry-specific compliance requirements",
    "Implement comprehensive compliance strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 135,
  sections: [
    {
      title: "Azure Compliance Framework",
      content: `
        <h2>Azure Compliance Programs and Certifications</h2>
        <p>Microsoft Azure maintains comprehensive compliance programs with global, regional, and industry-specific certifications to help customers meet their regulatory and compliance requirements.</p>
        
        <h3>Global Compliance Certifications</h3>
        <ul>
          <li><strong>Security and Assurance Standards:</strong>
            <ul>
              <li>SOC 1, 2, and 3 (Service Organization Control)</li>
              <li>ISO 27001, 27017, 27018 (Information Security Management)</li>
              <li>CSA STAR (Cloud Security Alliance)</li>
              <li>FedRAMP (Federal Risk and Authorization Management Program)</li>
            </ul>
          </li>
          <li><strong>Privacy and Data Protection:</strong>
            <ul>
              <li>GDPR (General Data Protection Regulation)</li>
              <li>ISO 27701 (Privacy Information Management)</li>
              <li>Privacy Shield Framework</li>
              <li>CCPA (California Consumer Privacy Act)</li>
            </ul>
          </li>
          <li><strong>Industry-Specific Standards:</strong>
            <ul>
              <li>HIPAA/HITECH (Healthcare)</li>
              <li>PCI DSS (Payment Card Industry)</li>
              <li>FISMA (Federal Information Security Management)</li>
              <li>FERPA (Family Educational Rights and Privacy)</li>
            </ul>
          </li>
        </ul>
        
        <h3>Regional and Local Compliance</h3>
        <ul>
          <li><strong>European Union:</strong>
            <ul>
              <li>GDPR compliance and data residency</li>
              <li>EU Model Clauses</li>
              <li>ENISA (European Network and Information Security Agency)</li>
              <li>National cybersecurity frameworks</li>
            </ul>
          </li>
          <li><strong>United States:</strong>
            <ul>
              <li>FedRAMP High and Moderate</li>
              <li>NIST Cybersecurity Framework</li>
              <li>CJIS (Criminal Justice Information Services)</li>
              <li>IRS 1075 (Internal Revenue Service)</li>
            </ul>
          </li>
          <li><strong>Asia Pacific:</strong>
            <ul>
              <li>Australia IRAP (Information Security Registered Assessors)</li>
              <li>Singapore MTCS (Multi-Tier Cloud Security)</li>
              <li>Japan FISC (Financial Information Systems Center)</li>
              <li>China GB 18030 (National Standard)</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Trust Center and Documentation</h3>
        <ul>
          <li><strong>Microsoft Trust Center:</strong>
            <ul>
              <li>Compliance offerings and certifications</li>
              <li>Security, privacy, and compliance information</li>
              <li>Regional compliance guidance</li>
              <li>Industry-specific resources</li>
            </ul>
          </li>
          <li><strong>Service Trust Portal:</strong>
            <ul>
              <li>Audit reports and certifications</li>
              <li>Compliance guides and whitepapers</li>
              <li>Risk assessment tools</li>
              <li>Data protection resources</li>
            </ul>
          </li>
          <li><strong>Compliance Documentation:</strong>
            <ul>
              <li>Data Processing Agreements (DPA)</li>
              <li>Business Associate Agreements (BAA)</li>
              <li>Standard contractual clauses</li>
              <li>Compliance blueprints and guides</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Azure Policy and Governance",
      content: `
        <h2>Azure Policy and Governance Framework</h2>
        <p>Azure Policy provides governance capabilities to enforce organizational standards and assess compliance at scale across Azure resources and subscriptions.</p>
        
        <h3>Azure Policy Fundamentals</h3>
        <ul>
          <li><strong>Policy Definitions:</strong>
            <ul>
              <li>JSON-based policy rules</li>
              <li>Built-in and custom policies</li>
              <li>Policy effects (deny, audit, append, modify)</li>
              <li>Conditional logic and parameters</li>
            </ul>
          </li>
          <li><strong>Policy Assignments:</strong>
            <ul>
              <li>Scope-based assignment (management group, subscription, resource group)</li>
              <li>Exclusion and exemption management</li>
              <li>Parameter value specification</li>
              <li>Assignment metadata and descriptions</li>
            </ul>
          </li>
          <li><strong>Policy Initiatives:</strong>
            <ul>
              <li>Grouped policy definitions</li>
              <li>Compliance framework mapping</li>
              <li>Simplified assignment and management</li>
              <li>Regulatory standard alignment</li>
            </ul>
          </li>
        </ul>
        
        <h3>Compliance Assessment and Monitoring</h3>
        <ul>
          <li><strong>Compliance Dashboard:</strong>
            <ul>
              <li>Overall compliance score</li>
              <li>Policy compliance status</li>
              <li>Non-compliant resource identification</li>
              <li>Compliance trend analysis</li>
            </ul>
          </li>
          <li><strong>Remediation and Enforcement:</strong>
            <ul>
              <li>Automatic remediation tasks</li>
              <li>Manual remediation workflows</li>
              <li>Bulk remediation operations</li>
              <li>Remediation impact assessment</li>
            </ul>
          </li>
          <li><strong>Compliance Reporting:</strong>
            <ul>
              <li>Detailed compliance reports</li>
              <li>Export capabilities (CSV, JSON)</li>
              <li>Historical compliance data</li>
              <li>Custom report generation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Blueprints and Governance</h3>
        <ul>
          <li><strong>Blueprint Components:</strong>
            <ul>
              <li>ARM templates for resource deployment</li>
              <li>Policy assignments and initiatives</li>
              <li>Role-based access control (RBAC)</li>
              <li>Resource groups and organization</li>
            </ul>
          </li>
          <li><strong>Blueprint Lifecycle:</strong>
            <ul>
              <li>Blueprint definition and versioning</li>
              <li>Assignment to subscriptions</li>
              <li>Tracking and compliance monitoring</li>
              <li>Update and evolution management</li>
            </ul>
          </li>
          <li><strong>Governance Automation:</strong>
            <ul>
              <li>Standardized environment deployment</li>
              <li>Compliance baseline establishment</li>
              <li>Governance artifact management</li>
              <li>Audit trail and change tracking</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Regulatory Compliance Implementation",
      content: `
        <h2>Implementing Regulatory Compliance in Azure</h2>
        <p>Implementing regulatory compliance in Azure requires understanding specific requirements and leveraging Azure services and features to meet compliance obligations effectively.</p>
        
        <h3>GDPR Compliance in Azure</h3>
        <ul>
          <li><strong>Data Protection Principles:</strong>
            <ul>
              <li>Lawfulness, fairness, and transparency</li>
              <li>Purpose limitation and data minimization</li>
              <li>Accuracy and storage limitation</li>
              <li>Integrity, confidentiality, and accountability</li>
            </ul>
          </li>
          <li><strong>Azure GDPR Features:</strong>
            <ul>
              <li>Data Subject Rights (DSR) tools</li>
              <li>Data Processing Agreement (DPA)</li>
              <li>EU data boundary and residency</li>
              <li>Breach notification capabilities</li>
            </ul>
          </li>
          <li><strong>Implementation Strategies:</strong>
            <ul>
              <li>Data discovery and classification</li>
              <li>Privacy by design implementation</li>
              <li>Consent management systems</li>
              <li>Data retention and deletion policies</li>
            </ul>
          </li>
        </ul>
        
        <h3>HIPAA Compliance in Azure</h3>
        <ul>
          <li><strong>HIPAA Safeguards:</strong>
            <ul>
              <li>Administrative safeguards</li>
              <li>Physical safeguards</li>
              <li>Technical safeguards</li>
              <li>Organizational requirements</li>
            </ul>
          </li>
          <li><strong>Azure HIPAA Services:</strong>
            <ul>
              <li>HIPAA-eligible Azure services</li>
              <li>Business Associate Agreement (BAA)</li>
              <li>Encryption and access controls</li>
              <li>Audit logging and monitoring</li>
            </ul>
          </li>
          <li><strong>PHI Protection Strategies:</strong>
            <ul>
              <li>End-to-end encryption</li>
              <li>Role-based access control</li>
              <li>Network isolation and segmentation</li>
              <li>Incident response procedures</li>
            </ul>
          </li>
        </ul>
        
        <h3>Continuous Compliance Monitoring</h3>
        <ul>
          <li><strong>Automated Compliance Assessment:</strong>
            <ul>
              <li>Real-time compliance monitoring</li>
              <li>Policy violation detection</li>
              <li>Compliance drift identification</li>
              <li>Automated remediation triggers</li>
            </ul>
          </li>
          <li><strong>Audit and Evidence Collection:</strong>
            <ul>
              <li>Comprehensive audit trails</li>
              <li>Evidence preservation and retention</li>
              <li>Compliance artifact management</li>
              <li>Regulatory reporting automation</li>
            </ul>
          </li>
          <li><strong>Risk Management Integration:</strong>
            <ul>
              <li>Risk assessment frameworks</li>
              <li>Compliance risk scoring</li>
              <li>Threat and vulnerability correlation</li>
              <li>Business impact analysis</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which Azure service provides governance capabilities to enforce organizational standards and assess compliance at scale?",
            options: [
              "Azure Monitor",
              "Azure Policy",
              "Azure Security Center",
              "Azure Active Directory"
            ],
            correctAnswer: 1,
            explanation: "Azure Policy provides governance capabilities to enforce organizational standards and assess compliance at scale by defining, assigning, and managing policies across Azure resources."
          },
          {
            question: "What is the primary purpose of Azure Blueprints in compliance management?",
            options: [
              "Network monitoring",
              "Standardized environment deployment with compliance baselines",
              "User authentication",
              "Cost management"
            ],
            correctAnswer: 1,
            explanation: "Azure Blueprints enable standardized environment deployment with compliance baselines by packaging ARM templates, policies, RBAC assignments, and other governance artifacts for consistent compliance implementation."
          },
          {
            question: "For GDPR compliance in Azure, what does the Data Processing Agreement (DPA) establish?",
            options: [
              "Technical security requirements only",
              "Microsoft's role as data processor and compliance commitments",
              "User access permissions",
              "Network configuration settings"
            ],
            correctAnswer: 1,
            explanation: "The Data Processing Agreement (DPA) establishes Microsoft's role as a data processor and outlines compliance commitments, data protection measures, and responsibilities for GDPR compliance in Azure services."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
