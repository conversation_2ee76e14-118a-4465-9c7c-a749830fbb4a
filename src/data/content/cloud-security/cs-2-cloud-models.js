/**
 * Cloud Service and Deployment Models Module
 */

export const cloudModelsContent = {
  id: "cs-2",
  pathId: "cloud-security",
  title: "Cloud Service and Deployment Models",
  description: "Master cloud service models (IaaS, PaaS, SaaS) and deployment models (public, private, hybrid, multi-cloud) with their security implications.",
  objectives: [
    "Understand cloud service models and their characteristics",
    "Learn cloud deployment models and use cases",
    "Analyze security implications of different models",
    "Master the shared responsibility model variations",
    "Evaluate model selection criteria",
    "Understand hybrid and multi-cloud architectures"
  ],
  difficulty: "Beginner",
  estimatedTime: 85,
  sections: [
    {
      title: "Cloud Service Models",
      content: `
        <h2>Cloud Service Models</h2>
        <p>Cloud service models define the level of control and responsibility between cloud providers and customers.</p>
        
        <h3>Infrastructure as a Service (IaaS)</h3>
        <ul>
          <li><strong>Definition:</strong> Provides virtualized computing infrastructure</li>
          <li><strong>Components:</strong> Virtual machines, storage, networks, load balancers</li>
          <li><strong>Examples:</strong> AWS EC2, Azure VMs, Google Compute Engine</li>
          <li><strong>Customer Responsibilities:</strong>
            <ul>
              <li>Operating systems and patches</li>
              <li>Applications and middleware</li>
              <li>Data and access management</li>
              <li>Network traffic protection</li>
              <li>Host-based firewalls</li>
            </ul>
          </li>
          <li><strong>Provider Responsibilities:</strong>
            <ul>
              <li>Physical infrastructure security</li>
              <li>Hypervisor and virtualization</li>
              <li>Network infrastructure</li>
              <li>Physical access controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>Platform as a Service (PaaS)</h3>
        <ul>
          <li><strong>Definition:</strong> Provides development and deployment platform</li>
          <li><strong>Components:</strong> Runtime environments, development tools, databases</li>
          <li><strong>Examples:</strong> AWS Elastic Beanstalk, Azure App Service, Google App Engine</li>
          <li><strong>Customer Responsibilities:</strong>
            <ul>
              <li>Application code and configuration</li>
              <li>Data protection and access controls</li>
              <li>User authentication and authorization</li>
              <li>Application-level security</li>
            </ul>
          </li>
          <li><strong>Provider Responsibilities:</strong>
            <ul>
              <li>Platform security and patching</li>
              <li>Runtime environment security</li>
              <li>Infrastructure protection</li>
              <li>Service availability</li>
            </ul>
          </li>
        </ul>
        
        <h3>Software as a Service (SaaS)</h3>
        <ul>
          <li><strong>Definition:</strong> Provides complete software applications</li>
          <li><strong>Components:</strong> Fully functional applications accessible via web</li>
          <li><strong>Examples:</strong> Office 365, Salesforce, Google Workspace</li>
          <li><strong>Customer Responsibilities:</strong>
            <ul>
              <li>User access management</li>
              <li>Data classification and protection</li>
              <li>Endpoint security</li>
              <li>User training and awareness</li>
            </ul>
          </li>
          <li><strong>Provider Responsibilities:</strong>
            <ul>
              <li>Application security</li>
              <li>Infrastructure protection</li>
              <li>Data encryption and backup</li>
              <li>Compliance and certifications</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Cloud Deployment Models",
      content: `
        <h2>Cloud Deployment Models</h2>
        <p>Cloud deployment models determine how cloud infrastructure is provisioned and who has access to it.</p>
        
        <h3>Public Cloud</h3>
        <ul>
          <li><strong>Characteristics:</strong>
            <ul>
              <li>Shared infrastructure among multiple tenants</li>
              <li>Services offered over public internet</li>
              <li>Managed by third-party providers</li>
              <li>Pay-as-you-use pricing model</li>
            </ul>
          </li>
          <li><strong>Security Considerations:</strong>
            <ul>
              <li>Shared responsibility model applies</li>
              <li>Multi-tenancy isolation concerns</li>
              <li>Data location and sovereignty</li>
              <li>Compliance and regulatory requirements</li>
            </ul>
          </li>
          <li><strong>Use Cases:</strong>
            <ul>
              <li>Development and testing environments</li>
              <li>Web applications and websites</li>
              <li>Backup and disaster recovery</li>
              <li>Big data analytics</li>
            </ul>
          </li>
        </ul>
        
        <h3>Private Cloud</h3>
        <ul>
          <li><strong>Characteristics:</strong>
            <ul>
              <li>Dedicated infrastructure for single organization</li>
              <li>Can be on-premises or hosted</li>
              <li>Greater control over security and compliance</li>
              <li>Higher costs but enhanced customization</li>
            </ul>
          </li>
          <li><strong>Security Advantages:</strong>
            <ul>
              <li>Complete control over security policies</li>
              <li>No multi-tenancy concerns</li>
              <li>Customizable compliance controls</li>
              <li>Enhanced data protection</li>
            </ul>
          </li>
          <li><strong>Use Cases:</strong>
            <ul>
              <li>Highly regulated industries</li>
              <li>Sensitive data processing</li>
              <li>Legacy application modernization</li>
              <li>Compliance-critical workloads</li>
            </ul>
          </li>
        </ul>
        
        <h3>Hybrid Cloud</h3>
        <ul>
          <li><strong>Characteristics:</strong>
            <ul>
              <li>Combination of public and private clouds</li>
              <li>Workload portability between environments</li>
              <li>Unified management and orchestration</li>
              <li>Flexible resource allocation</li>
            </ul>
          </li>
          <li><strong>Security Challenges:</strong>
            <ul>
              <li>Consistent security policies across environments</li>
              <li>Secure connectivity between clouds</li>
              <li>Identity and access management complexity</li>
              <li>Data protection during transit</li>
            </ul>
          </li>
        </ul>
        
        <h3>Multi-Cloud</h3>
        <ul>
          <li><strong>Characteristics:</strong>
            <ul>
              <li>Use of multiple cloud service providers</li>
              <li>Avoids vendor lock-in</li>
              <li>Best-of-breed service selection</li>
              <li>Enhanced resilience and redundancy</li>
            </ul>
          </li>
          <li><strong>Security Complexity:</strong>
            <ul>
              <li>Multiple security models to manage</li>
              <li>Inconsistent security controls</li>
              <li>Complex identity federation</li>
              <li>Varied compliance requirements</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "In which cloud service model does the customer have the most security responsibilities?",
            options: [
              "Software as a Service (SaaS)",
              "Platform as a Service (PaaS)",
              "Infrastructure as a Service (IaaS)",
              "All models have equal responsibility"
            ],
            correctAnswer: 2,
            explanation: "In IaaS, customers have the most security responsibilities including operating systems, applications, data, and network traffic protection, while the provider only secures the underlying infrastructure."
          },
          {
            question: "Which deployment model offers the highest level of control over security configurations?",
            options: [
              "Public cloud",
              "Private cloud",
              "Hybrid cloud",
              "Multi-cloud"
            ],
            correctAnswer: 1,
            explanation: "Private cloud offers the highest level of control over security configurations as it provides dedicated infrastructure for a single organization with complete control over security policies."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
