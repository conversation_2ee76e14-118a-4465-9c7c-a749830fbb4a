/**
 * Cloud Security Learning Path - 45 Comprehensive Modules
 * Master cloud security across AWS, Azure, GCP, and multi-cloud environments
 * Complete curriculum for cloud security professionals
 */

// Foundation Phase (CS-1 to CS-15) - Core Fundamentals
import { cloudSecurityIntroContent } from './cs-1-cloud-security-intro.js';
import { cloudModelsContent } from './cs-2-cloud-models.js';
import { sharedResponsibilityContent } from './cs-3-shared-responsibility.js';
import { cloudIdentityContent } from './cs-4-cloud-identity.js';
import { cloudNetworkingContent } from './cs-5-cloud-networking.js';
import { cloudStorageSecurityContent } from './cs-6-cloud-storage-security.js';
import { cloudComputeSecurityContent } from './cs-7-cloud-compute-security.js';
import { cloudDatabaseSecurityContent } from './cs-8-cloud-database-security.js';
import { cloudMonitoringContent } from './cs-9-cloud-monitoring.js';
import { cloudComplianceContent } from './cs-10-cloud-compliance.js';
import { containerSecurityContent } from './cs-11-container-security.js';
import { serverlessSecurityContent } from './cs-12-serverless-security.js';
import { cloudIncidentResponseContent } from './cs-13-cloud-incident-response.js';
import { cloudDisasterRecoveryContent } from './cs-14-cloud-disaster-recovery.js';
import { cloudCostSecurityContent } from './cs-15-cloud-cost-security.js';
import { cloudCryptographyContent } from './cs-16-cloud-cryptography.js';
import { cloudApiSecurityContent } from './cs-17-cloud-api-security.js';
import { cloudDevSecOpsContent } from './cs-18-cloud-devsecops.js';
import { awsSecurityFundamentalsContent } from './cs-19-aws-security-fundamentals.js';
import { awsIamContent } from './cs-20-aws-iam.js';
import { awsVpcSecurityContent } from './cs-21-aws-vpc-security.js';
import { awsSecurityServicesContent } from './cs-22-aws-security-services.js';
import { awsComplianceContent } from './cs-23-aws-compliance.js';
import { azureSecurityFundamentalsContent } from './cs-24-azure-security-fundamentals.js';
import { azureActiveDirectoryContent } from './cs-25-azure-active-directory.js';
import { azureNetworkSecurityContent } from './cs-26-azure-network-security.js';
import { azureSecurityCenterContent } from './cs-27-azure-security-center.js';
import { azureComplianceContent } from './cs-28-azure-compliance.js';
import { gcpSecurityFundamentalsContent } from './cs-29-gcp-security-fundamentals.js';
import { gcpIamContent } from './cs-30-gcp-iam.js';
import { gcpNetworkSecurityContent } from './cs-31-gcp-network-security.js';
import { gcpSecurityServicesContent } from './cs-32-gcp-security-services.js';
import { gcpComplianceContent } from './cs-33-gcp-compliance.js';
import { multiCloudSecurityContent } from './cs-34-multi-cloud-security.js';
import { hybridCloudSecurityContent } from './cs-35-hybrid-cloud-security.js';
import { cloudThreatHuntingContent } from './cs-36-cloud-threat-hunting.js';
import { cloudForensicsContent } from './cs-37-cloud-forensics.js';
import { cloudPenetrationTestingContent } from './cs-38-cloud-penetration-testing.js';
import { cloudSecurityAutomationContent } from './cs-39-cloud-security-automation.js';
import { cloudGovernanceContent } from './cs-40-cloud-governance.js';
import { cloudRiskManagementContent } from './cs-41-cloud-risk-management.js';
import { cloudArchitectureSecurityContent } from './cs-42-cloud-architecture-security.js';
import { emergingCloudThreatsContent } from './cs-43-emerging-cloud-threats.js';
import { cloudSecurityStrategyContent } from './cs-44-cloud-security-strategy.js';
import { capstoneProjectContent } from './cs-45-capstone-project.js';

// All modules are now real modules imported above
const createPlaceholderModule = (id, title, description) => ({
  id,
  pathId: "cloud-security",
  title,
  description,
  objectives: [`Learn ${title.toLowerCase()}`, "Understand key concepts", "Apply best practices"],
  difficulty: "Intermediate",
  estimatedTime: 90,
  sections: [
    {
      title: "Overview",
      content: `<h2>${title}</h2><p>${description}</p><p>This module is under development and will be available soon.</p>`,
      type: "text"
    }
  ]
});
// CS-6 through CS-18 are now real modules imported above

// Intermediate Phase (CS-19 to CS-33) - Platform-Specific Security
// AWS modules (CS-19 to CS-23), Azure modules (CS-24 to CS-28), and GCP modules (CS-29 to CS-33) are now real modules imported above

// Expert Phase (CS-34 to CS-45) - Advanced and Specialized Topics
// All Expert Phase modules (CS-34 to CS-45) are now real modules imported above

export const cloudSecurityLearningPath = {
  id: "cloud-security",
  title: "Cloud Security",
  description: "Master cloud security across AWS, Azure, GCP, and multi-cloud environments. Learn cloud architecture security, identity management, compliance, and advanced threat protection.",
  category: "Cloud & Infrastructure Security",
  difficulty: "Beginner to Expert",
  estimatedTime: "180+ hours",
  prerequisites: [
    "Basic understanding of cloud computing concepts",
    "Fundamental networking and security knowledge",
    "Experience with at least one cloud platform (AWS, Azure, or GCP)",
    "Understanding of virtualization and containerization",
    "Basic scripting and automation skills"
  ],
  outcomes: [
    "Design and implement comprehensive cloud security architectures",
    "Master security controls across AWS, Azure, and Google Cloud Platform",
    "Implement cloud identity and access management (IAM) best practices",
    "Secure cloud networks, storage, and compute resources",
    "Develop cloud security automation and monitoring solutions",
    "Ensure cloud compliance with industry standards and regulations",
    "Perform cloud security assessments and penetration testing",
    "Respond to and investigate cloud security incidents",
    "Lead cloud security transformation and governance initiatives",
    "Stay ahead of emerging cloud threats and security technologies"
  ],
  modules: [
    // Foundation Phase (CS-1 to CS-15) - Core Fundamentals
    cloudSecurityIntroContent,
    cloudModelsContent,
    sharedResponsibilityContent,
    cloudIdentityContent,
    cloudNetworkingContent,
    cloudStorageSecurityContent,
    cloudComputeSecurityContent,
    cloudDatabaseSecurityContent,
    cloudMonitoringContent,
    cloudComplianceContent,
    containerSecurityContent,
    serverlessSecurityContent,
    cloudIncidentResponseContent,
    cloudDisasterRecoveryContent,
    cloudCostSecurityContent,
    cloudCryptographyContent,
    cloudApiSecurityContent,
    cloudDevSecOpsContent,

    // Intermediate Phase (CS-19 to CS-33) - Platform-Specific Security
    awsSecurityFundamentalsContent,
    awsIamContent,
    awsVpcSecurityContent,
    awsSecurityServicesContent,
    awsComplianceContent,
    azureSecurityFundamentalsContent,
    azureActiveDirectoryContent,
    azureNetworkSecurityContent,
    azureSecurityCenterContent,
    azureComplianceContent,
    gcpSecurityFundamentalsContent,
    gcpIamContent,
    gcpNetworkSecurityContent,
    gcpSecurityServicesContent,
    gcpComplianceContent,

    // Expert Phase (CS-34 to CS-45) - Advanced and Specialized Topics
    multiCloudSecurityContent,
    hybridCloudSecurityContent,
    cloudThreatHuntingContent,
    cloudForensicsContent,
    cloudPenetrationTestingContent,
    cloudSecurityAutomationContent,
    cloudGovernanceContent,
    cloudRiskManagementContent,
    cloudArchitectureSecurityContent,
    emergingCloudThreatsContent,
    cloudSecurityStrategyContent,
    capstoneProjectContent
  ]
};

export const getAllCloudSecurityModules = () => {
  return cloudSecurityLearningPath.modules;
};

export const getCloudSecurityModuleById = (id) => {
  return cloudSecurityLearningPath.modules.find(module => module.id === id);
};
