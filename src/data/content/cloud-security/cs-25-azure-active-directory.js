/**
 * Azure Active Directory Module
 */

export const azureActiveDirectoryContent = {
  id: "cs-25",
  pathId: "cloud-security",
  title: "Azure Active Directory",
  description: "Master Azure Active Directory (Azure AD) for identity and access management, including authentication, authorization, conditional access, and advanced security features.",
  objectives: [
    "Understand Azure AD fundamentals and architecture",
    "Learn user and group management in Azure AD",
    "Master authentication and authorization mechanisms",
    "Develop skills in conditional access and security policies",
    "Learn Azure AD advanced security features",
    "Implement comprehensive identity security strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "Azure AD Fundamentals",
      content: `
        <h2>Azure Active Directory Overview</h2>
        <p>Azure Active Directory (Azure AD) is Microsoft's cloud-based identity and access management service that provides authentication, authorization, and identity protection for cloud and on-premises resources.</p>
        
        <h3>Azure AD Core Components</h3>
        <ul>
          <li><strong>Tenants and Directories:</strong>
            <ul>
              <li>Azure AD tenant as identity boundary</li>
              <li>Directory services and schema</li>
              <li>Multi-tenant architecture</li>
              <li>Custom domain configuration</li>
            </ul>
          </li>
          <li><strong>Users and Groups:</strong>
            <ul>
              <li>Cloud-only and synchronized users</li>
              <li>Guest users and B2B collaboration</li>
              <li>Security and distribution groups</li>
              <li>Dynamic group membership</li>
            </ul>
          </li>
          <li><strong>Applications and Service Principals:</strong>
            <ul>
              <li>Enterprise applications</li>
              <li>App registrations and service principals</li>
              <li>Application permissions and consent</li>
              <li>API permissions and scopes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure AD Editions and Licensing</h3>
        <ul>
          <li><strong>Azure AD Free:</strong>
            <ul>
              <li>Basic user and group management</li>
              <li>Single sign-on (SSO) for popular SaaS apps</li>
              <li>Basic security reports</li>
              <li>Self-service password change</li>
            </ul>
          </li>
          <li><strong>Azure AD Premium P1:</strong>
            <ul>
              <li>Advanced group management</li>
              <li>Conditional access policies</li>
              <li>Self-service password reset</li>
              <li>Hybrid identity features</li>
            </ul>
          </li>
          <li><strong>Azure AD Premium P2:</strong>
            <ul>
              <li>Identity Protection and risk-based policies</li>
              <li>Privileged Identity Management (PIM)</li>
              <li>Access reviews and entitlement management</li>
              <li>Advanced security analytics</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure AD Authentication Methods</h3>
        <ul>
          <li><strong>Password-Based Authentication:</strong>
            <ul>
              <li>Traditional username and password</li>
              <li>Password policies and complexity</li>
              <li>Password protection and banned passwords</li>
              <li>Self-service password reset</li>
            </ul>
          </li>
          <li><strong>Multi-Factor Authentication (MFA):</strong>
            <ul>
              <li>Phone call and SMS verification</li>
              <li>Microsoft Authenticator app</li>
              <li>Hardware tokens and FIDO2 keys</li>
              <li>Conditional MFA policies</li>
            </ul>
          </li>
          <li><strong>Passwordless Authentication:</strong>
            <ul>
              <li>Windows Hello for Business</li>
              <li>Microsoft Authenticator passwordless</li>
              <li>FIDO2 security keys</li>
              <li>Certificate-based authentication</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Conditional Access and Security Policies",
      content: `
        <h2>Azure AD Conditional Access and Security Policies</h2>
        <p>Conditional Access provides intelligent access control decisions based on user, device, location, and application context to enforce security policies and protect organizational resources.</p>
        
        <h3>Conditional Access Framework</h3>
        <ul>
          <li><strong>Policy Components:</strong>
            <ul>
              <li>Users and groups (who)</li>
              <li>Cloud apps and actions (what)</li>
              <li>Conditions (when and where)</li>
              <li>Access controls (how)</li>
            </ul>
          </li>
          <li><strong>Conditions and Signals:</strong>
            <ul>
              <li>User and group membership</li>
              <li>IP location and named locations</li>
              <li>Device platform and state</li>
              <li>Application and user risk</li>
            </ul>
          </li>
          <li><strong>Access Controls:</strong>
            <ul>
              <li>Block or grant access</li>
              <li>Require multi-factor authentication</li>
              <li>Require compliant or hybrid Azure AD joined device</li>
              <li>Require approved client app</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Conditional Access Scenarios</h3>
        <ul>
          <li><strong>Location-Based Access:</strong>
            <ul>
              <li>Block access from untrusted locations</li>
              <li>Require MFA for external access</li>
              <li>Allow access only from corporate networks</li>
              <li>Country/region-based restrictions</li>
            </ul>
          </li>
          <li><strong>Device-Based Access:</strong>
            <ul>
              <li>Require device compliance</li>
              <li>Block access from unmanaged devices</li>
              <li>Platform-specific policies</li>
              <li>Device registration requirements</li>
            </ul>
          </li>
          <li><strong>Application Protection:</strong>
            <ul>
              <li>High-value application protection</li>
              <li>App-specific access requirements</li>
              <li>Session controls and monitoring</li>
              <li>Data loss prevention integration</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure AD Identity Protection</h3>
        <ul>
          <li><strong>Risk Detection:</strong>
            <ul>
              <li>Sign-in risk detection</li>
              <li>User risk detection</li>
              <li>Anonymous IP usage</li>
              <li>Impossible travel scenarios</li>
            </ul>
          </li>
          <li><strong>Risk-Based Policies:</strong>
            <ul>
              <li>User risk policy automation</li>
              <li>Sign-in risk policy enforcement</li>
              <li>MFA registration policy</li>
              <li>Risk remediation workflows</li>
            </ul>
          </li>
          <li><strong>Investigation and Remediation:</strong>
            <ul>
              <li>Risk event investigation</li>
              <li>User risk confirmation</li>
              <li>Automated remediation actions</li>
              <li>Risk reporting and analytics</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Azure AD Advanced Security Features",
      content: `
        <h2>Azure AD Advanced Security and Management Features</h2>
        <p>Advanced Azure AD security features provide comprehensive identity protection, privileged access management, and governance capabilities for enterprise environments.</p>
        
        <h3>Privileged Identity Management (PIM)</h3>
        <ul>
          <li><strong>Just-in-Time Access:</strong>
            <ul>
              <li>Time-bound privileged role assignments</li>
              <li>Activation workflows and approvals</li>
              <li>Multi-factor authentication requirements</li>
              <li>Business justification and audit trails</li>
            </ul>
          </li>
          <li><strong>Privileged Role Management:</strong>
            <ul>
              <li>Azure AD role management</li>
              <li>Azure resource role management</li>
              <li>Custom role definitions</li>
              <li>Role assignment reviews</li>
            </ul>
          </li>
          <li><strong>Access Reviews and Governance:</strong>
            <ul>
              <li>Periodic access reviews</li>
              <li>Automated review workflows</li>
              <li>Guest user access reviews</li>
              <li>Compliance reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure AD B2B and B2C</h3>
        <ul>
          <li><strong>B2B Collaboration:</strong>
            <ul>
              <li>External user invitations</li>
              <li>Guest user management</li>
              <li>Cross-tenant collaboration</li>
              <li>External identity providers</li>
            </ul>
          </li>
          <li><strong>B2C Customer Identity:</strong>
            <ul>
              <li>Customer identity management</li>
              <li>Social identity providers</li>
              <li>Custom user journeys</li>
              <li>API-driven identity experiences</li>
            </ul>
          </li>
          <li><strong>Security Considerations:</strong>
            <ul>
              <li>Guest user access controls</li>
              <li>External collaboration policies</li>
              <li>Data sharing governance</li>
              <li>Cross-tenant security monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure AD Monitoring and Reporting</h3>
        <ul>
          <li><strong>Sign-in and Audit Logs:</strong>
            <ul>
              <li>Interactive and non-interactive sign-ins</li>
              <li>Directory activity auditing</li>
              <li>Provisioning activity logs</li>
              <li>Risk detection events</li>
            </ul>
          </li>
          <li><strong>Security Reports:</strong>
            <ul>
              <li>Users flagged for risk</li>
              <li>Risky sign-ins analysis</li>
              <li>Security usage analytics</li>
              <li>Authentication methods usage</li>
            </ul>
          </li>
          <li><strong>Integration and Automation:</strong>
            <ul>
              <li>Azure Monitor integration</li>
              <li>SIEM connector and APIs</li>
              <li>PowerShell and Graph API</li>
              <li>Custom reporting solutions</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which Azure AD feature provides time-bound privileged role assignments with approval workflows?",
            options: [
              "Conditional Access",
              "Identity Protection",
              "Privileged Identity Management (PIM)",
              "Access Reviews"
            ],
            correctAnswer: 2,
            explanation: "Privileged Identity Management (PIM) provides just-in-time access with time-bound privileged role assignments, activation workflows, approvals, and comprehensive audit trails for privileged access governance."
          },
          {
            question: "What is the primary purpose of Azure AD Conditional Access policies?",
            options: [
              "User provisioning automation",
              "Intelligent access control based on user, device, location, and application context",
              "Password complexity enforcement",
              "Application development"
            ],
            correctAnswer: 1,
            explanation: "Azure AD Conditional Access provides intelligent access control decisions based on signals like user identity, device state, location, and application context to enforce security policies and protect resources."
          },
          {
            question: "Which Azure AD edition is required for Identity Protection and risk-based policies?",
            options: [
              "Azure AD Free",
              "Azure AD Premium P1",
              "Azure AD Premium P2",
              "All editions include Identity Protection"
            ],
            correctAnswer: 2,
            explanation: "Azure AD Premium P2 is required for Identity Protection features, including risk detection, risk-based policies, and advanced security analytics capabilities."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
