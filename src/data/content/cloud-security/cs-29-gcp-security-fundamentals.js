/**
 * GCP Security Fundamentals Module
 */

export const gcpSecurityFundamentalsContent = {
  id: "cs-29",
  pathId: "cloud-security",
  title: "GCP Security Fundamentals",
  description: "Master Google Cloud Platform security fundamentals, including GCP security architecture, shared responsibility model, and core security services for comprehensive GCP protection.",
  objectives: [
    "Understand GCP security architecture and shared responsibility",
    "Learn GCP security services and tools",
    "Master GCP project and resource security",
    "Develop skills in GCP security monitoring",
    "Learn GCP compliance and governance",
    "Implement comprehensive GCP security strategies"
  ],
  difficulty: "Intermediate",
  estimatedTime: 130,
  sections: [
    {
      title: "GCP Security Architecture",
      content: `
        <h2>Google Cloud Platform Security Architecture</h2>
        <p>GCP security architecture is built on Google's defense-in-depth approach, zero-trust principles, and a shared responsibility model that leverages Google's global infrastructure security.</p>
        
        <h3>GCP Shared Responsibility Model</h3>
        <ul>
          <li><strong>Google Responsibilities (Security OF the Cloud):</strong>
            <ul>
              <li>Physical infrastructure and data center security</li>
              <li>Hardware and software stack security</li>
              <li>Network infrastructure and global backbone</li>
              <li>Service infrastructure and platform security</li>
            </ul>
          </li>
          <li><strong>Customer Responsibilities (Security IN the Cloud):</strong>
            <ul>
              <li>Identity and access management configuration</li>
              <li>Data classification and protection</li>
              <li>Application and workload security</li>
              <li>Network configuration and firewall rules</li>
            </ul>
          </li>
          <li><strong>Shared Responsibilities:</strong>
            <ul>
              <li>Operating system configuration (varies by service)</li>
              <li>Network controls and traffic protection</li>
              <li>Platform and application-level access controls</li>
              <li>Data encryption and key management</li>
            </ul>
          </li>
        </ul>
        
        <h3>Google's Security Infrastructure</h3>
        <ul>
          <li><strong>Global Infrastructure Security:</strong>
            <ul>
              <li>Custom-designed data centers</li>
              <li>Multi-layered physical security</li>
              <li>Custom security chips (Titan)</li>
              <li>Secure boot and hardware attestation</li>
            </ul>
          </li>
          <li><strong>Network Security:</strong>
            <ul>
              <li>Private global network backbone</li>
              <li>Encryption in transit by default</li>
              <li>DDoS protection and mitigation</li>
              <li>Network isolation and micro-segmentation</li>
            </ul>
          </li>
          <li><strong>Data Protection:</strong>
            <ul>
              <li>Encryption at rest by default</li>
              <li>Customer-managed encryption keys (CMEK)</li>
              <li>Customer-supplied encryption keys (CSEK)</li>
              <li>External key management (EKM)</li>
            </ul>
          </li>
        </ul>
        
        <h3>GCP Security Services Overview</h3>
        <ul>
          <li><strong>Identity and Access Management:</strong>
            <ul>
              <li>Cloud Identity and Access Management (IAM)</li>
              <li>Cloud Identity for workforce identity</li>
              <li>Identity-Aware Proxy (IAP)</li>
              <li>Cloud Asset Inventory</li>
            </ul>
          </li>
          <li><strong>Security Management and Monitoring:</strong>
            <ul>
              <li>Security Command Center (SCC)</li>
              <li>Cloud Security Scanner</li>
              <li>Cloud Logging and Cloud Monitoring</li>
              <li>Binary Authorization</li>
            </ul>
          </li>
          <li><strong>Data Protection and Privacy:</strong>
            <ul>
              <li>Cloud Key Management Service (KMS)</li>
              <li>Cloud Data Loss Prevention (DLP)</li>
              <li>Secret Manager</li>
              <li>VPC Service Controls</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "GCP Project and Resource Security",
      content: `
        <h2>GCP Project and Resource Security Management</h2>
        <p>Securing GCP projects and resources requires implementing proper organization hierarchy, resource management, and security controls across the GCP resource hierarchy.</p>
        
        <h3>GCP Resource Hierarchy</h3>
        <ul>
          <li><strong>Organization Level:</strong>
            <ul>
              <li>Root node for enterprise resources</li>
              <li>Organization-wide policies and controls</li>
              <li>Centralized billing and administration</li>
              <li>Domain verification and ownership</li>
            </ul>
          </li>
          <li><strong>Folder Level:</strong>
            <ul>
              <li>Grouping and organizing projects</li>
              <li>Department or team-based organization</li>
              <li>Policy inheritance and delegation</li>
              <li>Environment-based separation</li>
            </ul>
          </li>
          <li><strong>Project Level:</strong>
            <ul>
              <li>Resource container and billing boundary</li>
              <li>API enablement and quotas</li>
              <li>Service accounts and credentials</li>
              <li>Network and security configurations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Resource Manager and Security</h3>
        <ul>
          <li><strong>Project Security Configuration:</strong>
            <ul>
              <li>Project-level IAM policies</li>
              <li>Service account management</li>
              <li>API access controls</li>
              <li>Resource quotas and limits</li>
            </ul>
          </li>
          <li><strong>Organization Policies:</strong>
            <ul>
              <li>Constraint-based governance</li>
              <li>Resource creation restrictions</li>
              <li>Service usage limitations</li>
              <li>Compliance enforcement</li>
            </ul>
          </li>
          <li><strong>Resource Labeling and Tagging:</strong>
            <ul>
              <li>Resource organization and categorization</li>
              <li>Cost allocation and tracking</li>
              <li>Security policy application</li>
              <li>Compliance and governance</li>
            </ul>
          </li>
        </ul>
        
        <h3>GCP Security Best Practices</h3>
        <ul>
          <li><strong>Principle of Least Privilege:</strong>
            <ul>
              <li>Minimal required permissions</li>
              <li>Role-based access control</li>
              <li>Regular access reviews</li>
              <li>Temporary access patterns</li>
            </ul>
          </li>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple security layers</li>
              <li>Network and application security</li>
              <li>Data protection and encryption</li>
              <li>Monitoring and detection</li>
            </ul>
          </li>
          <li><strong>Security by Default:</strong>
            <ul>
              <li>Secure default configurations</li>
              <li>Automatic security updates</li>
              <li>Built-in encryption and protection</li>
              <li>Proactive threat detection</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "GCP Security Monitoring and Compliance",
      content: `
        <h2>GCP Security Monitoring and Compliance Framework</h2>
        <p>Comprehensive security monitoring and compliance in GCP requires leveraging Security Command Center, Cloud Logging, and other monitoring services for continuous security assessment.</p>
        
        <h3>Security Command Center (SCC)</h3>
        <ul>
          <li><strong>Security Posture Management:</strong>
            <ul>
              <li>Centralized security dashboard</li>
              <li>Asset discovery and inventory</li>
              <li>Security findings aggregation</li>
              <li>Risk assessment and scoring</li>
            </ul>
          </li>
          <li><strong>Threat Detection:</strong>
            <ul>
              <li>Built-in security detectors</li>
              <li>Anomaly detection</li>
              <li>Threat intelligence integration</li>
              <li>Custom finding sources</li>
            </ul>
          </li>
          <li><strong>Security Analytics:</strong>
            <ul>
              <li>Finding correlation and analysis</li>
              <li>Security insights and recommendations</li>
              <li>Compliance monitoring</li>
              <li>Executive reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Logging and Monitoring</h3>
        <ul>
          <li><strong>Audit Logging:</strong>
            <ul>
              <li>Admin activity logs</li>
              <li>Data access logs</li>
              <li>System event logs</li>
              <li>Policy violation logs</li>
            </ul>
          </li>
          <li><strong>Security Monitoring:</strong>
            <ul>
              <li>Real-time log analysis</li>
              <li>Custom alerting policies</li>
              <li>Anomaly detection</li>
              <li>Incident response triggers</li>
            </ul>
          </li>
          <li><strong>Compliance Reporting:</strong>
            <ul>
              <li>Audit trail preservation</li>
              <li>Compliance dashboard</li>
              <li>Regulatory reporting</li>
              <li>Evidence collection</li>
            </ul>
          </li>
        </ul>
        
        <h3>GCP Compliance and Certifications</h3>
        <ul>
          <li><strong>Global Compliance Standards:</strong>
            <ul>
              <li>SOC 1, 2, and 3 compliance</li>
              <li>ISO 27001, 27017, 27018</li>
              <li>PCI DSS certification</li>
              <li>FedRAMP authorization</li>
            </ul>
          </li>
          <li><strong>Regional Compliance:</strong>
            <ul>
              <li>GDPR compliance and data residency</li>
              <li>HIPAA and HITECH compliance</li>
              <li>Local data protection laws</li>
              <li>Industry-specific requirements</li>
            </ul>
          </li>
          <li><strong>Compliance Tools:</strong>
            <ul>
              <li>Compliance reports and documentation</li>
              <li>Risk assessment frameworks</li>
              <li>Audit support and evidence</li>
              <li>Continuous compliance monitoring</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "In the GCP Shared Responsibility Model, who is responsible for configuring Identity and Access Management (IAM) policies?",
            options: [
              "Google is responsible",
              "The customer is responsible",
              "Both Google and customer share responsibility",
              "It depends on the service type"
            ],
            correctAnswer: 1,
            explanation: "The customer is responsible for configuring IAM policies as part of their security responsibilities 'in the cloud', while Google handles the underlying infrastructure and platform security."
          },
          {
            question: "Which GCP service provides centralized security posture management and threat detection?",
            options: [
              "Cloud IAM",
              "Cloud Logging",
              "Security Command Center (SCC)",
              "Cloud Monitoring"
            ],
            correctAnswer: 2,
            explanation: "Security Command Center (SCC) provides centralized security posture management, asset discovery, threat detection, and security findings aggregation across GCP resources."
          },
          {
            question: "What is Google's custom security chip called that provides hardware-based security?",
            options: [
              "Titan",
              "Secure Element",
              "TPM",
              "HSM"
            ],
            correctAnswer: 0,
            explanation: "Titan is Google's custom-designed security chip that provides hardware-based security features including secure boot, hardware attestation, and cryptographic operations."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
