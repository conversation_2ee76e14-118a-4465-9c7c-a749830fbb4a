/**
 * AWS Compliance Module
 */

export const awsComplianceContent = {
  id: "cs-23",
  pathId: "cloud-security",
  title: "AWS Compliance",
  description: "Master AWS compliance frameworks, regulatory requirements, and governance tools to ensure adherence to industry standards and legal obligations in AWS environments.",
  objectives: [
    "Understand AWS compliance programs and certifications",
    "Learn regulatory compliance implementation in AWS",
    "Master AWS governance and audit tools",
    "Develop skills in compliance automation",
    "Learn industry-specific compliance requirements",
    "Implement comprehensive compliance strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 135,
  sections: [
    {
      title: "AWS Compliance Framework",
      content: `
        <h2>AWS Compliance Programs and Certifications</h2>
        <p>AWS maintains a comprehensive compliance program with certifications and attestations that help customers meet their regulatory and compliance requirements.</p>
        
        <h3>AWS Global Compliance Certifications</h3>
        <ul>
          <li><strong>Security and Assurance:</strong>
            <ul>
              <li>SOC 1, 2, and 3 (Service Organization Control)</li>
              <li>ISO 27001, 27017, 27018 (Information Security Management)</li>
              <li>CSA STAR (Cloud Security Alliance)</li>
              <li>FedRAMP (Federal Risk and Authorization Management Program)</li>
            </ul>
          </li>
          <li><strong>Industry-Specific Certifications:</strong>
            <ul>
              <li>PCI DSS (Payment Card Industry Data Security Standard)</li>
              <li>HIPAA (Health Insurance Portability and Accountability Act)</li>
              <li>FISMA (Federal Information Security Management Act)</li>
              <li>GDPR (General Data Protection Regulation) compliance</li>
            </ul>
          </li>
          <li><strong>Regional and Local Compliance:</strong>
            <ul>
              <li>UK G-Cloud and Cyber Essentials Plus</li>
              <li>Australia IRAP (Information Security Registered Assessors Program)</li>
              <li>Singapore MTCS (Multi-Tier Cloud Security)</li>
              <li>Japan FISC (Financial Information Systems Center)</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Shared Responsibility for Compliance</h3>
        <ul>
          <li><strong>AWS Responsibilities:</strong>
            <ul>
              <li>Infrastructure compliance and certifications</li>
              <li>Physical security and environmental controls</li>
              <li>Service-level compliance features</li>
              <li>Compliance documentation and reports</li>
            </ul>
          </li>
          <li><strong>Customer Responsibilities:</strong>
            <ul>
              <li>Data classification and protection</li>
              <li>Identity and access management</li>
              <li>Operating system and application compliance</li>
              <li>Network traffic protection</li>
            </ul>
          </li>
          <li><strong>Shared Controls:</strong>
            <ul>
              <li>Patch management (infrastructure vs. guest OS)</li>
              <li>Configuration management</li>
              <li>Awareness and training</li>
              <li>Physical and environmental controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Artifact and Documentation</h3>
        <ul>
          <li><strong>AWS Artifact Portal:</strong>
            <ul>
              <li>On-demand compliance report access</li>
              <li>Security and compliance documentation</li>
              <li>Agreement management (BAA, GDPR DPA)</li>
              <li>Audit artifact repository</li>
            </ul>
          </li>
          <li><strong>Compliance Reports:</strong>
            <ul>
              <li>SOC reports and certifications</li>
              <li>ISO certification documents</li>
              <li>PCI attestation of compliance</li>
              <li>Third-party audit reports</li>
            </ul>
          </li>
          <li><strong>Agreement Management:</strong>
            <ul>
              <li>Business Associate Agreements (BAA)</li>
              <li>Data Processing Agreements (DPA)</li>
              <li>Non-disclosure agreements</li>
              <li>Custom compliance agreements</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Regulatory Compliance Implementation",
      content: `
        <h2>Implementing Regulatory Compliance in AWS</h2>
        <p>Implementing regulatory compliance in AWS requires understanding specific requirements and leveraging AWS services and features to meet compliance obligations.</p>
        
        <h3>GDPR Compliance in AWS</h3>
        <ul>
          <li><strong>Data Protection Requirements:</strong>
            <ul>
              <li>Data encryption at rest and in transit</li>
              <li>Data minimization and purpose limitation</li>
              <li>Right to erasure (right to be forgotten)</li>
              <li>Data portability and access rights</li>
            </ul>
          </li>
          <li><strong>AWS GDPR Features:</strong>
            <ul>
              <li>Data Processing Agreement (DPA)</li>
              <li>EU data residency options</li>
              <li>Encryption and key management</li>
              <li>Audit logging and monitoring</li>
            </ul>
          </li>
          <li><strong>Implementation Strategies:</strong>
            <ul>
              <li>Data classification and mapping</li>
              <li>Privacy by design principles</li>
              <li>Consent management systems</li>
              <li>Data breach notification procedures</li>
            </ul>
          </li>
        </ul>
        
        <h3>HIPAA Compliance in AWS</h3>
        <ul>
          <li><strong>HIPAA Requirements:</strong>
            <ul>
              <li>Administrative safeguards</li>
              <li>Physical safeguards</li>
              <li>Technical safeguards</li>
              <li>Business Associate Agreement (BAA)</li>
            </ul>
          </li>
          <li><strong>HIPAA-Eligible AWS Services:</strong>
            <ul>
              <li>EC2, EBS, S3, RDS</li>
              <li>Lambda, API Gateway</li>
              <li>CloudTrail, CloudWatch</li>
              <li>KMS, IAM, VPC</li>
            </ul>
          </li>
          <li><strong>Implementation Best Practices:</strong>
            <ul>
              <li>Encryption of PHI at rest and in transit</li>
              <li>Access controls and audit logging</li>
              <li>Network isolation and segmentation</li>
              <li>Incident response procedures</li>
            </ul>
          </li>
        </ul>
        
        <h3>PCI DSS Compliance in AWS</h3>
        <ul>
          <li><strong>PCI DSS Requirements:</strong>
            <ul>
              <li>Build and maintain secure networks</li>
              <li>Protect cardholder data</li>
              <li>Maintain vulnerability management</li>
              <li>Implement strong access controls</li>
            </ul>
          </li>
          <li><strong>AWS PCI DSS Features:</strong>
            <ul>
              <li>Level 1 Service Provider certification</li>
              <li>Secure network architecture</li>
              <li>Encryption and tokenization</li>
              <li>Logging and monitoring capabilities</li>
            </ul>
          </li>
          <li><strong>Compliance Implementation:</strong>
            <ul>
              <li>Cardholder Data Environment (CDE) design</li>
              <li>Network segmentation strategies</li>
              <li>Vulnerability scanning and testing</li>
              <li>Compensating controls documentation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "AWS Governance and Compliance Automation",
      content: `
        <h2>AWS Governance Tools and Compliance Automation</h2>
        <p>AWS provides comprehensive governance tools and automation capabilities to help organizations maintain continuous compliance and implement effective governance frameworks.</p>
        
        <h3>AWS Config for Compliance</h3>
        <ul>
          <li><strong>Configuration Management:</strong>
            <ul>
              <li>Resource configuration tracking</li>
              <li>Configuration change history</li>
              <li>Compliance rule evaluation</li>
              <li>Resource relationship mapping</li>
            </ul>
          </li>
          <li><strong>Compliance Rules:</strong>
            <ul>
              <li>AWS managed compliance rules</li>
              <li>Custom compliance rules</li>
              <li>Conformance packs for standards</li>
              <li>Multi-account compliance aggregation</li>
            </ul>
          </li>
          <li><strong>Remediation Automation:</strong>
            <ul>
              <li>Automatic remediation actions</li>
              <li>Systems Manager automation</li>
              <li>Lambda-based custom remediation</li>
              <li>Manual remediation workflows</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Organizations and SCPs</h3>
        <ul>
          <li><strong>Organizational Controls:</strong>
            <ul>
              <li>Service Control Policies (SCPs)</li>
              <li>Account-level restrictions</li>
              <li>Organizational unit management</li>
              <li>Consolidated billing and cost control</li>
            </ul>
          </li>
          <li><strong>Governance Policies:</strong>
            <ul>
              <li>Preventive controls implementation</li>
              <li>Service usage restrictions</li>
              <li>Regional access limitations</li>
              <li>Resource creation policies</li>
            </ul>
          </li>
          <li><strong>Compliance Enforcement:</strong>
            <ul>
              <li>Mandatory security baselines</li>
              <li>Compliance guardrails</li>
              <li>Policy inheritance and delegation</li>
              <li>Exception management processes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Continuous Compliance Monitoring</h3>
        <ul>
          <li><strong>Automated Assessment:</strong>
            <ul>
              <li>Continuous compliance checking</li>
              <li>Real-time violation detection</li>
              <li>Compliance dashboard and reporting</li>
              <li>Trend analysis and metrics</li>
            </ul>
          </li>
          <li><strong>Audit and Reporting:</strong>
            <ul>
              <li>Compliance report generation</li>
              <li>Evidence collection automation</li>
              <li>Audit trail maintenance</li>
              <li>Regulatory reporting automation</li>
            </ul>
          </li>
          <li><strong>Integration and Orchestration:</strong>
            <ul>
              <li>Third-party GRC tool integration</li>
              <li>SIEM and security tool connectivity</li>
              <li>Workflow automation</li>
              <li>Custom compliance frameworks</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which AWS service provides on-demand access to compliance reports and certifications?",
            options: [
              "AWS Config",
              "AWS Artifact",
              "AWS Security Hub",
              "AWS CloudTrail"
            ],
            correctAnswer: 1,
            explanation: "AWS Artifact provides on-demand access to AWS compliance reports, certifications, and agreements, serving as a central repository for compliance documentation."
          },
          {
            question: "For HIPAA compliance in AWS, what is required from AWS as the cloud service provider?",
            options: [
              "AWS automatically ensures HIPAA compliance for all services",
              "AWS provides a Business Associate Agreement (BAA) and HIPAA-eligible services",
              "AWS is not involved in HIPAA compliance",
              "AWS only provides technical safeguards"
            ],
            correctAnswer: 1,
            explanation: "For HIPAA compliance, AWS provides a Business Associate Agreement (BAA) and offers HIPAA-eligible services, but customers are responsible for implementing proper safeguards and controls for their PHI."
          },
          {
            question: "What is the primary purpose of Service Control Policies (SCPs) in AWS Organizations?",
            options: [
              "To grant additional permissions to accounts",
              "To set maximum permissions and implement guardrails across accounts",
              "To manage billing and cost allocation",
              "To automate resource provisioning"
            ],
            correctAnswer: 1,
            explanation: "Service Control Policies (SCPs) set maximum permissions and implement guardrails across AWS accounts in an organization, acting as preventive controls to ensure compliance with organizational policies."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
