/**
 * Cloud Storage Security Module
 */

export const cloudStorageSecurityContent = {
  id: "cs-6",
  pathId: "cloud-security",
  title: "Cloud Storage Security",
  description: "Master cloud storage security including encryption, access controls, data classification, backup strategies, and compliance requirements across different storage types.",
  objectives: [
    "Understand cloud storage types and security implications",
    "Learn data encryption strategies for cloud storage",
    "Master access controls and permissions for storage resources",
    "Implement data classification and lifecycle management",
    "Understand backup, recovery, and disaster recovery strategies",
    "Learn compliance and regulatory requirements for cloud storage"
  ],
  difficulty: "Intermediate",
  estimatedTime: 110,
  sections: [
    {
      title: "Cloud Storage Fundamentals",
      content: `
        <h2>Cloud Storage Security Fundamentals</h2>
        <p>Cloud storage security involves protecting data at rest, controlling access, and ensuring compliance across various storage services and deployment models.</p>
        
        <h3>Types of Cloud Storage</h3>
        <ul>
          <li><strong>Object Storage:</strong>
            <ul>
              <li>Scalable storage for unstructured data</li>
              <li>REST API access and web-based interfaces</li>
              <li>Examples: Amazon S3, Azure Blob Storage, Google Cloud Storage</li>
              <li>Ideal for backups, archives, and content distribution</li>
            </ul>
          </li>
          <li><strong>Block Storage:</strong>
            <ul>
              <li>High-performance storage for databases and file systems</li>
              <li>Raw block-level access</li>
              <li>Examples: Amazon EBS, Azure Disk Storage, Google Persistent Disk</li>
              <li>Attached to virtual machines and containers</li>
            </ul>
          </li>
          <li><strong>File Storage:</strong>
            <ul>
              <li>Shared file systems accessible over network protocols</li>
              <li>NFS, SMB/CIFS protocol support</li>
              <li>Examples: Amazon EFS, Azure Files, Google Filestore</li>
              <li>Multi-instance access and collaboration</li>
            </ul>
          </li>
          <li><strong>Database Storage:</strong>
            <ul>
              <li>Managed database services with built-in storage</li>
              <li>Relational and NoSQL database options</li>
              <li>Examples: Amazon RDS, Azure SQL Database, Google Cloud SQL</li>
              <li>Automated backup and high availability</li>
            </ul>
          </li>
        </ul>
        
        <h3>Storage Security Challenges</h3>
        <ul>
          <li><strong>Data Exposure Risks:</strong>
            <ul>
              <li>Misconfigured access permissions</li>
              <li>Public bucket exposures</li>
              <li>Inadequate encryption implementation</li>
              <li>Weak authentication mechanisms</li>
            </ul>
          </li>
          <li><strong>Compliance and Governance:</strong>
            <ul>
              <li>Data residency and sovereignty requirements</li>
              <li>Regulatory compliance (GDPR, HIPAA, SOX)</li>
              <li>Data retention and deletion policies</li>
              <li>Audit trails and access logging</li>
            </ul>
          </li>
          <li><strong>Operational Security:</strong>
            <ul>
              <li>Key management and rotation</li>
              <li>Backup and disaster recovery</li>
              <li>Performance and availability</li>
              <li>Cost optimization and monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Design Principles</h3>
        <ul>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple layers of security controls</li>
              <li>Network, application, and data-level protection</li>
              <li>Redundant security mechanisms</li>
              <li>Fail-safe defaults and error handling</li>
            </ul>
          </li>
          <li><strong>Least Privilege Access:</strong>
            <ul>
              <li>Minimal necessary permissions</li>
              <li>Role-based access control</li>
              <li>Regular access reviews and audits</li>
              <li>Temporary and just-in-time access</li>
            </ul>
          </li>
          <li><strong>Data Classification:</strong>
            <ul>
              <li>Sensitivity-based data categorization</li>
              <li>Appropriate security controls per classification</li>
              <li>Automated classification and tagging</li>
              <li>Lifecycle management policies</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Encryption and Key Management",
      content: `
        <h2>Cloud Storage Encryption and Key Management</h2>
        <p>Encryption is fundamental to cloud storage security, protecting data confidentiality and integrity both at rest and in transit.</p>
        
        <h3>Encryption at Rest</h3>
        <ul>
          <li><strong>Server-Side Encryption (SSE):</strong>
            <ul>
              <li>Encryption performed by the cloud storage service</li>
              <li>Transparent to applications and users</li>
              <li>Provider-managed or customer-managed keys</li>
              <li>Hardware security module (HSM) integration</li>
            </ul>
          </li>
          <li><strong>Client-Side Encryption:</strong>
            <ul>
              <li>Encryption performed before uploading to cloud</li>
              <li>Customer maintains full control of keys</li>
              <li>End-to-end encryption protection</li>
              <li>Application-level implementation required</li>
            </ul>
          </li>
          <li><strong>Envelope Encryption:</strong>
            <ul>
              <li>Data encrypted with data encryption keys (DEKs)</li>
              <li>DEKs encrypted with key encryption keys (KEKs)</li>
              <li>Hierarchical key management structure</li>
              <li>Performance and scalability benefits</li>
            </ul>
          </li>
        </ul>
        
        <h3>Key Management Strategies</h3>
        <ul>
          <li><strong>Cloud Provider Key Management:</strong>
            <ul>
              <li>Fully managed key lifecycle</li>
              <li>Automatic key rotation</li>
              <li>High availability and durability</li>
              <li>Integration with cloud services</li>
            </ul>
          </li>
          <li><strong>Customer-Managed Keys:</strong>
            <ul>
              <li>Customer control over key policies</li>
              <li>Custom key rotation schedules</li>
              <li>Audit trails and access logging</li>
              <li>Cross-service key usage</li>
            </ul>
          </li>
          <li><strong>Hybrid Key Management:</strong>
            <ul>
              <li>On-premises key generation</li>
              <li>Cloud-based key storage and management</li>
              <li>Hardware security module integration</li>
              <li>Compliance with regulatory requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>Encryption Best Practices</h3>
        <ul>
          <li><strong>Algorithm Selection:</strong>
            <ul>
              <li>Use strong encryption algorithms (AES-256)</li>
              <li>Avoid deprecated or weak ciphers</li>
              <li>Regular algorithm review and updates</li>
              <li>Compliance with industry standards</li>
            </ul>
          </li>
          <li><strong>Key Lifecycle Management:</strong>
            <ul>
              <li>Secure key generation and distribution</li>
              <li>Regular key rotation policies</li>
              <li>Secure key storage and backup</li>
              <li>Proper key destruction procedures</li>
            </ul>
          </li>
          <li><strong>Access Control and Auditing:</strong>
            <ul>
              <li>Strict access controls for encryption keys</li>
              <li>Separation of duties for key management</li>
              <li>Comprehensive audit logging</li>
              <li>Regular security assessments</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Access Controls and Data Governance",
      content: `
        <h2>Storage Access Controls and Data Governance</h2>
        <p>Implementing robust access controls and data governance ensures that only authorized users can access sensitive data and that data handling complies with organizational policies and regulations.</p>
        
        <h3>Access Control Models</h3>
        <ul>
          <li><strong>Identity-Based Access Control:</strong>
            <ul>
              <li>User and service account permissions</li>
              <li>Role-based access control (RBAC)</li>
              <li>Attribute-based access control (ABAC)</li>
              <li>Multi-factor authentication requirements</li>
            </ul>
          </li>
          <li><strong>Resource-Based Access Control:</strong>
            <ul>
              <li>Bucket and object-level policies</li>
              <li>Cross-account access permissions</li>
              <li>Conditional access based on context</li>
              <li>Time-based and location-based restrictions</li>
            </ul>
          </li>
          <li><strong>Network-Based Access Control:</strong>
            <ul>
              <li>VPC endpoint restrictions</li>
              <li>IP address and CIDR block filtering</li>
              <li>Private network access only</li>
              <li>VPN and direct connect requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Classification and Labeling</h3>
        <ul>
          <li><strong>Classification Schemes:</strong>
            <ul>
              <li>Public, Internal, Confidential, Restricted</li>
              <li>Regulatory classification (PII, PHI, PCI)</li>
              <li>Business impact-based classification</li>
              <li>Custom organizational categories</li>
            </ul>
          </li>
          <li><strong>Automated Classification:</strong>
            <ul>
              <li>Content-based classification algorithms</li>
              <li>Machine learning pattern recognition</li>
              <li>Regular expression and keyword matching</li>
              <li>Integration with data loss prevention (DLP)</li>
            </ul>
          </li>
          <li><strong>Metadata and Tagging:</strong>
            <ul>
              <li>Consistent tagging strategies</li>
              <li>Automated tag application</li>
              <li>Tag-based access controls</li>
              <li>Compliance and audit reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Lifecycle Management</h3>
        <ul>
          <li><strong>Lifecycle Policies:</strong>
            <ul>
              <li>Automated data tiering and archiving</li>
              <li>Retention period enforcement</li>
              <li>Deletion and purging schedules</li>
              <li>Cost optimization strategies</li>
            </ul>
          </li>
          <li><strong>Backup and Recovery:</strong>
            <ul>
              <li>Regular backup schedules</li>
              <li>Cross-region backup replication</li>
              <li>Point-in-time recovery capabilities</li>
              <li>Backup encryption and access controls</li>
            </ul>
          </li>
          <li><strong>Compliance and Legal Hold:</strong>
            <ul>
              <li>Legal hold and litigation support</li>
              <li>Regulatory retention requirements</li>
              <li>Data subject rights (GDPR)</li>
              <li>Audit trail preservation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary advantage of client-side encryption over server-side encryption?",
            options: [
              "Better performance",
              "Lower cost",
              "Customer maintains full control of encryption keys",
              "Easier implementation"
            ],
            correctAnswer: 2,
            explanation: "Client-side encryption allows customers to maintain full control over encryption keys and provides end-to-end encryption, ensuring data is encrypted before it leaves the customer's environment."
          },
          {
            question: "Which access control model allows permissions to be granted based on multiple attributes like user role, time of access, and location?",
            options: [
              "Role-Based Access Control (RBAC)",
              "Discretionary Access Control (DAC)",
              "Attribute-Based Access Control (ABAC)",
              "Mandatory Access Control (MAC)"
            ],
            correctAnswer: 2,
            explanation: "Attribute-Based Access Control (ABAC) allows for fine-grained access control based on multiple attributes including user attributes, resource attributes, and environmental conditions."
          },
          {
            question: "What is envelope encryption in cloud storage?",
            options: [
              "Encrypting data twice with the same key",
              "Using data encryption keys (DEKs) encrypted by key encryption keys (KEKs)",
              "Storing encryption keys in envelopes",
              "Encrypting only the metadata"
            ],
            correctAnswer: 1,
            explanation: "Envelope encryption uses a hierarchical approach where data is encrypted with data encryption keys (DEKs), and the DEKs are then encrypted with key encryption keys (KEKs), providing scalability and security benefits."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
