/**
 * Cloud Compute Security Module
 */

export const cloudComputeSecurityContent = {
  id: "cs-7",
  pathId: "cloud-security",
  title: "Cloud Compute Security",
  description: "Master security for cloud compute resources including virtual machines, containers, serverless functions, and orchestration platforms with comprehensive hardening and monitoring strategies.",
  objectives: [
    "Understand cloud compute security fundamentals and threat models",
    "Learn virtual machine security and hardening techniques",
    "Master container and Kubernetes security best practices",
    "Implement serverless security controls and monitoring",
    "Understand compute resource isolation and multi-tenancy",
    "Learn patch management and vulnerability assessment for cloud compute"
  ],
  difficulty: "Intermediate",
  estimatedTime: 115,
  sections: [
    {
      title: "Cloud Compute Security Fundamentals",
      content: `
        <h2>Cloud Compute Security Fundamentals</h2>
        <p>Cloud compute security encompasses protecting virtual machines, containers, serverless functions, and the underlying infrastructure from various threats and vulnerabilities.</p>
        
        <h3>Cloud Compute Models</h3>
        <ul>
          <li><strong>Virtual Machines (VMs):</strong>
            <ul>
              <li>Traditional virtualization in the cloud</li>
              <li>Full operating system control</li>
              <li>Examples: Amazon EC2, Azure VMs, Google Compute Engine</li>
              <li>Customer responsibility for OS and application security</li>
            </ul>
          </li>
          <li><strong>Containers:</strong>
            <ul>
              <li>Lightweight application packaging and deployment</li>
              <li>Shared kernel with host operating system</li>
              <li>Examples: Docker, containerd, CRI-O</li>
              <li>Container runtime and orchestration security</li>
            </ul>
          </li>
          <li><strong>Serverless Functions:</strong>
            <ul>
              <li>Event-driven, stateless compute execution</li>
              <li>Managed runtime environments</li>
              <li>Examples: AWS Lambda, Azure Functions, Google Cloud Functions</li>
              <li>Function code and dependency security</li>
            </ul>
          </li>
          <li><strong>Platform Services:</strong>
            <ul>
              <li>Managed application platforms</li>
              <li>Abstracted infrastructure management</li>
              <li>Examples: AWS Elastic Beanstalk, Azure App Service</li>
              <li>Application-level security focus</li>
            </ul>
          </li>
        </ul>
        
        <h3>Compute Security Threat Model</h3>
        <ul>
          <li><strong>Hypervisor and Infrastructure Threats:</strong>
            <ul>
              <li>Hypervisor escape attacks</li>
              <li>Side-channel attacks</li>
              <li>Resource exhaustion and DoS</li>
              <li>Hardware vulnerabilities (Spectre, Meltdown)</li>
            </ul>
          </li>
          <li><strong>Guest Operating System Threats:</strong>
            <ul>
              <li>Unpatched vulnerabilities</li>
              <li>Malware and rootkits</li>
              <li>Privilege escalation</li>
              <li>Configuration weaknesses</li>
            </ul>
          </li>
          <li><strong>Application and Runtime Threats:</strong>
            <ul>
              <li>Application vulnerabilities</li>
              <li>Dependency and supply chain attacks</li>
              <li>Runtime injection attacks</li>
              <li>Data exposure and leakage</li>
            </ul>
          </li>
          <li><strong>Multi-Tenancy Risks:</strong>
            <ul>
              <li>Tenant isolation failures</li>
              <li>Resource contention attacks</li>
              <li>Information leakage between tenants</li>
              <li>Shared resource vulnerabilities</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Design Principles</h3>
        <ul>
          <li><strong>Immutable Infrastructure:</strong>
            <ul>
              <li>Infrastructure as code (IaC)</li>
              <li>Immutable server patterns</li>
              <li>Configuration drift prevention</li>
              <li>Reproducible deployments</li>
            </ul>
          </li>
          <li><strong>Least Privilege Execution:</strong>
            <ul>
              <li>Minimal runtime permissions</li>
              <li>Non-root container execution</li>
              <li>Service account restrictions</li>
              <li>Resource quota enforcement</li>
            </ul>
          </li>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple security layers</li>
              <li>Host and network-based controls</li>
              <li>Runtime protection mechanisms</li>
              <li>Monitoring and detection systems</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Virtual Machine Security",
      content: `
        <h2>Virtual Machine Security and Hardening</h2>
        <p>Securing virtual machines in the cloud requires comprehensive hardening, monitoring, and maintenance practices to protect against various attack vectors.</p>
        
        <h3>VM Hardening Strategies</h3>
        <ul>
          <li><strong>Operating System Hardening:</strong>
            <ul>
              <li>Minimal OS installation and service reduction</li>
              <li>Security baseline configuration</li>
              <li>Disable unnecessary services and ports</li>
              <li>Secure boot and trusted platform module (TPM)</li>
            </ul>
          </li>
          <li><strong>Access Control Hardening:</strong>
            <ul>
              <li>Strong authentication mechanisms</li>
              <li>SSH key-based authentication</li>
              <li>Multi-factor authentication</li>
              <li>Privileged access management</li>
            </ul>
          </li>
          <li><strong>Network Security:</strong>
            <ul>
              <li>Host-based firewall configuration</li>
              <li>Network segmentation and isolation</li>
              <li>Intrusion detection and prevention</li>
              <li>Encrypted communication channels</li>
            </ul>
          </li>
        </ul>
        
        <h3>Patch Management</h3>
        <ul>
          <li><strong>Automated Patching:</strong>
            <ul>
              <li>Scheduled patch deployment</li>
              <li>Critical security patch prioritization</li>
              <li>Rollback and recovery procedures</li>
              <li>Patch testing and validation</li>
            </ul>
          </li>
          <li><strong>Vulnerability Management:</strong>
            <ul>
              <li>Regular vulnerability scanning</li>
              <li>Risk-based patch prioritization</li>
              <li>Compensating controls for unpatched systems</li>
              <li>Vulnerability lifecycle tracking</li>
            </ul>
          </li>
          <li><strong>Configuration Management:</strong>
            <ul>
              <li>Desired state configuration</li>
              <li>Configuration drift detection</li>
              <li>Automated remediation</li>
              <li>Change management processes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Runtime Protection</h3>
        <ul>
          <li><strong>Endpoint Detection and Response (EDR):</strong>
            <ul>
              <li>Real-time threat detection</li>
              <li>Behavioral analysis and anomaly detection</li>
              <li>Automated response and containment</li>
              <li>Forensic data collection</li>
            </ul>
          </li>
          <li><strong>Anti-Malware Protection:</strong>
            <ul>
              <li>Signature-based detection</li>
              <li>Heuristic and behavioral analysis</li>
              <li>Cloud-based threat intelligence</li>
              <li>Real-time scanning and protection</li>
            </ul>
          </li>
          <li><strong>Application Control:</strong>
            <ul>
              <li>Application whitelisting</li>
              <li>Code integrity verification</li>
              <li>Execution policy enforcement</li>
              <li>Script and macro protection</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Container and Serverless Security",
      content: `
        <h2>Container and Serverless Security</h2>
        <p>Container and serverless technologies introduce unique security challenges that require specialized approaches and tools for effective protection.</p>
        
        <h3>Container Security</h3>
        <ul>
          <li><strong>Image Security:</strong>
            <ul>
              <li>Base image vulnerability scanning</li>
              <li>Trusted registry and image signing</li>
              <li>Minimal base images and distroless containers</li>
              <li>Regular image updates and rebuilds</li>
            </ul>
          </li>
          <li><strong>Runtime Security:</strong>
            <ul>
              <li>Container isolation and sandboxing</li>
              <li>Resource limits and quotas</li>
              <li>Security context and capabilities</li>
              <li>Runtime behavior monitoring</li>
            </ul>
          </li>
          <li><strong>Orchestration Security:</strong>
            <ul>
              <li>Kubernetes security best practices</li>
              <li>Pod security policies and standards</li>
              <li>Network policies and segmentation</li>
              <li>RBAC and service account management</li>
            </ul>
          </li>
        </ul>
        
        <h3>Serverless Security</h3>
        <ul>
          <li><strong>Function Security:</strong>
            <ul>
              <li>Secure coding practices</li>
              <li>Dependency vulnerability management</li>
              <li>Input validation and sanitization</li>
              <li>Output encoding and data protection</li>
            </ul>
          </li>
          <li><strong>Runtime Protection:</strong>
            <ul>
              <li>Function execution monitoring</li>
              <li>Anomaly detection and alerting</li>
              <li>Resource usage monitoring</li>
              <li>Performance and security metrics</li>
            </ul>
          </li>
          <li><strong>Event and Data Security:</strong>
            <ul>
              <li>Event source validation</li>
              <li>Data encryption in transit and at rest</li>
              <li>Secrets management</li>
              <li>API gateway security</li>
            </ul>
          </li>
        </ul>
        
        <h3>DevSecOps Integration</h3>
        <ul>
          <li><strong>Security in CI/CD:</strong>
            <ul>
              <li>Security testing automation</li>
              <li>Static and dynamic analysis</li>
              <li>Dependency scanning</li>
              <li>Security gate enforcement</li>
            </ul>
          </li>
          <li><strong>Infrastructure as Code Security:</strong>
            <ul>
              <li>Template security scanning</li>
              <li>Policy as code implementation</li>
              <li>Configuration validation</li>
              <li>Compliance checking</li>
            </ul>
          </li>
          <li><strong>Continuous Monitoring:</strong>
            <ul>
              <li>Runtime security monitoring</li>
              <li>Compliance drift detection</li>
              <li>Threat intelligence integration</li>
              <li>Incident response automation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary security advantage of immutable infrastructure?",
            options: [
              "Better performance",
              "Lower costs",
              "Prevention of configuration drift and unauthorized changes",
              "Easier management"
            ],
            correctAnswer: 2,
            explanation: "Immutable infrastructure prevents configuration drift and unauthorized changes by replacing rather than modifying infrastructure components, ensuring consistent and secure deployments."
          },
          {
            question: "Which container security practice helps reduce the attack surface?",
            options: [
              "Using the latest container runtime",
              "Running containers as root",
              "Using minimal base images or distroless containers",
              "Disabling all security features"
            ],
            correctAnswer: 2,
            explanation: "Using minimal base images or distroless containers reduces the attack surface by eliminating unnecessary components, tools, and potential vulnerabilities."
          },
          {
            question: "What is a key security consideration for serverless functions?",
            options: [
              "Operating system patching",
              "Dependency vulnerability management",
              "Hardware security",
              "Network firewall configuration"
            ],
            correctAnswer: 1,
            explanation: "In serverless environments, dependency vulnerability management is crucial since developers are responsible for the security of their function code and its dependencies."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
