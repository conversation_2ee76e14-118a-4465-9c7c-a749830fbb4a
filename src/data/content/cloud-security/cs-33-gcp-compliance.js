/**
 * GCP Compliance Module
 */

export const gcpComplianceContent = {
  id: "cs-33",
  pathId: "cloud-security",
  title: "GCP Compliance",
  description: "Master Google Cloud Platform compliance frameworks, regulatory requirements, and governance tools to ensure adherence to industry standards and legal obligations in GCP environments.",
  objectives: [
    "Understand GCP compliance programs and certifications",
    "Learn regulatory compliance implementation in GCP",
    "Master GCP governance and policy management",
    "Develop skills in compliance automation and monitoring",
    "Learn industry-specific compliance requirements",
    "Implement comprehensive GCP compliance strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 135,
  sections: [
    {
      title: "GCP Compliance Framework",
      content: `
        <h2>Google Cloud Compliance Programs and Certifications</h2>
        <p>Google Cloud maintains comprehensive compliance programs with global, regional, and industry-specific certifications to help customers meet their regulatory and compliance requirements.</p>
        
        <h3>Global Compliance Certifications</h3>
        <ul>
          <li><strong>Security and Assurance Standards:</strong>
            <ul>
              <li>SOC 1, 2, and 3 (Service Organization Control)</li>
              <li>ISO 27001, 27017, 27018 (Information Security Management)</li>
              <li>CSA STAR (Cloud Security Alliance)</li>
              <li>FedRAMP (Federal Risk and Authorization Management Program)</li>
            </ul>
          </li>
          <li><strong>Privacy and Data Protection:</strong>
            <ul>
              <li>GDPR (General Data Protection Regulation)</li>
              <li>ISO 27701 (Privacy Information Management)</li>
              <li>Privacy Shield Framework (historical)</li>
              <li>CCPA (California Consumer Privacy Act)</li>
            </ul>
          </li>
          <li><strong>Industry-Specific Standards:</strong>
            <ul>
              <li>HIPAA/HITECH (Healthcare)</li>
              <li>PCI DSS (Payment Card Industry)</li>
              <li>FISMA (Federal Information Security Management)</li>
              <li>FERPA (Family Educational Rights and Privacy)</li>
            </ul>
          </li>
        </ul>
        
        <h3>Regional and Local Compliance</h3>
        <ul>
          <li><strong>European Union:</strong>
            <ul>
              <li>GDPR compliance and data residency</li>
              <li>EU Standard Contractual Clauses</li>
              <li>ENISA (European Network and Information Security Agency)</li>
              <li>National cybersecurity frameworks</li>
            </ul>
          </li>
          <li><strong>United States:</strong>
            <ul>
              <li>FedRAMP High and Moderate</li>
              <li>NIST Cybersecurity Framework</li>
              <li>CJIS (Criminal Justice Information Services)</li>
              <li>IRS 1075 (Internal Revenue Service)</li>
            </ul>
          </li>
          <li><strong>Asia Pacific:</strong>
            <ul>
              <li>Australia IRAP (Information Security Registered Assessors)</li>
              <li>Singapore MTCS (Multi-Tier Cloud Security)</li>
              <li>Japan FISC (Financial Information Systems Center)</li>
              <li>India MeitY (Ministry of Electronics and IT)</li>
            </ul>
          </li>
        </ul>
        
        <h3>Google Cloud Compliance Resources</h3>
        <ul>
          <li><strong>Compliance Resource Center:</strong>
            <ul>
              <li>Compliance offerings and certifications</li>
              <li>Audit reports and attestations</li>
              <li>Compliance guides and whitepapers</li>
              <li>Risk assessment tools</li>
            </ul>
          </li>
          <li><strong>Compliance Documentation:</strong>
            <ul>
              <li>Data Processing and Security Terms (DPST)</li>
              <li>Business Associate Agreements (BAA)</li>
              <li>Standard contractual clauses</li>
              <li>Compliance mapping guides</li>
            </ul>
          </li>
          <li><strong>Third-Party Assessments:</strong>
            <ul>
              <li>Independent audit reports</li>
              <li>Penetration testing results</li>
              <li>Vulnerability assessments</li>
              <li>Compliance validation reports</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "GCP Governance and Policy Management",
      content: `
        <h2>GCP Governance and Organization Policy Framework</h2>
        <p>GCP governance and organization policies provide centralized control and compliance enforcement across Google Cloud resources and projects.</p>
        
        <h3>Organization Policy Service</h3>
        <ul>
          <li><strong>Policy Constraints:</strong>
            <ul>
              <li>Boolean constraints (enable/disable)</li>
              <li>List constraints (allow/deny values)</li>
              <li>Custom constraints (CEL expressions)</li>
              <li>Inheritance and override behavior</li>
            </ul>
          </li>
          <li><strong>Policy Hierarchy:</strong>
            <ul>
              <li>Organization-level policies</li>
              <li>Folder-level inheritance</li>
              <li>Project-level overrides</li>
              <li>Resource-level enforcement</li>
            </ul>
          </li>
          <li><strong>Common Policy Use Cases:</strong>
            <ul>
              <li>Resource location restrictions</li>
              <li>Service usage limitations</li>
              <li>Network security enforcement</li>
              <li>Data protection requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>Resource Manager and Governance</h3>
        <ul>
          <li><strong>Resource Hierarchy Management:</strong>
            <ul>
              <li>Organization structure design</li>
              <li>Folder-based organization</li>
              <li>Project lifecycle management</li>
              <li>Resource labeling and tagging</li>
            </ul>
          </li>
          <li><strong>IAM and Access Control:</strong>
            <ul>
              <li>Hierarchical permission inheritance</li>
              <li>Role-based access control</li>
              <li>Service account management</li>
              <li>Conditional access policies</li>
            </ul>
          </li>
          <li><strong>Billing and Cost Management:</strong>
            <ul>
              <li>Billing account organization</li>
              <li>Budget controls and alerts</li>
              <li>Cost allocation and tracking</li>
              <li>Resource quota management</li>
            </ul>
          </li>
        </ul>
        
        <h3>Compliance Monitoring and Automation</h3>
        <ul>
          <li><strong>Security Command Center Compliance:</strong>
            <ul>
              <li>Compliance posture assessment</li>
              <li>Policy violation detection</li>
              <li>Continuous compliance monitoring</li>
              <li>Compliance dashboard and reporting</li>
            </ul>
          </li>
          <li><strong>Cloud Asset Inventory:</strong>
            <ul>
              <li>Resource discovery and tracking</li>
              <li>Configuration change monitoring</li>
              <li>Policy compliance assessment</li>
              <li>Historical data analysis</li>
            </ul>
          </li>
          <li><strong>Automated Remediation:</strong>
            <ul>
              <li>Cloud Functions-based automation</li>
              <li>Policy violation response</li>
              <li>Resource configuration correction</li>
              <li>Compliance workflow automation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Regulatory Compliance Implementation",
      content: `
        <h2>Implementing Regulatory Compliance in GCP</h2>
        <p>Implementing regulatory compliance in GCP requires understanding specific requirements and leveraging GCP services and features to meet compliance obligations effectively.</p>
        
        <h3>GDPR Compliance in GCP</h3>
        <ul>
          <li><strong>Data Protection Principles:</strong>
            <ul>
              <li>Lawfulness, fairness, and transparency</li>
              <li>Purpose limitation and data minimization</li>
              <li>Accuracy and storage limitation</li>
              <li>Integrity, confidentiality, and accountability</li>
            </ul>
          </li>
          <li><strong>GCP GDPR Features:</strong>
            <ul>
              <li>Data Processing and Security Terms (DPST)</li>
              <li>EU data residency options</li>
              <li>Data Subject Rights (DSR) tools</li>
              <li>Breach notification capabilities</li>
            </ul>
          </li>
          <li><strong>Implementation Strategies:</strong>
            <ul>
              <li>Data discovery and classification with Cloud DLP</li>
              <li>Privacy by design implementation</li>
              <li>Consent management systems</li>
              <li>Data retention and deletion automation</li>
            </ul>
          </li>
        </ul>
        
        <h3>HIPAA Compliance in GCP</h3>
        <ul>
          <li><strong>HIPAA Safeguards:</strong>
            <ul>
              <li>Administrative safeguards</li>
              <li>Physical safeguards</li>
              <li>Technical safeguards</li>
              <li>Organizational requirements</li>
            </ul>
          </li>
          <li><strong>GCP HIPAA Services:</strong>
            <ul>
              <li>HIPAA-eligible GCP services</li>
              <li>Business Associate Agreement (BAA)</li>
              <li>Encryption and access controls</li>
              <li>Audit logging and monitoring</li>
            </ul>
          </li>
          <li><strong>PHI Protection Implementation:</strong>
            <ul>
              <li>End-to-end encryption with Cloud KMS</li>
              <li>IAM-based access control</li>
              <li>VPC network isolation</li>
              <li>Incident response procedures</li>
            </ul>
          </li>
        </ul>
        
        <h3>Continuous Compliance Management</h3>
        <ul>
          <li><strong>Compliance Assessment Automation:</strong>
            <ul>
              <li>Real-time compliance monitoring</li>
              <li>Policy violation detection</li>
              <li>Compliance drift identification</li>
              <li>Automated remediation triggers</li>
            </ul>
          </li>
          <li><strong>Audit and Evidence Management:</strong>
            <ul>
              <li>Comprehensive audit trails</li>
              <li>Evidence preservation and retention</li>
              <li>Compliance artifact management</li>
              <li>Regulatory reporting automation</li>
            </ul>
          </li>
          <li><strong>Risk Management Integration:</strong>
            <ul>
              <li>Risk assessment frameworks</li>
              <li>Compliance risk scoring</li>
              <li>Threat and vulnerability correlation</li>
              <li>Business impact analysis</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which GCP service provides centralized policy management and governance across the organization hierarchy?",
            options: [
              "Cloud IAM",
              "Organization Policy Service",
              "Security Command Center",
              "Cloud Asset Inventory"
            ],
            correctAnswer: 1,
            explanation: "Organization Policy Service provides centralized policy management and governance across the GCP organization hierarchy, allowing administrators to set constraints and controls at organization, folder, and project levels."
          },
          {
            question: "What is the primary document that establishes Google's role as a data processor for GDPR compliance?",
            options: [
              "Business Associate Agreement (BAA)",
              "Data Processing and Security Terms (DPST)",
              "Service Level Agreement (SLA)",
              "Terms of Service (ToS)"
            ],
            correctAnswer: 1,
            explanation: "The Data Processing and Security Terms (DPST) establishes Google's role as a data processor and outlines compliance commitments, data protection measures, and responsibilities for GDPR compliance in GCP services."
          },
          {
            question: "Which GCP service helps with sensitive data discovery and classification for compliance purposes?",
            options: [
              "Cloud KMS",
              "Cloud DLP (Data Loss Prevention)",
              "Secret Manager",
              "Binary Authorization"
            ],
            correctAnswer: 1,
            explanation: "Cloud DLP (Data Loss Prevention) helps with sensitive data discovery, classification, and protection, making it essential for compliance with regulations like GDPR, HIPAA, and PCI DSS that require data protection."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
