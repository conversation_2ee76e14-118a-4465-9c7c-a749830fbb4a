/**
 * Shared Responsibility Model Module
 */

export const sharedResponsibilityContent = {
  id: "cs-3",
  pathId: "cloud-security",
  title: "Shared Responsibility Model",
  description: "Master the shared responsibility model for cloud security, understanding the division of security responsibilities between cloud providers and customers across different service models.",
  objectives: [
    "Understand the fundamental principles of the shared responsibility model",
    "Learn responsibility divisions across IaaS, PaaS, and SaaS models",
    "Master security controls mapping and implementation",
    "Understand compliance implications and shared responsibilities",
    "Learn to create responsibility matrices for your organization",
    "Develop strategies for managing shared security responsibilities"
  ],
  difficulty: "Beginner",
  estimatedTime: 95,
  sections: [
    {
      title: "Shared Responsibility Model Fundamentals",
      content: `
        <h2>Shared Responsibility Model Fundamentals</h2>
        <p>The shared responsibility model is a fundamental concept in cloud security that defines the division of security responsibilities between cloud service providers and customers.</p>
        
        <h3>Core Principles</h3>
        <ul>
          <li><strong>Security "OF" the Cloud:</strong> Provider's responsibility for infrastructure security</li>
          <li><strong>Security "IN" the Cloud:</strong> Customer's responsibility for their data and applications</li>
          <li><strong>Shared Controls:</strong> Security responsibilities that are shared between provider and customer</li>
          <li><strong>Inherited Controls:</strong> Security controls that customers inherit from the provider</li>
        </ul>
        
        <h3>Provider Responsibilities (Security OF the Cloud)</h3>
        <ul>
          <li><strong>Physical Infrastructure:</strong>
            <ul>
              <li>Data center physical security</li>
              <li>Hardware maintenance and replacement</li>
              <li>Environmental controls (power, cooling, fire suppression)</li>
              <li>Physical access controls and monitoring</li>
            </ul>
          </li>
          <li><strong>Infrastructure Software:</strong>
            <ul>
              <li>Hypervisor security and patching</li>
              <li>Host operating system maintenance</li>
              <li>Network infrastructure security</li>
              <li>Service availability and redundancy</li>
            </ul>
          </li>
          <li><strong>Global Infrastructure:</strong>
            <ul>
              <li>Regions and availability zones</li>
              <li>Edge locations and content delivery</li>
              <li>Network backbone security</li>
              <li>Service isolation between customers</li>
            </ul>
          </li>
        </ul>
        
        <h3>Customer Responsibilities (Security IN the Cloud)</h3>
        <ul>
          <li><strong>Data Protection:</strong>
            <ul>
              <li>Data classification and handling</li>
              <li>Encryption at rest and in transit</li>
              <li>Data backup and recovery</li>
              <li>Data retention and disposal</li>
            </ul>
          </li>
          <li><strong>Identity and Access Management:</strong>
            <ul>
              <li>User account management</li>
              <li>Multi-factor authentication</li>
              <li>Role-based access controls</li>
              <li>Privileged access management</li>
            </ul>
          </li>
          <li><strong>Application Security:</strong>
            <ul>
              <li>Secure application development</li>
              <li>Application configuration</li>
              <li>Code security and vulnerability management</li>
              <li>Application monitoring and logging</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Service Model Variations",
      content: `
        <h2>Shared Responsibility Across Service Models</h2>
        <p>The shared responsibility model varies significantly across different cloud service models, with customers taking on more responsibility as they move from SaaS to IaaS.</p>
        
        <h3>Infrastructure as a Service (IaaS)</h3>
        <div class="responsibility-matrix">
          <h4>Customer Responsibilities:</h4>
          <ul>
            <li>Guest operating systems and patches</li>
            <li>Application software and runtime</li>
            <li>Security groups and firewall configuration</li>
            <li>Network traffic protection (encryption)</li>
            <li>Identity and access management</li>
            <li>Data encryption and key management</li>
            <li>Server-side encryption configuration</li>
            <li>Network and host-based intrusion detection</li>
          </ul>
          
          <h4>Provider Responsibilities:</h4>
          <ul>
            <li>Physical infrastructure and facilities</li>
            <li>Hypervisor and virtualization layer</li>
            <li>Network infrastructure and controls</li>
            <li>Host operating system patches</li>
            <li>Physical access controls</li>
            <li>Hardware lifecycle management</li>
          </ul>
        </div>
        
        <h3>Platform as a Service (PaaS)</h3>
        <div class="responsibility-matrix">
          <h4>Customer Responsibilities:</h4>
          <ul>
            <li>Application code and configuration</li>
            <li>Data protection and encryption</li>
            <li>Identity and access management</li>
            <li>Network traffic protection</li>
            <li>Application-level security controls</li>
            <li>User access and authentication</li>
          </ul>
          
          <h4>Provider Responsibilities:</h4>
          <ul>
            <li>Platform runtime environment</li>
            <li>Operating system maintenance</li>
            <li>Network controls and infrastructure</li>
            <li>Physical infrastructure security</li>
            <li>Platform service availability</li>
            <li>Underlying infrastructure patching</li>
          </ul>
        </div>
        
        <h3>Software as a Service (SaaS)</h3>
        <div class="responsibility-matrix">
          <h4>Customer Responsibilities:</h4>
          <ul>
            <li>User access management</li>
            <li>Data classification and protection</li>
            <li>Endpoint security</li>
            <li>User training and awareness</li>
            <li>Account and identity management</li>
            <li>Data usage and sharing policies</li>
          </ul>
          
          <h4>Provider Responsibilities:</h4>
          <ul>
            <li>Application security and maintenance</li>
            <li>Infrastructure and platform security</li>
            <li>Data center physical security</li>
            <li>Network security and controls</li>
            <li>Service availability and performance</li>
            <li>Application-level access controls</li>
          </ul>
        </div>
      `,
      type: "text"
    },
    {
      title: "Implementation and Best Practices",
      content: `
        <h2>Implementing Shared Responsibility</h2>
        <p>Successfully implementing the shared responsibility model requires clear understanding, documentation, and ongoing management of security responsibilities.</p>
        
        <h3>Creating a Responsibility Matrix</h3>
        <ul>
          <li><strong>Inventory Cloud Services:</strong> List all cloud services in use</li>
          <li><strong>Map Service Models:</strong> Identify IaaS, PaaS, and SaaS components</li>
          <li><strong>Define Responsibilities:</strong> Clearly document who is responsible for what</li>
          <li><strong>Identify Gaps:</strong> Find areas where responsibilities are unclear</li>
          <li><strong>Document Controls:</strong> Map security controls to responsible parties</li>
          <li><strong>Regular Reviews:</strong> Update matrix as services change</li>
        </ul>
        
        <h3>Best Practices for Customers</h3>
        <ul>
          <li><strong>Understand Your Responsibilities:</strong>
            <ul>
              <li>Read and understand provider documentation</li>
              <li>Regularly review shared responsibility models</li>
              <li>Stay updated on service changes</li>
              <li>Validate understanding with provider support</li>
            </ul>
          </li>
          <li><strong>Implement Defense in Depth:</strong>
            <ul>
              <li>Layer security controls across all responsible areas</li>
              <li>Don't rely solely on provider security</li>
              <li>Implement monitoring and detection</li>
              <li>Plan for incident response</li>
            </ul>
          </li>
          <li><strong>Maintain Visibility:</strong>
            <ul>
              <li>Monitor your cloud environment continuously</li>
              <li>Log and audit all activities</li>
              <li>Implement configuration management</li>
              <li>Regular security assessments</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Misconceptions</h3>
        <ul>
          <li><strong>Myth:</strong> "The cloud provider handles all security"
            <ul><li><strong>Reality:</strong> Customers are responsible for significant security aspects</li></ul>
          </li>
          <li><strong>Myth:</strong> "Shared responsibility means shared liability"
            <ul><li><strong>Reality:</strong> Customers remain liable for their data and compliance</li></ul>
          </li>
          <li><strong>Myth:</strong> "All cloud services have the same responsibility model"
            <ul><li><strong>Reality:</strong> Responsibilities vary significantly by service type</li></ul>
          </li>
          <li><strong>Myth:</strong> "Provider compliance means customer compliance"
            <ul><li><strong>Reality:</strong> Customers must implement their own compliance controls</li></ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "In the shared responsibility model, who is responsible for patching the guest operating system in an IaaS deployment?",
            options: [
              "Cloud service provider",
              "Customer",
              "Both provider and customer",
              "Third-party vendor"
            ],
            correctAnswer: 1,
            explanation: "In IaaS deployments, customers are responsible for managing and patching the guest operating systems, while the provider manages the underlying infrastructure."
          },
          {
            question: "Which of the following is typically a shared control in cloud environments?",
            options: [
              "Physical data center security",
              "Patch management",
              "Application code security",
              "User access management"
            ],
            correctAnswer: 1,
            explanation: "Patch management is often a shared control where the provider patches infrastructure components while the customer patches their applications and guest operating systems."
          },
          {
            question: "In a SaaS model, what is the customer's primary security responsibility?",
            options: [
              "Infrastructure security",
              "Application security",
              "User access management and data protection",
              "Network security"
            ],
            correctAnswer: 2,
            explanation: "In SaaS models, customers are primarily responsible for managing user access, data classification, and ensuring proper use of the service."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
