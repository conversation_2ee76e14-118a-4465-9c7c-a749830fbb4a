/**
 * Azure Security Fundamentals Module
 */

export const azureSecurityFundamentalsContent = {
  id: "cs-24",
  pathId: "cloud-security",
  title: "Azure Security Fundamentals",
  description: "Master Microsoft Azure security fundamentals, including Azure security architecture, shared responsibility model, and core security services for comprehensive Azure protection.",
  objectives: [
    "Understand Azure security architecture and shared responsibility",
    "Learn Azure security services and tools",
    "Master Azure subscription and resource security",
    "Develop skills in Azure security monitoring",
    "Learn Azure compliance and governance",
    "Implement comprehensive Azure security strategies"
  ],
  difficulty: "Intermediate",
  estimatedTime: 130,
  sections: [
    {
      title: "Azure Security Architecture",
      content: `
        <h2>Azure Security Architecture Overview</h2>
        <p>Azure security architecture is built on a foundation of defense in depth, zero trust principles, and a shared responsibility model between Microsoft and customers.</p>
        
        <h3>Azure Shared Responsibility Model</h3>
        <ul>
          <li><strong>Microsoft Responsibilities (Security OF the Cloud):</strong>
            <ul>
              <li>Physical datacenter security</li>
              <li>Infrastructure and platform security</li>
              <li>Host operating system patching</li>
              <li>Network controls and hypervisor security</li>
            </ul>
          </li>
          <li><strong>Customer Responsibilities (Security IN the Cloud):</strong>
            <ul>
              <li>Data classification and protection</li>
              <li>Identity and access management</li>
              <li>Application and operating system security</li>
              <li>Network traffic protection</li>
            </ul>
          </li>
          <li><strong>Shared Responsibilities:</strong>
            <ul>
              <li>Identity and directory infrastructure</li>
              <li>Application controls and network controls</li>
              <li>Operating system configuration (varies by service)</li>
              <li>Endpoint protection and network security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Security Services Overview</h3>
        <ul>
          <li><strong>Identity and Access Management:</strong>
            <ul>
              <li>Azure Active Directory (Azure AD)</li>
              <li>Azure AD Privileged Identity Management</li>
              <li>Azure AD Identity Protection</li>
              <li>Azure Multi-Factor Authentication</li>
            </ul>
          </li>
          <li><strong>Security Management and Monitoring:</strong>
            <ul>
              <li>Azure Security Center (now Microsoft Defender for Cloud)</li>
              <li>Azure Sentinel (SIEM and SOAR)</li>
              <li>Azure Monitor and Log Analytics</li>
              <li>Azure Policy and Blueprints</li>
            </ul>
          </li>
          <li><strong>Data Protection and Encryption:</strong>
            <ul>
              <li>Azure Key Vault</li>
              <li>Azure Information Protection</li>
              <li>Azure Disk Encryption</li>
              <li>Azure Storage Service Encryption</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Security Design Principles</h3>
        <ul>
          <li><strong>Zero Trust Architecture:</strong>
            <ul>
              <li>Never trust, always verify</li>
              <li>Least privilege access</li>
              <li>Assume breach mentality</li>
              <li>Continuous verification</li>
            </ul>
          </li>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple layers of security</li>
              <li>Physical, identity, perimeter, network, compute, application, and data security</li>
              <li>Redundant security controls</li>
              <li>Comprehensive threat protection</li>
            </ul>
          </li>
          <li><strong>Security by Default:</strong>
            <ul>
              <li>Secure configurations out of the box</li>
              <li>Encryption enabled by default</li>
              <li>Security monitoring and alerting</li>
              <li>Automated security updates</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Azure Subscription and Resource Security",
      content: `
        <h2>Azure Subscription and Resource Security</h2>
        <p>Securing Azure subscriptions and resources requires implementing proper governance, access controls, and security policies across the Azure hierarchy.</p>
        
        <h3>Azure Hierarchy and Security</h3>
        <ul>
          <li><strong>Management Group Level:</strong>
            <ul>
              <li>Enterprise-scale governance</li>
              <li>Policy and compliance inheritance</li>
              <li>Role-based access control (RBAC)</li>
              <li>Cost management and billing</li>
            </ul>
          </li>
          <li><strong>Subscription Level:</strong>
            <ul>
              <li>Billing and cost boundaries</li>
              <li>Resource quotas and limits</li>
              <li>Security and compliance policies</li>
              <li>Access control and permissions</li>
            </ul>
          </li>
          <li><strong>Resource Group Level:</strong>
            <ul>
              <li>Logical resource organization</li>
              <li>Lifecycle management</li>
              <li>Access control inheritance</li>
              <li>Policy and tag application</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Resource Manager (ARM) Security</h3>
        <ul>
          <li><strong>ARM Security Features:</strong>
            <ul>
              <li>Consistent management layer</li>
              <li>Role-based access control integration</li>
              <li>Resource locks and policies</li>
              <li>Template-based deployment</li>
            </ul>
          </li>
          <li><strong>Resource Security Controls:</strong>
            <ul>
              <li>Resource-level RBAC assignments</li>
              <li>Azure Policy enforcement</li>
              <li>Resource tagging for governance</li>
              <li>Activity logging and monitoring</li>
            </ul>
          </li>
          <li><strong>ARM Template Security:</strong>
            <ul>
              <li>Infrastructure as Code (IaC) security</li>
              <li>Parameter and variable security</li>
              <li>Secret management in templates</li>
              <li>Template validation and testing</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Security Baselines</h3>
        <ul>
          <li><strong>Azure Security Benchmark:</strong>
            <ul>
              <li>Microsoft cloud security guidance</li>
              <li>Industry best practices alignment</li>
              <li>Service-specific recommendations</li>
              <li>Compliance mapping and controls</li>
            </ul>
          </li>
          <li><strong>Security Baseline Implementation:</strong>
            <ul>
              <li>Azure Policy for baseline enforcement</li>
              <li>Azure Blueprints for standardization</li>
              <li>Automated compliance assessment</li>
              <li>Continuous monitoring and remediation</li>
            </ul>
          </li>
          <li><strong>Custom Security Standards:</strong>
            <ul>
              <li>Organization-specific requirements</li>
              <li>Industry compliance frameworks</li>
              <li>Regulatory requirement mapping</li>
              <li>Custom policy development</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Azure Security Monitoring and Governance",
      content: `
        <h2>Azure Security Monitoring and Governance</h2>
        <p>Comprehensive security monitoring and governance in Azure requires leveraging multiple services and implementing continuous monitoring strategies for threat detection and compliance.</p>
        
        <h3>Microsoft Defender for Cloud</h3>
        <ul>
          <li><strong>Security Posture Management:</strong>
            <ul>
              <li>Secure score and recommendations</li>
              <li>Security policy and compliance</li>
              <li>Asset inventory and discovery</li>
              <li>Vulnerability assessment</li>
            </ul>
          </li>
          <li><strong>Threat Protection:</strong>
            <ul>
              <li>Advanced threat detection</li>
              <li>Security alerts and incidents</li>
              <li>Just-in-time VM access</li>
              <li>Adaptive application controls</li>
            </ul>
          </li>
          <li><strong>Hybrid and Multi-Cloud Protection:</strong>
            <ul>
              <li>On-premises server protection</li>
              <li>AWS and GCP resource monitoring</li>
              <li>Kubernetes cluster security</li>
              <li>Container security scanning</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Sentinel (SIEM/SOAR)</h3>
        <ul>
          <li><strong>Data Collection and Ingestion:</strong>
            <ul>
              <li>Multi-source data connectors</li>
              <li>Custom log ingestion</li>
              <li>Real-time data streaming</li>
              <li>Data normalization and parsing</li>
            </ul>
          </li>
          <li><strong>Threat Detection and Analytics:</strong>
            <ul>
              <li>Machine learning-based detection</li>
              <li>Custom analytics rules</li>
              <li>Threat intelligence integration</li>
              <li>User and entity behavior analytics (UEBA)</li>
            </ul>
          </li>
          <li><strong>Incident Response and Automation:</strong>
            <ul>
              <li>Security orchestration and automation</li>
              <li>Playbook-driven response</li>
              <li>Case management and investigation</li>
              <li>Threat hunting capabilities</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure Governance and Compliance</h3>
        <ul>
          <li><strong>Azure Policy:</strong>
            <ul>
              <li>Policy definition and assignment</li>
              <li>Compliance assessment and reporting</li>
              <li>Automatic remediation</li>
              <li>Initiative and policy sets</li>
            </ul>
          </li>
          <li><strong>Azure Blueprints:</strong>
            <ul>
              <li>Environment standardization</li>
              <li>Compliance template deployment</li>
              <li>Governance artifact management</li>
              <li>Version control and tracking</li>
            </ul>
          </li>
          <li><strong>Compliance and Certifications:</strong>
            <ul>
              <li>Industry compliance frameworks</li>
              <li>Regulatory requirement mapping</li>
              <li>Audit and assessment tools</li>
              <li>Compliance dashboard and reporting</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "In the Azure Shared Responsibility Model, who is responsible for securing the guest operating system?",
            options: [
              "Microsoft is responsible",
              "The customer is responsible",
              "Both Microsoft and customer share responsibility",
              "It depends on the Azure service type"
            ],
            correctAnswer: 1,
            explanation: "The customer is responsible for securing the guest operating system as part of their security responsibilities 'in the cloud', while Microsoft handles the underlying infrastructure security."
          },
          {
            question: "Which Azure service provides centralized security posture management and threat protection?",
            options: [
              "Azure Active Directory",
              "Azure Monitor",
              "Microsoft Defender for Cloud",
              "Azure Policy"
            ],
            correctAnswer: 2,
            explanation: "Microsoft Defender for Cloud (formerly Azure Security Center) provides centralized security posture management, threat protection, and security recommendations across Azure resources."
          },
          {
            question: "What is the primary purpose of Azure Blueprints in security governance?",
            options: [
              "Network traffic monitoring",
              "Environment standardization and compliance template deployment",
              "User authentication",
              "Data encryption"
            ],
            correctAnswer: 1,
            explanation: "Azure Blueprints enable environment standardization and compliance template deployment by packaging ARM templates, policies, role assignments, and other artifacts for consistent governance implementation."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
