/**
 * GCP Security Services Module
 */

export const gcpSecurityServicesContent = {
  id: "cs-32",
  pathId: "cloud-security",
  title: "GCP Security Services",
  description: "Master Google Cloud Platform security services including Security Command Center, Cloud DLP, Binary Authorization, and other security tools for comprehensive threat detection and protection.",
  objectives: [
    "Understand GCP security services ecosystem",
    "Learn Security Command Center and threat detection",
    "Master data protection with Cloud DLP",
    "Develop skills in container and application security",
    "Learn security monitoring and compliance services",
    "Implement integrated GCP security service strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 150,
  sections: [
    {
      title: "GCP Security Services Overview",
      content: `
        <h2>Google Cloud Security Services Ecosystem</h2>
        <p>GCP provides a comprehensive suite of security services that work together to provide threat detection, data protection, compliance monitoring, and security management across Google Cloud environments.</p>
        
        <h3>Security Service Categories</h3>
        <ul>
          <li><strong>Security Management and Monitoring:</strong>
            <ul>
              <li>Security Command Center (SCC)</li>
              <li>Cloud Asset Inventory</li>
              <li>Cloud Security Scanner</li>
              <li>Event Threat Detection</li>
            </ul>
          </li>
          <li><strong>Data Protection and Privacy:</strong>
            <ul>
              <li>Cloud Data Loss Prevention (DLP)</li>
              <li>Cloud Key Management Service (KMS)</li>
              <li>Secret Manager</li>
              <li>Confidential Computing</li>
            </ul>
          </li>
          <li><strong>Application and Container Security:</strong>
            <ul>
              <li>Binary Authorization</li>
              <li>Container Analysis</li>
              <li>Web Security Scanner</li>
              <li>reCAPTCHA Enterprise</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Service Integration</h3>
        <ul>
          <li><strong>Centralized Security Management:</strong>
            <ul>
              <li>Security Command Center as central hub</li>
              <li>Cross-service finding correlation</li>
              <li>Unified security posture view</li>
              <li>Automated response workflows</li>
            </ul>
          </li>
          <li><strong>API and Automation:</strong>
            <ul>
              <li>REST APIs for all security services</li>
              <li>Infrastructure as Code integration</li>
              <li>Custom security orchestration</li>
              <li>CI/CD pipeline integration</li>
            </ul>
          </li>
          <li><strong>Third-Party Integration:</strong>
            <ul>
              <li>SIEM and SOAR platform connectivity</li>
              <li>Security partner integrations</li>
              <li>Custom connector development</li>
              <li>Multi-cloud security management</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Service Deployment Strategies</h3>
        <ul>
          <li><strong>Organization-Wide Security:</strong>
            <ul>
              <li>Organization-level security policies</li>
              <li>Centralized security monitoring</li>
              <li>Cross-project security management</li>
              <li>Unified compliance reporting</li>
            </ul>
          </li>
          <li><strong>Project-Level Security:</strong>
            <ul>
              <li>Project-specific security controls</li>
              <li>Workload-based security policies</li>
              <li>Environment-specific configurations</li>
              <li>Development lifecycle integration</li>
            </ul>
          </li>
          <li><strong>Resource-Level Security:</strong>
            <ul>
              <li>Fine-grained security controls</li>
              <li>Resource-specific policies</li>
              <li>Dynamic security adaptation</li>
              <li>Context-aware protection</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Security Command Center and Threat Detection",
      content: `
        <h2>Security Command Center and Advanced Threat Detection</h2>
        <p>Security Command Center provides centralized security management and threat detection capabilities, offering comprehensive visibility and control over GCP security posture.</p>
        
        <h3>Security Command Center (SCC)</h3>
        <ul>
          <li><strong>Asset Discovery and Inventory:</strong>
            <ul>
              <li>Automatic asset discovery</li>
              <li>Resource inventory management</li>
              <li>Asset relationship mapping</li>
              <li>Configuration change tracking</li>
            </ul>
          </li>
          <li><strong>Security Findings Management:</strong>
            <ul>
              <li>Centralized finding aggregation</li>
              <li>Finding categorization and prioritization</li>
              <li>Custom finding sources</li>
              <li>Finding lifecycle management</li>
            </ul>
          </li>
          <li><strong>Security Posture Assessment:</strong>
            <ul>
              <li>Security health analytics</li>
              <li>Compliance monitoring</li>
              <li>Risk assessment and scoring</li>
              <li>Trend analysis and reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Event Threat Detection</h3>
        <ul>
          <li><strong>Threat Detection Capabilities:</strong>
            <ul>
              <li>Malware detection in Cloud Storage</li>
              <li>Cryptomining detection</li>
              <li>DDoS attack identification</li>
              <li>Brute force attack detection</li>
            </ul>
          </li>
          <li><strong>Behavioral Analysis:</strong>
            <ul>
              <li>Anomalous user behavior detection</li>
              <li>Unusual API activity monitoring</li>
              <li>Suspicious network traffic analysis</li>
              <li>Data exfiltration detection</li>
            </ul>
          </li>
          <li><strong>Threat Intelligence Integration:</strong>
            <ul>
              <li>Google threat intelligence feeds</li>
              <li>IOC (Indicators of Compromise) matching</li>
              <li>Threat actor attribution</li>
              <li>Campaign tracking and analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Security Scanner</h3>
        <ul>
          <li><strong>Web Application Scanning:</strong>
            <ul>
              <li>Automated vulnerability scanning</li>
              <li>OWASP Top 10 detection</li>
              <li>Custom scan configurations</li>
              <li>Continuous security testing</li>
            </ul>
          </li>
          <li><strong>Scan Management:</strong>
            <ul>
              <li>Scheduled and on-demand scans</li>
              <li>Scan result analysis</li>
              <li>False positive management</li>
              <li>Remediation guidance</li>
            </ul>
          </li>
          <li><strong>Integration and Reporting:</strong>
            <ul>
              <li>Security Command Center integration</li>
              <li>CI/CD pipeline integration</li>
              <li>Custom reporting and alerts</li>
              <li>Compliance documentation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Data Protection and Application Security",
      content: `
        <h2>GCP Data Protection and Application Security Services</h2>
        <p>GCP data protection and application security services provide comprehensive protection for sensitive data and applications through advanced detection, classification, and security controls.</p>
        
        <h3>Cloud Data Loss Prevention (DLP)</h3>
        <ul>
          <li><strong>Data Discovery and Classification:</strong>
            <ul>
              <li>Sensitive data identification</li>
              <li>Built-in and custom data types</li>
              <li>Pattern matching and ML detection</li>
              <li>Data profiling and analysis</li>
            </ul>
          </li>
          <li><strong>Data Protection Actions:</strong>
            <ul>
              <li>Data masking and redaction</li>
              <li>Tokenization and encryption</li>
              <li>Data anonymization</li>
              <li>Access restriction and blocking</li>
            </ul>
          </li>
          <li><strong>DLP Integration:</strong>
            <ul>
              <li>Cloud Storage scanning</li>
              <li>BigQuery data inspection</li>
              <li>Dataflow pipeline integration</li>
              <li>Real-time API protection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Binary Authorization and Container Security</h3>
        <ul>
          <li><strong>Container Image Security:</strong>
            <ul>
              <li>Image vulnerability scanning</li>
              <li>Policy-based deployment control</li>
              <li>Attestation and verification</li>
              <li>Supply chain security</li>
            </ul>
          </li>
          <li><strong>Binary Authorization Policies:</strong>
            <ul>
              <li>Attestor configuration</li>
              <li>Policy rule definition</li>
              <li>Cluster and project-level policies</li>
              <li>Break-glass procedures</li>
            </ul>
          </li>
          <li><strong>Container Analysis:</strong>
            <ul>
              <li>Vulnerability database integration</li>
              <li>Package and library analysis</li>
              <li>Security finding correlation</li>
              <li>Remediation recommendations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Security Services</h3>
        <ul>
          <li><strong>Confidential Computing:</strong>
            <ul>
              <li>Confidential VMs</li>
              <li>Confidential GKE nodes</li>
              <li>Data encryption in use</li>
              <li>Trusted execution environments</li>
            </ul>
          </li>
          <li><strong>reCAPTCHA Enterprise:</strong>
            <ul>
              <li>Bot detection and mitigation</li>
              <li>Risk scoring and analysis</li>
              <li>Fraud prevention</li>
              <li>User experience optimization</li>
            </ul>
          </li>
          <li><strong>Certificate Authority Service:</strong>
            <ul>
              <li>Private CA management</li>
              <li>Certificate lifecycle automation</li>
              <li>PKI infrastructure</li>
              <li>Compliance and audit support</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which GCP service provides centralized security findings management and asset discovery?",
            options: [
              "Cloud DLP",
              "Security Command Center (SCC)",
              "Binary Authorization",
              "Cloud Asset Inventory"
            ],
            correctAnswer: 1,
            explanation: "Security Command Center (SCC) provides centralized security findings management, asset discovery, and security posture assessment across all GCP resources and services."
          },
          {
            question: "What is the primary purpose of Cloud Data Loss Prevention (DLP) in GCP?",
            options: [
              "Network traffic monitoring",
              "Sensitive data discovery, classification, and protection",
              "Container security scanning",
              "Identity management"
            ],
            correctAnswer: 1,
            explanation: "Cloud Data Loss Prevention (DLP) is designed for sensitive data discovery, classification, and protection through various actions like masking, redaction, and tokenization."
          },
          {
            question: "Which GCP service ensures that only verified and approved container images are deployed to GKE clusters?",
            options: [
              "Container Analysis",
              "Cloud Security Scanner",
              "Binary Authorization",
              "Event Threat Detection"
            ],
            correctAnswer: 2,
            explanation: "Binary Authorization ensures that only verified and approved container images are deployed by enforcing policy-based deployment controls and requiring attestations from trusted authorities."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
