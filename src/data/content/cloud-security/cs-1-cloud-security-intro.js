/**
 * Introduction to Cloud Security Module
 */

export const cloudSecurityIntroContent = {
  id: "cs-1",
  pathId: "cloud-security",
  title: "Introduction to Cloud Security",
  description: "Master the fundamentals of cloud security, including cloud service models, deployment models, and core security principles for cloud environments.",
  objectives: [
    "Understand cloud computing fundamentals and service models",
    "Learn cloud deployment models and their security implications",
    "Master the shared responsibility model for cloud security",
    "Explore cloud security frameworks and best practices",
    "Understand cloud-specific threats and attack vectors",
    "Learn cloud security governance and compliance requirements"
  ],
  difficulty: "Beginner",
  estimatedTime: 90,
  sections: [
    {
      title: "Cloud Computing Fundamentals",
      content: `
        <h2>Cloud Computing Fundamentals</h2>
        <p>Cloud computing represents a paradigm shift in how organizations consume and manage IT resources, offering scalability, flexibility, and cost-effectiveness while introducing new security challenges.</p>
        
        <h3>What is Cloud Computing?</h3>
        <ul>
          <li><strong>Definition:</strong> On-demand delivery of computing services over the internet</li>
          <li><strong>Key Characteristics:</strong> On-demand self-service, broad network access, resource pooling, rapid elasticity, measured service</li>
          <li><strong>Benefits:</strong> Cost reduction, scalability, flexibility, innovation acceleration</li>
          <li><strong>Challenges:</strong> Security concerns, compliance requirements, vendor lock-in, data sovereignty</li>
        </ul>
        
        <h3>Cloud Service Models</h3>
        <ul>
          <li><strong>Infrastructure as a Service (IaaS):</strong>
            <ul>
              <li>Provides virtualized computing resources over the internet</li>
              <li>Examples: Amazon EC2, Microsoft Azure VMs, Google Compute Engine</li>
              <li>Customer responsibility: OS, applications, data, runtime, middleware</li>
              <li>Provider responsibility: Physical infrastructure, hypervisor, networking</li>
            </ul>
          </li>
          <li><strong>Platform as a Service (PaaS):</strong>
            <ul>
              <li>Provides platform and environment for developers to build applications</li>
              <li>Examples: AWS Elastic Beanstalk, Azure App Service, Google App Engine</li>
              <li>Customer responsibility: Applications and data</li>
              <li>Provider responsibility: Runtime, middleware, OS, infrastructure</li>
            </ul>
          </li>
          <li><strong>Software as a Service (SaaS):</strong>
            <ul>
              <li>Provides complete software applications over the internet</li>
              <li>Examples: Office 365, Salesforce, Google Workspace</li>
              <li>Customer responsibility: Data and user access management</li>
              <li>Provider responsibility: Everything else including application security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Deployment Models</h3>
        <ul>
          <li><strong>Public Cloud:</strong>
            <ul>
              <li>Services offered over the public internet</li>
              <li>Shared infrastructure among multiple tenants</li>
              <li>Cost-effective but less control over security</li>
              <li>Examples: AWS, Microsoft Azure, Google Cloud</li>
            </ul>
          </li>
          <li><strong>Private Cloud:</strong>
            <ul>
              <li>Dedicated cloud infrastructure for a single organization</li>
              <li>Greater control and customization</li>
              <li>Higher costs but enhanced security and compliance</li>
              <li>Can be on-premises or hosted by third party</li>
            </ul>
          </li>
          <li><strong>Hybrid Cloud:</strong>
            <ul>
              <li>Combination of public and private cloud environments</li>
              <li>Allows data and applications to move between environments</li>
              <li>Balances cost, control, and flexibility</li>
              <li>Complex security management across environments</li>
            </ul>
          </li>
          <li><strong>Multi-Cloud:</strong>
            <ul>
              <li>Use of multiple cloud service providers</li>
              <li>Avoids vendor lock-in and improves resilience</li>
              <li>Increased complexity in security management</li>
              <li>Requires consistent security policies across providers</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Cloud Security Fundamentals",
      content: `
        <h2>Cloud Security Fundamentals</h2>
        <p>Cloud security encompasses the policies, technologies, applications, and controls utilized to protect virtualized IP, data, applications, services, and the associated infrastructure of cloud computing.</p>
        
        <h3>Core Cloud Security Principles</h3>
        <ul>
          <li><strong>Shared Responsibility Model:</strong>
            <ul>
              <li>Security responsibilities are shared between cloud provider and customer</li>
              <li>Provider secures the infrastructure, customer secures their data and applications</li>
              <li>Responsibility varies by service model (IaaS, PaaS, SaaS)</li>
              <li>Clear understanding prevents security gaps</li>
            </ul>
          </li>
          <li><strong>Defense in Depth:</strong>
            <ul>
              <li>Multiple layers of security controls</li>
              <li>Network, host, application, and data-level protections</li>
              <li>Redundancy ensures security even if one layer fails</li>
              <li>Comprehensive monitoring and logging</li>
            </ul>
          </li>
          <li><strong>Zero Trust Architecture:</strong>
            <ul>
              <li>Never trust, always verify approach</li>
              <li>Continuous verification of users and devices</li>
              <li>Micro-segmentation and least privilege access</li>
              <li>Essential for cloud and hybrid environments</li>
            </ul>
          </li>
          <li><strong>Identity-Centric Security:</strong>
            <ul>
              <li>Identity as the new security perimeter</li>
              <li>Strong authentication and authorization</li>
              <li>Privileged access management</li>
              <li>Identity governance and lifecycle management</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Security Challenges</h3>
        <ul>
          <li><strong>Visibility and Control:</strong>
            <ul>
              <li>Limited visibility into cloud provider infrastructure</li>
              <li>Reduced control over security configurations</li>
              <li>Dependency on provider security capabilities</li>
              <li>Need for cloud-native security tools</li>
            </ul>
          </li>
          <li><strong>Data Protection:</strong>
            <ul>
              <li>Data location and sovereignty concerns</li>
              <li>Encryption in transit and at rest</li>
              <li>Key management complexities</li>
              <li>Data residency and compliance requirements</li>
            </ul>
          </li>
          <li><strong>Identity and Access Management:</strong>
            <ul>
              <li>Managing identities across multiple cloud platforms</li>
              <li>Federation and single sign-on challenges</li>
              <li>Privileged access in cloud environments</li>
              <li>Service account and API key management</li>
            </ul>
          </li>
          <li><strong>Compliance and Governance:</strong>
            <ul>
              <li>Meeting regulatory requirements in cloud</li>
              <li>Audit and compliance reporting</li>
              <li>Data governance across cloud services</li>
              <li>Risk management in shared environments</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "In the shared responsibility model, who is responsible for securing the guest operating system in an IaaS deployment?",
            options: [
              "Cloud service provider",
              "Customer",
              "Both provider and customer equally",
              "Third-party security vendor"
            ],
            correctAnswer: 1,
            explanation: "In IaaS deployments, the customer is responsible for securing the guest operating system, applications, and data, while the provider secures the underlying infrastructure."
          },
          {
            question: "Which cloud deployment model offers the highest level of control over security configurations?",
            options: [
              "Public cloud",
              "Private cloud",
              "Hybrid cloud",
              "Multi-cloud"
            ],
            correctAnswer: 1,
            explanation: "Private cloud offers the highest level of control over security configurations as it provides dedicated infrastructure for a single organization."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
