/**
 * AWS IAM (Identity and Access Management) Module
 */

export const awsIamContent = {
  id: "cs-20",
  pathId: "cloud-security",
  title: "AWS IAM (Identity and Access Management)",
  description: "Master AWS Identity and Access Management, including users, groups, roles, policies, and advanced IAM features for secure access control in AWS environments.",
  objectives: [
    "Understand AWS IAM fundamentals and components",
    "Learn IAM users, groups, and roles management",
    "Master IAM policies and permissions",
    "Develop skills in advanced IAM features",
    "Learn IAM security best practices",
    "Implement comprehensive IAM strategies"
  ],
  difficulty: "Intermediate",
  estimatedTime: 135,
  sections: [
    {
      title: "AWS IAM Fundamentals",
      content: `
        <h2>AWS IAM Overview</h2>
        <p>AWS Identity and Access Management (IAM) is a web service that helps you securely control access to AWS resources by managing authentication and authorization.</p>
        
        <h3>IAM Core Components</h3>
        <ul>
          <li><strong>IAM Users:</strong>
            <ul>
              <li>Individual identities for people or applications</li>
              <li>Permanent credentials (access keys, passwords)</li>
              <li>Direct policy attachment</li>
              <li>Console and programmatic access</li>
            </ul>
          </li>
          <li><strong>IAM Groups:</strong>
            <ul>
              <li>Collections of IAM users</li>
              <li>Simplified permission management</li>
              <li>Policy inheritance by group members</li>
              <li>Organizational structure alignment</li>
            </ul>
          </li>
          <li><strong>IAM Roles:</strong>
            <ul>
              <li>Temporary credentials for entities</li>
              <li>Cross-account and service access</li>
              <li>Federated user access</li>
              <li>Enhanced security through temporary tokens</li>
            </ul>
          </li>
        </ul>
        
        <h3>IAM Policies and Permissions</h3>
        <ul>
          <li><strong>Policy Types:</strong>
            <ul>
              <li>Identity-based policies (attached to users, groups, roles)</li>
              <li>Resource-based policies (attached to resources)</li>
              <li>Permission boundaries (maximum permissions)</li>
              <li>Service Control Policies (SCPs) in Organizations</li>
            </ul>
          </li>
          <li><strong>Policy Structure:</strong>
            <ul>
              <li>JSON document format</li>
              <li>Effect (Allow or Deny)</li>
              <li>Action (API operations)</li>
              <li>Resource (AWS resource ARNs)</li>
              <li>Condition (optional constraints)</li>
            </ul>
          </li>
          <li><strong>Policy Evaluation Logic:</strong>
            <ul>
              <li>Default deny for all requests</li>
              <li>Explicit deny overrides allow</li>
              <li>Policy evaluation order</li>
              <li>Permission boundaries enforcement</li>
            </ul>
          </li>
        </ul>
        
        <h3>IAM Authentication Methods</h3>
        <ul>
          <li><strong>Console Access:</strong>
            <ul>
              <li>Username and password</li>
              <li>Multi-factor authentication (MFA)</li>
              <li>Temporary credentials via roles</li>
              <li>Federated access through SAML/OIDC</li>
            </ul>
          </li>
          <li><strong>Programmatic Access:</strong>
            <ul>
              <li>Access keys (Access Key ID and Secret)</li>
              <li>Temporary security credentials</li>
              <li>IAM roles for EC2 instances</li>
              <li>Cross-account role assumption</li>
            </ul>
          </li>
          <li><strong>Federated Access:</strong>
            <ul>
              <li>SAML 2.0 federation</li>
              <li>OpenID Connect (OIDC) providers</li>
              <li>AWS SSO integration</li>
              <li>Web identity federation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced IAM Features and Security",
      content: `
        <h2>Advanced IAM Features and Security Controls</h2>
        <p>Advanced IAM features provide enhanced security controls, fine-grained access management, and sophisticated authentication mechanisms for complex AWS environments.</p>
        
        <h3>IAM Advanced Security Features</h3>
        <ul>
          <li><strong>Multi-Factor Authentication (MFA):</strong>
            <ul>
              <li>Virtual MFA devices (Google Authenticator, Authy)</li>
              <li>Hardware MFA devices (YubiKey, Gemalto)</li>
              <li>SMS-based MFA (not recommended for root)</li>
              <li>MFA for API calls and console access</li>
            </ul>
          </li>
          <li><strong>IAM Access Analyzer:</strong>
            <ul>
              <li>External access identification</li>
              <li>Policy validation and recommendations</li>
              <li>Unused access detection</li>
              <li>Policy generation from CloudTrail logs</li>
            </ul>
          </li>
          <li><strong>IAM Credential Reports:</strong>
            <ul>
              <li>Account-wide credential usage</li>
              <li>Password and access key age</li>
              <li>MFA device status</li>
              <li>Last activity timestamps</li>
            </ul>
          </li>
        </ul>
        
        <h3>IAM Policy Advanced Techniques</h3>
        <ul>
          <li><strong>Condition Keys and Operators:</strong>
            <ul>
              <li>Date and time conditions</li>
              <li>IP address and VPC restrictions</li>
              <li>MFA and SSL requirements</li>
              <li>Resource tagging conditions</li>
            </ul>
          </li>
          <li><strong>Policy Variables:</strong>
            <ul>
              <li>Dynamic policy elements</li>
              <li>User-specific resource access</li>
              <li>Request context variables</li>
              <li>Simplified policy management</li>
            </ul>
          </li>
          <li><strong>Permission Boundaries:</strong>
            <ul>
              <li>Maximum permission limits</li>
              <li>Delegated administration</li>
              <li>Developer self-service with guardrails</li>
              <li>Compliance and governance</li>
            </ul>
          </li>
        </ul>
        
        <h3>IAM Cross-Account Access</h3>
        <ul>
          <li><strong>Cross-Account Roles:</strong>
            <ul>
              <li>Trust relationship configuration</li>
              <li>External ID for enhanced security</li>
              <li>Condition-based access control</li>
              <li>Temporary credential assumption</li>
            </ul>
          </li>
          <li><strong>Resource-Based Policies:</strong>
            <ul>
              <li>S3 bucket policies</li>
              <li>KMS key policies</li>
              <li>Lambda function policies</li>
              <li>Cross-account resource sharing</li>
            </ul>
          </li>
          <li><strong>AWS Organizations Integration:</strong>
            <ul>
              <li>Service Control Policies (SCPs)</li>
              <li>Organizational unit restrictions</li>
              <li>Account-level permission boundaries</li>
              <li>Centralized access management</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "IAM Best Practices and Monitoring",
      content: `
        <h2>IAM Security Best Practices and Monitoring</h2>
        <p>Implementing IAM best practices and continuous monitoring ensures secure access management and helps maintain the principle of least privilege across AWS environments.</p>
        
        <h3>IAM Security Best Practices</h3>
        <ul>
          <li><strong>Principle of Least Privilege:</strong>
            <ul>
              <li>Grant minimum required permissions</li>
              <li>Regular access reviews and cleanup</li>
              <li>Just-in-time access implementation</li>
              <li>Permission escalation controls</li>
            </ul>
          </li>
          <li><strong>Credential Management:</strong>
            <ul>
              <li>Avoid long-term access keys</li>
              <li>Use IAM roles for applications</li>
              <li>Regular credential rotation</li>
              <li>Secure credential storage</li>
            </ul>
          </li>
          <li><strong>Access Control Strategies:</strong>
            <ul>
              <li>Use groups for permission assignment</li>
              <li>Implement strong password policies</li>
              <li>Enforce MFA for sensitive operations</li>
              <li>Monitor and log all access</li>
            </ul>
          </li>
        </ul>
        
        <h3>IAM Monitoring and Auditing</h3>
        <ul>
          <li><strong>CloudTrail Integration:</strong>
            <ul>
              <li>IAM API call logging</li>
              <li>User activity tracking</li>
              <li>Policy change monitoring</li>
              <li>Suspicious activity detection</li>
            </ul>
          </li>
          <li><strong>Access Pattern Analysis:</strong>
            <ul>
              <li>Unused permission identification</li>
              <li>Excessive privilege detection</li>
              <li>Anomalous access patterns</li>
              <li>Compliance violation alerts</li>
            </ul>
          </li>
          <li><strong>Automated Compliance Checking:</strong>
            <ul>
              <li>AWS Config rules for IAM</li>
              <li>Security Hub findings</li>
              <li>Custom compliance scripts</li>
              <li>Continuous assessment reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>IAM Troubleshooting and Optimization</h3>
        <ul>
          <li><strong>Access Denied Troubleshooting:</strong>
            <ul>
              <li>Policy simulator usage</li>
              <li>CloudTrail error analysis</li>
              <li>Permission boundary conflicts</li>
              <li>SCP restriction identification</li>
            </ul>
          </li>
          <li><strong>Performance Optimization:</strong>
            <ul>
              <li>Policy consolidation strategies</li>
              <li>Role assumption optimization</li>
              <li>Credential caching techniques</li>
              <li>API call rate management</li>
            </ul>
          </li>
          <li><strong>Governance and Lifecycle Management:</strong>
            <ul>
              <li>Automated user provisioning</li>
              <li>Access request workflows</li>
              <li>Regular access certification</li>
              <li>Deprovisioning procedures</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary advantage of using IAM roles over IAM users for applications?",
            options: [
              "Roles are cheaper than users",
              "Roles provide temporary credentials and eliminate long-term access keys",
              "Roles have more permissions than users",
              "Roles are easier to create than users"
            ],
            correctAnswer: 1,
            explanation: "IAM roles provide temporary credentials through the AWS Security Token Service (STS), eliminating the need for long-term access keys and reducing security risks associated with credential management."
          },
          {
            question: "In IAM policy evaluation, what happens when there is both an explicit Allow and an explicit Deny for the same action?",
            options: [
              "Allow takes precedence",
              "Deny takes precedence",
              "The most recent policy takes precedence",
              "The action is randomly allowed or denied"
            ],
            correctAnswer: 1,
            explanation: "In IAM policy evaluation, an explicit Deny always takes precedence over an explicit Allow. This ensures that security restrictions cannot be accidentally overridden."
          },
          {
            question: "What is the purpose of IAM permission boundaries?",
            options: [
              "To grant additional permissions to users",
              "To set the maximum permissions that an identity can have",
              "To encrypt IAM policies",
              "To backup IAM configurations"
            ],
            correctAnswer: 1,
            explanation: "IAM permission boundaries set the maximum permissions that an identity (user or role) can have, acting as a filter that allows only the permissions that are allowed by both the identity-based policy and the permission boundary."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
