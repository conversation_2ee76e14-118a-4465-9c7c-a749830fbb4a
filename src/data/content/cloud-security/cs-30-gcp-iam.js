/**
 * GCP IAM (Identity and Access Management) Module
 */

export const gcpIamContent = {
  id: "cs-30",
  pathId: "cloud-security",
  title: "GCP IAM (Identity and Access Management)",
  description: "Master Google Cloud Platform Identity and Access Management, including users, service accounts, roles, policies, and advanced IAM features for secure access control.",
  objectives: [
    "Understand GCP IAM fundamentals and components",
    "Learn identity types and service account management",
    "Master roles, permissions, and policy management",
    "Develop skills in advanced IAM features",
    "Learn IAM security best practices",
    "Implement comprehensive GCP IAM strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "GCP IAM Fundamentals",
      content: `
        <h2>Google Cloud IAM Overview</h2>
        <p>Google Cloud Identity and Access Management (IAM) provides fine-grained access control and visibility for centrally managing cloud resources and user access across GCP services.</p>
        
        <h3>IAM Core Components</h3>
        <ul>
          <li><strong>Identities (Who):</strong>
            <ul>
              <li>Google Accounts (individual users)</li>
              <li>Service accounts (applications and VMs)</li>
              <li>Google Groups (collections of users)</li>
              <li>Google Workspace and Cloud Identity domains</li>
            </ul>
          </li>
          <li><strong>Resources (What):</strong>
            <ul>
              <li>GCP projects, folders, and organizations</li>
              <li>Compute instances, storage buckets, databases</li>
              <li>Networks, load balancers, and other services</li>
              <li>Resource hierarchy and inheritance</li>
            </ul>
          </li>
          <li><strong>Roles (Which permissions):</strong>
            <ul>
              <li>Basic roles (Owner, Editor, Viewer)</li>
              <li>Predefined roles (service-specific)</li>
              <li>Custom roles (organization-specific)</li>
              <li>Role hierarchy and inheritance</li>
            </ul>
          </li>
        </ul>
        
        <h3>IAM Policy Structure</h3>
        <ul>
          <li><strong>Policy Binding:</strong>
            <ul>
              <li>Member (identity) specification</li>
              <li>Role assignment</li>
              <li>Conditional access (optional)</li>
              <li>Resource-level application</li>
            </ul>
          </li>
          <li><strong>Policy Inheritance:</strong>
            <ul>
              <li>Organization-level policies</li>
              <li>Folder-level inheritance</li>
              <li>Project-level policies</li>
              <li>Resource-level overrides</li>
            </ul>
          </li>
          <li><strong>Policy Evaluation:</strong>
            <ul>
              <li>Union of all applicable policies</li>
              <li>Allow-only model (no explicit deny)</li>
              <li>Conditional access evaluation</li>
              <li>Resource hierarchy traversal</li>
            </ul>
          </li>
        </ul>
        
        <h3>Service Accounts</h3>
        <ul>
          <li><strong>Service Account Types:</strong>
            <ul>
              <li>User-managed service accounts</li>
              <li>Default service accounts</li>
              <li>Google-managed service accounts</li>
              <li>Cross-project service accounts</li>
            </ul>
          </li>
          <li><strong>Authentication Methods:</strong>
            <ul>
              <li>Service account keys (JSON/P12)</li>
              <li>Application Default Credentials (ADC)</li>
              <li>Workload Identity (recommended)</li>
              <li>Instance metadata service</li>
            </ul>
          </li>
          <li><strong>Service Account Security:</strong>
            <ul>
              <li>Key rotation and lifecycle management</li>
              <li>Least privilege access</li>
              <li>Service account impersonation</li>
              <li>Monitoring and auditing</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced IAM Features and Security",
      content: `
        <h2>Advanced GCP IAM Features and Security Controls</h2>
        <p>Advanced IAM features provide enhanced security controls, fine-grained access management, and sophisticated authentication mechanisms for complex GCP environments.</p>
        
        <h3>Conditional Access and Context-Aware Access</h3>
        <ul>
          <li><strong>IAM Conditions:</strong>
            <ul>
              <li>Time-based access controls</li>
              <li>IP address and location restrictions</li>
              <li>Resource attribute conditions</li>
              <li>Request attribute filtering</li>
            </ul>
          </li>
          <li><strong>Context-Aware Access:</strong>
            <ul>
              <li>Device-based access controls</li>
              <li>User context evaluation</li>
              <li>Risk-based access decisions</li>
              <li>Adaptive security policies</li>
            </ul>
          </li>
          <li><strong>Access Levels:</strong>
            <ul>
              <li>Device trust levels</li>
              <li>IP subnet restrictions</li>
              <li>Geographic location controls</li>
              <li>Custom access level creation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Identity-Aware Proxy (IAP)</h3>
        <ul>
          <li><strong>Zero Trust Access:</strong>
            <ul>
              <li>Application-level access control</li>
              <li>Identity verification before access</li>
              <li>No VPN required</li>
              <li>Centralized access management</li>
            </ul>
          </li>
          <li><strong>IAP Configuration:</strong>
            <ul>
              <li>OAuth consent screen setup</li>
              <li>IAP-secured resource configuration</li>
              <li>Access policy definition</li>
              <li>User and group access management</li>
            </ul>
          </li>
          <li><strong>IAP Security Features:</strong>
            <ul>
              <li>JWT token validation</li>
              <li>Request header injection</li>
              <li>Session management</li>
              <li>Audit logging and monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Workload Identity and Federation</h3>
        <ul>
          <li><strong>Workload Identity:</strong>
            <ul>
              <li>Kubernetes service account binding</li>
              <li>Elimination of service account keys</li>
              <li>Automatic credential management</li>
              <li>Enhanced security posture</li>
            </ul>
          </li>
          <li><strong>Workload Identity Federation:</strong>
            <ul>
              <li>External identity provider integration</li>
              <li>AWS, Azure, and on-premises federation</li>
              <li>OIDC and SAML support</li>
              <li>Attribute-based access control</li>
            </ul>
          </li>
          <li><strong>Identity Pool Configuration:</strong>
            <ul>
              <li>Provider configuration</li>
              <li>Attribute mapping</li>
              <li>Condition-based access</li>
              <li>Service account impersonation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "IAM Best Practices and Monitoring",
      content: `
        <h2>GCP IAM Security Best Practices and Monitoring</h2>
        <p>Implementing IAM best practices and continuous monitoring ensures secure access management and helps maintain the principle of least privilege across GCP environments.</p>
        
        <h3>IAM Security Best Practices</h3>
        <ul>
          <li><strong>Principle of Least Privilege:</strong>
            <ul>
              <li>Grant minimum required permissions</li>
              <li>Use predefined roles when possible</li>
              <li>Regular access reviews and cleanup</li>
              <li>Time-bound access grants</li>
            </ul>
          </li>
          <li><strong>Service Account Management:</strong>
            <ul>
              <li>Avoid service account keys when possible</li>
              <li>Use Workload Identity for GKE</li>
              <li>Implement key rotation policies</li>
              <li>Monitor service account usage</li>
            </ul>
          </li>
          <li><strong>Role and Permission Management:</strong>
            <ul>
              <li>Use groups for permission assignment</li>
              <li>Create custom roles for specific needs</li>
              <li>Avoid basic roles in production</li>
              <li>Document role assignments and purposes</li>
            </ul>
          </li>
        </ul>
        
        <h3>IAM Monitoring and Auditing</h3>
        <ul>
          <li><strong>Cloud Audit Logs:</strong>
            <ul>
              <li>Admin activity logging</li>
              <li>Data access logging</li>
              <li>System event logging</li>
              <li>Policy violation tracking</li>
            </ul>
          </li>
          <li><strong>IAM Recommender:</strong>
            <ul>
              <li>Over-privileged role identification</li>
              <li>Unused permission detection</li>
              <li>Role optimization suggestions</li>
              <li>Security improvement recommendations</li>
            </ul>
          </li>
          <li><strong>Access Transparency:</strong>
            <ul>
              <li>Google access logging</li>
              <li>Support case access tracking</li>
              <li>Administrative access visibility</li>
              <li>Compliance and audit support</li>
            </ul>
          </li>
        </ul>
        
        <h3>IAM Troubleshooting and Optimization</h3>
        <ul>
          <li><strong>Permission Troubleshooting:</strong>
            <ul>
              <li>Policy Troubleshooter tool</li>
              <li>IAM policy analyzer</li>
              <li>Permission inheritance analysis</li>
              <li>Conditional access debugging</li>
            </ul>
          </li>
          <li><strong>Performance Optimization:</strong>
            <ul>
              <li>Policy structure optimization</li>
              <li>Group-based access management</li>
              <li>Conditional access efficiency</li>
              <li>Service account optimization</li>
            </ul>
          </li>
          <li><strong>Governance and Lifecycle Management:</strong>
            <ul>
              <li>Automated user provisioning</li>
              <li>Access request workflows</li>
              <li>Regular access certification</li>
              <li>Deprovisioning procedures</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the recommended authentication method for applications running on Google Kubernetes Engine (GKE)?",
            options: [
              "Service account keys",
              "Workload Identity",
              "Application Default Credentials",
              "OAuth 2.0 tokens"
            ],
            correctAnswer: 1,
            explanation: "Workload Identity is the recommended authentication method for GKE as it eliminates the need for service account keys and provides automatic credential management with enhanced security."
          },
          {
            question: "In GCP IAM, what happens when there are conflicting permissions at different levels of the resource hierarchy?",
            options: [
              "Deny takes precedence",
              "The most restrictive permission applies",
              "The union of all permissions is granted (allow-only model)",
              "The most recent permission takes precedence"
            ],
            correctAnswer: 2,
            explanation: "GCP IAM uses an allow-only model where the union of all applicable permissions is granted. There are no explicit deny permissions, and policies are additive across the resource hierarchy."
          },
          {
            question: "Which GCP service provides zero-trust access control for applications without requiring a VPN?",
            options: [
              "Cloud IAM",
              "Identity-Aware Proxy (IAP)",
              "Cloud Identity",
              "VPC Service Controls"
            ],
            correctAnswer: 1,
            explanation: "Identity-Aware Proxy (IAP) provides zero-trust access control for applications by verifying user identity and context before granting access, eliminating the need for traditional VPN solutions."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
