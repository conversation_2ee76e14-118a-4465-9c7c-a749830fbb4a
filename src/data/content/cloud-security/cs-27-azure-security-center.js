/**
 * Azure Security Center Module
 */

export const azureSecurityCenterContent = {
  id: "cs-27",
  pathId: "cloud-security",
  title: "Azure Security Center",
  description: "Master Microsoft Defender for Cloud (formerly Azure Security Center) and Azure Sentinel for comprehensive security monitoring, threat detection, and incident response.",
  objectives: [
    "Understand Microsoft Defender for Cloud capabilities",
    "Learn security posture management and recommendations",
    "Master threat protection and security alerts",
    "Develop skills in Azure Sentinel SIEM/SOAR",
    "Learn incident response and investigation",
    "Implement comprehensive security operations"
  ],
  difficulty: "Advanced",
  estimatedTime: 150,
  sections: [
    {
      title: "Microsoft Defender for Cloud",
      content: `
        <h2>Microsoft Defender for Cloud Overview</h2>
        <p>Microsoft Defender for Cloud (formerly Azure Security Center) provides unified security management and advanced threat protection across hybrid cloud workloads.</p>
        
        <h3>Core Capabilities</h3>
        <ul>
          <li><strong>Security Posture Management:</strong>
            <ul>
              <li>Continuous security assessment</li>
              <li>Secure Score and recommendations</li>
              <li>Security policy and compliance</li>
              <li>Asset inventory and discovery</li>
            </ul>
          </li>
          <li><strong>Threat Protection:</strong>
            <ul>
              <li>Advanced threat detection</li>
              <li>Security alerts and incidents</li>
              <li>Behavioral analytics and machine learning</li>
              <li>Threat intelligence integration</li>
            </ul>
          </li>
          <li><strong>Hybrid and Multi-Cloud Protection:</strong>
            <ul>
              <li>Azure, AWS, and GCP coverage</li>
              <li>On-premises server protection</li>
              <li>Kubernetes and container security</li>
              <li>IoT and edge device protection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Defender Plans and Coverage</h3>
        <ul>
          <li><strong>Defender for Servers:</strong>
            <ul>
              <li>Virtual machine threat detection</li>
              <li>Vulnerability assessment</li>
              <li>Just-in-time VM access</li>
              <li>Adaptive application controls</li>
            </ul>
          </li>
          <li><strong>Defender for App Service:</strong>
            <ul>
              <li>Web application threat detection</li>
              <li>Runtime protection</li>
              <li>Vulnerability scanning</li>
              <li>Attack pattern recognition</li>
            </ul>
          </li>
          <li><strong>Defender for Storage:</strong>
            <ul>
              <li>Malware detection in storage</li>
              <li>Suspicious access pattern detection</li>
              <li>Data exfiltration alerts</li>
              <li>Threat intelligence correlation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Recommendations and Remediation</h3>
        <ul>
          <li><strong>Recommendation Categories:</strong>
            <ul>
              <li>Compute and app recommendations</li>
              <li>Data and storage security</li>
              <li>Identity and access management</li>
              <li>Networking and connectivity</li>
            </ul>
          </li>
          <li><strong>Remediation Options:</strong>
            <ul>
              <li>Manual remediation steps</li>
              <li>Quick fix automation</li>
              <li>Logic App integration</li>
              <li>Azure Policy enforcement</li>
            </ul>
          </li>
          <li><strong>Compliance and Standards:</strong>
            <ul>
              <li>Azure Security Benchmark</li>
              <li>Regulatory compliance dashboard</li>
              <li>Custom compliance initiatives</li>
              <li>Continuous compliance monitoring</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Azure Sentinel SIEM and SOAR",
      content: `
        <h2>Azure Sentinel Security Information and Event Management</h2>
        <p>Azure Sentinel is a cloud-native SIEM and SOAR solution that provides intelligent security analytics and threat intelligence across the enterprise.</p>
        
        <h3>Sentinel Architecture and Components</h3>
        <ul>
          <li><strong>Data Collection and Ingestion:</strong>
            <ul>
              <li>Built-in data connectors</li>
              <li>Custom log ingestion via API</li>
              <li>Common Event Format (CEF) support</li>
              <li>Syslog and Windows Event forwarding</li>
            </ul>
          </li>
          <li><strong>Data Storage and Processing:</strong>
            <ul>
              <li>Azure Monitor Logs workspace</li>
              <li>Kusto Query Language (KQL)</li>
              <li>Data retention and archiving</li>
              <li>Real-time and batch processing</li>
            </ul>
          </li>
          <li><strong>Analytics and Detection:</strong>
            <ul>
              <li>Built-in analytics rules</li>
              <li>Custom detection rules</li>
              <li>Machine learning analytics</li>
              <li>Threat intelligence matching</li>
            </ul>
          </li>
        </ul>
        
        <h3>Threat Detection and Analytics</h3>
        <ul>
          <li><strong>Analytics Rule Types:</strong>
            <ul>
              <li>Scheduled query rules</li>
              <li>Microsoft security alerts</li>
              <li>Fusion correlation rules</li>
              <li>Machine learning behavioral analytics</li>
            </ul>
          </li>
          <li><strong>Detection Techniques:</strong>
            <ul>
              <li>Signature-based detection</li>
              <li>Anomaly detection</li>
              <li>User and entity behavior analytics (UEBA)</li>
              <li>Threat hunting queries</li>
            </ul>
          </li>
          <li><strong>Threat Intelligence Integration:</strong>
            <ul>
              <li>Microsoft threat intelligence</li>
              <li>Third-party threat feeds</li>
              <li>Custom indicator import</li>
              <li>Threat intelligence workbook</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Orchestration and Automation</h3>
        <ul>
          <li><strong>Playbooks and Automation:</strong>
            <ul>
              <li>Logic Apps-based playbooks</li>
              <li>Automated response actions</li>
              <li>Custom workflow creation</li>
              <li>Third-party integration</li>
            </ul>
          </li>
          <li><strong>Incident Management:</strong>
            <ul>
              <li>Incident creation and assignment</li>
              <li>Investigation and case management</li>
              <li>Collaboration and comments</li>
              <li>Status tracking and reporting</li>
            </ul>
          </li>
          <li><strong>Response Automation:</strong>
            <ul>
              <li>Automated containment actions</li>
              <li>User account management</li>
              <li>Network isolation procedures</li>
              <li>Evidence collection automation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Security Operations and Incident Response",
      content: `
        <h2>Security Operations Center and Incident Response</h2>
        <p>Effective security operations require comprehensive monitoring, investigation capabilities, and structured incident response processes using Azure security tools.</p>
        
        <h3>Security Monitoring and Investigation</h3>
        <ul>
          <li><strong>Security Dashboards:</strong>
            <ul>
              <li>Real-time security metrics</li>
              <li>Threat landscape visualization</li>
              <li>Compliance status monitoring</li>
              <li>Custom workbook creation</li>
            </ul>
          </li>
          <li><strong>Investigation Tools:</strong>
            <ul>
              <li>Investigation graph and timeline</li>
              <li>Entity behavior analysis</li>
              <li>Cross-resource correlation</li>
              <li>Evidence collection and preservation</li>
            </ul>
          </li>
          <li><strong>Threat Hunting:</strong>
            <ul>
              <li>Proactive threat hunting queries</li>
              <li>Hypothesis-driven investigations</li>
              <li>Custom hunting workbooks</li>
              <li>Hunting query sharing</li>
            </ul>
          </li>
        </ul>
        
        <h3>Incident Response Workflow</h3>
        <ul>
          <li><strong>Incident Detection and Triage:</strong>
            <ul>
              <li>Automated alert correlation</li>
              <li>Incident severity classification</li>
              <li>Initial impact assessment</li>
              <li>Assignment and escalation</li>
            </ul>
          </li>
          <li><strong>Investigation and Analysis:</strong>
            <ul>
              <li>Root cause analysis</li>
              <li>Attack timeline reconstruction</li>
              <li>Scope and impact determination</li>
              <li>Evidence documentation</li>
            </ul>
          </li>
          <li><strong>Containment and Recovery:</strong>
            <ul>
              <li>Threat containment strategies</li>
              <li>System isolation procedures</li>
              <li>Recovery and restoration</li>
              <li>Lessons learned documentation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Metrics and Reporting</h3>
        <ul>
          <li><strong>Key Performance Indicators:</strong>
            <ul>
              <li>Mean time to detection (MTTD)</li>
              <li>Mean time to response (MTTR)</li>
              <li>Security alert volume and trends</li>
              <li>Incident resolution metrics</li>
            </ul>
          </li>
          <li><strong>Compliance Reporting:</strong>
            <ul>
              <li>Regulatory compliance status</li>
              <li>Security control effectiveness</li>
              <li>Audit trail documentation</li>
              <li>Executive summary reports</li>
            </ul>
          </li>
          <li><strong>Continuous Improvement:</strong>
            <ul>
              <li>Security posture trending</li>
              <li>Process optimization</li>
              <li>Tool effectiveness analysis</li>
              <li>Training and skill development</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary purpose of the Secure Score in Microsoft Defender for Cloud?",
            options: [
              "To measure network performance",
              "To provide a numerical representation of security posture with actionable recommendations",
              "To calculate cloud costs",
              "To monitor application performance"
            ],
            correctAnswer: 1,
            explanation: "Secure Score provides a numerical representation of your security posture based on security recommendations, helping organizations understand their current security state and prioritize improvements."
          },
          {
            question: "Which query language is used in Azure Sentinel for data analysis and threat hunting?",
            options: [
              "SQL",
              "PowerShell",
              "Kusto Query Language (KQL)",
              "Python"
            ],
            correctAnswer: 2,
            explanation: "Azure Sentinel uses Kusto Query Language (KQL) for data analysis, creating custom analytics rules, threat hunting, and building workbooks and dashboards."
          },
          {
            question: "What is the primary benefit of using playbooks in Azure Sentinel?",
            options: [
              "Data storage optimization",
              "Automated security response and orchestration",
              "User interface customization",
              "Cost reduction"
            ],
            correctAnswer: 1,
            explanation: "Playbooks in Azure Sentinel provide automated security response and orchestration capabilities using Logic Apps, enabling consistent and rapid response to security incidents and threats."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
