/**
 * Introduction to Blue Teaming Module
 */

export const blueTeamIntroContent = {
  id: "bt-1",
  pathId: "blue-teaming",
  title: "Introduction to Blue Team Operations",
  description: "Master the fundamentals of defensive cybersecurity operations, threat detection, incident response, and security monitoring to protect organizations from cyber threats.",
  objectives: [
    "Understand the role and responsibilities of blue teams",
    "Learn defensive cybersecurity frameworks and methodologies",
    "Master threat detection and security monitoring principles",
    "Explore incident response and forensics fundamentals",
    "Understand security operations center (SOC) operations",
    "Learn collaboration with red teams and threat intelligence"
  ],
  difficulty: "Beginner",
  estimatedTime: 100,
  sections: [
    {
      title: "Blue Team Fundamentals",
      content: `
        <h2>Blue Team Fundamentals</h2>
        <p>Blue teams are defensive cybersecurity professionals responsible for protecting organizations from cyber threats through proactive monitoring, detection, and response activities.</p>
        
        <h3>Blue Team Core Responsibilities</h3>
        <ul>
          <li><strong>Threat Detection:</strong> Identifying potential security incidents and malicious activities</li>
          <li><strong>Security Monitoring:</strong> Continuous surveillance of systems and networks for anomalies</li>
          <li><strong>Incident Response:</strong> Coordinating response to confirmed security incidents</li>
          <li><strong>Threat Hunting:</strong> Proactively searching for hidden threats and APTs</li>
          <li><strong>Vulnerability Management:</strong> Identifying and remedying security weaknesses</li>
          <li><strong>Security Controls:</strong> Implementing and maintaining defensive mechanisms</li>
        </ul>
        
        <h3>Blue Team vs Red Team</h3>
        <ul>
          <li><strong>Blue Team (Defense):</strong>
            <ul>
              <li>Protect organizational assets and data</li>
              <li>Detect and respond to security incidents</li>
              <li>Maintain security infrastructure and controls</li>
              <li>Focus on prevention, detection, and recovery</li>
            </ul>
          </li>
          <li><strong>Red Team (Offense):</strong>
            <ul>
              <li>Simulate adversary attacks and tactics</li>
              <li>Test defensive capabilities and controls</li>
              <li>Identify security gaps and weaknesses</li>
              <li>Focus on exploitation and impact assessment</li>
            </ul>
          </li>
        </ul>
        
        <h3>Purple Team Collaboration</h3>
        <ul>
          <li><strong>Knowledge Sharing:</strong> Exchange of tactics, techniques, and procedures</li>
          <li><strong>Exercise Collaboration:</strong> Joint training and simulation exercises</li>
          <li><strong>Gap Analysis:</strong> Identifying and addressing defensive capability gaps</li>
          <li><strong>Continuous Improvement:</strong> Iterative enhancement of security posture</li>
        </ul>
        
        <h3>Blue Team Skill Requirements</h3>
        <ul>
          <li><strong>Technical Skills:</strong>
            <ul>
              <li>Network security and analysis</li>
              <li>System administration and hardening</li>
              <li>Security tools and technologies (SIEM, EDR, IDS)</li>
              <li>Scripting and automation</li>
              <li>Digital forensics and malware analysis</li>
            </ul>
          </li>
          <li><strong>Analytical Skills:</strong>
            <ul>
              <li>Log analysis and pattern recognition</li>
              <li>Threat intelligence analysis</li>
              <li>Risk assessment and prioritization</li>
              <li>Incident investigation and documentation</li>
            </ul>
          </li>
          <li><strong>Communication Skills:</strong>
            <ul>
              <li>Technical writing and documentation</li>
              <li>Stakeholder communication and reporting</li>
              <li>Cross-team collaboration</li>
              <li>Training and knowledge transfer</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Defensive Frameworks and Methodologies",
      content: `
        <h2>Defensive Frameworks and Methodologies</h2>
        <p>Structured frameworks provide blue teams with systematic approaches to cybersecurity defense and incident management.</p>
        
        <h3>NIST Cybersecurity Framework</h3>
        <ul>
          <li><strong>Identify:</strong>
            <ul>
              <li>Asset inventory and classification</li>
              <li>Risk assessment and business context</li>
              <li>Governance and risk management strategy</li>
            </ul>
          </li>
          <li><strong>Protect:</strong>
            <ul>
              <li>Access controls and identity management</li>
              <li>Data security and privacy protection</li>
              <li>Security awareness and training</li>
              <li>Protective technology deployment</li>
            </ul>
          </li>
          <li><strong>Detect:</strong>
            <ul>
              <li>Security monitoring and anomaly detection</li>
              <li>Continuous security monitoring capabilities</li>
              <li>Detection process and procedures</li>
            </ul>
          </li>
          <li><strong>Respond:</strong>
            <ul>
              <li>Incident response planning and procedures</li>
              <li>Communication during incidents</li>
              <li>Analysis and mitigation activities</li>
              <li>Improvements based on lessons learned</li>
            </ul>
          </li>
          <li><strong>Recover:</strong>
            <ul>
              <li>Recovery planning and implementation</li>
              <li>Business continuity and disaster recovery</li>
              <li>Communication during recovery</li>
            </ul>
          </li>
        </ul>
        
        <h3>MITRE D3FEND Framework</h3>
        <ul>
          <li><strong>Defensive Countermeasures:</strong> Catalog of defensive techniques</li>
          <li><strong>ATT&CK Alignment:</strong> Maps defensive techniques to adversary TTPs</li>
          <li><strong>Knowledge Graph:</strong> Relationships between threats and defenses</li>
          <li><strong>Digital Artifacts:</strong> Focus on observable defensive actions</li>
        </ul>
        
        <h3>SANS GIAC Frameworks</h3>
        <ul>
          <li><strong>FOR508:</strong> Advanced digital forensics and incident response</li>
          <li><strong>SEC504:</strong> Hacker tools, techniques, and incident handling</li>
          <li><strong>SEC511:</strong> Continuous monitoring and security operations</li>
          <li><strong>FOR572:</strong> Advanced network forensics and analysis</li>
        </ul>
        
        <h3>Kill Chain Defense</h3>
        <ul>
          <li><strong>Reconnaissance Defense:</strong> Limiting information disclosure</li>
          <li><strong>Weaponization Defense:</strong> Threat intelligence and signature detection</li>
          <li><strong>Delivery Defense:</strong> Email security and web filtering</li>
          <li><strong>Exploitation Defense:</strong> Vulnerability management and patching</li>
          <li><strong>Installation Defense:</strong> Endpoint protection and whitelisting</li>
          <li><strong>C2 Defense:</strong> Network monitoring and traffic analysis</li>
          <li><strong>Actions Defense:</strong> Data loss prevention and backup systems</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Security Operations Center (SOC)",
      content: `
        <h2>Security Operations Center (SOC)</h2>
        <p>A SOC is the centralized facility where blue teams monitor, detect, and respond to cybersecurity incidents 24/7.</p>
        
        <h3>SOC Structure and Roles</h3>
        <ul>
          <li><strong>SOC Analyst (Tier 1):</strong>
            <ul>
              <li>Initial alert triage and investigation</li>
              <li>Event monitoring and basic incident response</li>
              <li>Documentation and escalation procedures</li>
            </ul>
          </li>
          <li><strong>Senior SOC Analyst (Tier 2):</strong>
            <ul>
              <li>Advanced investigation and analysis</li>
              <li>Complex incident response and containment</li>
              <li>Threat hunting and pattern analysis</li>
            </ul>
          </li>
          <li><strong>SOC Lead/Manager (Tier 3):</strong>
            <ul>
              <li>Strategic planning and process improvement</li>
              <li>Cross-team coordination and communication</li>
              <li>Advanced threat analysis and forensics</li>
            </ul>
          </li>
          <li><strong>SOC Engineer:</strong>
            <ul>
              <li>Tool deployment and configuration</li>
              <li>Integration and automation development</li>
              <li>Performance tuning and optimization</li>
            </ul>
          </li>
        </ul>
        
        <h3>SOC Technologies and Tools</h3>
        <ul>
          <li><strong>SIEM (Security Information and Event Management):</strong>
            <ul>
              <li>Log aggregation and correlation</li>
              <li>Real-time monitoring and alerting</li>
              <li>Compliance reporting and dashboards</li>
            </ul>
          </li>
          <li><strong>SOAR (Security Orchestration, Automation, and Response):</strong>
            <ul>
              <li>Incident response automation</li>
              <li>Playbook execution and workflows</li>
              <li>Case management and tracking</li>
            </ul>
          </li>
          <li><strong>EDR (Endpoint Detection and Response):</strong>
            <ul>
              <li>Endpoint monitoring and visibility</li>
              <li>Threat detection and containment</li>
              <li>Forensic data collection and analysis</li>
            </ul>
          </li>
          <li><strong>Network Security Monitoring:</strong>
            <ul>
              <li>IDS/IPS (Intrusion Detection/Prevention Systems)</li>
              <li>Network traffic analysis (NTA)</li>
              <li>DNS and web proxy monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>SOC Processes and Procedures</h3>
        <ul>
          <li><strong>Alert Triage:</strong>
            <ul>
              <li>Initial alert assessment and prioritization</li>
              <li>False positive identification and tuning</li>
              <li>Escalation criteria and procedures</li>
            </ul>
          </li>
          <li><strong>Incident Investigation:</strong>
            <ul>
              <li>Evidence collection and preservation</li>
              <li>Timeline reconstruction and analysis</li>
              <li>Impact assessment and scope determination</li>
            </ul>
          </li>
          <li><strong>Response and Containment:</strong>
            <ul>
              <li>Immediate threat containment actions</li>
              <li>Coordination with IT and business teams</li>
              <li>Communication with stakeholders</li>
            </ul>
          </li>
          <li><strong>Documentation and Reporting:</strong>
            <ul>
              <li>Incident documentation and case management</li>
              <li>Metrics and KPI tracking</li>
              <li>Executive and regulatory reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>SOC Metrics and KPIs</h3>
        <ul>
          <li><strong>Detection Metrics:</strong>
            <ul>
              <li>Mean Time to Detection (MTTD)</li>
              <li>Alert volume and false positive rates</li>
              <li>Coverage and visibility metrics</li>
            </ul>
          </li>
          <li><strong>Response Metrics:</strong>
            <ul>
              <li>Mean Time to Response (MTTR)</li>
              <li>Mean Time to Containment (MTTC)</li>
              <li>Incident escalation rates</li>
            </ul>
          </li>
          <li><strong>Operational Metrics:</strong>
            <ul>
              <li>SLA compliance and availability</li>
              <li>Analyst productivity and efficiency</li>
              <li>Training completion and certifications</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Threat Detection and Monitoring",
      content: `
        <h2>Threat Detection and Monitoring</h2>
        <p>Effective threat detection requires continuous monitoring, analysis, and correlation of security events across the enterprise.</p>
        
        <h3>Detection Methodologies</h3>
        <ul>
          <li><strong>Signature-Based Detection:</strong>
            <ul>
              <li>Known threat patterns and indicators</li>
              <li>Rules and signatures for specific attacks</li>
              <li>Low false positive rates but limited to known threats</li>
            </ul>
          </li>
          <li><strong>Anomaly-Based Detection:</strong>
            <ul>
              <li>Baseline behavior establishment</li>
              <li>Statistical analysis and machine learning</li>
              <li>Detection of unknown and zero-day threats</li>
            </ul>
          </li>
          <li><strong>Behavioral Analysis:</strong>
            <ul>
              <li>User and entity behavior analytics (UEBA)</li>
              <li>Process and network behavior monitoring</li>
              <li>Detection of insider threats and APTs</li>
            </ul>
          </li>
          <li><strong>Threat Intelligence Integration:</strong>
            <ul>
              <li>IOC (Indicators of Compromise) matching</li>
              <li>TTPs (Tactics, Techniques, Procedures) detection</li>
              <li>Contextual threat information enrichment</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Sources for Detection</h3>
        <ul>
          <li><strong>Network Data:</strong>
            <ul>
              <li>Network flow and traffic analysis</li>
              <li>DNS queries and responses</li>
              <li>Web proxy and firewall logs</li>
            </ul>
          </li>
          <li><strong>Endpoint Data:</strong>
            <ul>
              <li>System and application logs</li>
              <li>Process creation and execution</li>
              <li>File system and registry changes</li>
            </ul>
          </li>
          <li><strong>Cloud and SaaS Data:</strong>
            <ul>
              <li>Cloud platform audit logs</li>
              <li>SaaS application activity logs</li>
              <li>Identity and access management logs</li>
            </ul>
          </li>
          <li><strong>Security Tool Data:</strong>
            <ul>
              <li>Antivirus and anti-malware alerts</li>
              <li>IDS/IPS notifications</li>
              <li>Vulnerability scan results</li>
            </ul>
          </li>
        </ul>
        
        <h3>Detection Use Cases</h3>
        <ul>
          <li><strong>Malware Detection:</strong>
            <ul>
              <li>File hash and signature matching</li>
              <li>Behavioral analysis and sandboxing</li>
              <li>Command and control communication</li>
            </ul>
          </li>
          <li><strong>Intrusion Detection:</strong>
            <ul>
              <li>Unauthorized access attempts</li>
              <li>Privilege escalation activities</li>
              <li>Lateral movement indicators</li>
            </ul>
          </li>
          <li><strong>Data Exfiltration:</strong>
            <ul>
              <li>Unusual data transfer patterns</li>
              <li>Unauthorized file access</li>
              <li>Compression and encryption activities</li>
            </ul>
          </li>
          <li><strong>Insider Threats:</strong>
            <ul>
              <li>Abnormal user behavior patterns</li>
              <li>Policy violations and access anomalies</li>
              <li>Privileged account misuse</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Blue Team SOC Operations Lab",
    description: "Hands-on experience with security monitoring, alert triage, and incident response procedures in a simulated SOC environment.",
    tasks: [
      {
        category: "SIEM Analysis",
        commands: [
          {
            command: "Analyze sample SIEM alerts for potential security incidents",
            description: "Practice alert triage and initial investigation techniques",
            hint: "Look for patterns, correlations, and indicators of compromise",
            expectedOutput: "Prioritized alerts with initial assessment and next steps"
          }
        ]
      },
      {
        category: "Log Analysis",
        commands: [
          {
            command: "grep -i 'failed login' /var/log/auth.log | head -20",
            description: "Analyze authentication logs for suspicious login attempts",
            hint: "Look for patterns in failed login attempts and source IPs",
            expectedOutput: "Authentication anomalies and potential brute force attempts"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary focus of blue team operations?",
      options: [
        "Exploiting vulnerabilities in target systems",
        "Defending against and responding to cyber threats",
        "Developing offensive security tools",
        "Conducting penetration testing exercises"
      ],
      correct: 1,
      explanation: "Blue teams primarily focus on defending against and responding to cyber threats through monitoring, detection, and incident response activities."
    },
    {
      question: "Which NIST Cybersecurity Framework function involves continuous monitoring for security events?",
      options: [
        "Identify",
        "Protect",
        "Detect",
        "Respond"
      ],
      correct: 2,
      explanation: "The 'Detect' function of the NIST Cybersecurity Framework focuses on continuous monitoring and detection of cybersecurity events."
    },
    {
      question: "What is the main advantage of behavioral analysis over signature-based detection?",
      options: [
        "Lower false positive rates",
        "Faster processing speed",
        "Detection of unknown and zero-day threats",
        "Simpler implementation and maintenance"
      ],
      correct: 2,
      explanation: "Behavioral analysis can detect unknown and zero-day threats by identifying abnormal behavior patterns, while signature-based detection only catches known threats."
    }
  ]
}; 