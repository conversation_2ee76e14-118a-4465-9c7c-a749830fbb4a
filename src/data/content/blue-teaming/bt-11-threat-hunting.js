/**
 * BT-11: Advanced Threat Hunting
 * Blue Teaming - Intermediate Phase
 */

export const threatHuntingContent = {
  id: 'bt-11',
  title: 'Advanced Threat Hunting',
  description: 'Master proactive threat hunting techniques including hypothesis development, hunting methodologies, and advanced analytics for detecting sophisticated threats.',
  category: 'blue-teaming',
  phase: 'intermediate',
  difficulty: 'intermediate',
  estimatedTime: 240, // 4 hours
  learningObjectives: [
    'Develop effective threat hunting hypotheses',
    'Implement structured hunting methodologies',
    'Master advanced hunting tools and techniques',
    'Analyze threat intelligence for hunting opportunities',
    'Create and maintain threat hunting programs'
  ],
  prerequisites: [
    'bt-04', // Advanced Threat Detection and Analysis
    'bt-09', // Security Operations Center Fundamentals
    'bt-10'  // Digital Forensics and Evidence Analysis
  ],
  sections: [
    {
      id: 'hunting-fundamentals',
      title: 'Threat Hunting Fundamentals',
      content: `
# Threat Hunting Fundamentals

## What is Threat Hunting?

Threat hunting is the proactive and iterative search through networks and datasets to detect and isolate advanced threats that evade existing security solutions.

### Key Characteristics:
- **Proactive** - Don't wait for alerts
- **Hypothesis-driven** - Based on threat intelligence
- **Iterative** - Continuous improvement process
- **Human-led** - Requires analyst expertise
- **Data-driven** - Relies on comprehensive data

## Threat Hunting vs. Traditional Security:

### Traditional Security:
- Reactive to known threats
- Signature-based detection
- Automated response
- High volume, low complexity

### Threat Hunting:
- Proactive threat discovery
- Behavioral analysis
- Human-driven investigation
- Low volume, high complexity

## The Threat Hunting Loop:

### 1. Hypothesis Development:
- Based on threat intelligence
- Informed by attack patterns
- Driven by environmental factors
- Focused on specific TTPs

### 2. Data Collection:
- Identify relevant data sources
- Gather necessary datasets
- Ensure data quality and completeness
- Consider data retention policies

### 3. Investigation:
- Apply analytical techniques
- Search for indicators
- Correlate across data sources
- Validate findings

### 4. Analysis and Validation:
- Confirm true positives
- Eliminate false positives
- Understand attack progression
- Assess impact and scope

### 5. Response and Improvement:
- Implement containment measures
- Update detection rules
- Share threat intelligence
- Refine hunting techniques

## Hunting Maturity Levels:

### Level 0 - Initial:
- Minimal hunting capability
- Reactive security posture
- Limited data sources
- Ad-hoc investigations

### Level 1 - Minimal:
- Basic hunting procedures
- Some threat intelligence integration
- Limited automation
- Informal processes

### Level 2 - Procedural:
- Documented hunting procedures
- Regular hunting activities
- Integrated threat intelligence
- Basic metrics and reporting

### Level 3 - Innovative:
- Advanced hunting techniques
- Custom analytics and tools
- Proactive threat research
- Comprehensive metrics

### Level 4 - Leading:
- Cutting-edge hunting capabilities
- Automated hunting workflows
- Threat intelligence production
- Industry leadership
      `,
      quiz: [
        {
          question: "What is the primary difference between threat hunting and traditional security monitoring?",
          options: [
            "Threat hunting uses more expensive tools",
            "Threat hunting is proactive while traditional monitoring is reactive",
            "Threat hunting only works on Windows systems",
            "Traditional monitoring is more accurate"
          ],
          correct: 1,
          explanation: "Threat hunting is proactive, seeking out threats before they trigger alerts, while traditional monitoring is reactive, responding to known indicators and signatures."
        }
      ]
    },
    {
      id: 'hunting-methodologies',
      title: 'Hunting Methodologies and Frameworks',
      content: `
# Hunting Methodologies and Frameworks

## MITRE ATT&CK Framework:

### Tactics, Techniques, and Procedures (TTPs):
- **Tactics** - High-level goals (e.g., Persistence, Privilege Escalation)
- **Techniques** - Methods to achieve tactics (e.g., Registry Run Keys)
- **Procedures** - Specific implementations by threat actors

### ATT&CK for Threat Hunting:
- Hypothesis development based on techniques
- Mapping detections to ATT&CK
- Gap analysis for coverage
- Threat actor profiling

## Hunting Methodologies:

### 1. Intelligence-Driven Hunting:
- Start with threat intelligence
- Focus on known adversary TTPs
- Use IOCs and behavioral indicators
- Validate intelligence through hunting

### 2. Situational Awareness Hunting:
- Monitor for environmental changes
- Detect configuration anomalies
- Identify unusual network patterns
- Investigate system modifications

### 3. Domain Expertise Hunting:
- Leverage specialized knowledge
- Focus on specific technologies
- Apply industry-specific threats
- Use custom detection logic

### 4. Hypothesis-Driven Hunting:
- Develop testable hypotheses
- Design experiments to test theories
- Collect and analyze evidence
- Draw conclusions and iterate

## Hunting Frameworks:

### The Hunting Cycle (Sqrrl):
1. **Hypothesis** - Create educated guess
2. **Investigate** - Search for evidence
3. **Uncover** - Discover new patterns
4. **Inform** - Update defenses

### PEAK Framework:
- **Prepare** - Gather tools and data
- **Execute** - Conduct the hunt
- **Act** - Respond to findings
- **Knowledge** - Document lessons learned

### Diamond Model:
- **Adversary** - Who is attacking
- **Infrastructure** - What they're using
- **Capability** - How they attack
- **Victim** - Who they target

## Hunting Use Cases:

### Common Hunting Scenarios:
- Lateral movement detection
- Persistence mechanism discovery
- Data exfiltration identification
- Command and control communication
- Privilege escalation activities

### Advanced Hunting Scenarios:
- Living-off-the-land techniques
- Fileless malware detection
- Supply chain compromises
- Insider threat identification
- Advanced persistent threats
      `,
      practicalExercise: {
        title: "ATT&CK-Based Hunting Campaign",
        description: "Design and execute a threat hunting campaign based on MITRE ATT&CK techniques.",
        tasks: [
          "Select ATT&CK technique for hunting focus",
          "Develop hunting hypothesis",
          "Identify required data sources",
          "Create hunting queries and analytics",
          "Execute hunting campaign and document findings"
        ]
      }
    },
    {
      id: 'hunting-tools',
      title: 'Advanced Hunting Tools and Techniques',
      content: `
# Advanced Hunting Tools and Techniques

## Hunting Platforms:

### 1. Microsoft Sentinel:
- KQL (Kusto Query Language)
- Built-in hunting queries
- Threat intelligence integration
- Automated hunting workflows

### 2. Splunk Enterprise Security:
- SPL (Search Processing Language)
- Threat hunting dashboard
- MITRE ATT&CK integration
- Custom hunting apps

### 3. Elastic Security:
- EQL (Event Query Language)
- Timeline analysis
- Machine learning detection
- Open-source hunting rules

### 4. IBM QRadar:
- AQL (Ariel Query Language)
- Hunting dashboard
- Watson for Cyber Security
- Custom hunting rules

## Hunting Query Languages:

### KQL (Kusto Query Language):
\`\`\`
SecurityEvent
| where TimeGenerated > ago(24h)
| where EventID == 4624
| where LogonType == 10
| summarize count() by Account, Computer
| where count_ > 10
\`\`\`

### SPL (Search Processing Language):
\`\`\`
index=windows EventCode=4624 LogonType=10
| stats count by user dest
| where count > 10
\`\`\`

### EQL (Event Query Language):
\`\`\`
process where process_name == "powershell.exe"
| filter command_line contains "DownloadString"
\`\`\`

## Advanced Analytics:

### Statistical Analysis:
- Frequency analysis
- Outlier detection
- Trend analysis
- Correlation analysis

### Machine Learning:
- Anomaly detection
- Clustering analysis
- Classification models
- Behavioral baselines

### Graph Analysis:
- Network relationship mapping
- Communication pattern analysis
- Lateral movement visualization
- Attack path reconstruction

## Hunting Techniques:

### 1. Stack Counting:
- Group similar events
- Identify rare occurrences
- Focus on outliers
- Investigate anomalies

### 2. Clustering:
- Group related activities
- Identify patterns
- Detect coordinated attacks
- Find campaign indicators

### 3. Grouping:
- Aggregate by attributes
- Compare time periods
- Identify trends
- Spot deviations

### 4. Scoring:
- Risk-based prioritization
- Weighted indicators
- Composite scores
- Threshold-based alerting

## Data Sources for Hunting:

### Network Data:
- DNS logs
- Proxy logs
- Firewall logs
- Network flows

### Endpoint Data:
- Process execution
- File system changes
- Registry modifications
- Network connections

### Authentication Data:
- Login events
- Privilege changes
- Account modifications
- Access patterns

### Application Data:
- Web server logs
- Database logs
- Email logs
- Cloud service logs
      `,
      lab: {
        title: "Multi-Source Threat Hunting Lab",
        description: "Conduct comprehensive threat hunting using multiple data sources and tools.",
        environment: "SIEM platform with diverse log sources",
        tasks: [
          "Develop hunting hypothesis for lateral movement",
          "Query multiple data sources for evidence",
          "Correlate findings across different logs",
          "Create timeline of attacker activities",
          "Generate hunting report with recommendations"
        ]
      }
    }
  ],
  resources: [
    {
      type: 'framework',
      title: 'MITRE ATT&CK Framework',
      url: 'https://attack.mitre.org/'
    },
    {
      type: 'guide',
      title: 'SANS Threat Hunting Guide',
      url: 'https://www.sans.org/white-papers/threat-hunting/'
    },
    {
      type: 'tool',
      title: 'Sigma Detection Rules',
      url: 'https://github.com/SigmaHQ/sigma'
    }
  ],
  assessment: {
    type: 'hunting-campaign',
    title: 'Comprehensive Threat Hunting Campaign',
    description: 'Design and execute a complete threat hunting campaign targeting a specific threat actor or technique.',
    requirements: [
      'Threat intelligence research and hypothesis development',
      'Multi-source data analysis and correlation',
      'Advanced analytics and custom detection rules',
      'Timeline reconstruction and attack path analysis',
      'Comprehensive hunting report and recommendations'
    ]
  },
  tags: ['threat-hunting', 'mitre-attack', 'analytics', 'proactive-defense', 'threat-intelligence'],
  lastUpdated: '2024-12-19'
};
