/**
 * Defensive Cybersecurity Fundamentals Module
 */

export const defensiveFundamentalsContent = {
  id: "bt-01",
  pathId: "blue-teaming",
  title: "Defensive Cybersecurity Fundamentals",
  description: "Master the foundational concepts of defensive cybersecurity including security principles, defense-in-depth strategies, and core defensive methodologies.",
  category: 'blue-teaming',
  phase: 'foundation',
  objectives: [
    "Understand fundamental cybersecurity principles and concepts",
    "Learn defense-in-depth strategies and layered security",
    "Master the cybersecurity framework and risk management",
    "Explore threat landscape and attack methodologies",
    "Understand security controls and defensive technologies",
    "Learn incident response and business continuity basics"
  ],
  difficulty: "Beginner",
  estimatedTime: 240,
  sections: [
    {
      title: "Cybersecurity Principles and Core Concepts",
      content: `
        <h2>Fundamental Security Principles</h2>
        <p>Defensive cybersecurity is built upon core principles that guide the design, implementation, and management of security controls and processes.</p>
        
        <h3>CIA Triad and Security Objectives</h3>
        <ul>
          <li><strong>Confidentiality:</strong>
            <ul>
              <li>Information protection from unauthorized disclosure</li>
              <li>Access control and data classification</li>
              <li>Encryption and data masking techniques</li>
              <li>Privacy protection and data handling</li>
              <li>Need-to-know and least privilege principles</li>
            </ul>
          </li>
          <li><strong>Integrity:</strong>
            <ul>
              <li>Data accuracy and completeness assurance</li>
              <li>Change control and version management</li>
              <li>Digital signatures and checksums</li>
              <li>Input validation and sanitization</li>
              <li>Audit trails and logging mechanisms</li>
            </xs>
          </li>
          <li><strong>Availability:</strong>
            <ul>
              <li>System and service accessibility</li>
              <li>Redundancy and fault tolerance</li>
              <li>Disaster recovery and business continuity</li>
              <li>Performance monitoring and optimization</li>
              <li>Capacity planning and resource management</li>
            </xs>
          </li>
        </ul>
        
        <h3>Extended Security Principles</h3>
        <ul>
          <li><strong>Authentication and Authorization:</strong>
            <ul>
              <li>Identity verification and validation</li>
              <li>Multi-factor authentication mechanisms</li>
              <li>Role-based and attribute-based access control</li>
              <li>Privilege escalation prevention</li>
              <li>Session management and timeout controls</li>
            </xs>
          </li>
          <li><strong>Non-repudiation and Accountability:</strong>
            <ul>
              <li>Digital signatures and proof of origin</li>
              <li>Audit logging and evidence collection</li>
              <li>Time stamping and chronological records</li>
              <li>Legal admissibility and forensic readiness</li>
              <li>Compliance and regulatory requirements</li>
            </xs>
          </li>
          <li><strong>Privacy and Data Protection:</strong>
            <ul>
              <li>Personal data identification and classification</li>
              <li>Data minimization and purpose limitation</li>
              <li>Consent management and user rights</li>
              <li>Cross-border data transfer restrictions</li>
              <li>Privacy by design and default principles</li>
            </xs>
          </li>
        </ul>
        
        <h3>Security Governance and Risk Management</h3>
        <ul>
          <li><strong>Risk Assessment and Analysis:</strong>
            <ul>
              <li>Asset identification and valuation</li>
              <li>Threat and vulnerability assessment</li>
              <li>Risk calculation and prioritization</li>
              <li>Risk treatment and mitigation strategies</li>
              <li>Residual risk acceptance and monitoring</li>
            </xs>
          </li>
          <li><strong>Security Policies and Procedures:</strong>
            <ul>
              <li>Policy development and documentation</li>
              <li>Standard operating procedures</li>
              <li>Guidelines and best practices</li>
              <li>Training and awareness programs</li>
              <li>Compliance monitoring and enforcement</li>
            </xs>
          </li>
          <li><strong>Security Frameworks and Standards:</strong>
            <ul>
              <li>NIST Cybersecurity Framework</li>
              <li>ISO 27001/27002 security standards</li>
              <li>COBIT governance framework</li>
              <li>Industry-specific regulations and standards</li>
              <li>Maturity models and assessment tools</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Defense-in-Depth and Layered Security",
      content: `
        <h2>Multi-Layered Defense Strategies</h2>
        <p>Defense-in-depth provides multiple layers of security controls to protect against various attack vectors and ensure comprehensive protection.</p>
        
        <h3>Physical Security Layer</h3>
        <ul>
          <li><strong>Facility and Perimeter Security:</strong>
            <ul>
              <li>Building access control and monitoring</li>
              <li>Visitor management and escort procedures</li>
              <li>Surveillance systems and intrusion detection</li>
              <li>Environmental controls and protection</li>
              <li>Secure disposal and destruction procedures</li>
            </xs>
          </li>
          <li><strong>Hardware and Device Security:</strong>
            <ul>
              <li>Asset inventory and tracking systems</li>
              <li>Device encryption and secure boot</li>
              <li>Tamper detection and response</li>
              <li>Secure configuration and hardening</li>
              <li>End-of-life and disposal procedures</li>
            </xs>
          </li>
        </ul>
        
        <h3>Network Security Layer</h3>
        <ul>
          <li><strong>Perimeter Defense:</strong>
            <ul>
              <li>Firewalls and packet filtering</li>
              <li>Intrusion detection and prevention systems</li>
              <li>Web application firewalls</li>
              <li>DDoS protection and mitigation</li>
              <li>VPN and remote access security</li>
            </xs>
          </li>
          <li><strong>Internal Network Security:</strong>
            <ul>
              <li>Network segmentation and micro-segmentation</li>
              <li>VLAN isolation and access control</li>
              <li>Network access control (NAC)</li>
              <li>Wireless security and monitoring</li>
              <li>Network monitoring and traffic analysis</li>
            </xs>
          </li>
        </ul>
        
        <h3>System and Application Security Layer</h3>
        <ul>
          <li><strong>Operating System Security:</strong>
            <ul>
              <li>System hardening and configuration</li>
              <li>Patch management and vulnerability remediation</li>
              <li>Access control and privilege management</li>
              <li>Antivirus and endpoint protection</li>
              <li>System monitoring and logging</li>
            </xs>
          </li>
          <li><strong>Application Security:</strong>
            <ul>
              <li>Secure development lifecycle (SDLC)</li>
              <li>Input validation and output encoding</li>
              <li>Authentication and session management</li>
              <li>Application firewalls and runtime protection</li>
              <li>Code review and security testing</li>
            </xs>
          </li>
        </ul>
        
        <h3>Data Security Layer</h3>
        <ul>
          <li><strong>Data Classification and Handling:</strong>
            <ul>
              <li>Data discovery and classification</li>
              <li>Data loss prevention (DLP) systems</li>
              <li>Encryption at rest and in transit</li>
              <li>Key management and rotation</li>
              <li>Data retention and disposal policies</li>
            </xs>
          </li>
          <li><strong>Database and Storage Security:</strong>
            <ul>
              <li>Database access control and monitoring</li>
              <li>Database activity monitoring (DAM)</li>
              <li>Backup and recovery procedures</li>
              <li>Storage encryption and protection</li>
              <li>Cloud storage security controls</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Threat Landscape and Attack Methodologies",
      content: `
        <h2>Understanding the Threat Environment</h2>
        <p>Effective defense requires comprehensive understanding of the threat landscape, attack vectors, and adversary tactics, techniques, and procedures.</p>
        
        <h3>Threat Actor Categories</h3>
        <ul>
          <li><strong>Nation-State Actors:</strong>
            <ul>
              <li>Advanced persistent threats (APTs)</li>
              <li>Espionage and intelligence gathering</li>
              <li>Critical infrastructure targeting</li>
              <li>Long-term strategic objectives</li>
              <li>Sophisticated tools and techniques</li>
            </xs>
          </li>
          <li><strong>Cybercriminal Organizations:</strong>
            <ul>
              <li>Financial motivation and profit</li>
              <li>Ransomware and extortion schemes</li>
              <li>Identity theft and fraud</li>
              <li>Organized crime syndicates</li>
              <li>Cryptocurrency and money laundering</li>
            </xs>
          </li>
          <li><strong>Hacktivists and Ideological Groups:</strong>
            <ul>
              <li>Political and social motivations</li>
              <li>Website defacement and disruption</li>
              <li>Data leaks and whistleblowing</li>
              <li>Distributed denial of service attacks</li>
              <li>Public awareness and protest activities</li>
            </xs>
          </li>
        </ul>
        
        <h3>Attack Vectors and Methods</h3>
        <ul>
          <li><strong>Social Engineering Attacks:</strong>
            <ul>
              <li>Phishing and spear phishing campaigns</li>
              <li>Pretexting and impersonation</li>
              <li>Baiting and quid pro quo attacks</li>
              <li>Tailgating and physical manipulation</li>
              <li>Business email compromise (BEC)</li>
            </xs>
          </li>
          <li><strong>Technical Attack Methods:</strong>
            <ul>
              <li>Malware and ransomware deployment</li>
              <li>Vulnerability exploitation and zero-days</li>
              <li>Network intrusion and lateral movement</li>
              <li>Privilege escalation and persistence</li>
              <li>Data exfiltration and command and control</li>
            </xs>
          </li>
          <li><strong>Supply Chain and Third-Party Attacks:</strong>
            <ul>
              <li>Software supply chain compromise</li>
              <li>Hardware tampering and backdoors</li>
              <li>Vendor and partner security risks</li>
              <li>Cloud service provider vulnerabilities</li>
              <li>Managed service provider attacks</li>
            </xs>
          </li>
        </ul>
        
        <h3>MITRE ATT&CK Framework</h3>
        <ul>
          <li><strong>Tactics, Techniques, and Procedures:</strong>
            <ul>
              <li>Initial access and execution methods</li>
              <li>Persistence and privilege escalation</li>
              <li>Defense evasion and credential access</li>
              <li>Discovery and lateral movement</li>
              <li>Collection, exfiltration, and impact</li>
            </xs>
          </li>
          <li><strong>Threat Intelligence Integration:</strong>
            <ul>
              <li>Adversary behavior mapping</li>
              <li>Indicator of compromise (IOC) analysis</li>
              <li>Threat hunting and detection rules</li>
              <li>Security control gap analysis</li>
              <li>Red team and purple team exercises</li>
            </xs>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Defensive Security Fundamentals Lab",
    description: "Hands-on exercise in implementing basic defensive security controls and conducting risk assessment.",
    tasks: [
      {
        category: "Risk Assessment",
        commands: [
          {
            command: "Conduct basic asset inventory and classification",
            description: "Identify and classify critical assets in a sample environment",
            hint: "Use asset discovery tools and create classification matrix",
            expectedOutput: "Complete asset inventory with risk classifications"
          },
          {
            command: "Perform threat modeling exercise",
            description: "Create threat model for a web application using STRIDE methodology",
            hint: "Identify threats, vulnerabilities, and potential impacts",
            expectedOutput: "Comprehensive threat model with mitigation strategies"
          }
        ]
      },
      {
        category: "Defense Implementation",
        commands: [
          {
            command: "Configure basic firewall rules",
            description: "Implement network segmentation using firewall rules",
            hint: "Create rules for different network zones and services",
            expectedOutput: "Functional firewall configuration with documented rules"
          },
          {
            command: "Set up basic logging and monitoring",
            description: "Configure system and security event logging",
            hint: "Enable appropriate log sources and configure retention",
            expectedOutput: "Centralized logging system with security event collection"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which security principle ensures that data has not been altered or corrupted?",
      options: [
        "Confidentiality",
        "Integrity",
        "Availability",
        "Authentication"
      ],
      correct: 1,
      explanation: "Integrity ensures that data remains accurate, complete, and unaltered during storage, transmission, and processing. It protects against unauthorized modification, corruption, or destruction of information."
    },
    {
      question: "What is the primary purpose of defense-in-depth strategy?",
      options: [
        "To reduce security costs",
        "To provide multiple layers of security controls",
        "To simplify security management",
        "To eliminate all security risks"
      ],
      correct: 1,
      explanation: "Defense-in-depth provides multiple layers of security controls so that if one layer fails, other layers continue to provide protection. This layered approach significantly improves overall security posture."
    },
    {
      question: "Which threat actor category is typically motivated by financial gain?",
      options: [
        "Nation-state actors",
        "Hacktivists",
        "Cybercriminal organizations",
        "Script kiddies"
      ],
      correct: 2,
      explanation: "Cybercriminal organizations are primarily motivated by financial gain through activities like ransomware, fraud, identity theft, and other profit-driven cybercrimes."
    }
  ]
};
