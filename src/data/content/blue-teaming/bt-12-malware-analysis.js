/**
 * BT-12: Malware Analysis and Reverse Engineering
 * Blue Teaming - Intermediate Phase
 */

export const malwareAnalysisContent = {
  id: 'bt-12',
  title: 'Malware Analysis and Reverse Engineering',
  description: 'Learn comprehensive malware analysis techniques including static and dynamic analysis, reverse engineering, and malware family classification.',
  category: 'blue-teaming',
  phase: 'intermediate',
  difficulty: 'intermediate',
  estimatedTime: 300, // 5 hours
  learningObjectives: [
    'Understand malware analysis fundamentals and methodologies',
    'Perform static analysis of malicious samples',
    'Conduct dynamic analysis in controlled environments',
    'Apply reverse engineering techniques to understand malware behavior',
    'Develop malware signatures and detection rules'
  ],
  prerequisites: [
    'bt-07', // Endpoint Security and Protection
    'bt-10', // Digital Forensics and Evidence Analysis
    'bt-11'  // Advanced Threat Hunting
  ],
  sections: [
    {
      id: 'malware-fundamentals',
      title: 'Malware Analysis Fundamentals',
      content: `
# Malware Analysis Fundamentals

## What is Malware Analysis?

Malware analysis is the process of understanding the behavior and purpose of a suspicious file or URL. The goal is to provide the information needed to respond to a network intrusion.

### Types of Analysis:
- **Static Analysis** - Examining code without execution
- **Dynamic Analysis** - Observing behavior during execution
- **Hybrid Analysis** - Combining static and dynamic techniques
- **Code Analysis** - Reverse engineering and disassembly

### Analysis Objectives:
- Understand malware functionality
- Identify indicators of compromise (IOCs)
- Determine attack vectors and payloads
- Assess potential impact and damage
- Develop detection and mitigation strategies

## Malware Categories:

### By Propagation Method:
- **Virus** - Self-replicating code
- **Worm** - Network-spreading malware
- **Trojan** - Disguised malicious software
- **Rootkit** - Stealth and persistence focused

### By Payload:
- **Ransomware** - Data encryption for extortion
- **Spyware** - Information gathering
- **Adware** - Unwanted advertisement display
- **Backdoor** - Remote access provision

### By Target:
- **File Infectors** - Modify executable files
- **Boot Sector** - Infect system boot process
- **Macro** - Embedded in documents
- **Script-based** - PowerShell, JavaScript, etc.

## Analysis Environment Setup:

### Isolated Analysis Lab:
- Virtual machines for containment
- Network isolation and monitoring
- Snapshot and rollback capabilities
- Multiple OS environments

### Essential Tools:
- **Static Analysis**: Strings, file, hexdump, PEiD
- **Dynamic Analysis**: Process Monitor, Wireshark, Regshot
- **Disassemblers**: IDA Pro, Ghidra, x64dbg
- **Sandboxes**: Cuckoo, Joe Sandbox, Any.run

### Safety Considerations:
- Never analyze on production systems
- Use isolated networks
- Maintain proper backups
- Follow incident response procedures
      `,
      quiz: [
        {
          question: "What is the primary advantage of static malware analysis?",
          options: [
            "It shows real-time behavior",
            "It can analyze code without executing it safely",
            "It's faster than dynamic analysis",
            "It provides network traffic analysis"
          ],
          correct: 1,
          explanation: "Static analysis allows examination of malware code without execution, making it safer and able to reveal code structure and potential capabilities."
        }
      ]
    },
    {
      id: 'static-analysis',
      title: 'Static Analysis Techniques',
      content: `
# Static Analysis Techniques

## File Format Analysis:

### Portable Executable (PE) Analysis:
- **Headers**: DOS, NT, Optional headers
- **Sections**: .text, .data, .rsrc, .reloc
- **Import/Export Tables**: API dependencies
- **Resources**: Icons, strings, version info

### Analysis Tools:
- **PEiD** - Packer and compiler detection
- **PE Explorer** - PE file analysis
- **CFF Explorer** - Comprehensive PE editor
- **pestudio** - Malware initial assessment

## String Analysis:

### String Extraction:
\`\`\`
strings malware.exe | grep -i "http"
strings -e l malware.exe  # Unicode strings
strings -n 10 malware.exe # Minimum length 10
\`\`\`

### Interesting Strings:
- URLs and IP addresses
- File paths and registry keys
- API function names
- Error messages and debug info
- Encryption keys and passwords

## Cryptographic Analysis:

### Hash Calculation:
\`\`\`
md5sum malware.exe
sha1sum malware.exe
sha256sum malware.exe
ssdeep malware.exe  # Fuzzy hashing
\`\`\`

### Digital Signatures:
- Certificate validation
- Signature verification
- Timestamp analysis
- Certificate authority trust

## Packer Detection:

### Common Packers:
- **UPX** - Ultimate Packer for eXecutables
- **ASPack** - Advanced Software Protection
- **Themida** - Advanced Windows software protection
- **VMProtect** - Code virtualization

### Unpacking Techniques:
- Automated unpacking tools
- Manual unpacking methods
- Memory dumping
- API monitoring

## Disassembly Analysis:

### Assembly Language Basics:
- x86/x64 instruction sets
- Calling conventions
- Stack operations
- Control flow analysis

### Key Analysis Areas:
- Entry point identification
- Function prologue/epilogue
- API call analysis
- String references
- Control flow graphs

## Behavioral Indicators:

### File System Operations:
- File creation/modification
- Directory traversal
- Temporary file usage
- System file access

### Registry Operations:
- Key creation/modification
- Autostart entries
- Configuration storage
- Persistence mechanisms

### Network Indicators:
- Domain names and IPs
- URL patterns
- Protocol usage
- Communication encryption
      `,
      practicalExercise: {
        title: "Static Malware Analysis Lab",
        description: "Perform comprehensive static analysis of a malware sample.",
        tasks: [
          "Calculate file hashes and check reputation",
          "Analyze PE structure and sections",
          "Extract and analyze strings",
          "Identify imported APIs and functions",
          "Document static indicators of compromise"
        ]
      }
    },
    {
      id: 'dynamic-analysis',
      title: 'Dynamic Analysis and Behavioral Monitoring',
      content: `
# Dynamic Analysis and Behavioral Monitoring

## Dynamic Analysis Setup:

### Sandbox Environment:
- Isolated virtual machines
- Network monitoring capabilities
- System state snapshots
- Automated analysis tools

### Monitoring Tools:
- **Process Monitor** - File/registry/process activity
- **Process Explorer** - Running process analysis
- **Wireshark** - Network traffic capture
- **Regshot** - Registry change detection

## Execution Monitoring:

### Process Analysis:
- Process creation and termination
- Parent-child relationships
- Command line arguments
- Memory usage patterns

### File System Monitoring:
- File creation, modification, deletion
- Directory changes
- Temporary file usage
- System file modifications

### Registry Monitoring:
- Key creation and modification
- Value changes
- Permission modifications
- Autostart entry creation

### Network Monitoring:
- DNS queries and responses
- HTTP/HTTPS communications
- TCP/UDP connections
- Protocol analysis

## Behavioral Analysis Techniques:

### API Monitoring:
- Function call tracing
- Parameter analysis
- Return value examination
- Call frequency analysis

### Memory Analysis:
- Heap and stack examination
- Code injection detection
- Memory protection changes
- Process hollowing identification

### Evasion Detection:
- Anti-analysis techniques
- Sandbox detection
- Debugger detection
- Virtual machine detection

## Automated Sandbox Analysis:

### Cuckoo Sandbox:
- Open-source malware analysis
- Automated behavioral analysis
- Network traffic analysis
- Memory dump analysis

### Commercial Sandboxes:
- **Joe Sandbox** - Advanced malware analysis
- **FireEye AX** - Network security platform
- **Falcon Sandbox** - CrowdStrike analysis
- **Any.run** - Interactive online sandbox

### Analysis Reports:
- Executive summary
- Technical details
- IOC extraction
- MITRE ATT&CK mapping

## Advanced Dynamic Techniques:

### Kernel-Level Monitoring:
- System call tracing
- Driver analysis
- Rootkit detection
- Hypervisor-based analysis

### Anti-Evasion Techniques:
- Environment modification
- Timing manipulation
- User interaction simulation
- Network service emulation
      `,
      lab: {
        title: "Dynamic Malware Analysis Lab",
        description: "Conduct dynamic analysis of malware in a controlled environment.",
        environment: "Isolated VM with monitoring tools",
        tasks: [
          "Set up monitoring tools and baseline system",
          "Execute malware sample safely",
          "Monitor and document behavioral changes",
          "Analyze network communications",
          "Generate comprehensive analysis report"
        ]
      }
    }
  ],
  resources: [
    {
      type: 'tool',
      title: 'Ghidra Reverse Engineering Tool',
      url: 'https://ghidra-sre.org/'
    },
    {
      type: 'platform',
      title: 'Cuckoo Sandbox',
      url: 'https://cuckoosandbox.org/'
    },
    {
      type: 'guide',
      title: 'SANS Malware Analysis Guide',
      url: 'https://www.sans.org/white-papers/malware-analysis/'
    }
  ],
  assessment: {
    type: 'analysis-report',
    title: 'Complete Malware Analysis Report',
    description: 'Perform comprehensive static and dynamic analysis of an unknown malware sample.',
    requirements: [
      'Static analysis with PE structure examination',
      'Dynamic analysis with behavioral monitoring',
      'IOC extraction and signature development',
      'MITRE ATT&CK technique mapping',
      'Professional analysis report with recommendations'
    ]
  },
  tags: ['malware-analysis', 'reverse-engineering', 'static-analysis', 'dynamic-analysis', 'sandbox'],
  lastUpdated: '2024-12-19'
};
