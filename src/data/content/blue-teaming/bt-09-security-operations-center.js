/**
 * BT-09: Security Operations Center (SOC) Fundamentals
 * Blue Teaming - Foundation Phase
 */

export const securityOperationsCenterContent = {
  id: 'bt-09',
  title: 'Security Operations Center (SOC) Fundamentals',
  description: 'Master SOC operations including organizational structure, processes, technologies, and best practices for effective security monitoring and incident response.',
  category: 'blue-teaming',
  phase: 'foundation',
  difficulty: 'beginner',
  estimatedTime: 180, // 3 hours
  learningObjectives: [
    'Understand SOC organizational structure and roles',
    'Learn SOC processes and operational procedures',
    'Implement SOC technologies and tool integration',
    'Develop effective SOC metrics and KPIs',
    'Establish SOC maturity and improvement programs'
  ],
  prerequisites: [
    'bt-02', // Security Monitoring and Event Management
    'bt-03', // Incident Response and Crisis Management
    'bt-04'  // Advanced Threat Detection and Analysis
  ],
  sections: [
    {
      id: 'soc-fundamentals',
      title: 'SOC Structure and Organization',
      content: `
# SOC Structure and Organization

## What is a Security Operations Center?

A Security Operations Center (SOC) is a centralized unit that deals with security issues on an organizational and technical level. It serves as the nerve center for monitoring, detecting, analyzing, and responding to cybersecurity incidents.

### SOC Functions:
- **24/7 Security Monitoring**
- **Incident Detection and Response**
- **Threat Intelligence Analysis**
- **Vulnerability Management**
- **Compliance Monitoring**

## SOC Organizational Models:

### 1. In-House SOC:
- Full control and customization
- Higher initial investment
- Direct staff management
- Complete data control

### 2. Outsourced SOC (Managed Security Service Provider):
- Lower initial costs
- Immediate expertise access
- Shared resources
- Less direct control

### 3. Hybrid SOC:
- Combines in-house and outsourced elements
- Flexible resource allocation
- Balanced cost and control
- Scalable operations

### 4. Virtual SOC:
- Distributed team model
- Cloud-based infrastructure
- Remote collaboration tools
- Cost-effective for smaller organizations

## SOC Team Structure:

### Tier 1 - SOC Analysts:
- Alert monitoring and triage
- Initial incident classification
- Basic threat hunting
- Documentation and escalation

### Tier 2 - Senior SOC Analysts:
- Deep dive investigations
- Advanced threat analysis
- Incident response coordination
- Tool configuration and tuning

### Tier 3 - SOC Engineers/Experts:
- Complex incident handling
- Threat hunting and research
- Tool development and integration
- Process improvement
      `,
      quiz: [
        {
          question: "What is the primary responsibility of Tier 1 SOC analysts?",
          options: [
            "Complex malware analysis",
            "Alert monitoring and initial triage",
            "Threat hunting research",
            "Tool development"
          ],
          correct: 1,
          explanation: "Tier 1 SOC analysts are responsible for monitoring alerts, performing initial triage, and escalating incidents to higher tiers when necessary."
        }
      ]
    },
    {
      id: 'soc-processes',
      title: 'SOC Processes and Procedures',
      content: `
# SOC Processes and Procedures

## Core SOC Processes:

### 1. Monitoring and Detection:
- Continuous security monitoring
- Alert generation and correlation
- Threat intelligence integration
- Anomaly detection

### 2. Incident Response:
- Incident classification and prioritization
- Investigation and analysis
- Containment and eradication
- Recovery and lessons learned

### 3. Threat Hunting:
- Proactive threat searching
- Hypothesis-driven investigations
- IOC development and sharing
- Attack pattern analysis

### 4. Vulnerability Management:
- Vulnerability assessment coordination
- Risk prioritization
- Remediation tracking
- Compliance reporting

## SOC Workflows:

### Alert Handling Workflow:
1. **Alert Generation** - SIEM/monitoring tools
2. **Initial Triage** - Tier 1 analyst review
3. **Classification** - Determine alert type and severity
4. **Investigation** - Gather additional context
5. **Escalation** - Forward to appropriate tier if needed
6. **Resolution** - Document findings and close ticket

### Incident Response Workflow:
1. **Detection** - Alert or report received
2. **Analysis** - Determine if incident occurred
3. **Containment** - Limit impact and spread
4. **Eradication** - Remove threat from environment
5. **Recovery** - Restore normal operations
6. **Lessons Learned** - Post-incident review

## Standard Operating Procedures (SOPs):
- Alert handling procedures
- Escalation matrices
- Communication protocols
- Evidence handling
- Reporting requirements
      `,
      practicalExercise: {
        title: "SOC Workflow Design",
        description: "Design and document SOC workflows for common security scenarios.",
        tasks: [
          "Create alert triage workflow",
          "Design escalation procedures",
          "Develop communication templates",
          "Define SLA requirements",
          "Create process documentation"
        ]
      }
    },
    {
      id: 'soc-technologies',
      title: 'SOC Technologies and Integration',
      content: `
# SOC Technologies and Integration

## Core SOC Technologies:

### 1. Security Information and Event Management (SIEM):
- **Splunk Enterprise Security**
- **IBM QRadar**
- **Microsoft Sentinel**
- **LogRhythm**
- **ArcSight**

### 2. Security Orchestration, Automation and Response (SOAR):
- **Phantom (Splunk)**
- **Demisto (Palo Alto)**
- **IBM Resilient**
- **Swimlane**
- **TheHive**

### 3. Endpoint Detection and Response (EDR):
- **CrowdStrike Falcon**
- **Microsoft Defender for Endpoint**
- **SentinelOne**
- **Carbon Black**

### 4. Network Detection and Response (NDR):
- **Darktrace**
- **ExtraHop**
- **Vectra AI**
- **Corelight**

## SOC Technology Integration:

### Data Sources:
- Network devices (firewalls, routers, switches)
- Endpoint systems (workstations, servers)
- Security tools (antivirus, IPS, DLP)
- Cloud services (AWS, Azure, GCP)
- Applications (web servers, databases)

### Integration Patterns:
- **API-based Integration** - Real-time data exchange
- **Log Forwarding** - Syslog, CEF, LEEF formats
- **Agent-based Collection** - Endpoint data gathering
- **Network Monitoring** - Packet capture and analysis

### Automation Capabilities:
- Alert enrichment
- Threat intelligence lookup
- Automated response actions
- Report generation
- Workflow orchestration

## SOC Metrics and KPIs:

### Operational Metrics:
- Mean Time to Detection (MTTD)
- Mean Time to Response (MTTR)
- Alert volume and false positive rate
- Incident escalation rate
- SLA compliance

### Effectiveness Metrics:
- Threat detection accuracy
- Incident containment time
- Recovery time objectives
- Security posture improvement
- Cost per incident
      `,
      lab: {
        title: "SOC Technology Stack Setup",
        description: "Configure and integrate core SOC technologies in a lab environment.",
        environment: "Virtual SOC lab with SIEM, SOAR, and EDR tools",
        tasks: [
          "Configure SIEM data sources and parsing",
          "Set up automated alert workflows",
          "Integrate threat intelligence feeds",
          "Create custom dashboards and reports",
          "Test incident response automation"
        ]
      }
    }
  ],
  resources: [
    {
      type: 'framework',
      title: 'NIST Cybersecurity Framework - SOC Implementation',
      url: 'https://www.nist.gov/cyberframework'
    },
    {
      type: 'guide',
      title: 'SANS SOC Survey and Best Practices',
      url: 'https://www.sans.org/white-papers/soc-survey/'
    },
    {
      type: 'standard',
      title: 'ISO 27035 - Incident Management',
      url: 'https://www.iso.org/standard/44379.html'
    }
  ],
  assessment: {
    type: 'design-project',
    title: 'SOC Design and Implementation Plan',
    description: 'Design a comprehensive SOC for a medium-sized enterprise.',
    requirements: [
      'SOC organizational structure and staffing plan',
      'Technology stack selection and integration design',
      'Process documentation and SOPs',
      'Metrics and KPI framework',
      'Budget and implementation timeline'
    ]
  },
  tags: ['soc', 'security-operations', 'incident-response', 'siem', 'automation'],
  lastUpdated: '2024-12-19'
};
