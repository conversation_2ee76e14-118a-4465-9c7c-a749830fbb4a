/**
 * Blue Team Security Monitoring Module
 * Comprehensive security monitoring and detection strategies
 */

export const securityMonitoringContent = {
  id: "bt-security-monitoring",
  title: "Security Monitoring and Detection",
  description: "Learn advanced security monitoring techniques, detection strategies, and how to build effective security operations capabilities.",
  difficulty: "Intermediate",
  estimatedTime: 120,
  objectives: [
    "Understand security monitoring fundamentals",
    "Learn to design effective detection strategies",
    "Master SIEM and log analysis techniques",
    "Develop incident detection and response capabilities"
  ],
  sections: [
    {
      title: "Introduction to Security Monitoring",
      content: `
        <h2>Security Monitoring Fundamentals</h2>
        <p>Security monitoring is the continuous observation of an organization's IT infrastructure to detect, analyze, and respond to cybersecurity threats and incidents.</p>

        <h3>Key Components</h3>
        <ul>
          <li><strong>Data Collection:</strong> Gathering logs and telemetry from various sources</li>
          <li><strong>Analysis:</strong> Processing and correlating security events</li>
          <li><strong>Detection:</strong> Identifying potential threats and anomalies</li>
          <li><strong>Response:</strong> Taking action on detected threats</li>
        </ul>

        <h3>Monitoring Objectives</h3>
        <ul>
          <li>Early threat detection</li>
          <li>Incident response support</li>
          <li>Compliance requirements</li>
          <li>Forensic investigation</li>
          <li>Risk assessment and management</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Data Sources and Collection",
      content: `
        <h2>Security Data Sources</h2>
        <p>Effective security monitoring requires comprehensive data collection from multiple sources across the IT environment.</p>

        <h3>Network Data Sources</h3>
        <ul>
          <li><strong>Firewall Logs:</strong> Traffic filtering and blocking events</li>
          <li><strong>IDS/IPS Alerts:</strong> Intrusion detection and prevention</li>
          <li><strong>Network Flow Data:</strong> NetFlow, sFlow, IPFIX</li>
          <li><strong>DNS Logs:</strong> Domain name resolution activities</li>
          <li><strong>Proxy Logs:</strong> Web traffic and content filtering</li>
        </ul>

        <h3>Endpoint Data Sources</h3>
        <ul>
          <li><strong>System Logs:</strong> Windows Event Logs, Syslog</li>
          <li><strong>Application Logs:</strong> Software-specific events</li>
          <li><strong>Process Monitoring:</strong> Execution and behavior tracking</li>
          <li><strong>File System Monitoring:</strong> File access and modifications</li>
          <li><strong>Registry Monitoring:</strong> Windows registry changes</li>
        </ul>

        <h3>Cloud and Infrastructure</h3>
        <ul>
          <li><strong>Cloud Service Logs:</strong> AWS CloudTrail, Azure Activity Logs</li>
          <li><strong>Container Logs:</strong> Docker, Kubernetes events</li>
          <li><strong>Virtualization Logs:</strong> Hypervisor and VM events</li>
          <li><strong>Database Logs:</strong> Access and query monitoring</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "SIEM and Log Analysis",
      content: `
        <h2>Security Information and Event Management (SIEM)</h2>
        <p>SIEM systems provide centralized log management, correlation, and analysis capabilities for security monitoring.</p>

        <h3>SIEM Architecture</h3>
        <ul>
          <li><strong>Data Collection:</strong> Agents, syslog, APIs</li>
          <li><strong>Normalization:</strong> Standardizing log formats</li>
          <li><strong>Storage:</strong> Long-term retention and indexing</li>
          <li><strong>Correlation:</strong> Rule-based event analysis</li>
          <li><strong>Alerting:</strong> Notification and escalation</li>
          <li><strong>Reporting:</strong> Dashboards and compliance reports</li>
        </ul>

        <h3>Popular SIEM Platforms</h3>
        <ul>
          <li><strong>Splunk:</strong> Market-leading platform with powerful search</li>
          <li><strong>IBM QRadar:</strong> Enterprise-focused with advanced analytics</li>
          <li><strong>ArcSight:</strong> HP Enterprise security management</li>
          <li><strong>Elastic Stack:</strong> Open-source ELK (Elasticsearch, Logstash, Kibana)</li>
          <li><strong>Microsoft Sentinel:</strong> Cloud-native SIEM solution</li>
        </ul>

        <h3>Log Analysis Techniques</h3>
        <ul>
          <li><strong>Pattern Recognition:</strong> Identifying known attack signatures</li>
          <li><strong>Anomaly Detection:</strong> Statistical and behavioral analysis</li>
          <li><strong>Correlation Rules:</strong> Multi-event threat detection</li>
          <li><strong>Threat Intelligence Integration:</strong> IOC matching</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Detection Strategies and Use Cases",
      content: `
        <h2>Building Effective Detection Strategies</h2>
        <p>Successful security monitoring requires well-designed detection strategies that balance coverage, accuracy, and operational efficiency.</p>

        <h3>Detection Methodologies</h3>
        <ul>
          <li><strong>Signature-Based:</strong> Known threat patterns and IOCs</li>
          <li><strong>Anomaly-Based:</strong> Deviations from normal behavior</li>
          <li><strong>Heuristic-Based:</strong> Rule-based suspicious activity detection</li>
          <li><strong>Machine Learning:</strong> AI-powered threat detection</li>
        </ul>

        <h3>Common Use Cases</h3>
        <ul>
          <li><strong>Malware Detection:</strong> File hash matching, behavioral analysis</li>
          <li><strong>Lateral Movement:</strong> Unusual network connections and authentication</li>
          <li><strong>Data Exfiltration:</strong> Large data transfers and access patterns</li>
          <li><strong>Privilege Escalation:</strong> Administrative access anomalies</li>
          <li><strong>Brute Force Attacks:</strong> Failed authentication patterns</li>
        </ul>

        <h3>MITRE ATT&CK Mapping</h3>
        <p>Map detection capabilities to MITRE ATT&CK techniques:</p>
        <ul>
          <li>Initial Access detection</li>
          <li>Execution monitoring</li>
          <li>Persistence identification</li>
          <li>Defense Evasion detection</li>
          <li>Command and Control monitoring</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Monitoring Tools and Technologies",
      content: `
        <h2>Security Monitoring Tools</h2>
        <p>Various tools and technologies support comprehensive security monitoring capabilities.</p>

        <h3>Network Monitoring Tools</h3>
        <ul>
          <li><strong>Wireshark:</strong> Network protocol analyzer</li>
          <li><strong>Zeek (Bro):</strong> Network security monitoring framework</li>
          <li><strong>Suricata:</strong> Open-source IDS/IPS engine</li>
          <li><strong>Snort:</strong> Network intrusion detection system</li>
        </ul>

        <h3>Endpoint Monitoring</h3>
        <ul>
          <li><strong>Sysmon:</strong> Windows system monitoring</li>
          <li><strong>OSSEC:</strong> Host-based intrusion detection</li>
          <li><strong>Osquery:</strong> SQL-based operating system instrumentation</li>
          <li><strong>Wazuh:</strong> Open-source security monitoring platform</li>
        </ul>

        <h3>Cloud Monitoring</h3>
        <ul>
          <li><strong>AWS CloudWatch:</strong> Amazon Web Services monitoring</li>
          <li><strong>Azure Monitor:</strong> Microsoft Azure monitoring service</li>
          <li><strong>Google Cloud Monitoring:</strong> GCP monitoring solution</li>
          <li><strong>Falco:</strong> Cloud-native runtime security</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary purpose of a SIEM system?",
            options: [
              "To prevent all cyber attacks",
              "To centralize log management and correlation",
              "To replace all security tools",
              "To automatically respond to incidents"
            ],
            correctAnswer: 1,
            explanation: "The primary purpose of a SIEM system is to centralize log management and provide correlation capabilities to detect security threats across the organization."
          },
          {
            question: "Which detection methodology relies on identifying deviations from normal behavior?",
            options: [
              "Signature-based detection",
              "Heuristic-based detection",
              "Anomaly-based detection",
              "Rule-based detection"
            ],
            correctAnswer: 2,
            explanation: "Anomaly-based detection relies on identifying deviations from established normal behavior patterns to detect potential threats."
          }
        ]
      },
      type: "quiz"
    }
  ]
}; 