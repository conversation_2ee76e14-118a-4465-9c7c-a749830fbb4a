/**
 * Blue Team Digital Forensics Module
 */

export const forensicsContent = {
  title: 'Digital Forensics',
  description: 'Techniques and tools for investigating and analyzing digital evidence in cybersecurity incidents.',
  concepts: [
    'Evidence acquisition and preservation',
    'Disk and memory forensics',
    'Timeline reconstruction',
    'Malware and artifact analysis',
    'Chain of custody and reporting'
  ],
  labs: [
    {
      title: 'Disk Image Analysis Lab',
      description: 'Analyze a disk image to recover deleted files and artifacts',
      difficulty: 'Intermediate',
      duration: '2 hours',
      objectives: [
        'Acquire and mount disk images',
        'Recover deleted files',
        'Analyze file system artifacts',
        'Document findings'
      ],
      tools: ['Autopsy', 'FTK Imager', 'Sleuth Kit'],
      prerequisites: ['Basic file system knowledge', 'Understanding of disk imaging']
    },
    {
      title: 'Memory Forensics Workshop',
      description: 'Investigate memory dumps for malware and suspicious activity',
      difficulty: 'Advanced',
      duration: '2.5 hours',
      objectives: [
        'Acquire memory images',
        'Analyze running processes',
        'Detect malware in memory',
        'Extract forensic artifacts'
      ],
      tools: ['Volatility', 'Rekall', 'Redline'],
      prerequisites: ['Memory architecture basics', 'Malware analysis fundamentals']
    }
  ],
  useCases: [
    {
      title: 'Incident Response Investigation',
      description: 'Perform forensic analysis as part of incident response',
      scenario: 'Analyze compromised systems to determine root cause and impact',
      mitreTactics: ['Discovery', 'Impact'],
      tools: ['Forensic Suites', 'Log Analysis Tools'],
      steps: [
        'Acquire forensic images',
        'Analyze system and user activity',
        'Correlate with incident timeline',
        'Report findings and recommendations'
      ]
    },
    {
      title: 'Malware Artifact Recovery',
      description: 'Recover and analyze malware artifacts from disk and memory',
      scenario: 'Identify and extract malware samples for further analysis',
      mitreTactics: ['Execution', 'Persistence'],
      tools: ['Memory Forensics Tools', 'Disk Analysis Tools'],
      steps: [
        'Identify suspicious files and processes',
        'Extract and analyze artifacts',
        'Document malware behavior',
        'Support remediation efforts'
      ]
    }
  ],
  mitreMapping: [
    {
      tactic: 'Discovery',
      techniques: [
        {
          name: 'File and Directory Discovery',
          description: 'Analyze file system for suspicious files',
          detection: 'Monitor file creation and modification events'
        },
        {
          name: 'System Information Discovery',
          description: 'Gather system and user activity data',
          detection: 'Analyze system logs and user sessions'
        }
      ]
    },
    {
      tactic: 'Execution',
      techniques: [
        {
          name: 'Malicious File Execution',
          description: 'Detect execution of malicious files',
          detection: 'Analyze process creation and command history'
        },
        {
          name: 'Persistence Mechanisms',
          description: 'Identify persistence techniques used by malware',
          detection: 'Analyze autoruns and scheduled tasks'
        }
      ]
    }
  ],
  tools: [
    {
      name: 'Forensic Suites',
      description: 'Comprehensive forensic analysis tools',
      useCases: ['Disk analysis', 'Artifact recovery', 'Timeline reconstruction'],
      examples: ['Autopsy', 'Sleuth Kit', 'FTK Imager']
    },
    {
      name: 'Memory Forensics Tools',
      description: 'Tools for analyzing memory dumps',
      useCases: ['Malware detection', 'Process analysis', 'Artifact extraction'],
      examples: ['Volatility', 'Rekall', 'Redline']
    }
  ],
  prerequisites: [
    'Understanding of file systems',
    'Knowledge of memory architecture',
    'Familiarity with forensic imaging',
    'Incident response basics'
  ],
  resources: [
    {
      type: 'Guide',
      title: 'Digital Forensics Process',
      url: 'https://example.com/forensics-process-guide'
    },
    {
      type: 'Toolkit',
      title: 'Forensics Tools and Resources',
      url: 'https://example.com/forensics-toolkit'
    }
  ]
}; 