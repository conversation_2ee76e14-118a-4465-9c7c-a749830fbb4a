/**
 * BT-07: Endpoint Security and Protection
 * Blue Teaming - Foundation Phase
 */

export const endpointSecurityContent = {
  id: 'bt-07',
  title: 'Endpoint Security and Protection',
  description: 'Master endpoint security fundamentals including endpoint detection and response (EDR), antivirus technologies, and endpoint hardening strategies.',
  category: 'blue-teaming',
  phase: 'foundation',
  difficulty: 'beginner',
  estimatedTime: 180, // 3 hours
  learningObjectives: [
    'Understand endpoint security architecture and components',
    'Implement endpoint detection and response (EDR) solutions',
    'Configure and manage antivirus and anti-malware systems',
    'Apply endpoint hardening techniques and best practices',
    'Monitor and analyze endpoint security events'
  ],
  prerequisites: [
    'bt-01', // Defensive Cybersecurity Fundamentals
    'bt-02'  // Security Monitoring and Event Management
  ],
  sections: [
    {
      id: 'endpoint-fundamentals',
      title: 'Endpoint Security Fundamentals',
      content: `
# Endpoint Security Fundamentals

## What is Endpoint Security?

Endpoint security is the practice of securing endpoints or entry points of end-user devices such as desktops, laptops, and mobile devices from being exploited by malicious actors and campaigns.

### Key Components:
- **Endpoint Detection and Response (EDR)**
- **Antivirus and Anti-malware**
- **Host-based Intrusion Prevention Systems (HIPS)**
- **Device Control and Data Loss Prevention**
- **Endpoint Configuration Management**

## Endpoint Security Architecture

### Traditional vs. Modern Approaches:
1. **Signature-based Detection** - Traditional antivirus
2. **Behavioral Analysis** - Modern EDR solutions
3. **Machine Learning** - AI-powered threat detection
4. **Cloud-based Protection** - Real-time threat intelligence

### Defense in Depth for Endpoints:
- Application whitelisting
- Patch management
- User access controls
- Network segmentation
- Continuous monitoring
      `,
      quiz: [
        {
          question: "What is the primary difference between traditional antivirus and modern EDR solutions?",
          options: [
            "EDR only works on Windows systems",
            "Traditional antivirus uses signatures while EDR uses behavioral analysis",
            "EDR is cheaper than traditional antivirus",
            "There is no difference between them"
          ],
          correct: 1,
          explanation: "Traditional antivirus relies on signature-based detection, while EDR solutions use behavioral analysis and machine learning to detect threats."
        }
      ]
    },
    {
      id: 'edr-implementation',
      title: 'EDR Implementation and Management',
      content: `
# EDR Implementation and Management

## Popular EDR Solutions:
- **CrowdStrike Falcon**
- **Microsoft Defender for Endpoint**
- **SentinelOne**
- **Carbon Black**
- **Cylance**

## EDR Deployment Strategy:

### 1. Planning Phase:
- Asset inventory and classification
- Risk assessment
- Performance impact analysis
- Rollout timeline

### 2. Implementation:
- Agent deployment
- Policy configuration
- Integration with SIEM
- Testing and validation

### 3. Tuning and Optimization:
- False positive reduction
- Alert prioritization
- Custom detection rules
- Performance monitoring

## EDR Capabilities:
- Real-time monitoring
- Threat hunting
- Incident response
- Forensic analysis
- Automated remediation
      `,
      practicalExercise: {
        title: "EDR Alert Analysis",
        description: "Analyze a simulated EDR alert and determine the appropriate response actions.",
        tasks: [
          "Review the alert details and timeline",
          "Identify indicators of compromise (IoCs)",
          "Assess the threat severity",
          "Recommend containment actions",
          "Document findings and lessons learned"
        ]
      }
    },
    {
      id: 'endpoint-hardening',
      title: 'Endpoint Hardening and Configuration',
      content: `
# Endpoint Hardening and Configuration

## Windows Endpoint Hardening:

### Security Policies:
- Password policies
- Account lockout policies
- User rights assignments
- Security options
- Audit policies

### Registry Hardening:
- Disable unnecessary services
- Configure security settings
- Remove default shares
- Enable logging

### Group Policy Management:
- Centralized configuration
- Software restriction policies
- Application control
- Device control

## Linux Endpoint Hardening:

### System Configuration:
- Kernel parameter tuning
- Service management
- File system permissions
- Network configuration

### Security Tools:
- SELinux/AppArmor
- Fail2ban
- AIDE (Advanced Intrusion Detection Environment)
- Lynis security auditing

## Mobile Device Management (MDM):
- Device enrollment
- Policy enforcement
- Application management
- Remote wipe capabilities
      `,
      lab: {
        title: "Windows Endpoint Hardening Lab",
        description: "Implement security hardening measures on a Windows endpoint.",
        environment: "Windows 10/11 virtual machine",
        tasks: [
          "Configure local security policies",
          "Implement application whitelisting",
          "Enable advanced logging",
          "Test security configurations",
          "Document hardening checklist"
        ]
      }
    }
  ],
  resources: [
    {
      type: 'documentation',
      title: 'NIST Cybersecurity Framework - Endpoint Security',
      url: 'https://www.nist.gov/cyberframework'
    },
    {
      type: 'tool',
      title: 'Microsoft Security Compliance Toolkit',
      url: 'https://www.microsoft.com/en-us/download/details.aspx?id=55319'
    },
    {
      type: 'guide',
      title: 'CIS Controls for Endpoint Security',
      url: 'https://www.cisecurity.org/controls'
    }
  ],
  assessment: {
    type: 'practical',
    title: 'Endpoint Security Implementation',
    description: 'Design and implement a comprehensive endpoint security strategy for a small organization.',
    requirements: [
      'EDR solution selection and justification',
      'Endpoint hardening checklist',
      'Monitoring and alerting configuration',
      'Incident response procedures',
      'Performance impact assessment'
    ]
  },
  tags: ['endpoint-security', 'edr', 'antivirus', 'hardening', 'device-management'],
  lastUpdated: '2024-12-19'
};
