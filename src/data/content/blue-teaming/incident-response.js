/**
 * Blue Team Incident Response Module
 */

export const incidentResponseContent = {
  id: "bt-incident-response",
  title: "Incident Response",
  description: "Learn incident response procedures and techniques for effective security incident management.",
  difficulty: "Advanced",
  estimatedTime: 120,
  objectives: [
    "Understand incident response process",
    "Learn containment strategies",
    "Master evidence collection",
    "Develop response capabilities"
  ],
  sections: [
    {
      title: "Incident Response Fundamentals",
      content: `
        <h2>Incident Response</h2>
        <p>Learn how to effectively respond to and manage security incidents.</p>
        <h3>Response Phases</h3>
        <ul>
          <li>Preparation</li>
          <li>Identification</li>
          <li>Containment</li>
          <li>Eradication</li>
          <li>Recovery</li>
          <li>Lessons learned</li>
        </ul>
      `,
      type: "text"
    }
  ]
}; 