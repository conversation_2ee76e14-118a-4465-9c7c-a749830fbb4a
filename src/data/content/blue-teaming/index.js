/**
 * Blue Teaming Learning Path - Comprehensive Defensive Cybersecurity Curriculum
 * 50 modules covering all aspects of defensive cybersecurity operations
 */

// Import Foundation Phase modules (BT-1 to BT-10)
import { defensiveFundamentalsContent } from './bt-01-defensive-fundamentals.js';
import { securityMonitoringContent } from './bt-02-security-monitoring.js';
import { incidentResponseContent } from './bt-03-incident-response.js';
import { threatDetectionContent } from './bt-04-threat-detection.js';
import { logAnalysisContent } from './bt-05-log-analysis.js';
import { networkSecurityMonitoringContent } from './bt-06-network-security-monitoring.js';
// Foundation Phase modules (BT-7 to BT-10)
import { endpointSecurityContent } from './bt-07-endpoint-security.js';
import { vulnerabilityManagementContent } from './bt-08-vulnerability-management.js';
import { securityOperationsCenterContent } from './bt-09-security-operations-center.js';
import { digitalForensicsContent } from './bt-10-digital-forensics.js';

// Import Intermediate Phase modules (BT-11 to BT-20)
import { threatHuntingContent } from './bt-11-threat-hunting.js';
import { malwareAnalysisContent } from './bt-12-malware-analysis.js';
import { securityarchitectureanddesignContent } from './bt-13-security-architecture-and-design.js';
import { identityandaccessmanagementContent } from './bt-14-identity-and-access-management.js';
import { cloudsecuritydefenseContent } from './bt-15-cloud-security-defense.js';
import { networkdefenseandhardeningContent } from './bt-16-network-defense-and-hardening.js';
import { applicationsecuritydefenseContent } from './bt-17-application-security-defense.js';
import { cryptographyandpkimanagementContent } from './bt-18-cryptography-and-pki-management.js';
import { complianceandriskmanagementContent } from './bt-19-compliance-and-risk-management.js';
import { securityawarenessandtrainingContent } from './bt-20-security-awareness-and-training.js';

// Import Advanced Phase modules (BT-21 to BT-30)
import { advancedpersistentthreatdefenseContent } from './bt-21-advanced-persistent-threat-defense.js';
import { cyberthreatintelligenceContent } from './bt-22-cyber-threat-intelligence.js';
import { advancedsiemandanalyticsContent } from './bt-23-advanced-siem-and-analytics.js';
import { deceptiontechnologyandhoneypotsContent } from './bt-24-deception-technology-and-honeypots.js';
import { industrialcontrolsystemssecurityContent } from './bt-25-industrial-control-systems-security.js';
import { mobiledevicesecurityContent } from './bt-26-mobile-device-security.js';
import { iotandembeddedsystemssecurityContent } from './bt-27-iot-and-embedded-systems-security.js';
import { advancedincidentresponseContent } from './bt-28-advanced-incident-response.js';
import { cybercrisismanagementContent } from './bt-29-cyber-crisis-management.js';
import { securitymetricsandkpisContent } from './bt-30-security-metrics-and-kpis.js';

// Import Expert Phase modules (BT-31 to BT-40)
import { zerotrustarchitectureimplementationContent } from './bt-31-zero-trust-architecture-implementation.js';
import { advancedthreatmodelingContent } from './bt-32-advanced-threat-modeling.js';
import { securityresearchanddevelopmentContent } from './bt-33-security-research-and-development.js';
import { quantumsecurityandpostquantumcryptographyContent } from './bt-34-quantum-security-and-post-quantum-cryptography.js';
import { aimlsecurityanddefenseContent } from './bt-35-ai-ml-security-and-defense.js';
import { globalsecurityoperationsContent } from './bt-36-global-security-operations.js';
import { regulatorycomplianceandgovernanceContent } from './bt-37-regulatory-compliance-and-governance.js';
import { executivesecurityleadershipContent } from './bt-38-executive-security-leadership.js';
import { securityprogrammanagementContent } from './bt-39-security-program-management.js';
import { businesscontinuityanddisasterrecoveryContent } from './bt-40-business-continuity-and-disaster-recovery.js';

// Import Master Phase modules (BT-41 to BT-50)
import { nextgenerationsecuritytechnologiesContent } from './bt-41-next-generation-security-technologies.js';
import { autonomousdefensesystemsContent } from './bt-42-autonomous-defense-systems.js';
import { cyberwarfareandnationstatedefenseContent } from './bt-43-cyber-warfare-and-nation-state-defense.js';
import { securityinnovationandemergingthreatsContent } from './bt-44-security-innovation-and-emerging-threats.js';
import { globalcyberpolicyandstrategyContent } from './bt-45-global-cyber-policy-and-strategy.js';
import { advancedsecurityarchitectureContent } from './bt-46-advanced-security-architecture.js';
import { cyberresilienceandadaptivesecurityContent } from './bt-47-cyber-resilience-and-adaptive-security.js';
import { securitytransformationleadershipContent } from './bt-48-security-transformation-leadership.js';
import { futureofcybersecurityContent } from './bt-49-future-of-cybersecurity.js';
import { blueteamcapstoneprojectContent } from './bt-50-blue-team-capstone-project.js';
// import { networkDefenseContent } from './bt-16-network-defense.js';
// import { applicationSecurityDefenseContent } from './bt-17-application-security-defense.js';
// import { dataProtectionContent } from './bt-18-data-protection.js';
// import { complianceGovernanceContent } from './bt-19-compliance-governance.js';
// import { businessContinuityContent } from './bt-20-business-continuity.js';

// Import Advanced Phase modules (BT-21 to BT-30)
// import { advancedThreatHuntingContent } from './bt-21-advanced-threat-hunting.js';
// import { behavioralAnalyticsContent } from './bt-22-behavioral-analytics.js';
// import { threatIntelligenceAnalysisContent } from './bt-23-threat-intelligence-analysis.js';
// import { advancedForensicsContent } from './bt-24-advanced-forensics.js';
// import { securityAutomationContent } from './bt-25-security-automation.js';
// import { aiMlSecurityDefenseContent } from './bt-26-ai-ml-security-defense.js';
// import { iotSecurityDefenseContent } from './bt-27-iot-security-defense.js';
// import { industrialSecurityContent } from './bt-28-industrial-security.js';
// import { crisisManagementContent } from './bt-29-crisis-management.js';
// import { securityMetricsContent } from './bt-30-security-metrics.js';

// Import Expert Phase modules (BT-31 to BT-40)
// import { advancedPersistentThreatDefenseContent } from './bt-31-apt-defense.js';
// import { zeroTrustArchitectureContent } from './bt-32-zero-trust-architecture.js';
// import { quantumSecurityDefenseContent } from './bt-33-quantum-security-defense.js';
// import { emergingThreatDefenseContent } from './bt-34-emerging-threat-defense.js';
// import { securityResearchContent } from './bt-35-security-research.js';
// import { globalSecurityOperationsContent } from './bt-36-global-security-operations.js';
// import { regulatoryComplianceContent } from './bt-37-regulatory-compliance.js';
// import { executiveSecurityContent } from './bt-38-executive-security.js';
// import { securityTransformationContent } from './bt-39-security-transformation.js';
// import { strategicSecurityContent } from './bt-40-strategic-security.js';

// Import Master Phase modules (BT-41 to BT-50)
// import { nextGenSecurityContent } from './bt-41-next-gen-security.js';
// import { autonomousDefenseContent } from './bt-42-autonomous-defense.js';
// import { quantumCryptographyDefenseContent } from './bt-43-quantum-cryptography-defense.js';
// import { spaceSecurityDefenseContent } from './bt-44-space-security-defense.js';
// import { biometricSecurityDefenseContent } from './bt-45-biometric-security-defense.js';
// import { neurotechnologySecurityContent } from './bt-46-neurotechnology-security.js';
// import { blockchainSecurityDefenseContent } from './bt-47-blockchain-security-defense.js';
// import { metaverseSecurityContent } from './bt-48-metaverse-security.js';
// import { futureDefenseStrategiesContent } from './bt-49-future-defense-strategies.js';
// import { securityLeadershipContent } from './bt-50-security-leadership.js';

/**
 * Blue Teaming Learning Path Structure
 * Comprehensive 50-module defensive cybersecurity curriculum
 */
export const blueTeamingLearningPath = {
  id: 'blue-teaming',
  title: 'Blue Teaming: Comprehensive Defensive Cybersecurity',
  description: 'Master defensive cybersecurity operations with our comprehensive 50-module Blue Teaming curriculum. Learn threat detection, incident response, security monitoring, threat hunting, and strategic defense planning.',
  category: 'defensive',
  difficulty: 'beginner-to-expert',
  estimatedHours: 1800, // ~36 hours per module average
  prerequisites: [
    'Basic understanding of computer networks',
    'Fundamental knowledge of operating systems',
    'Basic cybersecurity awareness',
    'Familiarity with command-line interfaces'
  ],
  learningObjectives: [
    'Master comprehensive defensive cybersecurity operations',
    'Develop advanced threat detection and hunting capabilities',
    'Build expertise in incident response and digital forensics',
    'Understand security architecture and strategic planning',
    'Learn cutting-edge defense technologies and methodologies',
    'Prepare for leadership roles in cybersecurity defense'
  ],
  phases: [
    {
      name: 'Foundation Phase',
      description: 'Essential defensive cybersecurity fundamentals',
      modules: 'BT-1 to BT-10',
      focus: 'Core concepts, monitoring, incident response, and basic operations'
    },
    {
      name: 'Intermediate Phase',
      description: 'Advanced defensive techniques and specialized skills',
      modules: 'BT-11 to BT-20',
      focus: 'Threat hunting, malware analysis, architecture, and governance'
    },
    {
      name: 'Advanced Phase',
      description: 'Sophisticated defense strategies and emerging technologies',
      modules: 'BT-21 to BT-30',
      focus: 'Advanced analytics, automation, AI/ML defense, and metrics'
    },
    {
      name: 'Expert Phase',
      description: 'Strategic defense and organizational security leadership',
      modules: 'BT-31 to BT-40',
      focus: 'APT defense, zero trust, compliance, and transformation'
    },
    {
      name: 'Master Phase',
      description: 'Next-generation defense and future security leadership',
      modules: 'BT-41 to BT-50',
      focus: 'Emerging technologies, autonomous defense, and strategic leadership'
    }
  ],
  modules: [
    // Foundation Phase (BT-1 to BT-10)
    defensiveFundamentalsContent,
    securityMonitoringContent,
    incidentResponseContent,
    threatDetectionContent,
    logAnalysisContent,
    networkSecurityMonitoringContent,
    endpointSecurityContent,
    vulnerabilityManagementContent,
    securityOperationsCenterContent,
    digitalForensicsContent,

    // Intermediate Phase (BT-11 to BT-20)
    threatHuntingContent,
    malwareAnalysisContent,
    securityarchitectureanddesignContent,
    identityandaccessmanagementContent,
    cloudsecuritydefenseContent,
    networkdefenseandhardeningContent,
    applicationsecuritydefenseContent,
    cryptographyandpkimanagementContent,
    complianceandriskmanagementContent,
    securityawarenessandtrainingContent,

    // Advanced Phase (BT-21 to BT-30)
    advancedpersistentthreatdefenseContent,
    cyberthreatintelligenceContent,
    advancedsiemandanalyticsContent,
    deceptiontechnologyandhoneypotsContent,
    industrialcontrolsystemssecurityContent,
    mobiledevicesecurityContent,
    iotandembeddedsystemssecurityContent,
    advancedincidentresponseContent,
    cybercrisismanagementContent,
    securitymetricsandkpisContent,

    // Expert Phase (BT-31 to BT-40)
    zerotrustarchitectureimplementationContent,
    advancedthreatmodelingContent,
    securityresearchanddevelopmentContent,
    quantumsecurityandpostquantumcryptographyContent,
    aimlsecurityanddefenseContent,
    globalsecurityoperationsContent,
    regulatorycomplianceandgovernanceContent,
    executivesecurityleadershipContent,
    securityprogrammanagementContent,
    businesscontinuityanddisasterrecoveryContent,

    // Master Phase (BT-41 to BT-50)
    nextgenerationsecuritytechnologiesContent,
    autonomousdefensesystemsContent,
    cyberwarfareandnationstatedefenseContent,
    securityinnovationandemergingthreatsContent,
    globalcyberpolicyandstrategyContent,
    advancedsecurityarchitectureContent,
    cyberresilienceandadaptivesecurityContent,
    securitytransformationleadershipContent,
    futureofcybersecurityContent,
    blueteamcapstoneprojectContent
  ]
};

// Helper functions
export const getAllBlueTeamingModules = () => {
  return blueTeamingLearningPath.modules;
};

export const getBlueTeamingModuleById = (id) => {
  return blueTeamingLearningPath.modules.find(module => module.id === id);
};

export const getBlueTeamingModulesByPhase = (phase) => {
  const phaseRanges = {
    'foundation': { start: 1, end: 10 },
    'intermediate': { start: 11, end: 20 },
    'advanced': { start: 21, end: 30 },
    'expert': { start: 31, end: 40 },
    'master': { start: 41, end: 50 }
  };

  const range = phaseRanges[phase.toLowerCase()];
  if (!range) return [];

  return blueTeamingLearningPath.modules.filter(module => {
    const moduleNum = parseInt(module.id.split('-')[1]);
    return moduleNum >= range.start && moduleNum <= range.end;
  });
};

export const getBlueTeamingProgress = (completedModules = []) => {
  const totalModules = blueTeamingLearningPath.modules.length;
  const completed = completedModules.length;
  return {
    completed,
    total: totalModules,
    percentage: Math.round((completed / totalModules) * 100),
    phase: getPhaseFromProgress(completed)
  };
};

const getPhaseFromProgress = (completedCount) => {
  if (completedCount <= 10) return 'Foundation';
  if (completedCount <= 20) return 'Intermediate';
  if (completedCount <= 30) return 'Advanced';
  if (completedCount <= 40) return 'Expert';
  return 'Master';
};

export default blueTeamingLearningPath;