/**
 * Professional Report Writing Module
 */

export const professionalReportWritingContent = {
  id: "bb-11",
  pathId: "bug-bounty",
  title: "Professional Report Writing",
  description: "Master the art of writing professional vulnerability reports that maximize impact, ensure clear communication, and increase acceptance rates in bug bounty programs.",
  objectives: [
    "Understand professional report writing standards",
    "Learn to structure clear and comprehensive reports",
    "Master technical writing and communication skills",
    "Develop effective proof-of-concept creation",
    "Learn impact assessment and risk communication",
    "Create reports that maximize bounty rewards"
  ],
  difficulty: "Intermediate",
  estimatedTime: 110,
  sections: [
    {
      title: "Report Writing Fundamentals",
      content: `
        <h2>Professional Vulnerability Report Writing</h2>
        <p>Professional report writing is crucial for bug bounty success, ensuring vulnerabilities are clearly communicated, properly understood, and efficiently remediated.</p>
        
        <h3>Report Quality Importance</h3>
        <ul>
          <li><strong>Impact on Bounty Success:</strong>
            <ul>
              <li>Higher acceptance rates</li>
              <li>Increased bounty rewards</li>
              <li>Faster triage and resolution</li>
              <li>Improved researcher reputation</li>
            </ul>
          </li>
          <li><strong>Communication Benefits:</strong>
            <ul>
              <li>Clear vulnerability understanding</li>
              <li>Reduced back-and-forth clarification</li>
              <li>Efficient remediation guidance</li>
              <li>Professional relationship building</li>
            </ul>
          </li>
          <li><strong>Common Report Failures:</strong>
            <ul>
              <li>Insufficient technical detail</li>
              <li>Poor impact explanation</li>
              <li>Missing reproduction steps</li>
              <li>Unclear proof-of-concept</li>
            </ul>
          </li>
        </ul>
        
        <h3>Report Structure Standards</h3>
        <ul>
          <li><strong>Executive Summary:</strong>
            <ul>
              <li>Vulnerability overview</li>
              <li>Business impact summary</li>
              <li>Risk level assessment</li>
              <li>Recommended priority</li>
            </ul>
          </li>
          <li><strong>Technical Details:</strong>
            <ul>
              <li>Vulnerability description</li>
              <li>Affected components</li>
              <li>Root cause analysis</li>
              <li>Technical impact explanation</li>
            </ul>
          </li>
          <li><strong>Reproduction Information:</strong>
            <ul>
              <li>Step-by-step instructions</li>
              <li>Required tools and setup</li>
              <li>Expected vs. actual results</li>
              <li>Environmental considerations</li>
            </ul>
          </li>
          <li><strong>Supporting Evidence:</strong>
            <ul>
              <li>Screenshots and videos</li>
              <li>Code snippets and payloads</li>
              <li>Network traffic captures</li>
              <li>Log file excerpts</li>
            </ul>
          </li>
        </ul>
        
        <h3>Writing Style and Tone</h3>
        <ul>
          <li><strong>Professional Communication:</strong>
            <ul>
              <li>Clear and concise language</li>
              <li>Technical accuracy</li>
              <li>Respectful and constructive tone</li>
              <li>Objective and factual presentation</li>
            </ul>
          </li>
          <li><strong>Audience Considerations:</strong>
            <ul>
              <li>Technical vs. business stakeholders</li>
              <li>Security team expertise levels</li>
              <li>Developer background knowledge</li>
              <li>Management decision-making needs</li>
            </ul>
          </li>
          <li><strong>Clarity and Precision:</strong>
            <ul>
              <li>Specific terminology usage</li>
              <li>Unambiguous instructions</li>
              <li>Logical information flow</li>
              <li>Consistent formatting</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Technical Documentation and Evidence",
      content: `
        <h2>Technical Documentation and Evidence Collection</h2>
        <p>Comprehensive technical documentation and evidence collection are essential for demonstrating vulnerability validity and facilitating effective remediation.</p>
        
        <h3>Reproduction Steps Documentation</h3>
        <ul>
          <li><strong>Step-by-Step Instructions:</strong>
            <ul>
              <li>Numbered sequential steps</li>
              <li>Specific parameter values</li>
              <li>Required user actions</li>
              <li>Expected system responses</li>
            </ul>
          </li>
          <li><strong>Environmental Setup:</strong>
            <ul>
              <li>Browser and version requirements</li>
              <li>Tool configuration details</li>
              <li>Account and permission needs</li>
              <li>Network and proxy settings</li>
            </ul>
          </li>
          <li><strong>Payload Documentation:</strong>
            <ul>
              <li>Complete payload strings</li>
              <li>Encoding and formatting notes</li>
              <li>Customization instructions</li>
              <li>Alternative payload options</li>
            </ul>
          </li>
        </ul>
        
        <h3>Visual Evidence Creation</h3>
        <ul>
          <li><strong>Screenshot Best Practices:</strong>
            <ul>
              <li>High-resolution captures</li>
              <li>Relevant information highlighting</li>
              <li>Multiple perspective views</li>
              <li>Annotation and callouts</li>
            </ul>
          </li>
          <li><strong>Video Demonstrations:</strong>
            <ul>
              <li>Screen recording setup</li>
              <li>Clear narration or captions</li>
              <li>Focused content presentation</li>
              <li>Appropriate length and pacing</li>
            </ul>
          </li>
          <li><strong>Network Traffic Evidence:</strong>
            <ul>
              <li>HTTP request/response captures</li>
              <li>Burp Suite history exports</li>
              <li>Wireshark packet captures</li>
              <li>cURL command examples</li>
            </ul>
          </li>
        </ul>
        
        <h3>Code and Log Analysis</h3>
        <ul>
          <li><strong>Source Code References:</strong>
            <ul>
              <li>Vulnerable code snippets</li>
              <li>Line number references</li>
              <li>Function and method identification</li>
              <li>Code flow explanation</li>
            </ul>
          </li>
          <li><strong>Log File Analysis:</strong>
            <ul>
              <li>Relevant log entries</li>
              <li>Error message documentation</li>
              <li>Timestamp correlation</li>
              <li>Pattern identification</li>
            </ul>
          </li>
          <li><strong>Database Evidence:</strong>
            <ul>
              <li>Query result screenshots</li>
              <li>Schema information</li>
              <li>Data modification proof</li>
              <li>Injection payload effects</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Impact Assessment and Risk Communication",
      content: `
        <h2>Impact Assessment and Risk Communication</h2>
        <p>Effective impact assessment and risk communication help stakeholders understand vulnerability severity and prioritize remediation efforts appropriately.</p>
        
        <h3>Impact Assessment Framework</h3>
        <ul>
          <li><strong>Technical Impact:</strong>
            <ul>
              <li>System compromise level</li>
              <li>Data access and modification</li>
              <li>Service availability effects</li>
              <li>Privilege escalation potential</li>
            </ul>
          </li>
          <li><strong>Business Impact:</strong>
            <ul>
              <li>Financial loss potential</li>
              <li>Reputation damage risk</li>
              <li>Regulatory compliance issues</li>
              <li>Customer trust implications</li>
            </ul>
          </li>
          <li><strong>Operational Impact:</strong>
            <ul>
              <li>Service disruption potential</li>
              <li>Recovery time requirements</li>
              <li>Resource allocation needs</li>
              <li>Incident response activation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Risk Scoring and Prioritization</h3>
        <ul>
          <li><strong>CVSS Scoring:</strong>
            <ul>
              <li>Base score calculation</li>
              <li>Temporal score factors</li>
              <li>Environmental score adjustments</li>
              <li>Score justification explanation</li>
            </ul>
          </li>
          <li><strong>Custom Risk Metrics:</strong>
            <ul>
              <li>Organization-specific factors</li>
              <li>Asset criticality weighting</li>
              <li>Threat landscape considerations</li>
              <li>Exploitability assessment</li>
            </ul>
          </li>
          <li><strong>Comparative Analysis:</strong>
            <ul>
              <li>Similar vulnerability references</li>
              <li>Industry standard comparisons</li>
              <li>Historical incident examples</li>
              <li>Peer organization impacts</li>
            </ul>
          </li>
        </ul>
        
        <h3>Remediation Guidance</h3>
        <ul>
          <li><strong>Fix Recommendations:</strong>
            <ul>
              <li>Specific remediation steps</li>
              <li>Code change suggestions</li>
              <li>Configuration modifications</li>
              <li>Architecture improvements</li>
            </ul>
          </li>
          <li><strong>Temporary Mitigations:</strong>
            <ul>
              <li>Immediate protection measures</li>
              <li>WAF rule suggestions</li>
              <li>Access control adjustments</li>
              <li>Monitoring enhancements</li>
            </ul>
          </li>
          <li><strong>Verification Methods:</strong>
            <ul>
              <li>Fix validation procedures</li>
              <li>Testing methodologies</li>
              <li>Regression test suggestions</li>
              <li>Ongoing monitoring recommendations</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the most important element of a vulnerability report for ensuring quick triage?",
            options: [
              "Detailed technical analysis",
              "Clear reproduction steps",
              "Comprehensive impact assessment",
              "Professional formatting"
            ],
            correctAnswer: 1,
            explanation: "Clear reproduction steps are most important for quick triage because they allow security teams to immediately verify the vulnerability's existence and understand its scope without extensive investigation."
          },
          {
            question: "Which type of evidence is most effective for demonstrating the real-world impact of a vulnerability?",
            options: [
              "Source code snippets",
              "Network traffic captures",
              "Video demonstrations showing exploitation",
              "Log file excerpts"
            ],
            correctAnswer: 2,
            explanation: "Video demonstrations showing exploitation are most effective for demonstrating real-world impact because they provide visual proof of the vulnerability being exploited and its actual effects on the system."
          },
          {
            question: "What should be the primary focus when writing impact assessments for business stakeholders?",
            options: [
              "Technical implementation details",
              "Business consequences and financial implications",
              "Code-level vulnerabilities",
              "Network architecture issues"
            ],
            correctAnswer: 1,
            explanation: "Business consequences and financial implications should be the primary focus for business stakeholders because they need to understand the potential business impact to make informed decisions about prioritization and resource allocation."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
