/**
 * Mobile Application Security Testing Module
 */

export const mobileSecurityContent = {
  id: "bb-8",
  pathId: "bug-bounty",
  title: "Mobile Application Security Testing",
  description: "Master mobile application security testing for iOS and Android platforms, including static and dynamic analysis, reverse engineering, and mobile-specific vulnerability discovery.",
  objectives: [
    "Understand mobile application security fundamentals",
    "Learn static and dynamic analysis techniques",
    "Master mobile application reverse engineering",
    "Develop skills in mobile-specific vulnerability testing",
    "Learn iOS and Android security testing methodologies",
    "Create comprehensive mobile security assessments"
  ],
  difficulty: "Advanced",
  estimatedTime: 150,
  sections: [
    {
      title: "Mobile Security Fundamentals",
      content: `
        <h2>Mobile Application Security Testing</h2>
        <p>Mobile application security testing requires specialized knowledge of mobile platforms, architectures, and unique attack vectors specific to mobile environments.</p>
        
        <h3>Mobile Security Landscape</h3>
        <ul>
          <li><strong>Mobile Platform Architecture:</strong>
            <ul>
              <li>iOS security model and sandboxing</li>
              <li>Android security architecture</li>
              <li>Application lifecycle and permissions</li>
              <li>Inter-process communication (IPC)</li>
            </ul>
          </li>
          <li><strong>Mobile Attack Surface:</strong>
            <ul>
              <li>Application layer vulnerabilities</li>
              <li>Network communication security</li>
              <li>Local data storage issues</li>
              <li>Platform-specific vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Mobile Threat Model:</strong>
            <ul>
              <li>Malicious applications</li>
              <li>Network-based attacks</li>
              <li>Physical device access</li>
              <li>Social engineering attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>OWASP Mobile Top 10</h3>
        <ul>
          <li><strong>M1: Improper Platform Usage:</strong>
            <ul>
              <li>Misuse of platform features</li>
              <li>Violation of published guidelines</li>
              <li>Insecure platform permissions</li>
              <li>TouchID/FaceID bypass</li>
            </ul>
          </li>
          <li><strong>M2: Insecure Data Storage:</strong>
            <ul>
              <li>Unencrypted local databases</li>
              <li>Insecure shared preferences</li>
              <li>Keychain/Keystore vulnerabilities</li>
              <li>Log file data leakage</li>
            </ul>
          </li>
          <li><strong>M3: Insecure Communication:</strong>
            <ul>
              <li>Weak SSL/TLS implementation</li>
              <li>Certificate pinning bypass</li>
              <li>Man-in-the-middle vulnerabilities</li>
              <li>Insecure protocols usage</li>
            </ul>
          </li>
          <li><strong>M4: Insecure Authentication:</strong>
            <ul>
              <li>Weak authentication schemes</li>
              <li>Session management flaws</li>
              <li>Biometric authentication bypass</li>
              <li>OAuth implementation issues</li>
            </ul>
          </li>
          <li><strong>M5: Insufficient Cryptography:</strong>
            <ul>
              <li>Weak encryption algorithms</li>
              <li>Poor key management</li>
              <li>Hardcoded cryptographic keys</li>
              <li>Custom cryptographic implementations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Remaining Mobile Top 10</h3>
        <ul>
          <li><strong>M6: Insecure Authorization:</strong>
            <ul>
              <li>Privilege escalation</li>
              <li>Authorization bypass</li>
              <li>Insecure direct object references</li>
              <li>Function-level access control</li>
            </ul>
          </li>
          <li><strong>M7: Client Code Quality:</strong>
            <ul>
              <li>Buffer overflows</li>
              <li>Format string vulnerabilities</li>
              <li>Memory corruption issues</li>
              <li>Code injection vulnerabilities</li>
            </ul>
          </li>
          <li><strong>M8: Code Tampering:</strong>
            <ul>
              <li>Binary patching</li>
              <li>Runtime manipulation</li>
              <li>Anti-tampering bypass</li>
              <li>Repackaging attacks</li>
            </ul>
          </li>
          <li><strong>M9: Reverse Engineering:</strong>
            <ul>
              <li>Source code recovery</li>
              <li>Algorithm analysis</li>
              <li>Intellectual property theft</li>
              <li>Anti-reverse engineering bypass</li>
            </ul>
          </li>
          <li><strong>M10: Extraneous Functionality:</strong>
            <ul>
              <li>Hidden backdoors</li>
              <li>Debug functionality</li>
              <li>Test code in production</li>
              <li>Administrative interfaces</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Android Security Testing",
      content: `
        <h2>Android Application Security Testing</h2>
        <p>Android security testing involves analyzing APK files, runtime behavior, and platform-specific security mechanisms to identify vulnerabilities.</p>
        
        <h3>Android Static Analysis</h3>
        <ul>
          <li><strong>APK Analysis:</strong>
            <ul>
              <li>APK structure and components</li>
              <li>AndroidManifest.xml analysis</li>
              <li>Permissions and intent filters</li>
              <li>Resource and asset examination</li>
            </ul>
          </li>
          <li><strong>Code Analysis:</strong>
            <ul>
              <li>Java/Kotlin source code review</li>
              <li>Native library analysis (JNI)</li>
              <li>Smali code examination</li>
              <li>Hardcoded secrets detection</li>
            </ul>
          </li>
          <li><strong>Static Analysis Tools:</strong>
            <ul>
              <li>JADX for APK decompilation</li>
              <li>APKTool for resource extraction</li>
              <li>MobSF for automated analysis</li>
              <li>QARK for vulnerability scanning</li>
            </ul>
          </li>
        </ul>
        
        <h3>Android Dynamic Analysis</h3>
        <ul>
          <li><strong>Runtime Analysis Setup:</strong>
            <ul>
              <li>Android emulator configuration</li>
              <li>Physical device rooting</li>
              <li>Frida instrumentation framework</li>
              <li>Xposed framework usage</li>
            </ul>
          </li>
          <li><strong>Runtime Manipulation:</strong>
            <ul>
              <li>Method hooking and interception</li>
              <li>SSL pinning bypass</li>
              <li>Root detection bypass</li>
              <li>Anti-debugging bypass</li>
            </ul>
          </li>
          <li><strong>Network Traffic Analysis:</strong>
            <ul>
              <li>HTTP/HTTPS proxy setup</li>
              <li>Certificate installation</li>
              <li>Traffic interception and modification</li>
              <li>API endpoint discovery</li>
            </ul>
          </li>
        </ul>
        
        <h3>Android-Specific Vulnerabilities</h3>
        <ul>
          <li><strong>Intent-Based Attacks:</strong>
            <ul>
              <li>Intent spoofing and hijacking</li>
              <li>Exported component exploitation</li>
              <li>Broadcast receiver vulnerabilities</li>
              <li>Deep link manipulation</li>
            </ul>
          </li>
          <li><strong>Storage Vulnerabilities:</strong>
            <ul>
              <li>Shared preferences exposure</li>
              <li>SQLite database security</li>
              <li>External storage access</li>
              <li>Backup flag vulnerabilities</li>
            </ul>
          </li>
          <li><strong>WebView Security:</strong>
            <ul>
              <li>JavaScript interface exposure</li>
              <li>File access vulnerabilities</li>
              <li>Universal XSS in WebView</li>
              <li>WebView debugging enabled</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "iOS Security Testing",
      content: `
        <h2>iOS Application Security Testing</h2>
        <p>iOS security testing requires understanding of the iOS security model, code signing, and specialized tools for analyzing iOS applications.</p>
        
        <h3>iOS Static Analysis</h3>
        <ul>
          <li><strong>IPA Analysis:</strong>
            <ul>
              <li>IPA file structure and contents</li>
              <li>Info.plist configuration analysis</li>
              <li>Entitlements and capabilities</li>
              <li>Binary and resource examination</li>
            </ul>
          </li>
          <li><strong>Binary Analysis:</strong>
            <ul>
              <li>Mach-O binary format</li>
              <li>Objective-C/Swift code analysis</li>
              <li>Class-dump for header extraction</li>
              <li>Strings and symbol analysis</li>
            </ul>
          </li>
          <li><strong>iOS Static Analysis Tools:</strong>
            <ul>
              <li>Hopper Disassembler</li>
              <li>IDA Pro for iOS analysis</li>
              <li>MobSF for iOS applications</li>
              <li>iMazing for IPA extraction</li>
            </ul>
          </li>
        </ul>
        
        <h3>iOS Dynamic Analysis</h3>
        <ul>
          <li><strong>iOS Testing Environment:</strong>
            <ul>
              <li>Jailbroken device setup</li>
              <li>iOS Simulator limitations</li>
              <li>Cydia and package management</li>
              <li>SSH access configuration</li>
            </ul>
          </li>
          <li><strong>Runtime Manipulation:</strong>
            <ul>
              <li>Frida for iOS instrumentation</li>
              <li>Cycript for runtime exploration</li>
              <li>LLDB debugging techniques</li>
              <li>Method swizzling and hooking</li>
            </ul>
          </li>
          <li><strong>iOS-Specific Bypasses:</strong>
            <ul>
              <li>Jailbreak detection bypass</li>
              <li>SSL pinning circumvention</li>
              <li>TouchID/FaceID bypass</li>
              <li>Anti-debugging evasion</li>
            </ul>
          </li>
        </ul>
        
        <h3>iOS-Specific Vulnerabilities</h3>
        <ul>
          <li><strong>Keychain Vulnerabilities:</strong>
            <ul>
              <li>Keychain access control</li>
              <li>Shared keychain groups</li>
              <li>Keychain dumping techniques</li>
              <li>Backup and restore issues</li>
            </ul>
          </li>
          <li><strong>URL Scheme Attacks:</strong>
            <ul>
              <li>Custom URL scheme hijacking</li>
              <li>Universal link vulnerabilities</li>
              <li>Deep link parameter injection</li>
              <li>Cross-app communication flaws</li>
            </ul>
          </li>
          <li><strong>Data Protection Issues:</strong>
            <ul>
              <li>File protection class bypass</li>
              <li>Backup encryption weaknesses</li>
              <li>Pasteboard data leakage</li>
              <li>Screenshot security issues</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which OWASP Mobile Top 10 category covers SSL pinning bypass vulnerabilities?",
            options: [
              "M1: Improper Platform Usage",
              "M3: Insecure Communication",
              "M5: Insufficient Cryptography",
              "M8: Code Tampering"
            ],
            correctAnswer: 1,
            explanation: "SSL pinning bypass vulnerabilities fall under M3: Insecure Communication, as they involve weaknesses in the secure communication between mobile applications and backend servers."
          },
          {
            question: "What is the primary tool used for runtime manipulation in both Android and iOS applications?",
            options: [
              "Burp Suite",
              "JADX",
              "Frida",
              "APKTool"
            ],
            correctAnswer: 2,
            explanation: "Frida is the primary tool used for runtime manipulation in both Android and iOS applications, providing dynamic instrumentation capabilities for method hooking and runtime analysis."
          },
          {
            question: "Which Android component is most commonly exploited through intent-based attacks?",
            options: [
              "Services",
              "Exported activities and broadcast receivers",
              "Content providers",
              "Application class"
            ],
            correctAnswer: 1,
            explanation: "Exported activities and broadcast receivers are most commonly exploited through intent-based attacks because they can receive intents from other applications, potentially leading to unauthorized access or data exposure."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
