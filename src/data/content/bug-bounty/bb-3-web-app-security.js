/**
 * Web Application Security Fundamentals Module
 */

export const webAppSecurityContent = {
  id: "bb-3",
  pathId: "bug-bounty",
  title: "Web Application Security Fundamentals",
  description: "Master web application security concepts, architecture, and common vulnerabilities. Learn how web applications work and where security weaknesses typically occur.",
  objectives: [
    "Understand web application architecture and components",
    "Learn HTTP protocol and security implications",
    "Master client-server communication security",
    "Understand authentication and session management",
    "Learn input validation and output encoding principles",
    "Identify common web application attack vectors"
  ],
  difficulty: "Beginner",
  estimatedTime: 105,
  sections: [
    {
      title: "Web Application Architecture",
      content: `
        <h2>Web Application Architecture and Security</h2>
        <p>Understanding web application architecture is fundamental to identifying security vulnerabilities and implementing effective security measures.</p>
        
        <h3>Web Application Components</h3>
        <ul>
          <li><strong>Client-Side Components:</strong>
            <ul>
              <li>Web browsers and user agents</li>
              <li>HTML, CSS, and JavaScript</li>
              <li>Client-side frameworks (React, Angular, Vue)</li>
              <li>Mobile applications and APIs</li>
            </ul>
          </li>
          <li><strong>Server-Side Components:</strong>
            <ul>
              <li>Web servers (Apache, Nginx, IIS)</li>
              <li>Application servers and runtime environments</li>
              <li>Server-side languages (PHP, Python, Java, .NET)</li>
              <li>Frameworks and libraries</li>
            </ul>
          </li>
          <li><strong>Database Layer:</strong>
            <ul>
              <li>Relational databases (MySQL, PostgreSQL, SQL Server)</li>
              <li>NoSQL databases (MongoDB, Redis, Elasticsearch)</li>
              <li>Database management systems</li>
              <li>Data access layers and ORMs</li>
            </ul>
          </li>
          <li><strong>Infrastructure Components:</strong>
            <ul>
              <li>Load balancers and reverse proxies</li>
              <li>Content delivery networks (CDNs)</li>
              <li>Firewalls and security appliances</li>
              <li>Cloud services and containers</li>
            </ul>
          </li>
        </ul>
        
        <h3>Web Application Security Models</h3>
        <ul>
          <li><strong>Same-Origin Policy:</strong>
            <ul>
              <li>Fundamental browser security mechanism</li>
              <li>Restricts cross-origin resource access</li>
              <li>Origin defined by protocol, domain, and port</li>
              <li>Exceptions and bypass mechanisms</li>
            </ul>
          </li>
          <li><strong>Content Security Policy (CSP):</strong>
            <ul>
              <li>HTTP header-based security mechanism</li>
              <li>Controls resource loading and execution</li>
              <li>Prevents XSS and data injection attacks</li>
              <li>Policy directives and implementation</li>
            </ul>
          </li>
          <li><strong>Cross-Origin Resource Sharing (CORS):</strong>
            <ul>
              <li>Mechanism for controlled cross-origin access</li>
              <li>Preflight requests and headers</li>
              <li>Security implications and misconfigurations</li>
              <li>Best practices for implementation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Architecture Patterns</h3>
        <ul>
          <li><strong>Multi-Tier Architecture:</strong>
            <ul>
              <li>Presentation, business logic, and data tiers</li>
              <li>Separation of concerns and security boundaries</li>
              <li>Trust boundaries and validation points</li>
              <li>Attack surface considerations</li>
            </ul>
          </li>
          <li><strong>Microservices Architecture:</strong>
            <ul>
              <li>Distributed services and APIs</li>
              <li>Service-to-service communication</li>
              <li>Authentication and authorization challenges</li>
              <li>Network security and service mesh</li>
            </ul>
          </li>
          <li><strong>Single Page Applications (SPAs):</strong>
            <ul>
              <li>Client-side rendering and routing</li>
              <li>API-driven architecture</li>
              <li>Token-based authentication</li>
              <li>Client-side security considerations</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "HTTP Protocol and Security",
      content: `
        <h2>HTTP Protocol and Security Implications</h2>
        <p>The HTTP protocol is the foundation of web communication, and understanding its security features and limitations is crucial for web application security.</p>
        
        <h3>HTTP Protocol Fundamentals</h3>
        <ul>
          <li><strong>HTTP Methods:</strong>
            <ul>
              <li>GET, POST, PUT, DELETE, PATCH, OPTIONS</li>
              <li>Idempotent vs. non-idempotent methods</li>
              <li>Security implications of different methods</li>
              <li>Method override and security bypass</li>
            </ul>
          </li>
          <li><strong>HTTP Headers:</strong>
            <ul>
              <li>Request and response headers</li>
              <li>Security-related headers</li>
              <li>Custom headers and information disclosure</li>
              <li>Header injection vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Status Codes:</strong>
            <ul>
              <li>1xx Informational, 2xx Success, 3xx Redirection</li>
              <li>4xx Client Error, 5xx Server Error</li>
              <li>Security implications of status codes</li>
              <li>Information leakage through error messages</li>
            </ul>
          </li>
        </ul>
        
        <h3>HTTPS and Transport Security</h3>
        <ul>
          <li><strong>TLS/SSL Encryption:</strong>
            <ul>
              <li>Encryption of data in transit</li>
              <li>Certificate validation and trust</li>
              <li>Perfect Forward Secrecy (PFS)</li>
              <li>TLS version and cipher suite security</li>
            </ul>
          </li>
          <li><strong>HTTP Strict Transport Security (HSTS):</strong>
            <ul>
              <li>Enforces HTTPS connections</li>
              <li>Prevents protocol downgrade attacks</li>
              <li>Preload lists and browser support</li>
              <li>Implementation best practices</li>
            </ul>
          </li>
          <li><strong>Certificate Security:</strong>
            <ul>
              <li>Certificate authorities and trust chains</li>
              <li>Certificate pinning and validation</li>
              <li>Extended Validation (EV) certificates</li>
              <li>Certificate transparency and monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Headers</h3>
        <ul>
          <li><strong>Content Security Policy (CSP):</strong>
            <ul>
              <li>Controls resource loading and execution</li>
              <li>Prevents XSS and injection attacks</li>
              <li>Policy directives and nonce/hash usage</li>
              <li>Reporting and monitoring violations</li>
            </ul>
          </li>
          <li><strong>X-Frame-Options:</strong>
            <ul>
              <li>Prevents clickjacking attacks</li>
              <li>Controls iframe embedding</li>
              <li>DENY, SAMEORIGIN, ALLOW-FROM values</li>
              <li>Frame-ancestors CSP directive</li>
            </ul>
          </li>
          <li><strong>X-Content-Type-Options:</strong>
            <ul>
              <li>Prevents MIME type sniffing</li>
              <li>nosniff directive</li>
              <li>Content type confusion attacks</li>
              <li>File upload security implications</li>
            </ul>
          </li>
          <li><strong>Referrer Policy:</strong>
            <ul>
              <li>Controls referrer information leakage</li>
              <li>Privacy and security implications</li>
              <li>Policy values and browser support</li>
              <li>Cross-origin referrer handling</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Authentication and Session Management",
      content: `
        <h2>Authentication and Session Management Security</h2>
        <p>Proper authentication and session management are critical for web application security, controlling user access and maintaining secure user sessions.</p>
        
        <h3>Authentication Mechanisms</h3>
        <ul>
          <li><strong>Password-Based Authentication:</strong>
            <ul>
              <li>Username and password validation</li>
              <li>Password strength requirements</li>
              <li>Secure password storage (hashing and salting)</li>
              <li>Password reset and recovery mechanisms</li>
            </ul>
          </li>
          <li><strong>Multi-Factor Authentication (MFA):</strong>
            <ul>
              <li>Something you know, have, and are</li>
              <li>TOTP, SMS, and hardware tokens</li>
              <li>Biometric authentication</li>
              <li>Backup codes and recovery methods</li>
            </ul>
          </li>
          <li><strong>Token-Based Authentication:</strong>
            <ul>
              <li>JSON Web Tokens (JWT)</li>
              <li>OAuth 2.0 and OpenID Connect</li>
              <li>API keys and bearer tokens</li>
              <li>Token validation and expiration</li>
            </ul>
          </li>
          <li><strong>Certificate-Based Authentication:</strong>
            <ul>
              <li>Client certificates and mutual TLS</li>
              <li>Smart cards and hardware tokens</li>
              <li>Certificate validation and revocation</li>
              <li>PKI infrastructure requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>Session Management</h3>
        <ul>
          <li><strong>Session Lifecycle:</strong>
            <ul>
              <li>Session creation and initialization</li>
              <li>Session maintenance and renewal</li>
              <li>Session termination and cleanup</li>
              <li>Concurrent session handling</li>
            </ul>
          </li>
          <li><strong>Session Storage:</strong>
            <ul>
              <li>Server-side session storage</li>
              <li>Database and in-memory storage</li>
              <li>Distributed session management</li>
              <li>Session clustering and replication</li>
            </ul>
          </li>
          <li><strong>Session Security:</strong>
            <ul>
              <li>Session ID generation and entropy</li>
              <li>Session fixation prevention</li>
              <li>Session hijacking protection</li>
              <li>Secure cookie attributes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Authentication Vulnerabilities</h3>
        <ul>
          <li><strong>Broken Authentication:</strong>
            <ul>
              <li>Weak password policies</li>
              <li>Credential stuffing and brute force</li>
              <li>Session management flaws</li>
              <li>Authentication bypass vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Session Attacks:</strong>
            <ul>
              <li>Session fixation attacks</li>
              <li>Session hijacking and sidejacking</li>
              <li>Cross-site request forgery (CSRF)</li>
              <li>Session replay attacks</li>
            </ul>
          </li>
          <li><strong>Authorization Flaws:</strong>
            <ul>
              <li>Privilege escalation</li>
              <li>Insecure direct object references</li>
              <li>Missing function-level access control</li>
              <li>Business logic bypass</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary purpose of the Same-Origin Policy in web browsers?",
            options: [
              "To improve website performance",
              "To restrict cross-origin resource access for security",
              "To enable cross-domain communication",
              "To manage browser cookies"
            ],
            correctAnswer: 1,
            explanation: "The Same-Origin Policy is a fundamental browser security mechanism that restricts cross-origin resource access to prevent malicious websites from accessing sensitive data from other origins."
          },
          {
            question: "Which HTTP security header helps prevent clickjacking attacks?",
            options: [
              "Content-Security-Policy",
              "X-Frame-Options",
              "X-Content-Type-Options",
              "Strict-Transport-Security"
            ],
            correctAnswer: 1,
            explanation: "X-Frame-Options header prevents clickjacking attacks by controlling whether a page can be embedded in an iframe, with values like DENY or SAMEORIGIN."
          },
          {
            question: "What is the most secure way to store passwords in a web application?",
            options: [
              "Plain text storage",
              "Simple MD5 hashing",
              "Salted and hashed using strong algorithms like bcrypt",
              "Base64 encoding"
            ],
            correctAnswer: 2,
            explanation: "Passwords should be stored using strong hashing algorithms like bcrypt, scrypt, or Argon2 with unique salts to protect against rainbow table and brute force attacks."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
