/**
 * OWASP Top 10 Vulnerabilities Module
 */

export const owaspTop10Content = {
  id: "bb-4",
  pathId: "bug-bounty",
  title: "OWASP Top 10 Vulnerabilities",
  description: "Master the OWASP Top 10 web application security risks, understanding how to identify, exploit, and report these critical vulnerabilities in bug bounty programs.",
  objectives: [
    "Understand the OWASP Top 10 framework and methodology",
    "Learn to identify and exploit injection vulnerabilities",
    "Master authentication and session management flaws",
    "Develop skills in finding security misconfigurations",
    "Learn to exploit broken access controls",
    "Create comprehensive vulnerability reports for each category"
  ],
  difficulty: "Intermediate",
  estimatedTime: 120,
  sections: [
    {
      title: "OWASP Top 10 Framework",
      content: `
        <h2>OWASP Top 10 Web Application Security Risks</h2>
        <p>The OWASP Top 10 represents the most critical web application security risks, providing a foundation for bug bounty hunters to focus their efforts on high-impact vulnerabilities.</p>
        
        <h3>OWASP Top 10 2021 Categories</h3>
        <ul>
          <li><strong>A01:2021 - Broken Access Control:</strong>
            <ul>
              <li>Vertical privilege escalation</li>
              <li>Horizontal privilege escalation</li>
              <li>Insecure direct object references (IDOR)</li>
              <li>Missing function-level access controls</li>
            </ul>
          </li>
          <li><strong>A02:2021 - Cryptographic Failures:</strong>
            <ul>
              <li>Weak encryption algorithms</li>
              <li>Improper key management</li>
              <li>Data transmission without encryption</li>
              <li>Weak random number generation</li>
            </ul>
          </li>
          <li><strong>A03:2021 - Injection:</strong>
            <ul>
              <li>SQL injection (SQLi)</li>
              <li>Cross-site scripting (XSS)</li>
              <li>Command injection</li>
              <li>LDAP and NoSQL injection</li>
            </ul>
          </li>
          <li><strong>A04:2021 - Insecure Design:</strong>
            <ul>
              <li>Missing security controls</li>
              <li>Ineffective control design</li>
              <li>Threat modeling failures</li>
              <li>Secure design pattern violations</li>
            </ul>
          </li>
          <li><strong>A05:2021 - Security Misconfiguration:</strong>
            <ul>
              <li>Default configurations</li>
              <li>Incomplete configurations</li>
              <li>Open cloud storage</li>
              <li>Verbose error messages</li>
            </ul>
          </li>
        </ul>
        
        <h3>Remaining OWASP Top 10 Categories</h3>
        <ul>
          <li><strong>A06:2021 - Vulnerable and Outdated Components:</strong>
            <ul>
              <li>Outdated software versions</li>
              <li>Vulnerable dependencies</li>
              <li>Unsupported components</li>
              <li>Missing security patches</li>
            </ul>
          </li>
          <li><strong>A07:2021 - Identification and Authentication Failures:</strong>
            <ul>
              <li>Weak password policies</li>
              <li>Session management flaws</li>
              <li>Credential stuffing vulnerabilities</li>
              <li>Missing multi-factor authentication</li>
            </ul>
          </li>
          <li><strong>A08:2021 - Software and Data Integrity Failures:</strong>
            <ul>
              <li>Unsigned software updates</li>
              <li>Insecure CI/CD pipelines</li>
              <li>Auto-update without verification</li>
              <li>Serialization vulnerabilities</li>
            </ul>
          </li>
          <li><strong>A09:2021 - Security Logging and Monitoring Failures:</strong>
            <ul>
              <li>Insufficient logging</li>
              <li>Missing alerting mechanisms</li>
              <li>Inadequate incident response</li>
              <li>Log tampering vulnerabilities</li>
            </ul>
          </li>
          <li><strong>A10:2021 - Server-Side Request Forgery (SSRF):</strong>
            <ul>
              <li>Internal network access</li>
              <li>Cloud metadata exploitation</li>
              <li>Port scanning and service discovery</li>
              <li>Blind SSRF techniques</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Injection Vulnerabilities",
      content: `
        <h2>Injection Vulnerability Hunting</h2>
        <p>Injection flaws occur when untrusted data is sent to an interpreter as part of a command or query, allowing attackers to execute unintended commands or access data.</p>
        
        <h3>SQL Injection (SQLi)</h3>
        <ul>
          <li><strong>Detection Techniques:</strong>
            <ul>
              <li>Error-based SQL injection</li>
              <li>Boolean-based blind SQLi</li>
              <li>Time-based blind SQLi</li>
              <li>Union-based SQLi</li>
            </ul>
          </li>
          <li><strong>Common Injection Points:</strong>
            <ul>
              <li>URL parameters and form inputs</li>
              <li>HTTP headers and cookies</li>
              <li>JSON and XML data</li>
              <li>File upload functionality</li>
            </ul>
          </li>
          <li><strong>Advanced SQLi Techniques:</strong>
            <ul>
              <li>Second-order SQL injection</li>
              <li>NoSQL injection attacks</li>
              <li>Stored procedure exploitation</li>
              <li>Database-specific payloads</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cross-Site Scripting (XSS)</h3>
        <ul>
          <li><strong>XSS Types:</strong>
            <ul>
              <li>Reflected XSS (Non-persistent)</li>
              <li>Stored XSS (Persistent)</li>
              <li>DOM-based XSS</li>
              <li>Blind XSS</li>
            </ul>
          </li>
          <li><strong>XSS Discovery Methods:</strong>
            <ul>
              <li>Manual payload testing</li>
              <li>Automated scanning tools</li>
              <li>Source code analysis</li>
              <li>Browser developer tools</li>
            </ul>
          </li>
          <li><strong>Bypass Techniques:</strong>
            <ul>
              <li>Filter evasion methods</li>
              <li>Encoding and obfuscation</li>
              <li>Context-specific payloads</li>
              <li>WAF bypass techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Command Injection</h3>
        <ul>
          <li><strong>OS Command Injection:</strong>
            <ul>
              <li>Direct command execution</li>
              <li>Command chaining techniques</li>
              <li>Blind command injection</li>
              <li>Time-based detection</li>
            </ul>
          </li>
          <li><strong>Code Injection:</strong>
            <ul>
              <li>Server-side template injection</li>
              <li>Expression language injection</li>
              <li>PHP code injection</li>
              <li>Python/Ruby code injection</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Access Control and Authentication Flaws",
      content: `
        <h2>Access Control and Authentication Vulnerabilities</h2>
        <p>Broken access control and authentication failures are among the most critical vulnerabilities, often leading to complete system compromise.</p>
        
        <h3>Broken Access Control (A01:2021)</h3>
        <ul>
          <li><strong>Vertical Privilege Escalation:</strong>
            <ul>
              <li>Admin panel access bypass</li>
              <li>Role-based access control flaws</li>
              <li>Function-level authorization bypass</li>
              <li>API endpoint privilege escalation</li>
            </ul>
          </li>
          <li><strong>Horizontal Privilege Escalation:</strong>
            <ul>
              <li>Insecure Direct Object References (IDOR)</li>
              <li>User ID enumeration and manipulation</li>
              <li>Session token manipulation</li>
              <li>Parameter tampering attacks</li>
            </ul>
          </li>
          <li><strong>Testing Methodologies:</strong>
            <ul>
              <li>Parameter fuzzing and manipulation</li>
              <li>HTTP method tampering</li>
              <li>URL path traversal</li>
              <li>Cookie and token analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Authentication Failures (A07:2021)</h3>
        <ul>
          <li><strong>Weak Authentication Mechanisms:</strong>
            <ul>
              <li>Weak password policies</li>
              <li>Default credentials</li>
              <li>Predictable session tokens</li>
              <li>Missing account lockout</li>
            </ul>
          </li>
          <li><strong>Session Management Flaws:</strong>
            <ul>
              <li>Session fixation attacks</li>
              <li>Session hijacking vulnerabilities</li>
              <li>Insufficient session timeout</li>
              <li>Concurrent session handling</li>
            </ul>
          </li>
          <li><strong>Multi-Factor Authentication Bypass:</strong>
            <ul>
              <li>MFA implementation flaws</li>
              <li>Backup code vulnerabilities</li>
              <li>SMS/Email OTP bypass</li>
              <li>TOTP synchronization issues</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Misconfiguration (A05:2021)</h3>
        <ul>
          <li><strong>Common Misconfigurations:</strong>
            <ul>
              <li>Default application settings</li>
              <li>Unnecessary features enabled</li>
              <li>Missing security headers</li>
              <li>Verbose error messages</li>
            </ul>
          </li>
          <li><strong>Cloud Security Misconfigurations:</strong>
            <ul>
              <li>Open S3 buckets</li>
              <li>Misconfigured cloud databases</li>
              <li>Insecure API gateways</li>
              <li>Overprivileged IAM roles</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which OWASP Top 10 2021 category covers Insecure Direct Object References (IDOR)?",
            options: [
              "A03:2021 - Injection",
              "A01:2021 - Broken Access Control",
              "A07:2021 - Identification and Authentication Failures",
              "A05:2021 - Security Misconfiguration"
            ],
            correctAnswer: 1,
            explanation: "IDOR vulnerabilities fall under A01:2021 - Broken Access Control, as they involve unauthorized access to objects or resources by manipulating references."
          },
          {
            question: "What is the primary difference between reflected and stored XSS?",
            options: [
              "Reflected XSS is more dangerous",
              "Stored XSS persists on the server while reflected XSS is temporary",
              "Reflected XSS affects more users",
              "There is no difference"
            ],
            correctAnswer: 1,
            explanation: "Stored XSS persists on the server and affects multiple users who view the infected content, while reflected XSS is temporary and typically affects only the user who clicks a malicious link."
          },
          {
            question: "Which technique is most effective for detecting blind SQL injection?",
            options: [
              "Error message analysis",
              "Union-based queries",
              "Time-based delays",
              "Direct database access"
            ],
            correctAnswer: 2,
            explanation: "Time-based delays are most effective for detecting blind SQL injection because they don't rely on visible error messages or data output, making them useful when other detection methods fail."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
