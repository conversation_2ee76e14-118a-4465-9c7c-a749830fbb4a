/**
 * XXE (XML External Entity) Exploitation Module
 */

export const xxeExploitationContent = {
  id: "bb-16",
  pathId: "bug-bounty",
  title: "XXE (XML External Entity) Exploitation",
  description: "Master XML External Entity (XXE) vulnerabilities, including detection techniques, exploitation methods, and advanced attack vectors for data exfiltration and system compromise.",
  objectives: [
    "Understand XXE vulnerability fundamentals and XML processing",
    "Learn XXE detection and identification techniques",
    "Master basic and advanced XXE exploitation methods",
    "Develop skills in blind XXE and out-of-band techniques",
    "Learn XXE prevention and mitigation strategies",
    "Create comprehensive XXE testing methodologies"
  ],
  difficulty: "Advanced",
  estimatedTime: 120,
  sections: [
    {
      title: "XXE Fundamentals and XML Processing",
      content: `
        <h2>XML External Entity (XXE) Fundamentals</h2>
        <p>XXE vulnerabilities occur when XML parsers process external entity references without proper validation, allowing attackers to access local files, internal networks, and sensitive data.</p>
        
        <h3>XML and Entity Concepts</h3>
        <ul>
          <li><strong>XML Structure and Components:</strong>
            <ul>
              <li>XML document structure and syntax</li>
              <li>Document Type Definition (DTD)</li>
              <li>Entity declarations and references</li>
              <li>XML namespaces and schemas</li>
            </ul>
          </li>
          <li><strong>Entity Types:</strong>
            <ul>
              <li>Internal entities (defined within DTD)</li>
              <li>External entities (referenced from external sources)</li>
              <li>Parameter entities (used within DTD)</li>
              <li>General entities (used in document content)</li>
            </ul>
          </li>
          <li><strong>XML Parser Behavior:</strong>
            <ul>
              <li>Entity resolution and expansion</li>
              <li>External resource loading</li>
              <li>DTD processing and validation</li>
              <li>Error handling and reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>XXE Vulnerability Types</h3>
        <ul>
          <li><strong>Classic XXE:</strong>
            <ul>
              <li>Direct file disclosure</li>
              <li>Internal network scanning</li>
              <li>Service enumeration</li>
              <li>Configuration file access</li>
            </ul>
          </li>
          <li><strong>Blind XXE:</strong>
            <ul>
              <li>No direct output visibility</li>
              <li>Out-of-band data exfiltration</li>
              <li>Error-based information gathering</li>
              <li>Time-based confirmation</li>
            </ul>
          </li>
          <li><strong>XXE via File Upload:</strong>
            <ul>
              <li>Document processing vulnerabilities</li>
              <li>Image metadata exploitation</li>
              <li>Office document attacks</li>
              <li>SVG file processing</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common XXE Attack Vectors</h3>
        <ul>
          <li><strong>Direct XML Input:</strong>
            <ul>
              <li>API endpoint XML processing</li>
              <li>SOAP web service exploitation</li>
              <li>XML-based configuration</li>
              <li>Data import functionality</li>
            </ul>
          </li>
          <li><strong>Indirect XML Processing:</strong>
            <ul>
              <li>File upload and conversion</li>
              <li>Document generation services</li>
              <li>RSS and feed processing</li>
              <li>XML-based authentication</li>
            </ul>
          </li>
          <li><strong>Content-Type Manipulation:</strong>
            <ul>
              <li>JSON to XML conversion</li>
              <li>Form data to XML processing</li>
              <li>Content-Type header modification</li>
              <li>Accept header manipulation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "XXE Detection and Exploitation",
      content: `
        <h2>XXE Detection and Basic Exploitation</h2>
        <p>Effective XXE detection requires understanding XML processing points and systematically testing for external entity resolution capabilities.</p>
        
        <h3>Detection Techniques</h3>
        <ul>
          <li><strong>Input Vector Identification:</strong>
            <ul>
              <li>XML parameter discovery</li>
              <li>Content-Type analysis</li>
              <li>File upload functionality</li>
              <li>API endpoint enumeration</li>
            </ul>
          </li>
          <li><strong>Basic XXE Testing:</strong>
            <ul>
              <li>Simple entity declaration</li>
              <li>Local file inclusion attempts</li>
              <li>External DTD references</li>
              <li>Error message analysis</li>
            </ul>
          </li>
          <li><strong>Response Analysis:</strong>
            <ul>
              <li>Entity expansion in output</li>
              <li>Error message examination</li>
              <li>Response time variations</li>
              <li>Content length changes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Basic XXE Exploitation</h3>
        <ul>
          <li><strong>File Disclosure Attacks:</strong>
            <ul>
              <li>Local file system access</li>
              <li>Configuration file retrieval</li>
              <li>Source code disclosure</li>
              <li>Log file examination</li>
            </ul>
          </li>
          <li><strong>Internal Network Scanning:</strong>
            <ul>
              <li>Port scanning via XXE</li>
              <li>Service enumeration</li>
              <li>Internal host discovery</li>
              <li>Network topology mapping</li>
            </ul>
          </li>
          <li><strong>Denial of Service:</strong>
            <ul>
              <li>Billion laughs attack</li>
              <li>Quadratic blowup</li>
              <li>External entity recursion</li>
              <li>Resource exhaustion</li>
            </ul>
          </li>
        </ul>
        
        <h3>XXE Payload Construction</h3>
        <ul>
          <li><strong>Basic Payload Structure:</strong>
            <ul>
              <li>DTD declaration syntax</li>
              <li>Entity definition format</li>
              <li>External reference construction</li>
              <li>Parameter entity usage</li>
            </ul>
          </li>
          <li><strong>File Access Payloads:</strong>
            <ul>
              <li>Unix/Linux file system paths</li>
              <li>Windows file system access</li>
              <li>URL-based external references</li>
              <li>Protocol scheme exploitation</li>
            </ul>
          </li>
          <li><strong>Network Scanning Payloads:</strong>
            <ul>
              <li>HTTP-based internal requests</li>
              <li>FTP and other protocol abuse</li>
              <li>Port enumeration techniques</li>
              <li>Service fingerprinting</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced XXE Techniques and Blind Exploitation",
      content: `
        <h2>Advanced XXE Techniques and Blind Exploitation</h2>
        <p>Advanced XXE exploitation involves blind techniques, out-of-band data exfiltration, and sophisticated attack chains for maximum impact.</p>
        
        <h3>Blind XXE Exploitation</h3>
        <ul>
          <li><strong>Out-of-Band Techniques:</strong>
            <ul>
              <li>External DTD hosting</li>
              <li>HTTP callback methods</li>
              <li>DNS interaction testing</li>
              <li>FTP data exfiltration</li>
            </ul>
          </li>
          <li><strong>Error-Based Exploitation:</strong>
            <ul>
              <li>Induced error messages</li>
              <li>File existence confirmation</li>
              <li>Directory enumeration</li>
              <li>Content-based error analysis</li>
            </ul>
          </li>
          <li><strong>Time-Based Confirmation:</strong>
            <ul>
              <li>Response delay analysis</li>
              <li>Resource loading timing</li>
              <li>Network timeout exploitation</li>
              <li>Processing time variations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Data Exfiltration Techniques</h3>
        <ul>
          <li><strong>Direct Data Extraction:</strong>
            <ul>
              <li>File content in XML output</li>
              <li>Base64 encoding methods</li>
              <li>Character-by-character extraction</li>
              <li>Chunked data retrieval</li>
            </ul>
          </li>
          <li><strong>Out-of-Band Exfiltration:</strong>
            <ul>
              <li>HTTP parameter injection</li>
              <li>DNS subdomain encoding</li>
              <li>FTP filename manipulation</li>
              <li>Email-based data transfer</li>
            </ul>
          </li>
          <li><strong>Advanced Exfiltration:</strong>
            <ul>
              <li>Multi-stage data extraction</li>
              <li>Encrypted data channels</li>
              <li>Steganographic techniques</li>
              <li>Social media platform abuse</li>
            </ul>
          </li>
        </ul>
        
        <h3>XXE Prevention and Mitigation</h3>
        <ul>
          <li><strong>Parser Configuration:</strong>
            <ul>
              <li>Disable external entity processing</li>
              <li>Disable DTD processing</li>
              <li>Use secure parser settings</li>
              <li>Implement entity resolution restrictions</li>
            </ul>
          </li>
          <li><strong>Input Validation:</strong>
            <ul>
              <li>XML schema validation</li>
              <li>Content filtering and sanitization</li>
              <li>Whitelist-based validation</li>
              <li>Input size limitations</li>
            </ul>
          </li>
          <li><strong>Security Controls:</strong>
            <ul>
              <li>Network segmentation</li>
              <li>File system access restrictions</li>
              <li>Monitoring and logging</li>
              <li>Web application firewalls</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary difference between internal and external XML entities?",
            options: [
              "Internal entities are more dangerous",
              "External entities reference resources outside the XML document",
              "Internal entities cannot be exploited",
              "External entities are always encrypted"
            ],
            correctAnswer: 1,
            explanation: "External entities reference resources outside the XML document (like files or URLs), while internal entities are defined within the DTD itself. External entities enable XXE attacks by allowing access to external resources."
          },
          {
            question: "Which technique is most effective for exploiting blind XXE vulnerabilities?",
            options: [
              "Direct file inclusion",
              "Out-of-band data exfiltration",
              "Error message analysis only",
              "Response time measurement only"
            ],
            correctAnswer: 1,
            explanation: "Out-of-band data exfiltration is most effective for blind XXE because it allows data extraction even when the application doesn't display the XML processing results directly, using external servers to receive the data."
          },
          {
            question: "What is the 'billion laughs' attack in XXE exploitation?",
            options: [
              "A data exfiltration technique",
              "A file disclosure method",
              "A denial of service attack using entity expansion",
              "A network scanning technique"
            ],
            correctAnswer: 2,
            explanation: "The 'billion laughs' attack is a denial of service attack that uses recursive entity expansion to consume excessive memory and CPU resources, potentially crashing the XML parser or application."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
