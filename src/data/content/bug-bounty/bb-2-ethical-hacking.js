/**
 * Ethical Hacking Fundamentals Module
 */

export const ethicalHackingContent = {
  id: "bb-2",
  pathId: "bug-bounty",
  title: "Ethical Hacking Fundamentals",
  description: "Master the principles and practices of ethical hacking, including legal frameworks, responsible disclosure, and professional conduct in security research.",
  objectives: [
    "Understand the principles and ethics of ethical hacking",
    "Learn legal frameworks and compliance requirements",
    "Master responsible disclosure processes and timelines",
    "Understand the difference between ethical and malicious hacking",
    "Learn professional conduct and industry standards",
    "Develop a security researcher mindset and methodology"
  ],
  difficulty: "Beginner",
  estimatedTime: 95,
  sections: [
    {
      title: "Ethical Hacking Principles",
      content: `
        <h2>Ethical Hacking Principles and Philosophy</h2>
        <p>Ethical hacking is the practice of intentionally probing systems and networks for vulnerabilities in a legal and responsible manner to improve security.</p>
        
        <h3>Core Principles of Ethical Hacking</h3>
        <ul>
          <li><strong>Authorization:</strong>
            <ul>
              <li>Always obtain explicit permission before testing</li>
              <li>Respect scope and boundaries defined by the organization</li>
              <li>Document authorization and maintain proof</li>
              <li>Never exceed authorized testing parameters</li>
            </ul>
          </li>
          <li><strong>Responsible Disclosure:</strong>
            <ul>
              <li>Report vulnerabilities to the appropriate parties</li>
              <li>Allow reasonable time for remediation</li>
              <li>Protect sensitive information during the process</li>
              <li>Follow established disclosure timelines</li>
            </ul>
          </li>
          <li><strong>Do No Harm:</strong>
            <ul>
              <li>Minimize impact on systems and users</li>
              <li>Avoid data destruction or service disruption</li>
              <li>Use least intrusive testing methods</li>
              <li>Respect privacy and confidentiality</li>
            </ul>
          </li>
          <li><strong>Professional Integrity:</strong>
            <ul>
              <li>Maintain honesty and transparency</li>
              <li>Avoid conflicts of interest</li>
              <li>Respect intellectual property</li>
              <li>Uphold professional standards</li>
            </ul>
          </li>
        </ul>
        
        <h3>Ethical vs. Malicious Hacking</h3>
        <div class="comparison-table">
          <h4>Ethical Hacking:</h4>
          <ul>
            <li>Authorized and legal testing</li>
            <li>Constructive intent to improve security</li>
            <li>Responsible disclosure of findings</li>
            <li>Respect for privacy and data</li>
            <li>Professional conduct and standards</li>
            <li>Collaboration with organizations</li>
          </ul>
          
          <h4>Malicious Hacking:</h4>
          <ul>
            <li>Unauthorized access and illegal activities</li>
            <li>Intent to cause harm or gain unauthorized benefit</li>
            <li>Exploitation without disclosure</li>
            <li>Data theft or destruction</li>
            <li>Disregard for legal and ethical boundaries</li>
            <li>Adversarial relationship with targets</li>
          </ul>
        </div>
        
        <h3>The Security Researcher Mindset</h3>
        <ul>
          <li><strong>Curiosity and Persistence:</strong>
            <ul>
              <li>Question assumptions and explore edge cases</li>
              <li>Persistent investigation of potential vulnerabilities</li>
              <li>Creative thinking and problem-solving</li>
              <li>Continuous learning and skill development</li>
            </ul>
          </li>
          <li><strong>Systematic Approach:</strong>
            <ul>
              <li>Methodical testing and documentation</li>
              <li>Reproducible proof-of-concept development</li>
              <li>Risk assessment and impact analysis</li>
              <li>Clear communication of findings</li>
            </ul>
          </li>
          <li><strong>Empathy and Collaboration:</strong>
            <ul>
              <li>Understanding organizational constraints</li>
              <li>Constructive feedback and recommendations</li>
              <li>Patience with remediation timelines</li>
              <li>Building trust with security teams</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Legal Frameworks and Compliance",
      content: `
        <h2>Legal Frameworks and Compliance</h2>
        <p>Understanding the legal landscape is crucial for ethical hackers to operate within legal boundaries and avoid criminal liability.</p>
        
        <h3>Key Legal Considerations</h3>
        <ul>
          <li><strong>Computer Fraud and Abuse Act (CFAA) - United States:</strong>
            <ul>
              <li>Federal law governing computer crimes</li>
              <li>Prohibits unauthorized access to protected computers</li>
              <li>Severe penalties for violations</li>
              <li>Importance of explicit authorization</li>
            </ul>
          </li>
          <li><strong>General Data Protection Regulation (GDPR) - European Union:</strong>
            <ul>
              <li>Data protection and privacy regulations</li>
              <li>Requirements for handling personal data</li>
              <li>Consent and lawful basis for processing</li>
              <li>Data subject rights and obligations</li>
            </ul>
          </li>
          <li><strong>International Laws and Jurisdictions:</strong>
            <ul>
              <li>Varying laws across different countries</li>
              <li>Cross-border legal implications</li>
              <li>Extradition and international cooperation</li>
              <li>Local compliance requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>Authorization and Scope</h3>
        <ul>
          <li><strong>Written Authorization:</strong>
            <ul>
              <li>Formal written permission from authorized personnel</li>
              <li>Clear definition of scope and boundaries</li>
              <li>Specific systems and applications included</li>
              <li>Time limitations and testing windows</li>
            </ul>
          </li>
          <li><strong>Bug Bounty Program Terms:</strong>
            <ul>
              <li>Program-specific rules and guidelines</li>
              <li>Scope definition and exclusions</li>
              <li>Prohibited activities and testing methods</li>
              <li>Reporting requirements and timelines</li>
            </ul>
          </li>
          <li><strong>Safe Harbor Provisions:</strong>
            <ul>
              <li>Legal protection for good faith security research</li>
              <li>Compliance with program terms and conditions</li>
              <li>Responsible disclosure requirements</li>
              <li>Limitations and exceptions</li>
            </ul>
          </li>
        </ul>
        
        <h3>Professional Standards and Certifications</h3>
        <ul>
          <li><strong>Industry Certifications:</strong>
            <ul>
              <li>Certified Ethical Hacker (CEH)</li>
              <li>Offensive Security Certified Professional (OSCP)</li>
              <li>GIAC Penetration Tester (GPEN)</li>
              <li>Certified Information Systems Security Professional (CISSP)</li>
            </ul>
          </li>
          <li><strong>Professional Organizations:</strong>
            <ul>
              <li>International Association of Computer Security Professionals (IACSP)</li>
              <li>Information Systems Security Association (ISSA)</li>
              <li>SANS Institute and community</li>
              <li>Local security professional groups</li>
            </ul>
          </li>
          <li><strong>Codes of Ethics:</strong>
            <ul>
              <li>Professional conduct standards</li>
              <li>Confidentiality and integrity requirements</li>
              <li>Continuing education obligations</li>
              <li>Peer accountability and reporting</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Responsible Disclosure Process",
      content: `
        <h2>Responsible Disclosure Process</h2>
        <p>Responsible disclosure is a cornerstone of ethical hacking, ensuring that vulnerabilities are reported and remediated in a way that protects users while giving organizations time to fix issues.</p>
        
        <h3>Disclosure Timeline and Process</h3>
        <ul>
          <li><strong>Initial Discovery and Validation:</strong>
            <ul>
              <li>Confirm the vulnerability exists and is reproducible</li>
              <li>Assess the potential impact and severity</li>
              <li>Document the vulnerability with clear evidence</li>
              <li>Avoid unnecessary exploitation or data access</li>
            </ul>
          </li>
          <li><strong>Initial Report (Day 0):</strong>
            <ul>
              <li>Submit detailed vulnerability report</li>
              <li>Include proof-of-concept and reproduction steps</li>
              <li>Provide impact assessment and recommendations</li>
              <li>Request acknowledgment and timeline for response</li>
            </ul>
          </li>
          <li><strong>Vendor Response (Day 1-7):</strong>
            <ul>
              <li>Acknowledgment of report receipt</li>
              <li>Initial triage and validation</li>
              <li>Assignment of tracking identifier</li>
              <li>Communication of remediation timeline</li>
            </ul>
          </li>
          <li><strong>Remediation Period (Day 7-90):</strong>
            <ul>
              <li>Vendor develops and tests fixes</li>
              <li>Regular communication and status updates</li>
              <li>Researcher cooperation and additional testing</li>
              <li>Coordination of disclosure timeline</li>
            </ul>
          </li>
        </ul>
        
        <h3>Disclosure Best Practices</h3>
        <ul>
          <li><strong>Clear Communication:</strong>
            <ul>
              <li>Professional and respectful tone</li>
              <li>Technical accuracy and completeness</li>
              <li>Clear impact assessment</li>
              <li>Constructive remediation suggestions</li>
            </ul>
          </li>
          <li><strong>Documentation Standards:</strong>
            <ul>
              <li>Detailed vulnerability description</li>
              <li>Step-by-step reproduction instructions</li>
              <li>Screenshots and proof-of-concept code</li>
              <li>Risk assessment and business impact</li>
            </ul>
          </li>
          <li><strong>Coordination and Patience:</strong>
            <ul>
              <li>Reasonable remediation timelines</li>
              <li>Flexibility for complex fixes</li>
              <li>Coordination with multiple stakeholders</li>
              <li>Respect for organizational processes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Public Disclosure Considerations</h3>
        <ul>
          <li><strong>Full Disclosure:</strong>
            <ul>
              <li>Complete technical details after remediation</li>
              <li>Educational value for security community</li>
              <li>Transparency and accountability</li>
              <li>Risk of exploitation by malicious actors</li>
            </ul>
          </li>
          <li><strong>Coordinated Disclosure:</strong>
            <ul>
              <li>Collaboration between researcher and vendor</li>
              <li>Agreed-upon disclosure timeline</li>
              <li>Balanced approach to transparency and security</li>
              <li>Joint public disclosure when appropriate</li>
            </ul>
          </li>
          <li><strong>Responsible Timing:</strong>
            <ul>
              <li>Allow sufficient time for remediation</li>
              <li>Consider user impact and exposure</li>
              <li>Coordinate with security advisories</li>
              <li>Respect vendor communication preferences</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the most important prerequisite before conducting any security testing?",
            options: [
              "Having the right tools",
              "Obtaining explicit written authorization",
              "Understanding the technology",
              "Having sufficient time"
            ],
            correctAnswer: 1,
            explanation: "Explicit written authorization is the most critical prerequisite for ethical hacking. Without proper authorization, security testing could be illegal and result in criminal charges."
          },
          {
            question: "What is the typical timeline for initial vendor response in responsible disclosure?",
            options: [
              "Within 24 hours",
              "Within 1-7 days",
              "Within 30 days",
              "Within 90 days"
            ],
            correctAnswer: 1,
            explanation: "Vendors typically acknowledge vulnerability reports within 1-7 days, providing initial triage and validation of the reported issue."
          },
          {
            question: "Which principle best describes the 'Do No Harm' concept in ethical hacking?",
            options: [
              "Never test production systems",
              "Only use automated tools",
              "Minimize impact on systems and users while testing",
              "Always report vulnerabilities publicly"
            ],
            correctAnswer: 2,
            explanation: "'Do No Harm' means minimizing impact on systems and users during testing, avoiding data destruction or service disruption while still conducting effective security research."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
