/**
 * SSRF (Server-Side Request Forgery) Exploitation Module
 */

export const ssrfExploitationContent = {
  id: "bb-14",
  pathId: "bug-bounty",
  title: "SSRF (Server-Side Request Forgery) Exploitation",
  description: "Master Server-Side Request Forgery (SSRF) vulnerabilities, including detection techniques, exploitation methods, and advanced bypass strategies for modern applications.",
  objectives: [
    "Understand SSRF vulnerability fundamentals and types",
    "Learn SSRF detection and identification techniques",
    "Master basic and advanced SSRF exploitation methods",
    "Develop skills in SSRF filter and protection bypass",
    "Learn cloud-specific SSRF attack vectors",
    "Create comprehensive SSRF testing methodologies"
  ],
  difficulty: "Advanced",
  estimatedTime: 130,
  sections: [
    {
      title: "SSRF Fundamentals and Types",
      content: `
        <h2>Server-Side Request Forgery (SSRF) Fundamentals</h2>
        <p>SSRF vulnerabilities allow attackers to make requests from the server to internal or external resources, potentially accessing sensitive data or services not directly accessible.</p>
        
        <h3>SSRF Vulnerability Types</h3>
        <ul>
          <li><strong>Basic SSRF:</strong>
            <ul>
              <li>Direct server-side request manipulation</li>
              <li>URL parameter exploitation</li>
              <li>File upload and processing abuse</li>
              <li>Webhook and callback manipulation</li>
            </ul>
          </li>
          <li><strong>Blind SSRF:</strong>
            <ul>
              <li>No direct response visibility</li>
              <li>Time-based detection methods</li>
              <li>Out-of-band confirmation techniques</li>
              <li>DNS and HTTP log analysis</li>
            </ul>
          </li>
          <li><strong>Semi-Blind SSRF:</strong>
            <ul>
              <li>Partial response information</li>
              <li>Error message analysis</li>
              <li>Response time variations</li>
              <li>Status code differences</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common SSRF Attack Vectors</h3>
        <ul>
          <li><strong>URL-Based Parameters:</strong>
            <ul>
              <li>Direct URL manipulation</li>
              <li>Redirect and fetch functionality</li>
              <li>Image and media processing</li>
              <li>PDF generation and conversion</li>
            </ul>
          </li>
          <li><strong>File Processing Functions:</strong>
            <ul>
              <li>XML external entity (XXE) exploitation</li>
              <li>SVG file processing</li>
              <li>Office document conversion</li>
              <li>Image metadata processing</li>
            </ul>
          </li>
          <li><strong>API and Integration Points:</strong>
            <ul>
              <li>Webhook configuration</li>
              <li>Third-party service integration</li>
              <li>Proxy and gateway functions</li>
              <li>Health check and monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>SSRF Impact and Consequences</h3>
        <ul>
          <li><strong>Internal Network Access:</strong>
            <ul>
              <li>Internal service enumeration</li>
              <li>Database and cache access</li>
              <li>Administrative interface exposure</li>
              <li>Network topology discovery</li>
            </ul>
          </li>
          <li><strong>Cloud Metadata Exploitation:</strong>
            <ul>
              <li>AWS EC2 metadata service</li>
              <li>Azure instance metadata</li>
              <li>Google Cloud metadata API</li>
              <li>Credential and token extraction</li>
            </ul>
          </li>
          <li><strong>External Service Abuse:</strong>
            <ul>
              <li>Port scanning and reconnaissance</li>
              <li>Service fingerprinting</li>
              <li>Denial of service attacks</li>
              <li>Protocol smuggling</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "SSRF Detection and Identification",
      content: `
        <h2>SSRF Detection and Identification Techniques</h2>
        <p>Effective SSRF detection requires understanding application functionality and systematically testing various input vectors for server-side request capabilities.</p>
        
        <h3>Detection Methodologies</h3>
        <ul>
          <li><strong>Parameter Analysis:</strong>
            <ul>
              <li>URL parameter identification</li>
              <li>Hidden form field discovery</li>
              <li>JSON and XML payload analysis</li>
              <li>HTTP header manipulation</li>
            </ul>
          </li>
          <li><strong>Functionality Mapping:</strong>
            <ul>
              <li>File upload and processing features</li>
              <li>Image and media handling</li>
              <li>Webhook and callback configuration</li>
              <li>Import and export functions</li>
            </ul>
          </li>
          <li><strong>Response Analysis:</strong>
            <ul>
              <li>Response time measurement</li>
              <li>Error message examination</li>
              <li>Content length variations</li>
              <li>HTTP status code analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Testing Techniques</h3>
        <ul>
          <li><strong>Basic SSRF Testing:</strong>
            <ul>
              <li>Localhost and loopback testing</li>
              <li>Internal IP range scanning</li>
              <li>External service requests</li>
              <li>Protocol scheme manipulation</li>
            </ul>
          </li>
          <li><strong>Blind SSRF Detection:</strong>
            <ul>
              <li>DNS interaction testing</li>
              <li>HTTP callback services</li>
              <li>Time-based confirmation</li>
              <li>Out-of-band data exfiltration</li>
            </ul>
          </li>
          <li><strong>Advanced Detection Methods:</strong>
            <ul>
              <li>Protocol smuggling attempts</li>
              <li>Encoding and obfuscation testing</li>
              <li>Redirect chain exploitation</li>
              <li>DNS rebinding techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Detection Tools and Automation</h3>
        <ul>
          <li><strong>Manual Testing Tools:</strong>
            <ul>
              <li>Burp Suite SSRF detection</li>
              <li>OWASP ZAP SSRF scanner</li>
              <li>Custom Python scripts</li>
              <li>Browser developer tools</li>
            </ul>
          </li>
          <li><strong>Automated Scanners:</strong>
            <ul>
              <li>SSRFmap for comprehensive testing</li>
              <li>Nuclei SSRF templates</li>
              <li>Custom automation frameworks</li>
              <li>CI/CD integration tools</li>
            </ul>
          </li>
          <li><strong>Callback and Interaction Services:</strong>
            <ul>
              <li>Burp Collaborator</li>
              <li>Interactsh for out-of-band testing</li>
              <li>Custom callback servers</li>
              <li>DNS interaction monitoring</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced SSRF Exploitation and Bypass",
      content: `
        <h2>Advanced SSRF Exploitation and Bypass Techniques</h2>
        <p>Advanced SSRF exploitation involves bypassing filters, exploiting cloud services, and chaining SSRF with other vulnerabilities for maximum impact.</p>
        
        <h3>Filter and Protection Bypass</h3>
        <ul>
          <li><strong>URL Encoding and Obfuscation:</strong>
            <ul>
              <li>Double URL encoding</li>
              <li>Unicode and UTF-8 encoding</li>
              <li>Mixed case and character variations</li>
              <li>Decimal and hexadecimal IP notation</li>
            </ul>
          </li>
          <li><strong>DNS and Redirect Bypasses:</strong>
            <ul>
              <li>DNS rebinding attacks</li>
              <li>Subdomain takeover exploitation</li>
              <li>HTTP redirect chain abuse</li>
              <li>Short URL service manipulation</li>
            </ul>
          </li>
          <li><strong>Protocol and Scheme Manipulation:</strong>
            <ul>
              <li>Alternative protocol schemes</li>
              <li>Protocol smuggling techniques</li>
              <li>Port specification bypass</li>
              <li>IPv6 address exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud-Specific SSRF Attacks</h3>
        <ul>
          <li><strong>AWS Metadata Exploitation:</strong>
            <ul>
              <li>EC2 metadata service access</li>
              <li>IAM role credential extraction</li>
              <li>Security group enumeration</li>
              <li>User data and instance information</li>
            </ul>
          </li>
          <li><strong>Azure Metadata Service:</strong>
            <ul>
              <li>Instance metadata API access</li>
              <li>Managed identity token extraction</li>
              <li>Subscription and resource information</li>
              <li>Network configuration details</li>
            </ul>
          </li>
          <li><strong>Google Cloud Metadata:</strong>
            <ul>
              <li>Compute metadata server access</li>
              <li>Service account token retrieval</li>
              <li>Project and instance information</li>
              <li>SSH key and configuration data</li>
            </ul>
          </li>
        </ul>
        
        <h3>SSRF Chaining and Advanced Techniques</h3>
        <ul>
          <li><strong>SSRF + RCE Combinations:</strong>
            <ul>
              <li>Internal service exploitation</li>
              <li>Redis and database attacks</li>
              <li>Memcached exploitation</li>
              <li>Docker API abuse</li>
            </ul>
          </li>
          <li><strong>SSRF + Information Disclosure:</strong>
            <ul>
              <li>Internal file system access</li>
              <li>Configuration file retrieval</li>
              <li>Source code disclosure</li>
              <li>Database connection strings</li>
            </ul>
          </li>
          <li><strong>SSRF + Privilege Escalation:</strong>
            <ul>
              <li>Administrative interface access</li>
              <li>Internal API exploitation</li>
              <li>Service account abuse</li>
              <li>Network segmentation bypass</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary difference between basic SSRF and blind SSRF?",
            options: [
              "Basic SSRF is more dangerous",
              "Blind SSRF provides no direct response visibility",
              "Basic SSRF only works on internal networks",
              "Blind SSRF requires authentication"
            ],
            correctAnswer: 1,
            explanation: "Blind SSRF provides no direct response visibility, requiring attackers to use out-of-band techniques like DNS interactions or time-based methods to confirm the vulnerability's existence."
          },
          {
            question: "Which cloud service is most commonly targeted in SSRF attacks for credential extraction?",
            options: [
              "Load balancers",
              "Content delivery networks",
              "Instance metadata services",
              "DNS services"
            ],
            correctAnswer: 2,
            explanation: "Instance metadata services (like AWS EC2 metadata at ***************) are most commonly targeted because they provide access to sensitive information including IAM credentials, instance details, and configuration data."
          },
          {
            question: "What is the most effective technique for bypassing IP-based SSRF filters?",
            options: [
              "Using only HTTPS requests",
              "DNS rebinding and redirect chain exploitation",
              "Increasing request frequency",
              "Using only POST requests"
            ],
            correctAnswer: 1,
            explanation: "DNS rebinding and redirect chain exploitation are most effective for bypassing IP-based filters because they can circumvent blacklist restrictions by using legitimate domains that resolve to internal IPs or redirect to blocked addresses."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
