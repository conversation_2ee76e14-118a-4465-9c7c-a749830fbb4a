/**
 * Bug Bounty Hunting Learning Path
 * Comprehensive vulnerability research and ethical bug bounty hunting
 */

// Import all module content
import { bugBountyIntroContent } from './bb-1-bug-bounty-intro.js';
import { ethicalHackingContent } from './bb-2-ethical-hacking.js';
import { webAppSecurityContent } from './bb-3-web-app-security.js';
import { owaspTop10Content } from './bb-4-owasp-top10.js';
import { networkInfrastructureContent } from './bb-5-network-infrastructure.js';
import { cloudSecurityTestingContent } from './bb-6-cloud-security-testing.js';
import { automationScriptingContent } from './bb-7-automation-scripting.js';
import { mobileSecurityContent } from './bb-8-mobile-security.js';
import { businessLogicContent } from './bb-9-business-logic.js';
import { advancedExploitationContent } from './bb-10-advanced-exploitation.js';
import { professionalReportWritingContent } from './bb-11-report-writing.js';
import { legalComplianceContent } from './bb-12-legal-compliance.js';
import { communityNetworkingContent } from './bb-13-community-networking.js';
import { ssrfExploitationContent } from './bb-14-ssrf-exploitation.js';
import { deserializationContent } from './bb-15-deserialization.js';
import { xxeExploitationContent } from './bb-16-xxe-exploitation.js';
import { csrfExploitationContent } from './bb-17-csrf-exploitation.js';
import { fileUploadContent } from './bb-18-file-upload.js';
import { authenticationBypassContent } from './bb-19-authentication-bypass.js';
import { idorExploitationContent } from './bb-20-idor-exploitation.js';
import { programSelectionContent } from './program-selection.js';
import { reconMethodologyContent } from './recon-methodology.js';
import { webAppTestingContent } from './web-app-testing.js';
import { mobileAppTestingContent } from './mobile-app-testing.js';
import { apiTestingContent } from './api-testing.js';
// Temporarily comment out missing imports until files are created
// import { reportWritingContent } from './report-writing.js';
// import { businessLogicFlawsContent } from './business-logic-flaws.js';
// import { advancedExploitationContent } from './advanced-exploitation.js';
// import { communityNetworkingContent } from './community-networking.js';
// import { legalEthicalConsiderationsContent } from './legal-ethical-considerations.js';
// import { toolsFrameworksContent } from './tools-frameworks.js';

export const bugBountyLearningPath = {
  id: "bug-bounty",
  title: "Bug Bounty Hunting",
  description: "Master the art and science of bug bounty hunting, from reconnaissance to exploitation to building a successful career in ethical vulnerability research.",
  category: "Vulnerability Research",
  difficulty: "Intermediate to Expert",
  estimatedTime: "160+ hours",
  prerequisites: [
    "Solid web application security knowledge",
    "Understanding of common vulnerabilities (OWASP Top 10)",
    "Basic programming and scripting skills",
    "Network security fundamentals",
    "Ethical hacking foundation"
  ],
  outcomes: [
    "Build a successful bug bounty hunting career",
    "Master advanced vulnerability research techniques",
    "Develop efficient reconnaissance and automation workflows",
    "Write professional vulnerability reports and disclosures",
    "Understand legal and ethical aspects of security research",
    "Build reputation and expertise in the security community"
  ],
  modules: [
    bugBountyIntroContent,
    ethicalHackingContent,
    webAppSecurityContent,
    owaspTop10Content,
    networkInfrastructureContent,
    cloudSecurityTestingContent,
    automationScriptingContent,
    mobileSecurityContent,
    businessLogicContent,
    advancedExploitationContent,
    professionalReportWritingContent,
    legalComplianceContent,
    communityNetworkingContent,
    ssrfExploitationContent,
    deserializationContent,
    xxeExploitationContent,
    csrfExploitationContent,
    fileUploadContent,
    authenticationBypassContent,
    idorExploitationContent,
    programSelectionContent,
    reconMethodologyContent,
    webAppTestingContent,
    mobileAppTestingContent,
    apiTestingContent
    // Additional modules will be added as files are created
    // toolsFrameworksContent
  ]
};

export const getAllBugBountyModules = () => {
  return bugBountyLearningPath.modules;
};

export const getBugBountyModuleById = (id) => {
  return bugBountyLearningPath.modules.find(module => module.id === id);
}; 