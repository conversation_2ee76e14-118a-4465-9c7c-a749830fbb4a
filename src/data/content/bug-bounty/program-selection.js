/**
 * Bug Bounty Program Selection Module
 * Guide to selecting and evaluating bug bounty programs
 */

export const programSelectionContent = {
  id: "bb-program-selection",
  title: "Bug Bounty Program Selection",
  description: "Learn how to evaluate and select the right bug bounty programs to maximize your success and earnings.",
  difficulty: "Intermediate",
  estimatedTime: 90,
  objectives: [
    "Understand different types of bug bounty programs",
    "Learn to evaluate program quality and potential",
    "Master program research and reconnaissance",
    "Develop a strategic approach to program selection"
  ],
  sections: [
    {
      title: "Types of Bug Bounty Programs",
      content: `
        <h2>Bug Bounty Program Types</h2>
        <p>Understanding different types of bug bounty programs helps you choose the right targets for your skills and goals.</p>

        <h3>Public Programs</h3>
        <ul>
          <li><strong>Open to all researchers</strong></li>
          <li>Higher competition but more opportunities</li>
          <li>Transparent rules and scope</li>
          <li>Public leaderboards and statistics</li>
        </ul>

        <h3>Private Programs</h3>
        <ul>
          <li><strong>Invitation-only access</strong></li>
          <li>Lower competition, higher payouts</li>
          <li>Requires proven track record</li>
          <li>Often more sensitive targets</li>
        </ul>

        <h3>Continuous vs. Time-Limited</h3>
        <ul>
          <li><strong>Continuous:</strong> Always accepting submissions</li>
          <li><strong>Time-Limited:</strong> Specific duration (contests, events)</li>
          <li><strong>Live Hacking Events:</strong> Real-time collaborative testing</li>
        </ul>

        <h3>Platform-Based vs. Direct</h3>
        <ul>
          <li><strong>Platform-Based:</strong> HackerOne, Bugcrowd, Synack</li>
          <li><strong>Direct Programs:</strong> Company-hosted programs</li>
          <li><strong>Hybrid:</strong> Combination of both approaches</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Program Evaluation Criteria",
      content: `
        <h2>Evaluating Program Quality</h2>
        <p>Not all bug bounty programs are created equal. Use these criteria to assess program potential.</p>

        <h3>Financial Factors</h3>
        <ul>
          <li><strong>Payout Ranges:</strong> Minimum and maximum rewards</li>
          <li><strong>Average Payouts:</strong> Historical payment data</li>
          <li><strong>Payment Speed:</strong> Time from submission to payment</li>
          <li><strong>Bonus Opportunities:</strong> Special rewards and incentives</li>
        </ul>

        <h3>Scope and Assets</h3>
        <ul>
          <li><strong>Asset Diversity:</strong> Web apps, mobile apps, APIs, infrastructure</li>
          <li><strong>Scope Clarity:</strong> Well-defined in-scope and out-of-scope items</li>
          <li><strong>Asset Complexity:</strong> Modern technologies and frameworks</li>
          <li><strong>Update Frequency:</strong> Regular scope additions and updates</li>
        </ul>

        <h3>Program Maturity</h3>
        <ul>
          <li><strong>Response Time:</strong> How quickly team responds to reports</li>
          <li><strong>Triage Quality:</strong> Accuracy of initial assessments</li>
          <li><strong>Communication:</strong> Clear and professional interactions</li>
          <li><strong>Reputation:</strong> Community feedback and ratings</li>
        </ul>

        <h3>Competition Level</h3>
        <ul>
          <li><strong>Researcher Count:</strong> Number of active participants</li>
          <li><strong>Submission Volume:</strong> Reports submitted per month</li>
          <li><strong>Duplicate Rates:</strong> Frequency of duplicate findings</li>
          <li><strong>Skill Level:</strong> Expertise of competing researchers</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Program Research Techniques",
      content: `
        <h2>Researching Target Programs</h2>
        <p>Thorough research before engaging with a program can significantly improve your success rate.</p>

        <h3>Platform Research</h3>
        <ul>
          <li><strong>Program Statistics:</strong> Payout data, response times, ratings</li>
          <li><strong>Leaderboards:</strong> Top researchers and their earnings</li>
          <li><strong>Recent Activity:</strong> New programs and scope changes</li>
          <li><strong>Community Discussions:</strong> Forums and chat channels</li>
        </ul>

        <h3>Technical Reconnaissance</h3>
        <ul>
          <li><strong>Technology Stack:</strong> Frameworks, languages, infrastructure</li>
          <li><strong>Asset Mapping:</strong> Subdomains, endpoints, services</li>
          <li><strong>Previous Vulnerabilities:</strong> Public disclosures and CVEs</li>
          <li><strong>Security Posture:</strong> Existing security measures</li>
        </ul>

        <h3>Historical Analysis</h3>
        <ul>
          <li><strong>Past Reports:</strong> Types of vulnerabilities found</li>
          <li><strong>Payout Trends:</strong> Changes in reward amounts over time</li>
          <li><strong>Researcher Success:</strong> Who finds what types of bugs</li>
          <li><strong>Program Evolution:</strong> How scope and rules have changed</li>
        </ul>

        <h3>Tools for Research</h3>
        <ul>
          <li><strong>Subdomain Enumeration:</strong> Subfinder, Amass, Assetfinder</li>
          <li><strong>Technology Detection:</strong> Wappalyzer, BuiltWith, Shodan</li>
          <li><strong>Historical Data:</strong> Wayback Machine, SecurityTrails</li>
          <li><strong>Social Intelligence:</strong> LinkedIn, GitHub, job postings</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Strategic Program Selection",
      content: `
        <h2>Developing a Selection Strategy</h2>
        <p>Create a systematic approach to choosing programs that align with your skills and goals.</p>

        <h3>Skill-Based Selection</h3>
        <ul>
          <li><strong>Web Application Security:</strong> Focus on web app programs</li>
          <li><strong>Mobile Security:</strong> Target mobile app programs</li>
          <li><strong>Network Security:</strong> Infrastructure-focused programs</li>
          <li><strong>API Security:</strong> Programs with extensive API scope</li>
        </ul>

        <h3>Experience Level Matching</h3>
        <ul>
          <li><strong>Beginner:</strong> New programs with lower competition</li>
          <li><strong>Intermediate:</strong> Established programs with good payouts</li>
          <li><strong>Advanced:</strong> High-value private programs</li>
          <li><strong>Expert:</strong> Complex enterprise applications</li>
        </ul>

        <h3>Portfolio Approach</h3>
        <ul>
          <li><strong>High-Volume Programs:</strong> Consistent smaller payouts</li>
          <li><strong>High-Value Programs:</strong> Potential for large rewards</li>
          <li><strong>Learning Programs:</strong> Skill development opportunities</li>
          <li><strong>Relationship Programs:</strong> Long-term engagement focus</li>
        </ul>

        <h3>Time Management</h3>
        <ul>
          <li><strong>Quick Wins:</strong> Programs with fast response times</li>
          <li><strong>Deep Dives:</strong> Complex applications requiring time investment</li>
          <li><strong>Maintenance:</strong> Regular testing of familiar programs</li>
          <li><strong>Exploration:</strong> New programs and technologies</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Red Flags and Warning Signs",
      content: `
        <h2>Programs to Avoid</h2>
        <p>Recognize warning signs that indicate a program may not be worth your time and effort.</p>

        <h3>Financial Red Flags</h3>
        <ul>
          <li><strong>Extremely Low Payouts:</strong> Below market rates</li>
          <li><strong>Payment Delays:</strong> Consistent late payments</li>
          <li><strong>Unclear Reward Structure:</strong> Vague payout criteria</li>
          <li><strong>Frequent Downgrades:</strong> Reducing severity ratings</li>
        </ul>

        <h3>Communication Issues</h3>
        <ul>
          <li><strong>Poor Response Times:</strong> Weeks without acknowledgment</li>
          <li><strong>Unprofessional Communication:</strong> Rude or dismissive responses</li>
          <li><strong>Inconsistent Decisions:</strong> Contradictory rulings</li>
          <li><strong>Lack of Transparency:</strong> No explanation for decisions</li>
        </ul>

        <h3>Scope Problems</h3>
        <ul>
          <li><strong>Overly Restrictive Scope:</strong> Very limited testing areas</li>
          <li><strong>Unclear Boundaries:</strong> Ambiguous scope definitions</li>
          <li><strong>Frequent Scope Changes:</strong> Constant rule modifications</li>
          <li><strong>Unrealistic Expectations:</strong> Impossible testing requirements</li>
        </ul>

        <h3>Community Feedback</h3>
        <ul>
          <li><strong>Negative Reviews:</strong> Consistent poor ratings</li>
          <li><strong>Researcher Complaints:</strong> Public criticism</li>
          <li><strong>High Dropout Rates:</strong> Researchers leaving program</li>
          <li><strong>Platform Warnings:</strong> Official platform concerns</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is a key advantage of private bug bounty programs?",
            options: [
              "They are open to all researchers",
              "They have lower competition and higher payouts",
              "They require no experience",
              "They have unlimited scope"
            ],
            correctAnswer: 1,
            explanation: "Private bug bounty programs typically have lower competition due to invitation-only access and often offer higher payouts as a result."
          },
          {
            question: "Which factor is most important when evaluating program competition level?",
            options: [
              "Program age",
              "Company size",
              "Number of active researchers",
              "Technology stack"
            ],
            correctAnswer: 2,
            explanation: "The number of active researchers is a key indicator of competition level, as more researchers mean higher competition for finding vulnerabilities."
          }
        ]
      },
      type: "quiz"
    }
  ]
}; 