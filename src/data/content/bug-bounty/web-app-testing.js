/**
 * Web Application Testing Module for Bug Bounty
 */

export const webAppTestingContent = {
  id: "bb-4",
  pathId: "bug-bounty-hunting",
  title: "Web Application Testing",
  description: "Master web application security testing techniques for bug bounty hunting, including OWASP Top 10 vulnerabilities and advanced attack vectors.",
  objectives: [
    "Understand web application architecture and attack surface",
    "Master OWASP Top 10 vulnerability identification and exploitation",
    "Learn advanced web application testing techniques",
    "Develop efficient testing workflows and automation",
    "Understand business logic flaws and application-specific vulnerabilities"
  ],
  difficulty: "Intermediate",
  estimatedTime: 180,
  sections: [
    {
      title: "Web Application Security Fundamentals",
      content: `
        <h2>Web Application Security Fundamentals</h2>
        <p>Understanding web application architecture and common vulnerabilities is essential for effective bug bounty hunting.</p>

        <h3>Web Application Architecture</h3>
        <ul>
          <li><strong>Frontend Components:</strong>
            <ul>
              <li>HTML, CSS, and JavaScript frameworks</li>
              <li>Single Page Applications (SPAs)</li>
              <li>Client-side routing and state management</li>
              <li>Browser security features and limitations</li>
            </ul>
          </li>
          <li><strong>Backend Components:</strong>
            <ul>
              <li>Web servers and application frameworks</li>
              <li>Database systems and data storage</li>
              <li>Authentication and session management</li>
              <li>API endpoints and microservices</li>
            </ul>
          </li>
        </ul>

        <h3>Common Attack Vectors</h3>
        <ul>
          <li>Injection attacks (SQL, NoSQL, Command, LDAP)</li>
          <li>Cross-Site Scripting (XSS) vulnerabilities</li>
          <li>Cross-Site Request Forgery (CSRF) attacks</li>
          <li>Authentication and authorization bypasses</li>
          <li>Business logic flaws and race conditions</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "OWASP Top 10 Vulnerabilities",
      content: `
        <h2>OWASP Top 10 Vulnerabilities</h2>
        <p>The OWASP Top 10 represents the most critical web application security risks.</p>

        <h3>A01: Broken Access Control</h3>
        <ul>
          <li>Vertical privilege escalation</li>
          <li>Horizontal privilege escalation</li>
          <li>Insecure Direct Object References (IDOR)</li>
          <li>Missing function-level access controls</li>
        </ul>

        <h3>A02: Cryptographic Failures</h3>
        <ul>
          <li>Weak encryption algorithms</li>
          <li>Improper key management</li>
          <li>Data transmission without encryption</li>
          <li>Weak random number generation</li>
        </ul>

        <h3>A03: Injection</h3>
        <ul>
          <li>SQL injection vulnerabilities</li>
          <li>NoSQL injection attacks</li>
          <li>Command injection flaws</li>
          <li>LDAP and XML injection</li>
        </ul>
      `,
      type: "text"
    }
  ],
  quiz: {
    questions: [
      {
        question: "What is the most effective way to test for SQL injection vulnerabilities?",
        options: [
          "Manual testing with payloads",
          "Automated scanning tools only",
          "Combination of manual and automated testing",
          "Code review only"
        ],
        correct: 2,
        explanation: "A combination of manual and automated testing provides the most comprehensive coverage for SQL injection detection."
      }
    ]
  },
  practicalExercises: [
    {
      title: "OWASP Top 10 Vulnerability Assessment",
      description: "Identify and exploit OWASP Top 10 vulnerabilities in a test application",
      difficulty: "Intermediate",
      estimatedTime: 120
    }
  ]
};