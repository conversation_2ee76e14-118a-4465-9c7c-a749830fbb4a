/**
 * Cloud Security Testing Module
 */

export const cloudSecurityTestingContent = {
  id: "bb-6",
  pathId: "bug-bounty",
  title: "Cloud Security Testing",
  description: "Master cloud security assessment techniques for AWS, Azure, and GCP environments, including cloud-specific vulnerabilities, misconfigurations, and attack vectors in bug bounty programs.",
  objectives: [
    "Understand cloud security models and shared responsibility",
    "Learn cloud service enumeration and reconnaissance",
    "Master cloud-specific vulnerability assessment techniques",
    "Develop skills in cloud misconfiguration detection",
    "Learn container and serverless security testing",
    "Create comprehensive cloud security assessments"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "Cloud Security Fundamentals",
      content: `
        <h2>Cloud Security Assessment Fundamentals</h2>
        <p>Cloud security testing requires understanding of cloud service models, shared responsibility, and cloud-specific attack vectors that differ from traditional infrastructure.</p>
        
        <h3>Cloud Service Models</h3>
        <ul>
          <li><strong>Infrastructure as a Service (IaaS):</strong>
            <ul>
              <li>Virtual machines and compute instances</li>
              <li>Network and storage services</li>
              <li>Security group and firewall configurations</li>
              <li>Identity and access management (IAM)</li>
            </ul>
          </li>
          <li><strong>Platform as a Service (PaaS):</strong>
            <ul>
              <li>Application deployment platforms</li>
              <li>Database and middleware services</li>
              <li>Development and runtime environments</li>
              <li>API gateways and service mesh</li>
            </ul>
          </li>
          <li><strong>Software as a Service (SaaS):</strong>
            <ul>
              <li>Web-based applications</li>
              <li>Multi-tenant architectures</li>
              <li>Data isolation and privacy</li>
              <li>Integration and API security</li>
            </ul>
          </li>
        </ul>
        
        <h3>Shared Responsibility Model</h3>
        <ul>
          <li><strong>Cloud Provider Responsibilities:</strong>
            <ul>
              <li>Physical infrastructure security</li>
              <li>Hypervisor and host OS security</li>
              <li>Network infrastructure protection</li>
              <li>Service availability and resilience</li>
            </ul>
          </li>
          <li><strong>Customer Responsibilities:</strong>
            <ul>
              <li>Guest OS and application security</li>
              <li>Data encryption and protection</li>
              <li>Identity and access management</li>
              <li>Network traffic protection</li>
            </ul>
          </li>
          <li><strong>Shared Responsibilities:</strong>
            <ul>
              <li>Patch management (varies by service)</li>
              <li>Configuration management</li>
              <li>Network controls and firewalls</li>
              <li>Platform and application-level controls</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Attack Surface</h3>
        <ul>
          <li><strong>Management Interfaces:</strong>
            <ul>
              <li>Cloud console and web interfaces</li>
              <li>Command-line tools and APIs</li>
              <li>Mobile applications</li>
              <li>Third-party management tools</li>
            </ul>
          </li>
          <li><strong>Service APIs:</strong>
            <ul>
              <li>REST and GraphQL APIs</li>
              <li>Authentication and authorization</li>
              <li>Rate limiting and throttling</li>
              <li>API versioning and deprecation</li>
            </ul>
          </li>
          <li><strong>Data Storage:</strong>
            <ul>
              <li>Object storage (S3, Blob, Cloud Storage)</li>
              <li>Database services (RDS, CosmosDB, Cloud SQL)</li>
              <li>File systems and network storage</li>
              <li>Backup and archival services</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Cloud Reconnaissance and Enumeration",
      content: `
        <h2>Cloud Reconnaissance and Service Enumeration</h2>
        <p>Cloud reconnaissance involves discovering cloud assets, services, and configurations using both passive and active techniques specific to cloud environments.</p>
        
        <h3>Cloud Asset Discovery</h3>
        <ul>
          <li><strong>DNS and Subdomain Enumeration:</strong>
            <ul>
              <li>Cloud service subdomain patterns</li>
              <li>Certificate transparency logs</li>
              <li>DNS brute forcing for cloud services</li>
              <li>Reverse DNS lookups</li>
            </ul>
          </li>
          <li><strong>Cloud Storage Enumeration:</strong>
            <ul>
              <li>S3 bucket discovery and enumeration</li>
              <li>Azure Blob storage containers</li>
              <li>Google Cloud Storage buckets</li>
              <li>Public storage misconfiguration detection</li>
            </ul>
          </li>
          <li><strong>Cloud Service Fingerprinting:</strong>
            <ul>
              <li>HTTP header analysis</li>
              <li>Error message patterns</li>
              <li>Service-specific indicators</li>
              <li>Cloud provider identification</li>
            </ul>
          </li>
        </ul>
        
        <h3>AWS Security Assessment</h3>
        <ul>
          <li><strong>AWS Service Enumeration:</strong>
            <ul>
              <li>EC2 instance metadata service</li>
              <li>S3 bucket permissions and policies</li>
              <li>Lambda function discovery</li>
              <li>API Gateway endpoint enumeration</li>
            </ul>
          </li>
          <li><strong>IAM Security Testing:</strong>
            <ul>
              <li>Role and policy enumeration</li>
              <li>Cross-account access testing</li>
              <li>Privilege escalation paths</li>
              <li>Temporary credential abuse</li>
            </ul>
          </li>
          <li><strong>AWS-Specific Vulnerabilities:</strong>
            <ul>
              <li>SSRF via metadata service</li>
              <li>S3 bucket takeover</li>
              <li>Lambda function injection</li>
              <li>CloudFormation template injection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Azure and GCP Assessment</h3>
        <ul>
          <li><strong>Azure Security Testing:</strong>
            <ul>
              <li>Azure AD enumeration</li>
              <li>Storage account assessment</li>
              <li>Function app security testing</li>
              <li>Key Vault access testing</li>
            </ul>
          </li>
          <li><strong>Google Cloud Platform (GCP):</strong>
            <ul>
              <li>GCP project enumeration</li>
              <li>Cloud Storage bucket testing</li>
              <li>Cloud Function assessment</li>
              <li>IAM and service account testing</li>
            </ul>
          </li>
          <li><strong>Multi-Cloud Considerations:</strong>
            <ul>
              <li>Cross-cloud service integration</li>
              <li>Hybrid cloud architectures</li>
              <li>Cloud-to-cloud data transfer</li>
              <li>Federated identity management</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Container and Serverless Security",
      content: `
        <h2>Container and Serverless Security Testing</h2>
        <p>Modern cloud applications increasingly use containers and serverless architectures, introducing new attack vectors and security considerations.</p>
        
        <h3>Container Security Assessment</h3>
        <ul>
          <li><strong>Docker Security Testing:</strong>
            <ul>
              <li>Container image vulnerability scanning</li>
              <li>Docker daemon security assessment</li>
              <li>Container escape techniques</li>
              <li>Registry security evaluation</li>
            </ul>
          </li>
          <li><strong>Kubernetes Security:</strong>
            <ul>
              <li>Cluster configuration assessment</li>
              <li>Pod security policy evaluation</li>
              <li>RBAC (Role-Based Access Control) testing</li>
              <li>Network policy assessment</li>
            </ul>
          </li>
          <li><strong>Container Runtime Security:</strong>
            <ul>
              <li>Privilege escalation in containers</li>
              <li>Host filesystem access</li>
              <li>Inter-container communication</li>
              <li>Resource exhaustion attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>Serverless Security Testing</h3>
        <ul>
          <li><strong>Function-as-a-Service (FaaS) Security:</strong>
            <ul>
              <li>AWS Lambda security assessment</li>
              <li>Azure Functions testing</li>
              <li>Google Cloud Functions evaluation</li>
              <li>Function permission analysis</li>
            </ul>
          </li>
          <li><strong>Serverless Attack Vectors:</strong>
            <ul>
              <li>Event injection attacks</li>
              <li>Function timeout and resource abuse</li>
              <li>Cold start vulnerabilities</li>
              <li>Dependency confusion attacks</li>
            </ul>
          </li>
          <li><strong>API Gateway Security:</strong>
            <ul>
              <li>Authentication and authorization bypass</li>
              <li>Rate limiting and throttling</li>
              <li>Input validation and injection</li>
              <li>CORS misconfiguration</li>
            </ul>
          </li>
        </ul>
        
        <h3>Cloud Security Tools and Techniques</h3>
        <ul>
          <li><strong>Cloud Security Scanners:</strong>
            <ul>
              <li>ScoutSuite for multi-cloud assessment</li>
              <li>Prowler for AWS security auditing</li>
              <li>CloudSploit for configuration assessment</li>
              <li>Pacu for AWS exploitation</li>
            </ul>
          </li>
          <li><strong>Container Security Tools:</strong>
            <ul>
              <li>Trivy for vulnerability scanning</li>
              <li>Clair for static analysis</li>
              <li>Falco for runtime security</li>
              <li>kube-hunter for Kubernetes testing</li>
            </ul>
          </li>
          <li><strong>Custom Tool Development:</strong>
            <ul>
              <li>Cloud API automation scripts</li>
              <li>Custom enumeration tools</li>
              <li>Serverless function testing</li>
              <li>Container escape proof-of-concepts</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "In the cloud shared responsibility model, who is responsible for guest OS security?",
            options: [
              "Cloud provider only",
              "Customer only",
              "Both cloud provider and customer",
              "Third-party security vendors"
            ],
            correctAnswer: 1,
            explanation: "In the shared responsibility model, the customer is responsible for guest OS security, including patching, configuration, and security controls within their virtual machines."
          },
          {
            question: "Which AWS service is commonly exploited through SSRF attacks?",
            options: [
              "S3 buckets",
              "EC2 metadata service",
              "Lambda functions",
              "RDS databases"
            ],
            correctAnswer: 1,
            explanation: "The EC2 metadata service (169.254.169.254) is commonly exploited through SSRF attacks to retrieve instance metadata, IAM credentials, and other sensitive information."
          },
          {
            question: "What is a primary security concern with serverless functions?",
            options: [
              "Physical server access",
              "Event injection and function timeout abuse",
              "Network firewall configuration",
              "Hardware vulnerability patching"
            ],
            correctAnswer: 1,
            explanation: "Event injection and function timeout abuse are primary security concerns with serverless functions, as attackers can manipulate input events and exploit resource limitations to cause denial of service or unauthorized access."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
