/**
 * File Upload Vulnerabilities Module
 */

export const fileUploadContent = {
  id: "bb-18",
  pathId: "bug-bounty",
  title: "File Upload Vulnerabilities",
  description: "Master file upload vulnerability exploitation, including bypass techniques, malicious file creation, and achieving remote code execution through file upload mechanisms.",
  objectives: [
    "Understand file upload vulnerability fundamentals",
    "Learn file upload restriction bypass techniques",
    "Master malicious file creation and payload development",
    "Develop skills in achieving RCE through file uploads",
    "Learn file upload security testing methodologies",
    "Create comprehensive file upload attack strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 125,
  sections: [
    {
      title: "File Upload Security Fundamentals",
      content: `
        <h2>File Upload Vulnerability Fundamentals</h2>
        <p>File upload vulnerabilities occur when applications fail to properly validate, sanitize, or restrict uploaded files, potentially leading to remote code execution and system compromise.</p>
        
        <h3>File Upload Attack Vectors</h3>
        <ul>
          <li><strong>Remote Code Execution:</strong>
            <ul>
              <li>Web shell upload and execution</li>
              <li>Server-side script execution</li>
              <li>Binary executable upload</li>
              <li>Library and module injection</li>
            </ul>
          </li>
          <li><strong>Client-Side Attacks:</strong>
            <ul>
              <li>Malicious JavaScript execution</li>
              <li>HTML injection and XSS</li>
              <li>SVG-based attacks</li>
              <li>PDF and document exploits</li>
            </ul>
          </li>
          <li><strong>Information Disclosure:</strong>
            <ul>
              <li>Path traversal via filenames</li>
              <li>Configuration file overwrite</li>
              <li>Source code disclosure</li>
              <li>Database file replacement</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common File Upload Restrictions</h3>
        <ul>
          <li><strong>File Type Restrictions:</strong>
            <ul>
              <li>File extension blacklisting</li>
              <li>MIME type validation</li>
              <li>Magic number checking</li>
              <li>Content-based validation</li>
            </ul>
          </li>
          <li><strong>File Size and Content Limits:</strong>
            <ul>
              <li>Maximum file size restrictions</li>
              <li>Content length validation</li>
              <li>Upload rate limiting</li>
              <li>Storage quota enforcement</li>
            </ul>
          </li>
          <li><strong>Filename and Path Restrictions:</strong>
            <ul>
              <li>Filename character filtering</li>
              <li>Path traversal prevention</li>
              <li>Reserved name blocking</li>
              <li>Length limitations</li>
            </ul>
          </li>
        </ul>
        
        <h3>File Upload Processing Flow</h3>
        <ul>
          <li><strong>Upload Reception:</strong>
            <ul>
              <li>HTTP multipart parsing</li>
              <li>Temporary file creation</li>
              <li>Memory vs. disk storage</li>
              <li>Upload progress tracking</li>
            </ul>
          </li>
          <li><strong>Validation and Processing:</strong>
            <ul>
              <li>File type detection</li>
              <li>Content scanning and analysis</li>
              <li>Virus and malware scanning</li>
              <li>Metadata extraction</li>
            </ul>
          </li>
          <li><strong>Storage and Access:</strong>
            <ul>
              <li>File system storage</li>
              <li>Database blob storage</li>
              <li>Cloud storage integration</li>
              <li>Access control implementation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Bypass Techniques and Evasion Methods",
      content: `
        <h2>File Upload Restriction Bypass Techniques</h2>
        <p>Bypassing file upload restrictions requires understanding validation mechanisms and developing creative techniques to circumvent security controls.</p>
        
        <h3>File Extension Bypass</h3>
        <ul>
          <li><strong>Extension Manipulation:</strong>
            <ul>
              <li>Double extension attacks (.php.jpg)</li>
              <li>Null byte injection (.php%00.jpg)</li>
              <li>Case variation bypasses (.PHP, .Php)</li>
              <li>Alternative extensions (.phtml, .php5)</li>
            </ul>
          </li>
          <li><strong>Blacklist Evasion:</strong>
            <ul>
              <li>Uncommon executable extensions</li>
              <li>Server-specific extensions</li>
              <li>Configuration-dependent execution</li>
              <li>Polyglot file creation</li>
            </ul>
          </li>
          <li><strong>Whitelist Bypass:</strong>
            <ul>
              <li>Content-Type spoofing</li>
              <li>File signature manipulation</li>
              <li>Embedded payload techniques</li>
              <li>Archive file exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>MIME Type and Content Bypass</h3>
        <ul>
          <li><strong>MIME Type Manipulation:</strong>
            <ul>
              <li>Content-Type header spoofing</li>
              <li>Multipart boundary manipulation</li>
              <li>MIME type confusion</li>
              <li>Browser MIME sniffing abuse</li>
            </ul>
          </li>
          <li><strong>File Signature Bypass:</strong>
            <ul>
              <li>Magic number spoofing</li>
              <li>File header manipulation</li>
              <li>Polyglot file construction</li>
              <li>Steganographic techniques</li>
            </ul>
          </li>
          <li><strong>Content Validation Evasion:</strong>
            <ul>
              <li>Image-based payload embedding</li>
              <li>Document macro exploitation</li>
              <li>Archive file abuse</li>
              <li>Metadata injection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Path and Filename Manipulation</h3>
        <ul>
          <li><strong>Path Traversal Attacks:</strong>
            <ul>
              <li>Directory traversal sequences (../)</li>
              <li>Absolute path specification</li>
              <li>URL encoding bypass</li>
              <li>Unicode normalization abuse</li>
            </ul>
          </li>
          <li><strong>Filename Injection:</strong>
            <ul>
              <li>Special character injection</li>
              <li>Command injection via filenames</li>
              <li>SQL injection in filename processing</li>
              <li>XSS via filename display</li>
            </ul>
          </li>
          <li><strong>Overwrite Attacks:</strong>
            <ul>
              <li>Configuration file replacement</li>
              <li>Application file overwrite</li>
              <li>System file modification</li>
              <li>Database file corruption</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Malicious File Creation and RCE Achievement",
      content: `
        <h2>Malicious File Creation and Remote Code Execution</h2>
        <p>Achieving remote code execution through file uploads requires crafting effective payloads and understanding server-side execution contexts.</p>
        
        <h3>Web Shell Development</h3>
        <ul>
          <li><strong>Basic Web Shells:</strong>
            <ul>
              <li>PHP command execution shells</li>
              <li>ASP.NET web shell creation</li>
              <li>JSP-based command execution</li>
              <li>Python and Ruby web shells</li>
            </ul>
          </li>
          <li><strong>Advanced Web Shell Features:</strong>
            <ul>
              <li>File management capabilities</li>
              <li>Database interaction</li>
              <li>Network connectivity testing</li>
              <li>Privilege escalation helpers</li>
            </ul>
          </li>
          <li><strong>Stealth and Evasion:</strong>
            <ul>
              <li>Obfuscated code techniques</li>
              <li>Encrypted communication</li>
              <li>Log evasion methods</li>
              <li>Anti-forensics features</li>
            </ul>
          </li>
        </ul>
        
        <h3>Polyglot File Techniques</h3>
        <ul>
          <li><strong>Image-Script Polyglots:</strong>
            <ul>
              <li>JPEG with embedded PHP</li>
              <li>PNG with script payload</li>
              <li>GIF with executable code</li>
              <li>SVG with JavaScript/PHP</li>
            </ul>
          </li>
          <li><strong>Document-Based Payloads:</strong>
            <ul>
              <li>PDF with embedded scripts</li>
              <li>Office document macros</li>
              <li>ZIP archive exploitation</li>
              <li>XML-based payload delivery</li>
            </ul>
          </li>
          <li><strong>Multi-Format Exploitation:</strong>
            <ul>
              <li>Cross-format compatibility</li>
              <li>Parser confusion attacks</li>
              <li>Format-specific triggers</li>
              <li>Conditional payload execution</li>
            </ul>
          </li>
        </ul>
        
        <h3>Post-Upload Exploitation</h3>
        <ul>
          <li><strong>File Access and Execution:</strong>
            <ul>
              <li>Direct URL access attempts</li>
              <li>Include/require exploitation</li>
              <li>Server-side processing triggers</li>
              <li>Scheduled task exploitation</li>
            </ul>
          </li>
          <li><strong>Privilege Escalation:</strong>
            <ul>
              <li>Local file inclusion chaining</li>
              <li>Configuration file modification</li>
              <li>Service account abuse</li>
              <li>System command execution</li>
            </ul>
          </li>
          <li><strong>Persistence and Lateral Movement:</strong>
            <ul>
              <li>Backdoor installation</li>
              <li>User account creation</li>
              <li>Network reconnaissance</li>
              <li>Additional system compromise</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which technique is most effective for bypassing file extension blacklists?",
            options: [
              "Using only uppercase extensions",
              "Double extension attacks (.php.jpg)",
              "Compressing the file",
              "Changing the file size"
            ],
            correctAnswer: 1,
            explanation: "Double extension attacks (.php.jpg) are most effective because they can trick validation that only checks the final extension while still allowing server execution based on the first extension, depending on server configuration."
          },
          {
            question: "What is a polyglot file in the context of file upload attacks?",
            options: [
              "A file written in multiple programming languages",
              "A file that is valid in multiple formats simultaneously",
              "A compressed file containing multiple files",
              "A file with multiple extensions"
            ],
            correctAnswer: 1,
            explanation: "A polyglot file is valid in multiple formats simultaneously, such as a file that appears as a valid image to validation checks but also contains executable code that can be processed by the server."
          },
          {
            question: "Which file upload restriction is most difficult to bypass?",
            options: [
              "File extension blacklisting",
              "MIME type validation",
              "Content-based validation with sandboxed execution",
              "File size limitations"
            ],
            correctAnswer: 2,
            explanation: "Content-based validation with sandboxed execution is most difficult to bypass because it analyzes the actual file content and executes it in a controlled environment to detect malicious behavior, rather than relying on easily spoofed metadata."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
