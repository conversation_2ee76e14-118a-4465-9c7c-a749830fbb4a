export const apiTestingContent = {
  title: 'API Security Testing',
  description: 'Comprehensive guide to testing and securing REST and GraphQL APIs.',
  
  // Core concepts and learning objectives
  concepts: [
    'API architecture and design',
    'Authentication and authorization',
    'Input validation and sanitization',
    'Rate limiting and DoS protection',
    'API security best practices'
  ],

  // Practical labs with clear objectives
  labs: [
    {
      title: 'REST API Security Testing',
      description: 'Learn to test and secure REST APIs',
      difficulty: 'Intermediate',
      duration: '2 hours',
      objectives: [
        'Identify API endpoints',
        'Test authentication mechanisms',
        'Analyze request/response patterns',
        'Implement security controls'
      ],
      tools: ['Postman', 'Burp Suite', 'OWASP ZAP'],
      prerequisites: ['Basic API knowledge', 'Understanding of HTTP protocols']
    },
    {
      title: 'GraphQL Security Testing',
      description: 'Advanced testing of GraphQL APIs',
      difficulty: 'Advanced',
      duration: '2.5 hours',
      objectives: [
        'Analyze GraphQL schema',
        'Test query complexity',
        'Identify introspection vulnerabilities',
        'Implement rate limiting'
      ],
      tools: ['GraphQL Playground', 'Insomnia', 'Burp Suite'],
      prerequisites: ['GraphQL basics', 'API security concepts']
    }
  ],

  // Real-world use cases with detailed scenarios
  useCases: [
    {
      title: 'Authentication Bypass',
      description: 'Test and identify authentication vulnerabilities',
      scenario: 'Analyze and bypass API authentication mechanisms',
      mitreTactics: ['Initial Access', 'Persistence'],
      tools: ['Burp Suite', 'Postman', 'Custom Scripts'],
      steps: [
        'Analyze authentication flow',
        'Test token handling',
        'Identify weak implementations',
        'Document vulnerabilities'
      ]
    },
    {
      title: 'Data Exposure',
      description: 'Identify and prevent data leakage',
      scenario: 'Test API endpoints for sensitive data exposure',
      mitreTactics: ['Collection', 'Exfiltration'],
      tools: ['OWASP ZAP', 'Burp Suite', 'Custom Proxies'],
      steps: [
        'Map API endpoints',
        'Analyze response data',
        'Test access controls',
        'Implement data protection'
      ]
    }
  ],

  // MITRE ATT&CK mapping
  mitreMapping: [
    {
      tactic: 'Initial Access',
      techniques: [
        {
          name: 'Valid Accounts',
          description: 'Test authentication mechanisms',
          detection: 'Monitor authentication attempts and failures'
        },
        {
          name: 'Exploit Public-Facing Application',
          description: 'Test API vulnerabilities',
          detection: 'Monitor API access patterns and errors'
        }
      ]
    },
    {
      tactic: 'Persistence',
      techniques: [
        {
          name: 'Access Token Manipulation',
          description: 'Test token handling',
          detection: 'Monitor token usage and validation'
        },
        {
          name: 'Account Manipulation',
          description: 'Test account management APIs',
          detection: 'Monitor account modification requests'
        }
      ]
    }
  ],

  // Required tools and technologies
  tools: [
    {
      name: 'API Testing Tools',
      description: 'Tools for testing API security',
      useCases: ['Request manipulation', 'Response analysis', 'Vulnerability scanning'],
      examples: ['Postman', 'Burp Suite', 'OWASP ZAP']
    },
    {
      name: 'API Development Tools',
      description: 'Tools for API development and testing',
      useCases: ['API design', 'Testing', 'Documentation'],
      examples: ['Swagger', 'Insomnia', 'GraphQL Playground']
    }
  ],

  // Prerequisites and dependencies
  prerequisites: [
    'Understanding of API concepts',
    'Knowledge of HTTP protocols',
    'Familiarity with security testing',
    'Basic programming skills'
  ],

  // Additional resources
  resources: [
    {
      type: 'Guide',
      title: 'API Security Testing Guide',
      url: 'https://example.com/api-security-guide'
    },
    {
      type: 'Cheat Sheet',
      title: 'API Testing Commands',
      url: 'https://example.com/api-testing-cheatsheet'
    }
  ]
}; 