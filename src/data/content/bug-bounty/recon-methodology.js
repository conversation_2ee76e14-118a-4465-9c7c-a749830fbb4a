/**
 * Bug Bounty Reconnaissance Methodology Module
 */

export const reconMethodologyContent = {
  id: "bb-recon-methodology",
  title: "Reconnaissance Methodology",
  description: "Master systematic reconnaissance techniques for effective bug bounty hunting.",
  difficulty: "Intermediate",
  estimatedTime: 120,
  objectives: [
    "Understand reconnaissance fundamentals",
    "Learn passive and active recon techniques",
    "Master subdomain enumeration",
    "Develop systematic recon workflows"
  ],
  sections: [
    {
      title: "Reconnaissance Fundamentals",
      content: `
        <h2>Bug Bounty Reconnaissance</h2>
        <p>Learn systematic approaches to reconnaissance for bug bounty hunting success.</p>
        <h3>Reconnaissance Types</h3>
        <ul>
          <li>Passive reconnaissance (OSINT)</li>
          <li>Active reconnaissance</li>
          <li>Subdomain enumeration</li>
          <li>Technology stack identification</li>
        </ul>
      `,
      type: "text"
    }
  ]
}; 