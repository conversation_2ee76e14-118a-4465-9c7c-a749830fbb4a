/**
 * IDOR (Insecure Direct Object Reference) Exploitation Module
 */

export const idorExploitationContent = {
  id: "bb-20",
  pathId: "bug-bounty",
  title: "IDOR (Insecure Direct Object Reference) Exploitation",
  description: "Master Insecure Direct Object Reference (IDOR) vulnerabilities, including detection techniques, exploitation methods, and advanced enumeration strategies for unauthorized data access.",
  objectives: [
    "Understand IDOR vulnerability fundamentals and access control flaws",
    "Learn IDOR detection and identification techniques",
    "Master basic and advanced IDOR exploitation methods",
    "Develop skills in automated IDOR enumeration",
    "Learn blind IDOR and indirect reference attacks",
    "Create comprehensive IDOR testing methodologies"
  ],
  difficulty: "Intermediate",
  estimatedTime: 115,
  sections: [
    {
      title: "IDOR Fundamentals and Access Control",
      content: `
        <h2>Insecure Direct Object Reference (IDOR) Fundamentals</h2>
        <p>IDOR vulnerabilities occur when applications expose direct references to internal objects without proper access control, allowing attackers to access unauthorized data or functionality.</p>
        
        <h3>IDOR Vulnerability Concepts</h3>
        <ul>
          <li><strong>Direct Object References:</strong>
            <ul>
              <li>Database record identifiers</li>
              <li>File system path references</li>
              <li>URL parameter values</li>
              <li>API endpoint identifiers</li>
            </ul>
          </li>
          <li><strong>Access Control Failures:</strong>
            <ul>
              <li>Missing authorization checks</li>
              <li>Insufficient permission validation</li>
              <li>Horizontal privilege escalation</li>
              <li>Vertical privilege escalation</li>
            </ul>
          </li>
          <li><strong>Common IDOR Scenarios:</strong>
            <ul>
              <li>User profile access</li>
              <li>Document and file retrieval</li>
              <li>Financial transaction data</li>
              <li>Administrative functionality</li>
            </ul>
          </li>
        </ul>
        
        <h3>IDOR Types and Classifications</h3>
        <ul>
          <li><strong>Horizontal IDOR:</strong>
            <ul>
              <li>Same privilege level access</li>
              <li>User-to-user data access</li>
              <li>Peer resource enumeration</li>
              <li>Cross-account information disclosure</li>
            </ul>
          </li>
          <li><strong>Vertical IDOR:</strong>
            <ul>
              <li>Privilege escalation attacks</li>
              <li>Administrative function access</li>
              <li>Higher privilege data access</li>
              <li>System configuration exposure</li>
            </ul>
          </li>
          <li><strong>Blind IDOR:</strong>
            <ul>
              <li>No direct response visibility</li>
              <li>Side-channel confirmation</li>
              <li>Time-based detection</li>
              <li>Error message analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common IDOR Locations</h3>
        <ul>
          <li><strong>URL Parameters:</strong>
            <ul>
              <li>GET request parameters</li>
              <li>RESTful API endpoints</li>
              <li>Query string values</li>
              <li>Path parameter references</li>
            </ul>
          </li>
          <li><strong>Form Data and POST Bodies:</strong>
            <ul>
              <li>Hidden form fields</li>
              <li>JSON payload values</li>
              <li>XML data references</li>
              <li>Multipart form data</li>
            </ul>
          </li>
          <li><strong>HTTP Headers:</strong>
            <ul>
              <li>Custom header values</li>
              <li>Authorization header data</li>
              <li>Cookie parameter values</li>
              <li>Referer header references</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "IDOR Detection and Enumeration",
      content: `
        <h2>IDOR Detection and Systematic Enumeration</h2>
        <p>Effective IDOR detection requires systematic analysis of application functionality and automated enumeration of object references to identify access control gaps.</p>
        
        <h3>Detection Methodologies</h3>
        <ul>
          <li><strong>Functionality Mapping:</strong>
            <ul>
              <li>User account feature analysis</li>
              <li>Data access point identification</li>
              <li>Administrative function discovery</li>
              <li>API endpoint enumeration</li>
            </ul>
          </li>
          <li><strong>Parameter Analysis:</strong>
            <ul>
              <li>Numeric identifier patterns</li>
              <li>UUID and GUID references</li>
              <li>Encoded parameter values</li>
              <li>Hash-based identifiers</li>
            </ul>
          </li>
          <li><strong>Access Control Testing:</strong>
            <ul>
              <li>Multi-user account testing</li>
              <li>Privilege level comparison</li>
              <li>Session context switching</li>
              <li>Authorization bypass attempts</li>
            </ul>
          </li>
        </ul>
        
        <h3>Manual IDOR Testing</h3>
        <ul>
          <li><strong>Basic Enumeration:</strong>
            <ul>
              <li>Sequential ID manipulation</li>
              <li>Random ID generation</li>
              <li>Pattern-based guessing</li>
              <li>Boundary value testing</li>
            </ul>
          </li>
          <li><strong>Multi-User Testing:</strong>
            <ul>
              <li>Cross-account access attempts</li>
              <li>Role-based testing scenarios</li>
              <li>Privilege escalation testing</li>
              <li>Session sharing analysis</li>
            </ul>
          </li>
          <li><strong>Response Analysis:</strong>
            <ul>
              <li>Data disclosure examination</li>
              <li>Error message analysis</li>
              <li>Response time variations</li>
              <li>Content length differences</li>
            </ul>
          </li>
        </ul>
        
        <h3>Automated IDOR Discovery</h3>
        <ul>
          <li><strong>Burp Suite Extensions:</strong>
            <ul>
              <li>Autorize extension usage</li>
              <li>AuthMatrix for role testing</li>
              <li>Custom Intruder payloads</li>
              <li>Session handling rules</li>
            </ul>
          </li>
          <li><strong>Custom Automation Scripts:</strong>
            <ul>
              <li>Python-based enumeration</li>
              <li>Multi-threaded testing</li>
              <li>Response comparison logic</li>
              <li>Pattern recognition algorithms</li>
            </ul>
          </li>
          <li><strong>API Testing Tools:</strong>
            <ul>
              <li>Postman collection testing</li>
              <li>REST API enumeration</li>
              <li>GraphQL introspection</li>
              <li>OpenAPI specification abuse</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced IDOR Exploitation and Bypass Techniques",
      content: `
        <h2>Advanced IDOR Exploitation and Protection Bypass</h2>
        <p>Advanced IDOR exploitation involves bypassing protection mechanisms, exploiting complex reference systems, and chaining IDOR with other vulnerabilities.</p>
        
        <h3>Protection Bypass Techniques</h3>
        <ul>
          <li><strong>Encoding and Obfuscation:</strong>
            <ul>
              <li>Base64 encoded identifiers</li>
              <li>URL encoding manipulation</li>
              <li>Hash collision attacks</li>
              <li>Custom encoding schemes</li>
            </ul>
          </li>
          <li><strong>Parameter Manipulation:</strong>
            <ul>
              <li>HTTP method override</li>
              <li>Content-Type manipulation</li>
              <li>Parameter pollution attacks</li>
              <li>Case sensitivity bypass</li>
            </ul>
          </li>
          <li><strong>Indirect Reference Attacks:</strong>
            <ul>
              <li>Reference chain exploitation</li>
              <li>Nested object access</li>
              <li>Relationship-based enumeration</li>
              <li>Foreign key exploitation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Complex IDOR Scenarios</h3>
        <ul>
          <li><strong>Multi-Step IDOR:</strong>
            <ul>
              <li>Chained reference exploitation</li>
              <li>Progressive privilege escalation</li>
              <li>State-dependent access</li>
              <li>Workflow-based enumeration</li>
            </ul>
          </li>
          <li><strong>Time-Based IDOR:</strong>
            <ul>
              <li>Temporal access control bypass</li>
              <li>Race condition exploitation</li>
              <li>Session timing attacks</li>
              <li>Cache-based enumeration</li>
            </ul>
          </li>
          <li><strong>Blind IDOR Exploitation:</strong>
            <ul>
              <li>Boolean-based enumeration</li>
              <li>Error message analysis</li>
              <li>Side-channel information</li>
              <li>Timing-based confirmation</li>
            </ul>
          </li>
        </ul>
        
        <h3>IDOR Impact Maximization</h3>
        <ul>
          <li><strong>Data Exfiltration:</strong>
            <ul>
              <li>Bulk data extraction</li>
              <li>Sensitive information harvesting</li>
              <li>Personal data enumeration</li>
              <li>Financial record access</li>
            </ul>
          </li>
          <li><strong>Privilege Escalation:</strong>
            <ul>
              <li>Administrative function access</li>
              <li>System configuration exposure</li>
              <li>User management capabilities</li>
              <li>Security control bypass</li>
            </ul>
          </li>
          <li><strong>Chaining with Other Vulnerabilities:</strong>
            <ul>
              <li>IDOR + XSS combinations</li>
              <li>IDOR + CSRF attacks</li>
              <li>IDOR + SQL injection</li>
              <li>IDOR + file upload abuse</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary difference between horizontal and vertical IDOR?",
            options: [
              "Horizontal affects more users",
              "Vertical IDOR involves privilege escalation while horizontal involves same-level access",
              "Horizontal is more dangerous",
              "Vertical only affects administrators"
            ],
            correctAnswer: 1,
            explanation: "Vertical IDOR involves privilege escalation (accessing higher privilege data/functions), while horizontal IDOR involves accessing data at the same privilege level (like accessing another user's profile)."
          },
          {
            question: "Which technique is most effective for detecting blind IDOR vulnerabilities?",
            options: [
              "Direct response analysis",
              "Error message examination and timing analysis",
              "Source code review",
              "Network traffic analysis"
            ],
            correctAnswer: 1,
            explanation: "Error message examination and timing analysis are most effective for blind IDOR because there's no direct response showing the data, requiring indirect methods to confirm unauthorized access."
          },
          {
            question: "What is the most common location for IDOR vulnerabilities in modern web applications?",
            options: [
              "HTTP headers only",
              "Cookie values",
              "URL parameters and API endpoints",
              "Form hidden fields only"
            ],
            correctAnswer: 2,
            explanation: "URL parameters and API endpoints are the most common locations for IDOR vulnerabilities because they frequently contain direct object references like user IDs, document IDs, and resource identifiers."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
