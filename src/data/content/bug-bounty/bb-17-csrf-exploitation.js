/**
 * CSRF (Cross-Site Request Forgery) Exploitation Module
 */

export const csrfExploitationContent = {
  id: "bb-17",
  pathId: "bug-bounty",
  title: "CSRF (Cross-Site Request Forgery) Exploitation",
  description: "Master Cross-Site Request Forgery (CSRF) vulnerabilities, including detection techniques, exploitation methods, and modern bypass strategies for CSRF protection mechanisms.",
  objectives: [
    "Understand CSRF vulnerability fundamentals and attack mechanics",
    "Learn CSRF detection and identification techniques",
    "Master basic and advanced CSRF exploitation methods",
    "Develop skills in CSRF protection bypass techniques",
    "Learn modern CSRF attack vectors and SameSite bypass",
    "Create comprehensive CSRF testing methodologies"
  ],
  difficulty: "Intermediate",
  estimatedTime: 110,
  sections: [
    {
      title: "CSRF Fundamentals and Attack Mechanics",
      content: `
        <h2>Cross-Site Request Forgery (CSRF) Fundamentals</h2>
        <p>CSRF vulnerabilities allow attackers to perform unauthorized actions on behalf of authenticated users by exploiting the trust that web applications have in user browsers.</p>
        
        <h3>CSRF Attack Mechanics</h3>
        <ul>
          <li><strong>Attack Prerequisites:</strong>
            <ul>
              <li>User authentication to target application</li>
              <li>Predictable request structure</li>
              <li>Lack of proper CSRF protection</li>
              <li>Social engineering or malicious website</li>
            </ul>
          </li>
          <li><strong>Attack Flow:</strong>
            <ul>
              <li>User authenticates to vulnerable application</li>
              <li>Attacker crafts malicious request</li>
              <li>User visits attacker-controlled page</li>
              <li>Browser automatically sends authenticated request</li>
            </ul>
          </li>
          <li><strong>Browser Behavior:</strong>
            <ul>
              <li>Automatic cookie transmission</li>
              <li>Same-origin policy limitations</li>
              <li>Cross-origin request handling</li>
              <li>Preflight request requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>CSRF Vulnerability Types</h3>
        <ul>
          <li><strong>GET-based CSRF:</strong>
            <ul>
              <li>Simple URL-based attacks</li>
              <li>Image tag exploitation</li>
              <li>Link-based social engineering</li>
              <li>Iframe and embed attacks</li>
            </ul>
          </li>
          <li><strong>POST-based CSRF:</strong>
            <ul>
              <li>Form-based attacks</li>
              <li>JavaScript-driven requests</li>
              <li>XMLHttpRequest exploitation</li>
              <li>Fetch API abuse</li>
            </ul>
          </li>
          <li><strong>JSON and API CSRF:</strong>
            <ul>
              <li>Content-Type manipulation</li>
              <li>Simple request exploitation</li>
              <li>CORS misconfiguration abuse</li>
              <li>API endpoint targeting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common CSRF Targets</h3>
        <ul>
          <li><strong>Account Management:</strong>
            <ul>
              <li>Password change functionality</li>
              <li>Email address modification</li>
              <li>Profile information updates</li>
              <li>Security settings changes</li>
            </ul>
          </li>
          <li><strong>Financial Operations:</strong>
            <ul>
              <li>Money transfers and payments</li>
              <li>Account balance modifications</li>
              <li>Investment transactions</li>
              <li>Billing and subscription changes</li>
            </ul>
          </li>
          <li><strong>Administrative Functions:</strong>
            <ul>
              <li>User privilege modifications</li>
              <li>System configuration changes</li>
              <li>Content management operations</li>
              <li>Access control modifications</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "CSRF Detection and Basic Exploitation",
      content: `
        <h2>CSRF Detection and Basic Exploitation Techniques</h2>
        <p>Effective CSRF detection requires systematic analysis of application functionality and testing for proper cross-site request protection mechanisms.</p>
        
        <h3>Detection Methodologies</h3>
        <ul>
          <li><strong>Functionality Analysis:</strong>
            <ul>
              <li>State-changing operation identification</li>
              <li>Authentication requirement mapping</li>
              <li>Request structure analysis</li>
              <li>Protection mechanism evaluation</li>
            </ul>
          </li>
          <li><strong>Request Analysis:</strong>
            <ul>
              <li>HTTP method examination</li>
              <li>Parameter structure review</li>
              <li>Header requirement analysis</li>
              <li>Content-Type dependency</li>
            </ul>
          </li>
          <li><strong>Protection Testing:</strong>
            <ul>
              <li>CSRF token presence</li>
              <li>Referer header validation</li>
              <li>Origin header checking</li>
              <li>SameSite cookie attributes</li>
            </ul>
          </li>
        </ul>
        
        <h3>Basic CSRF Exploitation</h3>
        <ul>
          <li><strong>GET Request Exploitation:</strong>
            <ul>
              <li>Image tag attacks (&lt;img src="..."&gt;)</li>
              <li>Link-based exploitation</li>
              <li>CSS background-image abuse</li>
              <li>Iframe source manipulation</li>
            </ul>
          </li>
          <li><strong>POST Request Exploitation:</strong>
            <ul>
              <li>Auto-submitting forms</li>
              <li>JavaScript-driven requests</li>
              <li>Hidden iframe techniques</li>
              <li>XMLHttpRequest exploitation</li>
            </ul>
          </li>
          <li><strong>Payload Construction:</strong>
            <ul>
              <li>HTML form creation</li>
              <li>JavaScript payload development</li>
              <li>Social engineering integration</li>
              <li>Multi-step attack chains</li>
            </ul>
          </li>
        </ul>
        
        <h3>CSRF Testing Tools and Techniques</h3>
        <ul>
          <li><strong>Manual Testing:</strong>
            <ul>
              <li>Browser developer tools</li>
              <li>Request modification and replay</li>
              <li>Cross-origin testing</li>
              <li>Protection bypass attempts</li>
            </ul>
          </li>
          <li><strong>Automated Tools:</strong>
            <ul>
              <li>Burp Suite CSRF scanner</li>
              <li>OWASP ZAP CSRF testing</li>
              <li>Custom automation scripts</li>
              <li>Browser extension tools</li>
            </ul>
          </li>
          <li><strong>Proof-of-Concept Development:</strong>
            <ul>
              <li>Standalone HTML pages</li>
              <li>JavaScript-based exploits</li>
              <li>Social engineering scenarios</li>
              <li>Multi-browser compatibility</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced CSRF Techniques and Modern Bypasses",
      content: `
        <h2>Advanced CSRF Techniques and Modern Protection Bypasses</h2>
        <p>Advanced CSRF exploitation involves bypassing modern protection mechanisms, exploiting complex scenarios, and chaining CSRF with other vulnerabilities.</p>
        
        <h3>CSRF Token Bypass Techniques</h3>
        <ul>
          <li><strong>Token Validation Flaws:</strong>
            <ul>
              <li>Missing token validation</li>
              <li>Empty token acceptance</li>
              <li>Token reuse vulnerabilities</li>
              <li>Predictable token generation</li>
            </ul>
          </li>
          <li><strong>Token Extraction Methods:</strong>
            <ul>
              <li>XSS-based token theft</li>
              <li>Subdomain token leakage</li>
              <li>Referer header exploitation</li>
              <li>Error page token disclosure</li>
            </ul>
          </li>
          <li><strong>Token Bypass Strategies:</strong>
            <ul>
              <li>HTTP method override</li>
              <li>Content-Type manipulation</li>
              <li>Parameter pollution</li>
              <li>Encoding and case variations</li>
            </ul>
          </li>
        </ul>
        
        <h3>SameSite Cookie Bypass</h3>
        <ul>
          <li><strong>SameSite Attribute Understanding:</strong>
            <ul>
              <li>Strict, Lax, and None values</li>
              <li>Browser implementation differences</li>
              <li>Default behavior variations</li>
              <li>Cross-site context definitions</li>
            </ul>
          </li>
          <li><strong>SameSite Bypass Techniques:</strong>
            <ul>
              <li>Top-level navigation exploitation</li>
              <li>Subdomain-based attacks</li>
              <li>Browser quirk exploitation</li>
              <li>Legacy browser targeting</li>
            </ul>
          </li>
          <li><strong>Modern Attack Vectors:</strong>
            <ul>
              <li>WebSocket CSRF attacks</li>
              <li>PostMessage exploitation</li>
              <li>Service Worker abuse</li>
              <li>Progressive Web App attacks</li>
            </ul>
          </li>
        </ul>
        
        <h3>CSRF Chaining and Advanced Scenarios</h3>
        <ul>
          <li><strong>CSRF + XSS Combinations:</strong>
            <ul>
              <li>XSS-enabled CSRF token extraction</li>
              <li>DOM-based CSRF attacks</li>
              <li>Stored XSS for persistent CSRF</li>
              <li>Reflected XSS CSRF chains</li>
            </ul>
          </li>
          <li><strong>CSRF + Clickjacking:</strong>
            <ul>
              <li>Invisible iframe overlays</li>
              <li>UI redressing attacks</li>
              <li>Drag-and-drop exploitation</li>
              <li>Touch-based mobile attacks</li>
            </ul>
          </li>
          <li><strong>Advanced Attack Scenarios:</strong>
            <ul>
              <li>Multi-step CSRF attacks</li>
              <li>Time-delayed exploitation</li>
              <li>Conditional CSRF attacks</li>
              <li>State-dependent exploitation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the fundamental requirement for a successful CSRF attack?",
            options: [
              "The victim must have administrative privileges",
              "The victim must be authenticated to the target application",
              "The attack must use HTTPS",
              "The application must use cookies"
            ],
            correctAnswer: 1,
            explanation: "The victim must be authenticated to the target application because CSRF attacks rely on the browser automatically including authentication credentials (like session cookies) with cross-site requests."
          },
          {
            question: "Which SameSite cookie attribute provides the strongest CSRF protection?",
            options: [
              "SameSite=None",
              "SameSite=Lax",
              "SameSite=Strict",
              "SameSite=Secure"
            ],
            correctAnswer: 2,
            explanation: "SameSite=Strict provides the strongest CSRF protection because it prevents cookies from being sent with any cross-site requests, including top-level navigation, effectively blocking most CSRF attacks."
          },
          {
            question: "What is the most effective way to bypass CSRF token protection?",
            options: [
              "Brute forcing the token",
              "Using XSS to extract the token",
              "Disabling JavaScript",
              "Using only GET requests"
            ],
            correctAnswer: 1,
            explanation: "Using XSS to extract the token is most effective because it allows the attacker to read the CSRF token from the same origin and include it in the malicious request, bypassing the protection mechanism."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
