/**
 * Introduction to Bug Bounty Hunting Module
 */

export const bugBountyIntroContent = {
  id: "bb-1",
  pathId: "bug-bounty",
  title: "Introduction to Bug Bounty Hunting",
  description: "Learn the fundamentals of bug bounty hunting, including platforms, methodologies, and ethical considerations.",
  objectives: [
    "Understand bug bounty programs and their purpose",
    "Learn about major bug bounty platforms",
    "Master ethical hacking principles",
    "Understand legal and responsible disclosure",
    "Learn bug bounty hunting methodologies",
    "Develop a professional mindset for security research"
  ],
  difficulty: "Beginner",
  estimatedTime: 90,
  sections: [
    {
      title: "What is Bug Bounty Hunting?",
      content: `
        <h2>What is Bug Bounty Hunting?</h2>
        <p>Bug bounty hunting is the practice of finding and reporting security vulnerabilities in applications, websites, and systems in exchange for rewards.</p>
        
        <h3>Key Concepts</h3>
        <ul>
          <li><strong>Vulnerability Research:</strong> Systematic search for security flaws</li>
          <li><strong>Responsible Disclosure:</strong> Ethical reporting of vulnerabilities</li>
          <li><strong>Reward Programs:</strong> Financial incentives for valid findings</li>
          <li><strong>Community Collaboration:</strong> Learning and sharing knowledge</li>
        </ul>
        
        <h3>Benefits of Bug Bounty Programs</h3>
        <ul>
          <li><strong>For Organizations:</strong>
            <ul>
              <li>Cost-effective security testing</li>
              <li>Access to diverse security expertise</li>
              <li>Continuous security assessment</li>
              <li>Improved security posture</li>
            </ul>
          </li>
          <li><strong>For Researchers:</strong>
            <ul>
              <li>Financial rewards for findings</li>
              <li>Skill development and learning</li>
              <li>Recognition in security community</li>
              <li>Career advancement opportunities</li>
            </ul>
          </li>
        </ul>
        
        <h3>Types of Bug Bounty Programs</h3>
        <ul>
          <li><strong>Public Programs:</strong> Open to all researchers</li>
          <li><strong>Private Programs:</strong> Invitation-only programs</li>
          <li><strong>Continuous Programs:</strong> Always accepting submissions</li>
          <li><strong>Time-Limited Programs:</strong> Specific duration contests</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Bug Bounty Platforms",
      content: `
        <h2>Major Bug Bounty Platforms</h2>
        <p>Several platforms connect security researchers with organizations running bug bounty programs.</p>
        
        <h3>Popular Platforms</h3>
        <ul>
          <li><strong>HackerOne:</strong>
            <ul>
              <li>Largest bug bounty platform</li>
              <li>Wide variety of programs</li>
              <li>Strong community features</li>
              <li>Comprehensive reporting tools</li>
            </ul>
          </li>
          <li><strong>Bugcrowd:</strong>
            <ul>
              <li>Crowdsourced security platform</li>
              <li>Focus on continuous testing</li>
              <li>Advanced analytics and insights</li>
              <li>Researcher skill development</li>
            </ul>
          </li>
          <li><strong>Synack:</strong>
            <ul>
              <li>Invitation-only platform</li>
              <li>Vetted researcher community</li>
              <li>High-value targets</li>
              <li>Advanced testing tools</li>
            </ul>
          </li>
          <li><strong>Intigriti:</strong>
            <ul>
              <li>European-focused platform</li>
              <li>Strong compliance features</li>
              <li>Educational resources</li>
              <li>Community events</li>
            </ul>
          </li>
        </ul>
        
        <h3>Platform Selection Criteria</h3>
        <ul>
          <li>Program variety and quality</li>
          <li>Payment terms and reliability</li>
          <li>Community and support</li>
          <li>Tools and resources provided</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary purpose of responsible disclosure in bug bounty hunting?",
            options: [
              "To maximize financial rewards",
              "To gain public recognition",
              "To ethically report vulnerabilities without causing harm",
              "To compete with other researchers"
            ],
            correctAnswer: 2,
            explanation: "Responsible disclosure ensures that vulnerabilities are reported ethically to help organizations fix security issues without causing harm or exposing users to risk."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
