/**
 * Business Logic Vulnerabilities Module
 */

export const businessLogicContent = {
  id: "bb-9",
  pathId: "bug-bounty",
  title: "Business Logic Vulnerabilities",
  description: "Master the identification and exploitation of business logic flaws, including workflow bypasses, race conditions, and application-specific vulnerabilities that traditional scanners miss.",
  objectives: [
    "Understand business logic vulnerability fundamentals",
    "Learn to identify workflow and process flaws",
    "Master race condition and timing attack techniques",
    "Develop skills in application flow analysis",
    "Learn to exploit payment and transaction logic",
    "Create comprehensive business logic test cases"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "Business Logic Fundamentals",
      content: `
        <h2>Business Logic Vulnerability Fundamentals</h2>
        <p>Business logic vulnerabilities arise from flaws in application design and implementation that allow attackers to abuse legitimate functionality in unintended ways.</p>
        
        <h3>Understanding Business Logic</h3>
        <ul>
          <li><strong>Business Logic Definition:</strong>
            <ul>
              <li>Application-specific rules and workflows</li>
              <li>Business process implementation</li>
              <li>User interaction patterns</li>
              <li>Data validation and processing logic</li>
            </ul>
          </li>
          <li><strong>Common Business Logic Flaws:</strong>
            <ul>
              <li>Workflow bypass vulnerabilities</li>
              <li>Insufficient process validation</li>
              <li>Race condition vulnerabilities</li>
              <li>Price manipulation attacks</li>
            </ul>
          </li>
          <li><strong>Why Logic Flaws are Missed:</strong>
            <ul>
              <li>Application-specific nature</li>
              <li>Require understanding of business context</li>
              <li>Not detectable by automated scanners</li>
              <li>Need manual testing and analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Business Logic Attack Categories</h3>
        <ul>
          <li><strong>Authentication and Authorization Bypass:</strong>
            <ul>
              <li>Multi-step authentication bypass</li>
              <li>Role-based access control flaws</li>
              <li>Session management vulnerabilities</li>
              <li>Password reset logic flaws</li>
            </ul>
          </li>
          <li><strong>Transaction and Payment Logic:</strong>
            <ul>
              <li>Price manipulation attacks</li>
              <li>Discount and coupon abuse</li>
              <li>Currency conversion flaws</li>
              <li>Refund and chargeback logic</li>
            </ul>
          </li>
          <li><strong>Workflow and Process Flaws:</strong>
            <ul>
              <li>Step skipping in multi-step processes</li>
              <li>State manipulation attacks</li>
              <li>Approval process bypass</li>
              <li>Data validation bypass</li>
            </ul>
          </li>
          <li><strong>Resource and Rate Limiting:</strong>
            <ul>
              <li>Resource exhaustion attacks</li>
              <li>Rate limiting bypass</li>
              <li>Quota and limit manipulation</li>
              <li>Time-based restriction bypass</li>
            </ul>
          </li>
        </ul>
        
        <h3>Business Logic Testing Methodology</h3>
        <ul>
          <li><strong>Application Understanding:</strong>
            <ul>
              <li>Business model analysis</li>
              <li>User role identification</li>
              <li>Workflow mapping</li>
              <li>Critical function identification</li>
            </ul>
          </li>
          <li><strong>Test Case Development:</strong>
            <ul>
              <li>Negative test case creation</li>
              <li>Edge case identification</li>
              <li>Boundary value testing</li>
              <li>State transition testing</li>
            </ul>
          </li>
          <li><strong>Manual Testing Approach:</strong>
            <ul>
              <li>Step-by-step process analysis</li>
              <li>Parameter manipulation testing</li>
              <li>Timing and sequence testing</li>
              <li>Multi-user scenario testing</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Race Conditions and Timing Attacks",
      content: `
        <h2>Race Conditions and Timing Attack Exploitation</h2>
        <p>Race conditions occur when application behavior depends on timing or sequence of events, allowing attackers to exploit timing windows for unauthorized access or actions.</p>
        
        <h3>Race Condition Fundamentals</h3>
        <ul>
          <li><strong>Race Condition Types:</strong>
            <ul>
              <li>Time-of-check to time-of-use (TOCTOU)</li>
              <li>Concurrent request processing</li>
              <li>Database transaction races</li>
              <li>File system race conditions</li>
            </ul>
          </li>
          <li><strong>Common Scenarios:</strong>
            <ul>
              <li>Account balance manipulation</li>
              <li>Coupon and discount abuse</li>
              <li>Resource allocation races</li>
              <li>Authentication bypass</li>
            </ul>
          </li>
          <li><strong>Detection Techniques:</strong>
            <ul>
              <li>Concurrent request analysis</li>
              <li>Timing measurement</li>
              <li>State observation</li>
              <li>Resource monitoring</li>
            </ul>
          </li>
        </ul>
        
        <h3>Exploitation Techniques</h3>
        <ul>
          <li><strong>Concurrent Request Generation:</strong>
            <ul>
              <li>Multi-threaded request tools</li>
              <li>Burp Suite Intruder</li>
              <li>Custom Python scripts</li>
              <li>Browser developer tools</li>
            </ul>
          </li>
          <li><strong>Timing Optimization:</strong>
            <ul>
              <li>Request synchronization</li>
              <li>Network latency consideration</li>
              <li>Server processing time analysis</li>
              <li>Connection pooling effects</li>
            </ul>
          </li>
          <li><strong>State Manipulation:</strong>
            <ul>
              <li>Session state races</li>
              <li>Database state manipulation</li>
              <li>Cache invalidation races</li>
              <li>File lock bypasses</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Race Condition Scenarios</h3>
        <ul>
          <li><strong>E-commerce Vulnerabilities:</strong>
            <ul>
              <li>Inventory manipulation</li>
              <li>Price change exploitation</li>
              <li>Cart modification races</li>
              <li>Payment processing races</li>
            </ul>
          </li>
          <li><strong>Financial Application Races:</strong>
            <ul>
              <li>Double spending attacks</li>
              <li>Balance manipulation</li>
              <li>Transfer limit bypass</li>
              <li>Interest calculation races</li>
            </ul>
          </li>
          <li><strong>Social Media and Content:</strong>
            <ul>
              <li>Like/vote manipulation</li>
              <li>Comment posting races</li>
              <li>Follow/unfollow races</li>
              <li>Content moderation bypass</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Workflow and Process Analysis",
      content: `
        <h2>Workflow and Process Vulnerability Analysis</h2>
        <p>Analyzing application workflows and business processes helps identify logic flaws that allow bypassing intended security controls and business rules.</p>
        
        <h3>Workflow Analysis Methodology</h3>
        <ul>
          <li><strong>Process Mapping:</strong>
            <ul>
              <li>User journey documentation</li>
              <li>State transition diagrams</li>
              <li>Decision point identification</li>
              <li>Validation checkpoint mapping</li>
            </ul>
          </li>
          <li><strong>Critical Path Analysis:</strong>
            <ul>
              <li>Security-critical steps</li>
              <li>Authorization checkpoints</li>
              <li>Data validation points</li>
              <li>Business rule enforcement</li>
            </ul>
          </li>
          <li><strong>Bypass Opportunity Identification:</strong>
            <ul>
              <li>Step skipping possibilities</li>
              <li>Alternative path discovery</li>
              <li>State manipulation opportunities</li>
              <li>Validation bypass techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Common Workflow Vulnerabilities</h3>
        <ul>
          <li><strong>Multi-Step Process Flaws:</strong>
            <ul>
              <li>Step sequence manipulation</li>
              <li>Incomplete process completion</li>
              <li>State persistence issues</li>
              <li>Session management flaws</li>
            </ul>
          </li>
          <li><strong>Approval and Review Processes:</strong>
            <ul>
              <li>Approval bypass techniques</li>
              <li>Review process manipulation</li>
              <li>Escalation path abuse</li>
              <li>Delegation mechanism flaws</li>
            </ul>
          </li>
          <li><strong>Registration and Onboarding:</strong>
            <ul>
              <li>Account creation bypass</li>
              <li>Verification process flaws</li>
              <li>Identity validation bypass</li>
              <li>Terms acceptance manipulation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Testing Techniques and Tools</h3>
        <ul>
          <li><strong>Manual Testing Approaches:</strong>
            <ul>
              <li>Process flow manipulation</li>
              <li>Parameter tampering</li>
              <li>Request replay attacks</li>
              <li>State modification testing</li>
            </ul>
          </li>
          <li><strong>Automated Testing Tools:</strong>
            <ul>
              <li>Burp Suite workflow analysis</li>
              <li>Custom automation scripts</li>
              <li>State machine testing tools</li>
              <li>Process monitoring utilities</li>
            </ul>
          </li>
          <li><strong>Documentation and Reporting:</strong>
            <ul>
              <li>Workflow diagram creation</li>
              <li>Vulnerability impact assessment</li>
              <li>Business risk evaluation</li>
              <li>Remediation recommendations</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What makes business logic vulnerabilities particularly challenging to detect?",
            options: [
              "They require expensive tools",
              "They are application-specific and require understanding of business context",
              "They only affect legacy applications",
              "They are always encrypted"
            ],
            correctAnswer: 1,
            explanation: "Business logic vulnerabilities are challenging to detect because they are application-specific and require understanding of the business context, making them undetectable by automated scanners that focus on generic vulnerabilities."
          },
          {
            question: "Which type of race condition involves exploiting the time gap between checking and using a resource?",
            options: [
              "Concurrent request processing",
              "Database transaction races",
              "Time-of-check to time-of-use (TOCTOU)",
              "File system race conditions"
            ],
            correctAnswer: 2,
            explanation: "Time-of-check to time-of-use (TOCTOU) race conditions exploit the time gap between when a resource is checked (validated) and when it is actually used, allowing attackers to modify the resource in between."
          },
          {
            question: "What is the most effective approach for identifying workflow bypass vulnerabilities?",
            options: [
              "Automated vulnerability scanning",
              "Process mapping and manual step manipulation testing",
              "Code review only",
              "Network traffic analysis"
            ],
            correctAnswer: 1,
            explanation: "Process mapping and manual step manipulation testing is most effective for identifying workflow bypass vulnerabilities because it requires understanding the intended business process and manually testing deviations from the expected flow."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
