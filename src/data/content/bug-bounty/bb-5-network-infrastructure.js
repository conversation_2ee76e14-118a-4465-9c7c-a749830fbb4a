/**
 * Network Infrastructure Testing Module
 */

export const networkInfrastructureContent = {
  id: "bb-5",
  pathId: "bug-bounty",
  title: "Network Infrastructure Testing",
  description: "Master network infrastructure vulnerability assessment and penetration testing techniques for bug bounty hunting, including port scanning, service enumeration, and network-based attacks.",
  objectives: [
    "Understand network reconnaissance and enumeration techniques",
    "Learn port scanning and service identification methods",
    "Master network protocol vulnerabilities and exploits",
    "Develop skills in network-based attack vectors",
    "Learn to identify and exploit network misconfigurations",
    "Create comprehensive network security assessments"
  ],
  difficulty: "Intermediate",
  estimatedTime: 130,
  sections: [
    {
      title: "Network Reconnaissance and Enumeration",
      content: `
        <h2>Network Reconnaissance and Enumeration</h2>
        <p>Network reconnaissance is the foundation of infrastructure testing, involving systematic discovery and enumeration of network assets, services, and potential attack vectors.</p>
        
        <h3>Network Discovery Techniques</h3>
        <ul>
          <li><strong>Passive Reconnaissance:</strong>
            <ul>
              <li>DNS enumeration and zone transfers</li>
              <li>WHOIS database queries</li>
              <li>Search engine dorking</li>
              <li>Social media and public information gathering</li>
            </ul>
          </li>
          <li><strong>Active Reconnaissance:</strong>
            <ul>
              <li>Network range identification</li>
              <li>Subdomain enumeration</li>
              <li>IP address scanning</li>
              <li>Network topology mapping</li>
            </ul>
          </li>
          <li><strong>DNS Enumeration:</strong>
            <ul>
              <li>DNS record enumeration (A, AAAA, MX, TXT, etc.)</li>
              <li>DNS zone transfer attempts</li>
              <li>Reverse DNS lookups</li>
              <li>DNS cache snooping</li>
            </ul>
          </li>
        </ul>
        
        <h3>Port Scanning and Service Detection</h3>
        <ul>
          <li><strong>Port Scanning Techniques:</strong>
            <ul>
              <li>TCP SYN scanning (stealth scanning)</li>
              <li>TCP connect scanning</li>
              <li>UDP port scanning</li>
              <li>FIN, NULL, and Xmas scans</li>
            </ul>
          </li>
          <li><strong>Service Enumeration:</strong>
            <ul>
              <li>Banner grabbing and service identification</li>
              <li>Version detection and fingerprinting</li>
              <li>Operating system detection</li>
              <li>Service-specific enumeration</li>
            </ul>
          </li>
          <li><strong>Nmap Advanced Techniques:</strong>
            <ul>
              <li>NSE (Nmap Scripting Engine) usage</li>
              <li>Custom timing and evasion techniques</li>
              <li>Firewall and IDS evasion</li>
              <li>Output formatting and reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Protocol Analysis</h3>
        <ul>
          <li><strong>Protocol Vulnerability Assessment:</strong>
            <ul>
              <li>HTTP/HTTPS service analysis</li>
              <li>SSH service enumeration</li>
              <li>FTP and TFTP vulnerabilities</li>
              <li>SMTP and email service testing</li>
            </ul>
          </li>
          <li><strong>Database Service Testing:</strong>
            <ul>
              <li>MySQL and PostgreSQL enumeration</li>
              <li>MSSQL service vulnerabilities</li>
              <li>MongoDB and NoSQL databases</li>
              <li>Redis and cache service testing</li>
            </ul>
          </li>
          <li><strong>Network File Services:</strong>
            <ul>
              <li>SMB/CIFS share enumeration</li>
              <li>NFS mount point discovery</li>
              <li>SNMP community string testing</li>
              <li>LDAP directory service enumeration</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Network Attack Vectors",
      content: `
        <h2>Network-Based Attack Vectors</h2>
        <p>Understanding and exploiting network-based vulnerabilities requires knowledge of various attack techniques and their practical implementation.</p>
        
        <h3>Man-in-the-Middle (MITM) Attacks</h3>
        <ul>
          <li><strong>ARP Spoofing and Poisoning:</strong>
            <ul>
              <li>ARP table manipulation</li>
              <li>Network traffic interception</li>
              <li>Gateway impersonation</li>
              <li>DHCP spoofing attacks</li>
            </ul>
          </li>
          <li><strong>SSL/TLS Attacks:</strong>
            <ul>
              <li>SSL stripping attacks</li>
              <li>Certificate pinning bypass</li>
              <li>Weak cipher exploitation</li>
              <li>TLS downgrade attacks</li>
            </ul>
          </li>
          <li><strong>DNS Attacks:</strong>
            <ul>
              <li>DNS spoofing and cache poisoning</li>
              <li>DNS tunneling techniques</li>
              <li>DNS rebinding attacks</li>
              <li>Subdomain takeover vulnerabilities</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Service Exploitation</h3>
        <ul>
          <li><strong>SSH Service Attacks:</strong>
            <ul>
              <li>SSH brute force attacks</li>
              <li>SSH key-based authentication bypass</li>
              <li>SSH tunneling and port forwarding</li>
              <li>SSH configuration vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Web Server Exploitation:</strong>
            <ul>
              <li>HTTP method tampering</li>
              <li>Virtual host enumeration</li>
              <li>Web server misconfiguration</li>
              <li>CGI and script vulnerabilities</li>
            </ul>
          </li>
          <li><strong>Database Service Attacks:</strong>
            <ul>
              <li>Default credential testing</li>
              <li>Database-specific vulnerabilities</li>
              <li>Privilege escalation techniques</li>
              <li>Data extraction methods</li>
            </ul>
          </li>
        </ul>
        
        <h3>Network Evasion Techniques</h3>
        <ul>
          <li><strong>Firewall Evasion:</strong>
            <ul>
              <li>Packet fragmentation</li>
              <li>Source port manipulation</li>
              <li>Protocol tunneling</li>
              <li>Timing-based evasion</li>
            </ul>
          </li>
          <li><strong>IDS/IPS Evasion:</strong>
            <ul>
              <li>Signature evasion techniques</li>
              <li>Polymorphic payload generation</li>
              <li>Traffic obfuscation</li>
              <li>Decoy and noise generation</li>
            </ul>
          </li>
          <li><strong>Network Pivoting:</strong>
            <ul>
              <li>Internal network discovery</li>
              <li>Lateral movement techniques</li>
              <li>Proxy chaining</li>
              <li>Covert channel communication</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Infrastructure Security Assessment",
      content: `
        <h2>Infrastructure Security Assessment</h2>
        <p>Comprehensive infrastructure security assessment involves systematic evaluation of network components, configurations, and security controls.</p>
        
        <h3>Network Architecture Analysis</h3>
        <ul>
          <li><strong>Network Segmentation Assessment:</strong>
            <ul>
              <li>VLAN configuration analysis</li>
              <li>Network zone isolation testing</li>
              <li>DMZ security evaluation</li>
              <li>Internal network access controls</li>
            </ul>
          </li>
          <li><strong>Routing and Switching Security:</strong>
            <ul>
              <li>Router configuration vulnerabilities</li>
              <li>Switch security features</li>
              <li>VLAN hopping attacks</li>
              <li>Spanning tree protocol attacks</li>
            </ul>
          </li>
          <li><strong>Wireless Network Security:</strong>
            <ul>
              <li>WiFi encryption assessment</li>
              <li>Access point configuration</li>
              <li>Wireless authentication bypass</li>
              <li>Rogue access point detection</li>
            </ul>
          </li>
        </ul>
        
        <h3>Security Control Evaluation</h3>
        <ul>
          <li><strong>Firewall Rule Analysis:</strong>
            <ul>
              <li>Rule set effectiveness</li>
              <li>Default deny policies</li>
              <li>Unnecessary open ports</li>
              <li>Rule ordering and conflicts</li>
            </ul>
          </li>
          <li><strong>Network Monitoring and Logging:</strong>
            <ul>
              <li>Log collection and analysis</li>
              <li>Network traffic monitoring</li>
              <li>Intrusion detection effectiveness</li>
              <li>Security event correlation</li>
            </ul>
          </li>
          <li><strong>Access Control Assessment:</strong>
            <ul>
              <li>Network access control (NAC)</li>
              <li>VPN security configuration</li>
              <li>Remote access policies</li>
              <li>Privileged account management</li>
            </ul>
          </li>
        </ul>
        
        <h3>Vulnerability Assessment Tools</h3>
        <ul>
          <li><strong>Network Scanning Tools:</strong>
            <ul>
              <li>Nmap for port scanning and enumeration</li>
              <li>Masscan for large-scale scanning</li>
              <li>Zmap for internet-wide scanning</li>
              <li>Unicornscan for advanced scanning</li>
            </ul>
          </li>
          <li><strong>Vulnerability Scanners:</strong>
            <ul>
              <li>Nessus for comprehensive scanning</li>
              <li>OpenVAS for open-source scanning</li>
              <li>Nuclei for fast vulnerability detection</li>
              <li>Custom script development</li>
            </ul>
          </li>
          <li><strong>Network Analysis Tools:</strong>
            <ul>
              <li>Wireshark for packet analysis</li>
              <li>tcpdump for command-line capture</li>
              <li>Ettercap for MITM attacks</li>
              <li>Bettercap for network reconnaissance</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which Nmap scan type is considered the most stealthy for port scanning?",
            options: [
              "TCP Connect scan (-sT)",
              "TCP SYN scan (-sS)",
              "UDP scan (-sU)",
              "TCP FIN scan (-sF)"
            ],
            correctAnswer: 1,
            explanation: "TCP SYN scan (-sS) is considered the most stealthy because it doesn't complete the TCP handshake, making it less likely to be logged by target systems while still providing reliable results."
          },
          {
            question: "What is the primary purpose of ARP spoofing in network attacks?",
            options: [
              "To crash network devices",
              "To intercept network traffic between hosts",
              "To scan for open ports",
              "To enumerate network services"
            ],
            correctAnswer: 1,
            explanation: "ARP spoofing is primarily used to intercept network traffic between hosts by associating the attacker's MAC address with the IP address of another host, enabling man-in-the-middle attacks."
          },
          {
            question: "Which technique is most effective for evading signature-based intrusion detection systems?",
            options: [
              "Using default attack tools",
              "Increasing attack speed",
              "Polymorphic payload generation",
              "Using only encrypted protocols"
            ],
            correctAnswer: 2,
            explanation: "Polymorphic payload generation is most effective for evading signature-based IDS because it creates variations of the same attack that don't match known signatures while maintaining the same functionality."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
