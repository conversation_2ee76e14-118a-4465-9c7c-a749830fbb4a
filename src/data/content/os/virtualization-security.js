/**
 * Virtualization & Containerization Security Module
 */

export const virtualizationSecurityContent = {
  id: "os-6", 
  pathId: "operating-system-concepts",
  title: "Virtualization & Containerization Security",
  description: "Master virtualization technologies, container security, and their security implications.",
  objectives: [
    "Understand virtualization concepts and hypervisor types",
    "Learn about virtual machine security and isolation",
    "Explore container technologies and their security model",
    "Analyze virtualization-specific attacks and defenses",
    "Understand orchestration security in containerized environments"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "Virtualization Fundamentals",
      content: `
        <h2>Virtualization Fundamentals</h2>
        <p>Virtualization allows multiple operating systems to run on a single physical machine.</p>
        
        <h3>Types of Virtualization</h3>
        <ul>
          <li><strong>Full Virtualization:</strong> Complete hardware emulation</li>
          <li><strong>Paravirtualization:</strong> Modified guest OS for efficiency</li>
          <li><strong>Hardware-Assisted:</strong> CPU features for virtualization</li>
          <li><strong>OS-Level:</strong> Containers sharing kernel</li>
        </ul>
        
        <h3>Hypervisor Types</h3>
        <h4>Type 1 (Bare Metal)</h4>
        <ul>
          <li><strong>Examples:</strong> VMware vSphere, Xen, Hyper-V</li>
          <li><strong>Architecture:</strong> Runs directly on hardware</li>
          <li><strong>Security:</strong> Smaller attack surface</li>
          <li><strong>Performance:</strong> Better performance</li>
        </ul>
        
        <h4>Type 2 (Hosted)</h4>
        <ul>
          <li><strong>Examples:</strong> VMware Workstation, VirtualBox</li>
          <li><strong>Architecture:</strong> Runs on host OS</li>
          <li><strong>Security:</strong> Dependent on host OS security</li>
          <li><strong>Use Case:</strong> Development and testing</li>
        </ul>
        
        <h3>Container Technologies</h3>
        <ul>
          <li><strong>Docker:</strong> Application containerization platform</li>
          <li><strong>containerd:</strong> Container runtime</li>
          <li><strong>Kubernetes:</strong> Container orchestration</li>
          <li><strong>LXC/LXD:</strong> System containers</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Security Implications and Threats",
      content: `
        <h2>Security Implications and Threats</h2>
        <p>Virtualization introduces new security challenges and attack vectors.</p>
        
        <h3>VM Escape Attacks</h3>
        <ul>
          <li><strong>Definition:</strong> Breaking out of VM to access hypervisor</li>
          <li><strong>Vectors:</strong> Hypervisor vulnerabilities, device emulation bugs</li>
          <li><strong>Impact:</strong> Access to other VMs or host system</li>
          <li><strong>Examples:</strong> CVE-2017-5753 (Spectre), VM escape via USB</li>
        </ul>
        
        <h3>Container Security Challenges</h3>
        <ul>
          <li><strong>Shared Kernel:</strong> Less isolation than VMs</li>
          <li><strong>Privileged Containers:</strong> Running with elevated privileges</li>
          <li><strong>Container Escapes:</strong> Breaking out of container</li>
          <li><strong>Image Vulnerabilities:</strong> Vulnerable base images</li>
        </ul>
        
        <h3>Side-Channel Attacks</h3>
        <ul>
          <li><strong>Cache Timing:</strong> Inferring data through cache behavior</li>
          <li><strong>Memory Deduplication:</strong> Attacks on shared memory pages</li>
          <li><strong>CPU Speculation:</strong> Spectre, Meltdown vulnerabilities</li>
          <li><strong>Covert Channels:</strong> Information leakage between VMs</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Virtualization Security Lab", 
    description: "Hands-on exploration of virtualization and container security.",
    tasks: [
      {
        category: "Container Security",
        commands: [
          {
            command: "docker run --rm alpine:latest whoami",
            description: "Run simple container command",
            hint: "Shows container isolation in action",
            expectedOutput: "root"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is a VM escape attack?",
      options: [
        "Deleting virtual machines",
        "Breaking out of a VM to access the hypervisor",
        "Moving VMs between hosts",
        "Backing up virtual machines"
      ],
      correct: 1,
      explanation: "A VM escape attack involves breaking out of a virtual machine to gain access to the hypervisor or host system, potentially compromising other VMs."
    }
  ]
}; 