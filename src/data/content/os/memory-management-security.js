/**
 * Memory Management & Security Module
 * 
 * This comprehensive module covers memory management concepts and their
 * security implications in operating systems.
 */

export const memoryManagementSecurityContent = {
  id: "os-3",
  pathId: "operating-system-concepts", 
  title: "Memory Management & Security",
  description: "Master memory management concepts, virtual memory, and memory-based security vulnerabilities and protections.",
  objectives: [
    "Understand logical vs physical address spaces and memory translation",
    "Learn about memory allocation techniques and virtual memory",
    "Explore memory protection mechanisms and their security benefits",
    "Analyze memory-based vulnerabilities and attack techniques",
    "Understand memory forensics and analysis techniques"
  ],
  difficulty: "Intermediate",
  estimatedTime: 150,
  sections: [
    {
      title: "Memory Hierarchy and Address Spaces",
      content: `
        <h2>Memory Hierarchy and Address Spaces</h2>
        <p>Memory management is one of the most critical OS functions, with significant security implications.</p>
        
        <h3>Memory Hierarchy</h3>
        <ul>
          <li><strong>CPU Registers:</strong> Fastest, smallest capacity</li>
          <li><strong>Cache Memory:</strong> L1, L2, L3 caches</li>
          <li><strong>Main Memory (RAM):</strong> Primary storage</li>
          <li><strong>Secondary Storage:</strong> Disks, SSDs</li>
        </ul>
        
        <h3>Address Spaces</h3>
        <h4>Logical (Virtual) Address Space</h4>
        <ul>
          <li><strong>Definition:</strong> Addresses generated by CPU/program</li>
          <li><strong>Range:</strong> 0 to max (e.g., 0 to 2^32-1 for 32-bit)</li>
          <li><strong>Process View:</strong> Each process has its own virtual address space</li>
          <li><strong>Security Benefit:</strong> Process isolation</li>
        </ul>
        
        <h4>Physical Address Space</h4>
        <ul>
          <li><strong>Definition:</strong> Actual memory addresses in RAM</li>
          <li><strong>Range:</strong> 0 to max physical memory</li>
          <li><strong>Shared:</strong> Single physical address space for entire system</li>
          <li><strong>Management:</strong> OS manages allocation</li>
        </ul>
        
        <h3>Memory Management Unit (MMU)</h3>
        <ul>
          <li><strong>Purpose:</strong> Translates virtual to physical addresses</li>
          <li><strong>Hardware:</strong> Dedicated hardware component</li>
          <li><strong>Translation:</strong> Uses page tables for mapping</li>
          <li><strong>Protection:</strong> Enforces memory access permissions</li>
        </ul>
        
        <h3>Memory Layout of a Process</h3>
        <ul>
          <li><strong>Text Segment:</strong> Program code (read-only, executable)</li>
          <li><strong>Data Segment:</strong> Initialized global variables</li>
          <li><strong>BSS Segment:</strong> Uninitialized global variables</li>
          <li><strong>Heap:</strong> Dynamic memory allocation (grows upward)</li>
          <li><strong>Stack:</strong> Function calls, local variables (grows downward)</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Virtual Memory and Paging",
      content: `
        <h2>Virtual Memory and Paging</h2>
        <p>Virtual memory allows processes to use more memory than physically available and provides strong isolation.</p>
        
        <h3>Virtual Memory Benefits</h3>
        <ul>
          <li><strong>Process Isolation:</strong> Each process has separate address space</li>
          <li><strong>Memory Protection:</strong> Hardware-enforced access controls</li>
          <li><strong>Efficient Memory Use:</strong> Only active pages in physical memory</li>
          <li><strong>Large Address Space:</strong> Processes can use more than physical RAM</li>
        </ul>
        
        <h3>Paging Mechanism</h3>
        <h4>Page Structure</h4>
        <ul>
          <li><strong>Page:</strong> Fixed-size block in virtual memory (typically 4KB)</li>
          <li><strong>Frame:</strong> Fixed-size block in physical memory</li>
          <li><strong>Page Table:</strong> Maps virtual pages to physical frames</li>
          <li><strong>Page Table Entry (PTE):</strong> Contains frame number and flags</li>
        </ul>
        
        <h4>Page Table Entry Flags</h4>
        <ul>
          <li><strong>Present Bit:</strong> Page is in physical memory</li>
          <li><strong>Read/Write Bit:</strong> Write permission</li>
          <li><strong>User/Supervisor Bit:</strong> User mode access allowed</li>
          <li><strong>Execute Disable (NX/XD):</strong> Execution prevention</li>
          <li><strong>Dirty Bit:</strong> Page has been modified</li>
          <li><strong>Accessed Bit:</strong> Page has been accessed</li>
        </ul>
        
        <h3>Translation Lookaside Buffer (TLB)</h3>
        <ul>
          <li><strong>Purpose:</strong> Cache for frequently used page translations</li>
          <li><strong>Performance:</strong> Avoids accessing page table in memory</li>
          <li><strong>Security:</strong> TLB flushing on context switches</li>
          <li><strong>Attacks:</strong> TLB poisoning, side-channel attacks</li>
        </ul>
        
        <h3>Demand Paging and Swapping</h3>
        <h4>Demand Paging</h4>
        <ul>
          <li><strong>Concept:</strong> Load pages only when accessed</li>
          <li><strong>Page Fault:</strong> Exception when accessing non-present page</li>
          <li><strong>Lazy Loading:</strong> Defer loading until necessary</li>
          <li><strong>Security:</strong> Page fault timing can leak information</li>
        </ul>
        
        <h4>Swapping</h4>
        <ul>
          <li><strong>Purpose:</strong> Move inactive pages to secondary storage</li>
          <li><strong>Swap Space:</strong> Dedicated disk area for swapped pages</li>
          <li><strong>Security Risk:</strong> Sensitive data written to disk</li>
          <li><strong>Mitigation:</strong> Encrypted swap, memory locking</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Memory Protection Mechanisms",
      content: `
        <h2>Memory Protection Mechanisms</h2>
        <p>Modern processors and operating systems implement multiple layers of memory protection.</p>
        
        <h3>Hardware Memory Protection</h3>
        <h4>Segmentation (x86)</h4>
        <ul>
          <li><strong>Segments:</strong> Variable-size memory regions</li>
          <li><strong>Descriptor Tables:</strong> GDT (Global), LDT (Local)</li>
          <li><strong>Privilege Levels:</strong> Ring 0-3 protection</li>
          <li><strong>Legacy:</strong> Mostly replaced by paging in modern systems</li>
        </ul>
        
        <h4>Page-Level Protection</h4>
        <ul>
          <li><strong>Permission Bits:</strong> Read, Write, Execute</li>
          <li><strong>User/Kernel Mode:</strong> Privilege-based access</li>
          <li><strong>NX/XD Bit:</strong> Non-execute/Execute Disable</li>
          <li><strong>SMEP/SMAP:</strong> Supervisor mode protections</li>
        </ul>
        
        <h3>Execute Disable (NX/XD) Protection</h3>
        <ul>
          <li><strong>Purpose:</strong> Prevent code execution in data pages</li>
          <li><strong>Implementation:</strong> Hardware bit in page table entries</li>
          <li><strong>Protection Against:</strong> Buffer overflow exploits</li>
          <li><strong>Requirements:</strong> Hardware and OS support</li>
        </ul>
        
        <h3>Address Space Layout Randomization (ASLR)</h3>
        <ul>
          <li><strong>Concept:</strong> Randomize memory layout of processes</li>
          <li><strong>Components:</strong> Stack, heap, libraries, executable base</li>
          <li><strong>Security Benefit:</strong> Makes exploitation harder</li>
          <li><strong>Bypasses:</strong> Information leaks, brute force</li>
        </ul>
        
        <h3>Stack Protection</h3>
        <h4>Stack Canaries</h4>
        <ul>
          <li><strong>Implementation:</strong> Random value before return address</li>
          <li><strong>Detection:</strong> Canary corruption indicates overflow</li>
          <li><strong>Types:</strong> Terminator, random, XOR canaries</li>
          <li><strong>Limitations:</strong> Can be bypassed or leaked</li>
        </ul>
        
        <h4>Shadow Stack</h4>
        <ul>
          <li><strong>Concept:</strong> Hardware-maintained backup of return addresses</li>
          <li><strong>Implementation:</strong> Intel CET (Control-flow Enforcement Technology)</li>
          <li><strong>Protection:</strong> ROP/JOP attack mitigation</li>
          <li><strong>Performance:</strong> Low overhead</li>
        </ul>
        
        <h3>Control Flow Integrity (CFI)</h3>
        <ul>
          <li><strong>Purpose:</strong> Ensure program follows intended control flow</li>
          <li><strong>Implementation:</strong> Compiler and hardware features</li>
          <li><strong>Protection:</strong> ROP, JOP, and other code-reuse attacks</li>
          <li><strong>Challenges:</strong> Performance overhead, compatibility</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Memory-Based Vulnerabilities",
      content: `
        <h2>Memory-Based Vulnerabilities</h2>
        <p>Memory vulnerabilities are among the most common and dangerous security flaws in software.</p>
        
        <h3>Buffer Overflows</h3>
        <h4>Stack-Based Buffer Overflows</h4>
        <ul>
          <li><strong>Mechanism:</strong> Overwrite return address on stack</li>
          <li><strong>Impact:</strong> Code execution, privilege escalation</li>
          <li><strong>Example:</strong> strcpy() without bounds checking</li>
          <li><strong>Mitigation:</strong> Stack canaries, NX bit, ASLR</li>
        </ul>
        
        <h4>Heap-Based Buffer Overflows</h4>
        <ul>
          <li><strong>Target:</strong> Heap metadata corruption</li>
          <li><strong>Techniques:</strong> Heap spraying, feng shui</li>
          <li><strong>Impact:</strong> Arbitrary code execution</li>
          <li><strong>Mitigation:</strong> Heap cookies, guard pages</li>
        </ul>
        
        <h3>Memory Corruption Vulnerabilities</h3>
        <h4>Use-After-Free (UAF)</h4>
        <ul>
          <li><strong>Cause:</strong> Accessing freed memory</li>
          <li><strong>Exploitation:</strong> Type confusion, code execution</li>
          <li><strong>Detection:</strong> AddressSanitizer, Valgrind</li>
          <li><strong>Mitigation:</strong> Zeroing freed memory, delayed free</li>
        </ul>
        
        <h4>Double-Free</h4>
        <ul>
          <li><strong>Cause:</strong> Freeing same memory twice</li>
          <li><strong>Impact:</strong> Heap corruption, code execution</li>
          <li><strong>Detection:</strong> Runtime checks, debugging tools</li>
          <li><strong>Prevention:</strong> Set pointers to NULL after free</li>
        </ul>
        
        <h4>Integer Overflows</h4>
        <ul>
          <li><strong>Cause:</strong> Arithmetic result exceeds data type range</li>
          <li><strong>Impact:</strong> Unexpected behavior, buffer overflows</li>
          <li><strong>Example:</strong> malloc(size) with overflowed size</li>
          <li><strong>Mitigation:</strong> Safe integer libraries, compiler checks</li>
        </ul>
        
        <h3>Format String Vulnerabilities</h3>
        <ul>
          <li><strong>Cause:</strong> User input as format string</li>
          <li><strong>Techniques:</strong> %n for writing, %x for reading</li>
          <li><strong>Impact:</strong> Information disclosure, code execution</li>
          <li><strong>Example:</strong> printf(user_input) instead of printf("%s", user_input)</li>
          <li><strong>Prevention:</strong> Always use format strings, input validation</li>
        </ul>
        
        <h3>Return-Oriented Programming (ROP)</h3>
        <ul>
          <li><strong>Concept:</strong> Chain existing code snippets (gadgets)</li>
          <li><strong>Bypass:</strong> NX/XD protection</li>
          <li><strong>Requirements:</strong> Code reuse, stack control</li>
          <li><strong>Variants:</strong> JOP (Jump-oriented), COP (Call-oriented)</li>
          <li><strong>Mitigation:</strong> CFI, CET, code randomization</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Memory Analysis Lab",
    description: "Hands-on exploration of memory management, protection mechanisms, and vulnerability analysis.",
    tasks: [
      {
        category: "Memory Information",
        commands: [
          {
            command: "cat /proc/meminfo",
            description: "Display detailed memory information",
            hint: "Shows total, free, available memory and more",
            expectedOutput: "MemTotal: 16384000 kB\nMemFree: 8192000 kB"
          },
          {
            command: "pmap -x $$",
            description: "Show memory map of current shell process",
            hint: "Displays virtual memory layout with permissions",
            expectedOutput: "Address Kbytes RSS Dirty Mode Mapping"
          }
        ]
      },
      {
        category: "Memory Protection Analysis",
        commands: [
          {
            command: "cat /proc/sys/kernel/randomize_va_space",
            description: "Check ASLR status",
            hint: "0=disabled, 1=conservative, 2=full randomization",
            expectedOutput: "2"
          },
          {
            command: "grep -i nx /proc/cpuinfo",
            description: "Check for NX bit support",
            hint: "Look for 'nx' flag in CPU features",
            expectedOutput: "flags: ... nx ..."
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary security benefit of virtual memory?",
      options: [
        "Faster memory access",
        "Process isolation and memory protection",
        "Reduced memory usage",
        "Simplified programming"
      ],
      correct: 1,
      explanation: "Virtual memory provides process isolation by giving each process its own virtual address space and enables hardware-enforced memory protection."
    },
    {
      question: "What does the NX/XD bit prevent?",
      options: [
        "Reading from memory",
        "Writing to memory", 
        "Executing code from data pages",
        "Accessing kernel memory"
      ],
      correct: 2,
      explanation: "The NX (No-eXecute) or XD (eXecute Disable) bit prevents code execution from data pages, helping to mitigate buffer overflow exploits."
    },
    {
      question: "What is a use-after-free vulnerability?",
      options: [
        "Accessing memory before it's allocated",
        "Accessing memory after it has been freed",
        "Freeing memory twice",
        "Not freeing allocated memory"
      ],
      correct: 1,
      explanation: "A use-after-free vulnerability occurs when a program continues to use memory after it has been freed, potentially leading to corruption or code execution."
    }
  ]
}; 