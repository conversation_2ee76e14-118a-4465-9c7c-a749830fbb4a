/**
 * Incident Response & Digital Forensics Module
 */

export const incidentResponseContent = {
  id: "os-13",
  pathId: "operating-system-concepts",
  title: "Incident Response & Digital Forensics",
  description: "Master incident response procedures, digital forensics, and evidence handling.",
  objectives: [
    "Understand incident response lifecycle and procedures",
    "Learn digital forensics principles and methodologies",
    "Explore evidence collection and preservation techniques",
    "Analyze system artifacts and timeline reconstruction",
    "Master legal and compliance considerations"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "Incident Response Framework",
      content: `
        <h2>Incident Response Framework</h2>
        <p>Structured approach to detecting, analyzing, and responding to security incidents.</p>
        
        <h3>NIST Incident Response Lifecycle</h3>
        <ul>
          <li><strong>Preparation:</strong> Policies, procedures, tools, training</li>
          <li><strong>Detection & Analysis:</strong> Monitoring, triage, classification</li>
          <li><strong>Containment, Eradication & Recovery:</strong> Isolate, remove, restore</li>
          <li><strong>Post-Incident Activity:</strong> Lessons learned, improvements</li>
        </ul>
        
        <h3>Incident Classification</h3>
        <ul>
          <li><strong>Severity Levels:</strong> Critical, high, medium, low</li>
          <li><strong>Incident Types:</strong> Malware, data breach, DDoS, insider threat</li>
          <li><strong>Impact Assessment:</strong> Business, technical, regulatory</li>
          <li><strong>Response Priority:</strong> Resource allocation and urgency</li>
        </ul>
        
        <h3>Initial Response Actions</h3>
        <ul>
          <li><strong>Incident Declaration:</strong> Formal incident notification</li>
          <li><strong>Team Assembly:</strong> Incident response team activation</li>
          <li><strong>Evidence Preservation:</strong> Protect digital evidence</li>
          <li><strong>Communication:</strong> Stakeholder notifications</li>
          <li><strong>Documentation:</strong> Detailed incident logging</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Digital Forensics Methodology",
      content: `
        <h2>Digital Forensics Methodology</h2>
        <p>Scientific approach to collecting, preserving, and analyzing digital evidence.</p>
        
        <h3>Forensic Process</h3>
        <ul>
          <li><strong>Identification:</strong> Locate potential evidence sources</li>
          <li><strong>Preservation:</strong> Prevent evidence contamination</li>
          <li><strong>Collection:</strong> Acquire digital evidence</li>
          <li><strong>Examination:</strong> Technical analysis of evidence</li>
          <li><strong>Analysis:</strong> Interpret findings and draw conclusions</li>
          <li><strong>Presentation:</strong> Report findings and testimony</li>
        </ul>
        
        <h3>Evidence Types</h3>
        <ul>
          <li><strong>Volatile Data:</strong> Memory, network connections, processes</li>
          <li><strong>Non-Volatile Data:</strong> Hard drives, removable media</li>
          <li><strong>Network Evidence:</strong> Traffic captures, logs</li>
          <li><strong>Application Data:</strong> Databases, application logs</li>
          <li><strong>System Artifacts:</strong> Registry, file system metadata</li>
        </ul>
        
        <h3>Forensic Tools and Techniques</h3>
        <h4>Disk Imaging</h4>
        <ul>
          <li><strong>dd Command:</strong> Bit-by-bit disk copying</li>
          <li><strong>FTK Imager:</strong> GUI-based imaging tool</li>
          <li><strong>EnCase:</strong> Commercial forensic platform</li>
          <li><strong>Hash Verification:</strong> MD5, SHA checksums</li>
        </ul>
        
        <h4>Memory Analysis</h4>
        <ul>
          <li><strong>Volatility Framework:</strong> Memory dump analysis</li>
          <li><strong>Process Analysis:</strong> Running processes and DLLs</li>
          <li><strong>Network Connections:</strong> Active network sessions</li>
          <li><strong>Registry Analysis:</strong> In-memory registry data</li>
        </ul>
        
        <h3>Chain of Custody</h3>
        <ul>
          <li><strong>Documentation:</strong> Who, what, when, where, why</li>
          <li><strong>Handling Procedures:</strong> Proper evidence handling</li>
          <li><strong>Storage Requirements:</strong> Secure evidence storage</li>
          <li><strong>Transfer Protocols:</strong> Evidence handoff procedures</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Digital Forensics Lab",
    description: "Hands-on digital forensics and incident response procedures.",
    tasks: [
      {
        category: "System Analysis",
        commands: [
          {
            command: "ps aux --sort=-%cpu | head -10",
            description: "Show top CPU-consuming processes",
            hint: "Identify potentially suspicious processes",
            expectedOutput: "List of processes sorted by CPU usage"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the correct order of the NIST incident response lifecycle phases?",
      options: [
        "Detection, Preparation, Containment, Recovery",
        "Preparation, Detection & Analysis, Containment/Eradication/Recovery, Post-Incident",
        "Analysis, Detection, Response, Recovery",
        "Identification, Containment, Analysis, Documentation"
      ],
      correct: 1,
      explanation: "The NIST incident response lifecycle follows four phases: Preparation, Detection & Analysis, Containment/Eradication/Recovery, and Post-Incident Activity."
    }
  ]
}; 