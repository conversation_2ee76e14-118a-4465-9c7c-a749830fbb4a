/**
 * Backup & Recovery Security Module
 */

export const backupRecoverySecurityContent = {
  id: "os-18",
  pathId: "operating-system-concepts",
  title: "Backup & Recovery Security",
  description: "Master secure backup strategies, disaster recovery, and business continuity planning.",
  objectives: [
    "Understand backup security fundamentals and threat models",
    "Learn about encryption and secure storage for backups",
    "Explore disaster recovery planning and procedures",
    "Analyze backup verification and integrity checking",
    "Master recovery testing and validation processes"
  ],
  difficulty: "Intermediate",
  estimatedTime: 115,
  sections: [
    {
      title: "Backup Security Fundamentals",
      content: `
        <h2>Backup Security Fundamentals</h2>
        <p>Secure backup strategies protect against data loss while maintaining confidentiality and integrity.</p>
        
        <h3>Backup Types and Strategies</h3>
        <ul>
          <li><strong>Full Backup:</strong> Complete data copy</li>
          <li><strong>Incremental Backup:</strong> Changes since last backup</li>
          <li><strong>Differential Backup:</strong> Changes since last full backup</li>
          <li><strong>Continuous Backup:</strong> Real-time data protection</li>
          <li><strong>Snapshot Backup:</strong> Point-in-time copies</li>
        </ul>
        
        <h3>3-2-1 Backup Rule</h3>
        <ul>
          <li><strong>3 Copies:</strong> Original plus two backups</li>
          <li><strong>2 Different Media:</strong> Different storage types</li>
          <li><strong>1 Offsite:</strong> Geographic separation</li>
          <li><strong>Air Gap:</strong> Isolated backup storage</li>
        </ul>
        
        <h3>Backup Security Threats</h3>
        <ul>
          <li><strong>Ransomware:</strong> Encryption of backup data</li>
          <li><strong>Data Theft:</strong> Unauthorized access to backups</li>
          <li><strong>Insider Threats:</strong> Malicious internal actors</li>
          <li><strong>Physical Theft:</strong> Stolen backup media</li>
          <li><strong>Corruption:</strong> Data integrity compromise</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Secure Backup Implementation",
      content: `
        <h2>Secure Backup Implementation</h2>
        <p>Implementing security controls throughout the backup lifecycle.</p>
        
        <h3>Backup Encryption</h3>
        <ul>
          <li><strong>At-Rest Encryption:</strong> Encrypted backup storage</li>
          <li><strong>In-Transit Encryption:</strong> Secure backup transmission</li>
          <li><strong>Key Management:</strong> Secure encryption key handling</li>
          <li><strong>Algorithm Selection:</strong> Strong encryption standards</li>
        </ul>
        
        <h3>Access Controls</h3>
        <ul>
          <li><strong>Authentication:</strong> Multi-factor authentication</li>
          <li><strong>Authorization:</strong> Role-based access control</li>
          <li><strong>Segregation of Duties:</strong> Backup operation separation</li>
          <li><strong>Audit Logging:</strong> Backup activity monitoring</li>
        </ul>
        
        <h3>Disaster Recovery Planning</h3>
        <ul>
          <li><strong>RTO:</strong> Recovery Time Objective</li>
          <li><strong>RPO:</strong> Recovery Point Objective</li>
          <li><strong>Business Impact Analysis:</strong> Critical system identification</li>
          <li><strong>Recovery Procedures:</strong> Step-by-step restoration</li>
          <li><strong>Communication Plans:</strong> Stakeholder notification</li>
        </ul>
        
        <h3>Backup Verification</h3>
        <ul>
          <li><strong>Integrity Checking:</strong> Checksum verification</li>
          <li><strong>Restore Testing:</strong> Regular recovery validation</li>
          <li><strong>Backup Monitoring:</strong> Success/failure tracking</li>
          <li><strong>Performance Metrics:</strong> Backup efficiency measurement</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Backup Security Lab",
    description: "Hands-on secure backup configuration and recovery testing.",
    tasks: [
      {
        category: "Backup Operations",
        commands: [
          {
            command: "tar -czvf backup.tar.gz /home/<USER>/documents",
            description: "Create compressed backup archive",
            hint: "Basic file backup with compression",
            expectedOutput: "Compressed backup file created"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What does the '3-2-1' backup rule specify?",
      options: [
        "3 backups, 2 locations, 1 administrator",
        "3 copies total, 2 different media types, 1 offsite",
        "3 encryption keys, 2 access levels, 1 audit log",
        "3 recovery methods, 2 testing cycles, 1 documentation"
      ],
      correct: 1,
      explanation: "The 3-2-1 rule specifies keeping 3 copies of data (original + 2 backups), on 2 different media types, with 1 copy stored offsite for disaster recovery."
    }
  ]
}; 