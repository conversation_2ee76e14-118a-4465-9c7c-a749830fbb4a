/**
 * I/O Systems & Security Module
 */

export const ioSystemsSecurityContent = {
  id: "os-17",
  pathId: "operating-system-concepts",
  title: "I/O Systems & Security",
  description: "Master input/output systems security, device management, and peripheral security.",
  objectives: [
    "Understand I/O architecture and security implications",
    "Learn about device driver security and vulnerabilities",
    "Explore USB and peripheral device security",
    "Analyze storage device security and encryption",
    "Master I/O monitoring and access control"
  ],
  difficulty: "Advanced",
  estimatedTime: 130,
  sections: [
    {
      title: "I/O Architecture and Security",
      content: `
        <h2>I/O Architecture and Security</h2>
        <p>Input/Output systems present unique security challenges and attack vectors.</p>
        
        <h3>I/O System Components</h3>
        <ul>
          <li><strong>Device Controllers:</strong> Hardware interfaces for devices</li>
          <li><strong>Device Drivers:</strong> Software interfaces to hardware</li>
          <li><strong>I/O Ports:</strong> Communication channels</li>
          <li><strong>Memory-Mapped I/O:</strong> Memory-based device access</li>
          <li><strong>Interrupt Handlers:</strong> Asynchronous event processing</li>
        </ul>
        
        <h3>Device Driver Security</h3>
        <ul>
          <li><strong>Kernel Mode Access:</strong> Privileged execution context</li>
          <li><strong>Driver Signing:</strong> Code signing requirements</li>
          <li><strong>WHQL Certification:</strong> Windows Hardware Quality Labs</li>
          <li><strong>Driver Verification:</strong> Runtime verification tools</li>
          <li><strong>IOMMU Protection:</strong> Hardware-based memory protection</li>
        </ul>
        
        <h3>Common I/O Vulnerabilities</h3>
        <ul>
          <li><strong>Buffer Overflows:</strong> Driver input validation failures</li>
          <li><strong>Race Conditions:</strong> Concurrent access issues</li>
          <li><strong>Privilege Escalation:</strong> Exploiting driver vulnerabilities</li>
          <li><strong>DMA Attacks:</strong> Direct Memory Access exploitation</li>
          <li><strong>Hardware Backdoors:</strong> Malicious hardware components</li>
        </ul>
        
        <h3>USB Security Challenges</h3>
        <ul>
          <li><strong>USB Attacks:</strong> BadUSB, Rubber Ducky, malicious devices</li>
          <li><strong>Device Spoofing:</strong> Fake device identification</li>
          <li><strong>Autorun Exploits:</strong> Automatic execution vulnerabilities</li>
          <li><strong>Power Attacks:</strong> USB power delivery exploitation</li>
          <li><strong>Data Exfiltration:</strong> Unauthorized data copying</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Storage Security and Encryption",
      content: `
        <h2>Storage Security and Encryption</h2>
        <p>Storage devices require comprehensive security measures to protect data at rest.</p>
        
        <h3>Storage Encryption Technologies</h3>
        <h4>Full Disk Encryption (FDE)</h4>
        <ul>
          <li><strong>BitLocker:</strong> Windows native encryption</li>
          <li><strong>FileVault:</strong> macOS disk encryption</li>
          <li><strong>LUKS:</strong> Linux Unified Key Setup</li>
          <li><strong>VeraCrypt:</strong> Cross-platform encryption</li>
        </ul>
        
        <h4>Hardware-Based Encryption</h4>
        <ul>
          <li><strong>Self-Encrypting Drives (SED):</strong> Hardware encryption</li>
          <li><strong>TCG Opal:</strong> Trusted Computing Group standard</li>
          <li><strong>eDrive:</strong> Encrypted drive specification</li>
          <li><strong>Hardware Security Modules:</strong> Dedicated crypto processors</li>
        </ul>
        
        <h3>Storage Security Best Practices</h3>
        <ul>
          <li><strong>Secure Erasure:</strong> Cryptographic and physical wiping</li>
          <li><strong>Key Management:</strong> Secure key storage and rotation</li>
          <li><strong>Access Controls:</strong> User-based storage permissions</li>
          <li><strong>Backup Encryption:</strong> Encrypted backup solutions</li>
          <li><strong>Integrity Verification:</strong> Checksums and digital signatures</li>
        </ul>
        
        <h3>I/O Monitoring and Control</h3>
        <ul>
          <li><strong>Device Control:</strong> USB port management policies</li>
          <li><strong>Data Loss Prevention:</strong> Monitoring data transfers</li>
          <li><strong>Activity Logging:</strong> I/O operation auditing</li>
          <li><strong>Performance Monitoring:</strong> I/O performance metrics</li>
          <li><strong>Anomaly Detection:</strong> Unusual I/O pattern identification</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "I/O Security Lab",
    description: "Hands-on exploration of I/O systems security and device management.",
    tasks: [
      {
        category: "Device Analysis",
        commands: [
          {
            command: "lsusb",
            description: "List USB devices",
            hint: "Identify connected USB devices and their properties",
            expectedOutput: "List of connected USB devices"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary security risk of device drivers?",
      options: [
        "They consume too much memory",
        "They run with kernel-level privileges",
        "They are too slow",
        "They use too much CPU"
      ],
      correct: 1,
      explanation: "Device drivers run with kernel-level privileges, making vulnerabilities in drivers particularly dangerous as they can lead to complete system compromise."
    }
  ]
}; 