/**
 * Process Management & Security Module
 * 
 * This comprehensive module covers process management concepts and their
 * security implications in operating systems.
 */

export const processManagementSecurityContent = {
  id: "os-2",
  pathId: "operating-system-concepts",
  title: "Process Management & Security",
  description: "Master process and thread management concepts, inter-process communication, and their security implications.",
  objectives: [
    "Understand processes, threads, and their lifecycle",
    "Learn about process creation, termination, and context switching",
    "Explore inter-process communication mechanisms and their security risks",
    "Understand process scheduling and its security implications",
    "Analyze process isolation and sandboxing techniques"
  ],
  difficulty: "Intermediate",
  estimatedTime: 120,
  sections: [
    {
      title: "Processes and Threads",
      content: `
        <h2>Processes and Threads</h2>
        <p>A process is a program in execution, consisting of program code, data, and system resources. Understanding processes is fundamental to OS security.</p>
        
        <h3>Process Components</h3>
        <ul>
          <li><strong>Program Code (Text Section):</strong> Executable instructions</li>
          <li><strong>Data Section:</strong> Global and static variables</li>
          <li><strong>Heap:</strong> Dynamic memory allocation</li>
          <li><strong>Stack:</strong> Function calls, local variables, return addresses</li>
          <li><strong>Process Control Block (PCB):</strong> OS-maintained process metadata</li>
        </ul>
        
        <h3>Process Control Block (PCB)</h3>
        <p>The PCB contains critical information about each process:</p>
        <ul>
          <li><strong>Process ID (PID):</strong> Unique identifier</li>
          <li><strong>Process State:</strong> Running, ready, waiting, terminated</li>
          <li><strong>CPU Registers:</strong> Context during context switches</li>
          <li><strong>Memory Management:</strong> Page tables, memory limits</li>
          <li><strong>Security Context:</strong> User ID, group ID, privileges</li>
          <li><strong>Open Files:</strong> File descriptors and handles</li>
        </ul>
        
        <h3>Threads vs Processes</h3>
        <table border="1">
          <tr>
            <th>Aspect</th>
            <th>Process</th>
            <th>Thread</th>
          </tr>
          <tr>
            <td>Memory Space</td>
            <td>Separate address space</td>
            <td>Shared address space</td>
          </tr>
          <tr>
            <td>Creation Cost</td>
            <td>High (full context)</td>
            <td>Low (shared context)</td>
          </tr>
          <tr>
            <td>Communication</td>
            <td>IPC mechanisms</td>
            <td>Shared memory</td>
          </tr>
          <tr>
            <td>Security Isolation</td>
            <td>Strong isolation</td>
            <td>Weaker isolation</td>
          </tr>
          <tr>
            <td>Crash Impact</td>
            <td>Isolated to process</td>
            <td>Affects entire process</td>
          </tr>
        </table>
        
        <h3>Process States</h3>
        <ul>
          <li><strong>New:</strong> Process is being created</li>
          <li><strong>Ready:</strong> Waiting for CPU assignment</li>
          <li><strong>Running:</strong> Instructions are being executed</li>
          <li><strong>Waiting/Blocked:</strong> Waiting for I/O or event</li>
          <li><strong>Terminated:</strong> Process has finished execution</li>
        </ul>
        
        <h3>Security Implications</h3>
        <ul>
          <li><strong>Process Isolation:</strong> Prevents unauthorized access between processes</li>
          <li><strong>Privilege Levels:</strong> Different processes run with different privileges</li>
          <li><strong>Memory Protection:</strong> Hardware and software mechanisms protect process memory</li>
          <li><strong>Resource Limits:</strong> Prevent resource exhaustion attacks</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Process Creation and Termination",
      content: `
        <h2>Process Creation and Termination</h2>
        <p>Understanding how processes are created and terminated is crucial for security analysis and forensics.</p>
        
        <h3>Process Creation Methods</h3>
        <h4>Unix/Linux: fork() and exec()</h4>
        <ul>
          <li><strong>fork():</strong> Creates exact copy of parent process</li>
          <li><strong>exec():</strong> Replaces current process image with new program</li>
          <li><strong>Security:</strong> Inherits parent's privileges initially</li>
          <li><strong>Example:</strong> Shell creating new processes</li>
        </ul>
        
        <h4>Windows: CreateProcess()</h4>
        <ul>
          <li><strong>Direct Creation:</strong> Creates new process directly</li>
          <li><strong>Parameters:</strong> Executable path, command line, security attributes</li>
          <li><strong>Security Context:</strong> Can specify different user context</li>
          <li><strong>Inheritance:</strong> Handle and environment inheritance</li>
        </ul>
        
        <h3>Process Hierarchy</h3>
        <ul>
          <li><strong>Parent-Child Relationship:</strong> Processes form a tree structure</li>
          <li><strong>Process Groups:</strong> Related processes grouped together</li>
          <li><strong>Sessions:</strong> Higher-level grouping for job control</li>
          <li><strong>Security Inheritance:</strong> Child processes inherit security context</li>
        </ul>
        
        <h3>Process Termination</h3>
        <h4>Normal Termination</h4>
        <ul>
          <li><strong>exit() system call:</strong> Voluntary termination</li>
          <li><strong>Return from main():</strong> Implicit exit call</li>
          <li><strong>Cleanup:</strong> Resources freed, file descriptors closed</li>
        </ul>
        
        <h4>Abnormal Termination</h4>
        <ul>
          <li><strong>Signals (Unix/Linux):</strong> SIGKILL, SIGTERM, SIGSEGV</li>
          <li><strong>Exceptions (Windows):</strong> Access violations, stack overflow</li>
          <li><strong>External Termination:</strong> Kill command, Task Manager</li>
        </ul>
        
        <h3>Zombie and Orphan Processes</h3>
        <h4>Zombie Processes</h4>
        <ul>
          <li><strong>Definition:</strong> Terminated but not yet reaped by parent</li>
          <li><strong>Problem:</strong> Consume process table entries</li>
          <li><strong>Security Risk:</strong> Resource exhaustion attack</li>
          <li><strong>Solution:</strong> Parent must call wait() or signal handlers</li>
        </ul>
        
        <h4>Orphan Processes</h4>
        <ul>
          <li><strong>Definition:</strong> Parent terminated before child</li>
          <li><strong>Adoption:</strong> Become children of init process</li>
          <li><strong>Security:</strong> May inherit different security context</li>
        </ul>
        
        <h3>Security Considerations</h3>
        <ul>
          <li><strong>Process Injection:</strong> Malicious code in legitimate processes</li>
          <li><strong>DLL Injection:</strong> Loading malicious libraries</li>
          <li><strong>Process Hollowing:</strong> Replacing process memory</li>
          <li><strong>Privilege Escalation:</strong> Exploiting SUID/SGID bits</li>
          <li><strong>Fork Bombs:</strong> Exponential process creation</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Process Management Lab",
    description: "Hands-on exploration of process creation, monitoring, and security analysis.",
    tasks: [
      {
        category: "Process Information",
        commands: [
          {
            command: "ps aux",
            description: "List all running processes with detailed information",
            hint: "Shows PID, CPU usage, memory usage, and command",
            expectedOutput: "PID %CPU %MEM VSZ RSS TTY STAT START TIME COMMAND"
          },
          {
            command: "pstree",
            description: "Display processes in tree format",
            hint: "Shows parent-child relationships between processes",
            expectedOutput: "systemd─┬─NetworkManager"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the main security advantage of process isolation?",
      options: [
        "Faster execution",
        "Better memory usage",
        "Prevents unauthorized access between processes",
        "Simpler programming"
      ],
      correct: 2,
      explanation: "Process isolation prevents one process from accessing or modifying another process's memory or resources, which is fundamental for system security."
    }
  ]
}; 