/**
 * Operating System Concepts for Cybersecurity Learning Path
 */

// Import all module content
import { osFundamentalsArchitectureContent } from './os-fundamentals-architecture.js';
import { processManagementSecurityContent } from './process-management-security.js';
import { memoryManagementSecurityContent } from './memory-management-security.js';
import { fileSystemSecurityContent } from './file-system-security.js';
import { kernelSecurityContent } from './kernel-security.js';
import { virtualizationSecurityContent } from './virtualization-security.js';
import { windowsSecurityContent } from './windows-security.js';
import { linuxSecurityContent } from './linux-security.js';
import { authenticationAuthorizationContent } from './authentication-authorization.js';
import { loggingAuditingContent } from './logging-auditing.js';
import { malwareAnalysisContent } from './malware-analysis.js';
import { osHardeningContent } from './os-hardening.js';
import { incidentResponseContent } from './incident-response.js';
import { cloudOsSecurityContent } from './cloud-os-security.js';
import { mobileOsSecurityContent } from './mobile-os-security.js';
import { networkSecurityOsContent } from './network-security-os.js';
import { ioSystemsSecurityContent } from './io-systems-security.js';
import { backupRecoverySecurityContent } from './backup-recovery-security.js';
import { performanceMonitoringSecurityContent } from './performance-monitoring-security.js';
import { embeddedIotSecurityContent } from './embedded-iot-security.js';
import { complianceStandardsContent } from './compliance-standards.js';
import { advancedThreatsContent } from './advanced-threats.js';
import { zeroTrustArchitectureContent } from './zero-trust-architecture.js';
import { securityAutomationContent } from './security-automation.js';
import { futureOsSecurityContent } from './future-os-security.js';

export const osLearningPath = {
  id: "operating-system-concepts",
  title: "Operating System Concepts for Cybersecurity: Basics to Advanced",
  description: "Comprehensive learning path covering operating system security from fundamental concepts to advanced threat mitigation, designed for cybersecurity professionals.",
  category: "Operating Systems",
  difficulty: "Beginner to Advanced",
  estimatedTime: "50+ hours",
  prerequisites: [
    "Basic computer literacy",
    "Understanding of networking concepts",
    "Familiarity with command-line interfaces"
  ],
  outcomes: [
    "Master OS security architecture and design principles",
    "Understand major operating system security mechanisms",
    "Analyze and mitigate OS-level security threats",
    "Implement security controls and hardening techniques",
    "Perform OS forensics and incident response",
    "Design secure OS deployments and configurations"
  ],
  modules: [
    osFundamentalsArchitectureContent,
    processManagementSecurityContent,
    memoryManagementSecurityContent,
    fileSystemSecurityContent,
    kernelSecurityContent,
    virtualizationSecurityContent,
    windowsSecurityContent,
    linuxSecurityContent,
    authenticationAuthorizationContent,
    loggingAuditingContent,
    malwareAnalysisContent,
    osHardeningContent,
    incidentResponseContent,
    cloudOsSecurityContent,
    mobileOsSecurityContent,
    networkSecurityOsContent,
    ioSystemsSecurityContent,
    backupRecoverySecurityContent,
    performanceMonitoringSecurityContent,
    embeddedIotSecurityContent,
    complianceStandardsContent,
    advancedThreatsContent,
    zeroTrustArchitectureContent,
    securityAutomationContent,
    futureOsSecurityContent
  ]
};

export const getAllOSModules = () => {
  return osLearningPath.modules;
};

export const getOSModuleById = (id) => {
  return osLearningPath.modules.find(module => module.id === id);
}; 