/**
 * File System Management & Security Module
 */

export const fileSystemSecurityContent = {
  id: "os-4",
  pathId: "operating-system-concepts",
  title: "File System Management & Security",
  description: "Master file system concepts, permissions, and security mechanisms in operating systems.",
  objectives: [
    "Understand file system concepts and implementations",
    "Learn about file permissions and access control mechanisms",
    "Explore file system security features and vulnerabilities", 
    "Analyze file system forensics and integrity monitoring",
    "Understand encryption and secure deletion techniques"
  ],
  difficulty: "Intermediate",
  estimatedTime: 120,
  sections: [
    {
      title: "File System Fundamentals",
      content: `
        <h2>File System Fundamentals</h2>
        <p>File systems provide organized storage and retrieval of data, with critical security implications.</p>
        
        <h3>File System Components</h3>
        <ul>
          <li><strong>Files:</strong> Named collections of data</li>
          <li><strong>Directories:</strong> Containers for files and subdirectories</li>
          <li><strong>Metadata:</strong> Information about files (size, permissions, timestamps)</li>
          <li><strong>File Allocation Table:</strong> Maps file data to storage locations</li>
        </ul>
        
        <h3>Common File Systems</h3>
        <h4>Linux File Systems</h4>
        <ul>
          <li><strong>ext4:</strong> Fourth extended filesystem, journaling</li>
          <li><strong>XFS:</strong> High-performance, 64-bit filesystem</li>
          <li><strong>Btrfs:</strong> B-tree filesystem with snapshots</li>
          <li><strong>ZFS:</strong> Copy-on-write with integrity checking</li>
        </ul>
        
        <h4>Windows File Systems</h4>
        <ul>
          <li><strong>NTFS:</strong> New Technology File System</li>
          <li><strong>FAT32:</strong> File Allocation Table (legacy)</li>
          <li><strong>ReFS:</strong> Resilient File System</li>
        </ul>
        
        <h3>File System Security Features</h3>
        <ul>
          <li><strong>Access Control Lists (ACLs):</strong> Fine-grained permissions</li>
          <li><strong>File Encryption:</strong> Transparent file-level encryption</li>
          <li><strong>Integrity Checking:</strong> Checksums and verification</li>
          <li><strong>Auditing:</strong> Access logging and monitoring</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "File Permissions and Access Control",
      content: `
        <h2>File Permissions and Access Control</h2>
        <p>File permissions are fundamental to OS security, controlling who can access what resources.</p>
        
        <h3>Unix/Linux Permissions</h3>
        <h4>Basic Permissions (rwx)</h4>
        <ul>
          <li><strong>Read (r):</strong> View file contents or list directory</li>
          <li><strong>Write (w):</strong> Modify file or create/delete in directory</li>
          <li><strong>Execute (x):</strong> Run file or access directory</li>
        </ul>
        
        <h4>Permission Groups</h4>
        <ul>
          <li><strong>Owner (u):</strong> File creator/owner</li>
          <li><strong>Group (g):</strong> Users in same group as file</li>
          <li><strong>Others (o):</strong> All other users</li>
        </ul>
        
        <h4>Special Permissions</h4>
        <ul>
          <li><strong>SUID (Set User ID):</strong> Run with owner's privileges</li>
          <li><strong>SGID (Set Group ID):</strong> Run with group's privileges</li>
          <li><strong>Sticky Bit:</strong> Only owner can delete in directory</li>
        </ul>
        
        <h3>Windows Access Control</h3>
        <h4>Access Control Lists (ACLs)</h4>
        <ul>
          <li><strong>Discretionary ACL (DACL):</strong> Who can access object</li>
          <li><strong>System ACL (SACL):</strong> Auditing configuration</li>
          <li><strong>Access Control Entries (ACEs):</strong> Individual permissions</li>
        </ul>
        
        <h4>Windows Permissions</h4>
        <ul>
          <li><strong>Full Control:</strong> All permissions</li>
          <li><strong>Modify:</strong> Read, write, execute, delete</li>
          <li><strong>Read & Execute:</strong> View and run</li>
          <li><strong>Read:</strong> View only</li>
          <li><strong>Write:</strong> Create and modify</li>
        </ul>
        
        <h3>Extended Attributes and ACLs</h3>
        <ul>
          <li><strong>POSIX ACLs:</strong> Extended permissions beyond rwx</li>
          <li><strong>Extended Attributes:</strong> Additional metadata</li>
          <li><strong>Security Labels:</strong> SELinux, AppArmor contexts</li>
          <li><strong>File Capabilities:</strong> Fine-grained privileges</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "File System Security Lab",
    description: "Hands-on exploration of file permissions, ACLs, and file system security features.",
    tasks: [
      {
        category: "File Permissions",
        commands: [
          {
            command: "ls -la /etc/passwd",
            description: "Check permissions on password file",
            hint: "Look at the permission string and ownership",
            expectedOutput: "-rw-r--r-- 1 <USER> <GROUP> 2847 Mar 19 10:00 /etc/passwd"
          },
          {
            command: "find /usr/bin -perm -4000",
            description: "Find files with SUID bit set",
            hint: "SUID files run with owner privileges",
            expectedOutput: "/usr/bin/sudo\n/usr/bin/su"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What does the SUID permission bit do?",
      options: [
        "Makes a file read-only",
        "Allows a program to run with the owner's privileges",
        "Prevents file deletion",
        "Encrypts the file contents"
      ],
      correct: 1,
      explanation: "The SUID (Set User ID) bit allows a program to run with the privileges of the file owner, not the user executing it."
    }
  ]
}; 