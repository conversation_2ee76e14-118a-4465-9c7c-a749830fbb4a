/**
 * Logging & Auditing Systems Module
 */

export const loggingAuditingContent = {
  id: "os-10",
  pathId: "operating-system-concepts",
  title: "Logging & Auditing Systems",
  description: "Master system logging, audit frameworks, and security event monitoring.",
  objectives: [
    "Understand logging fundamentals and log types",
    "Learn about audit frameworks and compliance requirements",
    "Explore log analysis and correlation techniques",
    "Analyze security event monitoring and alerting",
    "Master log management and retention strategies"
  ],
  difficulty: "Intermediate",
  estimatedTime: 120,
  sections: [
    {
      title: "Logging Fundamentals",
      content: `
        <h2>Logging Fundamentals</h2>
        <p>System logging provides crucial visibility into system behavior and security events.</p>
        
        <h3>Types of Logs</h3>
        <ul>
          <li><strong>System Logs:</strong> Kernel messages, system events</li>
          <li><strong>Application Logs:</strong> Software-specific events</li>
          <li><strong>Security Logs:</strong> Authentication, authorization events</li>
          <li><strong>Audit Logs:</strong> Detailed security-relevant events</li>
          <li><strong>Network Logs:</strong> Traffic, connections, firewall events</li>
        </ul>
        
        <h3>Log Formats and Standards</h3>
        <ul>
          <li><strong>Syslog:</strong> RFC 3164 standard logging protocol</li>
          <li><strong>JSON:</strong> Structured logging format</li>
          <li><strong>Common Event Format (CEF):</strong> Security event format</li>
          <li><strong>W3C Extended Log Format:</strong> Web server logs</li>
          <li><strong>Custom Formats:</strong> Application-specific formats</li>
        </ul>
        
        <h3>Log Levels and Severity</h3>
        <ul>
          <li><strong>Emergency (0):</strong> System unusable</li>
          <li><strong>Alert (1):</strong> Action must be taken immediately</li>
          <li><strong>Critical (2):</strong> Critical conditions</li>
          <li><strong>Error (3):</strong> Error conditions</li>
          <li><strong>Warning (4):</strong> Warning conditions</li>
          <li><strong>Notice (5):</strong> Normal but significant</li>
          <li><strong>Info (6):</strong> Informational messages</li>
          <li><strong>Debug (7):</strong> Debug-level messages</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Audit Frameworks and Compliance",
      content: `
        <h2>Audit Frameworks and Compliance</h2>
        <p>Comprehensive auditing ensures accountability and regulatory compliance.</p>
        
        <h3>Linux Audit Framework</h3>
        <ul>
          <li><strong>auditd:</strong> Audit daemon for detailed logging</li>
          <li><strong>Audit Rules:</strong> Define what events to monitor</li>
          <li><strong>System Call Auditing:</strong> Track specific system calls</li>
          <li><strong>File Access Auditing:</strong> Monitor file operations</li>
        </ul>
        
        <h3>Windows Event Logging</h3>
        <ul>
          <li><strong>Event Viewer:</strong> GUI for viewing Windows events</li>
          <li><strong>Event Logs:</strong> System, Security, Application logs</li>
          <li><strong>Event IDs:</strong> Specific identifiers for event types</li>
          <li><strong>Event Forwarding:</strong> Centralized log collection</li>
        </ul>
        
        <h3>Compliance Requirements</h3>
        <ul>
          <li><strong>SOX:</strong> Sarbanes-Oxley financial reporting</li>
          <li><strong>HIPAA:</strong> Healthcare information protection</li>
          <li><strong>PCI DSS:</strong> Payment card industry standards</li>
          <li><strong>GDPR:</strong> General Data Protection Regulation</li>
          <li><strong>ISO 27001:</strong> Information security management</li>
        </ul>
        
        <h3>Log Analysis Tools</h3>
        <ul>
          <li><strong>ELK Stack:</strong> Elasticsearch, Logstash, Kibana</li>
          <li><strong>Splunk:</strong> Commercial log analysis platform</li>
          <li><strong>Graylog:</strong> Open-source log management</li>
          <li><strong>Fluentd:</strong> Data collection and unification</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Log Analysis Lab",
    description: "Hands-on exploration of system logging and audit frameworks.",
    tasks: [
      {
        category: "System Logs",
        commands: [
          {
            command: "journalctl -n 20",
            description: "View last 20 system log entries",
            hint: "systemd journal logs on modern Linux systems",
            expectedOutput: "Recent system log entries"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary purpose of audit logging?",
      options: [
        "System performance monitoring",
        "Application debugging",
        "Security event tracking and compliance",
        "Network troubleshooting"
      ],
      correct: 2,
      explanation: "Audit logging primarily tracks security-relevant events for accountability, forensic analysis, and regulatory compliance requirements."
    }
  ]
}; 