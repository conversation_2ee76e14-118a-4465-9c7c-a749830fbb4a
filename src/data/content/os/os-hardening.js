/**
 * OS Hardening & Best Practices Module
 */

export const osHardeningContent = {
  id: "os-12",
  pathId: "operating-system-concepts",
  title: "OS Hardening & Best Practices",
  description: "Master operating system hardening techniques and security best practices.",
  objectives: [
    "Understand OS hardening principles and methodologies",
    "Learn system configuration security best practices",
    "Explore patch management and vulnerability remediation",
    "Analyze security benchmarks and compliance frameworks",
    "Master ongoing security maintenance and monitoring"
  ],
  difficulty: "Intermediate",
  estimatedTime: 130,
  sections: [
    {
      title: "Hardening Fundamentals",
      content: `
        <h2>OS Hardening Fundamentals</h2>
        <p>System hardening reduces attack surface by eliminating unnecessary services and securing configurations.</p>
        
        <h3>Hardening Principles</h3>
        <ul>
          <li><strong>Principle of Least Privilege:</strong> Minimum necessary access</li>
          <li><strong>Defense in Depth:</strong> Multiple security layers</li>
          <li><strong>Fail Secure:</strong> Default to secure state</li>
          <li><strong>Separation of Duties:</strong> Distribute critical functions</li>
          <li><strong>Regular Updates:</strong> Maintain current security patches</li>
        </ul>
        
        <h3>Attack Surface Reduction</h3>
        <ul>
          <li><strong>Service Minimization:</strong> Disable unnecessary services</li>
          <li><strong>Port Closure:</strong> Close unused network ports</li>
          <li><strong>User Account Management:</strong> Remove default/unused accounts</li>
          <li><strong>Software Removal:</strong> Uninstall unnecessary applications</li>
          <li><strong>Feature Disabling:</strong> Turn off unused OS features</li>
        </ul>
        
        <h3>Configuration Security</h3>
        <h4>Windows Hardening</h4>
        <ul>
          <li><strong>Group Policy:</strong> Centralized security configuration</li>
          <li><strong>Security Templates:</strong> Predefined security settings</li>
          <li><strong>AppLocker:</strong> Application whitelisting</li>
          <li><strong>Windows Firewall:</strong> Host-based firewall configuration</li>
          <li><strong>UAC Settings:</strong> User Account Control optimization</li>
        </ul>
        
        <h4>Linux Hardening</h4>
        <ul>
          <li><strong>SSH Configuration:</strong> Secure remote access</li>
          <li><strong>Sudo Configuration:</strong> Privilege escalation controls</li>
          <li><strong>File Permissions:</strong> Proper access controls</li>
          <li><strong>Kernel Parameters:</strong> Security-focused tuning</li>
          <li><strong>Package Management:</strong> Secure software installation</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Security Benchmarks and Compliance",
      content: `
        <h2>Security Benchmarks and Compliance</h2>
        <p>Industry standards and frameworks provide structured approaches to system hardening.</p>
        
        <h3>CIS Benchmarks</h3>
        <ul>
          <li><strong>Coverage:</strong> Windows, Linux, macOS, network devices</li>
          <li><strong>Levels:</strong> Level 1 (basic) and Level 2 (advanced)</li>
          <li><strong>Sections:</strong> Installation, patches, network, logging, access control</li>
          <li><strong>Scoring:</strong> Automated compliance checking</li>
        </ul>
        
        <h3>NIST Cybersecurity Framework</h3>
        <ul>
          <li><strong>Identify:</strong> Asset management and risk assessment</li>
          <li><strong>Protect:</strong> Safeguards and security controls</li>
          <li><strong>Detect:</strong> Monitoring and detection systems</li>
          <li><strong>Respond:</strong> Incident response procedures</li>
          <li><strong>Recover:</strong> Recovery and restoration processes</li>
        </ul>
        
        <h3>DISA STIGs</h3>
        <ul>
          <li><strong>Purpose:</strong> Department of Defense security requirements</li>
          <li><strong>Categories:</strong> Operating systems, applications, network devices</li>
          <li><strong>Severity Levels:</strong> CAT I (high), CAT II (medium), CAT III (low)</li>
          <li><strong>SCAP Content:</strong> Automated compliance validation</li>
        </ul>
        
        <h3>Patch Management</h3>
        <ul>
          <li><strong>Vulnerability Assessment:</strong> Regular security scanning</li>
          <li><strong>Patch Testing:</strong> Validation in test environments</li>
          <li><strong>Deployment Scheduling:</strong> Planned maintenance windows</li>
          <li><strong>Rollback Procedures:</strong> Recovery from failed patches</li>
          <li><strong>Documentation:</strong> Change tracking and reporting</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "OS Hardening Lab",
    description: "Hands-on system hardening using security benchmarks and best practices.",
    tasks: [
      {
        category: "System Assessment",
        commands: [
          {
            command: "ss -tulpn",
            description: "List all listening ports and services",
            hint: "Identify potential attack surface",
            expectedOutput: "List of listening network services"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary goal of OS hardening?",
      options: [
        "Improve system performance",
        "Reduce attack surface and improve security",
        "Increase system compatibility",
        "Simplify system administration"
      ],
      correct: 1,
      explanation: "OS hardening aims to reduce the attack surface by removing unnecessary services, applying security configurations, and implementing defense-in-depth strategies."
    }
  ]
}; 