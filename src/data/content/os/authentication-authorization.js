/**
 * Authentication & Authorization Systems Module
 */

export const authenticationAuthorizationContent = {
  id: "os-9",
  pathId: "operating-system-concepts",
  title: "Authentication & Authorization Systems",
  description: "Master authentication protocols, authorization models, and identity management systems.", 
  objectives: [
    "Understand authentication vs authorization concepts",
    "Learn about various authentication mechanisms and protocols",
    "Explore authorization models and access control frameworks",
    "Analyze identity management systems and single sign-on",
    "Master multi-factor authentication and privileged access management"
  ],
  difficulty: "Intermediate",
  estimatedTime: 125,
  sections: [
    {
      title: "Authentication Fundamentals",
      content: `
        <h2>Authentication Fundamentals</h2>
        <p>Authentication verifies the identity of users, systems, or processes attempting to access resources.</p>
        
        <h3>Authentication Factors</h3>
        <ul>
          <li><strong>Something You Know:</strong> Passwords, PINs, security questions</li>
          <li><strong>Something You Have:</strong> Tokens, smart cards, mobile devices</li>
          <li><strong>Something You Are:</strong> Biometrics (fingerprints, iris, voice)</li>
          <li><strong>Something You Do:</strong> Behavioral patterns, typing dynamics</li>
          <li><strong>Somewhere You Are:</strong> Location-based authentication</li>
        </ul>
        
        <h3>Multi-Factor Authentication (MFA)</h3>
        <ul>
          <li><strong>Two-Factor Authentication (2FA):</strong> Combining two different factors</li>
          <li><strong>Time-based OTP (TOTP):</strong> Google Authenticator, Authy</li>
          <li><strong>SMS/Voice OTP:</strong> Phone-based verification</li>
          <li><strong>Hardware Tokens:</strong> RSA SecurID, YubiKey</li>
          <li><strong>Push Notifications:</strong> Mobile app approvals</li>
        </ul>
        
        <h3>Authentication Protocols</h3>
        <ul>
          <li><strong>Kerberos:</strong> Network authentication protocol</li>
          <li><strong>LDAP:</strong> Lightweight Directory Access Protocol</li>
          <li><strong>RADIUS:</strong> Remote Authentication Dial-In User Service</li>
          <li><strong>SAML:</strong> Security Assertion Markup Language</li>
          <li><strong>OAuth 2.0:</strong> Authorization framework</li>
          <li><strong>OpenID Connect:</strong> Authentication layer on OAuth 2.0</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Authorization Models and Access Control",
      content: `
        <h2>Authorization Models and Access Control</h2>
        <p>Authorization determines what authenticated users are allowed to do.</p>
        
        <h3>Access Control Models</h3>
        <h4>Discretionary Access Control (DAC)</h4>
        <ul>
          <li><strong>Owner Control:</strong> Resource owners set permissions</li>
          <li><strong>Examples:</strong> Unix file permissions, Windows ACLs</li>
          <li><strong>Advantages:</strong> Flexibility, user control</li>
          <li><strong>Disadvantages:</strong> Potential for privilege creep</li>
        </ul>
        
        <h4>Mandatory Access Control (MAC)</h4>
        <ul>
          <li><strong>System Enforced:</strong> Central authority sets policies</li>
          <li><strong>Examples:</strong> SELinux, classified government systems</li>
          <li><strong>Security Labels:</strong> Clearance levels and classifications</li>
          <li><strong>Bell-LaPadula Model:</strong> No read up, no write down</li>
        </ul>
        
        <h4>Role-Based Access Control (RBAC)</h4>
        <ul>
          <li><strong>Role Assignment:</strong> Users assigned to roles</li>
          <li><strong>Permission Assignment:</strong> Roles have specific permissions</li>
          <li><strong>Separation of Duties:</strong> Prevent conflicts of interest</li>
          <li><strong>Principle of Least Privilege:</strong> Minimum necessary access</li>
        </ul>
        
        <h4>Attribute-Based Access Control (ABAC)</h4>
        <ul>
          <li><strong>Dynamic Policies:</strong> Context-aware access decisions</li>
          <li><strong>Attributes:</strong> User, resource, environment, action</li>
          <li><strong>Policy Languages:</strong> XACML, JSON-based policies</li>
          <li><strong>Fine-Grained Control:</strong> Detailed access rules</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Authentication & Authorization Lab",
    description: "Hands-on exploration of authentication mechanisms and access control systems.",
    tasks: [
      {
        category: "User Management",
        commands: [
          {
            command: "whoami",
            description: "Display current user identity",
            hint: "Shows authenticated user context",
            expectedOutput: "username"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the main difference between authentication and authorization?",
      options: [
        "They are the same thing",
        "Authentication verifies identity, authorization determines permissions",
        "Authorization happens before authentication",
        "Authentication is only for users, authorization is for systems"
      ],
      correct: 1,
      explanation: "Authentication verifies who you are (identity verification), while authorization determines what you're allowed to do (permission verification)."
    }
  ]
}; 