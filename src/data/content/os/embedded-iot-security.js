/**
 * Embedded Systems & IoT Security Module
 */

export const embeddedIotSecurityContent = {
  id: "os-20",
  pathId: "operating-system-concepts",
  title: "Embedded Systems & IoT Security",
  description: "Master security challenges in embedded systems, IoT devices, and real-time operating systems.",
  objectives: [
    "Understand embedded system architecture and security constraints",
    "Learn about IoT device security challenges and solutions",
    "Explore real-time operating system security",
    "Analyze hardware security features and implementations",
    "Master IoT security frameworks and best practices"
  ],
  difficulty: "Advanced",
  estimatedTime: 130,
  sections: [
    {
      title: "Embedded Systems Security",
      content: `
        <h2>Embedded Systems Security</h2>
        <p>Embedded systems present unique security challenges due to resource constraints and specialized requirements.</p>
        
        <h3>Embedded System Characteristics</h3>
        <ul>
          <li><strong>Resource Constraints:</strong> Limited CPU, memory, storage</li>
          <li><strong>Real-Time Requirements:</strong> Deterministic response times</li>
          <li><strong>Single Purpose:</strong> Dedicated functionality</li>
          <li><strong>Long Lifecycle:</strong> Extended deployment periods</li>
          <li><strong>Physical Access:</strong> Potential for hardware attacks</li>
        </ul>
        
        <h3>Security Challenges</h3>
        <ul>
          <li><strong>Limited Resources:</strong> Insufficient capacity for security features</li>
          <li><strong>Update Mechanisms:</strong> Difficulty in patching and updates</li>
          <li><strong>Physical Security:</strong> Tamper resistance requirements</li>
          <li><strong>Power Constraints:</strong> Battery life considerations</li>
          <li><strong>Cost Sensitivity:</strong> Economic pressure on security features</li>
        </ul>
        
        <h3>Real-Time Operating Systems (RTOS)</h3>
        <ul>
          <li><strong>FreeRTOS:</strong> Open-source real-time kernel</li>
          <li><strong>ThreadX:</strong> Commercial RTOS</li>
          <li><strong>VxWorks:</strong> Industry-standard RTOS</li>
          <li><strong>Zephyr:</strong> Linux Foundation RTOS</li>
          <li><strong>Security Features:</strong> Memory protection, secure boot</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "IoT Security Lab",
    description: "Hands-on IoT device security assessment and embedded system analysis.",
    tasks: [
      {
        category: "Device Analysis",
        commands: [
          {
            command: "nmap -sP ***********/24",
            description: "Scan for IoT devices on local network",
            hint: "Discover connected IoT devices",
            expectedOutput: "List of discovered network devices"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary security challenge for embedded systems?",
      options: [
        "High network bandwidth",
        "Resource constraints limiting security implementations",
        "Too many security features",
        "Excessive processing power"
      ],
      correct: 1,
      explanation: "Embedded systems face significant resource constraints (CPU, memory, power) that limit the implementation of comprehensive security features."
    }
  ]
}; 