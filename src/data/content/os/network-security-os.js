/**
 * Network Security from OS Perspective Module
 */

export const networkSecurityOsContent = {
  id: "os-16",
  pathId: "operating-system-concepts",
  title: "Network Security from OS Perspective",
  description: "Master network security implementation and management at the operating system level.",
  objectives: [
    "Understand OS-level network stack security",
    "Learn about firewall configuration and management",
    "Explore network monitoring and intrusion detection",
    "Analyze VPN and secure communication protocols",
    "Master network troubleshooting and forensics"
  ],
  difficulty: "Advanced",
  estimatedTime: 140,
  sections: [
    {
      title: "OS Network Stack Security",
      content: `
        <h2>OS Network Stack Security</h2>
        <p>Operating systems implement multiple layers of network security controls and monitoring capabilities.</p>
        
        <h3>Network Stack Layers</h3>
        <ul>
          <li><strong>Application Layer:</strong> Application-specific protocols and security</li>
          <li><strong>Transport Layer:</strong> TCP/UDP security and port management</li>
          <li><strong>Network Layer:</strong> IP routing and packet filtering</li>
          <li><strong>Data Link Layer:</strong> MAC address filtering and switching</li>
          <li><strong>Physical Layer:</strong> Network interface security</li>
        </ul>
        
        <h3>Network Configuration Security</h3>
        <h4>Windows Network Security</h4>
        <ul>
          <li><strong>Windows Firewall:</strong> Host-based packet filtering</li>
          <li><strong>IPSec:</strong> IP-level encryption and authentication</li>
          <li><strong>Network Profiles:</strong> Domain, private, public configurations</li>
          <li><strong>Advanced Firewall:</strong> Windows Firewall with Advanced Security</li>
        </ul>
        
        <h4>Linux Network Security</h4>
        <ul>
          <li><strong>iptables:</strong> Netfilter-based packet filtering</li>
          <li><strong>firewalld:</strong> Dynamic firewall management</li>
          <li><strong>ufw:</strong> Uncomplicated Firewall interface</li>
          <li><strong>TCP Wrappers:</strong> Host-based access control</li>
        </ul>
        
        <h3>Network Monitoring and Analysis</h3>
        <ul>
          <li><strong>netstat:</strong> Network connection monitoring</li>
          <li><strong>ss:</strong> Socket statistics and connections</li>
          <li><strong>tcpdump:</strong> Command-line packet analyzer</li>
          <li><strong>Wireshark:</strong> GUI-based network protocol analyzer</li>
          <li><strong>nmap:</strong> Network discovery and security auditing</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Firewall Configuration and Management",
      content: `
        <h2>Firewall Configuration and Management</h2>
        <p>Host-based firewalls provide the first line of defense for network security.</p>
        
        <h3>Firewall Rule Design</h3>
        <ul>
          <li><strong>Default Deny:</strong> Block all traffic by default</li>
          <li><strong>Least Privilege:</strong> Allow only necessary traffic</li>
          <li><strong>Rule Ordering:</strong> Process rules from top to bottom</li>
          <li><strong>Logging:</strong> Log blocked and allowed connections</li>
          <li><strong>Regular Review:</strong> Periodic rule assessment</li>
        </ul>
        
        <h3>iptables Configuration</h3>
        <ul>
          <li><strong>Tables:</strong> Filter, NAT, mangle, raw</li>
          <li><strong>Chains:</strong> INPUT, OUTPUT, FORWARD</li>
          <li><strong>Targets:</strong> ACCEPT, DROP, REJECT, LOG</li>
          <li><strong>Matches:</strong> Source, destination, protocol, port</li>
          <li><strong>Extensions:</strong> Additional matching criteria</li>
        </ul>
        
        <h3>Network Security Monitoring</h3>
        <ul>
          <li><strong>Intrusion Detection:</strong> Snort, Suricata, AIDE</li>
          <li><strong>Log Analysis:</strong> Centralized logging and correlation</li>
          <li><strong>Traffic Analysis:</strong> Behavioral anomaly detection</li>
          <li><strong>Threat Intelligence:</strong> IOC feeds and reputation</li>
          <li><strong>Incident Response:</strong> Automated response actions</li>
        </ul>
        
        <h3>VPN and Secure Communications</h3>
        <ul>
          <li><strong>OpenVPN:</strong> Open-source VPN solution</li>
          <li><strong>IPSec:</strong> Internet Protocol Security suite</li>
          <li><strong>WireGuard:</strong> Modern VPN protocol</li>
          <li><strong>SSH Tunneling:</strong> Secure shell port forwarding</li>
          <li><strong>TLS/SSL:</strong> Transport layer security</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Network Security Lab",
    description: "Hands-on network security configuration and monitoring.",
    tasks: [
      {
        category: "Network Analysis",
        commands: [
          {
            command: "netstat -tuln",
            description: "Display listening network ports",
            hint: "Check for open ports and services",
            expectedOutput: "List of listening TCP and UDP ports"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary purpose of a host-based firewall?",
      options: [
        "Improve network performance",
        "Filter network traffic at the host level",
        "Manage network routing",
        "Monitor bandwidth usage"
      ],
      correct: 1,
      explanation: "A host-based firewall filters incoming and outgoing network traffic at the individual host level, providing granular control over network communications."
    }
  ]
}; 