/**
 * Mobile OS Security Module
 */

export const mobileOsSecurityContent = {
  id: "os-15",
  pathId: "operating-system-concepts",
  title: "Mobile OS Security",
  description: "Master mobile operating system security across iOS, Android, and enterprise mobility.",
  objectives: [
    "Understand mobile OS architecture and security models",
    "Learn about mobile-specific threats and attack vectors",
    "Explore mobile device management and security policies",
    "Analyze app security and sandboxing mechanisms",
    "Master mobile forensics and incident response"
  ],
  difficulty: "Intermediate",
  estimatedTime: 125,
  sections: [
    {
      title: "Mobile OS Architecture and Security",
      content: `
        <h2>Mobile OS Architecture and Security</h2>
        <p>Mobile operating systems implement unique security architectures to protect user data and system integrity.</p>
        
        <h3>iOS Security Architecture</h3>
        <ul>
          <li><strong>Secure Boot Chain:</strong> Hardware-based root of trust</li>
          <li><strong>Code Signing:</strong> All code must be signed by Apple</li>
          <li><strong>App Sandboxing:</strong> Strict application isolation</li>
          <li><strong>Data Protection:</strong> Hardware encryption integration</li>
          <li><strong>App Store Review:</strong> Centralized app distribution</li>
        </ul>
        
        <h3>Android Security Model</h3>
        <ul>
          <li><strong>Linux Kernel:</strong> Foundation with security enhancements</li>
          <li><strong>Application Sandbox:</strong> UID-based process isolation</li>
          <li><strong>Permission System:</strong> Runtime and install-time permissions</li>
          <li><strong>SELinux:</strong> Mandatory access control implementation</li>
          <li><strong>Verified Boot:</strong> Boot integrity verification</li>
        </ul>
        
        <h3>Mobile Security Features</h3>
        <h4>Biometric Authentication</h4>
        <ul>
          <li><strong>Fingerprint Recognition:</strong> TouchID, fingerprint sensors</li>
          <li><strong>Facial Recognition:</strong> FaceID, face unlock</li>
          <li><strong>Voice Recognition:</strong> Voice-based authentication</li>
          <li><strong>Behavioral Analytics:</strong> Usage pattern recognition</li>
        </ul>
        
        <h4>Device Encryption</h4>
        <ul>
          <li><strong>Full Disk Encryption:</strong> Complete device data protection</li>
          <li><strong>File-Based Encryption:</strong> Per-file encryption keys</li>
          <li><strong>Hardware Security Module:</strong> Secure Enclave, TEE</li>
          <li><strong>Key Management:</strong> Secure key storage and derivation</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Mobile Threats and Countermeasures",
      content: `
        <h2>Mobile Threats and Countermeasures</h2>
        <p>Understanding the mobile threat landscape and implementing appropriate security controls.</p>
        
        <h3>Mobile Threat Categories</h3>
        <ul>
          <li><strong>Malicious Apps:</strong> Trojans, spyware, adware</li>
          <li><strong>Network Attacks:</strong> Man-in-the-middle, rogue access points</li>
          <li><strong>Physical Attacks:</strong> Device theft, shoulder surfing</li>
          <li><strong>Social Engineering:</strong> Phishing, pretexting</li>
          <li><strong>Supply Chain:</strong> Compromised hardware or software</li>
        </ul>
        
        <h3>Android-Specific Threats</h3>
        <ul>
          <li><strong>Sideloading:</strong> Installing apps from unknown sources</li>
          <li><strong>Rooting:</strong> Gaining administrative access</li>
          <li><strong>Custom ROMs:</strong> Modified operating system versions</li>
          <li><strong>Permission Escalation:</strong> Exploiting privilege flaws</li>
        </ul>
        
        <h3>iOS-Specific Threats</h3>
        <ul>
          <li><strong>Jailbreaking:</strong> Removing Apple's restrictions</li>
          <li><strong>Enterprise Certificates:</strong> Misuse of development certificates</li>
          <li><strong>Profile Installation:</strong> Malicious configuration profiles</li>
          <li><strong>iTunes Backup:</strong> Unencrypted backup vulnerabilities</li>
        </ul>
        
        <h3>Mobile Device Management (MDM)</h3>
        <ul>
          <li><strong>Device Enrollment:</strong> Automatic device registration</li>
          <li><strong>Policy Enforcement:</strong> Security configuration management</li>
          <li><strong>App Management:</strong> Whitelisting and blacklisting</li>
          <li><strong>Remote Wipe:</strong> Data destruction capabilities</li>
          <li><strong>Compliance Monitoring:</strong> Policy adherence tracking</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Mobile Security Analysis Lab",
    description: "Hands-on mobile security assessment and analysis techniques.",
    tasks: [
      {
        category: "Device Analysis",
        commands: [
          {
            command: "adb devices",
            description: "List connected Android devices",
            hint: "Android Debug Bridge for device interaction",
            expectedOutput: "List of connected Android devices"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary difference between iOS and Android app sandboxing?",
      options: [
        "iOS uses containers, Android uses VMs",
        "iOS uses strict sandboxing, Android uses UID-based isolation",
        "Android is more secure than iOS",
        "They use identical sandboxing mechanisms"
      ],
      correct: 1,
      explanation: "iOS implements strict application sandboxing with limited inter-app communication, while Android uses UID-based process isolation with a more flexible permission system."
    }
  ]
}; 