/**
 * OS Fundamentals & Architecture Module
 * 
 * This comprehensive module covers the fundamental concepts of operating system
 * architecture from a cybersecurity perspective.
 */

export const osFundamentalsArchitectureContent = {
  id: "os-1",
  pathId: "operating-system-concepts",
  title: "OS Fundamentals & Architecture",
  description: "Master the fundamental concepts of operating system architecture, types, components, and their security implications.",
  objectives: [
    "Understand what an operating system is and its core responsibilities",
    "Identify different types of operating systems and their use cases",
    "Explain operating system structure and architecture models",
    "Understand system calls and privilege levels from a security perspective",
    "Analyze the boot process and its security implications"
  ],
  difficulty: "Beginner",
  estimatedTime: 90,
  sections: [
    {
      title: "What is an Operating System?",
      content: `
        <h2>What is an Operating System?</h2>
        <p>An operating system (OS) is the most critical software component that manages computer hardware and software resources, providing essential services for computer programs and acting as an intermediary between users and computer hardware.</p>
        
        <h3>Core Responsibilities of an Operating System</h3>
        <ul>
          <li><strong>Process Management:</strong> Controls creation, scheduling, and termination of processes</li>
          <li><strong>Memory Management:</strong> Allocates and deallocates memory space as needed</li>
          <li><strong>File System Management:</strong> Organizes and controls access to files and directories</li>
          <li><strong>I/O Management:</strong> Handles input/output operations with peripheral devices</li>
          <li><strong>Security and Protection:</strong> Enforces access controls and protects system resources</li>
          <li><strong>Network Management:</strong> Manages network communications and protocols</li>
        </ul>
        
        <h3>OS Components Architecture</h3>
        <p>Modern operating systems consist of several key components:</p>
        <ul>
          <li><strong>Kernel:</strong> The core component that directly interfaces with hardware</li>
          <li><strong>Shell:</strong> Command-line interface for user interaction</li>
          <li><strong>System Libraries:</strong> Provide standard functions for applications</li>
          <li><strong>System Utilities:</strong> Essential tools for system administration</li>
          <li><strong>Device Drivers:</strong> Software that controls hardware devices</li>
        </ul>
        
        <h3>Security Implications</h3>
        <p>From a cybersecurity perspective, the OS is the foundation of system security:</p>
        <ul>
          <li>All security controls are ultimately enforced by the OS</li>
          <li>OS vulnerabilities can compromise the entire system</li>
          <li>Proper OS configuration is critical for security</li>
          <li>OS logs provide essential forensic evidence</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Types of Operating Systems",
      content: `
        <h2>Types of Operating Systems</h2>
        <p>Operating systems can be classified based on various criteria. Understanding these types is crucial for cybersecurity professionals as each type has unique security characteristics and vulnerabilities.</p>
        
        <h3>Based on User Support</h3>
        <h4>Single-User Systems</h4>
        <ul>
          <li><strong>Examples:</strong> MS-DOS, early Windows versions</li>
          <li><strong>Characteristics:</strong> Support only one user at a time</li>
          <li><strong>Security Implications:</strong> Limited access controls, simpler attack surface</li>
        </ul>
        
        <h4>Multi-User Systems</h4>
        <ul>
          <li><strong>Examples:</strong> Unix, Linux, Windows Server</li>
          <li><strong>Characteristics:</strong> Support multiple concurrent users</li>
          <li><strong>Security Features:</strong> User isolation, access controls, audit trails</li>
        </ul>
        
        <h3>Based on Tasking</h3>
        <h4>Single-Tasking</h4>
        <ul>
          <li><strong>Examples:</strong> MS-DOS, embedded systems</li>
          <li><strong>Characteristics:</strong> Execute one program at a time</li>
          <li><strong>Security:</strong> Simple but limited isolation</li>
        </ul>
        
        <h4>Multi-Tasking</h4>
        <ul>
          <li><strong>Preemptive:</strong> OS controls task switching (Windows, Linux)</li>
          <li><strong>Cooperative:</strong> Programs voluntarily yield control (older Mac OS)</li>
          <li><strong>Security:</strong> Process isolation, resource protection</li>
        </ul>
        
        <h3>Real-Time Operating Systems (RTOS)</h3>
        <ul>
          <li><strong>Examples:</strong> QNX, VxWorks, FreeRTOS</li>
          <li><strong>Characteristics:</strong> Guaranteed response times</li>
          <li><strong>Applications:</strong> Industrial control, medical devices, automotive</li>
          <li><strong>Security Challenges:</strong> Real-time constraints vs. security checks</li>
        </ul>
        
        <h3>Distributed Operating Systems</h3>
        <ul>
          <li><strong>Examples:</strong> Amoeba, Plan 9</li>
          <li><strong>Characteristics:</strong> Manage multiple networked computers</li>
          <li><strong>Security Concerns:</strong> Network attacks, distributed trust</li>
        </ul>
        
        <h3>Mobile Operating Systems</h3>
        <ul>
          <li><strong>Examples:</strong> Android, iOS, Windows Mobile</li>
          <li><strong>Characteristics:</strong> Optimized for mobile devices</li>
          <li><strong>Security Features:</strong> App sandboxing, permission models</li>
        </ul>
      `,
      type: "text",
      visualization: {
        type: "interactive",
        component: "OSTypesComparison",
        data: {
          types: [
            {
              name: "Desktop OS",
              examples: ["Windows 11", "macOS", "Ubuntu"],
              users: "Single/Multi",
              tasks: "Multi",
              securityFeatures: ["User accounts", "File permissions", "Firewall"]
            },
            {
              name: "Server OS",
              examples: ["Windows Server", "Linux distributions", "Unix"],
              users: "Multi",
              tasks: "Multi",
              securityFeatures: ["Role-based access", "Audit logging", "Network security"]
            },
            {
              name: "Mobile OS",
              examples: ["Android", "iOS"],
              users: "Single",
              tasks: "Multi",
              securityFeatures: ["App sandboxing", "Biometric auth", "Encrypted storage"]
            },
            {
              name: "Real-Time OS",
              examples: ["QNX", "VxWorks"],
              users: "Single/Multi",
              tasks: "Multi",
              securityFeatures: ["Deterministic security", "Hardware isolation"]
            }
          ]
        }
      }
    },
    {
      title: "Operating System Structure",
      content: `
        <h2>Operating System Structure and Architecture</h2>
        <p>The internal structure of an operating system significantly impacts its security, performance, and reliability. Understanding these structures is essential for cybersecurity professionals.</p>
        
        <h3>Monolithic Kernel Architecture</h3>
        <ul>
          <li><strong>Design:</strong> All OS services run in kernel mode</li>
          <li><strong>Examples:</strong> Traditional Unix, Linux</li>
          <li><strong>Advantages:</strong> High performance, efficient communication</li>
          <li><strong>Security Risks:</strong> Large attack surface, single point of failure</li>
          <li><strong>Vulnerability Impact:</strong> Kernel exploits can compromise entire system</li>
        </ul>
        
        <h3>Microkernel Architecture</h3>
        <ul>
          <li><strong>Design:</strong> Minimal kernel, services in user space</li>
          <li><strong>Examples:</strong> QNX, Minix, seL4</li>
          <li><strong>Advantages:</strong> Better isolation, fault tolerance</li>
          <li><strong>Security Benefits:</strong> Smaller trusted computing base (TCB)</li>
          <li><strong>Trade-offs:</strong> Performance overhead from context switching</li>
        </ul>
        
        <h3>Hybrid Kernel Architecture</h3>
        <ul>
          <li><strong>Design:</strong> Combines monolithic and microkernel features</li>
          <li><strong>Examples:</strong> Windows NT, macOS</li>
          <li><strong>Approach:</strong> Critical services in kernel, others in user space</li>
          <li><strong>Security:</strong> Balanced approach to performance and isolation</li>
        </ul>
        
        <h3>Layered Architecture</h3>
        <ul>
          <li><strong>Design:</strong> OS organized in hierarchical layers</li>
          <li><strong>Examples:</strong> THE OS, Venus</li>
          <li><strong>Security Advantage:</strong> Clear separation of concerns</li>
          <li><strong>Verification:</strong> Easier to verify security properties</li>
        </ul>
        
        <h3>Security Implications of Architecture Choice</h3>
        <table border="1">
          <tr>
            <th>Architecture</th>
            <th>Security Strengths</th>
            <th>Security Weaknesses</th>
          </tr>
          <tr>
            <td>Monolithic</td>
            <td>Simple security model</td>
            <td>Large attack surface, privilege escalation risks</td>
          </tr>
          <tr>
            <td>Microkernel</td>
            <td>Strong isolation, minimal TCB</td>
            <td>Complex IPC security, covert channels</td>
          </tr>
          <tr>
            <td>Hybrid</td>
            <td>Balanced security/performance</td>
            <td>Complex security analysis</td>
          </tr>
          <tr>
            <td>Layered</td>
            <td>Clear security boundaries</td>
            <td>Performance penalties</td>
          </tr>
        </table>
      `,
      type: "text",
      visualization: {
        type: "interactive",
        component: "KernelArchitectureComparison",
        data: {
          architectures: [
            {
              name: "Monolithic Kernel",
              description: "All services in kernel space",
              components: ["Device Drivers", "File System", "Network Stack", "Memory Manager"],
              securityLevel: "Medium",
              performance: "High"
            },
            {
              name: "Microkernel",
              description: "Minimal kernel, services in user space",
              components: ["Basic IPC", "Memory Management", "Scheduling"],
              securityLevel: "High",
              performance: "Medium"
            },
            {
              name: "Hybrid Kernel",
              description: "Critical services in kernel",
              components: ["Core Services", "Device Drivers", "Some User Services"],
              securityLevel: "Medium-High",
              performance: "High"
            }
          ]
        }
      }
    },
    {
      title: "System Calls and Privilege Levels",
      content: `
        <h2>System Calls and Privilege Levels</h2>
        <p>System calls are the interface between user programs and the operating system kernel. Understanding this interface is crucial for cybersecurity as it represents a critical attack surface.</p>
        
        <h3>What are System Calls?</h3>
        <ul>
          <li><strong>Definition:</strong> Programming interface to OS services</li>
          <li><strong>Purpose:</strong> Controlled access to kernel functions</li>
          <li><strong>Examples:</strong> open(), read(), write(), fork(), exec()</li>
          <li><strong>Security Role:</strong> Enforcement point for access controls</li>
        </ul>
        
        <h3>System Call Mechanism</h3>
        <ol>
          <li><strong>User Request:</strong> Application calls library function</li>
          <li><strong>Mode Switch:</strong> Transition from user to kernel mode</li>
          <li><strong>Parameter Validation:</strong> Kernel validates parameters</li>
          <li><strong>Service Execution:</strong> Kernel performs requested operation</li>
          <li><strong>Return:</strong> Results returned to user mode</li>
        </ol>
        
        <h3>Privilege Levels and Protection Rings</h3>
        <h4>x86 Protection Rings</h4>
        <ul>
          <li><strong>Ring 0 (Kernel Mode):</strong> Full hardware access, OS kernel</li>
          <li><strong>Ring 1 & 2:</strong> Device drivers (rarely used in modern OS)</li>
          <li><strong>Ring 3 (User Mode):</strong> Application programs, limited access</li>
        </ul>
        
        <h4>ARM Architecture</h4>
        <ul>
          <li><strong>EL0 (User):</strong> Application execution</li>
          <li><strong>EL1 (Kernel):</strong> OS kernel</li>
          <li><strong>EL2 (Hypervisor):</strong> Virtualization</li>
          <li><strong>EL3 (Secure Monitor):</strong> Secure/non-secure worlds</li>
        </ul>
        
        <h3>Security Implications</h3>
        <h4>Attack Vectors</h4>
        <ul>
          <li><strong>System Call Injection:</strong> Malicious parameters</li>
          <li><strong>Race Conditions:</strong> Time-of-check vs. time-of-use</li>
          <li><strong>Buffer Overflows:</strong> In kernel parameter handling</li>
          <li><strong>Privilege Escalation:</strong> Exploiting system call vulnerabilities</li>
        </ul>
        
        <h4>Defense Mechanisms</h4>
        <ul>
          <li><strong>Parameter Validation:</strong> Strict input checking</li>
          <li><strong>Address Space Isolation:</strong> Separate user/kernel spaces</li>
          <li><strong>Stack Canaries:</strong> Detect buffer overflows</li>
          <li><strong>SMEP/SMAP:</strong> Prevent kernel execution of user code</li>
        </ul>
        
        <h3>Common System Call Categories</h3>
        <table border="1">
          <tr>
            <th>Category</th>
            <th>Examples</th>
            <th>Security Considerations</th>
          </tr>
          <tr>
            <td>Process Control</td>
            <td>fork(), exec(), exit(), wait()</td>
            <td>Process isolation, resource limits</td>
          </tr>
          <tr>
            <td>File Management</td>
            <td>open(), read(), write(), close()</td>
            <td>File permissions, path traversal</td>
          </tr>
          <tr>
            <td>Device Management</td>
            <td>ioctl(), read(), write()</td>
            <td>Device access controls</td>
          </tr>
          <tr>
            <td>Information Maintenance</td>
            <td>getpid(), getuid(), time()</td>
            <td>Information disclosure</td>
          </tr>
          <tr>
            <td>Communication</td>
            <td>pipe(), socket(), msgget()</td>
            <td>IPC security, network access</td>
          </tr>
        </table>
      `,
      type: "text",
      visualization: {
        type: "interactive",
        component: "SystemCallVisualization",
        data: {
          rings: [
            { level: 0, name: "Kernel Mode", privileges: "Full hardware access", color: "#ff4757" },
            { level: 1, name: "Device Drivers", privileges: "Limited hardware access", color: "#ffa726" },
            { level: 2, name: "System Services", privileges: "Service-level access", color: "#ffeb3b" },
            { level: 3, name: "User Mode", privileges: "Restricted access", color: "#4caf50" }
          ],
          systemCalls: [
            { name: "open()", description: "Open file", parameters: ["filename", "flags", "mode"] },
            { name: "read()", description: "Read data", parameters: ["fd", "buffer", "count"] },
            { name: "fork()", description: "Create process", parameters: [] },
            { name: "exec()", description: "Execute program", parameters: ["filename", "args"] }
          ]
        }
      }
    },
    {
      title: "Boot Process and Security",
      content: `
        <h2>Boot Process and Security</h2>
        <p>The boot process is the sequence of events that occurs when a computer starts. This process is critical from a security perspective as it establishes the foundation of trust for the entire system.</p>
        
        <h3>Boot Process Stages</h3>
        <h4>1. Power-On Self-Test (POST)</h4>
        <ul>
          <li><strong>Function:</strong> Hardware initialization and testing</li>
          <li><strong>Security Role:</strong> Basic hardware integrity checks</li>
          <li><strong>Vulnerabilities:</strong> Hardware tampering, malicious firmware</li>
        </ul>
        
        <h4>2. BIOS/UEFI Firmware</h4>
        <ul>
          <li><strong>BIOS (Basic Input/Output System):</strong> Legacy firmware interface</li>
          <li><strong>UEFI (Unified Extensible Firmware Interface):</strong> Modern replacement</li>
          <li><strong>Functions:</strong> Hardware initialization, boot device selection</li>
          <li><strong>Security Features:</strong> Secure Boot, TPM integration</li>
        </ul>
        
        <h4>3. Boot Loader</h4>
        <ul>
          <li><strong>Examples:</strong> GRUB (Linux), BOOTMGR (Windows), BootX (macOS)</li>
          <li><strong>Function:</strong> Load and start the operating system kernel</li>
          <li><strong>Security Concerns:</strong> Boot loader tampering, unauthorized OS loading</li>
        </ul>
        
        <h4>4. Kernel Loading and Initialization</h4>
        <ul>
          <li><strong>Process:</strong> Kernel loaded into memory and executed</li>
          <li><strong>Initialization:</strong> Hardware drivers, memory management setup</li>
          <li><strong>Security:</strong> Kernel integrity verification, secure initialization</li>
        </ul>
        
        <h4>5. System Initialization</h4>
        <ul>
          <li><strong>Init Process:</strong> First user-space process (PID 1)</li>
          <li><strong>Service Startup:</strong> System services and daemons</li>
          <li><strong>User Login:</strong> Authentication and session establishment</li>
        </ul>
        
        <h3>Secure Boot Process</h3>
        <h4>UEFI Secure Boot</h4>
        <ul>
          <li><strong>Purpose:</strong> Verify authenticity of boot components</li>
          <li><strong>Mechanism:</strong> Digital signatures and certificate chains</li>
          <li><strong>Components:</strong> Platform Key (PK), Key Exchange Key (KEK), Database (db), Forbidden Database (dbx)</li>
          <li><strong>Protection:</strong> Prevents malicious bootloader execution</li>
        </ul>
        
        <h4>Trusted Platform Module (TPM)</h4>
        <ul>
          <li><strong>Function:</strong> Hardware security module for cryptographic operations</li>
          <li><strong>Boot Security:</strong> Measured boot, attestation</li>
          <li><strong>Features:</strong> Secure key storage, random number generation</li>
        </ul>
        
        <h4>Measured Boot</h4>
        <ul>
          <li><strong>Concept:</strong> Cryptographic measurement of boot components</li>
          <li><strong>Process:</strong> Hash values stored in TPM Platform Configuration Registers (PCRs)</li>
          <li><strong>Verification:</strong> Remote attestation of system integrity</li>
        </ul>
        
        <h3>Boot Security Threats</h3>
        <h4>Bootkits and Rootkits</h4>
        <ul>
          <li><strong>Bootkits:</strong> Malware that infects boot process</li>
          <li><strong>Examples:</strong> TDL4, Alureon, BlackEnergy</li>
          <li><strong>Impact:</strong> Pre-OS execution, difficult detection</li>
        </ul>
        
        <h4>Evil Maid Attacks</h4>
        <ul>
          <li><strong>Scenario:</strong> Physical access to unattended system</li>
          <li><strong>Method:</strong> Boot from malicious media, install bootkit</li>
          <li><strong>Mitigation:</strong> Full disk encryption, secure boot</li>
        </ul>
        
        <h4>BIOS/UEFI Rootkits</h4>
        <ul>
          <li><strong>Examples:</strong> Hacking Team UEFI rootkit, LoJax</li>
          <li><strong>Persistence:</strong> Survives OS reinstallation</li>
          <li><strong>Detection:</strong> Firmware integrity monitoring</li>
        </ul>
        
        <h3>Boot Security Best Practices</h3>
        <ul>
          <li><strong>Enable Secure Boot:</strong> Verify boot component signatures</li>
          <li><strong>Use TPM:</strong> Hardware-based security anchor</li>
          <li><strong>BIOS/UEFI Password:</strong> Protect firmware configuration</li>
          <li><strong>Boot Order Control:</strong> Prevent unauthorized boot devices</li>
          <li><strong>Firmware Updates:</strong> Keep firmware current and signed</li>
          <li><strong>Physical Security:</strong> Protect against physical access</li>
        </ul>
      `,
      type: "text",
      visualization: {
        type: "interactive",
        component: "BootProcessVisualization",
        data: {
          stages: [
            {
              name: "Power On",
              description: "System powers on, POST begins",
              duration: "2-5 seconds",
              securityChecks: ["Hardware integrity", "Firmware verification"]
            },
            {
              name: "BIOS/UEFI",
              description: "Firmware initialization",
              duration: "5-15 seconds",
              securityChecks: ["Secure Boot verification", "TPM initialization"]
            },
            {
              name: "Boot Loader",
              description: "Load operating system",
              duration: "2-10 seconds",
              securityChecks: ["OS signature verification", "Measured boot"]
            },
            {
              name: "Kernel Load",
              description: "OS kernel initialization",
              duration: "5-30 seconds",
              securityChecks: ["Kernel integrity", "Driver verification"]
            },
            {
              name: "System Init",
              description: "Services and user login",
              duration: "10-60 seconds",
              securityChecks: ["Service authentication", "User verification"]
            }
          ],
          threats: [
            { name: "Bootkit", stage: "Boot Loader", impact: "High" },
            { name: "UEFI Rootkit", stage: "BIOS/UEFI", impact: "Critical" },
            { name: "Evil Maid", stage: "Power On", impact: "High" }
          ]
        }
      }
    }
  ],
  practicalLab: {
    title: "OS Architecture Analysis Lab",
    description: "Hands-on exploration of operating system architecture, system calls, and boot process analysis.",
    tasks: [
      {
        category: "System Information Gathering",
        commands: [
          {
            command: "uname -a",
            description: "Display complete system information",
            hint: "This shows kernel version, architecture, and build information",
            expectedOutput: "Linux cyberforce 5.15.0-generic #1 SMP x86_64 GNU/Linux"
          },
          {
            command: "cat /proc/version",
            description: "Show detailed kernel version information",
            hint: "Provides compiler information and build details",
            expectedOutput: "Linux version 5.15.0-generic (gcc version 11.2.0)"
          },
          {
            command: "lscpu",
            description: "Display CPU architecture information",
            hint: "Shows CPU features relevant to security (NX bit, virtualization)",
            expectedOutput: "CPU features: fpu vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca"
          }
        ]
      },
      {
        category: "System Call Tracing",
        commands: [
          {
            command: "strace ls /tmp",
            description: "Trace system calls made by ls command",
            hint: "Shows how user commands interact with the kernel",
            expectedOutput: "openat(AT_FDCWD, \"/tmp\", O_RDONLY|O_NONBLOCK|O_CLOEXEC|O_DIRECTORY)"
          },
          {
            command: "strace -c ls /tmp",
            description: "Count system calls by type",
            hint: "Provides summary of system call usage",
            expectedOutput: "% time     seconds  usecs/call     calls    errors syscall"
          }
        ]
      },
      {
        category: "Boot Process Analysis",
        commands: [
          {
            command: "dmesg | head -20",
            description: "View early boot messages",
            hint: "Shows kernel initialization messages",
            expectedOutput: "[    0.000000] Linux version 5.15.0-generic"
          },
          {
            command: "systemd-analyze",
            description: "Analyze boot performance",
            hint: "Shows boot time breakdown",
            expectedOutput: "Startup finished in 2.345s (kernel) + 8.234s (userspace)"
          },
          {
            command: "systemd-analyze blame",
            description: "Show services by initialization time",
            hint: "Identifies slow-starting services",
            expectedOutput: "2.345s networkd-dispatcher.service"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which OS architecture provides the smallest Trusted Computing Base (TCB)?",
      options: [
        "Monolithic kernel",
        "Microkernel",
        "Hybrid kernel",
        "Layered architecture"
      ],
      correct: 1,
      explanation: "Microkernel architecture has the smallest TCB because it keeps only essential services in kernel mode, moving most services to user space."
    },
    {
      question: "What is the primary security benefit of UEFI Secure Boot?",
      options: [
        "Faster boot times",
        "Better hardware compatibility",
        "Prevention of unauthorized bootloader execution",
        "Improved system performance"
      ],
      correct: 2,
      explanation: "UEFI Secure Boot prevents unauthorized bootloader execution by verifying digital signatures of boot components, protecting against bootkits and unauthorized OS loading."
    },
    {
      question: "In x86 architecture, which protection ring has the highest privilege level?",
      options: [
        "Ring 0",
        "Ring 1",
        "Ring 2",
        "Ring 3"
      ],
      correct: 0,
      explanation: "Ring 0 (kernel mode) has the highest privilege level with full hardware access, while Ring 3 (user mode) has the lowest privilege level."
    },
    {
      question: "What is a major security risk of monolithic kernel architecture?",
      options: [
        "Poor performance",
        "Limited functionality",
        "Large attack surface",
        "Complex user interface"
      ],
      correct: 2,
      explanation: "Monolithic kernels have a large attack surface because all OS services run in kernel mode, meaning a vulnerability in any service can compromise the entire system."
    },
    {
      question: "Which component is responsible for the transition between user mode and kernel mode?",
      options: [
        "Device drivers",
        "System calls",
        "File system",
        "Network stack"
      ],
      correct: 1,
      explanation: "System calls are the mechanism that allows controlled transition from user mode to kernel mode, providing access to OS services while maintaining security boundaries."
    }
  ],
  summary: {
    keyPoints: [
      "Operating systems manage hardware and software resources while enforcing security policies",
      "Different OS architectures (monolithic, microkernel, hybrid) have different security implications",
      "System calls provide controlled access to kernel services and represent critical attack surfaces",
      "The boot process establishes the foundation of system trust and is vulnerable to various attacks",
      "Modern security features like Secure Boot and TPM help protect the boot process",
      "Understanding OS fundamentals is essential for cybersecurity professionals"
    ],
    nextSteps: [
      "Explore process management and security in the next module",
      "Practice system call analysis using tools like strace",
      "Configure Secure Boot on UEFI systems",
      "Study specific OS architectures (Linux, Windows) in detail"
    ]
  }
}; 