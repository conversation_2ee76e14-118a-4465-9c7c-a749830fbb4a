/**
 * OS Compliance & Standards Module
 */

export const complianceStandardsContent = {
  id: "os-21",
  pathId: "operating-system-concepts",
  title: "OS Compliance & Standards",
  description: "Master compliance frameworks, security standards, and regulatory requirements for operating systems.",
  objectives: [
    "Understand major compliance frameworks and their OS requirements",
    "Learn about security standards and certification processes",
    "Explore regulatory compliance implementation",
    "Analyze audit processes and documentation requirements",
    "Master compliance monitoring and reporting"
  ],
  difficulty: "Intermediate",
  estimatedTime: 120,
  sections: [
    {
      title: "Major Compliance Frameworks",
      content: `
        <h2>Major Compliance Frameworks</h2>
        <p>Organizations must comply with various regulatory and industry standards that impact OS security.</p>
        
        <h3>SOX (Sarbanes-Oxley Act)</h3>
        <ul>
          <li><strong>Purpose:</strong> Financial reporting and corporate governance</li>
          <li><strong>OS Requirements:</strong> Access controls, audit logging, change management</li>
          <li><strong>Key Controls:</strong> Segregation of duties, data integrity</li>
          <li><strong>Documentation:</strong> Process documentation and testing</li>
        </ul>
        
        <h3>HIPAA (Health Insurance Portability and Accountability Act)</h3>
        <ul>
          <li><strong>Scope:</strong> Healthcare information protection</li>
          <li><strong>Technical Safeguards:</strong> Access control, encryption, audit controls</li>
          <li><strong>Administrative Safeguards:</strong> Security officer, training, incident response</li>
          <li><strong>Physical Safeguards:</strong> Facility access, workstation controls</li>
        </ul>
        
        <h3>PCI DSS (Payment Card Industry Data Security Standard)</h3>
        <ul>
          <li><strong>Requirements:</strong> 12 security requirements</li>
          <li><strong>Network Security:</strong> Firewalls, secure configurations</li>
          <li><strong>Data Protection:</strong> Encryption, access controls</li>
          <li><strong>Monitoring:</strong> Logging, file integrity monitoring</li>
        </ul>
        
        <h3>GDPR (General Data Protection Regulation)</h3>
        <ul>
          <li><strong>Data Protection by Design:</strong> Built-in privacy controls</li>
          <li><strong>Technical Measures:</strong> Encryption, pseudonymization</li>
          <li><strong>Organizational Measures:</strong> Policies, training, governance</li>
          <li><strong>Rights Management:</strong> Data subject rights implementation</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Security Standards and Certifications",
      content: `
        <h2>Security Standards and Certifications</h2>
        <p>Industry standards provide frameworks for implementing and validating OS security controls.</p>
        
        <h3>ISO 27001/27002</h3>
        <ul>
          <li><strong>Information Security Management:</strong> Systematic approach</li>
          <li><strong>Risk Management:</strong> Risk assessment and treatment</li>
          <li><strong>Control Objectives:</strong> 114 security controls</li>
          <li><strong>Continuous Improvement:</strong> Plan-Do-Check-Act cycle</li>
        </ul>
        
        <h3>NIST Cybersecurity Framework</h3>
        <ul>
          <li><strong>Identify:</strong> Asset management, governance</li>
          <li><strong>Protect:</strong> Access control, data security</li>
          <li><strong>Detect:</strong> Anomaly detection, monitoring</li>
          <li><strong>Respond:</strong> Incident response, communications</li>
          <li><strong>Recover:</strong> Recovery planning, improvements</li>
        </ul>
        
        <h3>Common Criteria (CC)</h3>
        <ul>
          <li><strong>Evaluation Levels:</strong> EAL1 through EAL7</li>
          <li><strong>Security Targets:</strong> Product security claims</li>
          <li><strong>Protection Profiles:</strong> Security requirements templates</li>
          <li><strong>OS Evaluations:</strong> Windows, Linux, Unix evaluations</li>
        </ul>
        
        <h3>FIPS 140-2</h3>
        <ul>
          <li><strong>Cryptographic Modules:</strong> Hardware and software validation</li>
          <li><strong>Security Levels:</strong> Level 1 through Level 4</li>
          <li><strong>Requirements:</strong> Cryptographic algorithms, key management</li>
          <li><strong>Testing:</strong> NIST-approved testing laboratories</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Compliance Assessment Lab",
    description: "Hands-on compliance assessment and audit preparation.",
    tasks: [
      {
        category: "Compliance Check",
        commands: [
          {
            command: "auditctl -l",
            description: "List active audit rules",
            hint: "Check current audit configuration for compliance",
            expectedOutput: "List of audit rules"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which compliance framework specifically addresses healthcare data protection?",
      options: [
        "SOX",
        "PCI DSS",
        "HIPAA",
        "GDPR"
      ],
      correct: 2,
      explanation: "HIPAA (Health Insurance Portability and Accountability Act) specifically addresses the protection of healthcare information and patient privacy."
    }
  ]
}; 