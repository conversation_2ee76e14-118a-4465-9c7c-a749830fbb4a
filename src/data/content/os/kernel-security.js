/**
 * Kernel & System Calls Security Module
 */

export const kernelSecurityContent = {
  id: "os-5",
  pathId: "operating-system-concepts",
  title: "Kernel & System Calls Security",
  description: "Master kernel security concepts, system call vulnerabilities, and kernel hardening techniques.",
  objectives: [
    "Understand kernel architecture and security boundaries",
    "Learn about kernel vulnerabilities and exploitation techniques",
    "Explore system call security and interception mechanisms",
    "Analyze kernel rootkits and detection methods",
    "Understand kernel hardening and protection mechanisms"
  ],
  difficulty: "Advanced",
  estimatedTime: 150,
  sections: [
    {
      title: "Kernel Security Fundamentals",
      content: `
        <h2>Kernel Security Fundamentals</h2>
        <p>The kernel is the most privileged component of the operating system, making its security critical.</p>
        
        <h3>Kernel Security Model</h3>
        <ul>
          <li><strong>Ring 0 Privileges:</strong> Full hardware access</li>
          <li><strong>Trusted Computing Base:</strong> Security-critical components</li>
          <li><strong>Kernel-User Boundary:</strong> Privilege separation</li>
          <li><strong>Attack Surface:</strong> System calls, device drivers, kernel modules</li>
        </ul>
        
        <h3>Common Kernel Vulnerabilities</h3>
        <ul>
          <li><strong>Buffer Overflows:</strong> Stack and heap corruption in kernel space</li>
          <li><strong>Race Conditions:</strong> Time-of-check vs time-of-use bugs</li>
          <li><strong>Null Pointer Dereferences:</strong> Kernel crashes and exploitation</li>
          <li><strong>Integer Overflows:</strong> Arithmetic errors in kernel code</li>
          <li><strong>Use-After-Free:</strong> Accessing freed kernel memory</li>
        </ul>
        
        <h3>Kernel Exploitation Techniques</h3>
        <ul>
          <li><strong>Privilege Escalation:</strong> Gaining root/administrator access</li>
          <li><strong>Kernel ROP:</strong> Return-oriented programming in kernel space</li>
          <li><strong>Ret2usr:</strong> Redirecting kernel execution to user space</li>
          <li><strong>Stack Pivoting:</strong> Changing stack pointer in kernel</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Kernel Security Analysis Lab",
    description: "Hands-on exploration of kernel security mechanisms and vulnerability analysis.",
    tasks: [
      {
        category: "Kernel Information",
        commands: [
          {
            command: "uname -r",
            description: "Display kernel version",
            hint: "Check for known vulnerabilities in this version",
            expectedOutput: "5.15.0-generic"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary security risk of kernel vulnerabilities?",
      options: [
        "Application crashes",
        "Data loss",
        "Complete system compromise",
        "Network connectivity issues"
      ],
      correct: 2,
      explanation: "Kernel vulnerabilities can lead to complete system compromise because the kernel runs with the highest privileges and controls all system resources."
    }
  ]
};
