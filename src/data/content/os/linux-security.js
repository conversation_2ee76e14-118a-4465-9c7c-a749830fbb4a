/**
 * Linux Security Fundamentals Module
 */

export const linuxSecurityContent = {
  id: "os-8",
  pathId: "operating-system-concepts",
  title: "Linux Security Fundamentals", 
  description: "Master Linux-specific security features, hardening techniques, and defensive mechanisms.",
  objectives: [
    "Understand Linux security architecture and access control",
    "Learn about Linux authentication and authorization mechanisms",
    "Explore SELinux and AppArmor mandatory access controls",
    "Analyze Linux vulnerabilities and attack vectors",
    "Master Linux system hardening and monitoring techniques"
  ],
  difficulty: "Intermediate",
  estimatedTime: 135,
  sections: [
    {
      title: "Linux Security Architecture",
      content: `
        <h2>Linux Security Architecture</h2>
        <p>Linux provides a robust multi-layered security model with various access control mechanisms.</p>
        
        <h3>Traditional Unix Security Model</h3>
        <ul>
          <li><strong>User/Group/Other:</strong> Basic permission model</li>
          <li><strong>File Permissions:</strong> Read, write, execute for owner, group, others</li>
          <li><strong>SUID/SGID:</strong> Set user/group ID on execution</li>
          <li><strong>Sticky Bit:</strong> Special directory permissions</li>
        </ul>
        
        <h3>Extended Security Features</h3>
        <ul>
          <li><strong>Access Control Lists (ACLs):</strong> Fine-grained permissions</li>
          <li><strong>Capabilities:</strong> Granular privilege division</li>
          <li><strong>Namespaces:</strong> Process isolation</li>
          <li><strong>Control Groups (cgroups):</strong> Resource management</li>
        </ul>
        
        <h3>Mandatory Access Control Systems</h3>
        <h4>SELinux (Security-Enhanced Linux)</h4>
        <ul>
          <li><strong>Purpose:</strong> Enforce security policies at kernel level</li>
          <li><strong>Modes:</strong> Enforcing, permissive, disabled</li>
          <li><strong>Policies:</strong> Type enforcement, role-based access control</li>
          <li><strong>Labels:</strong> Security contexts for files and processes</li>
        </ul>
        
        <h4>AppArmor</h4>
        <ul>
          <li><strong>Path-based:</strong> Controls based on file paths</li>
          <li><strong>Profiles:</strong> Application-specific security policies</li>
          <li><strong>Modes:</strong> Enforce, complain, unconfined</li>
          <li><strong>Learning Mode:</strong> Automatic profile generation</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Linux Security Tools and Techniques",
      content: `
        <h2>Linux Security Tools and Techniques</h2>
        <p>Essential tools and techniques for securing and monitoring Linux systems.</p>
        
        <h3>System Hardening</h3>
        <ul>
          <li><strong>Package Management:</strong> Keep system updated</li>
          <li><strong>Service Management:</strong> Disable unnecessary services</li>
          <li><strong>Firewall Configuration:</strong> iptables, ufw, firewalld</li>
          <li><strong>SSH Security:</strong> Key-based authentication, configuration</li>
        </ul>
        
        <h3>Monitoring and Logging</h3>
        <ul>
          <li><strong>System Logs:</strong> /var/log directory structure</li>
          <li><strong>Audit Framework:</strong> auditd for detailed logging</li>
          <li><strong>Process Monitoring:</strong> ps, top, htop, pstree</li>
          <li><strong>Network Monitoring:</strong> netstat, ss, tcpdump</li>
        </ul>
        
        <h3>Intrusion Detection</h3>
        <ul>
          <li><strong>AIDE:</strong> Advanced Intrusion Detection Environment</li>
          <li><strong>Tripwire:</strong> File integrity monitoring</li>
          <li><strong>OSSEC:</strong> Host-based intrusion detection</li>
          <li><strong>Fail2ban:</strong> Automated response to attacks</li>
        </ul>
        
        <h3>Vulnerability Assessment</h3>
        <ul>
          <li><strong>Lynis:</strong> Security auditing tool</li>
          <li><strong>OpenVAS:</strong> Vulnerability scanner</li>
          <li><strong>Nessus:</strong> Commercial vulnerability scanner</li>
          <li><strong>CIS Benchmarks:</strong> Security configuration guidelines</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Linux Security Hardening Lab",
    description: "Hands-on exploration of Linux security features and hardening techniques.",
    tasks: [
      {
        category: "System Information",
        commands: [
          {
            command: "uname -a",
            description: "Display system information",
            hint: "Check kernel version and architecture",
            expectedOutput: "Linux hostname 5.15.0-generic #72-Ubuntu SMP"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary difference between SELinux and traditional Unix permissions?",
      options: [
        "SELinux is faster",
        "SELinux provides mandatory access control",
        "SELinux only works on Red Hat systems",
        "SELinux replaces file permissions entirely"
      ],
      correct: 1,
      explanation: "SELinux provides mandatory access control (MAC) that enforces security policies at the kernel level, complementing traditional discretionary access control (DAC) permissions."
    }
  ]
}; 