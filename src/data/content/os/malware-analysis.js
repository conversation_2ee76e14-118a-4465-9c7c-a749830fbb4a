/**
 * Malware Analysis & OS Interaction Module
 */

export const malwareAnalysisContent = {
  id: "os-11",
  pathId: "operating-system-concepts",
  title: "Malware Analysis & OS Interaction",
  description: "Master malware behavior analysis, system interaction patterns, and forensic techniques.",
  objectives: [
    "Understand malware types and classification systems",
    "Learn static and dynamic analysis techniques",
    "Explore malware persistence and evasion methods",
    "Analyze OS-level indicators of compromise",
    "Master sandboxing and containment strategies"
  ],
  difficulty: "Advanced",
  estimatedTime: 145,
  sections: [
    {
      title: "Malware Classification and Behavior",
      content: `
        <h2>Malware Classification and Behavior</h2>
        <p>Understanding different types of malware and their interaction with operating systems.</p>
        
        <h3>Malware Types</h3>
        <ul>
          <li><strong>Viruses:</strong> Self-replicating code attached to files</li>
          <li><strong>Worms:</strong> Self-propagating across networks</li>
          <li><strong>Trojans:</strong> Malicious code disguised as legitimate software</li>
          <li><strong>Rootkits:</strong> Hide presence and maintain persistence</li>
          <li><strong>Ransomware:</strong> Encrypt files for ransom</li>
          <li><strong>Spyware:</strong> Collect and transmit user data</li>
          <li><strong>Adware:</strong> Display unwanted advertisements</li>
          <li><strong>Banking Trojans:</strong> Target financial information</li>
        </ul>
        
        <h3>Malware Behavior Patterns</h3>
        <ul>
          <li><strong>File System Interaction:</strong> File creation, modification, deletion</li>
          <li><strong>Registry Manipulation:</strong> Windows registry changes</li>
          <li><strong>Process Creation:</strong> Spawning child processes</li>
          <li><strong>Network Communication:</strong> C2 server connections</li>
          <li><strong>System Service Abuse:</strong> Legitimate service exploitation</li>
        </ul>
        
        <h3>Persistence Mechanisms</h3>
        <h4>Windows Persistence</h4>
        <ul>
          <li><strong>Registry Run Keys:</strong> Startup execution</li>
          <li><strong>Scheduled Tasks:</strong> Automated execution</li>
          <li><strong>Windows Services:</strong> System service installation</li>
          <li><strong>DLL Hijacking:</strong> Library replacement</li>
          <li><strong>WMI Event Subscriptions:</strong> Event-driven execution</li>
        </ul>
        
        <h4>Linux Persistence</h4>
        <ul>
          <li><strong>Cron Jobs:</strong> Scheduled task execution</li>
          <li><strong>Init Scripts:</strong> System startup scripts</li>
          <li><strong>Library Preloading:</strong> LD_PRELOAD abuse</li>
          <li><strong>SSH Keys:</strong> Authorized key installation</li>
          <li><strong>Kernel Modules:</strong> Rootkit installation</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Analysis Techniques and Tools",
      content: `
        <h2>Analysis Techniques and Tools</h2>
        <p>Comprehensive approaches to analyzing malware behavior and system impact.</p>
        
        <h3>Static Analysis</h3>
        <ul>
          <li><strong>File Hash Analysis:</strong> MD5, SHA1, SHA256 checksums</li>
          <li><strong>String Analysis:</strong> Extract readable strings</li>
          <li><strong>PE Header Analysis:</strong> Windows executable structure</li>
          <li><strong>Disassembly:</strong> IDA Pro, Ghidra, Radare2</li>
          <li><strong>Signature Detection:</strong> YARA rules, antivirus engines</li>
        </ul>
        
        <h3>Dynamic Analysis</h3>
        <ul>
          <li><strong>Sandbox Analysis:</strong> Cuckoo Sandbox, Joe Sandbox</li>
          <li><strong>Process Monitoring:</strong> Process Monitor, Process Explorer</li>
          <li><strong>Network Monitoring:</strong> Wireshark, tcpdump</li>
          <li><strong>System Call Tracing:</strong> strace, ltrace</li>
          <li><strong>Memory Analysis:</strong> Volatility Framework</li>
        </ul>
        
        <h3>Indicators of Compromise (IOCs)</h3>
        <ul>
          <li><strong>File Hashes:</strong> Known malicious file signatures</li>
          <li><strong>Network Indicators:</strong> C2 domains, IP addresses</li>
          <li><strong>Registry Keys:</strong> Suspicious registry modifications</li>
          <li><strong>Behavioral Indicators:</strong> Unusual process activity</li>
          <li><strong>Artifact Timeline:</strong> Sequence of malicious activities</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Malware Analysis Lab",
    description: "Hands-on malware analysis using static and dynamic techniques.",
    tasks: [
      {
        category: "File Analysis",
        commands: [
          {
            command: "file suspicious_binary",
            description: "Analyze file type and properties",
            hint: "Identify file format and characteristics",
            expectedOutput: "File type information"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the main difference between static and dynamic malware analysis?",
      options: [
        "Static is faster than dynamic",
        "Static analyzes code without execution, dynamic analyzes running behavior",
        "Static is more accurate than dynamic",
        "Static only works on Windows malware"
      ],
      correct: 1,
      explanation: "Static analysis examines malware code without executing it, while dynamic analysis observes malware behavior during execution in a controlled environment."
    }
  ]
}; 