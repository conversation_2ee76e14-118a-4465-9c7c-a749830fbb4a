/**
 * Windows Security Fundamentals Module
 */

export const windowsSecurityContent = {
  id: "os-7",
  pathId: "operating-system-concepts", 
  title: "Windows Security Fundamentals",
  description: "Master Windows-specific security features, vulnerabilities, and defensive mechanisms.",
  objectives: [
    "Understand Windows security architecture and access control",
    "Learn about Windows authentication mechanisms",
    "Explore Windows-specific vulnerabilities and attack vectors",
    "Analyze Windows security features and hardening techniques",
    "Understand Windows event logging and monitoring"
  ],
  difficulty: "Intermediate",
  estimatedTime: 130,
  sections: [
    {
      title: "Windows Security Architecture",
      content: `
        <h2>Windows Security Architecture</h2>
        <p>Windows implements a comprehensive security model with multiple layers of protection.</p>
        
        <h3>Security Subsystem Components</h3>
        <ul>
          <li><strong>Local Security Authority (LSA):</strong> Central security component</li>
          <li><strong>Security Account Manager (SAM):</strong> Local account database</li>
          <li><strong>Active Directory:</strong> Domain-based authentication</li>
          <li><strong>Security Reference Monitor:</strong> Access control enforcement</li>
        </ul>
        
        <h3>Windows Access Control Model</h3>
        <ul>
          <li><strong>Security Identifiers (SIDs):</strong> Unique user/group identifiers</li>
          <li><strong>Access Control Lists (ACLs):</strong> Permission definitions</li>
          <li><strong>Discretionary ACLs (DACLs):</strong> User-controlled permissions</li>
          <li><strong>System ACLs (SACLs):</strong> Auditing configuration</li>
        </ul>
        
        <h3>User Account Control (UAC)</h3>
        <ul>
          <li><strong>Purpose:</strong> Prevent unauthorized system changes</li>
          <li><strong>Admin Approval Mode:</strong> Elevated privileges on demand</li>
          <li><strong>Integrity Levels:</strong> Mandatory access control</li>
          <li><strong>Bypass Techniques:</strong> Common UAC bypass methods</li>
        </ul>
        
        <h3>Windows Defender Components</h3>
        <ul>
          <li><strong>Antivirus:</strong> Real-time malware protection</li>
          <li><strong>Firewall:</strong> Network traffic filtering</li>
          <li><strong>SmartScreen:</strong> Web and download protection</li>
          <li><strong>Application Guard:</strong> Isolated browsing</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Windows Vulnerabilities and Attacks",
      content: `
        <h2>Windows Vulnerabilities and Attacks</h2>
        <p>Understanding common Windows attack vectors and exploitation techniques.</p>
        
        <h3>Authentication Attacks</h3>
        <ul>
          <li><strong>Pass-the-Hash:</strong> Using stolen password hashes</li>
          <li><strong>Golden Ticket:</strong> Forged Kerberos tickets</li>
          <li><strong>Silver Ticket:</strong> Service-specific ticket forgery</li>
          <li><strong>DCSync:</strong> Replicating domain controller data</li>
        </ul>
        
        <h3>Privilege Escalation</h3>
        <ul>
          <li><strong>Token Impersonation:</strong> Stealing user tokens</li>
          <li><strong>DLL Hijacking:</strong> Loading malicious libraries</li>
          <li><strong>Service Exploits:</strong> Vulnerable Windows services</li>
          <li><strong>Registry Manipulation:</strong> Persistence and escalation</li>
        </ul>
        
        <h3>Lateral Movement</h3>
        <ul>
          <li><strong>PsExec:</strong> Remote command execution</li>
          <li><strong>WMI:</strong> Windows Management Instrumentation abuse</li>
          <li><strong>PowerShell Remoting:</strong> Remote PowerShell sessions</li>
          <li><strong>RDP:</strong> Remote Desktop Protocol exploitation</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Windows Security Analysis Lab",
    description: "Hands-on exploration of Windows security features and analysis techniques.",
    tasks: [
      {
        category: "System Information",
        commands: [
          {
            command: "systeminfo",
            description: "Display detailed system configuration",
            hint: "Look for OS version, patches, and security features",
            expectedOutput: "Windows version and patch information"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary purpose of User Account Control (UAC)?",
      options: [
        "Password management",
        "Prevent unauthorized system changes",
        "Network access control",
        "File encryption"
      ],
      correct: 1,
      explanation: "UAC prevents unauthorized system changes by requiring explicit approval for administrative actions, reducing the risk of malware making system-level modifications."
    }
  ]
}; 