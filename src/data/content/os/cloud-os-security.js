/**
 * Cloud OS Security Module
 */

export const cloudOsSecurityContent = {
  id: "os-14",
  pathId: "operating-system-concepts",
  title: "Cloud OS Security",
  description: "Master cloud-specific operating system security challenges and solutions.",
  objectives: [
    "Understand cloud computing models and security implications",
    "Learn about hypervisor security in cloud environments",
    "Explore container orchestration and Kubernetes security",
    "Analyze cloud-native security tools and practices",
    "Master multi-tenancy and isolation challenges"
  ],
  difficulty: "Advanced",
  estimatedTime: 135,
  sections: [
    {
      title: "Cloud Computing Security Models",
      content: `
        <h2>Cloud Computing Security Models</h2>
        <p>Understanding security responsibilities across different cloud service models.</p>
        
        <h3>Cloud Service Models</h3>
        <h4>Infrastructure as a Service (IaaS)</h4>
        <ul>
          <li><strong>Provider Responsibility:</strong> Physical security, hypervisor, network</li>
          <li><strong>Customer Responsibility:</strong> OS, applications, data, access management</li>
          <li><strong>Examples:</strong> AWS EC2, Azure VMs, Google Compute Engine</li>
          <li><strong>Security Focus:</strong> VM hardening, network security, data encryption</li>
        </ul>
        
        <h4>Platform as a Service (PaaS)</h4>
        <ul>
          <li><strong>Provider Responsibility:</strong> Infrastructure, OS, runtime environment</li>
          <li><strong>Customer Responsibility:</strong> Applications, data, user access</li>
          <li><strong>Examples:</strong> AWS Lambda, Azure App Service, Google App Engine</li>
          <li><strong>Security Focus:</strong> Application security, data protection, identity management</li>
        </ul>
        
        <h4>Software as a Service (SaaS)</h4>
        <ul>
          <li><strong>Provider Responsibility:</strong> Everything except user data and access</li>
          <li><strong>Customer Responsibility:</strong> Data protection, user access management</li>
          <li><strong>Examples:</strong> Office 365, Salesforce, Google Workspace</li>
          <li><strong>Security Focus:</strong> Data governance, user training, access controls</li>
        </ul>
        
        <h3>Shared Responsibility Model</h3>
        <ul>
          <li><strong>Security OF the Cloud:</strong> Provider's infrastructure security</li>
          <li><strong>Security IN the Cloud:</strong> Customer's data and application security</li>
          <li><strong>Compliance:</strong> Shared compliance requirements</li>
          <li><strong>Incident Response:</strong> Coordinated response procedures</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Container and Kubernetes Security",
      content: `
        <h2>Container and Kubernetes Security</h2>
        <p>Securing containerized applications and orchestration platforms in cloud environments.</p>
        
        <h3>Container Security Challenges</h3>
        <ul>
          <li><strong>Image Vulnerabilities:</strong> Vulnerable base images and dependencies</li>
          <li><strong>Runtime Security:</strong> Container breakout and privilege escalation</li>
          <li><strong>Secrets Management:</strong> Secure handling of credentials and keys</li>
          <li><strong>Network Segmentation:</strong> Container-to-container communication</li>
          <li><strong>Resource Limits:</strong> CPU, memory, and storage constraints</li>
        </ul>
        
        <h3>Kubernetes Security</h3>
        <h4>Control Plane Security</h4>
        <ul>
          <li><strong>API Server:</strong> Authentication, authorization, admission control</li>
          <li><strong>etcd:</strong> Encrypted data storage and secure communication</li>
          <li><strong>Scheduler:</strong> Secure pod placement and node selection</li>
          <li><strong>Controller Manager:</strong> Secure resource management</li>
        </ul>
        
        <h4>Node Security</h4>
        <ul>
          <li><strong>kubelet:</strong> Node agent security and communication</li>
          <li><strong>Container Runtime:</strong> Docker, containerd, CRI-O security</li>
          <li><strong>Network Policies:</strong> Pod-to-pod communication rules</li>
          <li><strong>Pod Security:</strong> Security contexts and capabilities</li>
        </ul>
        
        <h3>Cloud-Native Security Tools</h3>
        <ul>
          <li><strong>Falco:</strong> Runtime security monitoring</li>
          <li><strong>Aqua Security:</strong> Container security platform</li>
          <li><strong>Twistlock:</strong> Cloud native cybersecurity</li>
          <li><strong>Open Policy Agent:</strong> Policy-based control</li>
          <li><strong>Istio:</strong> Service mesh security</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Cloud OS Security Lab",
    description: "Hands-on exploration of cloud operating system security concepts.",
    tasks: [
      {
        category: "Container Security",
        commands: [
          {
            command: "docker ps -a",
            description: "List all containers",
            hint: "Check for running containers and their status",
            expectedOutput: "List of Docker containers"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "In the cloud shared responsibility model, who is responsible for OS security in IaaS?",
      options: [
        "Cloud provider only",
        "Customer only", 
        "Both provider and customer equally",
        "Depends on the specific service"
      ],
      correct: 1,
      explanation: "In IaaS, the customer is responsible for OS security, including patching, configuration, and hardening, while the provider secures the underlying infrastructure."
    }
  ]
}; 