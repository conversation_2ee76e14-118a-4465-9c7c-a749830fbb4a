/**
 * Ethical Hacking Module: Incident Response and Digital Forensics
 * Module ID: eh-21
 */

export const incidentResponseContent = {
  id: "eh-21",
  title: "Incident Response and Digital Forensics",
  description: "Master incident response procedures, digital forensics techniques, and evidence collection for cybersecurity incident investigation and analysis.",
  difficulty: "Advanced",
  estimatedTime: 110,
  objectives: [
    "Understand incident response frameworks and procedures",
    "Master digital forensics tools and techniques",
    "Learn evidence collection and chain of custody procedures",
    "Develop skills in malware analysis for incident response",
    "Apply forensics techniques in breach investigation scenarios"
  ],
  prerequisites: ["eh-1", "eh-16", "eh-19", "eh-20"],
  sections: [
    {
      title: "Incident Response Fundamentals",
      content: `
        <h2>Incident Response Overview</h2>
        <p>Incident response is the systematic approach to handling security breaches, cyber attacks, and other security incidents to minimize damage and recovery time.</p>
        
        <h3>NIST Incident Response Lifecycle</h3>
        <ol>
          <li><strong>Preparation</strong> - Establish IR capabilities and procedures</li>
          <li><strong>Detection and Analysis</strong> - Identify and assess incidents</li>
          <li><strong>Containment, Eradication, and Recovery</strong> - Stop the incident and restore operations</li>
          <li><strong>Post-Incident Activity</strong> - Learn from the incident and improve</li>
        </ol>

        <h3>Incident Classification</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Severity</th>
              <th>Impact</th>
              <th>Response Time</th>
              <th>Examples</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Critical</td>
              <td>Business operations severely impacted</td>
              <td>Immediate (< 1 hour)</td>
              <td>Ransomware, data breach, system compromise</td>
            </tr>
            <tr>
              <td>High</td>
              <td>Significant business impact</td>
              <td>< 4 hours</td>
              <td>Malware infection, unauthorized access</td>
            </tr>
            <tr>
              <td>Medium</td>
              <td>Moderate business impact</td>
              <td>< 24 hours</td>
              <td>Policy violations, suspicious activity</td>
            </tr>
            <tr>
              <td>Low</td>
              <td>Minimal business impact</td>
              <td>< 72 hours</td>
              <td>Failed login attempts, minor policy violations</td>
            </tr>
          </tbody>
        </table>

        <h3>Evidence Types</h3>
        <ul>
          <li><strong>Volatile Evidence</strong> - Memory, network connections, running processes</li>
          <li><strong>Non-Volatile Evidence</strong> - Hard drives, log files, registry</li>
          <li><strong>Network Evidence</strong> - Packet captures, flow records, DNS logs</li>
          <li><strong>Application Evidence</strong> - Application logs, database records</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Digital Forensics Techniques",
      content: `
        <h2>Digital Evidence Collection and Analysis</h2>
        <p>Digital forensics involves the identification, preservation, analysis, and presentation of digital evidence in a legally admissible manner.</p>

        <h3>Memory Forensics</h3>
        <h4>Memory Acquisition</h4>
        <pre><code># Windows memory acquisition
winpmem_mini_x64_rc2.exe physmem.raw
DumpIt.exe /output memory.dmp

# Linux memory acquisition
lime-forensics
insmod lime.ko "path=/tmp/memory.lime format=lime"

# Memory analysis with Volatility
volatility -f memory.dmp --profile=Win10x64_19041 imageinfo
volatility -f memory.dmp --profile=Win10x64_19041 pslist
volatility -f memory.dmp --profile=Win10x64_19041 pstree
volatility -f memory.dmp --profile=Win10x64_19041 netscan
volatility -f memory.dmp --profile=Win10x64_19041 malfind</code></pre>

        <h3>Disk Forensics</h3>
        <h4>Disk Imaging</h4>
        <pre><code># Create forensic image
dd if=/dev/sda of=evidence.img bs=512 conv=noerror,sync
dcfldd if=/dev/sda of=evidence.img hash=md5,sha256

# Verify image integrity
md5sum evidence.img
sha256sum evidence.img

# Mount forensic image
mount -o ro,loop,noexec evidence.img /mnt/evidence

# Autopsy forensic analysis
autopsy &
# Create new case and add evidence image</code></pre>

        <h3>Network Forensics</h3>
        <h4>Packet Analysis</h4>
        <pre><code># Capture network traffic
tcpdump -i eth0 -w capture.pcap
tshark -i eth0 -w capture.pcapng

# Wireshark analysis
wireshark capture.pcap
# Filter examples:
# http.request.method == "POST"
# dns.qry.name contains "malicious"
# tcp.flags.syn == 1 and tcp.flags.ack == 0

# Network flow analysis
nfcapd -w -D -p 9995 -B 200000 -l /var/cache/nfcapd
nfdump -R /var/cache/nfcapd -s record/bytes</code></pre>

        <h3>Log Analysis</h3>
        <h4>Windows Event Logs</h4>
        <pre><code># Event log analysis
Get-WinEvent -LogName Security | Where-Object {$_.Id -eq 4624}
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4625; StartTime=(Get-Date).AddDays(-1)}

# PowerShell log analysis
Get-WinEvent -LogName "Microsoft-Windows-PowerShell/Operational" | Where-Object {$_.Id -eq 4104}

# Sysmon log analysis
Get-WinEvent -LogName "Microsoft-Windows-Sysmon/Operational" | Where-Object {$_.Id -eq 1}</code></pre>

        <h4>Linux Log Analysis</h4>
        <pre><code># System log analysis
grep "Failed password" /var/log/auth.log
grep "sudo" /var/log/auth.log
grep "COMMAND" /var/log/auth.log

# Web server log analysis
grep "POST" /var/log/apache2/access.log
awk '{print $1}' /var/log/apache2/access.log | sort | uniq -c | sort -nr

# Log correlation with ELK Stack
# Elasticsearch, Logstash, Kibana
# Centralized log analysis and visualization</code></pre>
      `,
      type: "text"
    },
    {
      title: "Malware Analysis for IR",
      content: `
        <h2>Incident-Focused Malware Analysis</h2>
        <p>During incident response, malware analysis focuses on quickly understanding the threat, its capabilities, and impact on the organization.</p>

        <h3>Rapid Malware Triage</h3>
        <h4>Initial Assessment</h4>
        <pre><code># File hash analysis
md5sum malware.exe
sha1sum malware.exe
sha256sum malware.exe

# VirusTotal lookup
curl -X POST 'https://www.virustotal.com/vtapi/v2/file/report' \
  --form apikey='API_KEY' \
  --form resource='FILE_HASH'

# Basic file analysis
file malware.exe
strings malware.exe | head -50
hexdump -C malware.exe | head -20</code></pre>

        <h3>Dynamic Analysis</h3>
        <h4>Sandbox Analysis</h4>
        <pre><code># Cuckoo Sandbox
cuckoo submit malware.exe
cuckoo web

# Process monitoring
procmon.exe  # Windows
strace ./malware  # Linux

# Network monitoring
wireshark &
./malware
# Analyze network connections and traffic</code></pre>

        <h3>IOC Extraction</h3>
        <h4>Indicators of Compromise</h4>
        <pre><code># File-based IOCs
# File hashes (MD5, SHA1, SHA256)
# File names and paths
# File sizes and timestamps

# Network IOCs
# IP addresses
# Domain names
# URLs
# User agents

# Registry IOCs (Windows)
# Registry keys and values
# Persistence mechanisms

# Behavioral IOCs
# Process names
# Command line arguments
# Network connections</code></pre>

        <h3>Threat Intelligence Integration</h3>
        <h4>IOC Enrichment</h4>
        <pre><code># MISP (Malware Information Sharing Platform)
# Share and receive threat intelligence
# IOC correlation and attribution

# STIX/TAXII
# Structured threat information exchange
# Automated threat intelligence sharing

# Commercial threat feeds
# Integration with security tools
# Real-time threat updates</code></pre>
      `,
      type: "text"
    },
    {
      title: "Incident Investigation",
      content: `
        <h2>Comprehensive Incident Investigation</h2>
        <p>Incident investigation involves systematic analysis of evidence to understand the attack timeline, scope, and impact.</p>

        <h3>Timeline Analysis</h3>
        <h4>Event Correlation</h4>
        <pre><code># Timeline creation tools
log2timeline.py --parsers=win7 -z UTC image.dd
psort.py -z UTC -o l2tcsv timeline.plaso timeline.csv

# Manual timeline analysis
# Correlate events across multiple sources
# Identify attack progression
# Determine dwell time

# Timeline visualization
# Use tools like Timesketch
# Create visual timeline of events
# Identify patterns and anomalies</code></pre>

        <h3>Attribution Analysis</h3>
        <h4>Threat Actor Identification</h4>
        <pre><code># TTP analysis
# Compare with known threat actors
# MITRE ATT&CK mapping
# Tool and technique correlation

# Infrastructure analysis
# Domain registration data
# IP geolocation
# Certificate analysis

# Code analysis
# Malware family identification
# Code reuse patterns
# Compilation timestamps</code></pre>

        <h3>Impact Assessment</h3>
        <h4>Damage Evaluation</h4>
        <pre><code># Data impact assessment
# Identify accessed/stolen data
# Classify data sensitivity
# Assess regulatory implications

# System impact assessment
# Compromised systems inventory
# Service availability impact
# Recovery time estimation

# Business impact assessment
# Financial impact calculation
# Reputation damage assessment
# Regulatory compliance impact</code></pre>

        <h3>Evidence Preservation</h3>
        <h4>Chain of Custody</h4>
        <pre><code># Evidence documentation
# Who collected the evidence
# When and where it was collected
# How it was collected and stored
# Chain of custody log

# Evidence integrity
# Hash verification
# Digital signatures
# Tamper-evident storage

# Legal considerations
# Admissibility requirements
# Privacy regulations
# Cross-border data transfer</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Ransomware Incident Investigation",
    description: "Conduct comprehensive incident response and forensic investigation of a ransomware attack including evidence collection, malware analysis, and timeline reconstruction.",
    environment: "Compromised enterprise network with ransomware infection, encrypted files, and various evidence sources",
    tasks: [
      {
        category: "Initial Response",
        tasks: [
          {
            task: "Perform initial incident triage and containment",
            method: "Network isolation, system imaging, and evidence preservation",
            expectedFindings: "Contained incident with preserved evidence",
            points: 20
          }
        ]
      },
      {
        category: "Evidence Collection",
        tasks: [
          {
            task: "Collect and analyze memory dumps from infected systems",
            method: "Memory acquisition and Volatility analysis",
            expectedFindings: "Running processes, network connections, and malware artifacts",
            points: 25
          },
          {
            task: "Perform disk forensics on compromised systems",
            method: "Disk imaging and file system analysis",
            expectedFindings: "File modifications, deleted files, and persistence mechanisms",
            points: 20
          }
        ]
      },
      {
        category: "Malware Analysis",
        tasks: [
          {
            task: "Analyze ransomware sample and extract IOCs",
            method: "Static and dynamic malware analysis",
            expectedFindings: "Malware capabilities, C2 infrastructure, and IOCs",
            points: 20
          }
        ]
      },
      {
        category: "Timeline Reconstruction",
        tasks: [
          {
            task: "Create comprehensive attack timeline",
            method: "Log correlation and timeline analysis",
            expectedFindings: "Complete attack progression from initial access to encryption",
            points: 15
          }
        ]
      }
    ],
    deliverables: [
      "Incident response report with timeline and impact assessment",
      "Digital forensics evidence documentation",
      "Malware analysis report with IOCs and TTPs",
      "Chain of custody documentation",
      "Lessons learned and improvement recommendations",
      "Executive summary for management"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which type of evidence is most volatile and should be collected first?",
        options: [
          "Hard drive contents",
          "System memory",
          "Log files",
          "Registry entries"
        ],
        correct: 1,
        explanation: "System memory is the most volatile evidence as it is lost when the system is powered off or rebooted."
      },
      {
        question: "What is the primary purpose of maintaining chain of custody?",
        options: [
          "Improve analysis efficiency",
          "Ensure evidence admissibility",
          "Reduce storage costs",
          "Speed up investigation"
        ],
        correct: 1,
        explanation: "Chain of custody ensures evidence integrity and admissibility in legal proceedings by documenting who handled the evidence and when."
      },
      {
        question: "Which tool is commonly used for memory forensics analysis?",
        options: [
          "Wireshark",
          "Autopsy",
          "Volatility",
          "Nmap"
        ],
        correct: 2,
        explanation: "Volatility is the most widely used framework for memory forensics analysis and investigation."
      }
    ],
    practicalTasks: [
      {
        task: "Perform memory forensics analysis and extract running processes and network connections",
        points: 25
      },
      {
        task: "Conduct disk forensics investigation and recover deleted files",
        points: 25
      },
      {
        task: "Analyze malware sample and extract indicators of compromise",
        points: 25
      },
      {
        task: "Create comprehensive incident timeline using multiple evidence sources",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "NIST Computer Security Incident Handling Guide",
      url: "https://csrc.nist.gov/publications/detail/sp/800-61/rev-2/final",
      type: "guide"
    },
    {
      title: "Volatility Framework Documentation",
      url: "https://github.com/volatilityfoundation/volatility",
      type: "tool"
    },
    {
      title: "SANS Digital Forensics Resources",
      url: "https://www.sans.org/digital-forensics-incident-response/",
      type: "training"
    }
  ],
  tags: ["incident-response", "digital-forensics", "malware-analysis", "evidence-collection", "timeline-analysis"],
  lastUpdated: "2024-01-15"
};
