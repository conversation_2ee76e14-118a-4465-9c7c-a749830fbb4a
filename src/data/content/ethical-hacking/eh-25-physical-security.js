/**
 * Ethical Hacking Module: Physical Security Testing
 * Module ID: eh-25
 */

export const physicalSecurityContent = {
  id: "eh-25",
  title: "Physical Security Testing",
  description: "Master physical security assessment techniques including facility penetration, lock picking, RFID/badge cloning, and social engineering for comprehensive security testing.",
  difficulty: "Advanced",
  estimatedTime: 90,
  objectives: [
    "Understand physical security principles and attack vectors",
    "Master lock picking and bypass techniques",
    "Learn RFID and access control system testing",
    "Develop skills in physical social engineering",
    "Apply physical security testing in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-11", "eh-20"],
  sections: [
    {
      title: "Physical Security Fundamentals",
      content: `
        <h2>Physical Security Overview</h2>
        <p>Physical security protects people, property, and information through physical measures and controls to prevent unauthorized access to facilities and assets.</p>
        
        <h3>Physical Security Layers</h3>
        <ol>
          <li><strong>Perimeter Security</strong> - Fencing, barriers, lighting</li>
          <li><strong>Building Security</strong> - Access controls, surveillance</li>
          <li><strong>Room/Area Security</strong> - Restricted access zones</li>
          <li><strong>Asset Security</strong> - Equipment protection, cable locks</li>
        </ol>

        <h3>Physical Attack Vectors</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Attack Type</th>
              <th>Target</th>
              <th>Techniques</th>
              <th>Impact</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Lock Bypass</td>
              <td>Doors, cabinets, safes</td>
              <td>Picking, bumping, impressioning</td>
              <td>Unauthorized access</td>
            </tr>
            <tr>
              <td>Access Control Bypass</td>
              <td>Card readers, biometrics</td>
              <td>Cloning, spoofing, tailgating</td>
              <td>Facility infiltration</td>
            </tr>
            <tr>
              <td>Social Engineering</td>
              <td>Personnel</td>
              <td>Pretexting, impersonation</td>
              <td>Information disclosure</td>
            </tr>
            <tr>
              <td>Surveillance Evasion</td>
              <td>CCTV, sensors</td>
              <td>Blind spots, jamming</td>
              <td>Undetected access</td>
            </tr>
            <tr>
              <td>Device Attacks</td>
              <td>Computers, servers</td>
              <td>USB drops, hardware implants</td>
              <td>System compromise</td>
            </tr>
          </tbody>
        </table>

        <h3>Legal and Ethical Considerations</h3>
        <div class="alert alert-danger">
          <strong>Critical Warning:</strong> Physical security testing must only be performed with explicit written authorization. Unauthorized entry, lock picking, and device tampering may constitute criminal activities. Always ensure proper legal agreements and scope definition.
        </div>
      `,
      type: "text"
    },
    {
      title: "Lock Picking and Bypass",
      content: `
        <h2>Mechanical Security Bypass</h2>
        <p>Lock picking and bypass techniques target mechanical security devices to gain unauthorized access to secured areas.</p>

        <h3>Lock Types and Vulnerabilities</h3>
        <h4>Pin Tumbler Locks</h4>
        <pre><code># Pin tumbler lock components:
- Key pins (bottom pins)
- Driver pins (top pins)
- Springs
- Plug (cylinder)
- Shell (housing)

# Picking technique:
1. Insert tension wrench
2. Apply slight rotational pressure
3. Use pick to lift pins to shear line
4. Set pins one by one
5. Rotate plug when all pins set

# Common vulnerabilities:
- Standard pin tumblers
- Worn or damaged pins
- Poor manufacturing tolerances
- Inadequate security pins</code></pre>

        <h4>Lock Picking Tools</h4>
        <pre><code># Essential lock picking tools:

Tension Wrenches:
- Top of keyway (TOK)
- Bottom of keyway (BOK)
- Various sizes and angles

Picks:
- Hook picks (short, medium, long)
- Rake picks (snake, bogota, city)
- Ball picks
- Diamond picks

Specialized Tools:
- Bump keys
- Snap guns
- Electric picks
- Decoder tools

# Tool usage guidelines:
- Start with appropriate tension
- Use lightest touch possible
- Listen for pin setting clicks
- Feel for binding pins
- Practice on training locks</code></pre>

        <h3>Advanced Bypass Techniques</h3>
        <h4>Lock Bumping</h4>
        <pre><code># Bump key technique:
1. Cut key to maximum depth (999...)
2. Insert bump key fully
3. Pull back one position
4. Apply rotational tension
5. Strike key with bump hammer
6. Attempt to turn

# Bump key creation:
- Use key blank for target lock
- Cut all positions to maximum depth
- File tip for easier insertion
- Test on practice locks

# Countermeasures:
- Bump-resistant locks
- Security pins
- Electronic locks
- Additional security layers</code></pre>

        <h4>Impressioning</h4>
        <pre><code># Impressioning process:
1. Insert blank key
2. Apply tension and wiggle
3. Remove and examine marks
4. File marked areas slightly
5. Repeat until key works

# Tools needed:
- Key blanks
- Files (various cuts)
- Tension wrench
- Good lighting
- Magnification

# Advantages:
- Creates working key
- No evidence of tampering
- Works on high-security locks
- Bypasses some countermeasures</code></pre>

        <h3>Electronic Lock Bypass</h3>
        <h4>Keypad Locks</h4>
        <pre><code># Attack techniques:

Thermal imaging:
- Use thermal camera after legitimate use
- Identify recently pressed keys
- Reduce combination possibilities

Shoulder surfing:
- Observe code entry
- Use binoculars or cameras
- Note finger positions

Brute force:
- Automated code testing
- Common code patterns
- Default manufacturer codes

Side-channel attacks:
- Power analysis
- Electromagnetic emanations
- Acoustic analysis

# Common vulnerabilities:
- Default codes not changed
- Weak code policies
- No lockout mechanisms
- Poor key management</code></pre>

        <h4>Magnetic Locks</h4>
        <pre><code># Magnetic lock bypass:

Power interruption:
- Cut power supply
- Use UPS bypass
- Emergency release activation

Mechanical bypass:
- Door frame manipulation
- Shim insertion
- Force application

Electronic bypass:
- Control signal manipulation
- Sensor spoofing
- Wiring attacks

# Countermeasures:
- Battery backup systems
- Fail-secure configuration
- Tamper detection
- Redundant locking mechanisms</code></pre>
      `,
      type: "text"
    },
    {
      title: "Access Control System Testing",
      content: `
        <h2>Electronic Access Control Assessment</h2>
        <p>Modern access control systems use various technologies including RFID, magnetic stripe, and biometric systems that present unique attack vectors.</p>

        <h3>RFID and Badge Cloning</h3>
        <h4>RFID Technology Overview</h4>
        <pre><code># RFID Frequencies:
- Low Frequency (LF): 125-134 kHz
- High Frequency (HF): 13.56 MHz
- Ultra High Frequency (UHF): 860-960 MHz

# Common RFID Standards:
- EM4100/EM4102 (LF)
- HID Prox (LF)
- MIFARE Classic (HF)
- MIFARE DESFire (HF)
- ISO14443 (HF)
- EPC Gen2 (UHF)

# RFID Components:
- Tag/Card: Contains chip and antenna
- Reader: Energizes tag and reads data
- Backend: Processes authentication</code></pre>

        <h4>RFID Cloning Techniques</h4>
        <pre><code># Proxmark3 RFID tool usage:

# LF card cloning (HID Prox):
proxmark3> lf hid read
proxmark3> lf hid clone --raw 2006ec0c86

# HF card analysis (MIFARE):
proxmark3> hf mf autopwn
proxmark3> hf mf dump
proxmark3> hf mf restore

# Card emulation:
proxmark3> lf hid sim --raw 2006ec0c86
proxmark3> hf mf sim --uid 12345678

# Tools for RFID testing:
- Proxmark3 (most versatile)
- Chameleon Mini (portable)
- RFID Cloner (simple cloning)
- HackRF (SDR analysis)
- FlipperZero (multi-tool)</code></pre>

        <h3>Biometric System Testing</h3>
        <h4>Fingerprint Bypass</h4>
        <pre><code># Fingerprint attack methods:

Gummy bear attack:
1. Obtain fingerprint (glass, surface)
2. Create mold using gelatin
3. Press gummy finger on scanner
4. May fool optical scanners

Photo attack:
1. High-resolution fingerprint photo
2. Print on transparency
3. Place over real finger
4. Works on some optical scanners

Silicone molding:
1. Create detailed fingerprint mold
2. Use silicone to create fake finger
3. Add conductive material if needed
4. Test on various scanner types

# Countermeasures:
- Liveness detection
- Multi-factor authentication
- Capacitive sensors
- Temperature detection
- Pulse detection</code></pre>

        <h4>Facial Recognition Bypass</h4>
        <pre><code># Facial recognition attacks:

Photo attack:
- High-quality printed photo
- Tablet/phone display
- 3D printed face model

Video replay:
- Recorded video of authorized user
- Deepfake technology
- Real-time video manipulation

Infrared spoofing:
- IR LED arrays
- Heat signature manipulation
- Thermal imaging bypass

# Advanced techniques:
- 3D face models
- Makeup and prosthetics
- Adversarial patterns
- Presentation attacks

# Detection methods:
- Liveness detection
- 3D depth sensing
- Infrared analysis
- Challenge-response
- Multi-modal biometrics</code></pre>

        <h3>Physical Social Engineering</h3>
        <h4>Tailgating and Piggybacking</h4>
        <pre><code># Tailgating techniques:

Close following:
- Follow authorized person closely
- Act like you belong
- Engage in conversation
- Carry appropriate props

Distraction methods:
- Carry large items
- Pretend to be on phone
- Ask for directions
- Create urgency

Authority impersonation:
- Dress as maintenance worker
- Carry official-looking ID
- Use company terminology
- Display confidence

# Countermeasures:
- Mantrap doors
- Security awareness training
- Visitor management systems
- Security guards
- CCTV monitoring
- Access logging</code></pre>

        <h4>Pretexting Scenarios</h4>
        <pre><code># Common pretexts:

IT Support:
"I'm here to fix the network issue"
"Need to update your computer"
"Security patch installation"

Delivery Person:
"Package for [employee name]"
"Urgent delivery requiring signature"
"Need access to server room"

Contractor/Vendor:
"Scheduled maintenance visit"
"Fire safety inspection"
"Cleaning service"

Emergency Services:
"Building evacuation drill"
"Safety inspection required"
"Emergency equipment check"

# Preparation requirements:
- Research target organization
- Obtain appropriate uniforms/props
- Learn company terminology
- Practice confident demeanor
- Prepare backup stories</code></pre>
      `,
      type: "text"
    },
    {
      title: "Physical Penetration Testing",
      content: `
        <h2>Comprehensive Physical Security Assessment</h2>
        <p>Physical penetration testing combines multiple techniques to assess the overall security posture of physical facilities and controls.</p>

        <h3>Reconnaissance and Planning</h3>
        <h4>Target Assessment</h4>
        <pre><code># Physical reconnaissance:

External observation:
- Building layout and entrances
- Security cameras and lighting
- Perimeter defenses
- Guard schedules and patterns
- Employee behavior patterns

OSINT gathering:
- Building plans and permits
- Employee information (LinkedIn)
- Vendor and contractor lists
- Security system information
- Emergency procedures

Technical reconnaissance:
- Wireless network scanning
- RFID frequency detection
- Camera system identification
- Alarm system analysis
- Communication interception</code></pre>

        <h3>Entry Techniques</h3>
        <h4>Covert Entry Methods</h4>
        <pre><code># Stealth entry techniques:

Lock manipulation:
- Pick locks during low-traffic times
- Use bump keys for quick entry
- Impression locks for future access
- Bypass electronic locks

Badge cloning:
- Clone employee access cards
- Use cloned cards during shift changes
- Combine with social engineering
- Test on multiple readers

Social engineering:
- Tailgate behind employees
- Impersonate authorized personnel
- Create distractions for guards
- Use pretexts for legitimate access

Physical bypass:
- Climb fences or walls
- Use maintenance tunnels
- Access through unsecured areas
- Exploit architectural weaknesses</code></pre>

        <h3>Post-Entry Activities</h3>
        <h4>Evidence Collection</h4>
        <pre><code># Documentation methods:

Photography:
- Take photos of accessed areas
- Document security weaknesses
- Capture sensitive information
- Record system configurations

Physical evidence:
- Collect discarded documents
- Note unlocked workstations
- Document unsecured assets
- Identify data storage locations

Digital evidence:
- USB device deployment
- Network access attempts
- System information gathering
- Credential harvesting

# Evidence handling:
- Maintain chain of custody
- Secure storage of materials
- Document all activities
- Prepare for legal review</code></pre>

        <h3>Advanced Physical Attacks</h3>
        <h4>Hardware Implants</h4>
        <pre><code># Hardware attack devices:

USB drops:
- Malicious USB devices
- Keystroke loggers
- Network implants
- Rubber Ducky payloads

Network taps:
- Ethernet implants
- Wireless bridges
- Packet capture devices
- Covert channels

Physical keyloggers:
- PS/2 keyloggers
- USB keyloggers
- Wireless keyloggers
- Acoustic keyloggers

RFID skimmers:
- Card reader overlays
- Wireless skimmers
- Bluetooth transmitters
- Data collection devices

# Deployment considerations:
- Device concealment
- Power requirements
- Data exfiltration methods
- Detection avoidance
- Remote access capabilities</code></pre>

        <h4>Surveillance Evasion</h4>
        <pre><code># Camera evasion techniques:

Blind spot exploitation:
- Map camera coverage areas
- Identify surveillance gaps
- Use architectural features
- Time movement patterns

Technical countermeasures:
- IR LED arrays (blind cameras)
- Laser pointers (temporary)
- Reflective materials
- Signal jamming (illegal)

Behavioral techniques:
- Disguise appearance
- Alter gait and posture
- Use crowd cover
- Avoid direct camera view

# Motion sensor bypass:
- Crawling under sensors
- Moving very slowly
- Using sensor dead zones
- Temperature masking
- Frequency jamming</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Corporate Facility Physical Penetration Test",
    description: "Conduct comprehensive physical security assessment of a corporate facility including lock bypass, access control testing, and social engineering.",
    environment: "Multi-story corporate building with various physical security controls, access systems, and surveillance",
    tasks: [
      {
        category: "Reconnaissance",
        tasks: [
          {
            task: "Perform physical and technical reconnaissance",
            method: "External observation, OSINT gathering, and wireless scanning",
            expectedFindings: "Complete facility security profile and attack vectors",
            points: 15
          }
        ]
      },
      {
        category: "Lock and Access Control Testing",
        tasks: [
          {
            task: "Test mechanical lock security",
            method: "Lock picking, bumping, and bypass techniques",
            expectedFindings: "Successful lock bypass and entry methods",
            points: 25
          },
          {
            task: "Assess electronic access control systems",
            method: "RFID cloning, badge testing, and system analysis",
            expectedFindings: "Access control vulnerabilities and bypass methods",
            points: 25
          }
        ]
      },
      {
        category: "Social Engineering",
        tasks: [
          {
            task: "Execute physical social engineering attacks",
            method: "Tailgating, pretexting, and authority impersonation",
            expectedFindings: "Successful unauthorized facility access",
            points: 20
          }
        ]
      },
      {
        category: "Post-Entry Assessment",
        tasks: [
          {
            task: "Document security weaknesses and collect evidence",
            method: "Photography, document collection, and system analysis",
            expectedFindings: "Comprehensive security weakness documentation",
            points: 15
          }
        ]
      }
    ],
    deliverables: [
      "Physical security assessment report with findings",
      "Lock bypass demonstration and documentation",
      "Access control system vulnerability analysis",
      "Social engineering attack documentation",
      "Photographic evidence of security weaknesses",
      "Physical security improvement recommendations"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which lock picking technique involves creating a working key through trial and error?",
        options: [
          "Bumping",
          "Raking",
          "Impressioning",
          "Snap gun"
        ],
        correct: 2,
        explanation: "Impressioning involves repeatedly inserting a blank key, applying pressure, and filing marked areas until a working key is created."
      },
      {
        question: "What is the primary vulnerability exploited in RFID badge cloning?",
        options: [
          "Weak encryption",
          "Unencrypted transmission",
          "Poor authentication",
          "All of the above"
        ],
        correct: 3,
        explanation: "RFID badge cloning exploits multiple vulnerabilities including weak encryption, unencrypted transmission, and poor authentication mechanisms."
      },
      {
        question: "Which physical social engineering technique involves following an authorized person through a secure door?",
        options: [
          "Pretexting",
          "Tailgating",
          "Baiting",
          "Quid pro quo"
        ],
        correct: 1,
        explanation: "Tailgating involves following an authorized person closely through a secure entrance without proper authentication."
      }
    ],
    practicalTasks: [
      {
        task: "Demonstrate lock picking techniques on various lock types",
        points: 25
      },
      {
        task: "Clone RFID badges and test access control bypass",
        points: 25
      },
      {
        task: "Execute successful physical social engineering attack",
        points: 25
      },
      {
        task: "Conduct comprehensive physical security assessment with documentation",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "Practical Lock Picking by Deviant Ollam",
      url: "https://www.elsevier.com/books/practical-lock-picking/ollam/978-1-59749-989-7",
      type: "book"
    },
    {
      title: "Proxmark3 Documentation",
      url: "https://github.com/RfidResearchGroup/proxmark3",
      type: "tool"
    },
    {
      title: "Physical Security Professional Handbook",
      url: "https://www.asisonline.org/",
      type: "reference"
    }
  ],
  tags: ["physical-security", "lock-picking", "rfid-cloning", "social-engineering", "facility-penetration"],
  lastUpdated: "2024-01-15"
};
