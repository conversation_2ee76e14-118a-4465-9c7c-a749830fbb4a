/**
 * Ethical Hacking Module: Mobile Application Security Testing
 * Module ID: eh-13
 */

export const mobileSecurityContent = {
  id: "eh-13",
  title: "Mobile Application Security Testing",
  description: "Master mobile application security assessment techniques for Android and iOS platforms, including static analysis, dynamic testing, and runtime manipulation.",
  difficulty: "Advanced",
  estimatedTime: 110,
  objectives: [
    "Understand mobile application security fundamentals and attack vectors",
    "Master Android application security testing techniques",
    "Learn iOS application security assessment methods",
    "Develop skills in mobile malware analysis and reverse engineering",
    "Apply mobile security testing in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-5", "eh-7", "eh-16"],
  sections: [
    {
      title: "Mobile Security Fundamentals",
      content: `
        <h2>Mobile Application Security Overview</h2>
        <p>Mobile applications present unique security challenges due to their distributed nature, diverse platforms, and complex ecosystem interactions.</p>
        
        <h3>Mobile Security Landscape</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Platform</th>
              <th>Market Share</th>
              <th>App Store</th>
              <th>Primary Language</th>
              <th>Security Model</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Android</td>
              <td>~71%</td>
              <td>Google Play Store</td>
              <td>Java/Kotlin</td>
              <td>Permission-based</td>
            </tr>
            <tr>
              <td>iOS</td>
              <td>~28%</td>
              <td>Apple App Store</td>
              <td>Objective-C/Swift</td>
              <td>Sandbox + Code Signing</td>
            </tr>
            <tr>
              <td>Others</td>
              <td>~1%</td>
              <td>Various</td>
              <td>Multiple</td>
              <td>Platform-specific</td>
            </tr>
          </tbody>
        </table>

        <h3>OWASP Mobile Top 10 (2016)</h3>
        <ol>
          <li><strong>M1: Improper Platform Usage</strong> - Misuse of platform features or security controls</li>
          <li><strong>M2: Insecure Data Storage</strong> - Inadequate protection of sensitive data</li>
          <li><strong>M3: Insecure Communication</strong> - Poor network communication security</li>
          <li><strong>M4: Insecure Authentication</strong> - Weak authentication mechanisms</li>
          <li><strong>M5: Insufficient Cryptography</strong> - Weak or broken cryptographic implementations</li>
          <li><strong>M6: Insecure Authorization</strong> - Poor authorization controls</li>
          <li><strong>M7: Client Code Quality</strong> - Code-level vulnerabilities</li>
          <li><strong>M8: Code Tampering</strong> - Lack of runtime application self-protection</li>
          <li><strong>M9: Reverse Engineering</strong> - Inadequate binary protection</li>
          <li><strong>M10: Extraneous Functionality</strong> - Hidden backdoors or debug features</li>
        </ol>

        <h3>Mobile Testing Methodology</h3>
        <ol>
          <li><strong>Static Analysis</strong> - Code review and binary analysis</li>
          <li><strong>Dynamic Analysis</strong> - Runtime behavior testing</li>
          <li><strong>Interactive Testing</strong> - Manual security testing</li>
          <li><strong>Network Analysis</strong> - Communication security assessment</li>
          <li><strong>Platform Testing</strong> - OS-level security evaluation</li>
        </ol>

        <h3>Mobile Testing Environment Setup</h3>
        <h4>Required Tools and Platforms</h4>
        <ul>
          <li><strong>Android Studio</strong> - Android development environment</li>
          <li><strong>Xcode</strong> - iOS development environment (macOS only)</li>
          <li><strong>Android Emulator/Physical Device</strong> - Testing environment</li>
          <li><strong>iOS Simulator/Physical Device</strong> - Testing environment</li>
          <li><strong>Proxy Tools</strong> - Burp Suite, OWASP ZAP</li>
          <li><strong>Static Analysis Tools</strong> - MobSF, QARK, SonarQube</li>
          <li><strong>Dynamic Analysis Tools</strong> - Frida, Objection, Xposed</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Android Security Testing",
      content: `
        <h2>Android Application Security Assessment</h2>
        <p>Android applications run in a Linux-based environment with specific security mechanisms and potential vulnerabilities.</p>

        <h3>Android Application Architecture</h3>
        <h4>APK Structure Analysis</h4>
        <pre><code># Extract APK contents
unzip application.apk -d extracted_apk/

# APK structure
META-INF/          # Signatures and certificates
res/               # Resources
lib/               # Native libraries
assets/            # Asset files
classes.dex        # Dalvik bytecode
AndroidManifest.xml # App configuration
resources.arsc     # Compiled resources</code></pre>

        <h4>Android Manifest Analysis</h4>
        <pre><code># Decode AndroidManifest.xml
aapt dump xmltree application.apk AndroidManifest.xml

# Extract manifest with apktool
apktool d application.apk

# Key manifest elements to analyze:
# - Permissions requested
# - Exported components
# - Intent filters
# - Debug flags
# - Backup settings</code></pre>

        <h3>Static Analysis Techniques</h3>
        <h4>Automated Static Analysis</h4>
        <pre><code># MobSF (Mobile Security Framework)
# Upload APK to MobSF web interface
# Automated analysis includes:
# - Permission analysis
# - Code quality assessment
# - Hardcoded secrets detection
# - Binary analysis

# QARK (Quick Android Review Kit)
qark --apk application.apk

# SonarQube for source code analysis
sonar-scanner -Dsonar.projectKey=mobile-app</code></pre>

        <h4>Manual Code Review</h4>
        <pre><code># Decompile APK to Java source
jadx application.apk

# Convert DEX to JAR
d2j-dex2jar classes.dex

# Analyze with JD-GUI
java -jar jd-gui.jar classes-dex2jar.jar

# Key areas to review:
# - Hardcoded credentials
# - Insecure cryptographic implementations
# - SQL injection vulnerabilities
# - Insecure data storage
# - Improper certificate validation</code></pre>

        <h3>Dynamic Analysis and Runtime Testing</h3>
        <h4>Frida for Runtime Manipulation</h4>
        <pre><code># Install Frida server on Android device
adb push frida-server /data/local/tmp/
adb shell "chmod 755 /data/local/tmp/frida-server"
adb shell "/data/local/tmp/frida-server &"

# List running processes
frida-ps -U

# Hook application methods
frida -U -l hook_script.js com.example.app

# Example Frida script for SSL pinning bypass
Java.perform(function() {
    var TrustManager = Java.use("javax.net.ssl.X509TrustManager");
    TrustManager.checkServerTrusted.implementation = function(chain, authType) {
        console.log("SSL certificate validation bypassed");
    };
});</code></pre>

        <h4>Objection for Mobile Testing</h4>
        <pre><code># Start objection session
objection -g com.example.app explore

# Common objection commands
android hooking list classes
android hooking search methods
android keystore list
android sslpinning disable
memory dump all</code></pre>

        <h3>Android-Specific Vulnerabilities</h3>
        <h4>Intent-Based Attacks</h4>
        <pre><code># Test for exported activities
adb shell am start -n com.example.app/.VulnerableActivity

# Intent fuzzing
adb shell am start -a android.intent.action.VIEW -d "malicious://payload"

# Broadcast intent testing
adb shell am broadcast -a com.example.CUSTOM_ACTION</code></pre>

        <h4>Content Provider Testing</h4>
        <pre><code># Query content providers
adb shell content query --uri content://com.example.provider/data

# Test for SQL injection
adb shell content query --uri "content://com.example.provider/data" --where "name='test' OR '1'='1'"

# Path traversal testing
adb shell content query --uri "content://com.example.provider/../../../etc/passwd"</code></pre>

        <h4>WebView Security Testing</h4>
        <pre><code># Test for JavaScript interface vulnerabilities
# Look for addJavascriptInterface usage
# Test for file:// URL access
# Check for mixed content issues

# WebView debugging
# Enable WebView debugging in app
# Use Chrome DevTools for inspection</code></pre>

        <h3>Android Malware Analysis</h3>
        <h4>Behavioral Analysis</h4>
        <pre><code># Monitor system calls
strace -p PID -o syscalls.log

# Network traffic monitoring
tcpdump -i any -w network_traffic.pcap

# File system monitoring
inotifywait -m -r /data/data/com.example.app/</code></pre>

        <h4>Anti-Analysis Evasion</h4>
        <pre><code># Detect and bypass anti-debugging
# Root detection bypass
# Emulator detection bypass
# Tamper detection bypass

# Use Magisk Hide for root concealment
# Custom ROM modifications
# Frida scripts for evasion</code></pre>
      `,
      type: "text"
    },
    {
      title: "iOS Security Testing",
      content: `
        <h2>iOS Application Security Assessment</h2>
        <p>iOS applications operate within Apple's strict security model, requiring specialized techniques for security testing.</p>

        <h3>iOS Application Architecture</h3>
        <h4>IPA Structure Analysis</h4>
        <pre><code># Extract IPA contents
unzip application.ipa -d extracted_ipa/

# IPA structure
Payload/
  Application.app/
    Info.plist         # App configuration
    executable         # Main binary
    Frameworks/        # Embedded frameworks
    PlugIns/          # App extensions
    _CodeSignature/   # Code signing information</code></pre>

        <h4>Info.plist Analysis</h4>
        <pre><code># Convert binary plist to XML
plutil -convert xml1 Info.plist

# Key plist entries to analyze:
# - URL schemes
# - Background modes
# - Permissions (NSLocationUsageDescription, etc.)
# - App Transport Security settings
# - Exported functions</code></pre>

        <h3>iOS Static Analysis</h3>
        <h4>Binary Analysis</h4>
        <pre><code># Extract binary from IPA
unzip application.ipa
cd Payload/Application.app/

# Analyze binary with otool
otool -L Application          # Linked libraries
otool -h Application          # Headers
otool -s __TEXT __cstring Application  # String constants

# Class-dump for Objective-C
class-dump Application > classes.h

# Hopper Disassembler for detailed analysis
# IDA Pro for advanced reverse engineering</code></pre>

        <h4>Source Code Analysis (if available)</h4>
        <pre><code># Key areas for iOS code review:
# - Keychain usage
# - Core Data security
# - Network communication
# - Cryptographic implementations
# - URL scheme handling
# - Touch ID/Face ID implementation</code></pre>

        <h3>iOS Dynamic Analysis</h3>
        <h4>Jailbreak Requirements</h4>
        <pre><code># Jailbreak tools (version-dependent):
# - checkra1n (iOS 12.0-14.8.1)
# - unc0ver (iOS 11.0-14.8)
# - Taurine (iOS 14.0-14.3)

# Install Cydia and essential tools:
# - OpenSSH
# - Frida
# - Cycript
# - AppSync Unified</code></pre>

        <h4>Runtime Analysis with Frida</h4>
        <pre><code># Install Frida on jailbroken device
# Connect to iOS device
frida-ps -U

# Hook iOS application
frida -U -l ios_hooks.js com.example.app

# Example iOS Frida script
if (ObjC.available) {
    var NSURLSession = ObjC.classes.NSURLSession;
    var method = NSURLSession["- dataTaskWithRequest:completionHandler:"];
    
    Interceptor.attach(method.implementation, {
        onEnter: function(args) {
            var request = new ObjC.Object(args[2]);
            console.log("URL: " + request.URL().absoluteString());
        }
    });
}</code></pre>

        <h3>iOS-Specific Vulnerabilities</h3>
        <h4>URL Scheme Attacks</h4>
        <pre><code># Test custom URL schemes
# Create test app or use Safari
myapp://action?param=value

# Test for parameter injection
myapp://action?param=../../../etc/passwd

# Fuzzing URL schemes
# Use automated tools or custom scripts</code></pre>

        <h4>Keychain Security Testing</h4>
        <pre><code># Analyze keychain usage
# Check for proper access control
# Test keychain data extraction
# Verify encryption implementation

# Keychain dumper tool
keychain_dumper</code></pre>

        <h4>App Transport Security (ATS) Testing</h4>
        <pre><code># Check ATS configuration in Info.plist
NSAppTransportSecurity
  NSAllowsArbitraryLoads: YES/NO
  NSExceptionDomains: {...}

# Test for ATS bypasses
# Monitor network traffic
# Check for HTTP connections</code></pre>

        <h3>iOS Malware Analysis</h3>
        <h4>Behavioral Monitoring</h4>
        <pre><code># Monitor file system access
fs_usage -w -f filesystem

# Network monitoring
tcpdump -i en0 -w ios_traffic.pcap

# System call tracing
dtruss -p PID</code></pre>

        <h4>Anti-Analysis Techniques</h4>
        <pre><code># Jailbreak detection bypass
# Anti-debugging evasion
# Code obfuscation analysis
# Runtime application self-protection (RASP) bypass</code></pre>
      `,
      type: "text"
    },
    {
      title: "Mobile Network Security",
      content: `
        <h2>Mobile Communication Security Testing</h2>
        <p>Mobile applications rely heavily on network communications, requiring comprehensive assessment of data transmission security.</p>

        <h3>Network Traffic Interception</h3>
        <h4>Proxy Setup and Configuration</h4>
        <pre><code># Burp Suite mobile testing setup
# 1. Configure Burp proxy listener
# 2. Install Burp CA certificate on device
# 3. Configure device proxy settings
# 4. Test HTTPS interception

# OWASP ZAP mobile testing
# Similar setup process
# Use ZAP's mobile app for easier configuration</code></pre>

        <h4>SSL/TLS Security Testing</h4>
        <pre><code># Test SSL pinning implementation
# Use Frida scripts to bypass pinning
# Check certificate validation logic
# Test for weak cipher suites

# SSL Labs mobile app testing
# Use testssl.sh for comprehensive SSL testing
testssl.sh --mobile https://api.example.com</code></pre>

        <h3>API Security Testing</h3>
        <h4>REST API Testing</h4>
        <pre><code># Common API vulnerabilities:
# - Broken authentication
# - Excessive data exposure
# - Lack of rate limiting
# - Injection attacks
# - Improper asset management

# API testing with Burp Suite
# Automated scanning
# Manual parameter manipulation
# Authentication bypass testing</code></pre>

        <h4>GraphQL API Testing</h4>
        <pre><code># GraphQL introspection
query IntrospectionQuery {
  __schema {
    queryType { name }
    mutationType { name }
    subscriptionType { name }
  }
}

# GraphQL injection testing
# Depth limiting bypass
# Query complexity attacks</code></pre>

        <h3>Mobile Device Management (MDM) Testing</h3>
        <h4>Enterprise Mobile Security</h4>
        <ul>
          <li><strong>App Wrapping</strong> - Security policy enforcement</li>
          <li><strong>Containerization</strong> - Data separation</li>
          <li><strong>Remote Wipe</strong> - Data protection capabilities</li>
          <li><strong>Compliance Checking</strong> - Device security validation</li>
        </ul>

        <h3>Mobile Threat Modeling</h3>
        <h4>STRIDE for Mobile</h4>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Threat</th>
              <th>Mobile Context</th>
              <th>Example</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Spoofing</td>
              <td>Identity impersonation</td>
              <td>Fake mobile apps</td>
            </tr>
            <tr>
              <td>Tampering</td>
              <td>Code/data modification</td>
              <td>App repackaging</td>
            </tr>
            <tr>
              <td>Repudiation</td>
              <td>Action denial</td>
              <td>Transaction disputes</td>
            </tr>
            <tr>
              <td>Information Disclosure</td>
              <td>Data leakage</td>
              <td>Insecure storage</td>
            </tr>
            <tr>
              <td>Denial of Service</td>
              <td>Service disruption</td>
              <td>Battery drain attacks</td>
            </tr>
            <tr>
              <td>Elevation of Privilege</td>
              <td>Unauthorized access</td>
              <td>Root/jailbreak exploits</td>
            </tr>
          </tbody>
        </table>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Banking App Security Assessment",
    description: "Conduct a comprehensive security assessment of a simulated mobile banking application for both Android and iOS platforms.",
    environment: "Mobile testing lab with Android and iOS devices, proxy tools, and static analysis platforms",
    tasks: [
      {
        category: "Android Security Testing",
        tasks: [
          {
            task: "Perform static analysis of Android banking app",
            method: "Use MobSF and manual code review techniques",
            expectedFindings: "Hardcoded secrets, insecure storage, permission issues",
            points: 20
          },
          {
            task: "Bypass SSL pinning and intercept API traffic",
            method: "Use Frida scripts and Burp Suite proxy",
            expectedFindings: "API endpoints, authentication tokens, data flows",
            points: 25
          },
          {
            task: "Test for root detection bypass",
            method: "Use Magisk Hide and custom Frida scripts",
            expectedFindings: "Successful root concealment",
            points: 15
          }
        ]
      },
      {
        category: "iOS Security Testing",
        tasks: [
          {
            task: "Analyze iOS banking app binary and configuration",
            method: "Use class-dump, otool, and plist analysis",
            expectedFindings: "App structure, URL schemes, ATS configuration",
            points: 20
          },
          {
            task: "Test keychain security implementation",
            method: "Use keychain dumper and runtime analysis",
            expectedFindings: "Keychain access controls and data protection",
            points: 15
          }
        ]
      },
      {
        category: "Network Security Assessment",
        tasks: [
          {
            task: "Assess API security and authentication mechanisms",
            method: "Comprehensive API testing with automated tools",
            expectedFindings: "Authentication flaws, data exposure issues",
            points: 5
          }
        ]
      }
    ],
    deliverables: [
      "Mobile application security assessment report",
      "Static analysis findings and code review results",
      "Dynamic testing evidence and network traffic analysis",
      "Platform-specific vulnerability documentation",
      "Mobile security best practices recommendations",
      "Remediation roadmap with priority rankings"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which OWASP Mobile Top 10 category addresses inadequate protection of sensitive data on mobile devices?",
        options: [
          "M1: Improper Platform Usage",
          "M2: Insecure Data Storage",
          "M3: Insecure Communication",
          "M4: Insecure Authentication"
        ],
        correct: 1,
        explanation: "M2: Insecure Data Storage specifically addresses inadequate protection of sensitive data stored on mobile devices."
      },
      {
        question: "What is the primary purpose of SSL pinning in mobile applications?",
        options: [
          "Improve network performance",
          "Prevent man-in-the-middle attacks",
          "Reduce battery consumption",
          "Enable offline functionality"
        ],
        correct: 1,
        explanation: "SSL pinning prevents man-in-the-middle attacks by validating that the server certificate matches a known good certificate."
      },
      {
        question: "Which tool is most commonly used for runtime manipulation of mobile applications?",
        options: [
          "MobSF",
          "QARK",
          "Frida",
          "APKTool"
        ],
        correct: 2,
        explanation: "Frida is the most popular dynamic instrumentation toolkit for runtime manipulation and analysis of mobile applications."
      }
    ],
    practicalTasks: [
      {
        task: "Perform static analysis on both Android and iOS applications",
        points: 25
      },
      {
        task: "Demonstrate SSL pinning bypass and traffic interception",
        points: 25
      },
      {
        task: "Conduct runtime analysis using Frida or similar tools",
        points: 25
      },
      {
        task: "Document mobile-specific vulnerabilities and provide remediation guidance",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "OWASP Mobile Security Testing Guide",
      url: "https://owasp.org/www-project-mobile-security-testing-guide/",
      type: "guide"
    },
    {
      title: "Mobile Security Framework (MobSF)",
      url: "https://github.com/MobSF/Mobile-Security-Framework-MobSF",
      type: "tool"
    },
    {
      title: "Frida Dynamic Instrumentation Toolkit",
      url: "https://frida.re/",
      type: "tool"
    }
  ],
  tags: ["mobile-security", "android-testing", "ios-testing", "mobile-malware", "api-security"],
  lastUpdated: "2024-01-15"
};
