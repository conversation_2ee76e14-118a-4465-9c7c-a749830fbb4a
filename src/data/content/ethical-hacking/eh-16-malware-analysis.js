/**
 * Ethical Hacking Module: Malware Analysis and Reverse Engineering
 * Module ID: eh-16
 */

export const malwareAnalysisContent = {
  id: "eh-16",
  title: "Malware Analysis and Reverse Engineering",
  description: "Master malware analysis techniques including static and dynamic analysis, reverse engineering, and advanced evasion detection for comprehensive threat assessment.",
  difficulty: "Advanced",
  estimatedTime: 120,
  objectives: [
    "Understand malware types, families, and attack vectors",
    "Master static analysis techniques and tools",
    "Learn dynamic analysis and behavioral monitoring",
    "Develop reverse engineering skills for malware dissection",
    "Apply malware analysis in threat intelligence and incident response"
  ],
  prerequisites: ["eh-1", "eh-7", "eh-9", "eh-10"],
  sections: [
    {
      title: "Malware Fundamentals",
      content: `
        <h2>Understanding Malware Landscape</h2>
        <p>Malware analysis is critical for understanding threats, developing defenses, and conducting forensic investigations in cybersecurity incidents.</p>
        
        <h3>Malware Classification</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Type</th>
              <th>Characteristics</th>
              <th>Propagation</th>
              <th>Examples</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Virus</td>
              <td>Self-replicating, requires host</td>
              <td>File infection</td>
              <td>CIH, ILOVEYOU</td>
            </tr>
            <tr>
              <td>Worm</td>
              <td>Self-propagating, network spread</td>
              <td>Network vulnerabilities</td>
              <td>Conficker, WannaCry</td>
            </tr>
            <tr>
              <td>Trojan</td>
              <td>Disguised malicious software</td>
              <td>Social engineering</td>
              <td>Zeus, Emotet</td>
            </tr>
            <tr>
              <td>Ransomware</td>
              <td>Encrypts files for ransom</td>
              <td>Email, exploits</td>
              <td>Ryuk, Maze</td>
            </tr>
            <tr>
              <td>Rootkit</td>
              <td>Hides presence, maintains access</td>
              <td>Privilege escalation</td>
              <td>Stuxnet, Flame</td>
            </tr>
            <tr>
              <td>Spyware</td>
              <td>Steals information</td>
              <td>Bundled software</td>
              <td>FinFisher, Pegasus</td>
            </tr>
          </tbody>
        </table>

        <h3>Malware Analysis Types</h3>
        <ol>
          <li><strong>Static Analysis</strong> - Examining malware without execution</li>
          <li><strong>Dynamic Analysis</strong> - Observing malware behavior during execution</li>
          <li><strong>Hybrid Analysis</strong> - Combining static and dynamic techniques</li>
          <li><strong>Code Analysis</strong> - Reverse engineering and disassembly</li>
        </ol>

        <h3>Analysis Environment Setup</h3>
        <h4>Isolated Analysis Environment</h4>
        <ul>
          <li><strong>Virtual Machines</strong> - VMware, VirtualBox with snapshots</li>
          <li><strong>Sandboxes</strong> - Cuckoo Sandbox, Joe Sandbox</li>
          <li><strong>Network Isolation</strong> - Air-gapped or controlled network</li>
          <li><strong>Monitoring Tools</strong> - Process Monitor, Wireshark, Volatility</li>
        </ul>

        <h3>Legal and Ethical Considerations</h3>
        <div class="alert alert-danger">
          <strong>Critical Warning:</strong> Malware analysis must only be conducted in controlled, isolated environments with proper authorization. Possession and analysis of malware may be subject to legal restrictions. Always ensure compliance with local laws and organizational policies.
        </div>
      `,
      type: "text"
    },
    {
      title: "Static Analysis Techniques",
      content: `
        <h2>Static Malware Analysis</h2>
        <p>Static analysis examines malware without executing it, providing insights into structure, capabilities, and potential behavior.</p>

        <h3>File Analysis and Identification</h3>
        <h4>Hash Analysis</h4>
        <pre><code># Generate file hashes
md5sum malware.exe
sha1sum malware.exe
sha256sum malware.exe

# VirusTotal lookup
curl -X POST 'https://www.virustotal.com/vtapi/v2/file/report' \
  --form apikey='YOUR_API_KEY' \
  --form resource='FILE_HASH'

# YARA rule matching
yara rules.yar malware.exe</code></pre>

        <h4>File Format Analysis</h4>
        <pre><code># File type identification
file malware.exe
hexdump -C malware.exe | head -20

# PE file analysis (Windows)
pefile malware.exe
peframe malware.exe
pestudio malware.exe

# ELF file analysis (Linux)
readelf -h malware.elf
objdump -f malware.elf

# Entropy analysis
ent malware.exe
python -c "import math; print(entropy('malware.exe'))"</code></pre>

        <h3>String Analysis</h3>
        <h4>Extracting Readable Strings</h4>
        <pre><code># Basic string extraction
strings malware.exe
strings -a malware.exe  # All strings
strings -e l malware.exe  # Little-endian 16-bit
strings -e b malware.exe  # Big-endian 16-bit

# Advanced string analysis
floss malware.exe  # FireEye Labs Obfuscated String Solver

# String categorization
# Look for:
# - URLs and domains
# - File paths
# - Registry keys
# - API function names
# - Error messages
# - Encryption keys</code></pre>

        <h3>Disassembly and Code Analysis</h3>
        <h4>Disassemblers and Debuggers</h4>
        <pre><code># IDA Pro (Industry standard)
# Load binary and analyze
# Identify functions, cross-references
# Graph view for control flow

# Ghidra (Free NSA tool)
# Decompiler capabilities
# Scripting support
# Collaborative analysis

# Radare2 (Open source)
r2 malware.exe
aaa  # Analyze all
pdf @ main  # Print disassembly of main function

# x64dbg (Windows debugger)
# Dynamic debugging capabilities
# Plugin ecosystem</code></pre>

        <h4>Assembly Code Analysis</h4>
        <pre><code># Common malicious patterns
# API hashing
mov eax, 0x12345678  ; Hashed API name
call resolve_api

# String obfuscation
xor eax, 0xDEADBEEF  ; XOR decryption
mov [esi], al

# Anti-analysis checks
rdtsc  ; Timing checks
cpuid  ; VM detection
int 2dh  ; Debugger detection

# Control flow obfuscation
jmp $+5  ; Jump to next instruction
call $+5  ; Call next instruction
pop eax   ; Get current address</code></pre>

        <h3>Packed Malware Analysis</h3>
        <h4>Packer Detection</h4>
        <pre><code># Detect packers
peid malware.exe  # PEiD signatures
exeinfo malware.exe
die malware.exe  # Detect It Easy

# Common packers
# UPX, ASPack, PECompact, Themida, VMProtect

# Entropy analysis for packing
python -c "
import pefile
pe = pefile.PE('malware.exe')
for section in pe.sections:
    print(f'{section.Name}: {section.get_entropy()}')"</code></pre>

        <h4>Unpacking Techniques</h4>
        <pre><code># Automated unpacking
upx -d malware.exe  # UPX unpacker
unipacker malware.exe  # Generic unpacker

# Manual unpacking process
# 1. Identify packer type
# 2. Find Original Entry Point (OEP)
# 3. Set breakpoint at OEP
# 4. Run until OEP
# 5. Dump unpacked code
# 6. Fix Import Address Table (IAT)

# Memory dumping
volatility -f memory.dump --profile=Win7SP1x64 procdump -p PID --dump-dir=./</code></pre>

        <h3>Configuration Extraction</h3>
        <h4>Malware Configuration Analysis</h4>
        <pre><code># Configuration extraction tools
malduck  # Malware configuration extractor
malconf  # Configuration parser

# Common configuration elements
# - C&C server addresses
# - Encryption keys
# - Campaign identifiers
# - Persistence mechanisms
# - Evasion settings

# YARA rules for config extraction
rule MalwareConfig {
    strings:
        $config = { 00 11 22 33 44 55 66 77 }  # Config marker
    condition:
        $config
}</code></pre>
      `,
      type: "text"
    },
    {
      title: "Dynamic Analysis Techniques",
      content: `
        <h2>Dynamic Malware Analysis</h2>
        <p>Dynamic analysis observes malware behavior during execution, revealing runtime activities and capabilities.</p>

        <h3>Sandbox Analysis</h3>
        <h4>Automated Sandbox Systems</h4>
        <pre><code># Cuckoo Sandbox
cuckoo submit malware.exe
cuckoo web  # Web interface

# Joe Sandbox
# Commercial sandbox with detailed reports
# API integration available

# Hybrid Analysis
# Online sandbox service
# Community and commercial versions

# ANY.RUN
# Interactive online sandbox
# Real-time analysis</code></pre>

        <h4>Manual Dynamic Analysis</h4>
        <pre><code># Process monitoring
procmon.exe  # Process Monitor (Windows)
ps aux | grep malware  # Linux process monitoring

# Network monitoring
wireshark  # Network packet capture
netstat -an  # Active connections
tcpdump -i any -w capture.pcap  # Linux packet capture

# File system monitoring
fsmon  # File system monitor
inotifywait -m -r /path/to/monitor  # Linux file monitoring

# Registry monitoring (Windows)
regshot  # Registry snapshot comparison
procmon with registry filter</code></pre>

        <h3>Behavioral Analysis</h3>
        <h4>System Behavior Monitoring</h4>
        <pre><code># API call monitoring
api-monitor  # Windows API monitoring
strace ./malware  # Linux system call tracing
ltrace ./malware  # Library call tracing

# Memory analysis
volatility -f memory.dump --profile=Win7SP1x64 pslist
volatility -f memory.dump --profile=Win7SP1x64 netscan
volatility -f memory.dump --profile=Win7SP1x64 malfind

# Process injection detection
hollows_hunter  # Process hollowing detection
pe-sieve  # Inline hooks and patches detection</code></pre>

        <h4>Network Behavior Analysis</h4>
        <pre><code># DNS monitoring
dig @******* malicious-domain.com
nslookup malicious-domain.com

# HTTP/HTTPS traffic analysis
mitmproxy  # Man-in-the-middle proxy
burpsuite  # Web application security testing

# C&C communication patterns
# Beaconing intervals
# Data exfiltration patterns
# Protocol analysis

# Network indicators extraction
# IP addresses
# Domain names
# URL patterns
# User agents</code></pre>

        <h3>Anti-Analysis Evasion</h3>
        <h4>VM and Sandbox Detection</h4>
        <pre><code># Common evasion techniques
# VM detection methods:
# - Hardware fingerprinting
# - Registry keys
# - Process names
# - File system artifacts
# - Timing attacks

# Sandbox evasion:
# - Sleep delays
# - User interaction checks
# - Environment validation
# - Geolocation checks

# Bypassing evasion
# Modify VM signatures
# Patch detection routines
# Use bare metal analysis
# Custom analysis environment</code></pre>

        <h4>Debugger Detection Bypass</h4>
        <pre><code># Anti-debugging techniques
# IsDebuggerPresent API
# PEB BeingDebugged flag
# NtGlobalFlag checks
# Timing attacks
# Exception handling

# Bypass methods
# Patch detection functions
# Modify PEB flags
# Hook API calls
# Use kernel debuggers

# ScyllaHide plugin
# Anti-anti-debugging tool
# Works with x64dbg and OllyDbg</code></pre>

        <h3>Memory Forensics</h3>
        <h4>Memory Dump Analysis</h4>
        <pre><code># Memory acquisition
winpmem  # Windows memory acquisition
lime  # Linux Memory Extractor
avml  # Azure VM memory acquisition

# Volatility Framework
volatility -f memory.dump --profile=Win10x64_19041 imageinfo
volatility -f memory.dump --profile=Win10x64_19041 pslist
volatility -f memory.dump --profile=Win10x64_19041 pstree
volatility -f memory.dump --profile=Win10x64_19041 dlllist -p PID
volatility -f memory.dump --profile=Win10x64_19041 handles -p PID
volatility -f memory.dump --profile=Win10x64_19041 cmdline
volatility -f memory.dump --profile=Win10x64_19041 filescan
volatility -f memory.dump --profile=Win10x64_19041 malfind

# Memory strings analysis
strings memory.dump | grep -i "malicious"
bulk_extractor -o output memory.dump</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced Reverse Engineering",
      content: `
        <h2>Malware Reverse Engineering</h2>
        <p>Advanced reverse engineering techniques for understanding complex malware functionality and developing countermeasures.</p>

        <h3>Advanced Static Analysis</h3>
        <h4>Control Flow Analysis</h4>
        <pre><code># IDA Pro scripting
import idaapi
import idc

# Function enumeration
for func_ea in Functions():
    func_name = idc.get_func_name(func_ea)
    print(f"Function: {func_name} at {hex(func_ea)}")

# Cross-reference analysis
xrefs = idautils.XrefsTo(address)
for xref in xrefs:
    print(f"Reference from {hex(xref.frm)}")

# Ghidra scripting
from ghidra.program.model.listing import *
from ghidra.program.model.symbol import *

# Get all functions
fm = currentProgram.getFunctionManager()
functions = fm.getFunctions(True)
for func in functions:
    print(f"Function: {func.getName()}")</code></pre>

        <h4>Cryptographic Analysis</h4>
        <pre><code># Crypto constant identification
# Look for known constants:
# AES S-box: 0x63, 0x7C, 0x77, 0x7B...
# MD5 constants: 0x67452301, 0xEFCDAB89...
# SHA constants: 0x67452301, 0xEFCDAB89...

# Entropy analysis for crypto
python -c "
import math
from collections import Counter

def entropy(data):
    counter = Counter(data)
    length = len(data)
    return -sum((count/length) * math.log2(count/length) 
                for count in counter.values())

with open('malware.exe', 'rb') as f:
    data = f.read()
    print(f'Entropy: {entropy(data)}')"

# Key extraction
# Look for hardcoded keys
# XOR key patterns
# Base64 encoded data</code></pre>

        <h3>Dynamic Reverse Engineering</h3>
        <h4>Runtime Analysis</h4>
        <pre><code># API hooking
# Detours library (Microsoft)
# EasyHook
# Microsoft Detours

# DLL injection
# SetWindowsHookEx
# CreateRemoteThread + LoadLibrary
# Manual DLL mapping

# Code injection monitoring
# Process Hollowing detection
# DLL injection detection
# Reflective DLL loading</code></pre>

        <h4>Kernel-Level Analysis</h4>
        <pre><code># Kernel debugging
# WinDbg for Windows kernel
# GDB for Linux kernel
# VMware + WinDbg setup

# Rootkit analysis
# GMER
# RootkitRevealer
# Malware Defender

# Driver analysis
# IDA Pro with kernel modules
# Ghidra kernel analysis
# Static driver verifier</code></pre>

        <h3>Malware Family Analysis</h3>
        <h4>Similarity Analysis</h4>
        <pre><code># Binary similarity
ssdeep malware1.exe malware2.exe  # Fuzzy hashing
imphash  # Import hash comparison
tlsh  # Trend Micro Locality Sensitive Hash

# Code similarity
bindiff  # Binary diffing tool
diaphora  # IDA Pro plugin for diffing

# Behavioral similarity
# API call sequences
# Network patterns
# File operations</code></pre>

        <h4>Attribution Analysis</h4>
        <pre><code># Malware attribution factors
# Code reuse patterns
# Compilation timestamps
# Language artifacts
# Coding style analysis
# Infrastructure overlap
# TTPs (Tactics, Techniques, Procedures)

# YARA rules for attribution
rule APT_Group_Signature {
    meta:
        author = "Analyst"
        description = "APT group signature"
    strings:
        $unique_string = "specific_malware_string"
        $api_sequence = { 68 ?? ?? ?? ?? E8 ?? ?? ?? ?? }
    condition:
        $unique_string and $api_sequence
}</code></pre>

        <h3>Automated Analysis Tools</h3>
        <h4>Machine Learning in Malware Analysis</h4>
        <pre><code># Feature extraction
# PE header features
# API call sequences
# Opcode n-grams
# String features

# Classification models
# Random Forest
# Support Vector Machines
# Neural Networks
# Deep Learning

# Tools and frameworks
# Ember (Endgame)
# MalConv
# DREBIN (Android)
# Cuckoo ML</code></pre>

        <h4>Threat Intelligence Integration</h4>
        <pre><code># IOC extraction
# File hashes
# Network indicators
# Registry keys
# Mutex names
# File paths

# STIX/TAXII integration
# Structured threat information
# Automated sharing
# Intelligence feeds

# MITRE ATT&CK mapping
# Technique identification
# Tactic classification
# Procedure documentation</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "APT Malware Family Analysis",
    description: "Conduct comprehensive analysis of an Advanced Persistent Threat (APT) malware family including static analysis, dynamic analysis, and reverse engineering to understand capabilities and develop countermeasures.",
    environment: "Isolated malware analysis lab with Windows and Linux VMs, analysis tools, and sandbox environments",
    tasks: [
      {
        category: "Static Analysis",
        tasks: [
          {
            task: "Perform initial triage and file analysis",
            method: "Hash analysis, file format examination, and packer detection",
            expectedFindings: "Malware family identification and basic characteristics",
            points: 20
          },
          {
            task: "Extract and analyze embedded strings and configuration",
            method: "String extraction, deobfuscation, and configuration parsing",
            expectedFindings: "C&C servers, encryption keys, and operational parameters",
            points: 25
          }
        ]
      },
      {
        category: "Dynamic Analysis",
        tasks: [
          {
            task: "Execute malware in controlled sandbox environment",
            method: "Behavioral monitoring and network traffic analysis",
            expectedFindings: "Runtime behavior, network communications, and persistence mechanisms",
            points: 25
          },
          {
            task: "Analyze memory artifacts and process injection",
            method: "Memory forensics and injection detection techniques",
            expectedFindings: "Memory-resident components and evasion techniques",
            points: 15
          }
        ]
      },
      {
        category: "Reverse Engineering",
        tasks: [
          {
            task: "Reverse engineer core malware functionality",
            method: "Disassembly, decompilation, and code analysis",
            expectedFindings: "Detailed understanding of malware capabilities and algorithms",
            points: 15
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive malware analysis report with technical details",
      "IOC extraction and threat intelligence documentation",
      "YARA rules for malware family detection",
      "Reverse engineering documentation with code analysis",
      "Countermeasure recommendations and detection strategies",
      "Attribution analysis and APT group assessment"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which analysis technique examines malware without executing it?",
        options: [
          "Dynamic analysis",
          "Static analysis",
          "Behavioral analysis",
          "Runtime analysis"
        ],
        correct: 1,
        explanation: "Static analysis examines malware code and structure without executing it, providing insights into potential capabilities."
      },
      {
        question: "What is the primary purpose of malware packing?",
        options: [
          "Reduce file size",
          "Improve performance",
          "Evade detection",
          "Add functionality"
        ],
        correct: 2,
        explanation: "Malware packing is primarily used to evade detection by antivirus software and analysis tools."
      },
      {
        question: "Which tool is commonly used for Windows memory forensics?",
        options: [
          "Wireshark",
          "IDA Pro",
          "Volatility",
          "Burp Suite"
        ],
        correct: 2,
        explanation: "Volatility is the most widely used framework for memory forensics and analysis of memory dumps."
      }
    ],
    practicalTasks: [
      {
        task: "Perform static analysis on a packed malware sample and extract configuration",
        points: 25
      },
      {
        task: "Conduct dynamic analysis in sandbox environment and document behavioral patterns",
        points: 25
      },
      {
        task: "Reverse engineer malware functionality and create detection signatures",
        points: 25
      },
      {
        task: "Develop comprehensive threat intelligence report with IOCs and countermeasures",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "Practical Malware Analysis",
      url: "https://nostarch.com/malware",
      type: "book"
    },
    {
      title: "Volatility Framework",
      url: "https://www.volatilityfoundation.org/",
      type: "tool"
    },
    {
      title: "YARA Documentation",
      url: "https://yara.readthedocs.io/",
      type: "documentation"
    }
  ],
  tags: ["malware-analysis", "reverse-engineering", "static-analysis", "dynamic-analysis", "threat-intelligence"],
  lastUpdated: "2024-01-15"
};
