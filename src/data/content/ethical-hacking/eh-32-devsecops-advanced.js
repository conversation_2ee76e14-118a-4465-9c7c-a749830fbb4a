/**
 * Ethical Hacking Module: Advanced DevSecOps and Container Security
 * Module ID: eh-32
 */

export const dev<PERSON><PERSON><PERSON>psAdvancedContent = {
  id: "eh-32",
  title: "Advanced DevSecOps and Container Security",
  description: "Master advanced DevSecOps practices including container security, Kubernetes security testing, infrastructure as code security, and cloud-native application security.",
  difficulty: "Expert",
  estimatedTime: 115,
  objectives: [
    "Master advanced container and Kubernetes security testing",
    "Learn infrastructure as code security assessment",
    "Develop skills in cloud-native application security",
    "Understand advanced CI/CD security integration",
    "Apply DevSecOps security testing in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-18", "eh-22", "eh-24"],
  sections: [
    {
      title: "Advanced Container Security",
      content: `
        <h2>Container Security Deep Dive</h2>
        <p>Advanced container security requires comprehensive testing of container images, runtime security, and orchestration platforms.</p>

        <h3>Container Image Security Analysis</h3>
        <h4>Advanced Image Scanning</h4>
        <pre><code># Multi-layer container security scanning
import docker
import json
import subprocess

class AdvancedContainerScanner:
    def __init__(self):
        self.docker_client = docker.from_env()
        self.scan_results = {}

    def comprehensive_image_scan(self, image_name):
        # Pull image for analysis
        image = self.docker_client.images.pull(image_name)

        # Multiple scanning approaches
        results = {
            'trivy_scan': self.trivy_scan(image_name),
            'clair_scan': self.clair_scan(image_name),
            'anchore_scan': self.anchore_scan(image_name),
            'dockerfile_analysis': self.analyze_dockerfile(image_name),
            'layer_analysis': self.analyze_layers(image),
            'secret_detection': self.detect_secrets(image_name)
        }

        return results

    def trivy_scan(self, image_name):
        cmd = ['trivy', 'image', '--format', 'json', image_name]
        result = subprocess.run(cmd, capture_output=True, text=True)
        return json.loads(result.stdout) if result.returncode == 0 else None

    def analyze_dockerfile(self, image_name):
        # Dockerfile security analysis
        dockerfile_rules = [
            {
                'rule': 'Running as root user',
                'pattern': r'USER\s+root',
                'severity': 'HIGH'
            },
            {
                'rule': 'Hardcoded secrets',
                'pattern': r'(password|secret|key)\s*=\s*["\'][^"\']+["\']',
                'severity': 'CRITICAL'
            },
            {
                'rule': 'Using latest tag',
                'pattern': r'FROM\s+[^:]+:latest',
                'severity': 'MEDIUM'
            },
            {
                'rule': 'Unnecessary packages',
                'pattern': r'(curl|wget|nc|netcat)',
                'severity': 'LOW'
            }
        ]

        # Analyze Dockerfile against rules
        violations = []
        # Implementation would check Dockerfile content
        return violations

    def detect_secrets(self, image_name):
        # Secret detection in container layers
        cmd = ['docker', 'run', '--rm', '-v', '/var/run/docker.sock:/var/run/docker.sock',
               'trufflesecurity/trufflehog:latest', 'docker', '--image', image_name]

        result = subprocess.run(cmd, capture_output=True, text=True)
        return self.parse_trufflehog_output(result.stdout)

# Container runtime security testing
class RuntimeSecurityTester:
    def __init__(self):
        self.test_results = {}

    def test_container_escape(self, container_id):
        # Test various container escape techniques
        escape_tests = [
            self.test_privileged_escape,
            self.test_capability_escape,
            self.test_mount_escape,
            self.test_proc_escape,
            self.test_cgroup_escape
        ]

        results = {}
        for test in escape_tests:
            try:
                results[test.__name__] = test(container_id)
            except Exception as e:
                results[test.__name__] = f"Error: {str(e)}"

        return results

    def test_privileged_escape(self, container_id):
        # Test privileged container escape
        # Mount host filesystem and chroot
        commands = [
            "mkdir /host",
            "mount /dev/sda1 /host",
            "chroot /host"
        ]

        for cmd in commands:
            result = self.execute_in_container(container_id, cmd)
            if result.returncode != 0:
                return "Privileged escape blocked"

        return "Privileged escape possible"

    def test_capability_escape(self, container_id):
        # Test capability-based escapes
        # CAP_SYS_ADMIN, CAP_SYS_PTRACE, etc.
        pass

    def execute_in_container(self, container_id, command):
        cmd = ['docker', 'exec', container_id] + command.split()
        return subprocess.run(cmd, capture_output=True, text=True)</code></pre>

        <h3>Kubernetes Security Testing</h3>
        <h4>Cluster Security Assessment</h4>
        <pre><code># Kubernetes security testing framework
from kubernetes import client, config
import yaml

class KubernetesSecurityTester:
    def __init__(self):
        config.load_kube_config()
        self.v1 = client.CoreV1Api()
        self.apps_v1 = client.AppsV1Api()
        self.rbac_v1 = client.RbacAuthorizationV1Api()

    def comprehensive_cluster_assessment(self):
        assessment = {
            'pod_security': self.assess_pod_security(),
            'rbac_analysis': self.analyze_rbac(),
            'network_policies': self.check_network_policies(),
            'secrets_management': self.audit_secrets(),
            'admission_controllers': self.test_admission_controllers(),
            'node_security': self.assess_node_security()
        }

        return assessment

    def assess_pod_security(self):
        pods = self.v1.list_pod_for_all_namespaces()
        security_issues = []

        for pod in pods.items:
            issues = self.analyze_pod_security(pod)
            if issues:
                security_issues.append({
                    'pod': pod.metadata.name,
                    'namespace': pod.metadata.namespace,
                    'issues': issues
                })

        return security_issues

    def analyze_pod_security(self, pod):
        issues = []

        for container in pod.spec.containers:
            # Check for privileged containers
            if container.security_context and container.security_context.privileged:
                issues.append("Privileged container detected")

            # Check for root user
            if (container.security_context and
                container.security_context.run_as_user == 0):
                issues.append("Container running as root")

            # Check for dangerous capabilities
            if (container.security_context and
                container.security_context.capabilities and
                container.security_context.capabilities.add):
                dangerous_caps = ['SYS_ADMIN', 'NET_ADMIN', 'SYS_PTRACE']
                for cap in container.security_context.capabilities.add:
                    if cap in dangerous_caps:
                        issues.append(f"Dangerous capability: {cap}")

            # Check for host network/PID
            if pod.spec.host_network:
                issues.append("Host network enabled")

            if pod.spec.host_pid:
                issues.append("Host PID namespace enabled")

        return issues

    def analyze_rbac(self):
        # RBAC security analysis
        rbac_issues = []

        # Check for overprivileged roles
        cluster_roles = self.rbac_v1.list_cluster_role()

        for role in cluster_roles.items:
            if self.is_overprivileged_role(role):
                rbac_issues.append({
                    'role': role.metadata.name,
                    'issue': 'Overprivileged cluster role',
                    'permissions': role.rules
                })

        return rbac_issues

    def is_overprivileged_role(self, role):
        # Check for dangerous permissions
        dangerous_permissions = [
            {'verbs': ['*'], 'resources': ['*']},
            {'verbs': ['create'], 'resources': ['pods/exec']},
            {'verbs': ['*'], 'resources': ['secrets']},
            {'verbs': ['escalate'], 'resources': ['*']}
        ]

        for rule in role.rules:
            for dangerous in dangerous_permissions:
                if (set(dangerous['verbs']).issubset(set(rule.verbs or [])) and
                    set(dangerous['resources']).issubset(set(rule.resources or []))):
                    return True

        return False</code></pre>
      `,
      type: "text"
    },
    {
      title: "Infrastructure as Code Security",
      content: `
        <h2>IaC Security Assessment</h2>
        <p>Infrastructure as Code security requires comprehensive testing of configuration templates, policy validation, and deployment security.</p>

        <h3>Terraform Security Testing</h3>
        <h4>Terraform Configuration Analysis</h4>
        <pre><code># Terraform security scanner
import hcl2
import json

class TerraformSecurityScanner:
    def __init__(self):
        self.security_rules = self.load_security_rules()

    def scan_terraform_files(self, directory):
        findings = []

        for tf_file in self.find_terraform_files(directory):
            with open(tf_file, 'r') as f:
                try:
                    tf_config = hcl2.load(f)
                    file_findings = self.analyze_configuration(tf_config, tf_file)
                    findings.extend(file_findings)
                except Exception as e:
                    findings.append({
                        'file': tf_file,
                        'severity': 'ERROR',
                        'message': f"Parse error: {str(e)}"
                    })

        return findings

    def analyze_configuration(self, config, filename):
        findings = []

        # Analyze resources
        for resource_type, resources in config.get('resource', {}).items():
            for resource_name, resource_config in resources.items():
                resource_findings = self.check_resource_security(
                    resource_type, resource_name, resource_config, filename
                )
                findings.extend(resource_findings)

        return findings

    def check_resource_security(self, resource_type, name, config, filename):
        findings = []

        # AWS S3 bucket security checks
        if resource_type == 'aws_s3_bucket':
            if not config.get('versioning', {}).get('enabled'):
                findings.append({
                    'file': filename,
                    'resource': f"{resource_type}.{name}",
                    'severity': 'MEDIUM',
                    'message': 'S3 bucket versioning not enabled'
                })

            if config.get('acl') == 'public-read':
                findings.append({
                    'file': filename,
                    'resource': f"{resource_type}.{name}",
                    'severity': 'HIGH',
                    'message': 'S3 bucket has public read access'
                })

        # AWS Security Group checks
        elif resource_type == 'aws_security_group':
            for rule in config.get('ingress', []):
                if (rule.get('from_port') == 22 and
                    '0.0.0.0/0' in rule.get('cidr_blocks', [])):
                    findings.append({
                        'file': filename,
                        'resource': f"{resource_type}.{name}",
                        'severity': 'CRITICAL',
                        'message': 'SSH access open to the world'
                    })

        return findings

# CloudFormation security testing
class CloudFormationScanner:
    def __init__(self):
        self.cfn_rules = self.load_cfn_rules()

    def scan_cloudformation_template(self, template_file):
        with open(template_file, 'r') as f:
            if template_file.endswith('.json'):
                template = json.load(f)
            else:  # YAML
                import yaml
                template = yaml.safe_load(f)

        findings = []

        # Analyze resources
        for resource_name, resource in template.get('Resources', {}).items():
            resource_findings = self.check_cfn_resource(
                resource_name, resource, template_file
            )
            findings.extend(resource_findings)

        return findings</code></pre>

        <h3>Policy as Code Testing</h3>
        <h4>OPA/Rego Policy Testing</h4>
        <pre><code># Open Policy Agent (OPA) testing
import subprocess
import json

class OPAPolicyTester:
    def __init__(self, policy_directory):
        self.policy_dir = policy_directory

    def test_policies(self, test_cases):
        results = []

        for test_case in test_cases:
            result = self.evaluate_policy(
                test_case['policy'],
                test_case['input'],
                test_case['expected']
            )
            results.append(result)

        return results

    def evaluate_policy(self, policy_name, input_data, expected_result):
        # Create input file
        input_file = f"/tmp/input_{policy_name}.json"
        with open(input_file, 'w') as f:
            json.dump(input_data, f)

        # Run OPA evaluation
        cmd = [
            'opa', 'eval',
            '-d', self.policy_dir,
            '-i', input_file,
            f'data.{policy_name}.allow'
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            output = json.loads(result.stdout)
            actual_result = output['result'][0]['expressions'][0]['value']

            return {
                'policy': policy_name,
                'input': input_data,
                'expected': expected_result,
                'actual': actual_result,
                'passed': actual_result == expected_result
            }
        else:
            return {
                'policy': policy_name,
                'error': result.stderr,
                'passed': False
            }

# Example Rego policy for Kubernetes
kubernetes_security_policy = '''
package kubernetes.security

# Deny privileged containers
deny[msg] {
    input.kind == "Pod"
    input.spec.containers[_].securityContext.privileged == true
    msg := "Privileged containers are not allowed"
}

# Require resource limits
deny[msg] {
    input.kind == "Pod"
    container := input.spec.containers[_]
    not container.resources.limits.memory
    msg := "Memory limits are required"
}

# Deny host network
deny[msg] {
    input.kind == "Pod"
    input.spec.hostNetwork == true
    msg := "Host network is not allowed"
}
'''</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Cloud-Native DevSecOps Security Assessment",
    description: "Conduct comprehensive DevSecOps security assessment including container security, Kubernetes cluster testing, and infrastructure as code security validation.",
    environment: "Cloud-native environment with Kubernetes clusters, container registries, CI/CD pipelines, and IaC templates",
    tasks: [
      {
        category: "Container Security",
        tasks: [
          {
            task: "Perform advanced container image security scanning",
            method: "Multi-tool scanning, secret detection, and vulnerability analysis",
            expectedFindings: "Container vulnerabilities, secrets, and security misconfigurations",
            points: 25
          },
          {
            task: "Test container runtime security and escape techniques",
            method: "Privileged container testing and escape attempt validation",
            expectedFindings: "Container escape vulnerabilities and runtime security gaps",
            points: 20
          }
        ]
      },
      {
        category: "Kubernetes Security",
        tasks: [
          {
            task: "Conduct comprehensive Kubernetes cluster security assessment",
            method: "RBAC analysis, pod security testing, and network policy validation",
            expectedFindings: "Kubernetes security misconfigurations and privilege escalation paths",
            points: 30
          }
        ]
      },
      {
        category: "Infrastructure as Code",
        tasks: [
          {
            task: "Assess IaC security and policy compliance",
            method: "Terraform/CloudFormation scanning and OPA policy testing",
            expectedFindings: "IaC security violations and policy compliance gaps",
            points: 25
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive DevSecOps security assessment report",
      "Container security analysis with vulnerability remediation",
      "Kubernetes cluster security evaluation and hardening guide",
      "Infrastructure as Code security findings and policy recommendations",
      "CI/CD pipeline security integration framework",
      "Automated security testing tools and configurations"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which container security practice is most effective for preventing privilege escalation?",
        options: [
          "Image scanning only",
          "Running as non-root user",
          "Network segmentation",
          "Regular updates"
        ],
        correct: 1,
        explanation: "Running containers as non-root users is one of the most effective practices for preventing privilege escalation attacks."
      },
      {
        question: "What is the primary security risk of privileged containers in Kubernetes?",
        options: [
          "Resource consumption",
          "Container escape to host",
          "Network performance",
          "Storage limitations"
        ],
        correct: 1,
        explanation: "Privileged containers have access to all host devices and capabilities, making container escape to the host system possible."
      },
      {
        question: "Which tool is most commonly used for Infrastructure as Code security scanning?",
        options: [
          "Terraform",
          "Checkov",
          "Ansible",
          "CloudFormation"
        ],
        correct: 1,
        explanation: "Checkov is a popular static analysis tool specifically designed for scanning Infrastructure as Code templates for security misconfigurations."
      }
    ],
    practicalTasks: [
      {
        task: "Perform comprehensive container security scanning and vulnerability assessment",
        points: 25
      },
      {
        task: "Conduct Kubernetes cluster security testing and RBAC analysis",
        points: 25
      },
      {
        task: "Assess Infrastructure as Code security and policy compliance",
        points: 25
      },
      {
        task: "Implement advanced DevSecOps security automation in CI/CD pipeline",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "CIS Kubernetes Benchmark",
      url: "https://www.cisecurity.org/benchmark/kubernetes",
      type: "benchmark"
    },
    {
      title: "OWASP Container Security Top 10",
      url: "https://owasp.org/www-project-container-security/",
      type: "guide"
    },
    {
      title: "Kubernetes Security Best Practices",
      url: "https://kubernetes.io/docs/concepts/security/",
      type: "documentation"
    }
  ],
  tags: ["devsecops", "container-security", "kubernetes", "infrastructure-as-code", "cloud-native"],
  lastUpdated: "2024-01-15"
};