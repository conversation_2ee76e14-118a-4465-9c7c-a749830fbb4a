/**
 * Ethical Hacking Module: AI/ML Security Testing
 * Module ID: eh-29
 */

export const aiMlSecurityContent = {
  id: "eh-29",
  title: "AI/ML Security Testing",
  description: "Master AI and machine learning security assessment including adversarial attacks, model poisoning, privacy attacks, and AI system security testing.",
  difficulty: "Expert",
  estimatedTime: 120,
  objectives: [
    "Understand AI/ML security threats and attack vectors",
    "Master adversarial attack techniques and defenses",
    "Learn model poisoning and data manipulation attacks",
    "Develop skills in AI privacy and inference attacks",
    "Apply AI/ML security testing in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-14", "eh-22", "eh-23"],
  sections: [
    {
      title: "AI/ML Security Fundamentals",
      content: `
        <h2>AI/ML Security Landscape</h2>
        <p>AI and machine learning systems introduce unique security challenges requiring specialized testing approaches for model integrity, data privacy, and system robustness.</p>

        <h3>AI/ML Attack Surface</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Component</th>
              <th>Attack Vectors</th>
              <th>Impact</th>
              <th>Examples</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Training Data</td>
              <td>Data poisoning, backdoor injection</td>
              <td>Model corruption, biased decisions</td>
              <td>Malicious training samples, label flipping</td>
            </tr>
            <tr>
              <td>Model Architecture</td>
              <td>Model extraction, inversion attacks</td>
              <td>IP theft, privacy violation</td>
              <td>API abuse, gradient analysis</td>
            </tr>
            <tr>
              <td>Inference Engine</td>
              <td>Adversarial examples, evasion</td>
              <td>Misclassification, system bypass</td>
              <td>Crafted inputs, perturbation attacks</td>
            </tr>
            <tr>
              <td>Model Updates</td>
              <td>Model replacement, version attacks</td>
              <td>System compromise, backdoors</td>
              <td>Supply chain attacks, malicious updates</td>
            </tr>
            <tr>
              <td>Infrastructure</td>
              <td>Traditional security vulnerabilities</td>
              <td>Data breach, system compromise</td>
              <td>API vulnerabilities, access control flaws</td>
            </tr>
          </tbody>
        </table>

        <h3>AI/ML Threat Categories</h3>
        <ul>
          <li><strong>Adversarial Attacks</strong> - Crafted inputs to fool models</li>
          <li><strong>Data Poisoning</strong> - Malicious training data injection</li>
          <li><strong>Model Extraction</strong> - Stealing model parameters or functionality</li>
          <li><strong>Privacy Attacks</strong> - Extracting sensitive information from models</li>
          <li><strong>Backdoor Attacks</strong> - Hidden triggers in models</li>
          <li><strong>Evasion Attacks</strong> - Bypassing detection systems</li>
        </ul>

        <h3>AI/ML Security Testing Methodology</h3>
        <ol>
          <li><strong>Model Architecture Review</strong> - Design and implementation analysis</li>
          <li><strong>Data Pipeline Security</strong> - Training and inference data validation</li>
          <li><strong>Adversarial Testing</strong> - Robustness against crafted inputs</li>
          <li><strong>Privacy Assessment</strong> - Information leakage evaluation</li>
          <li><strong>Infrastructure Security</strong> - Traditional security testing</li>
          <li><strong>Bias and Fairness Testing</strong> - Ethical AI evaluation</li>
        </ol>
      `,
      type: "text"
    },
    {
      title: "Adversarial Attacks",
      content: `
        <h2>Adversarial Machine Learning</h2>
        <p>Adversarial attacks involve crafting malicious inputs designed to fool machine learning models into making incorrect predictions.</p>

        <h3>Adversarial Attack Types</h3>
        <h4>White-box Attacks</h4>
        <pre><code># FGSM (Fast Gradient Sign Method) Attack
import torch
import torch.nn.functional as F

def fgsm_attack(model, data, target, epsilon=0.1):
    # Enable gradient computation
    data.requires_grad = True

    # Forward pass
    output = model(data)
    loss = F.cross_entropy(output, target)

    # Backward pass
    model.zero_grad()
    loss.backward()

    # Generate adversarial example
    data_grad = data.grad.data
    perturbed_data = data + epsilon * data_grad.sign()

    return torch.clamp(perturbed_data, 0, 1)

# PGD (Projected Gradient Descent) Attack
def pgd_attack(model, data, target, epsilon=0.1, alpha=0.01, iterations=10):
    perturbed_data = data.clone()

    for i in range(iterations):
        perturbed_data.requires_grad = True
        output = model(perturbed_data)
        loss = F.cross_entropy(output, target)

        model.zero_grad()
        loss.backward()

        # Update with gradient
        data_grad = perturbed_data.grad.data
        perturbed_data = perturbed_data + alpha * data_grad.sign()

        # Project back to epsilon ball
        eta = torch.clamp(perturbed_data - data, -epsilon, epsilon)
        perturbed_data = torch.clamp(data + eta, 0, 1).detach()

    return perturbed_data</code></pre>

        <h4>Black-box Attacks</h4>
        <pre><code># Query-based black-box attack
import numpy as np
from scipy.optimize import minimize

class BlackBoxAttack:
    def __init__(self, model_api, target_class):
        self.model_api = model_api
        self.target_class = target_class
        self.query_count = 0

    def query_model(self, x):
        self.query_count += 1
        return self.model_api(x)

    def boundary_attack(self, original_image, max_queries=1000):
        # Start with random noise
        adversarial = np.random.uniform(0, 1, original_image.shape)

        for i in range(max_queries):
            # Generate candidate
            candidate = self.generate_candidate(adversarial, original_image)

            # Query model
            prediction = self.query_model(candidate)

            # Update if better
            if np.argmax(prediction) == self.target_class:
                adversarial = candidate

            if self.query_count >= max_queries:
                break

        return adversarial

    def generate_candidate(self, current, original):
        # Generate candidate near decision boundary
        direction = np.random.normal(0, 1, current.shape)
        candidate = current + 0.01 * direction
        return np.clip(candidate, 0, 1)</code></pre>

        <h3>Adversarial Defense Testing</h3>
        <h4>Defense Evaluation</h4>
        <pre><code># Test adversarial training defense
def test_adversarial_training(model, test_loader, attack_method):
    correct_clean = 0
    correct_adversarial = 0
    total = 0

    for data, target in test_loader:
        # Test on clean examples
        output_clean = model(data)
        pred_clean = output_clean.argmax(dim=1)
        correct_clean += pred_clean.eq(target).sum().item()

        # Generate adversarial examples
        adversarial_data = attack_method(model, data, target)

        # Test on adversarial examples
        output_adv = model(adversarial_data)
        pred_adv = output_adv.argmax(dim=1)
        correct_adversarial += pred_adv.eq(target).sum().item()

        total += target.size(0)

    clean_accuracy = correct_clean / total
    robust_accuracy = correct_adversarial / total

    return clean_accuracy, robust_accuracy

# Test gradient masking defense
def test_gradient_masking(model, data, target):
    # Check if gradients are meaningful
    data.requires_grad = True
    output = model(data)
    loss = F.cross_entropy(output, target)
    loss.backward()

    gradient_norm = torch.norm(data.grad.data)

    # Low gradient norm might indicate masking
    if gradient_norm < 0.001:
        print("Warning: Potential gradient masking detected")

    return gradient_norm</code></pre>
      `,
      type: "text"
    },
    {
      title: "Model Extraction and Privacy Attacks",
      content: `
        <h2>AI Privacy and Model Security</h2>
        <p>Privacy attacks target sensitive information in AI models, while model extraction attacks steal intellectual property and model functionality.</p>

        <h3>Model Extraction Attacks</h3>
        <h4>API-based Model Stealing</h4>
        <pre><code># Model extraction through API queries
import numpy as np
from sklearn.ensemble import RandomForestClassifier

class ModelExtractionAttack:
    def __init__(self, target_api, input_shape):
        self.target_api = target_api
        self.input_shape = input_shape
        self.stolen_model = RandomForestClassifier()
        self.query_budget = 10000

    def extract_model(self):
        # Generate synthetic queries
        X_synthetic = self.generate_synthetic_data()

        # Query target model
        y_synthetic = []
        for x in X_synthetic:
            if len(y_synthetic) >= self.query_budget:
                break
            prediction = self.target_api(x.reshape(1, -1))
            y_synthetic.append(np.argmax(prediction))

        # Train surrogate model
        self.stolen_model.fit(X_synthetic[:len(y_synthetic)], y_synthetic)

        return self.stolen_model

    def generate_synthetic_data(self):
        # Generate diverse synthetic inputs
        return np.random.uniform(-1, 1, (self.query_budget, *self.input_shape))

    def evaluate_extraction(self, test_data, test_labels):
        # Compare stolen model with original
        original_predictions = []
        stolen_predictions = []

        for x, y in zip(test_data, test_labels):
            orig_pred = self.target_api(x.reshape(1, -1))
            stolen_pred = self.stolen_model.predict(x.reshape(1, -1))

            original_predictions.append(np.argmax(orig_pred))
            stolen_predictions.append(stolen_pred[0])

        # Calculate agreement rate
        agreement = np.mean(np.array(original_predictions) == np.array(stolen_predictions))
        return agreement</code></pre>

        <h3>Privacy Attacks</h3>
        <h4>Membership Inference Attacks</h4>
        <pre><code># Membership inference attack
class MembershipInferenceAttack:
    def __init__(self, target_model, shadow_models):
        self.target_model = target_model
        self.shadow_models = shadow_models
        self.attack_model = None

    def train_attack_model(self, shadow_data, shadow_labels, member_flags):
        # Extract features from shadow models
        features = []
        labels = []

        for i, (data, label, is_member) in enumerate(zip(shadow_data, shadow_labels, member_flags)):
            # Get prediction confidence
            prediction = self.shadow_models[i % len(self.shadow_models)](data)
            confidence = np.max(prediction)
            entropy = -np.sum(prediction * np.log(prediction + 1e-10))

            features.append([confidence, entropy])
            labels.append(is_member)

        # Train binary classifier
        from sklearn.linear_model import LogisticRegression
        self.attack_model = LogisticRegression()
        self.attack_model.fit(features, labels)

    def infer_membership(self, data, label):
        # Get target model prediction
        prediction = self.target_model(data)
        confidence = np.max(prediction)
        entropy = -np.sum(prediction * np.log(prediction + 1e-10))

        # Predict membership
        membership_prob = self.attack_model.predict_proba([[confidence, entropy]])[0][1]
        return membership_prob > 0.5</code></pre>

        <h4>Model Inversion Attacks</h4>
        <pre><code># Model inversion attack
def model_inversion_attack(model, target_class, input_shape, iterations=1000):
    # Initialize random input
    x = torch.randn(1, *input_shape, requires_grad=True)
    optimizer = torch.optim.Adam([x], lr=0.01)

    for i in range(iterations):
        optimizer.zero_grad()

        # Forward pass
        output = model(x)

        # Loss: maximize confidence for target class
        loss = -F.log_softmax(output, dim=1)[0, target_class]

        # Add regularization
        loss += 0.01 * torch.norm(x)

        loss.backward()
        optimizer.step()

        # Clamp to valid range
        x.data = torch.clamp(x.data, 0, 1)

    return x.detach()

# Property inference attack
class PropertyInferenceAttack:
    def __init__(self, target_model):
        self.target_model = target_model
        self.property_classifier = None

    def train_property_classifier(self, models_with_property, models_without_property):
        # Extract features from models
        features = []
        labels = []

        # Models with property
        for model in models_with_property:
            feature = self.extract_model_features(model)
            features.append(feature)
            labels.append(1)

        # Models without property
        for model in models_without_property:
            feature = self.extract_model_features(model)
            features.append(feature)
            labels.append(0)

        # Train classifier
        from sklearn.svm import SVC
        self.property_classifier = SVC()
        self.property_classifier.fit(features, labels)

    def extract_model_features(self, model):
        # Extract statistical features from model behavior
        test_inputs = torch.randn(100, 3, 32, 32)  # Example for CIFAR-10
        predictions = model(test_inputs)

        # Statistical features
        mean_confidence = torch.mean(torch.max(F.softmax(predictions, dim=1), dim=1)[0])
        prediction_entropy = torch.mean(-torch.sum(F.softmax(predictions, dim=1) *
                                                  F.log_softmax(predictions, dim=1), dim=1))

        return [mean_confidence.item(), prediction_entropy.item()]</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "AI/ML Security Assessment of Image Classification System",
    description: "Conduct comprehensive AI/ML security testing of an image classification system including adversarial attacks, model extraction, and privacy assessment.",
    environment: "Deep learning environment with image classification models, datasets, and AI security testing tools",
    tasks: [
      {
        category: "Adversarial Testing",
        tasks: [
          {
            task: "Generate adversarial examples using multiple attack methods",
            method: "FGSM, PGD, and black-box attacks against image classifier",
            expectedFindings: "Successful adversarial examples causing misclassification",
            points: 30
          }
        ]
      },
      {
        category: "Model Extraction",
        tasks: [
          {
            task: "Perform model extraction attack via API queries",
            method: "Query-based model stealing and surrogate model training",
            expectedFindings: "Functional surrogate model with high agreement rate",
            points: 25
          }
        ]
      },
      {
        category: "Privacy Assessment",
        tasks: [
          {
            task: "Execute membership inference and model inversion attacks",
            method: "Privacy attack implementation and sensitive data extraction",
            expectedFindings: "Privacy vulnerabilities and data leakage demonstration",
            points: 25
          }
        ]
      },
      {
        category: "Defense Evaluation",
        tasks: [
          {
            task: "Test and evaluate AI security defenses",
            method: "Defense mechanism testing and robustness evaluation",
            expectedFindings: "Defense effectiveness analysis and bypass techniques",
            points: 20
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive AI/ML security assessment report",
      "Adversarial attack demonstrations and success rates",
      "Model extraction results and intellectual property risks",
      "Privacy attack analysis and data leakage evidence",
      "Defense evaluation and security recommendations",
      "AI security testing framework and automated tools"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which attack method generates adversarial examples by adding small perturbations in the direction of the gradient?",
        options: [
          "PGD (Projected Gradient Descent)",
          "FGSM (Fast Gradient Sign Method)",
          "C&W (Carlini & Wagner)",
          "DeepFool"
        ],
        correct: 1,
        explanation: "FGSM generates adversarial examples by adding small perturbations in the direction of the sign of the gradient to maximize the loss."
      },
      {
        question: "What is the primary goal of a membership inference attack?",
        options: [
          "Extract model parameters",
          "Generate adversarial examples",
          "Determine if data was used in training",
          "Steal model functionality"
        ],
        correct: 2,
        explanation: "Membership inference attacks aim to determine whether a specific data point was used in the training dataset of a machine learning model."
      },
      {
        question: "Which defense mechanism is most effective against gradient-based adversarial attacks?",
        options: [
          "Input preprocessing",
          "Adversarial training",
          "Model ensemble",
          "Gradient masking"
        ],
        correct: 1,
        explanation: "Adversarial training, which includes adversarial examples in the training process, is currently the most effective defense against gradient-based attacks."
      }
    ],
    practicalTasks: [
      {
        task: "Generate and evaluate adversarial examples using white-box and black-box attacks",
        points: 25
      },
      {
        task: "Perform model extraction attack and measure extraction success rate",
        points: 25
      },
      {
        task: "Execute membership inference attack and quantify privacy leakage",
        points: 25
      },
      {
        task: "Implement and test adversarial defenses against various attack methods",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "Adversarial Robustness Toolbox (ART)",
      url: "https://github.com/Trusted-AI/adversarial-robustness-toolbox",
      type: "tool"
    },
    {
      title: "CleverHans Library",
      url: "https://github.com/cleverhans-lab/cleverhans",
      type: "tool"
    },
    {
      title: "AI Security and Privacy Research",
      url: "https://www.ieee-security.org/TC/SPW2021/AISec/",
      type: "research"
    }
  ],
  tags: ["ai-security", "adversarial-attacks", "model-extraction", "privacy-attacks", "machine-learning"],
  lastUpdated: "2024-01-15"
};