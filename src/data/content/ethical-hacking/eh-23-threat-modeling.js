/**
 * Ethical Hacking Module: Threat Modeling and Risk Assessment
 * Module ID: eh-23
 */

export const threatModelingContent = {
  id: "eh-23",
  title: "Threat Modeling and Risk Assessment",
  description: "Master threat modeling methodologies, risk assessment frameworks, and security architecture analysis for comprehensive security evaluation.",
  difficulty: "Advanced",
  estimatedTime: 95,
  objectives: [
    "Understand threat modeling principles and methodologies",
    "Master STRIDE, PASTA, and other threat modeling frameworks",
    "Learn risk assessment and quantification techniques",
    "Develop skills in security architecture analysis",
    "Apply threat modeling in secure design and penetration testing"
  ],
  prerequisites: ["eh-1", "eh-20", "eh-21"],
  sections: [
    {
      title: "Threat Modeling Fundamentals",
      content: `
        <h2>Introduction to Threat Modeling</h2>
        <p>Threat modeling is a structured approach to identifying, quantifying, and addressing security threats in systems and applications.</p>
        
        <h3>Threat Modeling Benefits</h3>
        <ul>
          <li><strong>Proactive Security</strong> - Identify threats before implementation</li>
          <li><strong>Risk Prioritization</strong> - Focus on high-impact threats</li>
          <li><strong>Security by Design</strong> - Integrate security into development</li>
          <li><strong>Communication</strong> - Bridge technical and business teams</li>
          <li><strong>Compliance</strong> - Meet regulatory requirements</li>
        </ul>

        <h3>Threat Modeling Process</h3>
        <ol>
          <li><strong>Define Scope</strong> - Identify what to protect</li>
          <li><strong>Create Architecture Overview</strong> - Map system components</li>
          <li><strong>Decompose Application</strong> - Break down into elements</li>
          <li><strong>Identify Threats</strong> - Find potential attack vectors</li>
          <li><strong>Document Threats</strong> - Record threat details</li>
          <li><strong>Rate Threats</strong> - Assess risk and impact</li>
          <li><strong>Develop Countermeasures</strong> - Plan mitigation strategies</li>
        </ol>

        <h3>Threat Categories</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Category</th>
              <th>Description</th>
              <th>Examples</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Spoofing</td>
              <td>Impersonating someone or something else</td>
              <td>IP spoofing, email spoofing, identity theft</td>
            </tr>
            <tr>
              <td>Tampering</td>
              <td>Modifying data or code</td>
              <td>SQL injection, code injection, data corruption</td>
            </tr>
            <tr>
              <td>Repudiation</td>
              <td>Claiming to have not performed an action</td>
              <td>Denying transactions, log tampering</td>
            </tr>
            <tr>
              <td>Information Disclosure</td>
              <td>Exposing information to unauthorized users</td>
              <td>Data leaks, eavesdropping, reconnaissance</td>
            </tr>
            <tr>
              <td>Denial of Service</td>
              <td>Denying service to valid users</td>
              <td>DDoS attacks, resource exhaustion</td>
            </tr>
            <tr>
              <td>Elevation of Privilege</td>
              <td>Gaining capabilities without authorization</td>
              <td>Privilege escalation, unauthorized access</td>
            </tr>
          </tbody>
        </table>
      `,
      type: "text"
    },
    {
      title: "STRIDE Methodology",
      content: `
        <h2>STRIDE Threat Modeling</h2>
        <p>STRIDE is Microsoft's threat modeling methodology that categorizes threats into six types: Spoofing, Tampering, Repudiation, Information Disclosure, Denial of Service, and Elevation of Privilege.</p>

        <h3>STRIDE Analysis Process</h3>
        <h4>Data Flow Diagram Creation</h4>
        <pre><code># DFD Elements
External Entity - Users, systems outside control
Process - Code that processes data
Data Store - Files, databases, registries
Data Flow - Data movement between elements
Trust Boundary - Security/privilege boundaries

# DFD Example
[User] --login--> (Authentication) --token--> [Session Store]
[User] --request--> (Web Server) --query--> [Database]
[Admin] --config--> (Admin Panel) --update--> [Config Store]</code></pre>

        <h4>Threat Identification</h4>
        <pre><code># STRIDE per Element Type

External Entity Threats:
- Spoofing: Impersonating legitimate user
- Repudiation: Denying actions performed

Process Threats:
- Spoofing: Process impersonation
- Tampering: Code/memory modification
- Repudiation: Denying process execution
- Information Disclosure: Memory dumps, debug info
- Denial of Service: Process crashes, resource exhaustion
- Elevation of Privilege: Privilege escalation

Data Store Threats:
- Tampering: Data modification
- Repudiation: Denying data changes
- Information Disclosure: Unauthorized data access
- Denial of Service: Data corruption, unavailability

Data Flow Threats:
- Tampering: Data modification in transit
- Information Disclosure: Eavesdropping
- Denial of Service: Network disruption</code></pre>

        <h3>STRIDE Threat Examples</h3>
        <h4>Web Application STRIDE Analysis</h4>
        <pre><code># Login Process Threats

Spoofing:
- Weak authentication mechanisms
- Session hijacking
- Credential stuffing attacks

Tampering:
- SQL injection in login form
- Parameter manipulation
- Session token modification

Repudiation:
- Insufficient logging of login attempts
- Log tampering capabilities
- Anonymous access options

Information Disclosure:
- Username enumeration
- Password policy disclosure
- Error message information leakage

Denial of Service:
- Account lockout attacks
- Resource exhaustion via login attempts
- Database connection exhaustion

Elevation of Privilege:
- Authentication bypass
- Session fixation
- Privilege escalation post-login</code></pre>

        <h3>STRIDE Mitigation Strategies</h3>
        <h4>Countermeasures by Threat Type</h4>
        <pre><code># Spoofing Mitigations
- Strong authentication (MFA)
- Digital signatures
- Certificate validation
- Mutual authentication

# Tampering Mitigations
- Input validation
- Digital signatures
- Access controls
- Integrity checks

# Repudiation Mitigations
- Audit logging
- Digital signatures
- Timestamps
- Non-repudiation protocols

# Information Disclosure Mitigations
- Encryption
- Access controls
- Data classification
- Secure communication

# Denial of Service Mitigations
- Rate limiting
- Resource quotas
- Load balancing
- Redundancy

# Elevation of Privilege Mitigations
- Principle of least privilege
- Access controls
- Input validation
- Secure coding practices</code></pre>
      `,
      type: "text"
    },
    {
      title: "Risk Assessment Frameworks",
      content: `
        <h2>Risk Assessment and Quantification</h2>
        <p>Risk assessment provides quantitative and qualitative methods to evaluate and prioritize security threats based on likelihood and impact.</p>

        <h3>Risk Calculation Methods</h3>
        <h4>Qualitative Risk Assessment</h4>
        <pre><code># Risk Matrix
Risk = Likelihood × Impact

Likelihood Scale:
1 - Very Low (< 5% chance)
2 - Low (5-25% chance)
3 - Medium (25-75% chance)
4 - High (75-95% chance)
5 - Very High (> 95% chance)

Impact Scale:
1 - Very Low (minimal impact)
2 - Low (minor impact)
3 - Medium (moderate impact)
4 - High (significant impact)
5 - Very High (severe impact)

Risk Score = Likelihood × Impact
Risk Level:
1-4: Low Risk
5-12: Medium Risk
15-25: High Risk</code></pre>

        <h4>Quantitative Risk Assessment</h4>
        <pre><code># FAIR (Factor Analysis of Information Risk)

Risk = Loss Event Frequency × Loss Magnitude

Loss Event Frequency = Threat Event Frequency × Vulnerability
Loss Magnitude = Primary Loss + Secondary Loss

# Example Calculation
Asset Value: $1,000,000
Threat Event Frequency: 2 per year
Vulnerability: 30% (0.3)
Primary Loss: 20% of asset value
Secondary Loss: 10% of asset value

Loss Event Frequency = 2 × 0.3 = 0.6 per year
Primary Loss = $1,000,000 × 0.2 = $200,000
Secondary Loss = $1,000,000 × 0.1 = $100,000
Total Loss Magnitude = $300,000

Annual Loss Expectancy = 0.6 × $300,000 = $180,000</code></pre>

        <h3>CVSS Scoring</h3>
        <h4>Common Vulnerability Scoring System</h4>
        <pre><code># CVSS v3.1 Base Metrics

Attack Vector (AV):
- Network (N): 0.85
- Adjacent (A): 0.62
- Local (L): 0.55
- Physical (P): 0.2

Attack Complexity (AC):
- Low (L): 0.77
- High (H): 0.44

Privileges Required (PR):
- None (N): 0.85
- Low (L): 0.62/0.68
- High (H): 0.27/0.5

User Interaction (UI):
- None (N): 0.85
- Required (R): 0.62

Scope (S):
- Unchanged (U)
- Changed (C)

Confidentiality Impact (C):
- None (N): 0
- Low (L): 0.22
- High (H): 0.56

Integrity Impact (I):
- None (N): 0
- Low (L): 0.22
- High (H): 0.56

Availability Impact (A):
- None (N): 0
- Low (L): 0.22
- High (H): 0.56

# CVSS Score Calculation
Base Score = min(10, Impact × Exploitability)
Impact = 1 - [(1-C) × (1-I) × (1-A)]
Exploitability = 8.22 × AV × AC × PR × UI</code></pre>

        <h3>Business Risk Assessment</h3>
        <h4>Business Impact Analysis</h4>
        <pre><code># Business Impact Categories

Financial Impact:
- Direct costs (incident response, recovery)
- Lost revenue (downtime, customer loss)
- Regulatory fines
- Legal costs

Operational Impact:
- Service disruption
- Productivity loss
- Supply chain disruption
- Recovery time

Reputational Impact:
- Brand damage
- Customer trust loss
- Media coverage
- Competitive disadvantage

Compliance Impact:
- Regulatory violations
- Audit findings
- Certification loss
- Legal liability

# Risk Tolerance Levels
Risk Appetite: Maximum risk organization willing to accept
Risk Tolerance: Acceptable variation around risk appetite
Risk Capacity: Maximum risk organization can bear</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced Threat Modeling",
      content: `
        <h2>Advanced Threat Modeling Techniques</h2>
        <p>Advanced threat modeling incorporates multiple methodologies and considers complex attack scenarios, emerging threats, and business context.</p>

        <h3>PASTA Methodology</h3>
        <h4>Process for Attack Simulation and Threat Analysis</h4>
        <pre><code># PASTA Seven Stages

Stage 1: Define Objectives
- Business objectives
- Security objectives
- Compliance requirements

Stage 2: Define Technical Scope
- Application boundaries
- Infrastructure components
- Dependencies

Stage 3: Application Decomposition
- Data flow diagrams
- Component analysis
- Trust boundaries

Stage 4: Threat Analysis
- Threat intelligence
- Attack patterns
- Vulnerability research

Stage 5: Weakness and Vulnerability Analysis
- Design weaknesses
- Implementation flaws
- Configuration issues

Stage 6: Attack Modeling
- Attack trees
- Attack scenarios
- Exploit development

Stage 7: Risk and Impact Analysis
- Business impact
- Risk scoring
- Mitigation strategies</code></pre>

        <h3>Attack Trees</h3>
        <h4>Hierarchical Attack Modeling</h4>
        <pre><code># Attack Tree Example: Compromise Database

Goal: Access Sensitive Database
├── Direct Database Attack
│   ├── SQL Injection
│   │   ├── Blind SQL Injection
│   │   └── Union-based SQL Injection
│   ├── Database Credential Theft
│   │   ├── Configuration File Access
│   │   └── Memory Dump Analysis
│   └── Database Vulnerability Exploitation
├── Application Layer Attack
│   ├── Authentication Bypass
│   ├── Session Hijacking
│   └── Privilege Escalation
└── Infrastructure Attack
    ├── Network Sniffing
    ├── Man-in-the-Middle
    └── Server Compromise

# Attack Tree Analysis
- Probability calculation
- Cost-benefit analysis
- Countermeasure effectiveness
- Attack path optimization</code></pre>

        <h3>Threat Intelligence Integration</h3>
        <h4>Intelligence-Driven Threat Modeling</h4>
        <pre><code># Threat Actor Profiling

Nation-State Actors:
- Capabilities: Advanced, persistent
- Motivations: Espionage, disruption
- TTPs: APT techniques, zero-days
- Targets: Government, critical infrastructure

Cybercriminals:
- Capabilities: Moderate to advanced
- Motivations: Financial gain
- TTPs: Ransomware, fraud, theft
- Targets: Financial, healthcare, retail

Hacktivists:
- Capabilities: Variable
- Motivations: Political, social causes
- TTPs: DDoS, defacement, leaks
- Targets: Government, corporations

Insiders:
- Capabilities: Privileged access
- Motivations: Financial, revenge, ideology
- TTPs: Data theft, sabotage
- Targets: Employer systems and data

# Threat Intelligence Sources
- Commercial threat feeds
- Open source intelligence
- Government advisories
- Industry sharing groups
- Internal security data</code></pre>

        <h3>Automated Threat Modeling</h3>
        <h4>Tool-Assisted Analysis</h4>
        <pre><code># Microsoft Threat Modeling Tool
# Features:
- DFD creation
- Automated threat generation
- STRIDE analysis
- Mitigation suggestions
- Report generation

# OWASP Threat Dragon
# Features:
- Web-based modeling
- Collaborative editing
- Multiple methodologies
- Integration capabilities

# Custom Automation
import json

class ThreatModel:
    def __init__(self, application_name):
        self.name = application_name
        self.components = []
        self.data_flows = []
        self.threats = []
    
    def add_component(self, name, type, trust_level):
        component = {
            'name': name,
            'type': type,
            'trust_level': trust_level
        }
        self.components.append(component)
    
    def generate_stride_threats(self):
        stride_threats = {
            'process': ['spoofing', 'tampering', 'repudiation', 
                       'information_disclosure', 'denial_of_service', 
                       'elevation_of_privilege'],
            'data_store': ['tampering', 'repudiation', 
                          'information_disclosure', 'denial_of_service'],
            'data_flow': ['tampering', 'information_disclosure', 
                         'denial_of_service'],
            'external_entity': ['spoofing', 'repudiation']
        }
        
        for component in self.components:
            applicable_threats = stride_threats.get(component['type'], [])
            for threat in applicable_threats:
                self.threats.append({
                    'component': component['name'],
                    'threat_type': threat,
                    'risk_level': self.calculate_risk(component, threat)
                })
    
    def calculate_risk(self, component, threat):
        # Risk calculation logic
        base_risk = 3  # Medium
        if component['trust_level'] == 'low':
            base_risk += 1
        return min(5, base_risk)

# Usage
model = ThreatModel("Web Application")
model.add_component("Web Server", "process", "medium")
model.add_component("Database", "data_store", "high")
model.generate_stride_threats()</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Enterprise Application Threat Model",
    description: "Develop comprehensive threat model for a multi-tier enterprise application including risk assessment, attack tree analysis, and mitigation strategies.",
    environment: "Enterprise web application with database backend, API services, and mobile clients",
    tasks: [
      {
        category: "Architecture Analysis",
        tasks: [
          {
            task: "Create detailed data flow diagrams and component analysis",
            method: "System decomposition and trust boundary identification",
            expectedFindings: "Complete application architecture documentation",
            points: 20
          }
        ]
      },
      {
        category: "STRIDE Analysis",
        tasks: [
          {
            task: "Perform comprehensive STRIDE threat analysis",
            method: "Systematic threat identification for each component",
            expectedFindings: "Complete STRIDE threat catalog with risk ratings",
            points: 25
          }
        ]
      },
      {
        category: "Risk Assessment",
        tasks: [
          {
            task: "Conduct quantitative and qualitative risk assessment",
            method: "CVSS scoring and business impact analysis",
            expectedFindings: "Prioritized risk register with business context",
            points: 25
          }
        ]
      },
      {
        category: "Attack Modeling",
        tasks: [
          {
            task: "Develop attack trees for high-risk scenarios",
            method: "Hierarchical attack path analysis",
            expectedFindings: "Detailed attack scenarios with probability analysis",
            points: 15
          }
        ]
      },
      {
        category: "Mitigation Planning",
        tasks: [
          {
            task: "Design comprehensive mitigation strategy",
            method: "Cost-benefit analysis of security controls",
            expectedFindings: "Prioritized security control implementation plan",
            points: 15
          }
        ]
      }
    ],
    deliverables: [
      "Complete threat model documentation with DFDs",
      "STRIDE threat analysis with risk ratings",
      "Quantitative risk assessment report",
      "Attack tree diagrams and analysis",
      "Mitigation strategy and implementation roadmap",
      "Executive summary with business recommendations"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which threat modeling methodology uses six threat categories: Spoofing, Tampering, Repudiation, Information Disclosure, Denial of Service, and Elevation of Privilege?",
        options: [
          "PASTA",
          "STRIDE",
          "OCTAVE",
          "FAIR"
        ],
        correct: 1,
        explanation: "STRIDE is Microsoft's threat modeling methodology that categorizes threats into these six types."
      },
      {
        question: "In risk assessment, what does the formula Risk = Likelihood × Impact represent?",
        options: [
          "Quantitative risk calculation",
          "Qualitative risk assessment",
          "Threat probability",
          "Vulnerability score"
        ],
        correct: 1,
        explanation: "This formula represents qualitative risk assessment where both likelihood and impact are rated on scales and multiplied."
      },
      {
        question: "What is the primary purpose of attack trees in threat modeling?",
        options: [
          "Document system architecture",
          "Calculate risk scores",
          "Model hierarchical attack paths",
          "Generate security requirements"
        ],
        correct: 2,
        explanation: "Attack trees model hierarchical attack paths showing different ways an attacker could achieve their goal."
      }
    ],
    practicalTasks: [
      {
        task: "Create comprehensive data flow diagrams and perform STRIDE analysis",
        points: 25
      },
      {
        task: "Conduct quantitative risk assessment using FAIR or similar methodology",
        points: 25
      },
      {
        task: "Develop attack trees for critical business processes",
        points: 25
      },
      {
        task: "Design and justify security control implementation strategy",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "Threat Modeling: Designing for Security",
      url: "https://www.microsoft.com/en-us/securityengineering/sdl/threatmodeling",
      type: "book"
    },
    {
      title: "OWASP Threat Modeling",
      url: "https://owasp.org/www-community/Threat_Modeling",
      type: "guide"
    },
    {
      title: "Microsoft Threat Modeling Tool",
      url: "https://docs.microsoft.com/en-us/azure/security/develop/threat-modeling-tool",
      type: "tool"
    }
  ],
  tags: ["threat-modeling", "risk-assessment", "stride", "security-architecture", "attack-trees"],
  lastUpdated: "2024-01-15"
};
