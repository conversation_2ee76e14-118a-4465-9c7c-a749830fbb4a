/**
 * Ethical Hacking Module: Network Penetration Testing Fundamentals
 * Module ID: eh-6
 */

export const networkPenetrationTestingContent = {
  id: "eh-6",
  title: "Network Penetration Testing Fundamentals",
  description: "Master the fundamentals of network penetration testing including discovery, mapping, scanning, and exploitation techniques used in professional security assessments.",
  difficulty: "Intermediate",
  estimatedTime: 90,
  objectives: [
    "Understand network penetration testing methodology and phases",
    "Master network discovery and mapping techniques",
    "Learn advanced port scanning and service enumeration",
    "Identify and exploit common network vulnerabilities",
    "Develop skills in network-based attack vectors"
  ],
  prerequisites: ["eh-1", "eh-2", "eh-3"],
  sections: [
    {
      title: "Network Penetration Testing Overview",
      content: `
        <h2>Network Penetration Testing Fundamentals</h2>
        <p>Network penetration testing is a critical component of cybersecurity assessments that focuses on identifying vulnerabilities in network infrastructure, services, and protocols.</p>
        
        <h3>Network Pen Testing Methodology</h3>
        <ol>
          <li><strong>Planning and Reconnaissance</strong> - Define scope and gather intelligence</li>
          <li><strong>Network Discovery</strong> - Identify live hosts and network topology</li>
          <li><strong>Port Scanning</strong> - Enumerate open ports and services</li>
          <li><strong>Service Enumeration</strong> - Fingerprint services and versions</li>
          <li><strong>Vulnerability Assessment</strong> - Identify security weaknesses</li>
          <li><strong>Exploitation</strong> - Attempt to exploit identified vulnerabilities</li>
          <li><strong>Post-Exploitation</strong> - Maintain access and gather evidence</li>
          <li><strong>Reporting</strong> - Document findings and recommendations</li>
        </ol>

        <h3>Network Testing Scope</h3>
        <ul>
          <li>External network perimeter testing</li>
          <li>Internal network segmentation testing</li>
          <li>Wireless network security assessment</li>
          <li>Network device configuration review</li>
          <li>Protocol-specific vulnerability testing</li>
        </ul>

        <h3>Legal and Ethical Considerations</h3>
        <div class="alert alert-warning">
          <strong>Important:</strong> Network penetration testing must only be performed with explicit written authorization. Unauthorized network scanning and testing is illegal and unethical.
        </div>
      `,
      type: "text"
    },
    {
      title: "Network Discovery Techniques",
      content: `
        <h2>Network Discovery and Mapping</h2>
        <p>Network discovery is the process of identifying live hosts, network topology, and infrastructure components within the target environment.</p>

        <h3>Host Discovery Methods</h3>
        <h4>1. ICMP-Based Discovery</h4>
        <pre><code># Basic ping sweep
nmap -sn ***********/24

# ICMP echo, timestamp, and netmask requests
nmap -PE -PP -PM ***********/24</code></pre>

        <h4>2. TCP-Based Discovery</h4>
        <pre><code># TCP SYN ping to common ports
nmap -PS21,22,23,25,53,80,110,443,993,995 ***********/24

# TCP ACK ping
nmap -PA80,443 ***********/24</code></pre>

        <h4>3. UDP-Based Discovery</h4>
        <pre><code># UDP ping to common services
nmap -PU53,67,68,69,111,123,135,137,138,139,161,162 ***********/24</code></pre>

        <h3>Network Topology Mapping</h3>
        <h4>Traceroute Analysis</h4>
        <pre><code># TCP traceroute
nmap --traceroute ***********

# UDP traceroute with specific port
traceroute -U -p 53 *******

# ICMP traceroute
traceroute -I *******</code></pre>

        <h4>Network Visualization</h4>
        <ul>
          <li><strong>Zenmap</strong> - Graphical network topology mapping</li>
          <li><strong>Netdiscover</strong> - ARP-based network discovery</li>
          <li><strong>Masscan</strong> - High-speed network scanner</li>
          <li><strong>Zmap</strong> - Internet-wide network scanner</li>
        </ul>

        <h3>Stealth Discovery Techniques</h3>
        <pre><code># Fragmented packets
nmap -f ***********/24

# Decoy scanning
nmap -D RND:10 ***********/24

# Timing templates for stealth
nmap -T1 ***********/24  # Paranoid
nmap -T2 ***********/24  # Sneaky</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced Port Scanning",
      content: `
        <h2>Port Scanning Techniques and Evasion</h2>
        <p>Port scanning is fundamental to network penetration testing, revealing open services and potential attack vectors.</p>

        <h3>TCP Scan Types</h3>
        <h4>1. TCP Connect Scan (-sT)</h4>
        <pre><code># Full TCP connection
nmap -sT ***********00</code></pre>
        <p><strong>Characteristics:</strong> Completes full TCP handshake, easily detected, works without root privileges</p>

        <h4>2. TCP SYN Scan (-sS)</h4>
        <pre><code># Half-open scan (default)
nmap -sS ***********00</code></pre>
        <p><strong>Characteristics:</strong> Stealthier, faster, requires root privileges</p>

        <h4>3. TCP ACK Scan (-sA)</h4>
        <pre><code># Firewall rule detection
nmap -sA ***********00</code></pre>
        <p><strong>Purpose:</strong> Determines firewall rules and port filtering</p>

        <h4>4. TCP Window Scan (-sW)</h4>
        <pre><code># Window size analysis
nmap -sW ***********00</code></pre>

        <h4>5. TCP Maimon Scan (-sM)</h4>
        <pre><code># FIN/ACK probe
nmap -sM ***********00</code></pre>

        <h3>UDP Scanning</h3>
        <pre><code># UDP scan (slower but important)
nmap -sU ***********00

# Top UDP ports
nmap -sU --top-ports 100 ***********00

# UDP scan with version detection
nmap -sUV ***********00</code></pre>

        <h3>Firewall and IDS Evasion</h3>
        <h4>Fragmentation</h4>
        <pre><code># Fragment packets
nmap -f ***********00

# Custom MTU
nmap --mtu 24 ***********00</code></pre>

        <h4>Decoy Scanning</h4>
        <pre><code># Use decoy IPs
nmap -D ***********,***********,ME ***********00

# Random decoys
nmap -D RND:10 ***********00</code></pre>

        <h4>Source Port Manipulation</h4>
        <pre><code># Use specific source port
nmap --source-port 53 ***********00
nmap -g 53 ***********00</code></pre>

        <h4>Timing and Performance</h4>
        <pre><code># Timing templates
nmap -T0 ***********00  # Paranoid (5 min between probes)
nmap -T1 ***********00  # Sneaky (15 sec between probes)
nmap -T2 ***********00  # Polite (0.4 sec between probes)
nmap -T3 ***********00  # Normal (default)
nmap -T4 ***********00  # Aggressive
nmap -T5 ***********00  # Insane</code></pre>
      `,
      type: "text"
    },
    {
      title: "Service Enumeration and Fingerprinting",
      content: `
        <h2>Service Enumeration Techniques</h2>
        <p>Service enumeration involves identifying specific services, versions, and configurations running on discovered ports.</p>

        <h3>Version Detection</h3>
        <pre><code># Service version detection
nmap -sV ***********00

# Aggressive version detection
nmap -sV --version-intensity 9 ***********00

# Light version detection
nmap -sV --version-intensity 2 ***********00</code></pre>

        <h3>Operating System Detection</h3>
        <pre><code># OS fingerprinting
nmap -O ***********00

# Aggressive OS detection
nmap -O --osscan-guess ***********00

# OS detection with version scanning
nmap -A ***********00</code></pre>

        <h3>NSE Script Scanning</h3>
        <pre><code># Default scripts
nmap -sC ***********00

# Specific script categories
nmap --script vuln ***********00
nmap --script auth ***********00
nmap --script discovery ***********00

# Individual scripts
nmap --script http-enum ***********00
nmap --script smb-enum-shares ***********00</code></pre>

        <h3>Service-Specific Enumeration</h3>
        <h4>HTTP/HTTPS Services</h4>
        <pre><code># HTTP enumeration
nmap --script http-enum,http-headers,http-methods,http-robots.txt -p 80,443 ***********00

# Directory enumeration
dirb http://***********00
gobuster dir -u http://***********00 -w /usr/share/wordlists/dirb/common.txt</code></pre>

        <h4>SMB Services</h4>
        <pre><code># SMB enumeration
nmap --script smb-enum-shares,smb-enum-users,smb-enum-domains -p 445 ***********00

# SMB client enumeration
smbclient -L //***********00
enum4linux ***********00</code></pre>

        <h4>DNS Services</h4>
        <pre><code># DNS enumeration
nmap --script dns-zone-transfer,dns-recursion -p 53 ***********00

# Zone transfer attempts
dig @***********00 domain.com axfr
dnsrecon -d domain.com -t axfr</code></pre>

        <h4>SNMP Services</h4>
        <pre><code># SNMP enumeration
nmap --script snmp-enum,snmp-info,snmp-sysdescr -p 161 ***********00

# SNMP walk
snmpwalk -v2c -c public ***********00</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Corporate Network Penetration Test Simulation",
    description: "Conduct a comprehensive network penetration test against a simulated corporate environment with multiple network segments and security controls.",
    environment: "Virtual lab with segmented network topology",
    tasks: [
      {
        category: "Network Discovery",
        tasks: [
          {
            task: "Perform network discovery on the 10.0.0.0/16 range",
            command: "nmap -sn 10.0.0.0/16",
            expectedFindings: "Identify live hosts in multiple subnets",
            points: 15
          },
          {
            task: "Map network topology using traceroute",
            command: "nmap --traceroute ********",
            expectedFindings: "Identify network hops and potential firewalls",
            points: 10
          }
        ]
      },
      {
        category: "Port Scanning",
        tasks: [
          {
            task: "Perform comprehensive port scan on discovered hosts",
            command: "nmap -sS -p- **********",
            expectedFindings: "Identify all open ports and services",
            points: 20
          },
          {
            task: "Conduct UDP scan on critical services",
            command: "nmap -sU --top-ports 100 **********",
            expectedFindings: "Discover UDP services like DNS, SNMP",
            points: 15
          }
        ]
      },
      {
        category: "Service Enumeration",
        tasks: [
          {
            task: "Enumerate web services and directories",
            command: "nmap --script http-enum -p 80,443 **********",
            expectedFindings: "Discover web applications and admin interfaces",
            points: 20
          },
          {
            task: "Enumerate SMB shares and users",
            command: "nmap --script smb-enum-shares,smb-enum-users -p 445 **********",
            expectedFindings: "Identify accessible shares and user accounts",
            points: 20
          }
        ]
      }
    ],
    deliverables: [
      "Network topology diagram",
      "Comprehensive port scan results",
      "Service enumeration report",
      "Vulnerability assessment summary",
      "Recommendations for security improvements"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which Nmap scan type is considered the most stealthy for TCP port scanning?",
        options: [
          "TCP Connect scan (-sT)",
          "TCP SYN scan (-sS)",
          "TCP ACK scan (-sA)",
          "TCP FIN scan (-sF)"
        ],
        correct: 3,
        explanation: "TCP FIN scan sends FIN packets and is less likely to be detected by basic intrusion detection systems."
      },
      {
        question: "What is the primary purpose of using decoy scanning in Nmap?",
        options: [
          "To increase scanning speed",
          "To bypass firewall rules",
          "To hide the true source of the scan",
          "To improve accuracy of results"
        ],
        correct: 2,
        explanation: "Decoy scanning uses multiple fake source IPs to hide the real scanner's IP address among decoys."
      },
      {
        question: "Which timing template should be used for the most stealthy scanning approach?",
        options: [
          "T0 (Paranoid)",
          "T1 (Sneaky)",
          "T2 (Polite)",
          "T3 (Normal)"
        ],
        correct: 0,
        explanation: "T0 (Paranoid) timing uses 5-minute delays between probes, making it the most stealthy but slowest option."
      }
    ],
    practicalTasks: [
      {
        task: "Demonstrate three different TCP scan types and explain their characteristics",
        points: 25
      },
      {
        task: "Perform service enumeration on a target and identify at least 5 different services",
        points: 25
      },
      {
        task: "Use firewall evasion techniques to scan a protected target",
        points: 25
      },
      {
        task: "Create a network topology map from scan results",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "Nmap Network Scanning Guide",
      url: "https://nmap.org/book/",
      type: "documentation"
    },
    {
      title: "Network Penetration Testing Methodology",
      url: "https://owasp.org/www-project-web-security-testing-guide/",
      type: "methodology"
    },
    {
      title: "Advanced Nmap Techniques",
      url: "https://nmap.org/book/nse.html",
      type: "tutorial"
    }
  ],
  tags: ["network-security", "penetration-testing", "nmap", "reconnaissance", "scanning"],
  lastUpdated: "2024-01-15"
};
