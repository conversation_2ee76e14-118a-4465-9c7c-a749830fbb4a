/**
 * Ethical Hacking Module: Industrial Control Systems (ICS/SCADA) Security
 * Module ID: eh-37
 */

export const icsScadaContent = {
  id: "eh-37",
  title: "Industrial Control Systems (ICS/SCADA) Security",
  description: "Master ICS/SCADA security testing including industrial protocol analysis, HMI security assessment, and critical infrastructure protection for operational technology environments.",
  difficulty: "Expert",
  estimatedTime: 115,
  objectives: [
    "Understand ICS/SCADA architecture and security challenges",
    "Master industrial protocol security testing techniques",
    "Learn HMI and engineering workstation security assessment",
    "Develop skills in OT network security testing",
    "Apply ICS security testing in critical infrastructure environments"
  ],
  prerequisites: ["eh-1", "eh-17", "eh-27", "eh-31"],
  sections: [
    {
      title: "ICS/SCADA Security Fundamentals",
      content: `
        <h2>Industrial Control Systems Overview</h2>
        <p>ICS/SCADA systems control critical infrastructure and industrial processes, requiring specialized security testing approaches for operational technology environments.</p>
        
        <h3>ICS Architecture Components</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Component</th>
              <th>Function</th>
              <th>Security Concerns</th>
              <th>Testing Approach</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>SCADA Server</td>
              <td>Supervisory control and data acquisition</td>
              <td>Unauthorized access, data manipulation</td>
              <td>Authentication testing, protocol analysis</td>
            </tr>
            <tr>
              <td>HMI (Human Machine Interface)</td>
              <td>Operator interface and control</td>
              <td>Privilege escalation, session hijacking</td>
              <td>Application security testing, access control</td>
            </tr>
            <tr>
              <td>PLC (Programmable Logic Controller)</td>
              <td>Process control and automation</td>
              <td>Firmware vulnerabilities, logic manipulation</td>
              <td>Firmware analysis, protocol fuzzing</td>
            </tr>
            <tr>
              <td>RTU (Remote Terminal Unit)</td>
              <td>Remote monitoring and control</td>
              <td>Communication interception, device spoofing</td>
              <td>Protocol security, device authentication</td>
            </tr>
            <tr>
              <td>Engineering Workstation</td>
              <td>System configuration and programming</td>
              <td>Malware infection, credential theft</td>
              <td>Endpoint security, network isolation</td>
            </tr>
          </tbody>
        </table>

        <h3>Industrial Protocol Security</h3>
        <h4>Common Industrial Protocols</h4>
        <pre><code># Industrial protocol security testing framework
import struct
import socket
import time

class IndustrialProtocolTester:
    def __init__(self):
        self.protocols = {
            'modbus': {'port': 502, 'functions': [1, 2, 3, 4, 5, 6, 15, 16]},
            'dnp3': {'port': 20000, 'functions': ['read', 'write', 'control']},
            'iec61850': {'port': 102, 'services': ['MMS', 'GOOSE', 'SMV']},
            'bacnet': {'port': 47808, 'services': ['read', 'write', 'subscribe']},
            'ethernet_ip': {'port': 44818, 'services': ['explicit', 'implicit']}
        }
    
    def test_modbus_security(self, target_ip, target_port=502):
        # Comprehensive Modbus security testing
        modbus_tests = {
            'authentication': self.test_modbus_authentication(target_ip, target_port),
            'authorization': self.test_modbus_authorization(target_ip, target_port),
            'function_code_fuzzing': self.fuzz_modbus_functions(target_ip, target_port),
            'exception_handling': self.test_modbus_exceptions(target_ip, target_port),
            'coil_manipulation': self.test_coil_manipulation(target_ip, target_port)
        }
        
        return modbus_tests
    
    def test_modbus_authentication(self, ip, port):
        # Test Modbus authentication mechanisms
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((ip, port))
            
            # Modbus TCP header + Read Coils function
            modbus_request = struct.pack('>HHHBBHH', 
                0x0001,  # Transaction ID
                0x0000,  # Protocol ID
                0x0006,  # Length
                0x01,    # Unit ID
                0x01,    # Function Code (Read Coils)
                0x0000,  # Starting Address
                0x0001   # Quantity
            )
            
            sock.send(modbus_request)
            response = sock.recv(1024)
            sock.close()
            
            if len(response) > 0:
                return {
                    'authentication_required': False,
                    'anonymous_access': True,
                    'response_received': True
                }
            
        except Exception as e:
            return {
                'authentication_required': True,
                'error': str(e)
            }
    
    def fuzz_modbus_functions(self, ip, port):
        # Fuzz Modbus function codes
        fuzzing_results = []
        
        for function_code in range(1, 128):  # Valid function codes 1-127
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect((ip, port))
                
                # Craft Modbus request with function code
                modbus_request = struct.pack('>HHHBBHH',
                    0x0001,        # Transaction ID
                    0x0000,        # Protocol ID
                    0x0006,        # Length
                    0x01,          # Unit ID
                    function_code, # Function Code (fuzzing)
                    0x0000,        # Starting Address
                    0x0001         # Quantity
                )
                
                start_time = time.time()
                sock.send(modbus_request)
                response = sock.recv(1024)
                response_time = time.time() - start_time
                
                fuzzing_results.append({
                    'function_code': function_code,
                    'response_length': len(response),
                    'response_time': response_time,
                    'exception_code': self.parse_modbus_exception(response)
                })
                
                sock.close()
                
            except Exception as e:
                fuzzing_results.append({
                    'function_code': function_code,
                    'error': str(e)
                })
        
        return fuzzing_results</code></pre>

        <h3>Safety and Availability Considerations</h3>
        <div class="alert alert-danger">
          <strong>Critical Safety Warning:</strong> ICS/SCADA testing must prioritize safety and availability. Never test on production systems controlling critical processes. Always use isolated test environments and coordinate with operations teams.
        </div>
      `,
      type: "text"
    },
    {
      title: "HMI and Engineering Workstation Security",
      content: `
        <h2>Human Machine Interface Security Testing</h2>
        <p>HMI and engineering workstations are critical attack vectors in ICS environments, requiring comprehensive security assessment of applications and configurations.</p>

        <h3>HMI Application Security</h3>
        <h4>HMI Vulnerability Assessment</h4>
        <pre><code># HMI security testing framework
class HMISecurityTester:
    def __init__(self):
        self.common_hmi_ports = [80, 443, 8080, 8443, 1433, 3389]
        self.hmi_vendors = ['wonderware', 'ge_ifix', 'siemens_wincc', 'rockwell_factorytalk']
    
    def comprehensive_hmi_assessment(self, hmi_target):
        # Complete HMI security assessment
        assessment_results = {
            'network_services': self.scan_hmi_services(hmi_target),
            'web_interface': self.test_web_hmi_security(hmi_target),
            'authentication': self.test_hmi_authentication(hmi_target),
            'session_management': self.test_hmi_sessions(hmi_target),
            'privilege_escalation': self.test_hmi_privileges(hmi_target),
            'data_validation': self.test_hmi_input_validation(hmi_target),
            'vendor_specific': self.test_vendor_vulnerabilities(hmi_target)
        }
        
        return assessment_results
    
    def test_web_hmi_security(self, target):
        # Test web-based HMI security
        web_tests = {
            'ssl_configuration': self.test_ssl_config(target),
            'authentication_bypass': self.test_auth_bypass(target),
            'session_fixation': self.test_session_fixation(target),
            'xss_vulnerabilities': self.test_hmi_xss(target),
            'csrf_protection': self.test_csrf_protection(target),
            'directory_traversal': self.test_directory_traversal(target),
            'file_upload': self.test_file_upload_security(target)
        }
        
        return web_tests
    
    def test_hmi_authentication(self, target):
        # Test HMI authentication mechanisms
        auth_tests = {
            'default_credentials': self.test_default_credentials(target),
            'password_policy': self.test_password_requirements(target),
            'brute_force_protection': self.test_brute_force_protection(target),
            'account_lockout': self.test_account_lockout(target),
            'multi_factor_auth': self.test_mfa_implementation(target),
            'session_timeout': self.test_session_timeout(target)
        }
        
        return auth_tests
    
    def test_default_credentials(self, target):
        # Test for default HMI credentials
        default_creds = [
            ('admin', 'admin'),
            ('administrator', 'password'),
            ('operator', 'operator'),
            ('engineer', 'engineer'),
            ('guest', 'guest'),
            ('wonderware', 'wonderware'),
            ('ifix', 'ifix'),
            ('wincc', 'wincc')
        ]
        
        successful_logins = []
        
        for username, password in default_creds:
            login_result = self.attempt_login(target, username, password)
            if login_result['success']:
                successful_logins.append({
                    'username': username,
                    'password': password,
                    'access_level': login_result.get('access_level', 'unknown')
                })
        
        return {
            'vulnerable': len(successful_logins) > 0,
            'successful_logins': successful_logins
        }

# Engineering workstation security testing
class EngineeringWorkstationTester:
    def __init__(self):
        self.engineering_software = [
            'rslogix', 'step7', 'unity_pro', 'codesys', 'wonderware_intouch'
        ]
    
    def assess_workstation_security(self, workstation_target):
        # Comprehensive engineering workstation assessment
        workstation_assessment = {
            'os_security': self.test_os_hardening(workstation_target),
            'software_inventory': self.inventory_engineering_software(workstation_target),
            'network_isolation': self.test_network_segmentation(workstation_target),
            'usb_controls': self.test_usb_security(workstation_target),
            'antivirus_status': self.check_antivirus_protection(workstation_target),
            'patch_management': self.assess_patch_status(workstation_target),
            'backup_procedures': self.test_backup_security(workstation_target)
        }
        
        return workstation_assessment
    
    def test_engineering_software_security(self, software_list):
        # Test security of engineering software
        software_security = {}
        
        for software in software_list:
            software_tests = {
                'version_analysis': self.check_software_version(software),
                'vulnerability_scan': self.scan_software_vulnerabilities(software),
                'configuration_review': self.review_software_config(software),
                'project_file_security': self.test_project_file_security(software),
                'communication_security': self.test_software_communications(software)
            }
            
            software_security[software] = software_tests
        
        return software_security</code></pre>
      `,
      type: "text"
    },
    {
      title: "OT Network Security Testing",
      content: `
        <h2>Operational Technology Network Security</h2>
        <p>OT networks require specialized security testing approaches that consider operational requirements, safety systems, and industrial protocols.</p>

        <h3>OT Network Architecture Assessment</h3>
        <h4>Network Segmentation Testing</h4>
        <pre><code># OT network security testing
class OTNetworkTester:
    def __init__(self):
        self.ot_zones = {
            'level_0': 'Field devices and sensors',
            'level_1': 'PLCs and local control',
            'level_2': 'SCADA and HMI systems',
            'level_3': 'Manufacturing operations',
            'level_4': 'Business planning systems'
        }
    
    def assess_network_segmentation(self, network_topology):
        # Test OT network segmentation
        segmentation_tests = {
            'zone_isolation': self.test_zone_isolation(network_topology),
            'firewall_rules': self.analyze_firewall_rules(network_topology),
            'vlan_configuration': self.test_vlan_segmentation(network_topology),
            'air_gap_validation': self.validate_air_gaps(network_topology),
            'dmz_security': self.test_dmz_configuration(network_topology)
        }
        
        return segmentation_tests
    
    def test_zone_isolation(self, topology):
        # Test isolation between OT zones
        isolation_results = {}
        
        for source_zone in self.ot_zones:
            for target_zone in self.ot_zones:
                if source_zone != target_zone:
                    connectivity_test = self.test_zone_connectivity(
                        topology, source_zone, target_zone
                    )
                    
                    isolation_results[f"{source_zone}_to_{target_zone}"] = {
                        'connectivity_allowed': connectivity_test['allowed'],
                        'protocols_permitted': connectivity_test['protocols'],
                        'security_controls': connectivity_test['controls'],
                        'compliance_status': self.evaluate_zone_compliance(
                            source_zone, target_zone, connectivity_test
                        )
                    }
        
        return isolation_results
    
    def test_industrial_device_security(self, device_inventory):
        # Test security of industrial devices
        device_security = {}
        
        for device in device_inventory:
            device_tests = {
                'firmware_analysis': self.analyze_device_firmware(device),
                'default_credentials': self.test_device_credentials(device),
                'protocol_security': self.test_device_protocols(device),
                'physical_security': self.assess_physical_security(device),
                'configuration_security': self.review_device_config(device)
            }
            
            device_security[device['id']] = device_tests
        
        return device_security
    
    def test_safety_system_security(self, safety_systems):
        # Test safety instrumented systems (SIS)
        safety_tests = {}
        
        for system in safety_systems:
            # Special considerations for safety systems
            safety_assessment = {
                'isolation_verification': self.verify_sis_isolation(system),
                'integrity_level': self.assess_sil_compliance(system),
                'bypass_protection': self.test_bypass_mechanisms(system),
                'fail_safe_behavior': self.test_fail_safe_operation(system),
                'maintenance_security': self.test_maintenance_procedures(system)
            }
            
            safety_tests[system['name']] = safety_assessment
        
        return safety_tests

# ICS incident response testing
class ICSIncidentResponseTester:
    def __init__(self):
        self.ics_incident_types = [
            'unauthorized_access', 'malware_infection', 'protocol_attack',
            'safety_system_compromise', 'data_manipulation', 'denial_of_service'
        ]
    
    def test_ics_incident_response(self, incident_scenarios):
        # Test ICS-specific incident response capabilities
        response_tests = {}
        
        for scenario in incident_scenarios:
            scenario_test = {
                'detection_capability': self.test_incident_detection(scenario),
                'response_procedures': self.test_response_procedures(scenario),
                'safety_considerations': self.test_safety_response(scenario),
                'business_continuity': self.test_operational_continuity(scenario),
                'forensic_capabilities': self.test_ics_forensics(scenario),
                'recovery_procedures': self.test_system_recovery(scenario)
            }
            
            response_tests[scenario['type']] = scenario_test
        
        return response_tests</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Critical Infrastructure ICS/SCADA Security Assessment",
    description: "Conduct comprehensive ICS/SCADA security assessment including industrial protocol testing, HMI security evaluation, and OT network security analysis.",
    environment: "ICS/SCADA testbed with PLCs, HMIs, SCADA servers, and industrial network infrastructure",
    tasks: [
      {
        category: "Industrial Protocol Security",
        tasks: [
          {
            task: "Test Modbus, DNP3, and other industrial protocol security",
            method: "Protocol fuzzing, authentication testing, and communication analysis",
            expectedFindings: "Industrial protocol vulnerabilities and communication security gaps",
            points: 30
          }
        ]
      },
      {
        category: "HMI Security Assessment",
        tasks: [
          {
            task: "Assess HMI and engineering workstation security",
            method: "Application security testing, authentication analysis, and privilege testing",
            expectedFindings: "HMI vulnerabilities and workstation security weaknesses",
            points: 25
          }
        ]
      },
      {
        category: "OT Network Security",
        tasks: [
          {
            task: "Test OT network segmentation and device security",
            method: "Network isolation testing, device assessment, and safety system evaluation",
            expectedFindings: "Network segmentation gaps and device security vulnerabilities",
            points: 25
          }
        ]
      },
      {
        category: "Safety System Security",
        tasks: [
          {
            task: "Evaluate safety instrumented system security",
            method: "SIS isolation testing, integrity assessment, and fail-safe validation",
            expectedFindings: "Safety system security risks and compliance gaps",
            points: 20
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive ICS/SCADA security assessment report",
      "Industrial protocol security analysis and vulnerability findings",
      "HMI and engineering workstation security evaluation",
      "OT network segmentation assessment and recommendations",
      "Safety system security analysis and compliance review",
      "ICS security hardening guide and best practices"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which industrial protocol is commonly used for communication between SCADA systems and RTUs?",
        options: [
          "Modbus",
          "DNP3",
          "IEC 61850",
          "BACnet"
        ],
        correct: 1,
        explanation: "DNP3 (Distributed Network Protocol) is commonly used for communication between SCADA systems and Remote Terminal Units (RTUs) in utility and industrial applications."
      },
      {
        question: "What is the primary security concern with default credentials in ICS environments?",
        options: [
          "Performance impact",
          "Unauthorized access to critical systems",
          "Protocol compatibility",
          "Maintenance complexity"
        ],
        correct: 1,
        explanation: "Default credentials in ICS environments pose a critical security risk as they can provide unauthorized access to systems controlling critical infrastructure and industrial processes."
      },
      {
        question: "Which principle is most important when testing safety instrumented systems (SIS)?",
        options: [
          "Maximum test coverage",
          "Maintaining system availability",
          "Preserving safety functions",
          "Performance optimization"
        ],
        correct: 2,
        explanation: "When testing safety instrumented systems, preserving safety functions is paramount as these systems are designed to prevent or mitigate hazardous conditions."
      }
    ],
    practicalTasks: [
      {
        task: "Conduct industrial protocol security testing and vulnerability assessment",
        points: 25
      },
      {
        task: "Perform HMI and engineering workstation security evaluation",
        points: 25
      },
      {
        task: "Test OT network segmentation and device security",
        points: 25
      },
      {
        task: "Assess safety instrumented system security and compliance",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "NIST Cybersecurity Framework for Manufacturing",
      url: "https://www.nist.gov/cyberframework/manufacturing",
      type: "framework"
    },
    {
      title: "ICS-CERT Advisories",
      url: "https://www.cisa.gov/uscert/ics/advisories",
      type: "advisories"
    },
    {
      title: "SANS ICS Security Training",
      url: "https://www.sans.org/cyber-security-courses/industrial-control-system-scada-security/",
      type: "training"
    }
  ],
  tags: ["ics-security", "scada", "industrial-protocols", "ot-security", "critical-infrastructure"],
  lastUpdated: "2024-01-15"
};
