/**
 * Introduction to Ethical Hacking Module
 */

export const ethicalHackingIntroContent = {
  id: "eh-1",
  pathId: "ethical-hacking-fundamentals",
  title: "Introduction to Ethical Hacking",
  description: "Master the fundamental concepts, ethics, and legal frameworks of ethical hacking and penetration testing.",
  objectives: [
    "Understand the difference between ethical and malicious hacking",
    "Learn the legal and ethical frameworks governing penetration testing",
    "Explore penetration testing methodologies and standards",
    "Understand the role of ethical hackers in cybersecurity",
    "Master the essential terminology and concepts"
  ],
  difficulty: "Beginner",
  estimatedTime: 90,
  sections: [
    {
      title: "What is Ethical Hacking?",
      content: `
        <h2>What is Ethical Hacking?</h2>
        <p>Ethical hacking, also known as penetration testing or white-hat hacking, is the practice of intentionally probing systems, networks, and applications for security vulnerabilities in an authorized manner.</p>
        
        <h3>Key Principles of Ethical Hacking</h3>
        <ul>
          <li><strong>Authorization:</strong> Always obtain explicit written permission before testing</li>
          <li><strong>Scope Definition:</strong> Clearly define what systems and methods are approved</li>
          <li><strong>Responsible Disclosure:</strong> Report vulnerabilities responsibly to the organization</li>
          <li><strong>Documentation:</strong> Maintain detailed records of testing activities</li>
          <li><strong>Legal Compliance:</strong> Adhere to all applicable laws and regulations</li>
        </ul>
        
        <h3>Types of Hackers</h3>
        <ul>
          <li><strong>White Hat Hackers (Ethical Hackers):</strong> Security professionals who hack with permission to improve security</li>
          <li><strong>Black Hat Hackers:</strong> Malicious individuals who hack for personal gain or to cause harm</li>
          <li><strong>Gray Hat Hackers:</strong> Hackers who operate in a legal gray area, sometimes without permission but without malicious intent</li>
          <li><strong>Script Kiddies:</strong> Inexperienced hackers who use tools created by others</li>
          <li><strong>Hacktivists:</strong> Hackers motivated by political or social causes</li>
        </ul>
        
        <h3>The Role of Ethical Hackers</h3>
        <p>Ethical hackers serve as crucial defenders in the cybersecurity ecosystem:</p>
        <ul>
          <li>Identifying security vulnerabilities before malicious actors</li>
          <li>Testing security controls and defensive mechanisms</li>
          <li>Providing actionable recommendations for security improvements</li>
          <li>Helping organizations comply with security standards and regulations</li>
          <li>Educating stakeholders about security risks and best practices</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Legal and Ethical Framework",
      content: `
        <h2>Legal and Ethical Framework</h2>
        <p>Understanding the legal landscape is crucial for ethical hackers to operate within appropriate boundaries.</p>
        
        <h3>Key Legal Considerations</h3>
        <ul>
          <li><strong>Computer Fraud and Abuse Act (CFAA):</strong> US federal law criminalizing unauthorized computer access</li>
          <li><strong>Digital Millennium Copyright Act (DMCA):</strong> Protects digital content and criminalizes circumvention</li>
          <li><strong>General Data Protection Regulation (GDPR):</strong> EU regulation affecting data handling during testing</li>
          <li><strong>Local Laws:</strong> State and local regulations that may apply to testing activities</li>
        </ul>
        
        <h3>Essential Documentation</h3>
        <ul>
          <li><strong>Rules of Engagement (RoE):</strong> Detailed guidelines for testing scope and methods</li>
          <li><strong>Statement of Work (SoW):</strong> Formal contract outlining testing objectives and deliverables</li>
          <li><strong>Non-Disclosure Agreement (NDA):</strong> Legal protection for sensitive information</li>
          <li><strong>Permission Letters:</strong> Written authorization from system owners</li>
        </ul>
        
        <h3>Ethical Guidelines</h3>
        <ul>
          <li>Respect privacy and confidentiality of discovered information</li>
          <li>Minimize impact on business operations during testing</li>
          <li>Report all findings accurately and completely</li>
          <li>Avoid accessing or modifying data unnecessarily</li>
          <li>Maintain professional integrity and objectivity</li>
        </ul>
        
        <h3>Professional Standards</h3>
        <ul>
          <li><strong>EC-Council Code of Ethics:</strong> Professional standards for certified ethical hackers</li>
          <li><strong>SANS Ethics:</strong> Guidelines for information security professionals</li>
          <li><strong>ISC2 Code of Ethics:</strong> Professional ethics for cybersecurity practitioners</li>
          <li><strong>PTES (Penetration Testing Execution Standard):</strong> Technical guidelines for penetration testing</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Penetration Testing Methodologies",
      content: `
        <h2>Penetration Testing Methodologies</h2>
        <p>Structured methodologies ensure comprehensive and consistent penetration testing approaches.</p>
        
        <h3>PTES (Penetration Testing Execution Standard)</h3>
        <ul>
          <li><strong>Pre-engagement Interactions:</strong> Scoping, RoE, and contracts</li>
          <li><strong>Intelligence Gathering:</strong> Information collection and reconnaissance</li>
          <li><strong>Threat Modeling:</strong> Understanding attack vectors and business impact</li>
          <li><strong>Vulnerability Analysis:</strong> Identifying and prioritizing vulnerabilities</li>
          <li><strong>Exploitation:</strong> Validating vulnerabilities through controlled exploitation</li>
          <li><strong>Post Exploitation:</strong> Assessing impact and maintaining access</li>
          <li><strong>Reporting:</strong> Documenting findings and recommendations</li>
        </ul>
        
        <h3>OWASP Testing Guide</h3>
        <ul>
          <li><strong>Information Gathering:</strong> Mapping application architecture and functionality</li>
          <li><strong>Configuration and Deployment Management Testing:</strong> Infrastructure security</li>
          <li><strong>Identity Management Testing:</strong> Authentication and authorization</li>
          <li><strong>Authentication Testing:</strong> Password policies and multi-factor authentication</li>
          <li><strong>Authorization Testing:</strong> Access controls and privilege escalation</li>
          <li><strong>Session Management Testing:</strong> Session handling and security</li>
        </ul>
        
        <h3>NIST SP 800-115</h3>
        <ul>
          <li><strong>Planning:</strong> Test planning and coordination</li>
          <li><strong>Discovery:</strong> Network and service discovery</li>
          <li><strong>Attack:</strong> Vulnerability validation and exploitation</li>
          <li><strong>Reporting:</strong> Results documentation and recommendations</li>
        </ul>
        
        <h3>Cyber Kill Chain</h3>
        <ul>
          <li><strong>Reconnaissance:</strong> Research and target identification</li>
          <li><strong>Weaponization:</strong> Exploit and payload creation</li>
          <li><strong>Delivery:</strong> Payload transmission to target</li>
          <li><strong>Exploitation:</strong> Code execution on target system</li>
          <li><strong>Installation:</strong> Malware installation and persistence</li>
          <li><strong>Command and Control:</strong> Remote access establishment</li>
          <li><strong>Actions on Objectives:</strong> Goal achievement and data exfiltration</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Ethical Hacking Legal Framework Lab",
    description: "Hands-on exploration of legal documentation and ethical frameworks in penetration testing.",
    tasks: [
      {
        category: "Legal Documentation",
        commands: [
          {
            command: "Review sample penetration testing contract",
            description: "Analyze a template Rules of Engagement document",
            hint: "Look for scope definition, testing methods, and legal protections",
            expectedOutput: "Understanding of contract components"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the most critical requirement before beginning any penetration test?",
      options: [
        "Installing penetration testing tools",
        "Obtaining written authorization from the system owner",
        "Scanning the target for vulnerabilities",
        "Creating attack scenarios"
      ],
      correct: 1,
      explanation: "Written authorization from the system owner is absolutely essential before beginning any penetration testing activities. Testing without proper authorization is illegal and unethical."
    },
    {
      question: "Which methodology provides the most comprehensive framework for penetration testing?",
      options: [
        "OWASP Top 10",
        "PTES (Penetration Testing Execution Standard)",
        "Cyber Kill Chain",
        "NIST Cybersecurity Framework"
      ],
      correct: 1,
      explanation: "PTES provides the most comprehensive framework for penetration testing, covering all phases from pre-engagement through reporting with detailed technical guidelines."
    }
  ]
}; 