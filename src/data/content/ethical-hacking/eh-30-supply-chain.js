/**
 * Ethical Hacking Module: Supply Chain Security Assessment
 * Module ID: eh-30
 */

export const supplyChainSecurityContent = {
  id: "eh-30",
  title: "Supply Chain Security Assessment",
  description: "Master supply chain security testing including software supply chain attacks, dependency analysis, CI/CD pipeline security, and third-party risk assessment.",
  difficulty: "Expert",
  estimatedTime: 105,
  objectives: [
    "Understand supply chain attack vectors and methodologies",
    "Master software dependency security analysis",
    "Learn CI/CD pipeline security testing techniques",
    "Develop skills in third-party vendor risk assessment",
    "Apply supply chain security testing in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-22", "eh-24", "eh-28"],
  sections: [
    {
      title: "Supply Chain Security Fundamentals",
      content: `
        <h2>Supply Chain Attack Landscape</h2>
        <p>Supply chain attacks target the software development and distribution process to compromise end-user systems through trusted channels.</p>
        
        <h3>Supply Chain Attack Vectors</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Attack Type</th>
              <th>Target</th>
              <th>Method</th>
              <th>Impact</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Dependency Confusion</td>
              <td>Package repositories</td>
              <td>Malicious packages with similar names</td>
              <td>Code execution, data theft</td>
            </tr>
            <tr>
              <td>Typosquatting</td>
              <td>Package names</td>
              <td>Misspelled legitimate package names</td>
              <td>Malware installation</td>
            </tr>
            <tr>
              <td>Compromised Dependencies</td>
              <td>Legitimate packages</td>
              <td>Inject malicious code into updates</td>
              <td>Widespread compromise</td>
            </tr>
            <tr>
              <td>Build System Compromise</td>
              <td>CI/CD pipelines</td>
              <td>Inject malicious code during build</td>
              <td>Backdoored software</td>
            </tr>
            <tr>
              <td>Code Signing Abuse</td>
              <td>Digital certificates</td>
              <td>Stolen or compromised signing keys</td>
              <td>Trusted malware distribution</td>
            </tr>
          </tbody>
        </table>

        <h3>Software Bill of Materials (SBOM)</h3>
        <h4>SBOM Analysis</h4>
        <pre><code># SBOM generation and analysis
# SPDX format example
SPDXVersion: SPDX-2.2
DataLicense: CC0-1.0
SPDXID: SPDXRef-DOCUMENT
Name: MyApplication-v1.0
DocumentNamespace: https://example.com/myapp-v1.0
Creator: Tool: MyTool

PackageName: express
SPDXID: SPDXRef-Package-express
PackageVersion: 4.18.2
PackageDownloadLocation: https://registry.npmjs.org/express/-/express-4.18.2.tgz
FilesAnalyzed: false
PackageLicenseConcluded: MIT
PackageLicenseDeclared: MIT

# Automated SBOM analysis
import json
import requests

class SBOMAnalyzer:
    def __init__(self, sbom_file):
        with open(sbom_file, 'r') as f:
            self.sbom = json.load(f)
    
    def analyze_vulnerabilities(self):
        vulnerabilities = []
        
        for component in self.sbom.get('components', []):
            name = component.get('name')
            version = component.get('version')
            
            # Check against vulnerability databases
            vulns = self.check_vulnerabilities(name, version)
            vulnerabilities.extend(vulns)
        
        return vulnerabilities
    
    def check_vulnerabilities(self, name, version):
        # Query OSV database
        url = "https://api.osv.dev/v1/query"
        data = {
            "package": {"name": name, "ecosystem": "npm"},
            "version": version
        }
        
        response = requests.post(url, json=data)
        if response.status_code == 200:
            return response.json().get('vulns', [])
        return []</code></pre>
      `,
      type: "text"
    },
    {
      title: "Dependency Security Testing",
      content: `
        <h2>Software Dependency Analysis</h2>
        <p>Modern applications rely heavily on third-party dependencies, creating significant attack surfaces that require comprehensive security testing.</p>

        <h3>Dependency Vulnerability Scanning</h3>
        <h4>Automated Scanning Tools</h4>
        <pre><code># npm audit for Node.js
npm audit
npm audit --audit-level high
npm audit fix

# Snyk vulnerability scanning
snyk test
snyk monitor
snyk test --severity-threshold=high

# OWASP Dependency Check
dependency-check --project "MyApp" --scan ./src --format JSON

# Safety for Python
safety check
safety check --json
safety check --file requirements.txt

# Bundler audit for Ruby
bundle audit
bundle audit --update

# Custom dependency scanner
import subprocess
import json

class DependencyScanner:
    def __init__(self, project_path):
        self.project_path = project_path
        self.results = {}
    
    def scan_npm_dependencies(self):
        cmd = ["npm", "audit", "--json"]
        result = subprocess.run(cmd, cwd=self.project_path, 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            audit_data = json.loads(result.stdout)
            return self.parse_npm_audit(audit_data)
        return []
    
    def scan_python_dependencies(self):
        cmd = ["safety", "check", "--json"]
        result = subprocess.run(cmd, cwd=self.project_path,
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            return json.loads(result.stdout)
        return []
    
    def parse_npm_audit(self, audit_data):
        vulnerabilities = []
        for vuln_id, vuln_data in audit_data.get('vulnerabilities', {}).items():
            vulnerabilities.append({
                'id': vuln_id,
                'severity': vuln_data.get('severity'),
                'title': vuln_data.get('title'),
                'package': vuln_data.get('name'),
                'patched_versions': vuln_data.get('patched_versions')
            })
        return vulnerabilities</code></pre>

        <h3>Package Repository Security</h3>
        <h4>Typosquatting Detection</h4>
        <pre><code># Typosquatting detection
import difflib
import requests

class TyposquattingDetector:
    def __init__(self):
        self.popular_packages = self.load_popular_packages()
    
    def load_popular_packages(self):
        # Load list of popular packages from registry
        url = "https://registry.npmjs.org/-/v1/search?text=popularity:high&size=1000"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            return [pkg['package']['name'] for pkg in data['objects']]
        return []
    
    def detect_typosquatting(self, package_name):
        suspicious_packages = []
        
        for popular_pkg in self.popular_packages:
            similarity = difflib.SequenceMatcher(None, package_name, popular_pkg).ratio()
            
            # Check for high similarity but not exact match
            if 0.7 < similarity < 1.0:
                suspicious_packages.append({
                    'package': package_name,
                    'similar_to': popular_pkg,
                    'similarity': similarity
                })
        
        return suspicious_packages
    
    def check_dependency_confusion(self, package_name):
        # Check if package exists in multiple registries
        registries = [
            "https://registry.npmjs.org",
            "https://npm.pkg.github.com",
            # Add internal registry URLs
        ]
        
        results = {}
        for registry in registries:
            url = f"{registry}/{package_name}"
            response = requests.get(url)
            results[registry] = response.status_code == 200
        
        # Flag if package exists in public but not private registry
        return results</code></pre>

        <h3>License Compliance Analysis</h3>
        <h4>License Risk Assessment</h4>
        <pre><code># License analysis
class LicenseAnalyzer:
    def __init__(self):
        self.high_risk_licenses = [
            'GPL-2.0', 'GPL-3.0', 'AGPL-3.0', 'LGPL-2.1', 'LGPL-3.0'
        ]
        self.medium_risk_licenses = [
            'MPL-2.0', 'EPL-1.0', 'EPL-2.0', 'CDDL-1.0'
        ]
        self.low_risk_licenses = [
            'MIT', 'Apache-2.0', 'BSD-2-Clause', 'BSD-3-Clause', 'ISC'
        ]
    
    def analyze_license_risk(self, license_name):
        if license_name in self.high_risk_licenses:
            return 'HIGH'
        elif license_name in self.medium_risk_licenses:
            return 'MEDIUM'
        elif license_name in self.low_risk_licenses:
            return 'LOW'
        else:
            return 'UNKNOWN'
    
    def scan_project_licenses(self, sbom_data):
        license_report = {}
        
        for component in sbom_data.get('components', []):
            license_name = component.get('license')
            if license_name:
                risk_level = self.analyze_license_risk(license_name)
                
                if risk_level not in license_report:
                    license_report[risk_level] = []
                
                license_report[risk_level].append({
                    'component': component.get('name'),
                    'version': component.get('version'),
                    'license': license_name
                })
        
        return license_report</code></pre>
      `,
      type: "text"
    },
    {
      title: "CI/CD Pipeline Security",
      content: `
        <h2>Build Pipeline Security Assessment</h2>
        <p>CI/CD pipelines are critical attack vectors in supply chain security, requiring comprehensive security testing and hardening.</p>

        <h3>Pipeline Security Analysis</h3>
        <h4>CI/CD Configuration Review</h4>
        <pre><code># GitHub Actions security analysis
name: Security Analysis
on: [push, pull_request]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      # Dependency scanning
      - name: Run Snyk
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: \${{ secrets.SNYK_TOKEN }}
      
      # SAST scanning
      - name: Run CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: javascript
      
      # Container scanning
      - name: Scan Docker image
        run: |
          docker build -t myapp .
          trivy image myapp

# Pipeline security checklist
pipeline_security_checks = {
    'secrets_management': [
        'No hardcoded secrets in code',
        'Secrets stored in secure vaults',
        'Secrets rotation implemented',
        'Least privilege access to secrets'
    ],
    'access_control': [
        'Multi-factor authentication required',
        'Role-based access control',
        'Regular access reviews',
        'Audit logging enabled'
    ],
    'build_security': [
        'Dependency vulnerability scanning',
        'Static code analysis',
        'Container image scanning',
        'Code signing implemented'
    ],
    'infrastructure': [
        'Build agents regularly updated',
        'Network segmentation',
        'Monitoring and alerting',
        'Backup and recovery procedures'
    ]
}</code></pre>

        <h3>Build Artifact Security</h3>
        <h4>Code Signing and Verification</h4>
        <pre><code># Code signing verification
import subprocess
import hashlib

class ArtifactVerifier:
    def __init__(self):
        self.trusted_signers = [
            # List of trusted certificate fingerprints
        ]
    
    def verify_signature(self, artifact_path):
        # Verify digital signature
        cmd = ["signtool", "verify", "/pa", artifact_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        return result.returncode == 0
    
    def calculate_checksums(self, artifact_path):
        checksums = {}
        
        with open(artifact_path, 'rb') as f:
            content = f.read()
            
            checksums['md5'] = hashlib.md5(content).hexdigest()
            checksums['sha1'] = hashlib.sha1(content).hexdigest()
            checksums['sha256'] = hashlib.sha256(content).hexdigest()
        
        return checksums
    
    def verify_provenance(self, artifact_path, expected_source):
        # Verify build provenance using SLSA framework
        # Check build metadata and attestations
        pass

# Container image security
def scan_container_image(image_name):
    # Trivy scanning
    cmd = ["trivy", "image", "--format", "json", image_name]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        return json.loads(result.stdout)
    return None

# Dockerfile security analysis
dockerfile_security_rules = [
    {
        'rule': 'No root user',
        'pattern': r'USER\s+root',
        'severity': 'HIGH'
    },
    {
        'rule': 'No hardcoded secrets',
        'pattern': r'(password|secret|key)\s*=\s*["\'][^"\']+["\']',
        'severity': 'CRITICAL'
    },
    {
        'rule': 'Use specific image tags',
        'pattern': r'FROM\s+[^:]+:latest',
        'severity': 'MEDIUM'
    }
]</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Enterprise Supply Chain Security Assessment",
    description: "Conduct comprehensive supply chain security assessment including dependency analysis, CI/CD pipeline testing, and third-party vendor risk evaluation.",
    environment: "Enterprise development environment with CI/CD pipelines, package repositories, and third-party integrations",
    tasks: [
      {
        category: "Dependency Analysis",
        tasks: [
          {
            task: "Perform comprehensive dependency vulnerability scanning",
            method: "Automated scanning tools and manual analysis of software dependencies",
            expectedFindings: "Vulnerable dependencies and license compliance issues",
            points: 25
          },
          {
            task: "Test for dependency confusion and typosquatting attacks",
            method: "Package repository analysis and malicious package detection",
            expectedFindings: "Supply chain attack vectors and vulnerable package management",
            points: 20
          }
        ]
      },
      {
        category: "CI/CD Security",
        tasks: [
          {
            task: "Assess CI/CD pipeline security configuration",
            method: "Pipeline configuration review and security control testing",
            expectedFindings: "Pipeline vulnerabilities and security misconfigurations",
            points: 25
          },
          {
            task: "Test build artifact integrity and code signing",
            method: "Artifact verification and provenance analysis",
            expectedFindings: "Build security weaknesses and signing vulnerabilities",
            points: 15
          }
        ]
      },
      {
        category: "Third-party Risk",
        tasks: [
          {
            task: "Evaluate third-party vendor security posture",
            method: "Vendor assessment questionnaires and security reviews",
            expectedFindings: "Third-party security risks and compliance gaps",
            points: 15
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive supply chain security assessment report",
      "Software Bill of Materials (SBOM) analysis and vulnerability report",
      "CI/CD pipeline security evaluation and recommendations",
      "Dependency security analysis with remediation priorities",
      "Third-party vendor risk assessment matrix",
      "Supply chain security monitoring and detection framework"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which attack exploits the trust relationship between internal and external package repositories?",
        options: [
          "Typosquatting",
          "Dependency confusion",
          "Code injection",
          "Man-in-the-middle"
        ],
        correct: 1,
        explanation: "Dependency confusion attacks exploit the trust relationship by uploading malicious packages to public repositories with names that match internal private packages."
      },
      {
        question: "What is the primary purpose of a Software Bill of Materials (SBOM)?",
        options: [
          "License compliance tracking",
          "Inventory of software components",
          "Vulnerability management",
          "All of the above"
        ],
        correct: 3,
        explanation: "SBOM serves multiple purposes including component inventory, license compliance tracking, and vulnerability management across the software supply chain."
      },
      {
        question: "Which CI/CD security practice is most critical for preventing supply chain attacks?",
        options: [
          "Regular updates",
          "Code signing",
          "Access control",
          "Monitoring"
        ],
        correct: 1,
        explanation: "Code signing is critical as it ensures the integrity and authenticity of build artifacts, preventing tampering in the supply chain."
      }
    ],
    practicalTasks: [
      {
        task: "Conduct dependency vulnerability scanning and analyze results",
        points: 25
      },
      {
        task: "Test CI/CD pipeline security and identify vulnerabilities",
        points: 25
      },
      {
        task: "Perform supply chain attack simulation (dependency confusion)",
        points: 25
      },
      {
        task: "Create comprehensive supply chain security monitoring framework",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "NIST Cybersecurity Supply Chain Risk Management",
      url: "https://csrc.nist.gov/Projects/Supply-Chain-Risk-Management",
      type: "framework"
    },
    {
      title: "SLSA Framework",
      url: "https://slsa.dev/",
      type: "framework"
    },
    {
      title: "OWASP Dependency Check",
      url: "https://owasp.org/www-project-dependency-check/",
      type: "tool"
    }
  ],
  tags: ["supply-chain", "dependency-security", "ci-cd", "sbom", "third-party-risk"],
  lastUpdated: "2024-01-15"
};
