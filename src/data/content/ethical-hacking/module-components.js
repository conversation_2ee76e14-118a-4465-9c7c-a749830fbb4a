/**
 * Enhanced Module Components for Ethical Hacking with Advanced Simulations
 */

import ThreatHuntingSimulation from '../../../components/simulations/ThreatHuntingSimulation';
import MalwareAnalysisLab from '../../../components/simulations/MalwareAnalysisLab';

// Import knowledge check content
import { ethicalHackingKnowledgeCheckContent } from './ethical-hacking-knowledge-check';
import { reconnaissanceKnowledgeCheckContent } from './reconnaissance-knowledge-check';

export const ethicalHackingModuleComponents = {
  // Module 1: Introduction to Ethical Hacking
  'eh-1': {
    'Ethical Hacking Fundamentals': {
      threatHuntingDemo: {
        component: ThreatHuntingSimulation,
        props: { scenarioType: 'demonstration' },
        title: 'Threat Hunting Demonstration',
        description: 'Introduction to proactive threat detection methodologies used in ethical hacking.'
      }
    },
    'Legal and Ethical Framework': {
      complianceSimulator: {
        component: 'ComplianceFrameworkSimulator',
        props: {},
        title: 'Legal Compliance Simulator',
        description: 'Interactive exploration of legal frameworks governing ethical hacking activities.'
      }
    },
    'Knowledge Check': {
      ethicalHackingKnowledgeCheck: {
        component: 'KnowledgeCheck',
        props: { content: ethicalHackingKnowledgeCheckContent },
        title: 'Ethical Hacking Fundamentals Quiz',
        description: 'Test your understanding of ethical hacking principles and legal considerations.'
      }
    }
  },

  // Module 2: Reconnaissance and Information Gathering
  'eh-2': {
    'OSINT and Passive Reconnaissance': {
      osintSimulator: {
        component: 'OSINTSimulator',
        props: { mode: 'passive' },
        title: 'OSINT Intelligence Gathering',
        description: 'Practice passive reconnaissance techniques using open source intelligence.'
      }
    },
    'Active Reconnaissance Techniques': {
      activeReconLab: {
        component: 'ActiveReconLab',
        props: { difficulty: 'intermediate' },
        title: 'Active Reconnaissance Laboratory',
        description: 'Hands-on practice with active information gathering techniques.'
      }
    },
    'Advanced Reconnaissance Tools': {
      maltegoPlatform: {
        component: 'MaltegoSimulator',
        props: {},
        title: 'Maltego Investigation Platform',
        description: 'Virtual Maltego environment for relationship mapping and intelligence analysis.'
      }
    },
    'Knowledge Check': {
      reconnaissanceKnowledgeCheck: {
        component: 'KnowledgeCheck',
        props: { content: reconnaissanceKnowledgeCheckContent },
        title: 'Reconnaissance Techniques Quiz',
        description: 'Assess your knowledge of information gathering methodologies.'
      }
    }
  },

  // Module 3: Scanning and Enumeration (Enhanced)
  'eh-3': {
    'Network Scanning Fundamentals': {
      networkScannerSimulation: {
        component: 'NetworkScannerSimulation',
        props: { scanType: 'comprehensive' },
        title: 'Advanced Network Scanner',
        description: 'Interactive network scanning laboratory with multiple techniques and evasion methods.'
      }
    },
    'Port Scanning Techniques': {
      portScanningLab: {
        component: 'PortScanningLab',
        props: { techniques: ['syn', 'connect', 'stealth'] },
        title: 'Port Scanning Techniques Lab',
        description: 'Practice various port scanning methods and stealth techniques.'
      }
    },
    'Service Enumeration': {
      serviceEnumerationLab: {
        component: 'ServiceEnumerationLab',
        props: { services: ['web', 'smb', 'dns', 'snmp'] },
        title: 'Service Enumeration Laboratory',
        description: 'Comprehensive service discovery and banner grabbing exercises.'
      }
    },
    'Vulnerability Scanning': {
      vulnerabilityScannerSimulation: {
        component: 'VulnerabilityScannerSimulation',
        props: { tools: ['nmap', 'openvas', 'nessus'] },
        title: 'Vulnerability Assessment Platform',
        description: 'Integrated vulnerability scanning and assessment environment.'
      }
    }
  },

  // Module 4: Vulnerability Assessment
  'eh-4': {
    'Web Application Vulnerabilities': {
      xssSimulation: {
        component: 'XSSSimulation',
        props: { scenarioType: 'comprehensive' },
        title: 'Cross-Site Scripting Laboratory',
        description: 'Interactive XSS vulnerability testing and exploitation environment.'
      },
      sqlInjectionLab: {
        component: 'SQLInjectionLab',
        props: { databases: ['mysql', 'mssql', 'postgresql'] },
        title: 'SQL Injection Testing Lab',
        description: 'Comprehensive SQL injection vulnerability assessment and exploitation.'
      }
    },
    'Network Vulnerabilities': {
      networkVulnScanner: {
        component: 'NetworkVulnerabilityScanner',
        props: { protocols: ['tcp', 'udp', 'icmp'] },
        title: 'Network Vulnerability Scanner',
        description: 'Advanced network vulnerability identification and analysis.'
      }
    },
    'System Vulnerabilities': {
      systemVulnAnalyzer: {
        component: 'SystemVulnerabilityAnalyzer',
        props: { systems: ['windows', 'linux', 'macos'] },
        title: 'System Vulnerability Analyzer',
        description: 'Operating system vulnerability assessment and patch analysis.'
      }
    }
  },

  // Module 5: Exploitation Techniques
  'eh-5': {
    'Web Application Exploitation': {
      webExploitFramework: {
        component: 'WebExploitFramework',
        props: { vulnerabilities: ['xss', 'sqli', 'csrf', 'lfi'] },
        title: 'Web Application Exploit Framework',
        description: 'Comprehensive web application exploitation laboratory.'
      }
    },
    'Network Service Exploitation': {
      networkExploitLab: {
        component: 'NetworkExploitLab',
        props: { services: ['ssh', 'rdp', 'ftp', 'telnet'] },
        title: 'Network Service Exploitation Lab',
        description: 'Practice exploitation of common network services and protocols.'
      }
    },
    'Payload Development': {
      payloadGenerator: {
        component: 'PayloadGenerator',
        props: { types: ['shellcode', 'reverse_shell', 'bind_shell'] },
        title: 'Payload Development Workshop',
        description: 'Learn to create and customize exploitation payloads.'
      }
    },
    'Metasploit Framework': {
      metasploitSimulator: {
        component: 'MetasploitSimulator',
        props: { modules: ['exploits', 'payloads', 'encoders', 'nops'] },
        title: 'Metasploit Framework Simulator',
        description: 'Virtual Metasploit environment for exploitation practice.'
      }
    }
  },

  // Module 6: Post-Exploitation and Persistence
  'eh-6': {
    'System Access and Control': {
      postExploitationLab: {
        component: 'PostExploitationLab',
        props: { systems: ['windows', 'linux'] },
        title: 'Post-Exploitation Laboratory',
        description: 'Practice post-exploitation techniques and system control methods.'
      }
    },
    'Privilege Escalation': {
      privEscSimulator: {
        component: 'PrivilegeEscalationSimulator',
        props: { techniques: ['local', 'domain', 'kernel'] },
        title: 'Privilege Escalation Simulator',
        description: 'Learn and practice various privilege escalation techniques.'
      }
    },
    'Persistence Mechanisms': {
      persistenceWorkshop: {
        component: 'PersistenceWorkshop',
        props: { methods: ['registry', 'services', 'scheduled_tasks'] },
        title: 'Persistence Mechanisms Workshop',
        description: 'Explore various methods for maintaining persistent access.'
      }
    },
    'Data Exfiltration': {
      dataExfiltrationLab: {
        component: 'DataExfiltrationLab',
        props: { channels: ['dns', 'http', 'ftp', 'email'] },
        title: 'Data Exfiltration Laboratory',
        description: 'Practice secure data extraction and covert communication channels.'
      }
    }
  },

  // Module 7: Malware Analysis and Reverse Engineering
  'eh-7': {
    'Static Malware Analysis': {
      malwareAnalysisLab: {
        component: MalwareAnalysisLab,
        props: { analysisType: 'static' },
        title: 'Static Malware Analysis Laboratory',
        description: 'Safe environment for static malware analysis and reverse engineering.'
      }
    },
    'Dynamic Malware Analysis': {
      dynamicAnalysisLab: {
        component: MalwareAnalysisLab,
        props: { analysisType: 'dynamic' },
        title: 'Dynamic Malware Analysis Lab',
        description: 'Behavioral analysis and runtime monitoring of malware samples.'
      }
    },
    'Reverse Engineering Workshop': {
      reverseEngineeringLab: {
        component: 'ReverseEngineeringLab',
        props: { tools: ['ida', 'ghidra', 'x64dbg', 'ollydbg'] },
        title: 'Reverse Engineering Workshop',
        description: 'Comprehensive reverse engineering laboratory with multiple analysis tools.'
      }
    },
    'Malware Development': {
      malwareDevelopmentLab: {
        component: 'MalwareDevelopmentLab',
        props: { types: ['trojan', 'keylogger', 'backdoor'] },
        title: 'Educational Malware Development',
        description: 'Learn malware development techniques for defensive purposes.'
      }
    }
  },

  // Module 8: Social Engineering
  'eh-8': {
    'Phishing Campaigns': {
      phishingSimulator: {
        component: 'PhishingSimulator',
        props: { campaigns: ['email', 'sms', 'voice'] },
        title: 'Phishing Campaign Simulator',
        description: 'Practice creating and analyzing phishing campaigns for awareness training.'
      }
    },
    'Physical Security Testing': {
      physicalSecurityLab: {
        component: 'PhysicalSecurityLab',
        props: { techniques: ['lockpicking', 'badge_cloning', 'tailgating'] },
        title: 'Physical Security Assessment Lab',
        description: 'Virtual physical security testing environment and techniques.'
      }
    },
    'Social Engineering Toolkit': {
      setFramework: {
        component: 'SETFramework',
        props: { modules: ['spearphishing', 'website_attack', 'infectious_media'] },
        title: 'Social Engineering Toolkit',
        description: 'Comprehensive social engineering attack simulation framework.'
      }
    }
  },

  // Module 9: Wireless Security Testing
  'eh-9': {
    'WiFi Security Assessment': {
      wifiSecurityLab: {
        component: 'WiFiSecurityLab',
        props: { protocols: ['wep', 'wpa', 'wpa2', 'wpa3'] },
        title: 'Wireless Security Assessment Lab',
        description: 'Comprehensive WiFi security testing and exploitation environment.'
      }
    },
    'Bluetooth Security Testing': {
      bluetoothSecurityLab: {
        component: 'BluetoothSecurityLab',
        props: { attacks: ['bluejacking', 'bluesnarfing', 'bluebugging'] },
        title: 'Bluetooth Security Laboratory',
        description: 'Bluetooth protocol security assessment and testing environment.'
      }
    },
    'RF Analysis and Exploitation': {
      rfAnalysisLab: {
        component: 'RFAnalysisLab',
        props: { frequencies: ['433mhz', '915mhz', '2.4ghz'] },
        title: 'Radio Frequency Analysis Lab',
        description: 'RF signal analysis and exploitation techniques laboratory.'
      }
    }
  },

  // Module 10: Advanced Persistent Threats
  'eh-10': {
    'APT Simulation': {
      aptSimulator: {
        component: 'APTSimulator',
        props: { groups: ['apt1', 'apt28', 'apt29', 'lazarus'] },
        title: 'Advanced Persistent Threat Simulator',
        description: 'Simulate real-world APT attack campaigns and techniques.'
      }
    },
    'Threat Hunting': {
      threatHuntingLab: {
        component: ThreatHuntingSimulation,
        props: { scenarioType: 'apt' },
        title: 'Threat Hunting Laboratory',
        description: 'Proactive threat detection and hunting methodology training.'
      }
    },
    'Digital Forensics': {
      digitalForensicsLab: {
        component: 'DigitalForensicsLab',
        props: { evidence_types: ['disk', 'memory', 'network', 'mobile'] },
        title: 'Digital Forensics Laboratory',
        description: 'Comprehensive digital forensics and incident response training.'
      }
    }
  }
};

// Enhanced use case mapping for practical scenarios
export const ethicalHackingUseCases = {
  // Reconnaissance Use Cases
  reconnaissance: [
    {
      id: 'EH-UC-001',
      title: 'Corporate OSINT Investigation',
      description: 'Comprehensive open source intelligence gathering on target organization',
      difficulty: 'Intermediate',
      estimatedTime: 120,
      objectives: [
        'Gather employee information from social media',
        'Identify technology stack and infrastructure',
        'Discover potential attack vectors',
        'Create comprehensive target profile'
      ],
      tools: ['Google Dorking', 'Shodan', 'LinkedIn', 'Maltego', 'theHarvester'],
      deliverables: [
        'Employee contact database',
        'Technology inventory',
        'Network infrastructure map',
        'Potential vulnerability assessment'
      ]
    }
  ],

  // Vulnerability Assessment Use Cases
  vulnerabilityAssessment: [
    {
      id: 'EH-UC-002',
      title: 'Web Application Security Assessment',
      description: 'Comprehensive security testing of web application',
      difficulty: 'Advanced',
      estimatedTime: 180,
      objectives: [
        'Identify all web application entry points',
        'Test for common web vulnerabilities',
        'Perform authentication and authorization testing',
        'Assess data protection mechanisms'
      ],
      tools: ['Burp Suite', 'OWASP ZAP', 'Nikto', 'SQLMap', 'Nessus'],
      vulnerabilities: ['XSS', 'SQL Injection', 'CSRF', 'LFI/RFI', 'Authentication Bypass'],
      deliverables: [
        'Vulnerability assessment report',
        'Risk prioritization matrix',
        'Proof of concept exploits',
        'Remediation recommendations'
      ]
    }
  ],

  // Penetration Testing Use Cases
  penetrationTesting: [
    {
      id: 'EH-UC-003',
      title: 'Internal Network Penetration Test',
      description: 'Simulate insider threat and lateral movement scenarios',
      difficulty: 'Expert',
      estimatedTime: 240,
      objectives: [
        'Achieve initial network access',
        'Perform privilege escalation',
        'Execute lateral movement',
        'Demonstrate data exfiltration'
      ],
      tools: ['Metasploit', 'Cobalt Strike', 'PowerShell Empire', 'Mimikatz', 'BloodHound'],
      techniques: ['Pass-the-Hash', 'Golden Ticket', 'DCSync', 'Kerberoasting'],
      deliverables: [
        'Attack path documentation',
        'Compromised system inventory',
        'Sensitive data identification',
        'Security control bypass documentation'
      ]
    }
  ]
};

// Assessment and progress tracking
export const ethicalHackingAssessments = {
  practical: [
    {
      id: 'EH-PRAC-001',
      title: 'Comprehensive Penetration Testing Lab',
      description: 'End-to-end penetration testing simulation',
      duration: 480, // 8 hours
      scenario: 'External penetration test against simulated corporate network',
      requirements: [
        'Complete reconnaissance and vulnerability assessment',
        'Achieve domain administrator access',
        'Document all findings and attack paths',
        'Provide comprehensive remediation report'
      ],
      evaluation: [
        'Technical accuracy of findings',
        'Thoroughness of methodology',
        'Quality of documentation',
        'Professional report presentation'
      ]
    }
  ],
  certification: [
    {
      id: 'EH-CERT-001',
      title: 'Certified Ethical Hacker Simulation',
      description: 'CEH certification preparation assessment',
      format: 'Multiple choice and practical labs',
      domains: [
        'Information Security and Ethical Hacking Overview',
        'Reconnaissance and Footprinting',
        'Scanning and Enumeration',
        'Vulnerability Assessment',
        'System Hacking and Post-Exploitation'
      ],
      passingScore: 70,
      timeLimit: 240
    }
  ]
};

export default ethicalHackingModuleComponents; 