/**
 * Ethical Hacking Module: IoT and Embedded Device Security
 * Module ID: eh-17
 */

export const iotSecurityContent = {
  id: "eh-17",
  title: "IoT and Embedded Device Security",
  description: "Master IoT and embedded device security testing including firmware analysis, hardware hacking, wireless protocol security, and industrial control system assessment.",
  difficulty: "Advanced",
  estimatedTime: 110,
  objectives: [
    "Understand IoT ecosystem security challenges and attack vectors",
    "Master firmware analysis and reverse engineering techniques",
    "Learn hardware hacking and physical security testing",
    "Develop skills in wireless IoT protocol security assessment",
    "Apply IoT security testing in industrial and consumer environments"
  ],
  prerequisites: ["eh-1", "eh-7", "eh-12", "eh-16"],
  sections: [
    {
      title: "IoT Security Fundamentals",
      content: `
        <h2>IoT Security Landscape</h2>
        <p>Internet of Things (IoT) devices present unique security challenges due to resource constraints, diverse protocols, and widespread deployment in critical infrastructure.</p>
        
        <h3>IoT Device Categories</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Category</th>
              <th>Examples</th>
              <th>Common Protocols</th>
              <th>Security Challenges</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Consumer IoT</td>
              <td>Smart home devices, wearables</td>
              <td>WiFi, Bluetooth, Zigbee</td>
              <td>Weak authentication, poor encryption</td>
            </tr>
            <tr>
              <td>Industrial IoT</td>
              <td>SCADA, PLCs, sensors</td>
              <td>Modbus, DNP3, OPC-UA</td>
              <td>Legacy protocols, air-gap bypass</td>
            </tr>
            <tr>
              <td>Medical IoT</td>
              <td>Pacemakers, insulin pumps</td>
              <td>Bluetooth LE, proprietary</td>
              <td>Life-critical vulnerabilities</td>
            </tr>
            <tr>
              <td>Automotive IoT</td>
              <td>Connected cars, telematics</td>
              <td>CAN bus, cellular, WiFi</td>
              <td>Safety-critical systems</td>
            </tr>
            <tr>
              <td>Smart City</td>
              <td>Traffic lights, meters</td>
              <td>LoRaWAN, NB-IoT, cellular</td>
              <td>Large-scale deployment risks</td>
            </tr>
          </tbody>
        </table>

        <h3>OWASP IoT Top 10 (2018)</h3>
        <ol>
          <li><strong>I1: Weak, Guessable, or Hardcoded Passwords</strong></li>
          <li><strong>I2: Insecure Network Services</strong></li>
          <li><strong>I3: Insecure Ecosystem Interfaces</strong></li>
          <li><strong>I4: Lack of Secure Update Mechanism</strong></li>
          <li><strong>I5: Use of Insecure or Outdated Components</strong></li>
          <li><strong>I6: Insufficient Privacy Protection</strong></li>
          <li><strong>I7: Insecure Data Transfer and Storage</strong></li>
          <li><strong>I8: Lack of Device Management</strong></li>
          <li><strong>I9: Insecure Default Settings</strong></li>
          <li><strong>I10: Lack of Physical Hardening</strong></li>
        </ol>

        <h3>IoT Attack Surface</h3>
        <ul>
          <li><strong>Device Hardware</strong> - Physical access, debug interfaces</li>
          <li><strong>Device Firmware</strong> - Operating system, applications</li>
          <li><strong>Communication</strong> - Wireless protocols, network traffic</li>
          <li><strong>Cloud Services</strong> - Backend APIs, data storage</li>
          <li><strong>Mobile Applications</strong> - Device control apps</li>
          <li><strong>Web Applications</strong> - Management interfaces</li>
        </ul>

        <h3>IoT Testing Methodology</h3>
        <ol>
          <li><strong>Information Gathering</strong> - Device identification and reconnaissance</li>
          <li><strong>Firmware Analysis</strong> - Static and dynamic firmware examination</li>
          <li><strong>Hardware Analysis</strong> - Physical security assessment</li>
          <li><strong>Communication Analysis</strong> - Protocol and traffic analysis</li>
          <li><strong>Application Testing</strong> - Mobile and web app security</li>
          <li><strong>Cloud Security</strong> - Backend service assessment</li>
        </ol>

        <h3>Legal and Ethical Considerations</h3>
        <div class="alert alert-warning">
          <strong>Important:</strong> IoT security testing must only be performed on devices you own or have explicit authorization to test. Testing critical infrastructure or medical devices requires special considerations and may be subject to regulatory oversight.
        </div>
      `,
      type: "text"
    },
    {
      title: "Firmware Analysis",
      content: `
        <h2>IoT Firmware Security Assessment</h2>
        <p>Firmware analysis is critical for understanding IoT device security, as it contains the core functionality and potential vulnerabilities.</p>

        <h3>Firmware Acquisition</h3>
        <h4>Firmware Extraction Methods</h4>
        <pre><code># Vendor sources
# Official firmware downloads
# Update mechanisms
# Support websites

# Hardware extraction
# JTAG interfaces
# SPI/I2C flash memory
# UART debug interfaces
# Chip-off techniques

# Network extraction
# Firmware update interception
# TFTP/HTTP downloads
# OTA update capture

# Tools for firmware extraction
binwalk firmware.bin  # Firmware analysis tool
dd if=/dev/mtdblock0 of=firmware.bin  # Direct flash dump
flashrom -r firmware.bin  # SPI flash reading</code></pre>

        <h4>Firmware Format Analysis</h4>
        <pre><code># File format identification
file firmware.bin
hexdump -C firmware.bin | head -20

# Binwalk analysis
binwalk firmware.bin
binwalk -e firmware.bin  # Extract filesystem
binwalk -A firmware.bin  # Architecture analysis
binwalk -E firmware.bin  # Entropy analysis

# Firmware modification kit
./extract-firmware.sh firmware.bin
# Modify extracted files
./build-firmware.sh</code></pre>

        <h3>Filesystem Analysis</h3>
        <h4>Common Filesystem Types</h4>
        <pre><code># SquashFS (common in embedded devices)
unsquashfs firmware.squashfs
mksquashfs modified_fs firmware_new.squashfs

# JFFS2 (Flash filesystem)
jefferson firmware.jffs2  # JFFS2 extractor

# UBIFS (UBI filesystem)
ubireader_extract_images firmware.ubi

# CRAMFS
cramfsck -x extracted_dir firmware.cramfs

# YAFFS2
unyaffs2 firmware.yaffs2</code></pre>

        <h4>Configuration and Secrets Analysis</h4>
        <pre><code># Search for sensitive information
grep -r "password" extracted_firmware/
grep -r "key" extracted_firmware/
grep -r "secret" extracted_firmware/
grep -r "admin" extracted_firmware/

# Configuration files
find . -name "*.conf" -o -name "*.cfg" -o -name "*.ini"
cat etc/passwd
cat etc/shadow

# Hardcoded credentials
strings firmware.bin | grep -i "password\|admin\|root"

# Private keys and certificates
find . -name "*.pem" -o -name "*.key" -o -name "*.crt"
openssl x509 -in certificate.pem -text -noout</code></pre>

        <h3>Binary Analysis</h3>
        <h4>Executable Analysis</h4>
        <pre><code># Architecture identification
file binary_executable
readelf -h binary_executable

# Disassembly
objdump -d binary_executable
radare2 binary_executable

# String analysis
strings binary_executable
rabin2 -z binary_executable  # Radare2 strings

# Function analysis
nm binary_executable  # Symbol table
objdump -t binary_executable  # Symbol table</code></pre>

        <h4>Vulnerability Scanning</h4>
        <pre><code># Automated firmware analysis
FACT (Firmware Analysis and Comparison Tool)
# Web-based firmware analysis platform

# Emba (Embedded Analyzer)
./emba.sh -l ./logs -f firmware.bin -i

# Firmwalker
./firmwalker.sh extracted_firmware/

# Custom vulnerability scanning
# Buffer overflow detection
# Format string vulnerabilities
# Command injection flaws
# Path traversal issues</code></pre>

        <h3>Firmware Emulation</h3>
        <h4>QEMU Emulation</h4>
        <pre><code># ARM firmware emulation
qemu-system-arm -M versatilepb -kernel vmlinuz -initrd initrd.img -hda rootfs.img

# MIPS firmware emulation
qemu-system-mips -M malta -kernel vmlinux -hda rootfs.img

# Firmware Analysis Toolkit (FAT)
./fat.py firmware.bin

# Firmadyne
# Automated firmware emulation system
./sources/extractor/extractor.py -b brand -sql 127.0.0.1 -np -nk firmware.bin</code></pre>

        <h4>Dynamic Analysis</h4>
        <pre><code># Network service enumeration
nmap -sS -O emulated_device_ip
nmap -sU emulated_device_ip

# Web interface testing
dirb http://emulated_device_ip/
nikto -h http://emulated_device_ip/

# Service interaction
telnet emulated_device_ip 23
ssh root@emulated_device_ip
curl http://emulated_device_ip/cgi-bin/test</code></pre>
      `,
      type: "text"
    },
    {
      title: "Hardware Security Testing",
      content: `
        <h2>IoT Hardware Security Assessment</h2>
        <p>Hardware security testing involves physical analysis of IoT devices to identify vulnerabilities in circuit design, debug interfaces, and physical protections.</p>

        <h3>Hardware Reconnaissance</h3>
        <h4>Device Teardown</h4>
        <pre><code># Physical examination
# PCB layout analysis
# Component identification
# Connector identification
# Test point location

# Tools required:
# - Screwdrivers and spudgers
# - Multimeter
# - Oscilloscope
# - Logic analyzer
# - Microscope
# - Hot air station</code></pre>

        <h4>Component Identification</h4>
        <pre><code># Microcontroller identification
# Read part numbers
# Datasheet analysis
# Pin configuration
# Memory mapping

# Flash memory identification
# SPI flash chips
# eMMC storage
# SD cards
# NAND flash

# Communication interfaces
# UART pins
# JTAG connectors
# SPI interfaces
# I2C buses</code></pre>

        <h3>Debug Interface Exploitation</h3>
        <h4>UART Interface</h4>
        <pre><code># UART pin identification
# VCC (3.3V or 5V)
# GND (Ground)
# TX (Transmit)
# RX (Receive)

# Baud rate detection
# Common rates: 9600, 19200, 38400, 57600, 115200
# Use oscilloscope or logic analyzer

# UART connection
screen /dev/ttyUSB0 115200
minicom -D /dev/ttyUSB0 -b 115200
picocom -b 115200 /dev/ttyUSB0

# Bootloader interaction
# U-Boot commands
printenv  # Print environment variables
setenv  # Set environment variables
boot  # Boot the system
md  # Memory dump</code></pre>

        <h4>JTAG Interface</h4>
        <pre><code># JTAG pin identification
# TDI (Test Data In)
# TDO (Test Data Out)
# TCK (Test Clock)
# TMS (Test Mode Select)
# TRST (Test Reset) - optional

# JTAG tools
openocd  # Open On-Chip Debugger
jtagulator  # JTAG pin finder
bus pirate  # Multi-protocol tool

# OpenOCD configuration
interface ftdi
ftdi_vid_pid 0x0403 0x6010
adapter_khz 1000
transport select jtag

# Memory dumping via JTAG
dump_image firmware_dump.bin 0x00000000 0x100000</code></pre>

        <h3>Side-Channel Attacks</h3>
        <h4>Power Analysis</h4>
        <pre><code># Simple Power Analysis (SPA)
# Observe power consumption patterns
# Identify cryptographic operations
# Extract timing information

# Differential Power Analysis (DPA)
# Statistical analysis of power traces
# Extract secret keys from crypto operations

# Tools for power analysis
# ChipWhisperer
# PicoScope
# Oscilloscope with current probe</code></pre>

        <h4>Electromagnetic Analysis</h4>
        <pre><code># EM emanations capture
# Near-field probes
# Spectrum analyzer
# Software-defined radio (SDR)

# EM analysis tools
# GNU Radio
# Universal Radio Hacker (URH)
# Inspectrum</code></pre>

        <h3>Flash Memory Extraction</h3>
        <h4>SPI Flash Reading</h4>
        <pre><code># SPI flash identification
# Read chip markings
# Datasheet lookup
# Pin configuration

# SPI flash tools
flashrom -p buspirate_spi:dev=/dev/ttyUSB0 -r firmware.bin
flashrom -p ch341a_spi -r firmware.bin

# Bus Pirate SPI commands
# Enter SPI mode
# Set speed and configuration
# Read flash memory</code></pre>

        <h4>eMMC/SD Card Extraction</h4>
        <pre><code># eMMC reading
# Requires eMMC adapter
# Direct connection to eMMC pins
dd if=/dev/mmcblk0 of=emmc_dump.img

# SD card analysis
# Remove SD card from device
# Image with dd or specialized tools
dd if=/dev/sdb of=sdcard_dump.img</code></pre>

        <h3>Hardware Modification</h3>
        <h4>Firmware Modification</h4>
        <pre><code># Firmware patching
# Extract firmware
# Modify binaries or filesystem
# Recalculate checksums
# Flash modified firmware

# Bootloader modification
# Bypass signature verification
# Enable debug features
# Modify boot parameters</code></pre>

        <h4>Hardware Implants</h4>
        <pre><code># Hardware implant concepts
# Malicious chip insertion
# PCB modification
# Wire tapping
# Component replacement

# Detection methods
# X-ray imaging
# Microscopic inspection
# Electrical testing
# Functional testing</code></pre>
      `,
      type: "text"
    },
    {
      title: "IoT Protocol Security",
      content: `
        <h2>IoT Communication Protocol Security</h2>
        <p>IoT devices use various communication protocols, each with unique security considerations and potential vulnerabilities.</p>

        <h3>Wireless Protocol Analysis</h3>
        <h4>Zigbee Security Testing</h4>
        <pre><code># Zigbee tools
# Killerbee framework
zbstumbler  # Network discovery
zbdump  # Packet capture
zbreplay  # Packet replay

# Zigbee attacks
# Key extraction
# Network infiltration
# Replay attacks
# Jamming attacks

# Zigbee sniffing
# Requires Zigbee sniffer hardware
# ApiMote or similar device
zbwireshark  # Wireshark integration</code></pre>

        <h4>LoRaWAN Security Testing</h4>
        <pre><code># LoRaWAN analysis tools
# GNU Radio with LoRa blocks
# SDR hardware (HackRF, USRP)
# gr-lora GNU Radio module

# LoRaWAN attacks
# Replay attacks
# Bit flipping
# ACK spoofing
# Wormhole attacks

# LoRaWAN packet analysis
# Decrypt with known keys
# Analyze frame structure
# Identify device fingerprints</code></pre>

        <h3>Industrial Protocol Security</h3>
        <h4>Modbus Security Testing</h4>
        <pre><code># Modbus tools
nmap --script modbus-discover *************
mbtget -r1 -c5 *************  # Read coils

# Modbus attacks
# Function code fuzzing
# Register manipulation
# Denial of service
# Man-in-the-middle

# Modbus packet analysis
wireshark  # Modbus dissector
# Filter: modbus</code></pre>

        <h4>DNP3 Security Testing</h4>
        <pre><code># DNP3 analysis
# Triangle MicroWorks tools
# Aegis DNP3 fuzzer
# Wireshark DNP3 dissector

# DNP3 vulnerabilities
# Authentication bypass
# Unsolicited response injection
# Buffer overflow attacks
# Replay attacks</code></pre>

        <h3>Cellular IoT Security</h3>
        <h4>NB-IoT and LTE-M Testing</h4>
        <pre><code># Cellular IoT analysis
# Software-defined radio (SDR)
# OpenBTS for GSM
# srsLTE for LTE

# Cellular attacks
# IMSI catching
# SMS interception
# Data manipulation
# Jamming attacks

# Tools for cellular analysis
# Osmocom suite
# YateBTS
# OpenAirInterface</code></pre>

        <h3>Bluetooth Low Energy (BLE)</h3>
        <h4>BLE Security Testing</h4>
        <pre><code># BLE scanning and enumeration
hcitool lescan
gatttool -b AA:BB:CC:DD:EE:FF --primary

# BLE packet capture
btmon  # Bluetooth monitor
hcidump -i hci0

# BLE attacks
# Passive eavesdropping
# Man-in-the-middle
# Replay attacks
# Fuzzing

# BLE tools
# Ubertooth One
# nRF52840 dongle
# BlueZ stack</code></pre>

        <h3>Protocol Fuzzing</h3>
        <h4>Custom Protocol Fuzzing</h4>
        <pre><code># Boofuzz framework
from boofuzz import *

# Define protocol structure
s_initialize("custom_protocol")
s_string("HEADER")
s_delim(" ")
s_string("COMMAND", fuzzable=True)
s_delim(" ")
s_int(0, format="ascii", fuzzable=True)

# Fuzzing session
session = Session(target=Target(connection=TCPSocketConnection("*************", 1234)))
session.fuzz()</code></pre>

        <h4>Network Protocol Testing</h4>
        <pre><code># Scapy for packet crafting
from scapy.all import *

# Custom packet creation
packet = IP(dst="*************")/UDP(dport=1234)/Raw(load="FUZZ_DATA")
send(packet)

# Protocol state fuzzing
# Test different protocol states
# Invalid state transitions
# Malformed packets</code></pre>

        <h3>Cloud and Backend Security</h3>
        <h4>IoT Cloud Platform Testing</h4>
        <pre><code># API security testing
# Authentication mechanisms
# Authorization controls
# Data validation
# Rate limiting

# Common IoT cloud platforms
# AWS IoT Core
# Azure IoT Hub
# Google Cloud IoT
# ThingWorx

# MQTT security testing
mosquitto_pub -h broker.example.com -t "topic/test" -m "message"
mosquitto_sub -h broker.example.com -t "topic/+"

# CoAP security testing
coap-client -m get coap://[::1]/test</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Smart Home IoT Security Assessment",
    description: "Conduct comprehensive security assessment of a smart home ecosystem including multiple IoT devices, wireless protocols, and cloud services.",
    environment: "Smart home lab with various IoT devices, wireless protocols, mobile apps, and cloud services",
    tasks: [
      {
        category: "Firmware Analysis",
        tasks: [
          {
            task: "Extract and analyze firmware from smart thermostat",
            method: "Hardware extraction via UART/JTAG and firmware analysis tools",
            expectedFindings: "Hardcoded credentials, configuration files, and vulnerabilities",
            points: 25
          },
          {
            task: "Emulate extracted firmware and test network services",
            method: "QEMU emulation and network service enumeration",
            expectedFindings: "Running services, web interfaces, and attack vectors",
            points: 20
          }
        ]
      },
      {
        category: "Hardware Security Testing",
        tasks: [
          {
            task: "Identify and exploit debug interfaces on smart lock",
            method: "Hardware teardown, UART identification, and bootloader access",
            expectedFindings: "Debug console access and firmware modification capability",
            points: 20
          },
          {
            task: "Perform side-channel analysis on smart card reader",
            method: "Power analysis and electromagnetic emanation capture",
            expectedFindings: "Cryptographic key extraction or timing vulnerabilities",
            points: 15
          }
        ]
      },
      {
        category: "Protocol Security Assessment",
        tasks: [
          {
            task: "Analyze Zigbee network security and perform attacks",
            method: "Zigbee sniffing, key extraction, and network infiltration",
            expectedFindings: "Network key compromise and device control",
            points: 15
          }
        ]
      },
      {
        category: "Cloud and Mobile Security",
        tasks: [
          {
            task: "Test IoT cloud platform and mobile application security",
            method: "API testing, authentication bypass, and data manipulation",
            expectedFindings: "Cloud service vulnerabilities and mobile app flaws",
            points: 5
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive IoT security assessment report",
      "Firmware analysis results with extracted secrets and configurations",
      "Hardware security evaluation with debug interface documentation",
      "Wireless protocol security analysis and attack demonstrations",
      "Cloud and mobile application security findings",
      "IoT device hardening recommendations and security best practices"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which OWASP IoT Top 10 category addresses inadequate physical security measures?",
        options: [
          "I8: Lack of Device Management",
          "I9: Insecure Default Settings",
          "I10: Lack of Physical Hardening",
          "I2: Insecure Network Services"
        ],
        correct: 2,
        explanation: "I10: Lack of Physical Hardening specifically addresses inadequate physical security measures in IoT devices."
      },
      {
        question: "What is the most common debug interface found in IoT devices?",
        options: [
          "JTAG",
          "UART",
          "SPI",
          "I2C"
        ],
        correct: 1,
        explanation: "UART is the most commonly found debug interface in IoT devices, often used for console access and debugging."
      },
      {
        question: "Which tool is specifically designed for Zigbee security testing?",
        options: [
          "Wireshark",
          "Nmap",
          "Killerbee",
          "Metasploit"
        ],
        correct: 2,
        explanation: "Killerbee is a framework specifically designed for Zigbee security research and testing."
      }
    ],
    practicalTasks: [
      {
        task: "Extract and analyze firmware from an IoT device using hardware techniques",
        points: 25
      },
      {
        task: "Identify and exploit debug interfaces (UART/JTAG) on embedded hardware",
        points: 25
      },
      {
        task: "Perform wireless protocol security testing on Zigbee or BLE devices",
        points: 25
      },
      {
        task: "Conduct comprehensive IoT ecosystem security assessment including cloud services",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "IoT Penetration Testing Cookbook",
      url: "https://www.packtpub.com/product/iot-penetration-testing-cookbook/9781787280571",
      type: "book"
    },
    {
      title: "Binwalk Firmware Analysis Tool",
      url: "https://github.com/ReFirmLabs/binwalk",
      type: "tool"
    },
    {
      title: "OWASP IoT Security Guidance",
      url: "https://owasp.org/www-project-iot-security-guidance/",
      type: "guide"
    }
  ],
  tags: ["iot-security", "firmware-analysis", "hardware-hacking", "embedded-systems", "wireless-protocols"],
  lastUpdated: "2024-01-15"
};
