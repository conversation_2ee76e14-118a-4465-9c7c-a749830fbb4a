/**
 * Ethical Hacking Fundamentals Learning Path
 * Comprehensive cybersecurity offensive security training from basics to advanced
 */

// Import all module content
import { ethicalHackingIntroContent } from './ethical-hacking-intro.js';
import { reconnaissanceContent } from './reconnaissance.js';
import { scanningEnumerationContent } from './scanning-enumeration.js';
import { vulnerabilityAssessmentContent } from './vulnerability-assessment.js';
import { webApplicationTestingContent } from './web-application-testing.js';
import { networkPenetrationTestingContent } from './eh-6-network-penetration.js';
import { systemHackingContent } from './eh-7-system-hacking.js';
import { passwordAttacksContent } from './eh-8-password-attacks.js';
import { privilegeEscalationContent } from './eh-9-privilege-escalation.js';
import { postExploitationContent } from './eh-10-post-exploitation.js';
import { socialEngineeringContent } from './eh-11-social-engineering.js';
import { wirelessSecurityContent } from './eh-12-wireless-security.js';
import { mobileSecurityContent } from './eh-13-mobile-security.js';
import { webAdvancedContent } from './eh-14-web-advanced.js';
import { apiSecurityContent } from './eh-15-api-security.js';
import { malwareAnalysisContent } from './eh-16-malware-analysis.js';
import { iotSecurityContent } from './eh-17-iot-security.js';
import { cloudPenetrationContent } from './eh-18-cloud-penetration.js';
import { activeDirectoryContent } from './eh-19-active-directory.js';
import { aptSimulationContent } from './eh-20-apt-simulation.js';
import { incidentResponseContent } from './eh-21-incident-response.js';
import { automationScriptingContent } from './eh-22-automation-scripting.js';
import { threatModelingContent } from './eh-23-threat-modeling.js';
import { secureCodingContent } from './eh-24-secure-coding.js';
import { physicalSecurityContent } from './eh-25-physical-security.js';
import { osintReconnaissanceContent } from './eh-26-osint-reconnaissance.js';
import { wirelessAdvancedContent } from './eh-27-wireless-advanced.js';
import { blockchainSecurityContent } from './eh-28-blockchain-security.js';
import { aiMlSecurityContent } from './eh-29-ai-ml-security.js';
import { supplyChainSecurityContent } from './eh-30-supply-chain.js';
import { zeroTrustContent } from './eh-31-zero-trust.js';
import { devSecOpsAdvancedContent } from './eh-32-devsecops-advanced.js';
import { complianceRegulatoryContent } from './eh-33-compliance-regulatory.js';
import { crisisManagementContent } from './eh-34-crisis-management.js';
import { advancedCryptographyContent } from './eh-35-advanced-cryptography.js';
import { quantumSecurityContent } from './eh-36-quantum-security.js';
import { icsScadaContent } from './eh-37-ics-scada.js';
import { medicalDeviceContent } from './eh-38-medical-device.js';
import { automotiveIoTContent } from './eh-39-automotive-iot.js';
import { financialServicesContent } from './eh-40-financial-services.js';
import { threatHuntingContent } from './eh-41-threat-hunting.js';
import { socTestingContent } from './eh-42-soc-testing.js';
import { threatIntelligenceContent } from './eh-43-threat-intelligence.js';
import { malwareAnalysisContent as advancedMalwareAnalysisContent } from './eh-44-malware-analysis.js';
import { cloudArchitectureContent } from './eh-45-cloud-architecture.js';
import { emergingTechContent } from './eh-46-emerging-tech.js';
import { securityMetricsContent } from './eh-47-security-metrics.js';
import { advancedSocialEngineeringContent } from './eh-48-advanced-social-engineering.js';
import { cyberWarfareContent } from './eh-49-cyber-warfare.js';
import { futureCybersecurityContent } from './eh-50-future-cybersecurity.js';
// Temporarily comment out missing imports until files are created
// import { networkPenetrationTestingContent } from './network-penetration-testing.js';
// import { systemHackingContent } from './system-hacking.js';
// import { malwareThreatContent } from './malware-threat.js';
// import { sniffingContent } from './sniffing.js';
// import { socialEngineeringContent } from './social-engineering.js';
// import { denialOfServiceContent } from './denial-of-service.js';
// import { sessionHijackingContent } from './session-hijacking.js';
// import { webserverHackingContent } from './webserver-hacking.js';
// import { webApplicationHackingContent } from './web-application-hacking.js';
// import { sqlInjectionContent } from './sql-injection.js';
// import { wirelessNetworkHackingContent } from './wireless-network-hacking.js';
// import { mobilePlatformHackingContent } from './mobile-platform-hacking.js';
// import { iotHackingContent } from './iot-hacking.js';
// import { cloudComputingHackingContent } from './cloud-computing-hacking.js';
// import { cryptographyContent } from './cryptography.js';

export const ethicalHackingLearningPath = {
  id: "ethical-hacking-fundamentals",
  title: "Ethical Hacking Fundamentals: Comprehensive Penetration Testing",
  description: "Master ethical hacking and penetration testing from fundamental concepts to advanced attack techniques, designed for cybersecurity professionals pursuing CEH, OSCP, and professional penetration testing careers.",
  category: "Offensive Security",
  difficulty: "Beginner to Expert",
  estimatedTime: "120+ hours",
  prerequisites: [
    "Basic networking knowledge",
    "Understanding of operating systems (Windows/Linux)",
    "Basic programming/scripting knowledge",
    "Cybersecurity fundamentals"
  ],
  outcomes: [
    "Master ethical hacking methodologies and frameworks",
    "Perform comprehensive penetration testing assessments",
    "Identify and exploit security vulnerabilities responsibly",
    "Develop advanced offensive security techniques",
    "Understand legal and ethical considerations in security testing",
    "Prepare for industry certifications (CEH, OSCP, CISSP)"
  ],
  modules: [
    ethicalHackingIntroContent,
    reconnaissanceContent,
    scanningEnumerationContent,
    vulnerabilityAssessmentContent,
    webApplicationTestingContent,
    networkPenetrationTestingContent,
    systemHackingContent,
    passwordAttacksContent,
    privilegeEscalationContent,
    postExploitationContent,
    socialEngineeringContent,
    wirelessSecurityContent,
    mobileSecurityContent,
    webAdvancedContent,
    apiSecurityContent,
    malwareAnalysisContent,
    iotSecurityContent,
    cloudPenetrationContent,
    activeDirectoryContent,
    aptSimulationContent,
    incidentResponseContent,
    automationScriptingContent,
    threatModelingContent,
    secureCodingContent,
    physicalSecurityContent,
    osintReconnaissanceContent,
    wirelessAdvancedContent,
    blockchainSecurityContent,
    aiMlSecurityContent,
    supplyChainSecurityContent,
    zeroTrustContent,
    devSecOpsAdvancedContent,
    complianceRegulatoryContent,
    crisisManagementContent,
    advancedCryptographyContent,
    quantumSecurityContent,
    icsScadaContent,
    medicalDeviceContent,
    automotiveIoTContent,
    financialServicesContent,
    threatHuntingContent,
    socTestingContent,
    threatIntelligenceContent,
    advancedMalwareAnalysisContent,
    cloudArchitectureContent,
    emergingTechContent,
    securityMetricsContent,
    advancedSocialEngineeringContent,
    cyberWarfareContent,
    futureCybersecurityContent
    // All 50 modules complete! 🎉
    // malwareThreatContent,
    // sniffingContent,
    // socialEngineeringContent,
    // denialOfServiceContent,
    // sessionHijackingContent,
    // webserverHackingContent,
    // webApplicationHackingContent,
    // sqlInjectionContent,
    // wirelessNetworkHackingContent,
    // mobilePlatformHackingContent,
    // iotHackingContent,
    // cloudComputingHackingContent,
    // cryptographyContent
  ]
};

export const getAllEthicalHackingModules = () => {
  return ethicalHackingLearningPath.modules;
};

export const getEthicalHackingModuleById = (id) => {
  return ethicalHackingLearningPath.modules.find(module => module.id === id);
}; 