/**
 * Scanning and Enumeration Module with Advanced Simulations
 */

export const scanningEnumerationContent = {
  id: "eh-3",
  pathId: "ethical-hacking-fundamentals",
  title: "Scanning & Enumeration: Advanced Network Discovery",
  description: "Master network scanning, port enumeration, and service discovery techniques with interactive simulations and hands-on virtual labs.",
  objectives: [
    "Master network scanning methodologies and techniques",
    "Understand port enumeration and service discovery",
    "Learn stealth scanning and evasion techniques",
    "Practice vulnerability scanning and assessment",
    "Implement automated scanning workflows",
    "Analyze and interpret scan results effectively"
  ],
  difficulty: "Beginner",
  estimatedTime: 150,
  sections: [
    {
      title: "Network Scanning Fundamentals",
      content: `
        <h2>Network Scanning Fundamentals</h2>
        <p>Network scanning is the process of identifying active hosts, open ports, and running services on a target network. This is a critical phase in ethical hacking that provides the foundation for vulnerability assessment and exploitation.</p>
        
        <h3>Types of Network Scans</h3>
        <ul>
          <li><strong>Network Discovery (Ping Sweep):</strong>
            <ul>
              <li>ICMP Echo Request/Reply</li>
              <li>ARP requests (local network)</li>
              <li>TCP/UDP ping alternatives</li>
              <li>Broadcast and multicast discovery</li>
            </ul>
          </li>
          <li><strong>Port Scanning:</strong>
            <ul>
              <li>TCP Connect() scans</li>
              <li>TCP SYN scans (half-open)</li>
              <li>UDP scans</li>
              <li>Specialized protocol scans</li>
            </ul>
          </li>
          <li><strong>Service Enumeration:</strong>
            <ul>
              <li>Banner grabbing and service identification</li>
              <li>Version detection and fingerprinting</li>
              <li>Service-specific enumeration</li>
              <li>Application layer discovery</li>
            </ul>
          </li>
          <li><strong>OS Fingerprinting:</strong>
            <ul>
              <li>Active fingerprinting techniques</li>
              <li>Passive fingerprinting methods</li>
              <li>TCP/IP stack analysis</li>
              <li>Application behavior analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Scanning Methodologies</h3>
        <ul>
          <li><strong>Reconnaissance Phase:</strong>
            <ul>
              <li>Target identification and scope definition</li>
              <li>Network topology mapping</li>
              <li>Asset discovery and inventory</li>
              <li>Initial vulnerability assessment</li>
            </ul>
          </li>
          <li><strong>Scanning Strategy:</strong>
            <ul>
              <li>Stealth vs. speed considerations</li>
              <li>Timing and rate limiting</li>
              <li>Evasion technique selection</li>
              <li>Result correlation and analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Legal and Ethical Considerations</h3>
        <ul>
          <li><strong>Authorization Requirements:</strong>
            <ul>
              <li>Written permission for all scanning activities</li>
              <li>Scope limitations and boundaries</li>
              <li>Time windows and restrictions</li>
              <li>Notification and coordination procedures</li>
            </ul>
          </li>
          <li><strong>Impact Minimization:</strong>
            <ul>
              <li>Non-disruptive scanning techniques</li>
              <li>Resource usage considerations</li>
              <li>Service availability protection</li>
              <li>Data integrity preservation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Port Scanning Techniques",
      content: `
        <h2>Port Scanning Techniques</h2>
        <p>Port scanning is the process of probing target systems to identify open network ports and the services running on them. Understanding different scanning techniques allows for comprehensive network assessment while maintaining stealth.</p>
        
        <h3>TCP Scanning Methods</h3>
        <ul>
          <li><strong>TCP Connect() Scan:</strong>
            <ul>
              <li>Full three-way handshake completion</li>
              <li>Reliable but easily detectable</li>
              <li>No special privileges required</li>
              <li>Command: <code>nmap -sT target</code></li>
            </ul>
          </li>
          <li><strong>TCP SYN Scan (Half-Open):</strong>
            <ul>
              <li>Sends SYN, analyzes response</li>
              <li>Doesn't complete connection</li>
              <li>Stealthier than connect() scan</li>
              <li>Command: <code>nmap -sS target</code></li>
            </ul>
          </li>
          <li><strong>TCP FIN Scan:</strong>
            <ul>
              <li>Sends FIN packet to closed ports</li>
              <li>Bypasses simple packet filters</li>
              <li>Works against RFC-compliant systems</li>
              <li>Command: <code>nmap -sF target</code></li>
            </ul>
          </li>
          <li><strong>TCP NULL Scan:</strong>
            <ul>
              <li>Sends packet with no flags set</li>
              <li>Elicits RST response from closed ports</li>
              <li>Effective against certain firewalls</li>
              <li>Command: <code>nmap -sN target</code></li>
            </ul>
          </li>
          <li><strong>TCP XMAS Scan:</strong>
            <ul>
              <li>Sets FIN, PSH, and URG flags</li>
              <li>Named for "lit up like Christmas tree"</li>
              <li>Similar behavior to FIN scan</li>
              <li>Command: <code>nmap -sX target</code></li>
            </ul>
          </li>
        </ul>
        
        <h3>UDP Scanning Methods</h3>
        <ul>
          <li><strong>UDP Scan Challenges:</strong>
            <ul>
              <li>Connectionless protocol complexity</li>
              <li>ICMP Port Unreachable messages</li>
              <li>Rate limiting and timeouts</li>
              <li>Stateful firewall considerations</li>
            </ul>
          </li>
          <li><strong>UDP Scan Techniques:</strong>
            <ul>
              <li>Basic UDP scan: <code>nmap -sU target</code></li>
              <li>Service-specific probes</li>
              <li>Protocol-aware scanning</li>
              <li>Timing optimization strategies</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced Scanning Techniques</h3>
        <ul>
          <li><strong>Idle Scan (Zombie Scan):</strong>
            <ul>
              <li>Uses intermediate host as proxy</li>
              <li>Exploits predictable IP ID sequences</li>
              <li>Provides ultimate stealth</li>
              <li>Command: <code>nmap -sI zombie_host target</code></li>
            </ul>
          </li>
          <li><strong>FTP Bounce Scan:</strong>
            <ul>
              <li>Exploits FTP PORT command</li>
              <li>Routes scan through FTP server</li>
              <li>Bypasses firewall restrictions</li>
              <li>Command: <code>nmap -b ftp_server target</code></li>
            </ul>
          </li>
          <li><strong>Fragmentation Attacks:</strong>
            <ul>
              <li>Splits packets to evade detection</li>
              <li>Tiny fragment overlapping</li>
              <li>Fragment reassembly exploitation</li>
              <li>Command: <code>nmap -f -f target</code></li>
            </ul>
          </li>
        </ul>
        
        <h3>Evasion Techniques</h3>
        <ul>
          <li><strong>Timing Controls:</strong>
            <ul>
              <li>Rate limiting to avoid detection</li>
              <li>Random delays between probes</li>
              <li>Spread scans over time</li>
              <li>Commands: <code>nmap -T0</code> through <code>-T5</code></li>
            </ul>
          </li>
          <li><strong>Source Port Manipulation:</strong>
            <ul>
              <li>Use common source ports (53, 80, 443)</li>
              <li>Bypass port-based filtering</li>
              <li>Mimic legitimate traffic</li>
              <li>Command: <code>nmap --source-port 53 target</code></li>
            </ul>
          </li>
          <li><strong>Decoy Scanning:</strong>
            <ul>
              <li>Generate noise with fake sources</li>
              <li>Hide real scanner among decoys</li>
              <li>Overwhelm log analysis</li>
              <li>Command: <code>nmap -D decoy1,decoy2,ME target</code></li>
            </ul>
          </li>
          <li><strong>Proxy and Relay Techniques:</strong>
            <ul>
              <li>Route through proxy servers</li>
              <li>Use anonymization networks</li>
              <li>Leverage compromised systems</li>
              <li>Implement traffic laundering</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Service Enumeration and Fingerprinting",
      content: `
        <h2>Service Enumeration and Fingerprinting</h2>
        <p>Once open ports are identified, the next step is to determine what services are running and gather detailed information about their configurations, versions, and potential vulnerabilities.</p>
        
        <h3>Banner Grabbing Techniques</h3>
        <ul>
          <li><strong>Manual Banner Grabbing:</strong>
            <ul>
              <li>Telnet connections: <code>telnet target 80</code></li>
              <li>Netcat connections: <code>nc target 22</code></li>
              <li>Custom socket programming</li>
              <li>Protocol-specific clients</li>
            </ul>
          </li>
          <li><strong>Automated Banner Grabbing:</strong>
            <ul>
              <li>Nmap service detection: <code>nmap -sV target</code></li>
              <li>Nmap script engine: <code>nmap --script banner target</code></li>
              <li>Specialized tools: Amap, Service Probe</li>
              <li>Custom scripts and frameworks</li>
            </ul>
          </li>
        </ul>
        
        <h3>Operating System Fingerprinting</h3>
        <ul>
          <li><strong>Active OS Fingerprinting:</strong>
            <ul>
              <li>TCP/IP stack behavior analysis</li>
              <li>Response to malformed packets</li>
              <li>Timing and sequence analysis</li>
              <li>Command: <code>nmap -O target</code></li>
            </ul>
          </li>
          <li><strong>Passive OS Fingerprinting:</strong>
            <ul>
              <li>Traffic analysis and monitoring</li>
              <li>TTL and window size analysis</li>
              <li>Protocol implementation quirks</li>
              <li>Tools: p0f, Xprobe2, SinFP</li>
            </ul>
          </li>
          <li><strong>Fingerprinting Indicators:</strong>
            <ul>
              <li>Initial TTL values</li>
              <li>TCP window sizes</li>
              <li>TCP option ordering</li>
              <li>ICMP response characteristics</li>
              <li>Fragment handling behavior</li>
            </ul>
          </li>
        </ul>
        
        <h3>Service-Specific Enumeration</h3>
        <ul>
          <li><strong>Web Services (HTTP/HTTPS):</strong>
            <ul>
              <li>HTTP methods enumeration</li>
              <li>Server and application fingerprinting</li>
              <li>Directory and file discovery</li>
              <li>Virtual host enumeration</li>
              <li>Tools: Nikto, Dirb, Gobuster, Wfuzz</li>
            </ul>
          </li>
          <li><strong>DNS Services:</strong>
            <ul>
              <li>Zone transfer attempts</li>
              <li>DNS record enumeration</li>
              <li>Subdomain brute forcing</li>
              <li>DNS cache snooping</li>
              <li>Tools: DNSEnum, Fierce, DNSRecon</li>
            </ul>
          </li>
          <li><strong>SMB/NetBIOS Services:</strong>
            <ul>
              <li>Share enumeration</li>
              <li>User and group enumeration</li>
              <li>Policy information gathering</li>
              <li>Session establishment</li>
              <li>Tools: enum4linux, smbclient, NBTScan</li>
            </ul>
          </li>
          <li><strong>SNMP Services:</strong>
            <ul>
              <li>Community string brute forcing</li>
              <li>MIB tree walking</li>
              <li>System information extraction</li>
              <li>Network topology discovery</li>
              <li>Tools: SNMPWalk, SNMP-Check, OneSixtyOne</li>
            </ul>
          </li>
          <li><strong>Database Services:</strong>
            <ul>
              <li>Database type identification</li>
              <li>Version and configuration enumeration</li>
              <li>Default account testing</li>
              <li>Schema and table discovery</li>
              <li>Tools: SQLMap, DBPwAudit, OraclePasswordCracker</li>
            </ul>
          </li>
        </ul>
        
        <h3>Vulnerability Scanning</h3>
        <ul>
          <li><strong>Automated Vulnerability Scanners:</strong>
            <ul>
              <li>Nessus - Comprehensive vulnerability assessment</li>
              <li>OpenVAS - Open source vulnerability scanner</li>
              <li>Qualys - Cloud-based scanning platform</li>
              <li>Rapid7 Nexpose - Enterprise vulnerability management</li>
            </ul>
          </li>
          <li><strong>Nmap Scripting Engine (NSE):</strong>
            <ul>
              <li>Pre-built vulnerability scripts</li>
              <li>Custom script development</li>
              <li>Service-specific testing</li>
              <li>Examples:
                <ul>
                  <li><code>nmap --script vuln target</code></li>
                  <li><code>nmap --script smb-vuln-* target</code></li>
                  <li><code>nmap --script http-vuln-* target</code></li>
                </ul>
              </li>
            </ul>
          </li>
          <li><strong>Manual Vulnerability Assessment:</strong>
            <ul>
              <li>Service-specific testing</li>
              <li>Configuration analysis</li>
              <li>Patch level verification</li>
              <li>Custom exploit development</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced Scanning Tools and Techniques",
      content: `
        <h2>Advanced Scanning Tools and Techniques</h2>
        <p>Professional penetration testing requires mastery of advanced scanning tools and methodologies for comprehensive network assessment and vulnerability identification.</p>
        
        <h3>Nmap Advanced Features</h3>
        <ul>
          <li><strong>Target Specification:</strong>
            <ul>
              <li>CIDR notation: <code>***********/24</code></li>
              <li>Range specification: <code>***********-254</code></li>
              <li>Input from file: <code>-iL targets.txt</code></li>
              <li>Random targets: <code>-iR 1000</code></li>
              <li>Exclude hosts: <code>--exclude ***********</code></li>
            </ul>
          </li>
          <li><strong>Advanced Scan Types:</strong>
            <ul>
              <li>Window scan: <code>nmap -sW target</code></li>
              <li>Maimon scan: <code>nmap -sM target</code></li>
              <li>ACK scan: <code>nmap -sA target</code></li>
              <li>Protocol scan: <code>nmap -sO target</code></li>
            </ul>
          </li>
          <li><strong>Output Formats:</strong>
            <ul>
              <li>Normal format: <code>-oN output.txt</code></li>
              <li>XML format: <code>-oX output.xml</code></li>
              <li>Grepable format: <code>-oG output.gnmap</code></li>
              <li>All formats: <code>-oA basename</code></li>
            </ul>
          </li>
        </ul>
        
        <h3>Masscan and ZMap</h3>
        <ul>
          <li><strong>Masscan Features:</strong>
            <ul>
              <li>High-speed scanning capability</li>
              <li>Asynchronous transmission</li>
              <li>Custom packet rate control</li>
              <li>Example: <code>masscan -p1-65535 10.0.0.0/8 --rate=1000</code></li>
            </ul>
          </li>
          <li><strong>ZMap Capabilities:</strong>
            <ul>
              <li>Internet-wide scanning</li>
              <li>Stateless scanning approach</li>
              <li>Research-focused design</li>
              <li>Example: <code>zmap -p 80 -o results.csv 0.0.0.0/0</code></li>
            </ul>
          </li>
        </ul>
        
        <h3>Specialized Scanning Tools</h3>
        <ul>
          <li><strong>Network Discovery:</strong>
            <ul>
              <li>Angry IP Scanner - GUI-based network scanner</li>
              <li>Advanced IP Scanner - Windows network discovery</li>
              <li>NetCrunch Tools - Comprehensive network utilities</li>
              <li>Lansweeper - Asset discovery and inventory</li>
            </ul>
          </li>
          <li><strong>Port Scanning Alternatives:</strong>
            <ul>
              <li>RustScan - Modern port scanner</li>
              <li>UnicornScan - Asynchronous network stimulus delivery</li>
              <li>Hping3 - Custom packet crafting</li>
              <li>Scapy - Packet manipulation library</li>
            </ul>
          </li>
          <li><strong>Service Enumeration:</strong>
            <ul>
              <li>Enum4linux - SMB enumeration tool</li>
              <li>SNMPWalk - SNMP enumeration</li>
              <li>DNSEnum - DNS enumeration tool</li>
              <li>SMBMap - SMB share enumeration</li>
            </ul>
          </li>
        </ul>
        
        <h3>Automated Scanning Frameworks</h3>
        <ul>
          <li><strong>AutoRecon:</strong>
            <ul>
              <li>Automated reconnaissance tool</li>
              <li>Multi-stage enumeration</li>
              <li>Service-specific scanning</li>
              <li>Organized output structure</li>
            </ul>
          </li>
          <li><strong>Sparta:</strong>
            <ul>
              <li>GUI-based scanning framework</li>
              <li>Automated tool integration</li>
              <li>Result visualization</li>
              <li>Note-taking capabilities</li>
            </ul>
          </li>
          <li><strong>Reconng:</strong>
            <ul>
              <li>Modular reconnaissance framework</li>
              <li>Database-driven results</li>
              <li>Plugin architecture</li>
              <li>API integration support</li>
            </ul>
          </li>
        </ul>
        
        <h3>Scan Result Analysis</h3>
        <ul>
          <li><strong>Data Correlation:</strong>
            <ul>
              <li>Cross-reference multiple scan results</li>
              <li>Identify patterns and relationships</li>
              <li>Validate findings across tools</li>
              <li>Eliminate false positives</li>
            </ul>
          </li>
          <li><strong>Risk Prioritization:</strong>
            <ul>
              <li>Criticality assessment</li>
              <li>Exploitability analysis</li>
              <li>Business impact evaluation</li>
              <li>Attack path identification</li>
            </ul>
          </li>
          <li><strong>Reporting and Documentation:</strong>
            <ul>
              <li>Executive summary creation</li>
              <li>Technical finding details</li>
              <li>Remediation recommendations</li>
              <li>Evidence preservation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Comprehensive Network Scanning Laboratory",
    description: "Hands-on scanning and enumeration exercises using multiple tools and techniques against simulated target environments.",
    tasks: [
      {
        category: "Network Discovery",
        commands: [
          {
            command: "nmap -sn ***********/24",
            description: "Perform ping sweep to discover live hosts",
            hint: "This discovers active hosts without port scanning",
            expectedOutput: "List of responsive IP addresses with MAC addresses"
          },
          {
            command: "arp-scan -l",
            description: "Discover hosts on local network using ARP",
            hint: "More reliable than ping for local network discovery",
            expectedOutput: "Complete list of devices with MAC addresses and vendors"
          },
          {
            command: "fping -g ***********/24",
            description: "Fast ping sweep using fping",
            hint: "Faster than nmap for simple host discovery",
            expectedOutput: "Quick enumeration of alive hosts"
          }
        ]
      },
      {
        category: "Port Scanning",
        commands: [
          {
            command: "nmap -sS -p 1-1000 ***********00",
            description: "TCP SYN scan of top 1000 ports",
            hint: "Stealth scan that doesn't complete connections",
            expectedOutput: "List of open ports and their states"
          },
          {
            command: "nmap -sU -p 53,67,68,69,123,161,162 ***********00",
            description: "UDP scan of common UDP ports",
            hint: "UDP scanning is slower but reveals different services",
            expectedOutput: "Open UDP ports and potential services"
          },
          {
            command: "nmap -sS -p- --min-rate 1000 ***********00",
            description: "Full port scan with rate limiting",
            hint: "Scans all 65535 ports with speed optimization",
            expectedOutput: "Complete port enumeration results"
          },
          {
            command: "masscan -p1-65535 ***********/24 --rate=1000",
            description: "High-speed port scanning with masscan",
            hint: "Much faster than nmap for large port ranges",
            expectedOutput: "Rapid identification of open ports across network"
          }
        ]
      },
      {
        category: "Service Enumeration",
        commands: [
          {
            command: "nmap -sV -p 22,80,443,3389 ***********00",
            description: "Version detection on specific ports",
            hint: "Identifies service versions and additional details",
            expectedOutput: "Detailed service information including versions"
          },
          {
            command: "nmap -sC -p 80,443 ***********00",
            description: "Default script scan on web ports",
            hint: "Runs common scripts against discovered services",
            expectedOutput: "Additional service information and potential vulnerabilities"
          },
          {
            command: "nc -nv ***********00 80",
            description: "Manual banner grabbing with netcat",
            hint: "Connect and send HTTP request to grab banner",
            expectedOutput: "Raw service banner and response headers"
          },
          {
            command: "curl -I http://***********00",
            description: "HTTP header enumeration",
            hint: "Reveals web server information and technologies",
            expectedOutput: "HTTP response headers with server details"
          }
        ]
      },
      {
        category: "OS Fingerprinting",
        commands: [
          {
            command: "nmap -O ***********00",
            description: "Operating system detection",
            hint: "Analyzes TCP/IP responses to determine OS",
            expectedOutput: "Operating system identification with confidence percentage"
          },
          {
            command: "p0f -i eth0",
            description: "Passive OS fingerprinting",
            hint: "Monitor traffic to identify operating systems passively",
            expectedOutput: "OS identification from network traffic analysis"
          },
          {
            command: "xprobe2 ***********00",
            description: "Advanced OS fingerprinting",
            hint: "Uses multiple techniques for OS identification",
            expectedOutput: "Detailed OS analysis with multiple detection methods"
          }
        ]
      },
      {
        category: "Vulnerability Scanning",
        commands: [
          {
            command: "nmap --script vuln ***********00",
            description: "Vulnerability detection using NSE scripts",
            hint: "Runs all vulnerability detection scripts",
            expectedOutput: "List of potential vulnerabilities with CVE numbers"
          },
          {
            command: "nmap --script smb-vuln-* ***********00",
            description: "SMB-specific vulnerability scanning",
            hint: "Checks for SMB-related vulnerabilities like EternalBlue",
            expectedOutput: "SMB vulnerability assessment results"
          },
          {
            command: "nikto -h http://***********00",
            description: "Web vulnerability scanning with Nikto",
            hint: "Comprehensive web server vulnerability assessment",
            expectedOutput: "Web application security issues and misconfigurations"
          },
          {
            command: "openvas-cli -T xml -h ***********00",
            description: "Comprehensive vulnerability scan with OpenVAS",
            hint: "Professional-grade vulnerability assessment",
            expectedOutput: "Detailed vulnerability report with risk ratings"
          }
        ]
      },
      {
        category: "Advanced Enumeration",
        commands: [
          {
            command: "enum4linux -a ***********00",
            description: "Comprehensive SMB enumeration",
            hint: "Enumerates users, shares, groups, and policies",
            expectedOutput: "Complete SMB service enumeration"
          },
          {
            command: "dnsenum domain.com",
            description: "DNS enumeration and subdomain discovery",
            hint: "Discovers subdomains and DNS information",
            expectedOutput: "DNS records and subdomain enumeration"
          },
          {
            command: "snmpwalk -c public -v1 ***********00",
            description: "SNMP enumeration",
            hint: "Walks SNMP tree to gather system information",
            expectedOutput: "System information via SNMP protocol"
          },
          {
            command: "showmount -e ***********00",
            description: "NFS share enumeration",
            hint: "Lists available NFS exports",
            expectedOutput: "Available NFS shares and access permissions"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "Which Nmap scan type is considered most stealthy while still being reliable?",
      options: [
        "TCP Connect scan (-sT)",
        "TCP SYN scan (-sS)", 
        "TCP FIN scan (-sF)",
        "UDP scan (-sU)"
      ],
      correct: 1,
      explanation: "TCP SYN scan (-sS) is considered most stealthy while reliable because it doesn't complete the three-way handshake, making it harder to detect while still providing accurate results about port states."
    },
    {
      question: "What is the primary advantage of using masscan over nmap for large-scale scanning?",
      options: [
        "Better service detection capabilities",
        "More comprehensive vulnerability scanning",
        "Significantly faster scanning speed",
        "Better stealth and evasion features"
      ],
      correct: 2,
      explanation: "Masscan's primary advantage is its significantly faster scanning speed, capable of scanning the entire internet's IPv4 space in under 6 minutes, making it ideal for large-scale port discovery."
    },
    {
      question: "Which technique is most effective for bypassing simple stateless firewalls?",
      options: [
        "TCP Connect scans",
        "TCP SYN scans", 
        "TCP FIN/NULL/XMAS scans",
        "UDP scans"
      ],
      correct: 2,
      explanation: "TCP FIN/NULL/XMAS scans are most effective against simple stateless firewalls because they don't match typical firewall rules that look for SYN packets, and RFC-compliant systems will respond with RST to closed ports."
    },
    {
      question: "What information does OS fingerprinting primarily analyze to identify operating systems?",
      options: [
        "Open ports and running services",
        "TCP/IP stack implementation differences",
        "Application banners and headers",
        "Network topology and routing"
      ],
      correct: 1,
      explanation: "OS fingerprinting primarily analyzes TCP/IP stack implementation differences, including how different operating systems handle malformed packets, set TCP options, choose initial sequence numbers, and implement various protocol features."
    },
    {
      question: "Which enumeration technique is most likely to provide user account information on a Windows domain?",
      options: [
        "HTTP banner grabbing",
        "SNMP community string enumeration",
        "SMB NULL session enumeration",
        "DNS zone transfer attempts"
      ],
      correct: 2,
      explanation: "SMB NULL session enumeration is most likely to provide user account information on Windows domains, as it can often access user lists, shares, and group information through anonymous connections to the SMB service."
    }
  ],
  virtualLabs: [
    {
      title: "Interactive Network Scanner",
      description: "Virtual environment for practicing port scanning techniques",
      component: "NetworkScannerSimulation",
      scenarios: [
        "Basic host discovery and port scanning",
        "Stealth scanning and evasion techniques", 
        "Service enumeration and banner grabbing",
        "Vulnerability scanning and assessment"
      ]
    },
    {
      title: "Service Enumeration Lab",
      description: "Hands-on practice with service-specific enumeration",
      component: "ServiceEnumerationLab",
      scenarios: [
        "Web service enumeration and directory discovery",
        "SMB share and user enumeration",
        "DNS enumeration and subdomain discovery",
        "SNMP enumeration and system information gathering"
      ]
    }
  ],
  useCases: [
    {
      id: "uc-scan-001",
      title: "Corporate Network Assessment",
      description: "Comprehensive scanning of enterprise network infrastructure",
      scenario: "Large corporation needs security assessment of 10.0.0.0/16 network",
      steps: [
        "Perform network discovery to identify live hosts",
        "Conduct comprehensive port scanning on discovered hosts",
        "Enumerate services and grab banners for version information",
        "Perform OS fingerprinting on critical systems",
        "Run vulnerability scans against identified services",
        "Document findings and prioritize by risk level"
      ],
      expectedFindings: [
        "Network topology and asset inventory",
        "Open ports and running services",
        "Service versions and potential vulnerabilities",
        "Operating system identification",
        "Risk assessment and remediation priorities"
      ]
    },
    {
      id: "uc-scan-002", 
      title: "Web Application Discovery",
      description: "Focused scanning and enumeration of web applications",
      scenario: "External penetration test against company web presence",
      steps: [
        "Perform subdomain enumeration and DNS analysis",
        "Scan web ports and identify web applications",
        "Enumerate virtual hosts and hidden directories",
        "Perform web vulnerability scanning",
        "Analyze SSL/TLS configurations",
        "Document web application attack surface"
      ],
      expectedFindings: [
        "Complete subdomain inventory",
        "Web application technologies and versions",
        "Hidden directories and files", 
        "SSL/TLS configuration issues",
        "Web application vulnerabilities"
      ]
    }
  ],
  mitreMapping: {
    tactics: ["T1046", "T1040", "T1083"],
    techniques: [
      {
        id: "T1046",
        name: "Network Service Scanning", 
        description: "Adversaries may attempt to get a listing of services running on remote hosts"
      },
      {
        id: "T1040",
        name: "Network Sniffing",
        description: "Adversaries may sniff network traffic to capture information"
      },
      {
        id: "T1083", 
        name: "File and Directory Discovery",
        description: "Adversaries may enumerate files and directories"
      }
    ]
  }
}; 