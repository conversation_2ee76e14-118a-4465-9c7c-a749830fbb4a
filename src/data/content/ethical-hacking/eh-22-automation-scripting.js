/**
 * Ethical Hacking Module: Security Automation and Scripting
 * Module ID: eh-22
 */

export const automationScriptingContent = {
  id: "eh-22",
  title: "Security Automation and Scripting",
  description: "Master security automation techniques including custom tool development, exploit scripting, and automated testing frameworks for efficient penetration testing.",
  difficulty: "Advanced",
  estimatedTime: 100,
  objectives: [
    "Understand security automation principles and benefits",
    "Master Python scripting for security testing",
    "Learn PowerShell automation for Windows environments",
    "Develop custom security tools and exploits",
    "Apply automation in continuous security testing"
  ],
  prerequisites: ["eh-1", "eh-5", "eh-7", "eh-14"],
  sections: [
    {
      title: "Security Automation Fundamentals",
      content: `
        <h2>Automation in Cybersecurity</h2>
        <p>Security automation enables efficient, repeatable, and scalable security testing by reducing manual effort and human error.</p>

        <h3>Benefits of Security Automation</h3>
        <ul>
          <li><strong>Efficiency</strong> - Faster execution of repetitive tasks</li>
          <li><strong>Consistency</strong> - Standardized testing procedures</li>
          <li><strong>Scalability</strong> - Handle large-scale assessments</li>
          <li><strong>Accuracy</strong> - Reduce human error</li>
          <li><strong>Coverage</strong> - Comprehensive testing scope</li>
        </ul>

        <h3>Automation Use Cases</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Category</th>
              <th>Use Cases</th>
              <th>Tools/Languages</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Reconnaissance</td>
              <td>Subdomain enumeration, port scanning</td>
              <td>Python, Bash, Nmap</td>
            </tr>
            <tr>
              <td>Vulnerability Assessment</td>
              <td>Automated scanning, report generation</td>
              <td>Python, Nessus API, OpenVAS</td>
            </tr>
            <tr>
              <td>Exploitation</td>
              <td>Custom exploits, payload generation</td>
              <td>Python, Metasploit, PowerShell</td>
            </tr>
            <tr>
              <td>Post-Exploitation</td>
              <td>Data collection, privilege escalation</td>
              <td>PowerShell, Python, C#</td>
            </tr>
            <tr>
              <td>Reporting</td>
              <td>Automated report generation</td>
              <td>Python, LaTeX, Markdown</td>
            </tr>
          </tbody>
        </table>
      `,
      type: "text"
    },
    {
      title: "Python for Security",
      content: `
        <h2>Python Security Scripting</h2>
        <p>Python is the most popular language for security automation due to its simplicity, extensive libraries, and strong community support.</p>

        <h3>Essential Python Libraries</h3>
        <h4>Network and Web Libraries</h4>
        <pre><code># Requests for HTTP operations
import requests

response = requests.get('https://example.com')
print(response.status_code)
print(response.headers)

# Socket programming
import socket

s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
s.connect(('target.com', 80))
s.send(b'GET / HTTP/1.1\\r\\nHost: target.com\\r\\n\\r\\n')
response = s.recv(4096)
print(response.decode())

# Scapy for packet manipulation
from scapy.all import *

# Create and send packets
packet = IP(dst="***********")/ICMP()
send(packet)

# Packet sniffing
def packet_handler(packet):
    print(packet.summary())

sniff(prn=packet_handler, count=10)</code></pre>

        <h3>Custom Security Tools</h3>
        <h4>Port Scanner</h4>
        <pre><code>#!/usr/bin/env python3
import socket
import threading
from datetime import datetime

class PortScanner:
    def __init__(self, target, threads=100):
        self.target = target
        self.threads = threads
        self.open_ports = []

    def scan_port(self, port):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((self.target, port))
            if result == 0:
                self.open_ports.append(port)
                print(f"Port {port}: Open")
            sock.close()
        except:
            pass

    def scan(self, start_port=1, end_port=1000):
        print(f"Scanning {self.target} from port {start_port} to {end_port}")

        for port in range(start_port, end_port + 1):
            thread = threading.Thread(target=self.scan_port, args=(port,))
            thread.start()

            # Limit concurrent threads
            if threading.active_count() > self.threads:
                thread.join()

# Usage
scanner = PortScanner("***********")
scanner.scan(1, 1000)</code></pre>

        <h4>Web Directory Brute Forcer</h4>
        <pre><code>#!/usr/bin/env python3
import requests
import threading
from queue import Queue

class DirBuster:
    def __init__(self, target, wordlist, threads=10):
        self.target = target.rstrip('/')
        self.wordlist = wordlist
        self.threads = threads
        self.queue = Queue()
        self.found_dirs = []

    def check_directory(self):
        while not self.queue.empty():
            directory = self.queue.get()
            url = f"{self.target}/{directory}"

            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    self.found_dirs.append(url)
                    print(f"[{response.status_code}] {url}")
                elif response.status_code == 403:
                    print(f"[{response.status_code}] {url} (Forbidden)")
            except:
                pass

            self.queue.task_done()

    def run(self):
        # Load wordlist
        with open(self.wordlist, 'r') as f:
            directories = f.read().splitlines()

        # Add directories to queue
        for directory in directories:
            self.queue.put(directory)

        # Start threads
        for _ in range(self.threads):
            thread = threading.Thread(target=self.check_directory)
            thread.daemon = True
            thread.start()

        self.queue.join()

# Usage
buster = DirBuster("https://example.com", "wordlist.txt")
buster.run()</code></pre>
      `,
      type: "text"
    },
    {
      title: "PowerShell Automation",
      content: `
        <h2>PowerShell for Security Testing</h2>
        <p>PowerShell is essential for Windows security testing, offering powerful automation capabilities and deep system integration.</p>

        <h3>PowerShell Security Modules</h3>
        <h4>PowerView for AD Enumeration</h4>
        <pre><code># Import PowerView
Import-Module PowerView.ps1

# Domain enumeration
Get-NetDomain
Get-NetDomainController
Get-NetUser | Select-Object samaccountname,description
Get-NetGroup | Select-Object samaccountname,description
Get-NetComputer | Select-Object name,operatingsystem

# Find interesting users
Get-NetUser -SPN | Select-Object samaccountname,serviceprincipalname
Get-NetUser -AdminCount | Select-Object samaccountname

# Group membership analysis
Get-NetGroupMember "Domain Admins"
Get-NetLocalGroup -ComputerName "SERVER01"</code></pre>

        <h4>Custom PowerShell Tools</h4>
        <pre><code># Network scanner function
function Invoke-PortScan {
    param(
        [string]$ComputerName,
        [int[]]$Ports = @(21,22,23,25,53,80,110,443,993,995)
    )

    foreach ($Port in $Ports) {
        try {
            $Socket = New-Object System.Net.Sockets.TcpClient
            $Connect = $Socket.BeginConnect($ComputerName, $Port, $null, $null)
            $Wait = $Connect.AsyncWaitHandle.WaitOne(1000, $false)

            if ($Wait) {
                $Socket.EndConnect($Connect)
                Write-Output "$ComputerName : $Port - Open"
                $Socket.Close()
            }
        }
        catch {
            # Port closed or filtered
        }
    }
}

# Usage
Invoke-PortScan -ComputerName "***********" -Ports @(80,443,22,21)</code></pre>

        <h3>PowerShell Empire Automation</h3>
        <h4>Empire REST API</h4>
        <pre><code># PowerShell Empire API automation
$EmpireServer = "https://empire-server:1337"
$Token = "your-api-token"

# Create headers
$Headers = @{
    "Authorization" = "Bearer $Token"
    "Content-Type" = "application/json"
}

# List agents
$Agents = Invoke-RestMethod -Uri "$EmpireServer/api/agents" -Headers $Headers -Method GET

# Execute module on agent
$ModuleData = @{
    "Agent" = "agent-name"
    "Module" = "powershell/collection/keylogger"
    "Options" = @{}
} | ConvertTo-Json

Invoke-RestMethod -Uri "$EmpireServer/api/modules/execute" -Headers $Headers -Method POST -Body $ModuleData</code></pre>

        <h3>Automated Privilege Escalation</h3>
        <h4>PowerUp Integration</h4>
        <pre><code># Automated privilege escalation checks
Import-Module PowerUp.ps1

# Run all checks
Invoke-AllChecks | Out-File -FilePath "privesc-results.txt"

# Specific checks
Get-UnquotedService
Get-ModifiableServiceFile
Get-ModifiableService
Get-ServiceUnquoted
Get-ModifiableRegistryAutoRun</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced Automation Techniques",
      content: `
        <h2>Enterprise Security Automation</h2>
        <p>Advanced automation techniques enable large-scale security testing and continuous security monitoring.</p>

        <h3>API Integration</h3>
        <h4>Vulnerability Scanner APIs</h4>
        <pre><code># Nessus API automation
import requests
import json
import time

class NessusAPI:
    def __init__(self, server, username, password):
        self.server = server
        self.token = None
        self.login(username, password)

    def login(self, username, password):
        data = {'username': username, 'password': password}
        response = requests.post(f"{self.server}/session",
                               data=data, verify=False)
        self.token = response.json()['token']

    def create_scan(self, name, targets, template_uuid):
        headers = {'X-Cookie': f'token={self.token}'}
        data = {
            'uuid': template_uuid,
            'settings': {
                'name': name,
                'text_targets': targets
            }
        }
        response = requests.post(f"{self.server}/scans",
                               headers=headers, json=data, verify=False)
        return response.json()['scan']['id']

    def launch_scan(self, scan_id):
        headers = {'X-Cookie': f'token={self.token}'}
        requests.post(f"{self.server}/scans/{scan_id}/launch",
                     headers=headers, verify=False)

    def get_scan_status(self, scan_id):
        headers = {'X-Cookie': f'token={self.token}'}
        response = requests.get(f"{self.server}/scans/{scan_id}",
                              headers=headers, verify=False)
        return response.json()['info']['status']

# Usage
nessus = NessusAPI("https://nessus-server:8834", "admin", "password")
scan_id = nessus.create_scan("Automated Scan", "***********/24", "template-uuid")
nessus.launch_scan(scan_id)</code></pre>

        <h3>Continuous Security Testing</h3>
        <h4>CI/CD Integration</h4>
        <pre><code># Jenkins pipeline for security testing
pipeline {
    agent any

    stages {
        stage('SAST') {
            steps {
                script {
                    sh 'bandit -r . -f json -o bandit-report.json'
                    sh 'safety check --json --output safety-report.json'
                }
            }
        }

        stage('DAST') {
            steps {
                script {
                    sh 'zap-baseline.py -t http://app-url -J zap-report.json'
                }
            }
        }

        stage('Infrastructure Scan') {
            steps {
                script {
                    sh 'nmap -sS -O target-network -oX nmap-results.xml'
                    sh 'python3 parse-nmap.py nmap-results.xml'
                }
            }
        }
    }

    post {
        always {
            archiveArtifacts artifacts: '*.json,*.xml', fingerprint: true
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: true,
                keepAll: true,
                reportDir: 'reports',
                reportFiles: 'security-report.html',
                reportName: 'Security Report'
            ])
        }
    }
}</code></pre>

        <h3>Automated Reporting</h3>
        <h4>Report Generation</h4>
        <pre><code># Automated report generation
import json
from jinja2 import Template
import pdfkit

class SecurityReportGenerator:
    def __init__(self, template_path):
        with open(template_path, 'r') as f:
            self.template = Template(f.read())

    def generate_report(self, scan_results, output_path):
        # Process scan results
        vulnerabilities = self.process_vulnerabilities(scan_results)

        # Generate HTML report
        html_content = self.template.render(
            vulnerabilities=vulnerabilities,
            scan_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            total_vulns=len(vulnerabilities)
        )

        # Convert to PDF
        pdfkit.from_string(html_content, output_path)

    def process_vulnerabilities(self, scan_results):
        vulnerabilities = []
        for result in scan_results:
            if result['severity'] in ['High', 'Critical']:
                vulnerabilities.append({
                    'title': result['name'],
                    'severity': result['severity'],
                    'description': result['description'],
                    'solution': result['solution']
                })
        return vulnerabilities

# Usage
generator = SecurityReportGenerator('report_template.html')
generator.generate_report(scan_results, 'security_report.pdf')</code></pre>

        <h3>Threat Intelligence Automation</h3>
        <h4>IOC Processing</h4>
        <pre><code># Automated IOC processing
import requests
import json

class ThreatIntelProcessor:
    def __init__(self, api_keys):
        self.vt_api_key = api_keys['virustotal']
        self.ot_api_key = api_keys['otx']

    def check_hash_reputation(self, file_hash):
        # VirusTotal lookup
        vt_url = f"https://www.virustotal.com/vtapi/v2/file/report"
        vt_params = {'apikey': self.vt_api_key, 'resource': file_hash}
        vt_response = requests.get(vt_url, params=vt_params)

        # AlienVault OTX lookup
        otx_url = f"https://otx.alienvault.com/api/v1/indicators/file/{file_hash}/general"
        otx_headers = {'X-OTX-API-KEY': self.ot_api_key}
        otx_response = requests.get(otx_url, headers=otx_headers)

        return {
            'virustotal': vt_response.json(),
            'otx': otx_response.json()
        }

    def process_iocs(self, ioc_list):
        results = []
        for ioc in ioc_list:
            if len(ioc) in [32, 40, 64]:  # Hash lengths
                result = self.check_hash_reputation(ioc)
                results.append({'ioc': ioc, 'type': 'hash', 'data': result})
        return results

# Usage
processor = ThreatIntelProcessor({
    'virustotal': 'your-vt-api-key',
    'otx': 'your-otx-api-key'
})
results = processor.process_iocs(['hash1', 'hash2', 'hash3'])</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Automated Security Testing Framework",
    description: "Develop and implement a comprehensive automated security testing framework including reconnaissance, vulnerability assessment, and reporting capabilities.",
    environment: "Development environment with target applications, networks, and CI/CD pipeline integration",
    tasks: [
      {
        category: "Tool Development",
        tasks: [
          {
            task: "Create custom Python reconnaissance tool",
            method: "Develop multi-threaded subdomain enumeration and port scanning tool",
            expectedFindings: "Functional reconnaissance automation tool",
            points: 25
          },
          {
            task: "Build PowerShell post-exploitation automation script",
            method: "Automate privilege escalation checks and data collection",
            expectedFindings: "Comprehensive Windows automation framework",
            points: 20
          }
        ]
      },
      {
        category: "API Integration",
        tasks: [
          {
            task: "Integrate vulnerability scanner APIs for automated testing",
            method: "Develop API wrappers for Nessus/OpenVAS automation",
            expectedFindings: "Automated vulnerability assessment pipeline",
            points: 20
          }
        ]
      },
      {
        category: "CI/CD Integration",
        tasks: [
          {
            task: "Implement security testing in CI/CD pipeline",
            method: "Jenkins/GitLab CI integration with security tools",
            expectedFindings: "Continuous security testing implementation",
            points: 20
          }
        ]
      },
      {
        category: "Reporting Automation",
        tasks: [
          {
            task: "Create automated report generation system",
            method: "Template-based reporting with PDF generation",
            expectedFindings: "Professional automated security reports",
            points: 15
          }
        ]
      }
    ],
    deliverables: [
      "Custom security automation tools with source code",
      "API integration documentation and examples",
      "CI/CD pipeline configuration for security testing",
      "Automated report generation system",
      "Framework documentation and usage guides",
      "Performance metrics and efficiency analysis"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which Python library is most commonly used for HTTP operations in security scripts?",
        options: [
          "urllib",
          "requests",
          "httplib",
          "socket"
        ],
        correct: 1,
        explanation: "The requests library is the most popular and user-friendly Python library for HTTP operations in security scripting."
      },
      {
        question: "What is the primary benefit of integrating security testing into CI/CD pipelines?",
        options: [
          "Faster deployment",
          "Continuous security validation",
          "Reduced costs",
          "Better documentation"
        ],
        correct: 1,
        explanation: "Integrating security testing into CI/CD pipelines provides continuous security validation throughout the development lifecycle."
      },
      {
        question: "Which PowerShell module is commonly used for Active Directory enumeration?",
        options: [
          "PowerUp",
          "PowerView",
          "PowerSploit",
          "Empire"
        ],
        correct: 1,
        explanation: "PowerView is the most commonly used PowerShell module for Active Directory enumeration and reconnaissance."
      }
    ],
    practicalTasks: [
      {
        task: "Develop custom Python security tool with multi-threading and API integration",
        points: 25
      },
      {
        task: "Create PowerShell automation script for Windows post-exploitation",
        points: 25
      },
      {
        task: "Implement automated vulnerability scanning with API integration",
        points: 25
      },
      {
        task: "Build comprehensive automated reporting system with multiple output formats",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "Automate the Boring Stuff with Python",
      url: "https://automatetheboringstuff.com/",
      type: "book"
    },
    {
      title: "PowerShell Empire Documentation",
      url: "https://github.com/EmpireProject/Empire",
      type: "tool"
    },
    {
      title: "OWASP DevSecOps Guideline",
      url: "https://owasp.org/www-project-devsecops-guideline/",
      type: "guide"
    }
  ],
  tags: ["automation", "scripting", "python", "powershell", "ci-cd"],
  lastUpdated: "2024-01-15"
};