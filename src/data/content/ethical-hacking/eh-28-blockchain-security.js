/**
 * Ethical Hacking Module: Blockchain and Cryptocurrency Security
 * Module ID: eh-28
 */

export const blockchainSecurityContent = {
  id: "eh-28",
  title: "Blockchain and Cryptocurrency Security",
  description: "Master blockchain security assessment including smart contract auditing, cryptocurrency analysis, DeFi protocol testing, and blockchain infrastructure security.",
  difficulty: "Expert",
  estimatedTime: 115,
  objectives: [
    "Understand blockchain technology and security fundamentals",
    "Master smart contract security auditing techniques",
    "Learn cryptocurrency and DeFi protocol security testing",
    "Develop skills in blockchain infrastructure assessment",
    "Apply blockchain security testing in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-14", "eh-22", "eh-24"],
  sections: [
    {
      title: "Blockchain Security Fundamentals",
      content: `
        <h2>Blockchain Technology Overview</h2>
        <p>Blockchain technology introduces unique security challenges requiring specialized assessment techniques for distributed systems and cryptographic protocols.</p>
        
        <h3>Blockchain Architecture Components</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Component</th>
              <th>Function</th>
              <th>Security Considerations</th>
              <th>Attack Vectors</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Consensus Mechanism</td>
              <td>Network agreement protocol</td>
              <td>51% attacks, nothing-at-stake</td>
              <td>Mining pool concentration, validator corruption</td>
            </tr>
            <tr>
              <td>Smart Contracts</td>
              <td>Automated execution logic</td>
              <td>Code vulnerabilities, logic flaws</td>
              <td>Reentrancy, overflow, access control</td>
            </tr>
            <tr>
              <td>Wallets</td>
              <td>Key management and transactions</td>
              <td>Private key security</td>
              <td>Key theft, phishing, malware</td>
            </tr>
            <tr>
              <td>Exchanges</td>
              <td>Trading platforms</td>
              <td>Centralized vulnerabilities</td>
              <td>API attacks, insider threats, hacks</td>
            </tr>
            <tr>
              <td>DApps</td>
              <td>Decentralized applications</td>
              <td>Frontend and backend security</td>
              <td>Web vulnerabilities, oracle attacks</td>
            </tr>
          </tbody>
        </table>

        <h3>Common Blockchain Vulnerabilities</h3>
        <ul>
          <li><strong>Smart Contract Bugs</strong> - Logic errors, reentrancy, overflow</li>
          <li><strong>Consensus Attacks</strong> - 51% attacks, selfish mining</li>
          <li><strong>Wallet Vulnerabilities</strong> - Key management, seed phrase exposure</li>
          <li><strong>Exchange Security</strong> - API vulnerabilities, insider threats</li>
          <li><strong>Oracle Manipulation</strong> - External data source attacks</li>
          <li><strong>Front-running</strong> - MEV extraction, transaction ordering</li>
        </ul>

        <h3>Blockchain Security Testing Methodology</h3>
        <ol>
          <li><strong>Architecture Review</strong> - System design and threat modeling</li>
          <li><strong>Smart Contract Audit</strong> - Code review and vulnerability testing</li>
          <li><strong>Infrastructure Assessment</strong> - Node and network security</li>
          <li><strong>Wallet Security</strong> - Key management and storage testing</li>
          <li><strong>DApp Testing</strong> - Frontend and integration security</li>
          <li><strong>Economic Analysis</strong> - Tokenomics and incentive alignment</li>
        </ol>
      `,
      type: "text"
    },
    {
      title: "Smart Contract Security Auditing",
      content: `
        <h2>Smart Contract Vulnerability Assessment</h2>
        <p>Smart contracts are immutable programs that require thorough security auditing to prevent financial losses and system compromises.</p>

        <h3>Common Smart Contract Vulnerabilities</h3>
        <h4>Reentrancy Attacks</h4>
        <pre><code>// Vulnerable contract example (Solidity):
contract VulnerableBank {
    mapping(address => uint) public balances;
    
    function withdraw(uint amount) public {
        require(balances[msg.sender] >= amount);
        
        // Vulnerable: External call before state update
        (bool success, ) = msg.sender.call{value: amount}("");
        require(success);
        
        balances[msg.sender] -= amount; // State update after external call
    }
}

// Attack contract:
contract ReentrancyAttack {
    VulnerableBank public bank;
    
    constructor(address _bank) {
        bank = VulnerableBank(_bank);
    }
    
    function attack() public payable {
        bank.withdraw(1 ether);
    }
    
    receive() external payable {
        if (address(bank).balance >= 1 ether) {
            bank.withdraw(1 ether); // Reentrant call
        }
    }
}

// Secure implementation:
contract SecureBank {
    mapping(address => uint) public balances;
    
    function withdraw(uint amount) public {
        require(balances[msg.sender] >= amount);
        
        // Secure: State update before external call
        balances[msg.sender] -= amount;
        
        (bool success, ) = msg.sender.call{value: amount}("");
        require(success);
    }
}</code></pre>

        <h4>Integer Overflow/Underflow</h4>
        <pre><code>// Vulnerable contract (Solidity < 0.8.0):
contract VulnerableToken {
    mapping(address => uint256) public balances;
    
    function transfer(address to, uint256 amount) public {
        // Vulnerable: No overflow check
        balances[msg.sender] -= amount;
        balances[to] += amount;
    }
}

// Secure implementation:
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

contract SecureToken {
    using SafeMath for uint256;
    mapping(address => uint256) public balances;
    
    function transfer(address to, uint256 amount) public {
        balances[msg.sender] = balances[msg.sender].sub(amount);
        balances[to] = balances[to].add(amount);
    }
}

// Solidity 0.8.0+ has built-in overflow protection</code></pre>

        <h3>Smart Contract Analysis Tools</h3>
        <h4>Static Analysis Tools</h4>
        <pre><code># Slither - Static analysis framework
slither contract.sol
slither contract.sol --print human-summary
slither contract.sol --detect reentrancy-eth

# Mythril - Security analysis tool
myth analyze contract.sol
myth analyze contract.sol --execution-timeout 300

# Securify - Automated security scanner
securify contract.sol

# Manticore - Symbolic execution
manticore contract.sol --contract ContractName

# Custom analysis script:
from slither import Slither

def analyze_contract(contract_path):
    slither = Slither(contract_path)
    
    vulnerabilities = []
    
    for contract in slither.contracts:
        for function in contract.functions:
            # Check for reentrancy
            if function.can_reenter():
                vulnerabilities.append(f"Reentrancy in {function.name}")
            
            # Check for unchecked external calls
            for call in function.external_calls:
                if not call.is_checked:
                    vulnerabilities.append(f"Unchecked call in {function.name}")
    
    return vulnerabilities</code></pre>

        <h4>Dynamic Analysis and Fuzzing</h4>
        <pre><code># Echidna - Property-based fuzzing
echidna-test contract.sol --contract ContractName

# Harvey - Greybox fuzzing
harvey --contract contract.sol

# Custom fuzzing with Web3.py:
from web3 import Web3
import random

class SmartContractFuzzer:
    def __init__(self, contract_address, abi, web3_provider):
        self.w3 = Web3(Web3.HTTPProvider(web3_provider))
        self.contract = self.w3.eth.contract(address=contract_address, abi=abi)
    
    def fuzz_function(self, function_name, iterations=1000):
        function = getattr(self.contract.functions, function_name)
        
        for i in range(iterations):
            # Generate random inputs
            random_inputs = self.generate_random_inputs(function)
            
            try:
                # Call function with random inputs
                tx_hash = function(*random_inputs).transact()
                receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash)
                
                # Analyze results
                self.analyze_transaction(receipt)
                
            except Exception as e:
                print(f"Exception in iteration {i}: {e}")
    
    def generate_random_inputs(self, function):
        # Generate random inputs based on function signature
        inputs = []
        for input_type in function.abi['inputs']:
            if input_type['type'] == 'uint256':
                inputs.append(random.randint(0, 2**256 - 1))
            elif input_type['type'] == 'address':
                inputs.append(self.w3.eth.accounts[random.randint(0, len(self.w3.eth.accounts)-1)])
        return inputs</code></pre>

        <h3>DeFi Protocol Security</h3>
        <h4>Flash Loan Attacks</h4>
        <pre><code>// Flash loan attack example:
contract FlashLoanAttack {
    function executeFlashLoan(uint256 amount) external {
        // 1. Borrow large amount via flash loan
        IFlashLoanProvider(flashLoanProvider).flashLoan(amount);
    }
    
    function onFlashLoan(uint256 amount) external {
        // 2. Manipulate price oracle
        manipulatePriceOracle();
        
        // 3. Execute profitable trade
        executeTrade();
        
        // 4. Restore oracle price
        restorePrice();
        
        // 5. Repay flash loan + fee
        repayFlashLoan(amount);
    }
}

// Oracle manipulation detection:
contract OracleSecurityCheck {
    uint256 constant MAX_PRICE_DEVIATION = 10; // 10%
    uint256 lastPrice;
    uint256 lastUpdateTime;
    
    function updatePrice(uint256 newPrice) external {
        require(block.timestamp > lastUpdateTime + 300, "Too frequent updates");
        
        if (lastPrice > 0) {
            uint256 deviation = abs(newPrice - lastPrice) * 100 / lastPrice;
            require(deviation <= MAX_PRICE_DEVIATION, "Price deviation too high");
        }
        
        lastPrice = newPrice;
        lastUpdateTime = block.timestamp;
    }
}</code></pre>

        <h4>Governance Attacks</h4>
        <pre><code>// Governance vulnerability example:
contract VulnerableDAO {
    mapping(address => uint256) public votes;
    uint256 public totalVotes;
    
    function vote(uint256 amount) external {
        // Vulnerable: No snapshot mechanism
        votes[msg.sender] += amount;
        totalVotes += amount;
    }
    
    function executeProposal() external {
        require(votes[msg.sender] > totalVotes / 2, "Insufficient votes");
        // Execute proposal
    }
}

// Secure governance implementation:
contract SecureDAO {
    struct Proposal {
        uint256 id;
        uint256 snapshotBlock;
        mapping(address => bool) hasVoted;
        uint256 forVotes;
        uint256 againstVotes;
    }
    
    mapping(uint256 => Proposal) public proposals;
    
    function vote(uint256 proposalId, bool support) external {
        Proposal storage proposal = proposals[proposalId];
        require(!proposal.hasVoted[msg.sender], "Already voted");
        
        uint256 votingPower = getVotingPowerAt(msg.sender, proposal.snapshotBlock);
        
        if (support) {
            proposal.forVotes += votingPower;
        } else {
            proposal.againstVotes += votingPower;
        }
        
        proposal.hasVoted[msg.sender] = true;
    }
}</code></pre>
      `,
      type: "text"
    },
    {
      title: "Cryptocurrency Security Testing",
      content: `
        <h2>Cryptocurrency Infrastructure Security</h2>
        <p>Cryptocurrency systems require comprehensive security testing of wallets, exchanges, and supporting infrastructure.</p>

        <h3>Wallet Security Assessment</h3>
        <h4>Private Key Management</h4>
        <pre><code># Wallet security testing:

# Seed phrase analysis
import mnemonic
from bip32 import BIP32

def analyze_seed_security(seed_phrase):
    # Check entropy
    entropy = mnemonic.Mnemonic("english").to_entropy(seed_phrase)
    entropy_bits = len(entropy) * 8
    
    # Validate checksum
    is_valid = mnemonic.Mnemonic("english").check(seed_phrase)
    
    # Generate keys
    bip32 = BIP32.from_seed(entropy)
    master_key = bip32.get_master_key()
    
    return {
        'entropy_bits': entropy_bits,
        'is_valid': is_valid,
        'master_key': master_key.hex()
    }

# Hardware wallet testing
def test_hardware_wallet_security():
    # Physical security tests
    # Side-channel analysis
    # Firmware security
    # PIN/passphrase protection
    pass

# Hot wallet vulnerabilities
def analyze_hot_wallet(wallet_file):
    # Check encryption
    # Analyze key storage
    # Test backup mechanisms
    # Verify access controls
    pass</code></pre>

        <h3>Exchange Security Testing</h3>
        <h4>API Security Assessment</h4>
        <pre><code># Exchange API security testing:

import requests
import hmac
import hashlib
import time

class ExchangeAPITester:
    def __init__(self, api_key, secret_key, base_url):
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = base_url
    
    def test_authentication(self):
        # Test API key validation
        # Test signature verification
        # Test timestamp validation
        # Test nonce handling
        pass
    
    def test_rate_limiting(self):
        # Test rate limit enforcement
        # Test burst handling
        # Test IP-based limits
        pass
    
    def test_input_validation(self):
        # Test parameter validation
        # Test SQL injection
        # Test XSS in responses
        # Test command injection
        pass
    
    def test_authorization(self):
        # Test privilege escalation
        # Test account isolation
        # Test order manipulation
        pass

# Cryptocurrency transaction analysis:
def analyze_transaction_security(tx_hash, blockchain):
    # Analyze transaction structure
    # Check for double spending
    # Verify signatures
    # Analyze fee calculation
    # Check for replay attacks
    pass</code></pre>

        <h3>Blockchain Network Security</h3>
        <h4>Node Security Assessment</h4>
        <pre><code># Blockchain node security testing:

# Bitcoin node testing
def test_bitcoin_node(node_ip, port=8333):
    # Test RPC interface
    # Check for information disclosure
    # Test DoS resistance
    # Analyze peer connections
    pass

# Ethereum node testing
def test_ethereum_node(node_url):
    from web3 import Web3
    
    w3 = Web3(Web3.HTTPProvider(node_url))
    
    # Test JSON-RPC endpoints
    # Check for unauthorized access
    # Test gas limit manipulation
    # Analyze transaction pool
    
    # Example: Test for information disclosure
    try:
        accounts = w3.eth.accounts
        if accounts:
            print(f"Warning: Node exposes {len(accounts)} accounts")
    except:
        print("Accounts endpoint properly secured")

# Consensus mechanism testing
def test_consensus_security():
    # Test for 51% attack vectors
    # Analyze validator behavior
    # Test slashing conditions
    # Check for nothing-at-stake problems
    pass

# P2P network analysis
def analyze_p2p_network():
    # Test peer discovery
    # Analyze message propagation
    # Test for eclipse attacks
    # Check for Sybil resistance
    pass</code></pre>

        <h3>DeFi Protocol Security</h3>
        <h4>Automated Market Maker (AMM) Testing</h4>
        <pre><code># AMM security testing:

def test_amm_security(pool_address):
    # Test for impermanent loss
    # Analyze slippage calculation
    # Test for sandwich attacks
    # Check for MEV extraction
    
    # Example: Slippage analysis
    def calculate_slippage(amount_in, reserve_in, reserve_out):
        # Constant product formula: x * y = k
        amount_out = (amount_in * reserve_out) / (reserve_in + amount_in)
        
        # Calculate price impact
        price_before = reserve_out / reserve_in
        price_after = (reserve_out - amount_out) / (reserve_in + amount_in)
        slippage = abs(price_after - price_before) / price_before
        
        return slippage

# Yield farming security
def test_yield_farming_protocol():
    # Test for rug pull risks
    # Analyze tokenomics
    # Check for infinite mint bugs
    # Test reward calculation
    pass

# Lending protocol testing
def test_lending_protocol():
    # Test liquidation mechanisms
    # Analyze collateral ratios
    # Check for oracle manipulation
    # Test interest rate models
    pass</code></pre>

        <h3>NFT and Token Security</h3>
        <h4>ERC Standards Security</h4>
        <pre><code>// ERC-721 (NFT) security testing:
contract SecureNFT is ERC721 {
    using Counters for Counters.Counter;
    Counters.Counter private _tokenIds;
    
    mapping(uint256 => string) private _tokenURIs;
    
    function mint(address to, string memory tokenURI) public onlyOwner {
        _tokenIds.increment();
        uint256 newTokenId = _tokenIds.current();
        
        _mint(to, newTokenId);
        _setTokenURI(newTokenId, tokenURI);
    }
    
    // Security check: Prevent metadata manipulation
    function _setTokenURI(uint256 tokenId, string memory tokenURI) internal {
        require(_exists(tokenId), "Token does not exist");
        require(bytes(tokenURI).length > 0, "Empty URI not allowed");
        _tokenURIs[tokenId] = tokenURI;
    }
}

// ERC-20 token security testing:
contract SecureToken is ERC20 {
    uint256 private _maxSupply;
    
    constructor(uint256 maxSupply) ERC20("SecureToken", "STK") {
        _maxSupply = maxSupply;
    }
    
    function mint(address to, uint256 amount) public onlyOwner {
        require(totalSupply() + amount <= _maxSupply, "Exceeds max supply");
        _mint(to, amount);
    }
    
    // Security: Prevent flash loan attacks
    mapping(address => uint256) private _lastTransferBlock;
    
    function _beforeTokenTransfer(address from, address to, uint256 amount) internal override {
        if (from != address(0)) {
            require(block.number > _lastTransferBlock[from], "Same block transfer");
        }
        _lastTransferBlock[to] = block.number;
    }
}</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "DeFi Protocol Security Audit",
    description: "Conduct comprehensive security audit of a DeFi protocol including smart contract analysis, flash loan attack testing, and governance security assessment.",
    environment: "Ethereum testnet with deployed DeFi protocol, smart contracts, and testing tools",
    tasks: [
      {
        category: "Smart Contract Audit",
        tasks: [
          {
            task: "Perform comprehensive smart contract security audit",
            method: "Static analysis, dynamic testing, and manual code review",
            expectedFindings: "Smart contract vulnerabilities and security recommendations",
            points: 30
          }
        ]
      },
      {
        category: "DeFi Attack Simulation",
        tasks: [
          {
            task: "Execute flash loan and oracle manipulation attacks",
            method: "Attack contract development and exploitation testing",
            expectedFindings: "Successful DeFi attack demonstrations",
            points: 25
          }
        ]
      },
      {
        category: "Governance Security",
        tasks: [
          {
            task: "Assess governance mechanism security",
            method: "Voting system analysis and attack vector identification",
            expectedFindings: "Governance vulnerabilities and manipulation risks",
            points: 20
          }
        ]
      },
      {
        category: "Infrastructure Security",
        tasks: [
          {
            task: "Test blockchain infrastructure and wallet security",
            method: "Node security assessment and wallet vulnerability testing",
            expectedFindings: "Infrastructure security weaknesses and recommendations",
            points: 25
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive blockchain security audit report",
      "Smart contract vulnerability analysis with proof-of-concepts",
      "DeFi attack simulation results and impact assessment",
      "Governance security evaluation and recommendations",
      "Infrastructure security assessment and hardening guide",
      "Automated security testing tools and scripts"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which vulnerability allows an attacker to repeatedly call a function before the state is updated?",
        options: [
          "Integer overflow",
          "Reentrancy",
          "Access control",
          "Oracle manipulation"
        ],
        correct: 1,
        explanation: "Reentrancy attacks exploit the ability to repeatedly call a function before the contract's state is properly updated, allowing attackers to drain funds."
      },
      {
        question: "What is the primary risk of flash loan attacks in DeFi protocols?",
        options: [
          "High transaction fees",
          "Oracle price manipulation",
          "Network congestion",
          "Smart contract bugs"
        ],
        correct: 1,
        explanation: "Flash loan attacks primarily exploit oracle price manipulation by borrowing large amounts to manipulate market prices and profit from arbitrage."
      },
      {
        question: "Which tool is most commonly used for static analysis of Solidity smart contracts?",
        options: [
          "Truffle",
          "Slither",
          "Ganache",
          "Remix"
        ],
        correct: 1,
        explanation: "Slither is the most widely used static analysis framework for detecting vulnerabilities in Solidity smart contracts."
      }
    ],
    practicalTasks: [
      {
        task: "Conduct smart contract security audit using static and dynamic analysis tools",
        points: 25
      },
      {
        task: "Demonstrate flash loan attack against DeFi protocol",
        points: 25
      },
      {
        task: "Assess governance mechanism security and voting manipulation",
        points: 25
      },
      {
        task: "Perform comprehensive blockchain infrastructure security testing",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "ConsenSys Smart Contract Security Best Practices",
      url: "https://consensys.github.io/smart-contract-best-practices/",
      type: "guide"
    },
    {
      title: "Slither Static Analysis Tool",
      url: "https://github.com/crytic/slither",
      type: "tool"
    },
    {
      title: "DeFi Security Summit Resources",
      url: "https://defisecuritysummit.org/",
      type: "conference"
    }
  ],
  tags: ["blockchain-security", "smart-contracts", "defi", "cryptocurrency", "ethereum"],
  lastUpdated: "2024-01-15"
};
