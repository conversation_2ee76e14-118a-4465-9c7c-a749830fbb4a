/**
 * Ethical Hacking Module: Secure Coding and Code Review
 * Module ID: eh-24
 */

export const secureCodingContent = {
  id: "eh-24",
  title: "Secure Coding and Code Review",
  description: "Master secure coding practices, code review techniques, and static analysis for identifying and preventing security vulnerabilities in applications.",
  difficulty: "Advanced",
  estimatedTime: 105,
  objectives: [
    "Understand secure coding principles and best practices",
    "Master code review techniques for security assessment",
    "Learn static and dynamic code analysis tools",
    "Develop skills in vulnerability pattern recognition",
    "Apply secure coding in penetration testing and development"
  ],
  prerequisites: ["eh-1", "eh-5", "eh-14", "eh-22"],
  sections: [
    {
      title: "Secure Coding Fundamentals",
      content: `
        <h2>Secure Coding Principles</h2>
        <p>Secure coding involves implementing software in a way that guards against security vulnerabilities and protects against attacks.</p>
        
        <h3>Core Security Principles</h3>
        <ul>
          <li><strong>Defense in Depth</strong> - Multiple layers of security controls</li>
          <li><strong>Principle of Least Privilege</strong> - Minimal necessary access</li>
          <li><strong>Fail Securely</strong> - Secure failure modes</li>
          <li><strong>Input Validation</strong> - Validate all input data</li>
          <li><strong>Output Encoding</strong> - Encode output for context</li>
          <li><strong>Authentication and Session Management</strong> - Secure user handling</li>
        </ul>

        <h3>Common Vulnerability Categories</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Category</th>
              <th>Examples</th>
              <th>Impact</th>
              <th>Prevention</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Injection</td>
              <td>SQL, NoSQL, OS command, LDAP</td>
              <td>Data breach, system compromise</td>
              <td>Parameterized queries, input validation</td>
            </tr>
            <tr>
              <td>Authentication</td>
              <td>Broken auth, session management</td>
              <td>Account takeover</td>
              <td>Strong auth, secure session handling</td>
            </tr>
            <tr>
              <td>Sensitive Data</td>
              <td>Exposure, weak encryption</td>
              <td>Data breach, privacy violation</td>
              <td>Encryption, access controls</td>
            </tr>
            <tr>
              <td>XML External Entities</td>
              <td>XXE attacks</td>
              <td>Data disclosure, SSRF</td>
              <td>Disable external entities</td>
            </tr>
            <tr>
              <td>Access Control</td>
              <td>Broken authorization</td>
              <td>Privilege escalation</td>
              <td>Proper access controls</td>
            </tr>
          </tbody>
        </table>

        <h3>Secure Development Lifecycle</h3>
        <ol>
          <li><strong>Requirements</strong> - Security requirements definition</li>
          <li><strong>Design</strong> - Threat modeling and secure architecture</li>
          <li><strong>Implementation</strong> - Secure coding practices</li>
          <li><strong>Testing</strong> - Security testing and code review</li>
          <li><strong>Deployment</strong> - Secure configuration and monitoring</li>
          <li><strong>Maintenance</strong> - Ongoing security updates</li>
        </ol>
      `,
      type: "text"
    },
    {
      title: "Code Review Techniques",
      content: `
        <h2>Security-Focused Code Review</h2>
        <p>Code review is a systematic examination of source code to identify security vulnerabilities, logic flaws, and adherence to secure coding standards.</p>

        <h3>Manual Code Review Process</h3>
        <h4>Review Methodology</h4>
        <pre><code># Code Review Checklist

Input Validation:
□ All input validated and sanitized
□ Whitelist validation used where possible
□ Length limits enforced
□ Data type validation implemented
□ Encoding validation performed

Authentication & Authorization:
□ Strong authentication mechanisms
□ Proper session management
□ Access controls implemented
□ Privilege escalation prevented
□ Password policies enforced

Data Protection:
□ Sensitive data encrypted
□ Secure communication channels
□ Proper key management
□ Data classification implemented
□ Privacy controls in place

Error Handling:
□ Secure error messages
□ No sensitive information in errors
□ Proper logging implemented
□ Fail-safe mechanisms
□ Exception handling complete

Configuration:
□ Secure default configurations
□ No hardcoded credentials
□ Environment-specific settings
□ Security headers implemented
□ Unnecessary features disabled</code></pre>

        <h3>Language-Specific Vulnerabilities</h3>
        <h4>Java Security Issues</h4>
        <pre><code>// Vulnerable: SQL Injection
String query = "SELECT * FROM users WHERE id = " + userId;
Statement stmt = connection.createStatement();
ResultSet rs = stmt.executeQuery(query);

// Secure: Parameterized Query
String query = "SELECT * FROM users WHERE id = ?";
PreparedStatement pstmt = connection.prepareStatement(query);
pstmt.setInt(1, userId);
ResultSet rs = pstmt.executeQuery();

// Vulnerable: Path Traversal
File file = new File(userInput);
FileInputStream fis = new FileInputStream(file);

// Secure: Path Validation
String basePath = "/safe/directory/";
String normalizedPath = Paths.get(basePath, userInput).normalize().toString();
if (!normalizedPath.startsWith(basePath)) {
    throw new SecurityException("Invalid path");
}
File file = new File(normalizedPath);</code></pre>

        <h4>Python Security Issues</h4>
        <pre><code># Vulnerable: Command Injection
import os
os.system("ping " + user_input)

# Secure: Subprocess with validation
import subprocess
import shlex

def safe_ping(host):
    if not re.match(r'^[a-zA-Z0-9.-]+$', host):
        raise ValueError("Invalid hostname")
    
    cmd = ["ping", "-c", "1", host]
    result = subprocess.run(cmd, capture_output=True, text=True)
    return result.stdout

# Vulnerable: Pickle Deserialization
import pickle
data = pickle.loads(user_data)

# Secure: JSON or safe serialization
import json
try:
    data = json.loads(user_data)
except json.JSONDecodeError:
    raise ValueError("Invalid JSON data")</code></pre>

        <h4>JavaScript/Node.js Security Issues</h4>
        <pre><code>// Vulnerable: eval() usage
var result = eval(userInput);

// Secure: JSON parsing with validation
try {
    var data = JSON.parse(userInput);
    // Validate data structure
    if (typeof data.name !== 'string' || data.name.length > 100) {
        throw new Error('Invalid data');
    }
} catch (e) {
    throw new Error('Invalid input');
}

// Vulnerable: NoSQL Injection
db.users.find({username: req.body.username, password: req.body.password});

// Secure: Input validation and sanitization
const username = validator.escape(req.body.username);
const password = validator.escape(req.body.password);

if (!validator.isAlphanumeric(username) || username.length > 50) {
    return res.status(400).json({error: 'Invalid username'});
}

db.users.find({username: username, password: hashedPassword});</code></pre>

        <h3>Automated Code Review</h3>
        <h4>Static Analysis Tools</h4>
        <pre><code># SonarQube analysis
sonar-scanner \
  -Dsonar.projectKey=myproject \
  -Dsonar.sources=. \
  -Dsonar.host.url=http://localhost:9000 \
  -Dsonar.login=token

# Bandit for Python
bandit -r /path/to/code -f json -o bandit-report.json

# ESLint for JavaScript
eslint --ext .js,.jsx src/ --format json --output-file eslint-report.json

# SpotBugs for Java
spotbugs -textui -output spotbugs-report.xml -xml myapp.jar

# Semgrep multi-language analysis
semgrep --config=auto --json --output=semgrep-report.json src/</code></pre>
      `,
      type: "text"
    },
    {
      title: "Vulnerability Pattern Recognition",
      content: `
        <h2>Common Vulnerability Patterns</h2>
        <p>Understanding common vulnerability patterns helps identify security issues during code review and penetration testing.</p>

        <h3>Injection Vulnerabilities</h3>
        <h4>SQL Injection Patterns</h4>
        <pre><code>// Vulnerable patterns to look for:

// String concatenation
"SELECT * FROM users WHERE id = " + userInput

// Format strings
String.format("SELECT * FROM users WHERE name = '%s'", userName)

// Dynamic query building
query += " AND status = '" + status + "'"

// Stored procedure calls
"EXEC GetUser '" + username + "'"

// Order by injection
"ORDER BY " + sortColumn

// LIKE clause injection
"WHERE name LIKE '%" + searchTerm + "%'"

// Secure alternatives:
PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM users WHERE id = ?");
pstmt.setInt(1, userId);</code></pre>

        <h4>Cross-Site Scripting (XSS) Patterns</h4>
        <pre><code>// Vulnerable patterns:

// Direct output without encoding
document.write(userInput);
innerHTML = userInput;
$('#content').html(userInput);

// URL parameter reflection
echo $_GET['message'];
response.write(request.query.data);

// JavaScript string building
var script = "alert('" + userInput + "')";

// Attribute injection
<img src="' + imageUrl + '">

// Secure alternatives:
// HTML encoding
document.createTextNode(userInput);
$('#content').text(userInput);

// Context-specific encoding
htmlEncode(userInput);
jsEncode(userInput);
urlEncode(userInput);</code></pre>

        <h3>Authentication and Session Vulnerabilities</h3>
        <h4>Weak Authentication Patterns</h4>
        <pre><code>// Vulnerable patterns:

// Weak password validation
if (password.length() > 6) { /* allow */ }

// Predictable session IDs
sessionId = username + timestamp;

// Session fixation
session.setId(request.getParameter("sessionId"));

// Insecure password storage
password.equals(storedPassword);

// Missing brute force protection
// No rate limiting or account lockout

// Secure alternatives:
// Strong password policy
Pattern.matches("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{12,}$", password);

// Cryptographically secure session IDs
SecureRandom.getInstanceStrong().nextBytes(sessionId);

// Secure password hashing
BCrypt.hashpw(password, BCrypt.gensalt(12));</code></pre>

        <h3>Access Control Vulnerabilities</h3>
        <h4>Authorization Bypass Patterns</h4>
        <pre><code>// Vulnerable patterns:

// Client-side access control
if (user.role === 'admin') {
    // Show admin functions
}

// Insecure direct object references
/api/users/{userId} // No ownership check

// Missing function-level access control
function deleteUser(userId) {
    // No authorization check
    database.delete(userId);
}

// Role-based access without validation
if (request.headers['X-User-Role'] === 'admin') {
    // Allow admin actions
}

// Secure alternatives:
// Server-side authorization
function deleteUser(userId, currentUser) {
    if (!hasPermission(currentUser, 'DELETE_USER')) {
        throw new UnauthorizedException();
    }
    if (!canAccessUser(currentUser, userId)) {
        throw new ForbiddenException();
    }
    database.delete(userId);
}</code></pre>

        <h3>Cryptographic Vulnerabilities</h3>
        <h4>Weak Cryptography Patterns</h4>
        <pre><code>// Vulnerable patterns:

// Weak algorithms
MD5 hash = MD5.getInstance();
DES cipher = DES.getInstance();

// Hardcoded keys
String key = "mySecretKey123";

// Weak random number generation
Random rand = new Random();
int sessionId = rand.nextInt();

// ECB mode encryption
Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");

// Secure alternatives:
// Strong algorithms
MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
Cipher aes = Cipher.getInstance("AES/GCM/NoPadding");

// Secure key generation
KeyGenerator keyGen = KeyGenerator.getInstance("AES");
keyGen.init(256);
SecretKey key = keyGen.generateKey();

// Cryptographically secure random
SecureRandom secureRand = SecureRandom.getInstanceStrong();</code></pre>
      `,
      type: "text"
    },
    {
      title: "Secure Development Practices",
      content: `
        <h2>Implementing Secure Development</h2>
        <p>Secure development practices integrate security throughout the software development lifecycle to prevent vulnerabilities.</p>

        <h3>Secure Coding Standards</h3>
        <h4>Input Validation Framework</h4>
        <pre><code>// Comprehensive input validation class
public class InputValidator {
    
    public static String validateString(String input, int maxLength, String pattern) {
        if (input == null) {
            throw new ValidationException("Input cannot be null");
        }
        
        if (input.length() > maxLength) {
            throw new ValidationException("Input exceeds maximum length");
        }
        
        if (!input.matches(pattern)) {
            throw new ValidationException("Input format invalid");
        }
        
        return input.trim();
    }
    
    public static int validateInteger(String input, int min, int max) {
        try {
            int value = Integer.parseInt(input);
            if (value < min || value > max) {
                throw new ValidationException("Value out of range");
            }
            return value;
        } catch (NumberFormatException e) {
            throw new ValidationException("Invalid integer format");
        }
    }
    
    public static String validateEmail(String email) {
        String emailPattern = "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$";
        if (!email.matches(emailPattern)) {
            throw new ValidationException("Invalid email format");
        }
        return email.toLowerCase();
    }
}</code></pre>

        <h3>Security Testing Integration</h3>
        <h4>Unit Tests for Security</h4>
        <pre><code>// Security-focused unit tests
@Test
public void testSQLInjectionPrevention() {
    String maliciousInput = "'; DROP TABLE users; --";
    
    assertThrows(ValidationException.class, () -> {
        userService.findByUsername(maliciousInput);
    });
}

@Test
public void testXSSPrevention() {
    String xssPayload = "<script>alert('XSS')</script>";
    String result = htmlEncoder.encode(xssPayload);
    
    assertFalse(result.contains("<script>"));
    assertTrue(result.contains("&lt;script&gt;"));
}

@Test
public void testAuthorizationEnforcement() {
    User regularUser = createRegularUser();
    
    assertThrows(UnauthorizedException.class, () -> {
        adminService.deleteUser(targetUserId, regularUser);
    });
}

@Test
public void testPasswordStrengthValidation() {
    String weakPassword = "123456";
    
    assertFalse(passwordValidator.isValid(weakPassword));
    
    String strongPassword = "MyStr0ng!P@ssw0rd";
    assertTrue(passwordValidator.isValid(strongPassword));
}</code></pre>

        <h3>DevSecOps Integration</h3>
        <h4>Security Pipeline Configuration</h4>
        <pre><code># GitLab CI security pipeline
stages:
  - build
  - test
  - security
  - deploy

sast:
  stage: security
  script:
    - semgrep --config=auto --json --output=sast-report.json src/
  artifacts:
    reports:
      sast: sast-report.json

dependency_scanning:
  stage: security
  script:
    - safety check --json --output=dependency-report.json
  artifacts:
    reports:
      dependency_scanning: dependency-report.json

container_scanning:
  stage: security
  script:
    - docker run --rm -v $(pwd):/app clair-scanner:latest
  artifacts:
    reports:
      container_scanning: container-report.json

dast:
  stage: security
  script:
    - zap-baseline.py -t $APPLICATION_URL -J dast-report.json
  artifacts:
    reports:
      dast: dast-report.json</code></pre>

        <h3>Secure Configuration Management</h3>
        <h4>Environment-Specific Security</h4>
        <pre><code># Secure configuration patterns

# Environment variables for secrets
DATABASE_PASSWORD=\${DB_PASSWORD}
API_KEY=\${API_SECRET_KEY}

# Configuration validation
public class SecurityConfig {
    @Value("\${app.jwt.secret}")
    private String jwtSecret;
    
    @PostConstruct
    public void validateConfig() {
        if (jwtSecret == null || jwtSecret.length() < 32) {
            throw new IllegalStateException("JWT secret must be at least 32 characters");
        }
        
        if (jwtSecret.equals("default") || jwtSecret.equals("changeme")) {
            throw new IllegalStateException("Default JWT secret detected");
        }
    }
}

# Security headers configuration
@Configuration
public class SecurityHeadersConfig {
    
    @Bean
    public FilterRegistrationBean<SecurityHeadersFilter> securityHeadersFilter() {
        FilterRegistrationBean<SecurityHeadersFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new SecurityHeadersFilter());
        registration.addUrlPatterns("/*");
        return registration;
    }
}

public class SecurityHeadersFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        httpResponse.setHeader("X-Content-Type-Options", "nosniff");
        httpResponse.setHeader("X-Frame-Options", "DENY");
        httpResponse.setHeader("X-XSS-Protection", "1; mode=block");
        httpResponse.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        httpResponse.setHeader("Content-Security-Policy", "default-src 'self'");
        
        chain.doFilter(request, response);
    }
}</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Secure Code Review and Remediation",
    description: "Conduct comprehensive security code review of a vulnerable web application and implement secure coding practices to remediate identified vulnerabilities.",
    environment: "Vulnerable web application with multiple security flaws across different languages and frameworks",
    tasks: [
      {
        category: "Manual Code Review",
        tasks: [
          {
            task: "Perform manual security code review",
            method: "Systematic review using security checklists and pattern recognition",
            expectedFindings: "Comprehensive vulnerability inventory with code locations",
            points: 25
          }
        ]
      },
      {
        category: "Automated Analysis",
        tasks: [
          {
            task: "Execute static analysis tools and interpret results",
            method: "Multiple SAST tools with result correlation and validation",
            expectedFindings: "Automated vulnerability detection with false positive analysis",
            points: 20
          }
        ]
      },
      {
        category: "Vulnerability Remediation",
        tasks: [
          {
            task: "Implement secure coding fixes for identified vulnerabilities",
            method: "Code modification following secure coding best practices",
            expectedFindings: "Remediated code with security improvements",
            points: 30
          }
        ]
      },
      {
        category: "Security Testing",
        tasks: [
          {
            task: "Develop security unit tests and validation",
            method: "Test-driven security validation and regression testing",
            expectedFindings: "Comprehensive security test suite",
            points: 25
          }
        ]
      }
    ],
    deliverables: [
      "Security code review report with vulnerability details",
      "Static analysis tool results and interpretation",
      "Remediated source code with security improvements",
      "Security unit test suite and validation framework",
      "Secure coding guidelines and best practices documentation",
      "Before/after security comparison analysis"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which secure coding practice is most effective against SQL injection attacks?",
        options: [
          "Input validation only",
          "Parameterized queries",
          "Output encoding",
          "Error handling"
        ],
        correct: 1,
        explanation: "Parameterized queries (prepared statements) are the most effective defense against SQL injection as they separate code from data."
      },
      {
        question: "What is the primary purpose of static application security testing (SAST)?",
        options: [
          "Runtime vulnerability detection",
          "Source code vulnerability analysis",
          "Network security testing",
          "Performance optimization"
        ],
        correct: 1,
        explanation: "SAST analyzes source code to identify security vulnerabilities without executing the application."
      },
      {
        question: "Which principle requires that systems fail to a secure state when errors occur?",
        options: [
          "Defense in depth",
          "Least privilege",
          "Fail securely",
          "Complete mediation"
        ],
        correct: 2,
        explanation: "The 'fail securely' principle ensures that when systems encounter errors, they default to a secure state rather than an insecure one."
      }
    ],
    practicalTasks: [
      {
        task: "Identify and document security vulnerabilities through manual code review",
        points: 25
      },
      {
        task: "Configure and execute multiple static analysis tools with result interpretation",
        points: 25
      },
      {
        task: "Implement secure coding fixes for identified vulnerabilities",
        points: 25
      },
      {
        task: "Develop comprehensive security unit tests and validation framework",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "OWASP Secure Coding Practices",
      url: "https://owasp.org/www-project-secure-coding-practices-quick-reference-guide/",
      type: "guide"
    },
    {
      title: "SANS Secure Coding",
      url: "https://www.sans.org/white-papers/2172/",
      type: "whitepaper"
    },
    {
      title: "SonarQube Documentation",
      url: "https://docs.sonarqube.org/",
      type: "tool"
    }
  ],
  tags: ["secure-coding", "code-review", "static-analysis", "vulnerability-remediation", "devsecops"],
  lastUpdated: "2024-01-15"
};
