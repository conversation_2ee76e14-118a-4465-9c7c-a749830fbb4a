/**
 * Ethical Hacking Module: Medical Device and Healthcare Security
 * Module ID: eh-38
 */

export const medicalDeviceContent = {
  id: "eh-38",
  title: "Medical Device and Healthcare Security",
  description: "Master medical device security testing including FDA cybersecurity requirements, healthcare network security, and patient safety considerations for connected medical devices.",
  difficulty: "Expert",
  estimatedTime: 100,
  objectives: [
    "Understand medical device security regulations and requirements",
    "Master connected medical device security testing techniques",
    "Learn healthcare network and infrastructure security assessment",
    "Develop skills in patient safety and privacy protection",
    "Apply medical device security testing in healthcare environments"
  ],
  prerequisites: ["eh-1", "eh-17", "eh-33", "eh-37"],
  sections: [
    {
      title: "Medical Device Security Fundamentals",
      content: `
        <h2>Healthcare Cybersecurity Landscape</h2>
        <p>Medical device security requires balancing cybersecurity with patient safety, regulatory compliance, and clinical workflow requirements.</p>
        
        <h3>Medical Device Categories</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Device Type</th>
              <th>Risk Level</th>
              <th>Security Concerns</th>
              <th>Testing Approach</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Implantable Devices</td>
              <td>Critical</td>
              <td>Life-threatening attacks, privacy</td>
              <td>RF analysis, protocol security, safety testing</td>
            </tr>
            <tr>
              <td>Infusion Pumps</td>
              <td>High</td>
              <td>Dosage manipulation, unauthorized access</td>
              <td>Network security, authentication testing</td>
            </tr>
            <tr>
              <td>Patient Monitors</td>
              <td>High</td>
              <td>Data integrity, alarm manipulation</td>
              <td>Data validation, communication security</td>
            </tr>
            <tr>
              <td>Imaging Systems</td>
              <td>Medium</td>
              <td>Data theft, system compromise</td>
              <td>Network assessment, access control testing</td>
            </tr>
            <tr>
              <td>Laboratory Equipment</td>
              <td>Medium</td>
              <td>Result manipulation, data breach</td>
              <td>Interface security, data integrity testing</td>
            </tr>
          </tbody>
        </table>

        <h3>Regulatory Framework</h3>
        <h4>FDA Cybersecurity Requirements</h4>
        <pre><code># Medical device security assessment framework
class MedicalDeviceSecurityTester:
    def __init__(self):
        self.fda_requirements = {
            'premarket': [
                'cybersecurity_risk_analysis',
                'security_controls_design',
                'security_testing_validation',
                'vulnerability_disclosure_process'
            ],
            'postmarket': [
                'vulnerability_monitoring',
                'security_update_process',
                'incident_response_plan',
                'coordinated_disclosure'
            ]
        }
        
        self.safety_classifications = {
            'class_i': 'Low risk - general controls',
            'class_ii': 'Moderate risk - special controls',
            'class_iii': 'High risk - premarket approval'
        }
    
    def assess_fda_compliance(self, device_profile):
        # Assess FDA cybersecurity compliance
        compliance_assessment = {
            'device_classification': self.determine_device_class(device_profile),
            'premarket_requirements': self.check_premarket_compliance(device_profile),
            'postmarket_requirements': self.check_postmarket_compliance(device_profile),
            'cybersecurity_documentation': self.review_cybersecurity_docs(device_profile),
            'risk_management': self.assess_risk_management_process(device_profile)
        }
        
        return compliance_assessment
    
    def comprehensive_device_assessment(self, medical_device):
        # Complete medical device security assessment
        assessment_results = {
            'device_identification': self.identify_device_characteristics(medical_device),
            'attack_surface_analysis': self.analyze_attack_surface(medical_device),
            'communication_security': self.test_communication_protocols(medical_device),
            'authentication_authorization': self.test_access_controls(medical_device),
            'data_protection': self.test_data_security(medical_device),
            'firmware_security': self.analyze_firmware_security(medical_device),
            'physical_security': self.assess_physical_security(medical_device),
            'safety_impact_analysis': self.analyze_safety_impact(medical_device)
        }
        
        return assessment_results
    
    def test_wireless_medical_devices(self, wireless_devices):
        # Test wireless medical device security
        wireless_tests = {}
        
        for device in wireless_devices:
            device_tests = {
                'rf_security': self.test_rf_communication_security(device),
                'bluetooth_security': self.test_bluetooth_implementation(device),
                'wifi_security': self.test_wifi_configuration(device),
                'cellular_security': self.test_cellular_communication(device),
                'jamming_resistance': self.test_jamming_resilience(device),
                'eavesdropping_protection': self.test_communication_encryption(device)
            }
            
            wireless_tests[device['id']] = device_tests
        
        return wireless_tests</code></pre>

        <h3>Patient Safety Considerations</h3>
        <div class="alert alert-danger">
          <strong>Critical Safety Warning:</strong> Medical device testing must prioritize patient safety above all else. Never test on devices connected to patients or in clinical use. Always coordinate with clinical engineering and use isolated test environments.
        </div>
      `,
      type: "text"
    },
    {
      title: "Connected Medical Device Testing",
      content: `
        <h2>IoMT (Internet of Medical Things) Security</h2>
        <p>Connected medical devices present unique security challenges requiring specialized testing approaches that consider clinical workflows and patient safety.</p>

        <h3>Network-Connected Device Assessment</h3>
        <h4>Medical Device Network Security</h4>
        <pre><code># IoMT security testing framework
class IoMTSecurityTester:
    def __init__(self):
        self.medical_protocols = {
            'hl7': {'versions': ['v2', 'v3', 'fhir'], 'ports': [2575, 8080, 443]},
            'dicom': {'port': 104, 'security': 'application_level'},
            'continua': {'protocols': ['bluetooth', 'usb', 'zigbee']},
            'ihe': {'profiles': ['pix', 'pdq', 'xds', 'atna']}
        }
    
    def test_medical_device_network_security(self, device_network):
        # Test medical device network security
        network_tests = {
            'network_segmentation': self.test_medical_network_segmentation(device_network),
            'device_discovery': self.test_device_discovery_security(device_network),
            'protocol_security': self.test_medical_protocol_security(device_network),
            'data_in_transit': self.test_medical_data_encryption(device_network),
            'network_monitoring': self.test_network_monitoring_capabilities(device_network)
        }
        
        return network_tests
    
    def test_hl7_security(self, hl7_endpoints):
        # Test HL7 protocol security
        hl7_tests = {}
        
        for endpoint in hl7_endpoints:
            endpoint_tests = {
                'authentication': self.test_hl7_authentication(endpoint),
                'message_integrity': self.test_hl7_message_integrity(endpoint),
                'data_validation': self.test_hl7_data_validation(endpoint),
                'audit_logging': self.test_hl7_audit_capabilities(endpoint),
                'encryption': self.test_hl7_encryption(endpoint)
            }
            
            hl7_tests[endpoint['name']] = endpoint_tests
        
        return hl7_tests
    
    def test_dicom_security(self, dicom_systems):
        # Test DICOM (Digital Imaging and Communications in Medicine) security
        dicom_tests = {}
        
        for system in dicom_systems:
            system_tests = {
                'association_security': self.test_dicom_association_security(system),
                'image_integrity': self.test_dicom_image_integrity(system),
                'patient_privacy': self.test_dicom_privacy_protection(system),
                'access_controls': self.test_dicom_access_controls(system),
                'audit_trail': self.test_dicom_audit_capabilities(system)
            }
            
            dicom_tests[system['ae_title']] = system_tests
        
        return dicom_tests
    
    def test_medical_device_apis(self, device_apis):
        # Test medical device API security
        api_tests = {}
        
        for api in device_apis:
            api_security_tests = {
                'authentication': self.test_api_authentication(api),
                'authorization': self.test_api_authorization(api),
                'input_validation': self.test_api_input_validation(api),
                'rate_limiting': self.test_api_rate_limiting(api),
                'data_exposure': self.test_api_data_exposure(api),
                'patient_data_protection': self.test_patient_data_apis(api)
            }
            
            api_tests[api['endpoint']] = api_security_tests
        
        return api_tests

# Implantable device security testing
class ImplantableDeviceTester:
    def __init__(self):
        self.implantable_types = [
            'pacemaker', 'icd', 'insulin_pump', 'neurostimulator', 'cochlear_implant'
        ]
    
    def test_implantable_device_security(self, device_type, device_config):
        # Test implantable medical device security
        # Note: This should only be done in controlled lab environments
        
        implant_tests = {
            'rf_communication': self.test_rf_security(device_config),
            'authentication': self.test_device_authentication(device_config),
            'encryption': self.test_communication_encryption(device_config),
            'replay_protection': self.test_replay_attack_protection(device_config),
            'emergency_access': self.test_emergency_access_procedures(device_config),
            'battery_depletion': self.test_battery_depletion_attacks(device_config)
        }
        
        return implant_tests
    
    def test_programmer_security(self, programmer_device):
        # Test medical device programmer security
        programmer_tests = {
            'access_controls': self.test_programmer_access_controls(programmer_device),
            'software_integrity': self.test_programmer_software_integrity(programmer_device),
            'communication_security': self.test_programmer_communication(programmer_device),
            'audit_capabilities': self.test_programmer_audit_logging(programmer_device),
            'update_security': self.test_programmer_update_process(programmer_device)
        }
        
        return programmer_tests</code></pre>
      `,
      type: "text"
    },
    {
      title: "Healthcare Infrastructure Security",
      content: `
        <h2>Healthcare Network and Infrastructure Assessment</h2>
        <p>Healthcare infrastructure security encompasses hospital networks, electronic health records, and supporting systems that enable medical device connectivity.</p>

        <h3>Hospital Network Security</h3>
        <h4>Healthcare Network Assessment</h4>
        <pre><code># Healthcare infrastructure security testing
class HealthcareInfrastructureTester:
    def __init__(self):
        self.healthcare_systems = {
            'ehr': 'Electronic Health Records',
            'his': 'Hospital Information System',
            'ris': 'Radiology Information System',
            'lis': 'Laboratory Information System',
            'pacs': 'Picture Archiving and Communication System',
            'emr': 'Electronic Medical Records'
        }
    
    def assess_healthcare_network_security(self, network_infrastructure):
        # Comprehensive healthcare network assessment
        network_assessment = {
            'network_segmentation': self.test_healthcare_network_segmentation(network_infrastructure),
            'medical_device_isolation': self.test_medical_device_network_isolation(network_infrastructure),
            'guest_network_security': self.test_guest_network_isolation(network_infrastructure),
            'wireless_security': self.test_healthcare_wireless_security(network_infrastructure),
            'remote_access': self.test_healthcare_remote_access(network_infrastructure),
            'network_monitoring': self.assess_network_monitoring_capabilities(network_infrastructure)
        }
        
        return network_assessment
    
    def test_ehr_security(self, ehr_systems):
        # Test Electronic Health Record system security
        ehr_tests = {}
        
        for ehr_system in ehr_systems:
            ehr_security_tests = {
                'access_controls': self.test_ehr_access_controls(ehr_system),
                'audit_logging': self.test_ehr_audit_capabilities(ehr_system),
                'data_encryption': self.test_ehr_data_protection(ehr_system),
                'user_authentication': self.test_ehr_authentication(ehr_system),
                'role_based_access': self.test_ehr_rbac(ehr_system),
                'data_integrity': self.test_ehr_data_integrity(ehr_system),
                'backup_security': self.test_ehr_backup_procedures(ehr_system)
            }
            
            ehr_tests[ehr_system['name']] = ehr_security_tests
        
        return ehr_tests
    
    def test_healthcare_cloud_security(self, cloud_services):
        # Test healthcare cloud service security
        cloud_tests = {}
        
        for service in cloud_services:
            cloud_security_tests = {
                'data_residency': self.test_data_residency_compliance(service),
                'encryption_at_rest': self.test_cloud_data_encryption(service),
                'encryption_in_transit': self.test_cloud_communication_security(service),
                'access_controls': self.test_cloud_access_management(service),
                'compliance': self.test_cloud_compliance_controls(service),
                'incident_response': self.test_cloud_incident_response(service),
                'vendor_security': self.assess_cloud_vendor_security(service)
            }
            
            cloud_tests[service['name']] = cloud_security_tests
        
        return cloud_tests
    
    def test_hipaa_compliance(self, healthcare_systems):
        # Test HIPAA compliance across healthcare systems
        hipaa_compliance = {
            'administrative_safeguards': self.test_administrative_safeguards(healthcare_systems),
            'physical_safeguards': self.test_physical_safeguards(healthcare_systems),
            'technical_safeguards': self.test_technical_safeguards(healthcare_systems),
            'breach_notification': self.test_breach_notification_procedures(healthcare_systems),
            'business_associate': self.test_business_associate_compliance(healthcare_systems)
        }
        
        return hipaa_compliance

# Medical device vulnerability management
class MedicalDeviceVulnerabilityManager:
    def __init__(self):
        self.vulnerability_sources = [
            'ics_cert', 'fda_safety_communications', 'vendor_advisories', 'cve_database'
        ]
    
    def assess_vulnerability_management(self, device_inventory):
        # Assess medical device vulnerability management
        vuln_management = {
            'inventory_accuracy': self.validate_device_inventory(device_inventory),
            'vulnerability_monitoring': self.test_vulnerability_monitoring_process(),
            'patch_management': self.assess_medical_device_patching(),
            'risk_assessment': self.test_vulnerability_risk_assessment(),
            'incident_response': self.test_medical_device_incident_response(),
            'vendor_coordination': self.assess_vendor_communication_process()
        }
        
        return vuln_management
    
    def test_coordinated_disclosure(self, disclosure_process):
        # Test coordinated vulnerability disclosure process
        disclosure_tests = {
            'reporting_process': self.test_vulnerability_reporting_process(disclosure_process),
            'vendor_response': self.test_vendor_response_procedures(disclosure_process),
            'timeline_compliance': self.test_disclosure_timeline_compliance(disclosure_process),
            'patient_safety': self.test_patient_safety_considerations(disclosure_process),
            'public_notification': self.test_public_notification_process(disclosure_process)
        }
        
        return disclosure_tests</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Healthcare Medical Device Security Assessment",
    description: "Conduct comprehensive medical device and healthcare infrastructure security assessment including connected device testing, network security evaluation, and regulatory compliance validation.",
    environment: "Healthcare testbed with medical devices, hospital network infrastructure, and healthcare information systems",
    tasks: [
      {
        category: "Medical Device Security",
        tasks: [
          {
            task: "Assess connected medical device security",
            method: "IoMT device testing, wireless security analysis, and protocol assessment",
            expectedFindings: "Medical device vulnerabilities and communication security gaps",
            points: 30
          }
        ]
      },
      {
        category: "Healthcare Protocol Testing",
        tasks: [
          {
            task: "Test HL7, DICOM, and healthcare protocol security",
            method: "Protocol analysis, message integrity testing, and authentication assessment",
            expectedFindings: "Healthcare protocol vulnerabilities and data protection issues",
            points: 25
          }
        ]
      },
      {
        category: "Healthcare Infrastructure",
        tasks: [
          {
            task: "Evaluate healthcare network and EHR system security",
            method: "Network segmentation testing, access control assessment, and audit analysis",
            expectedFindings: "Infrastructure security gaps and compliance violations",
            points: 25
          }
        ]
      },
      {
        category: "Regulatory Compliance",
        tasks: [
          {
            task: "Assess FDA cybersecurity and HIPAA compliance",
            method: "Regulatory requirement validation and compliance gap analysis",
            expectedFindings: "Compliance deficiencies and regulatory risk assessment",
            points: 20
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive medical device security assessment report",
      "Connected medical device vulnerability analysis",
      "Healthcare protocol security evaluation",
      "Healthcare infrastructure security assessment",
      "FDA cybersecurity and HIPAA compliance analysis",
      "Medical device security remediation roadmap"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which FDA requirement is most critical for premarket medical device cybersecurity?",
        options: [
          "Vulnerability monitoring",
          "Security update process",
          "Cybersecurity risk analysis",
          "Incident response plan"
        ],
        correct: 2,
        explanation: "Cybersecurity risk analysis is a critical premarket requirement that identifies and assesses cybersecurity risks throughout the device lifecycle."
      },
      {
        question: "What is the primary security concern with wireless medical devices?",
        options: [
          "Battery life",
          "Unauthorized access and control",
          "Device compatibility",
          "Network performance"
        ],
        correct: 1,
        explanation: "Unauthorized access and control of wireless medical devices poses the greatest security risk, potentially affecting patient safety and data privacy."
      },
      {
        question: "Which healthcare protocol is primarily used for medical imaging communication?",
        options: [
          "HL7",
          "DICOM",
          "FHIR",
          "IHE"
        ],
        correct: 1,
        explanation: "DICOM (Digital Imaging and Communications in Medicine) is the standard protocol for medical imaging communication and storage."
      }
    ],
    practicalTasks: [
      {
        task: "Conduct connected medical device security assessment and vulnerability analysis",
        points: 25
      },
      {
        task: "Test healthcare protocol security including HL7 and DICOM implementations",
        points: 25
      },
      {
        task: "Evaluate healthcare infrastructure and EHR system security",
        points: 25
      },
      {
        task: "Assess FDA cybersecurity and HIPAA compliance requirements",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "FDA Medical Device Cybersecurity Guidance",
      url: "https://www.fda.gov/medical-devices/digital-health-center-excellence/cybersecurity",
      type: "guidance"
    },
    {
      title: "HIPAA Security Rule",
      url: "https://www.hhs.gov/hipaa/for-professionals/security/",
      type: "regulation"
    },
    {
      title: "Healthcare Cybersecurity Framework",
      url: "https://www.healthit.gov/topic/privacy-security-and-hipaa/cybersecurity",
      type: "framework"
    }
  ],
  tags: ["medical-device-security", "healthcare-cybersecurity", "iomt", "hipaa", "fda-cybersecurity"],
  lastUpdated: "2024-01-15"
};
