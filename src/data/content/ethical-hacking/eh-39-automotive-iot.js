/**
 * Ethical Hacking Module: Automotive and Advanced IoT Security
 * Module ID: eh-39
 */

export const automotiveIoTContent = {
  id: "eh-39",
  title: "Automotive and Advanced IoT Security",
  description: "Master automotive cybersecurity and advanced IoT security testing including CAN bus analysis, V2X communication security, and smart city infrastructure assessment.",
  difficulty: "Expert",
  estimatedTime: 110,
  objectives: [
    "Understand automotive cybersecurity architecture and attack vectors",
    "Master CAN bus and automotive protocol security testing",
    "Learn V2X communication and connected vehicle security",
    "Develop skills in smart city and industrial IoT security",
    "Apply automotive and IoT security testing in real-world scenarios"
  ],
  prerequisites: ["eh-1", "eh-17", "eh-27", "eh-37"],
  sections: [
    {
      title: "Automotive Cybersecurity Fundamentals",
      content: `
        <h2>Connected Vehicle Security Landscape</h2>
        <p>Modern vehicles are complex cyber-physical systems with multiple attack surfaces requiring specialized security testing approaches.</p>
        
        <h3>Automotive Architecture Components</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Component</th>
              <th>Function</th>
              <th>Security Risks</th>
              <th>Testing Approach</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>ECU (Electronic Control Unit)</td>
              <td>Vehicle system control</td>
              <td>Firmware manipulation, unauthorized access</td>
              <td>Firmware analysis, CAN bus testing</td>
            </tr>
            <tr>
              <td>CAN Bus</td>
              <td>Internal vehicle communication</td>
              <td>Message injection, replay attacks</td>
              <td>Protocol analysis, message fuzzing</td>
            </tr>
            <tr>
              <td>Telematics Unit</td>
              <td>External connectivity</td>
              <td>Remote attacks, data theft</td>
              <td>Cellular security, API testing</td>
            </tr>
            <tr>
              <td>Infotainment System</td>
              <td>Entertainment and navigation</td>
              <td>Malware infection, privilege escalation</td>
              <td>Application security, OS hardening</td>
            </tr>
            <tr>
              <td>OBD-II Port</td>
              <td>Diagnostics interface</td>
              <td>Physical access attacks</td>
              <td>Physical security, access control</td>
            </tr>
          </tbody>
        </table>

        <h3>CAN Bus Security Testing</h3>
        <h4>Controller Area Network Analysis</h4>
        <pre><code># Automotive CAN bus security testing framework
import can
import struct
import time
from collections import defaultdict

class CANBusSecurityTester:
    def __init__(self, interface='socketcan', channel='vcan0'):
        self.bus = can.interface.Bus(interface=interface, channel=channel)
        self.message_log = []
        self.ecu_mapping = {}
        self.security_findings = []
    
    def comprehensive_can_assessment(self):
        # Complete CAN bus security assessment
        assessment_results = {
            'network_discovery': self.discover_can_network(),
            'message_analysis': self.analyze_can_messages(),
            'injection_testing': self.test_message_injection(),
            'replay_attacks': self.test_replay_attacks(),
            'fuzzing_results': self.fuzz_can_messages(),
            'authentication_analysis': self.analyze_authentication(),
            'encryption_assessment': self.assess_encryption_usage()
        }
        
        return assessment_results
    
    def discover_can_network(self):
        # Discover active ECUs and message IDs
        discovered_ids = set()
        ecu_activity = defaultdict(int)
        
        # Passive monitoring for network discovery
        start_time = time.time()
        while time.time() - start_time < 60:  # Monitor for 1 minute
            message = self.bus.recv(timeout=1.0)
            if message:
                discovered_ids.add(message.arbitration_id)
                ecu_activity[message.arbitration_id] += 1
                self.message_log.append({
                    'timestamp': message.timestamp,
                    'id': message.arbitration_id,
                    'data': message.data.hex(),
                    'dlc': message.dlc
                })
        
        return {
            'discovered_ids': list(discovered_ids),
            'message_frequency': dict(ecu_activity),
            'total_messages': len(self.message_log)
        }
    
    def test_message_injection(self):
        # Test CAN message injection attacks
        injection_results = []
        
        # Test critical system message injection
        critical_ids = [0x7E0, 0x7E8, 0x7DF]  # Common OBD-II IDs
        
        for msg_id in critical_ids:
            # Test various payloads
            test_payloads = [
                b'\x00\x00\x00\x00\x00\x00\x00\x00',  # All zeros
                b'\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xFF',  # All ones
                b'\x02\x01\x0C\x00\x00\x00\x00\x00',  # OBD-II RPM request
                b'\x30\x00\x00\x00\x00\x00\x00\x00'   # Flow control frame
            ]
            
            for payload in test_payloads:
                try:
                    message = can.Message(
                        arbitration_id=msg_id,
                        data=payload,
                        is_extended_id=False
                    )
                    
                    self.bus.send(message)
                    
                    # Monitor for responses
                    response = self.bus.recv(timeout=1.0)
                    
                    injection_results.append({
                        'injected_id': msg_id,
                        'payload': payload.hex(),
                        'response_received': response is not None,
                        'response_id': response.arbitration_id if response else None,
                        'response_data': response.data.hex() if response else None
                    })
                    
                except Exception as e:
                    injection_results.append({
                        'injected_id': msg_id,
                        'payload': payload.hex(),
                        'error': str(e)
                    })
        
        return injection_results
    
    def fuzz_can_messages(self):
        # Fuzz CAN messages to discover vulnerabilities
        fuzzing_results = []
        
        # Get active message IDs from discovery
        active_ids = list(set(msg['id'] for msg in self.message_log))
        
        for msg_id in active_ids[:10]:  # Limit fuzzing to first 10 IDs
            # Generate fuzzing payloads
            fuzz_payloads = self.generate_fuzz_payloads()
            
            for payload in fuzz_payloads:
                try:
                    fuzz_message = can.Message(
                        arbitration_id=msg_id,
                        data=payload,
                        is_extended_id=False
                    )
                    
                    # Send fuzzed message
                    self.bus.send(fuzz_message)
                    
                    # Monitor for unusual responses or behavior
                    responses = []
                    start_time = time.time()
                    while time.time() - start_time < 2.0:
                        response = self.bus.recv(timeout=0.1)
                        if response:
                            responses.append(response)
                    
                    fuzzing_results.append({
                        'fuzzed_id': msg_id,
                        'payload': payload.hex(),
                        'response_count': len(responses),
                        'unusual_behavior': self.detect_unusual_behavior(responses)
                    })
                    
                except Exception as e:
                    fuzzing_results.append({
                        'fuzzed_id': msg_id,
                        'payload': payload.hex(),
                        'error': str(e)
                    })
        
        return fuzzing_results
    
    def generate_fuzz_payloads(self):
        # Generate various fuzzing payloads
        payloads = []
        
        # Boundary value testing
        payloads.extend([
            b'\x00' * 8,  # All zeros
            b'\xFF' * 8,  # All ones
            b'\x7F' * 8,  # Max signed values
            b'\x80' * 8   # Min signed values
        ])
        
        # Random payloads
        import random
        for _ in range(20):
            payload = bytes([random.randint(0, 255) for _ in range(8)])
            payloads.append(payload)
        
        # Pattern-based payloads
        payloads.extend([
            b'\xAA\x55\xAA\x55\xAA\x55\xAA\x55',  # Alternating pattern
            b'\x01\x02\x04\x08\x10\x20\x40\x80',  # Bit shifting
            b'\xDE\xAD\xBE\xEF\xCA\xFE\xBA\xBE'   # Known patterns
        ])
        
        return payloads</code></pre>

        <h3>Vehicle Safety Considerations</h3>
        <div class="alert alert-danger">
          <strong>Critical Safety Warning:</strong> Automotive security testing must never be performed on vehicles in operation or on public roads. Always use isolated test environments, bench setups, or dedicated test vehicles in controlled environments.
        </div>
      `,
      type: "text"
    },
    {
      title: "V2X Communication Security",
      content: `
        <h2>Vehicle-to-Everything (V2X) Security Testing</h2>
        <p>V2X communications enable vehicles to communicate with infrastructure, other vehicles, and pedestrians, creating new attack surfaces requiring specialized security testing.</p>

        <h3>V2X Protocol Security</h3>
        <h4>DSRC and C-V2X Testing</h4>
        <pre><code># V2X communication security testing
class V2XSecurityTester:
    def __init__(self):
        self.v2x_protocols = {
            'dsrc': {
                'frequency': '5.9 GHz',
                'standard': 'IEEE 802.11p',
                'security': 'IEEE 1609.2'
            },
            'c_v2x': {
                'technology': 'LTE/5G',
                'modes': ['PC5', 'Uu'],
                'security': '3GPP security'
            }
        }
        
        self.v2x_message_types = {
            'bsm': 'Basic Safety Message',
            'spat': 'Signal Phase and Timing',
            'map': 'Map Data',
            'tim': 'Traveler Information Message',
            'rsa': 'Roadside Alert',
            'ssm': 'Signal Status Message'
        }
    
    def test_v2x_security(self, v2x_system):
        # Comprehensive V2X security testing
        v2x_tests = {
            'message_authentication': self.test_v2x_message_authentication(v2x_system),
            'certificate_validation': self.test_v2x_certificate_management(v2x_system),
            'privacy_protection': self.test_v2x_privacy_mechanisms(v2x_system),
            'replay_protection': self.test_v2x_replay_protection(v2x_system),
            'jamming_resistance': self.test_v2x_jamming_resistance(v2x_system),
            'misbehavior_detection': self.test_misbehavior_detection(v2x_system)
        }
        
        return v2x_tests
    
    def test_v2x_message_authentication(self, system):
        # Test V2X message authentication mechanisms
        auth_tests = {
            'digital_signatures': self.test_message_signatures(system),
            'certificate_chains': self.test_certificate_validation(system),
            'timestamp_validation': self.test_timestamp_verification(system),
            'message_integrity': self.test_message_integrity_protection(system)
        }
        
        return auth_tests
    
    def test_v2x_privacy_mechanisms(self, system):
        # Test V2X privacy protection mechanisms
        privacy_tests = {
            'pseudonym_management': self.test_pseudonym_certificates(system),
            'location_privacy': self.test_location_privacy_protection(system),
            'identity_resolution': self.test_identity_resolution_resistance(system),
            'tracking_resistance': self.test_vehicle_tracking_resistance(system)
        }
        
        return privacy_tests
    
    def simulate_v2x_attacks(self, target_system):
        # Simulate various V2X attack scenarios
        attack_simulations = {
            'false_emergency_message': self.simulate_false_emergency_broadcast(target_system),
            'traffic_light_spoofing': self.simulate_spat_message_spoofing(target_system),
            'phantom_vehicle': self.simulate_phantom_vehicle_attack(target_system),
            'replay_attack': self.simulate_message_replay_attack(target_system),
            'sybil_attack': self.simulate_sybil_attack(target_system)
        }
        
        return attack_simulations

# Connected vehicle ecosystem testing
class ConnectedVehicleEcosystemTester:
    def __init__(self):
        self.ecosystem_components = {
            'vehicle': ['telematics', 'infotainment', 'adas', 'gateway'],
            'infrastructure': ['rsu', 'traffic_management', 'cloud_services'],
            'mobile': ['smartphone_apps', 'key_fobs', 'wearables'],
            'backend': ['oem_servers', 'service_providers', 'data_analytics']
        }
    
    def test_ecosystem_security(self, ecosystem_config):
        # Test entire connected vehicle ecosystem
        ecosystem_tests = {
            'vehicle_security': self.test_vehicle_components(ecosystem_config['vehicle']),
            'infrastructure_security': self.test_infrastructure_components(ecosystem_config['infrastructure']),
            'mobile_security': self.test_mobile_components(ecosystem_config['mobile']),
            'backend_security': self.test_backend_components(ecosystem_config['backend']),
            'integration_security': self.test_ecosystem_integration(ecosystem_config)
        }
        
        return ecosystem_tests
    
    def test_over_the_air_updates(self, ota_system):
        # Test OTA update security
        ota_tests = {
            'update_authentication': self.test_update_signature_verification(ota_system),
            'secure_delivery': self.test_update_delivery_security(ota_system),
            'rollback_protection': self.test_update_rollback_mechanisms(ota_system),
            'partial_update_security': self.test_delta_update_security(ota_system),
            'update_verification': self.test_post_update_verification(ota_system)
        }
        
        return ota_tests</code></pre>
      `,
      type: "text"
    },
    {
      title: "Smart City and Industrial IoT",
      content: `
        <h2>Advanced IoT Security Assessment</h2>
        <p>Smart city and industrial IoT deployments create complex interconnected systems requiring comprehensive security testing across multiple domains.</p>

        <h3>Smart City Infrastructure Security</h3>
        <h4>Smart City Component Testing</h4>
        <pre><code># Smart city IoT security testing framework
class SmartCitySecurityTester:
    def __init__(self):
        self.smart_city_domains = {
            'transportation': ['traffic_lights', 'smart_parking', 'public_transit'],
            'utilities': ['smart_grid', 'water_management', 'waste_management'],
            'public_safety': ['surveillance', 'emergency_systems', 'environmental_monitoring'],
            'governance': ['citizen_services', 'data_analytics', 'communication_systems']
        }
    
    def assess_smart_city_security(self, city_infrastructure):
        # Comprehensive smart city security assessment
        city_assessment = {
            'iot_device_security': self.test_iot_device_security(city_infrastructure),
            'network_security': self.test_smart_city_networks(city_infrastructure),
            'data_security': self.test_smart_city_data_protection(city_infrastructure),
            'integration_security': self.test_system_integration_security(city_infrastructure),
            'privacy_protection': self.test_citizen_privacy_protection(city_infrastructure),
            'resilience_testing': self.test_infrastructure_resilience(city_infrastructure)
        }
        
        return city_assessment
    
    def test_smart_traffic_system(self, traffic_system):
        # Test smart traffic management system security
        traffic_tests = {
            'traffic_light_control': self.test_traffic_light_security(traffic_system),
            'sensor_network': self.test_traffic_sensor_security(traffic_system),
            'communication_protocols': self.test_traffic_communication_security(traffic_system),
            'central_management': self.test_traffic_management_center(traffic_system),
            'emergency_override': self.test_emergency_traffic_controls(traffic_system)
        }
        
        return traffic_tests
    
    def test_smart_grid_security(self, smart_grid):
        # Test smart grid IoT security
        grid_tests = {
            'smart_meters': self.test_smart_meter_security(smart_grid),
            'distribution_automation': self.test_grid_automation_security(smart_grid),
            'demand_response': self.test_demand_response_security(smart_grid),
            'grid_communication': self.test_grid_communication_protocols(smart_grid),
            'cybersecurity_framework': self.test_grid_cybersecurity_compliance(smart_grid)
        }
        
        return grid_tests

# Industrial IoT security testing
class IndustrialIoTTester:
    def __init__(self):
        self.iiot_protocols = {
            'mqtt': {'port': 1883, 'security': 'TLS, authentication'},
            'coap': {'port': 5683, 'security': 'DTLS'},
            'opcua': {'port': 4840, 'security': 'certificates, encryption'},
            'amqp': {'port': 5672, 'security': 'SASL, TLS'},
            'dds': {'multicast': True, 'security': 'DDS Security'}
        }
    
    def test_industrial_iot_security(self, iiot_deployment):
        # Test industrial IoT deployment security
        iiot_tests = {
            'device_security': self.test_iiot_device_security(iiot_deployment),
            'protocol_security': self.test_iiot_protocol_security(iiot_deployment),
            'edge_computing': self.test_edge_computing_security(iiot_deployment),
            'cloud_integration': self.test_iiot_cloud_security(iiot_deployment),
            'operational_technology': self.test_ot_integration_security(iiot_deployment)
        }
        
        return iiot_tests
    
    def test_mqtt_security(self, mqtt_brokers):
        # Test MQTT broker and client security
        mqtt_tests = {}
        
        for broker in mqtt_brokers:
            broker_tests = {
                'authentication': self.test_mqtt_authentication(broker),
                'authorization': self.test_mqtt_topic_authorization(broker),
                'encryption': self.test_mqtt_tls_configuration(broker),
                'message_integrity': self.test_mqtt_message_integrity(broker),
                'client_certificates': self.test_mqtt_client_certificates(broker),
                'broker_security': self.test_mqtt_broker_hardening(broker)
            }
            
            mqtt_tests[broker['name']] = broker_tests
        
        return mqtt_tests
    
    def test_opcua_security(self, opcua_servers):
        # Test OPC UA server security
        opcua_tests = {}
        
        for server in opcua_servers:
            server_tests = {
                'security_policies': self.test_opcua_security_policies(server),
                'certificate_management': self.test_opcua_certificates(server),
                'user_authentication': self.test_opcua_user_authentication(server),
                'access_control': self.test_opcua_access_control(server),
                'audit_logging': self.test_opcua_audit_capabilities(server),
                'secure_communication': self.test_opcua_secure_channels(server)
            }
            
            opcua_tests[server['endpoint']] = server_tests
        
        return opcua_tests</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Automotive and Smart City IoT Security Assessment",
    description: "Conduct comprehensive automotive cybersecurity and smart city IoT security assessment including CAN bus testing, V2X security evaluation, and smart infrastructure analysis.",
    environment: "Automotive testbed with CAN bus simulation, V2X communication systems, and smart city IoT infrastructure",
    tasks: [
      {
        category: "Automotive Security",
        tasks: [
          {
            task: "Perform CAN bus security testing and ECU analysis",
            method: "CAN message analysis, injection testing, and fuzzing",
            expectedFindings: "CAN bus vulnerabilities and ECU security weaknesses",
            points: 30
          }
        ]
      },
      {
        category: "V2X Communication Security",
        tasks: [
          {
            task: "Test V2X communication security and message authentication",
            method: "DSRC/C-V2X testing, certificate validation, and privacy assessment",
            expectedFindings: "V2X security vulnerabilities and privacy protection gaps",
            points: 25
          }
        ]
      },
      {
        category: "Smart City IoT",
        tasks: [
          {
            task: "Assess smart city infrastructure and IoT device security",
            method: "IoT device testing, protocol analysis, and network security assessment",
            expectedFindings: "Smart city security vulnerabilities and infrastructure risks",
            points: 25
          }
        ]
      },
      {
        category: "Industrial IoT Security",
        tasks: [
          {
            task: "Test industrial IoT protocols and edge computing security",
            method: "MQTT, OPC UA, and CoAP security testing",
            expectedFindings: "Industrial IoT protocol vulnerabilities and edge security gaps",
            points: 20
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive automotive and IoT security assessment report",
      "CAN bus security analysis and ECU vulnerability assessment",
      "V2X communication security evaluation and privacy analysis",
      "Smart city infrastructure security assessment",
      "Industrial IoT protocol security analysis",
      "Automotive and IoT security remediation roadmap"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which automotive communication protocol is most commonly used for internal vehicle communication?",
        options: [
          "LIN",
          "CAN",
          "FlexRay",
          "Ethernet"
        ],
        correct: 1,
        explanation: "CAN (Controller Area Network) is the most commonly used protocol for internal vehicle communication between ECUs."
      },
      {
        question: "What is the primary security mechanism used in V2X communications?",
        options: [
          "Symmetric encryption",
          "Digital signatures with certificates",
          "Pre-shared keys",
          "Physical layer security"
        ],
        correct: 1,
        explanation: "V2X communications primarily use digital signatures with certificates for message authentication and integrity protection."
      },
      {
        question: "Which IoT protocol is most commonly used in industrial environments?",
        options: [
          "HTTP",
          "MQTT",
          "OPC UA",
          "CoAP"
        ],
        correct: 2,
        explanation: "OPC UA (Open Platform Communications Unified Architecture) is the most commonly used protocol in industrial IoT environments for machine-to-machine communication."
      }
    ],
    practicalTasks: [
      {
        task: "Conduct CAN bus security testing and automotive ECU vulnerability assessment",
        points: 25
      },
      {
        task: "Test V2X communication security including message authentication and privacy",
        points: 25
      },
      {
        task: "Assess smart city IoT infrastructure security and device vulnerabilities",
        points: 25
      },
      {
        task: "Evaluate industrial IoT protocol security and edge computing risks",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "ISO/SAE 21434 Automotive Cybersecurity",
      url: "https://www.iso.org/standard/70918.html",
      type: "standard"
    },
    {
      title: "NIST Cybersecurity Framework for Smart Cities",
      url: "https://www.nist.gov/cyberframework/smart-cities",
      type: "framework"
    },
    {
      title: "Industrial Internet Security Framework",
      url: "https://www.iiconsortium.org/IISF.htm",
      type: "framework"
    }
  ],
  tags: ["automotive-security", "can-bus", "v2x", "smart-city", "industrial-iot"],
  lastUpdated: "2024-01-15"
};
