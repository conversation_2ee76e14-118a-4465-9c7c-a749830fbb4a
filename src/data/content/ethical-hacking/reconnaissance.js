/**
 * Reconnaissance Module - Information Gathering
 */

export const reconnaissanceContent = {
  id: "eh-2",
  pathId: "ethical-hacking-fundamentals",
  title: "Reconnaissance & Information Gathering",
  description: "Master passive and active reconnaissance techniques to gather critical information about targets while maintaining stealth and avoiding detection.",
  objectives: [
    "Understand passive vs active reconnaissance methodologies",
    "Master OSINT (Open Source Intelligence) techniques and tools",
    "Learn advanced Google dorking and search engine exploitation",
    "Perform DNS enumeration and subdomain discovery",
    "Conduct social media intelligence gathering",
    "Implement automated reconnaissance workflows"
  ],
  difficulty: "Beginner",
  estimatedTime: 120,
  sections: [
    {
      title: "Reconnaissance Fundamentals",
      content: `
        <h2>Reconnaissance Fundamentals</h2>
        <p>Reconnaissance is the initial phase of ethical hacking where information about the target is gathered to understand the attack surface and potential vulnerabilities.</p>
        
        <h3>Types of Reconnaissance</h3>
        <ul>
          <li><strong>Passive Reconnaissance:</strong> Gathering information without directly interacting with the target
            <ul>
              <li>Search engines and public databases</li>
              <li>Social media and professional networks</li>
              <li>DNS records and WHOIS information</li>
              <li>Cached content and archived websites</li>
            </ul>
          </li>
          <li><strong>Active Reconnaissance:</strong> Direct interaction with the target system
            <ul>
              <li>Port scanning and service enumeration</li>
              <li>Banner grabbing and version detection</li>
              <li>Network mapping and topology discovery</li>
              <li>Vulnerability scanning</li>
            </ul>
          </li>
        </ul>
        
        <h3>Information Categories</h3>
        <ul>
          <li><strong>Network Information:</strong> IP ranges, domain names, DNS servers</li>
          <li><strong>System Information:</strong> Operating systems, services, applications</li>
          <li><strong>Organization Information:</strong> Company structure, employees, locations</li>
          <li><strong>Personal Information:</strong> Email addresses, phone numbers, social profiles</li>
          <li><strong>Technical Information:</strong> Technologies used, security measures, policies</li>
        </ul>
        
        <h3>Reconnaissance Methodology</h3>
        <ol>
          <li><strong>Define Scope:</strong> Establish clear boundaries for information gathering</li>
          <li><strong>Passive Collection:</strong> Gather publicly available information</li>
          <li><strong>Active Enumeration:</strong> Directly probe target systems</li>
          <li><strong>Analysis & Correlation:</strong> Analyze collected data for patterns</li>
          <li><strong>Attack Surface Mapping:</strong> Identify potential entry points</li>
          <li><strong>Documentation:</strong> Record findings systematically</li>
        </ol>
      `,
      type: "text"
    },
    {
      title: "OSINT and Search Engine Techniques",
      content: `
        <h2>OSINT and Search Engine Techniques</h2>
        <p>Open Source Intelligence (OSINT) leverages publicly available information to gather intelligence about targets.</p>
        
        <h3>Google Dorking Advanced Techniques</h3>
        <ul>
          <li><strong>Site-specific searches:</strong> <code>site:example.com filetype:pdf</code></li>
          <li><strong>Cached content:</strong> <code>cache:example.com</code></li>
          <li><strong>File type targeting:</strong> <code>filetype:xlsx site:example.com</code></li>
          <li><strong>Specific content:</strong> <code>"confidential" site:example.com</code></li>
          <li><strong>URL patterns:</strong> <code>inurl:admin site:example.com</code></li>
          <li><strong>Title searches:</strong> <code>intitle:"index of" site:example.com</code></li>
          <li><strong>Exclude results:</strong> <code>site:example.com -www</code></li>
        </ul>
        
        <h3>Specialized Search Engines</h3>
        <ul>
          <li><strong>Shodan:</strong> Internet-connected device search engine
            <ul>
              <li>Search for specific services: <code>apache port:80</code></li>
              <li>Geographic targeting: <code>country:US port:22</code></li>
              <li>Vulnerable systems: <code>product:"Apache httpd" version:"2.2"</code></li>
            </ul>
          </li>
          <li><strong>Censys:</strong> Internet-wide scanning and search
            <ul>
              <li>Certificate transparency logs</li>
              <li>IPv4 and IPv6 host discovery</li>
              <li>Protocol-specific searches</li>
            </ul>
          </li>
          <li><strong>Binary Edge:</strong> Internet scanning and threat intelligence</li>
          <li><strong>ZoomEye:</strong> Cyberspace search engine</li>
        </ul>
        
        <h3>Social Media Intelligence</h3>
        <ul>
          <li><strong>LinkedIn Intelligence:</strong>
            <ul>
              <li>Employee enumeration and organizational structure</li>
              <li>Technology stack identification from job postings</li>
              <li>Contact information discovery</li>
            </ul>
          </li>
          <li><strong>Facebook/Instagram:</strong>
            <ul>
              <li>Personal information and relationships</li>
              <li>Location data and check-ins</li>
              <li>Photo metadata analysis</li>
            </ul>
          </li>
          <li><strong>Twitter/X Intelligence:</strong>
            <ul>
              <li>Real-time information and opinions</li>
              <li>Network analysis and connections</li>
              <li>Sentiment analysis and trends</li>
            </ul>
          </li>
        </ul>
        
        <h3>Professional Intelligence Gathering</h3>
        <ul>
          <li><strong>Company Registries:</strong> SEC filings, business registrations</li>
          <li><strong>Patent Databases:</strong> USPTO, WIPO patent searches</li>
          <li><strong>Job Boards:</strong> Technology requirements and internal processes</li>
          <li><strong>Press Releases:</strong> Company announcements and changes</li>
          <li><strong>Industry Reports:</strong> Market analysis and competitive intelligence</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "DNS Enumeration and Network Discovery",
      content: `
        <h2>DNS Enumeration and Network Discovery</h2>
        <p>DNS enumeration reveals network topology, subdomains, and infrastructure details critical for understanding the attack surface.</p>
        
        <h3>DNS Record Types and Intelligence</h3>
        <ul>
          <li><strong>A Records:</strong> IPv4 address mappings</li>
          <li><strong>AAAA Records:</strong> IPv6 address mappings</li>
          <li><strong>CNAME Records:</strong> Canonical name aliases</li>
          <li><strong>MX Records:</strong> Mail exchange servers</li>
          <li><strong>NS Records:</strong> Name server information</li>
          <li><strong>TXT Records:</strong> Text records (SPF, DKIM, verification)</li>
          <li><strong>SOA Records:</strong> Start of Authority information</li>
          <li><strong>PTR Records:</strong> Reverse DNS lookups</li>
        </ul>
        
        <h3>Subdomain Discovery Techniques</h3>
        <ul>
          <li><strong>Brute Force:</strong>
            <ul>
              <li>Dictionary-based subdomain enumeration</li>
              <li>Common subdomain wordlists</li>
              <li>Recursive subdomain discovery</li>
            </ul>
          </li>
          <li><strong>Certificate Transparency:</strong>
            <ul>
              <li>crt.sh certificate transparency logs</li>
              <li>SSL certificate analysis</li>
              <li>Historical certificate data</li>
            </ul>
          </li>
          <li><strong>DNS Zone Transfers:</strong>
            <ul>
              <li>AXFR request attempts</li>
              <li>Misconfigured DNS servers</li>
              <li>Complete zone enumeration</li>
            </ul>
          </li>
        </ul>
        
        <h3>Advanced DNS Techniques</h3>
        <ul>
          <li><strong>DNS Cache Snooping:</strong> Identifying cached domains</li>
          <li><strong>DNS Tunneling Detection:</strong> Identifying covert channels</li>
          <li><strong>Reverse DNS Sweeps:</strong> PTR record enumeration</li>
          <li><strong>DNS Wildcards:</strong> Identifying wildcard configurations</li>
        </ul>
        
        <h3>Network Topology Mapping</h3>
        <ul>
          <li><strong>ASN (Autonomous System Number) Enumeration:</strong>
            <ul>
              <li>BGP routing information</li>
              <li>IP address block identification</li>
              <li>Network infrastructure mapping</li>
            </ul>
          </li>
          <li><strong>WHOIS Information:</strong>
            <ul>
              <li>Domain registration details</li>
              <li>Administrative contacts</li>
              <li>Registration history</li>
            </ul>
          </li>
          <li><strong>Geolocation Intelligence:</strong>
            <ul>
              <li>IP geolocation databases</li>
              <li>Data center identification</li>
              <li>CDN and cloud service detection</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Advanced Reconnaissance Tools",
      content: `
        <h2>Advanced Reconnaissance Tools</h2>
        <p>Professional reconnaissance requires sophisticated tools for efficient and comprehensive information gathering.</p>
        
        <h3>Automated OSINT Frameworks</h3>
        <ul>
          <li><strong>Maltego:</strong>
            <ul>
              <li>Visual link analysis and data mining</li>
              <li>Transform-based intelligence gathering</li>
              <li>Relationship mapping and pattern recognition</li>
            </ul>
          </li>
          <li><strong>Recon-ng:</strong>
            <ul>
              <li>Modular reconnaissance framework</li>
              <li>Automated OSINT collection</li>
              <li>Database-driven result management</li>
            </ul>
          </li>
          <li><strong>theHarvester:</strong>
            <ul>
              <li>Email and subdomain enumeration</li>
              <li>Search engine aggregation</li>
              <li>Social media intelligence</li>
            </ul>
          </li>
        </ul>
        
        <h3>Specialized Reconnaissance Tools</h3>
        <ul>
          <li><strong>Amass:</strong> Advanced subdomain enumeration</li>
          <li><strong>Subfinder:</strong> Fast passive subdomain discovery</li>
          <li><strong>Assetfinder:</strong> Domain and subdomain discovery</li>
          <li><strong>Gobuster:</strong> Directory and DNS enumeration</li>
          <li><strong>Fierce:</strong> DNS reconnaissance and enumeration</li>
          <li><strong>DMitry:</strong> Comprehensive information gathering</li>
        </ul>
        
        <h3>Custom Reconnaissance Automation</h3>
        <ul>
          <li><strong>Bash Scripting:</strong>
            <ul>
              <li>Automated reconnaissance workflows</li>
              <li>Multi-tool integration</li>
              <li>Result correlation and analysis</li>
            </ul>
          </li>
          <li><strong>Python Scripts:</strong>
            <ul>
              <li>API integration and automation</li>
              <li>Custom OSINT collection</li>
              <li>Data parsing and analysis</li>
            </ul>
          </li>
          <li><strong>Reconnaissance Pipelines:</strong>
            <ul>
              <li>Continuous monitoring systems</li>
              <li>Change detection and alerting</li>
              <li>Automated reporting and visualization</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Comprehensive Reconnaissance Lab",
    description: "Hands-on reconnaissance techniques using multiple tools and methodologies for thorough target assessment.",
    tasks: [
      {
        category: "Passive Reconnaissance",
        commands: [
          {
            command: "whois example.com",
            description: "Gather domain registration information",
            hint: "Look for registrant details, name servers, and registration dates",
            expectedOutput: "Domain registration details including contacts and infrastructure"
          },
          {
            command: "dig example.com ANY",
            description: "Enumerate all DNS record types",
            hint: "Analyze different record types for infrastructure insights",
            expectedOutput: "Complete DNS record enumeration"
          },
          {
            command: "nslookup -type=mx example.com",
            description: "Identify mail exchange servers",
            hint: "Mail servers often reveal internal network information",
            expectedOutput: "Mail server configuration and priorities"
          }
        ]
      },
      {
        category: "OSINT Collection",
        commands: [
          {
            command: "theHarvester -d example.com -b google,bing,linkedin",
            description: "Automated email and subdomain enumeration",
            hint: "Use multiple sources for comprehensive results",
            expectedOutput: "Email addresses, subdomains, and associated information"
          },
          {
            command: "amass enum -d example.com",
            description: "Advanced subdomain discovery",
            hint: "Amass uses multiple techniques for thorough enumeration",
            expectedOutput: "Comprehensive subdomain list with sources"
          }
        ]
      },
      {
        category: "Advanced Techniques",
        commands: [
          {
            command: "subfinder -d example.com -silent | httpx -silent | nuclei",
            description: "Automated subdomain discovery and vulnerability scanning",
            hint: "Chain tools for comprehensive reconnaissance pipeline",
            expectedOutput: "Live subdomains with potential security issues"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary advantage of passive reconnaissance over active reconnaissance?",
      options: [
        "It provides more detailed technical information",
        "It cannot be detected by the target organization",
        "It is faster and more automated",
        "It requires fewer tools and resources"
      ],
      correct: 1,
      explanation: "Passive reconnaissance cannot be detected by the target because it doesn't involve direct interaction with target systems, making it safer and more stealthy."
    },
    {
      question: "Which Google dork would be most effective for finding exposed configuration files?",
      options: [
        'site:example.com "password"',
        'filetype:conf site:example.com',
        'inurl:admin site:example.com',
        'cache:example.com'
      ],
      correct: 1,
      explanation: "The filetype:conf search specifically targets configuration files (.conf) which often contain sensitive information like passwords, API keys, and system configurations."
    },
    {
      question: "What information can Certificate Transparency logs provide during reconnaissance?",
      options: [
        "SSH keys and certificates",
        "Database connection strings",
        "Subdomains and SSL certificate history",
        "Employee email addresses"
      ],
      correct: 2,
      explanation: "Certificate Transparency logs contain historical SSL certificate data, revealing subdomains, certificate authorities, and infrastructure changes over time."
    }
  ]
}; 