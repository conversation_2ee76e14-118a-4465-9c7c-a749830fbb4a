/**
 * Ethical Hacking Module: Advanced OSINT and Reconnaissance
 * Module ID: eh-26
 */

export const osintReconnaissanceContent = {
  id: "eh-26",
  title: "Advanced OSINT and Reconnaissance",
  description: "Master advanced open source intelligence gathering and reconnaissance techniques for comprehensive target profiling and attack surface mapping.",
  difficulty: "Advanced",
  estimatedTime: 100,
  objectives: [
    "Master advanced OSINT collection and analysis techniques",
    "Learn automated reconnaissance and data correlation",
    "Develop skills in social media intelligence gathering",
    "Understand threat intelligence and attribution analysis",
    "Apply OSINT in penetration testing and red team operations"
  ],
  prerequisites: ["eh-1", "eh-2", "eh-20", "eh-22"],
  sections: [
    {
      title: "Advanced OSINT Techniques",
      content: `
        <h2>Open Source Intelligence Mastery</h2>
        <p>Advanced OSINT involves sophisticated techniques for gathering, analyzing, and correlating publicly available information for security assessments.</p>
        
        <h3>OSINT Framework and Methodology</h3>
        <ol>
          <li><strong>Requirements</strong> - Define intelligence needs</li>
          <li><strong>Collection</strong> - Gather relevant information</li>
          <li><strong>Processing</strong> - Organize and structure data</li>
          <li><strong>Analysis</strong> - Extract actionable intelligence</li>
          <li><strong>Dissemination</strong> - Present findings effectively</li>
        </ol>

        <h3>Advanced Search Techniques</h3>
        <h4>Google Dorking Mastery</h4>
        <pre><code># Advanced Google operators:

Site-specific searches:
site:target.com filetype:pdf
site:target.com inurl:admin
site:target.com intitle:"index of"

File type hunting:
filetype:xlsx site:target.com
filetype:doc "confidential" site:target.com
filetype:sql site:target.com

Error pages and debug info:
site:target.com "mysql_connect"
site:target.com "Warning: mysql"
site:target.com "Fatal error"

Configuration files:
site:target.com filetype:conf
site:target.com filetype:ini
site:target.com filetype:env

Login pages and admin panels:
site:target.com inurl:login
site:target.com inurl:admin
site:target.com "admin panel"

# Shodan search operators:
org:"Target Company"
ssl:"target.com"
port:22 country:US
product:"Apache" version:"2.4"</code></pre>

        <h3>Social Media Intelligence</h3>
        <h4>LinkedIn Reconnaissance</h4>
        <pre><code># LinkedIn intelligence gathering:

Employee enumeration:
- Company employee lists
- Job titles and departments
- Contact information
- Professional connections
- Skills and technologies

Organizational structure:
- Management hierarchy
- Department relationships
- Key personnel identification
- Recent hires and departures

Technology stack identification:
- Job postings requirements
- Employee skill listings
- Project descriptions
- Technology mentions

# Tools for LinkedIn OSINT:
- theHarvester
- LinkedInt
- CrossLinked
- LinkedIn2Username</code></pre>

        <h4>Social Media Automation</h4>
        <pre><code># Automated social media collection:

Twitter intelligence:
- Hashtag monitoring
- Geolocation data
- User relationship mapping
- Sentiment analysis

Facebook reconnaissance:
- Public profile information
- Photo metadata analysis
- Check-in locations
- Friend network mapping

Instagram analysis:
- Photo geolocation
- Hashtag associations
- Story content analysis
- Follower relationships

# Python automation example:
import tweepy
import requests
from bs4 import BeautifulSoup

class SocialMediaOSINT:
    def __init__(self, api_keys):
        self.twitter_api = tweepy.API(api_keys['twitter'])
    
    def collect_tweets(self, username, count=100):
        tweets = []
        for tweet in tweepy.Cursor(self.twitter_api.user_timeline, 
                                 screen_name=username).items(count):
            tweets.append({
                'text': tweet.text,
                'created_at': tweet.created_at,
                'location': tweet.coordinates,
                'retweets': tweet.retweet_count
            })
        return tweets</code></pre>
      `,
      type: "text"
    },
    {
      title: "Technical Reconnaissance",
      content: `
        <h2>Technical Intelligence Gathering</h2>
        <p>Technical reconnaissance focuses on gathering technical information about target infrastructure, applications, and security posture.</p>

        <h3>Infrastructure Mapping</h3>
        <h4>DNS Intelligence</h4>
        <pre><code># Advanced DNS reconnaissance:

Subdomain enumeration:
dnsrecon -d target.com -t brt -D subdomains.txt
amass enum -d target.com
subfinder -d target.com

DNS zone transfers:
dig axfr @ns1.target.com target.com
fierce -dns target.com

DNS history analysis:
# SecurityTrails API
curl -H "APIKEY: your-api-key" \
  "https://api.securitytrails.com/v1/domain/target.com/subdomains"

Certificate transparency:
# crt.sh search
curl "https://crt.sh/?q=%.target.com&output=json"

# Tools for DNS intelligence:
- DNSdumpster
- Robtex
- VirusTotal
- PassiveTotal</code></pre>

        <h3>Application Fingerprinting</h3>
        <h4>Technology Stack Analysis</h4>
        <pre><code># Web application fingerprinting:

HTTP header analysis:
curl -I https://target.com
whatweb target.com
wappalyzer target.com

JavaScript framework detection:
# Analyze client-side frameworks
# React, Angular, Vue.js detection
# Library version identification

Server fingerprinting:
nmap -sV -p 80,443 target.com
httprint -h target.com -s signatures.txt

CMS detection:
wpscan --url https://target.com --enumerate
droopescan scan drupal -u https://target.com

# Automated technology detection:
import requests
from bs4 import BeautifulSoup
import re

def detect_technologies(url):
    response = requests.get(url)
    
    # Server header analysis
    server = response.headers.get('Server', '')
    
    # Meta generator tags
    soup = BeautifulSoup(response.text, 'html.parser')
    generator = soup.find('meta', {'name': 'generator'})
    
    # JavaScript library detection
    js_libs = []
    if 'jquery' in response.text.lower():
        js_libs.append('jQuery')
    if 'angular' in response.text.lower():
        js_libs.append('AngularJS')
    
    return {
        'server': server,
        'generator': generator.get('content') if generator else None,
        'javascript_libraries': js_libs
    }</code></pre>

        <h3>Email and Domain Intelligence</h3>
        <h4>Email Harvesting</h4>
        <pre><code># Email collection techniques:

Search engine harvesting:
theHarvester -d target.com -b google,bing,linkedin
hunter.io API integration
clearbit API for email verification

WHOIS analysis:
whois target.com
whois -h whois.arin.net "Target Company"

Email pattern analysis:
# Common patterns:
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>

# Email validation:
import re
import dns.resolver

def validate_email_domain(email):
    domain = email.split('@')[1]
    try:
        mx_records = dns.resolver.resolve(domain, 'MX')
        return len(mx_records) > 0
    except:
        return False</code></pre>

        <h3>Dark Web Intelligence</h3>
        <h4>Dark Web Monitoring</h4>
        <pre><code># Dark web reconnaissance:

Tor network access:
# Use Tor browser or proxy
# Access .onion sites
# Monitor dark web markets

Breach database searches:
# HaveIBeenPwned API
# DeHashed searches
# Breach compilation analysis

Paste site monitoring:
# Pastebin searches
# GitHub secret scanning
# Telegram channel monitoring

# Automated dark web monitoring:
import requests
from tor_requests import TorRequests

class DarkWebMonitor:
    def __init__(self):
        self.tor_session = TorRequests()
    
    def search_paste_sites(self, keywords):
        results = []
        paste_sites = [
            'https://pastebin.com/search',
            'https://ghostbin.co/search',
            'https://justpaste.it/search'
        ]
        
        for site in paste_sites:
            # Search for keywords
            # Parse results
            # Store findings
            pass
        
        return results</code></pre>

        <h3>Threat Intelligence Integration</h3>
        <h4>IOC Correlation</h4>
        <pre><code># Threat intelligence analysis:

IP reputation checking:
# VirusTotal API
# AbuseIPDB
# Shodan intelligence

Domain reputation:
# URLVoid analysis
# Cisco Talos
# IBM X-Force

File hash analysis:
# Malware family identification
# Attribution analysis
# Campaign correlation

# Automated threat intelligence:
import requests
import json

class ThreatIntelligence:
    def __init__(self, api_keys):
        self.vt_api_key = api_keys['virustotal']
        self.shodan_api_key = api_keys['shodan']
    
    def analyze_ip(self, ip_address):
        # VirusTotal lookup
        vt_url = f"https://www.virustotal.com/vtapi/v2/ip-address/report"
        vt_params = {'apikey': self.vt_api_key, 'ip': ip_address}
        vt_response = requests.get(vt_url, params=vt_params)
        
        # Shodan lookup
        shodan_url = f"https://api.shodan.io/shodan/host/{ip_address}"
        shodan_params = {'key': self.shodan_api_key}
        shodan_response = requests.get(shodan_url, params=shodan_params)
        
        return {
            'virustotal': vt_response.json(),
            'shodan': shodan_response.json()
        }</code></pre>
      `,
      type: "text"
    },
    {
      title: "OSINT Automation and Analysis",
      content: `
        <h2>Automated Intelligence Collection</h2>
        <p>Automation enables large-scale OSINT collection and analysis, improving efficiency and coverage of intelligence gathering operations.</p>

        <h3>Automated Reconnaissance Frameworks</h3>
        <h4>Custom OSINT Framework</h4>
        <pre><code># Comprehensive OSINT automation:

import asyncio
import aiohttp
import json
from datetime import datetime

class OSINTFramework:
    def __init__(self, target):
        self.target = target
        self.results = {
            'domains': [],
            'emails': [],
            'social_media': [],
            'technologies': [],
            'vulnerabilities': []
        }
    
    async def run_all_modules(self):
        tasks = [
            self.subdomain_enumeration(),
            self.email_harvesting(),
            self.social_media_analysis(),
            self.technology_detection(),
            self.vulnerability_scanning()
        ]
        
        await asyncio.gather(*tasks)
        return self.results
    
    async def subdomain_enumeration(self):
        # Multiple subdomain enumeration techniques
        # DNS brute force
        # Certificate transparency
        # Search engine dorking
        pass
    
    async def email_harvesting(self):
        # Search engines
        # Social media
        # WHOIS data
        # Breach databases
        pass
    
    async def social_media_analysis(self):
        # LinkedIn profiles
        # Twitter accounts
        # Facebook pages
        # Instagram profiles
        pass
    
    async def technology_detection(self):
        # Web application fingerprinting
        # Server identification
        # Framework detection
        # Version analysis
        pass
    
    async def vulnerability_scanning(self):
        # CVE database searches
        # Exploit database queries
        # Security advisory analysis
        pass

# Usage
framework = OSINTFramework("target.com")
results = asyncio.run(framework.run_all_modules())</code></pre>

        <h3>Data Correlation and Analysis</h3>
        <h4>Intelligence Fusion</h4>
        <pre><code># Data correlation techniques:

Entity relationship mapping:
# Person-to-organization links
# Domain-to-IP relationships
# Email-to-social media connections

Timeline analysis:
# Event correlation
# Activity patterns
# Temporal relationships

Geolocation correlation:
# IP geolocation
# Social media check-ins
# Photo metadata analysis

# Graph database for relationships:
from neo4j import GraphDatabase

class IntelligenceGraph:
    def __init__(self, uri, user, password):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
    
    def add_person(self, name, email, company):
        with self.driver.session() as session:
            session.run(
                "CREATE (p:Person {name: $name, email: $email}) "
                "CREATE (c:Company {name: $company}) "
                "CREATE (p)-[:WORKS_FOR]->(c)",
                name=name, email=email, company=company
            )
    
    def find_connections(self, entity):
        with self.driver.session() as session:
            result = session.run(
                "MATCH (n)-[r]-(m) WHERE n.name = $entity "
                "RETURN n, r, m",
                entity=entity
            )
            return [record for record in result]</code></pre>

        <h3>OSINT Reporting and Visualization</h3>
        <h4>Intelligence Products</h4>
        <pre><code># Automated report generation:

import matplotlib.pyplot as plt
import networkx as nx
from jinja2 import Template

class OSINTReporter:
    def __init__(self, intelligence_data):
        self.data = intelligence_data
    
    def generate_network_graph(self):
        G = nx.Graph()
        
        # Add nodes and edges from intelligence data
        for person in self.data['people']:
            G.add_node(person['name'], type='person')
            if person['company']:
                G.add_node(person['company'], type='company')
                G.add_edge(person['name'], person['company'])
        
        # Visualize network
        pos = nx.spring_layout(G)
        nx.draw(G, pos, with_labels=True, node_color='lightblue')
        plt.savefig('network_graph.png')
    
    def generate_timeline(self):
        # Create timeline of events
        # Plot activity patterns
        # Identify anomalies
        pass
    
    def create_intelligence_report(self):
        template = Template('''
        # OSINT Intelligence Report
        
        ## Executive Summary
        {{ summary }}
        
        ## Key Findings
        {% for finding in key_findings %}
        - {{ finding }}
        {% endfor %}
        
        ## Technical Details
        ### Domains Discovered: {{ domains|length }}
        ### Emails Collected: {{ emails|length }}
        ### Social Media Profiles: {{ social_profiles|length }}
        
        ## Recommendations
        {{ recommendations }}
        ''')
        
        return template.render(
            summary=self.data['summary'],
            key_findings=self.data['key_findings'],
            domains=self.data['domains'],
            emails=self.data['emails'],
            social_profiles=self.data['social_media'],
            recommendations=self.data['recommendations']
        )</code></pre>

        <h3>Operational Security for OSINT</h3>
        <h4>Attribution Avoidance</h4>
        <pre><code># OPSEC considerations:

Network anonymization:
# Tor browser usage
# VPN rotation
# Proxy chains
# Public WiFi networks

Browser fingerprinting:
# User agent rotation
# Resolution changes
# Plugin management
# Cookie isolation

API key management:
# Separate accounts
# Rate limiting awareness
# Geographic distribution
# Rotation schedules

# Automated OPSEC:
import random
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

class OSINTBrowser:
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
    
    def create_session(self):
        options = Options()
        options.add_argument(f'--user-agent={random.choice(self.user_agents)}')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        driver = webdriver.Chrome(options=options)
        return driver
    
    def random_delay(self, min_seconds=1, max_seconds=5):
        time.sleep(random.uniform(min_seconds, max_seconds))</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Corporate Intelligence Assessment",
    description: "Conduct comprehensive OSINT investigation of a target organization including employee profiling, infrastructure mapping, and threat intelligence analysis.",
    environment: "Target organization with public web presence, social media accounts, and employee profiles",
    tasks: [
      {
        category: "Automated Reconnaissance",
        tasks: [
          {
            task: "Deploy automated OSINT collection framework",
            method: "Custom scripts and tools for comprehensive data gathering",
            expectedFindings: "Complete target profile with technical and human intelligence",
            points: 25
          }
        ]
      },
      {
        category: "Social Media Intelligence",
        tasks: [
          {
            task: "Conduct social media intelligence gathering",
            method: "LinkedIn, Twitter, Facebook analysis and employee profiling",
            expectedFindings: "Employee directory with contact information and organizational structure",
            points: 25
          }
        ]
      },
      {
        category: "Technical Intelligence",
        tasks: [
          {
            task: "Map technical infrastructure and attack surface",
            method: "DNS analysis, subdomain enumeration, and technology fingerprinting",
            expectedFindings: "Complete infrastructure map with potential attack vectors",
            points: 25
          }
        ]
      },
      {
        category: "Intelligence Analysis",
        tasks: [
          {
            task: "Correlate intelligence and create actionable reports",
            method: "Data fusion, relationship mapping, and visualization",
            expectedFindings: "Comprehensive intelligence report with recommendations",
            points: 25
          }
        ]
      }
    ],
    deliverables: [
      "Comprehensive OSINT intelligence report",
      "Employee and organizational structure analysis",
      "Technical infrastructure and attack surface mapping",
      "Social media intelligence summary",
      "Threat intelligence correlation and analysis",
      "Automated OSINT collection framework and tools"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which Google search operator is most effective for finding specific file types on a target domain?",
        options: [
          "site: and inurl:",
          "site: and filetype:",
          "intitle: and inurl:",
          "cache: and site:"
        ],
        correct: 1,
        explanation: "The combination of 'site:' and 'filetype:' operators allows searching for specific file types within a target domain."
      },
      {
        question: "What is the primary purpose of certificate transparency logs in OSINT?",
        options: [
          "Finding SSL vulnerabilities",
          "Discovering subdomains",
          "Analyzing encryption strength",
          "Monitoring certificate revocation"
        ],
        correct: 1,
        explanation: "Certificate transparency logs are valuable for discovering subdomains as they contain records of all certificates issued for a domain."
      },
      {
        question: "Which technique is most important for maintaining operational security during OSINT collection?",
        options: [
          "Using strong passwords",
          "Attribution avoidance",
          "Data encryption",
          "Regular backups"
        ],
        correct: 1,
        explanation: "Attribution avoidance is crucial in OSINT to prevent the target from identifying the intelligence collection activities."
      }
    ],
    practicalTasks: [
      {
        task: "Develop and deploy automated OSINT collection framework",
        points: 25
      },
      {
        task: "Conduct comprehensive social media intelligence gathering",
        points: 25
      },
      {
        task: "Map technical infrastructure using advanced reconnaissance techniques",
        points: 25
      },
      {
        task: "Create intelligence fusion report with data correlation and visualization",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "OSINT Framework",
      url: "https://osintframework.com/",
      type: "framework"
    },
    {
      title: "Maltego Community Edition",
      url: "https://www.maltego.com/",
      type: "tool"
    },
    {
      title: "The OSINT Handbook",
      url: "https://www.i-intelligence.eu/wp-content/uploads/2016/11/2016_November_Open-Source-Intelligence-Tools-and-Resources-Handbook.pdf",
      type: "handbook"
    }
  ],
  tags: ["osint", "reconnaissance", "social-media-intelligence", "automation", "threat-intelligence"],
  lastUpdated: "2024-01-15"
};
