/**
 * Operational Intelligence Module
 */

export const operationalIntelligenceContent = {
  id: "ti-15",
  pathId: "threat-intelligence",
  title: "Operational Intelligence",
  description: "Master operational threat intelligence for medium-term planning and operations, bridging tactical and strategic intelligence to support security program management and operational decision-making.",
  objectives: [
    "Understand operational intelligence scope and applications",
    "Learn campaign analysis and tracking techniques",
    "Master threat actor operational pattern analysis",
    "Develop operational planning and resource allocation skills",
    "Learn operational intelligence integration with security programs",
    "Create operational intelligence products and assessments"
  ],
  difficulty: "Advanced",
  estimatedTime: 125,
  sections: [
    {
      title: "Operational Intelligence Framework",
      content: `
        <h2>Operational Threat Intelligence Framework</h2>
        <p>Operational intelligence provides medium-term analysis and insights to support security program planning, resource allocation, and operational decision-making.</p>
        
        <h3>Operational Intelligence Scope</h3>
        <ul>
          <li><strong>Time Horizon:</strong>
            <ul>
              <li>Medium-term perspective (weeks to months)</li>
              <li>Campaign and operation lifecycle analysis</li>
              <li>Seasonal and cyclical pattern recognition</li>
              <li>Planning cycle alignment</li>
            </ul>
          </li>
          <li><strong>Operational Focus:</strong>
            <ul>
              <li>Security program effectiveness</li>
              <li>Resource allocation optimization</li>
              <li>Capability gap identification</li>
              <li>Operational readiness assessment</li>
            </ul>
          </li>
          <li><strong>Decision Support:</strong>
            <ul>
              <li>Security investment priorities</li>
              <li>Technology deployment decisions</li>
              <li>Training and development needs</li>
              <li>Partnership and collaboration opportunities</li>
            </ul>
          </li>
        </ul>
        
        <h3>Operational Intelligence Requirements</h3>
        <ul>
          <li><strong>Security Program Management:</strong>
            <ul>
              <li>Threat landscape evolution tracking</li>
              <li>Control effectiveness assessment</li>
              <li>Risk posture evaluation</li>
              <li>Performance metrics and KPIs</li>
            </ul>
          </li>
          <li><strong>Operational Planning:</strong>
            <ul>
              <li>Threat-informed planning cycles</li>
              <li>Resource allocation guidance</li>
              <li>Capability development roadmaps</li>
              <li>Exercise and training planning</li>
            </ul>
          </li>
          <li><strong>Technology and Architecture:</strong>
            <ul>
              <li>Security architecture guidance</li>
              <li>Technology selection criteria</li>
              <li>Integration and deployment planning</li>
              <li>Performance and scalability requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>Intelligence Integration Levels</h3>
        <ul>
          <li><strong>Tactical Integration:</strong>
            <ul>
              <li>Tactical intelligence aggregation</li>
              <li>Pattern and trend identification</li>
              <li>Operational context development</li>
              <li>Medium-term impact assessment</li>
            </ul>
          </li>
          <li><strong>Strategic Alignment:</strong>
            <ul>
              <li>Strategic objective support</li>
              <li>Long-term trend validation</li>
              <li>Operational feasibility assessment</li>
              <li>Resource and capability alignment</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Campaign Analysis and Tracking",
      content: `
        <h2>Campaign Analysis and Operational Tracking</h2>
        <p>Campaign analysis involves comprehensive tracking and analysis of multi-stage threat actor operations to understand their objectives, methods, and evolution over time.</p>
        
        <h3>Campaign Identification and Definition</h3>
        <ul>
          <li><strong>Campaign Characteristics:</strong>
            <ul>
              <li>Coordinated multi-stage operations</li>
              <li>Common objectives and targets</li>
              <li>Shared infrastructure and tools</li>
              <li>Consistent TTPs and behaviors</li>
            </ul>
          </li>
          <li><strong>Campaign Boundaries:</strong>
            <ul>
              <li>Temporal boundaries and duration</li>
              <li>Geographic scope and targeting</li>
              <li>Sector and industry focus</li>
              <li>Technical and operational scope</li>
            </ul>
          </li>
          <li><strong>Campaign Classification:</strong>
            <ul>
              <li>Espionage and intelligence gathering</li>
              <li>Financial crime and fraud</li>
              <li>Sabotage and disruption</li>
              <li>Influence and information operations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Campaign Lifecycle Analysis</h3>
        <ul>
          <li><strong>Planning and Preparation:</strong>
            <ul>
              <li>Target selection and reconnaissance</li>
              <li>Infrastructure development</li>
              <li>Tool and capability preparation</li>
              <li>Operational security planning</li>
            </ul>
          </li>
          <li><strong>Execution and Operations:</strong>
            <ul>
              <li>Initial access and establishment</li>
              <li>Lateral movement and expansion</li>
              <li>Objective achievement activities</li>
              <li>Persistence and maintenance</li>
            </ul>
          </li>
          <li><strong>Adaptation and Evolution:</strong>
            <ul>
              <li>Response to defensive measures</li>
              <li>Technique and tool updates</li>
              <li>Infrastructure changes</li>
              <li>Operational procedure modifications</li>
            </ul>
          </li>
          <li><strong>Conclusion and Assessment:</strong>
            <ul>
              <li>Objective achievement evaluation</li>
              <li>Success and failure analysis</li>
              <li>Lessons learned integration</li>
              <li>Future campaign planning</li>
            </ul>
          </li>
        </ul>
        
        <h3>Campaign Tracking Methodologies</h3>
        <ul>
          <li><strong>Timeline Construction:</strong>
            <ul>
              <li>Chronological event mapping</li>
              <li>Phase and milestone identification</li>
              <li>Causal relationship analysis</li>
              <li>Gap and uncertainty documentation</li>
            </ul>
          </li>
          <li><strong>Infrastructure Tracking:</strong>
            <ul>
              <li>Domain and IP address monitoring</li>
              <li>Certificate and hosting analysis</li>
              <li>Communication channel tracking</li>
              <li>Infrastructure lifecycle management</li>
            </ul>
          </li>
          <li><strong>Behavioral Pattern Analysis:</strong>
            <ul>
              <li>Operational tempo and timing</li>
              <li>Target selection patterns</li>
              <li>Technique preference evolution</li>
              <li>Success and failure patterns</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Operational Planning and Resource Allocation",
      content: `
        <h2>Operational Planning and Resource Allocation</h2>
        <p>Operational intelligence informs security program planning and resource allocation decisions by providing threat-informed insights and recommendations.</p>
        
        <h3>Threat-Informed Planning</h3>
        <ul>
          <li><strong>Planning Cycle Integration:</strong>
            <ul>
              <li>Annual security planning alignment</li>
              <li>Quarterly review and adjustment</li>
              <li>Budget and resource planning</li>
              <li>Strategic initiative prioritization</li>
            </ul>
          </li>
          <li><strong>Risk-Based Prioritization:</strong>
            <ul>
              <li>Threat likelihood assessment</li>
              <li>Impact and consequence evaluation</li>
              <li>Risk tolerance alignment</li>
              <li>Cost-benefit analysis</li>
            </ul>
          </li>
          <li><strong>Capability Gap Analysis:</strong>
            <ul>
              <li>Current capability assessment</li>
              <li>Threat requirement mapping</li>
              <li>Gap identification and prioritization</li>
              <li>Development roadmap planning</li>
            </ul>
          </li>
        </ul>
        
        <h3>Resource Allocation Strategies</h3>
        <ul>
          <li><strong>Personnel and Skills:</strong>
            <ul>
              <li>Analyst skill requirement assessment</li>
              <li>Training and development priorities</li>
              <li>Staffing level optimization</li>
              <li>Expertise and specialization planning</li>
            </ul>
          </li>
          <li><strong>Technology and Tools:</strong>
            <ul>
              <li>Technology investment priorities</li>
              <li>Tool effectiveness evaluation</li>
              <li>Integration and interoperability</li>
              <li>Scalability and performance planning</li>
            </ul>
          </li>
          <li><strong>Partnerships and Collaboration:</strong>
            <ul>
              <li>Information sharing partnerships</li>
              <li>Vendor and service provider selection</li>
              <li>Community engagement strategies</li>
              <li>Government and law enforcement coordination</li>
            </ul>
          </li>
        </ul>
        
        <h3>Operational Intelligence Products</h3>
        <ul>
          <li><strong>Campaign Reports:</strong>
            <ul>
              <li>Comprehensive campaign analysis</li>
              <li>Timeline and phase documentation</li>
              <li>Impact and attribution assessment</li>
              <li>Lessons learned and recommendations</li>
            </ul>
          </li>
          <li><strong>Threat Landscape Assessments:</strong>
            <ul>
              <li>Sector and industry threat analysis</li>
              <li>Regional threat environment evaluation</li>
              <li>Emerging threat identification</li>
              <li>Trend analysis and forecasting</li>
            </ul>
          </li>
          <li><strong>Operational Recommendations:</strong>
            <ul>
              <li>Security program improvements</li>
              <li>Technology and capability investments</li>
              <li>Training and development priorities</li>
              <li>Partnership and collaboration opportunities</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary time horizon for operational threat intelligence?",
            options: [
              "Hours to days",
              "Weeks to months",
              "Months to years",
              "Years to decades"
            ],
            correctAnswer: 1,
            explanation: "Operational threat intelligence operates on a medium-term time horizon of weeks to months, bridging tactical and strategic intelligence for operational planning and decision-making."
          },
          {
            question: "Which activity is central to campaign analysis?",
            options: [
              "Individual IOC analysis",
              "Multi-stage operation tracking and timeline construction",
              "Single incident investigation",
              "Tool signature analysis"
            ],
            correctAnswer: 1,
            explanation: "Campaign analysis centers on tracking multi-stage operations and constructing timelines to understand the full scope and evolution of threat actor activities over time."
          },
          {
            question: "What is the primary purpose of operational intelligence in security program management?",
            options: [
              "Immediate incident response",
              "Long-term strategic planning",
              "Medium-term planning and resource allocation",
              "Real-time alerting"
            ],
            correctAnswer: 2,
            explanation: "Operational intelligence primarily supports medium-term planning and resource allocation by providing threat-informed insights for security program management and operational decision-making."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
