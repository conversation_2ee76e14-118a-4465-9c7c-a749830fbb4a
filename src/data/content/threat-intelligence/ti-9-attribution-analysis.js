/**
 * Attribution Analysis Module
 */

export const attributionAnalysisContent = {
  id: "ti-9",
  pathId: "threat-intelligence",
  title: "Attribution Analysis",
  description: "Master the complex process of threat actor attribution, including technical analysis, behavioral profiling, and confidence assessment for identifying threat actors behind cyber attacks.",
  objectives: [
    "Understand attribution fundamentals and challenges",
    "Learn technical attribution analysis techniques",
    "Master behavioral and operational pattern analysis",
    "Develop confidence assessment and uncertainty quantification",
    "Learn attribution frameworks and methodologies",
    "Create comprehensive attribution assessments"
  ],
  difficulty: "Advanced",
  estimatedTime: 130,
  sections: [
    {
      title: "Attribution Fundamentals",
      content: `
        <h2>Threat Actor Attribution Fundamentals</h2>
        <p>Attribution is the process of identifying who is responsible for a cyber attack, involving complex analysis of technical, behavioral, and contextual evidence.</p>
        
        <h3>Attribution Challenges</h3>
        <ul>
          <li><strong>Technical Challenges:</strong>
            <ul>
              <li>Anonymization and obfuscation techniques</li>
              <li>False flag operations and misdirection</li>
              <li>Shared tools and infrastructure</li>
              <li>Limited technical evidence availability</li>
            </ul>
          </li>
          <li><strong>Operational Challenges:</strong>
            <ul>
              <li>Time pressure for rapid attribution</li>
              <li>Incomplete or contaminated evidence</li>
              <li>Multiple potential suspects</li>
              <li>Evolving attack techniques</li>
            </ul>
          </li>
          <li><strong>Political and Legal Challenges:</strong>
            <ul>
              <li>Geopolitical implications of attribution</li>
              <li>Legal standards of evidence</li>
              <li>Public vs. private attribution standards</li>
              <li>International cooperation requirements</li>
            </ul>
          </li>
        </ul>
        
        <h3>Attribution Levels and Types</h3>
        <ul>
          <li><strong>Technical Attribution:</strong>
            <ul>
              <li>Specific individual or group identification</li>
              <li>Based on technical evidence and patterns</li>
              <li>High confidence requirements</li>
              <li>Often limited by operational security</li>
            </ul>
          </li>
          <li><strong>Tactical Attribution:</strong>
            <ul>
              <li>Campaign or operation-level attribution</li>
              <li>Behavioral pattern matching</li>
              <li>Medium to high confidence possible</li>
              <li>Useful for defensive planning</li>
            </ul>
          </li>
          <li><strong>Strategic Attribution:</strong>
            <ul>
              <li>Nation-state or organization-level</li>
              <li>Geopolitical context and motivation</li>
              <li>Lower confidence but broader implications</li>
              <li>Policy and diplomatic considerations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Attribution Evidence Types</h3>
        <ul>
          <li><strong>Technical Evidence:</strong>
            <ul>
              <li>Malware code similarities and signatures</li>
              <li>Infrastructure overlaps and connections</li>
              <li>Operational security mistakes</li>
              <li>Compilation timestamps and metadata</li>
            </ul>
          </li>
          <li><strong>Behavioral Evidence:</strong>
            <ul>
              <li>Attack timing and patterns</li>
              <li>Target selection and preferences</li>
              <li>Operational procedures and methods</li>
              <li>Communication and language patterns</li>
            </ul>
          </li>
          <li><strong>Contextual Evidence:</strong>
            <ul>
              <li>Geopolitical motivations and timing</li>
              <li>Economic and strategic interests</li>
              <li>Historical precedents and patterns</li>
              <li>Capability and resource requirements</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Technical Attribution Analysis",
      content: `
        <h2>Technical Attribution Analysis Techniques</h2>
        <p>Technical attribution relies on detailed analysis of malware, infrastructure, and operational artifacts to identify unique signatures and patterns.</p>
        
        <h3>Malware Analysis for Attribution</h3>
        <ul>
          <li><strong>Code Analysis:</strong>
            <ul>
              <li>Source code similarities and reuse</li>
              <li>Programming language and style analysis</li>
              <li>Compiler and development environment indicators</li>
              <li>Code quality and sophistication assessment</li>
            </ul>
          </li>
          <li><strong>Binary Analysis:</strong>
            <ul>
              <li>Compilation timestamps and metadata</li>
              <li>Packer and obfuscation techniques</li>
              <li>String analysis and language indicators</li>
              <li>Resource and version information</li>
            </ul>
          </li>
          <li><strong>Behavioral Analysis:</strong>
            <ul>
              <li>Execution patterns and sequences</li>
              <li>System interaction methods</li>
              <li>Network communication protocols</li>
              <li>Persistence and evasion techniques</li>
            </ul>
          </li>
        </ul>
        
        <h3>Infrastructure Analysis</h3>
        <ul>
          <li><strong>Domain and IP Analysis:</strong>
            <ul>
              <li>Registration patterns and information</li>
              <li>Hosting provider preferences</li>
              <li>DNS configuration patterns</li>
              <li>Geographic and temporal clustering</li>
            </ul>
          </li>
          <li><strong>Certificate Analysis:</strong>
            <ul>
              <li>SSL certificate patterns and reuse</li>
              <li>Certificate authority preferences</li>
              <li>Subject and issuer information</li>
              <li>Validity periods and renewal patterns</li>
            </ul>
          </li>
          <li><strong>Network Infrastructure:</strong>
            <ul>
              <li>Command and control architecture</li>
              <li>Communication protocols and encryption</li>
              <li>Proxy and anonymization usage</li>
              <li>Infrastructure lifecycle management</li>
            </ul>
          </li>
        </ul>
        
        <h3>Operational Security Analysis</h3>
        <ul>
          <li><strong>OPSEC Failures:</strong>
            <ul>
              <li>Personal information disclosure</li>
              <li>Account and identity reuse</li>
              <li>Timezone and language indicators</li>
              <li>Development and testing artifacts</li>
            </ul>
          </li>
          <li><strong>Tool and Technique Analysis:</strong>
            <ul>
              <li>Preferred tools and frameworks</li>
              <li>Custom tool development patterns</li>
              <li>Technique implementation variations</li>
              <li>Operational procedure consistency</li>
            </ul>
          </li>
          <li><strong>Communication Analysis:</strong>
            <ul>
              <li>Language and writing style analysis</li>
              <li>Communication timing patterns</li>
              <li>Platform and service preferences</li>
              <li>Social engineering approaches</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Attribution Frameworks and Assessment",
      content: `
        <h2>Attribution Frameworks and Confidence Assessment</h2>
        <p>Systematic attribution frameworks and confidence assessment methodologies ensure rigorous and defensible attribution conclusions.</p>
        
        <h3>Attribution Frameworks</h3>
        <ul>
          <li><strong>Q Model (Quadrant Model):</strong>
            <ul>
              <li>Four attribution levels: None, Possible, Probable, Certain</li>
              <li>Evidence quality and quantity assessment</li>
              <li>Confidence level mapping</li>
              <li>Decision-making threshold guidance</li>
            </ul>
          </li>
          <li><strong>MITRE Attribution Framework:</strong>
            <ul>
              <li>Structured attribution methodology</li>
              <li>Evidence collection and analysis</li>
              <li>Hypothesis development and testing</li>
              <li>Confidence assessment and reporting</li>
            </ul>
          </li>
          <li><strong>Diamond Model Attribution:</strong>
            <ul>
              <li>Adversary-Infrastructure-Capability-Victim analysis</li>
              <li>Meta-feature attribution indicators</li>
              <li>Confidence scoring methodology</li>
              <li>Activity threading and clustering</li>
            </ul>
          </li>
        </ul>
        
        <h3>Confidence Assessment Methods</h3>
        <ul>
          <li><strong>Evidence Weighting:</strong>
            <ul>
              <li>Technical evidence reliability scoring</li>
              <li>Source credibility assessment</li>
              <li>Corroboration and validation</li>
              <li>Alternative explanation consideration</li>
            </ul>
          </li>
          <li><strong>Analytical Confidence:</strong>
            <ul>
              <li>Analyst expertise and experience</li>
              <li>Methodology rigor and completeness</li>
              <li>Peer review and validation</li>
              <li>Time and resource constraints</li>
            </ul>
          </li>
          <li><strong>Uncertainty Quantification:</strong>
            <ul>
              <li>Probabilistic assessment methods</li>
              <li>Confidence interval estimation</li>
              <li>Sensitivity analysis</li>
              <li>Alternative scenario probability</li>
            </ul>
          </li>
        </ul>
        
        <h3>Attribution Reporting and Communication</h3>
        <ul>
          <li><strong>Attribution Products:</strong>
            <ul>
              <li>Technical attribution reports</li>
              <li>Executive attribution briefings</li>
              <li>Public attribution statements</li>
              <li>Legal and policy documentation</li>
            </ul>
          </li>
          <li><strong>Confidence Communication:</strong>
            <ul>
              <li>Clear confidence level expression</li>
              <li>Uncertainty and limitation disclosure</li>
              <li>Alternative scenario presentation</li>
              <li>Evidence quality assessment</li>
            </ul>
          </li>
          <li><strong>Stakeholder Considerations:</strong>
            <ul>
              <li>Audience-appropriate detail level</li>
              <li>Policy and operational implications</li>
              <li>Legal and diplomatic considerations</li>
              <li>Public and media communication</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which type of attribution focuses on nation-state or organization-level identification?",
            options: [
              "Technical attribution",
              "Tactical attribution",
              "Strategic attribution",
              "Operational attribution"
            ],
            correctAnswer: 2,
            explanation: "Strategic attribution focuses on nation-state or organization-level identification, considering geopolitical context and motivation, though often with lower confidence levels."
          },
          {
            question: "What is a primary challenge in technical attribution analysis?",
            options: [
              "Lack of analysis tools",
              "False flag operations and misdirection",
              "Insufficient computing power",
              "Limited internet access"
            ],
            correctAnswer: 1,
            explanation: "False flag operations and misdirection are primary challenges in attribution, as attackers may deliberately plant evidence to implicate other actors or hide their true identity."
          },
          {
            question: "In the Q Model attribution framework, what does 'Probable' attribution level indicate?",
            options: [
              "No evidence of attribution",
              "Some evidence but significant uncertainty",
              "Strong evidence with high confidence",
              "Absolute certainty of attribution"
            ],
            correctAnswer: 2,
            explanation: "In the Q Model, 'Probable' indicates strong evidence with high confidence, representing a high likelihood of correct attribution but not absolute certainty."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
