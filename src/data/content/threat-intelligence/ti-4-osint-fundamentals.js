/**
 * OSINT Fundamentals Module
 */

export const osintFundamentalsContent = {
  id: "ti-4",
  pathId: "threat-intelligence",
  title: "OSINT Fundamentals",
  description: "Master Open Source Intelligence (OSINT) collection techniques, tools, and methodologies for gathering actionable threat intelligence from publicly available sources.",
  objectives: [
    "Understand OSINT principles and legal considerations",
    "Learn systematic OSINT collection methodologies",
    "Master essential OSINT tools and platforms",
    "Develop source evaluation and validation techniques",
    "Learn to protect operational security during OSINT activities",
    "Create comprehensive OSINT collection plans"
  ],
  difficulty: "Beginner",
  estimatedTime: 120,
  sections: [
    {
      title: "OSINT Principles and Framework",
      content: `
        <h2>Open Source Intelligence (OSINT) Fundamentals</h2>
        <p>OSINT is the collection and analysis of information gathered from publicly available sources to produce actionable intelligence for decision-making.</p>
        
        <h3>OSINT Definition and Scope</h3>
        <ul>
          <li><strong>Open Source Information:</strong>
            <ul>
              <li>Publicly available information accessible to anyone</li>
              <li>No special access or authorization required</li>
              <li>Includes internet, media, academic, and commercial sources</li>
              <li>Legal and ethical collection methods</li>
            </ul>
          </li>
          <li><strong>OSINT vs. Other Intelligence Disciplines:</strong>
            <ul>
              <li>HUMINT (Human Intelligence) - Human sources</li>
              <li>SIGINT (Signals Intelligence) - Electronic communications</li>
              <li>GEOINT (Geospatial Intelligence) - Geographic information</li>
              <li>MASINT (Measurement and Signature Intelligence) - Technical data</li>
            </ul>
          </li>
          <li><strong>OSINT Categories:</strong>
            <ul>
              <li>Internet and social media intelligence</li>
              <li>Traditional media and publications</li>
              <li>Academic and research publications</li>
              <li>Government and public records</li>
              <li>Commercial databases and services</li>
            </ul>
          </li>
        </ul>
        
        <h3>OSINT Collection Framework</h3>
        <ul>
          <li><strong>Planning and Direction:</strong>
            <ul>
              <li>Define intelligence requirements</li>
              <li>Identify potential sources and methods</li>
              <li>Develop collection strategy</li>
              <li>Establish operational security measures</li>
            </ul>
          </li>
          <li><strong>Collection:</strong>
            <ul>
              <li>Systematic source monitoring</li>
              <li>Targeted information gathering</li>
              <li>Automated collection tools</li>
              <li>Manual research and analysis</li>
            </ul>
          </li>
          <li><strong>Processing and Exploitation:</strong>
            <ul>
              <li>Data normalization and formatting</li>
              <li>Information extraction and parsing</li>
              <li>Correlation and cross-referencing</li>
              <li>Quality assessment and validation</li>
            </ul>
          </li>
          <li><strong>Analysis and Production:</strong>
            <ul>
              <li>Pattern recognition and trend analysis</li>
              <li>Threat assessment and risk evaluation</li>
              <li>Intelligence product development</li>
              <li>Confidence and reliability ratings</li>
            </ul>
          </li>
        </ul>
        
        <h3>Legal and Ethical Considerations</h3>
        <ul>
          <li><strong>Legal Compliance:</strong>
            <ul>
              <li>Respect for privacy laws and regulations</li>
              <li>Terms of service compliance</li>
              <li>Copyright and intellectual property rights</li>
              <li>Jurisdictional considerations</li>
            </ul>
          </li>
          <li><strong>Ethical Guidelines:</strong>
            <ul>
              <li>Minimize harm to individuals and organizations</li>
              <li>Respect for human rights and dignity</li>
              <li>Responsible disclosure of sensitive information</li>
              <li>Professional conduct and integrity</li>
            </ul>
          </li>
          <li><strong>Operational Security:</strong>
            <ul>
              <li>Protect analyst identity and organization</li>
              <li>Secure collection methods and tools</li>
              <li>Information handling and storage</li>
              <li>Communication security</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "OSINT Sources and Collection Methods",
      content: `
        <h2>OSINT Sources and Collection Methods</h2>
        <p>Effective OSINT requires understanding diverse information sources and employing systematic collection methods to gather relevant intelligence.</p>
        
        <h3>Primary OSINT Sources</h3>
        <ul>
          <li><strong>Search Engines and Web Resources:</strong>
            <ul>
              <li>Google, Bing, DuckDuckGo advanced search techniques</li>
              <li>Specialized search engines (Shodan, Censys, ZoomEye)</li>
              <li>Web archives and cached content (Wayback Machine)</li>
              <li>Code repositories (GitHub, GitLab, Bitbucket)</li>
            </ul>
          </li>
          <li><strong>Social Media Platforms:</strong>
            <ul>
              <li>Twitter/X, Facebook, LinkedIn, Instagram</li>
              <li>Professional networks and forums</li>
              <li>Messaging platforms and chat services</li>
              <li>Video platforms (YouTube, TikTok, Vimeo)</li>
            </ul>
          </li>
          <li><strong>Technical Infrastructure:</strong>
            <ul>
              <li>Domain and IP address information (WHOIS)</li>
              <li>DNS records and subdomain enumeration</li>
              <li>Certificate transparency logs</li>
              <li>Network scanning and service identification</li>
            </ul>
          </li>
          <li><strong>Dark Web and Underground Sources:</strong>
            <ul>
              <li>Tor hidden services and marketplaces</li>
              <li>Cybercriminal forums and communities</li>
              <li>Leaked data and breach databases</li>
              <li>Ransomware group communications</li>
            </ul>
          </li>
        </ul>
        
        <h3>Collection Methodologies</h3>
        <ul>
          <li><strong>Passive Collection:</strong>
            <ul>
              <li>Monitoring and observation without interaction</li>
              <li>Automated feeds and alerts</li>
              <li>Public data aggregation</li>
              <li>Minimal operational footprint</li>
            </ul>
          </li>
          <li><strong>Active Collection:</strong>
            <ul>
              <li>Direct interaction with sources</li>
              <li>Targeted queries and searches</li>
              <li>Social engineering and elicitation</li>
              <li>Higher risk of detection</li>
            </ul>
          </li>
          <li><strong>Automated Collection:</strong>
            <ul>
              <li>Web scraping and crawling tools</li>
              <li>API-based data collection</li>
              <li>RSS feeds and webhooks</li>
              <li>Scheduled monitoring systems</li>
            </ul>
          </li>
          <li><strong>Manual Collection:</strong>
            <ul>
              <li>Human analysis and interpretation</li>
              <li>Contextual understanding</li>
              <li>Quality assessment and validation</li>
              <li>Creative problem-solving</li>
            </ul>
          </li>
        </ul>
        
        <h3>Source Evaluation and Validation</h3>
        <ul>
          <li><strong>Source Credibility Assessment:</strong>
            <ul>
              <li>Author expertise and reputation</li>
              <li>Publication history and track record</li>
              <li>Potential bias and motivations</li>
              <li>Verification through multiple sources</li>
            </ul>
          </li>
          <li><strong>Information Reliability:</strong>
            <ul>
              <li>Accuracy and factual correctness</li>
              <li>Timeliness and currency</li>
              <li>Completeness and context</li>
              <li>Consistency with other sources</li>
            </ul>
          </li>
          <li><strong>Confidence Levels:</strong>
            <ul>
              <li>High confidence - Multiple reliable sources</li>
              <li>Medium confidence - Limited corroboration</li>
              <li>Low confidence - Single or questionable source</li>
              <li>No confidence - Unverified or contradictory</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "OSINT Tools and Techniques",
      content: `
        <h2>Essential OSINT Tools and Techniques</h2>
        <p>Mastering OSINT requires proficiency with various tools and techniques for efficient collection, analysis, and management of open source intelligence.</p>
        
        <h3>Search and Discovery Tools</h3>
        <ul>
          <li><strong>Advanced Search Techniques:</strong>
            <ul>
              <li>Google dorking and advanced operators</li>
              <li>Boolean search logic and syntax</li>
              <li>Date range and file type filtering</li>
              <li>Site-specific and domain searches</li>
            </ul>
          </li>
          <li><strong>Specialized Search Engines:</strong>
            <ul>
              <li>Shodan - Internet-connected device search</li>
              <li>Censys - Internet-wide scanning data</li>
              <li>ZoomEye - Cyberspace search engine</li>
              <li>Binary Edge - Internet scanning platform</li>
            </ul>
          </li>
          <li><strong>Social Media Intelligence:</strong>
            <ul>
              <li>TweetDeck and Twitter advanced search</li>
              <li>Facebook Graph Search techniques</li>
              <li>LinkedIn intelligence gathering</li>
              <li>Instagram and visual content analysis</li>
            </ul>
          </li>
        </ul>
        
        <h3>Technical Analysis Tools</h3>
        <ul>
          <li><strong>Domain and Network Analysis:</strong>
            <ul>
              <li>WHOIS databases and historical records</li>
              <li>DNS enumeration tools (dnsrecon, fierce)</li>
              <li>Subdomain discovery (Sublist3r, Amass)</li>
              <li>Certificate transparency monitoring</li>
            </ul>
          </li>
          <li><strong>Web Application Analysis:</strong>
            <ul>
              <li>Wayback Machine and web archives</li>
              <li>Website technology identification (Wappalyzer)</li>
              <li>Directory and file enumeration</li>
              <li>Metadata extraction and analysis</li>
            </ul>
          </li>
          <li><strong>Email and Communication:</strong>
            <ul>
              <li>Email header analysis</li>
              <li>Email verification and validation</li>
              <li>Communication pattern analysis</li>
              <li>Messaging platform intelligence</li>
            </ul>
          </li>
        </ul>
        
        <h3>OSINT Frameworks and Platforms</h3>
        <ul>
          <li><strong>Maltego:</strong>
            <ul>
              <li>Visual link analysis and data mining</li>
              <li>Entity relationship mapping</li>
              <li>Transform-based data collection</li>
              <li>Collaborative investigation platform</li>
            </ul>
          </li>
          <li><strong>OSINT Framework:</strong>
            <ul>
              <li>Comprehensive tool directory</li>
              <li>Categorized resource collection</li>
              <li>Regular updates and maintenance</li>
              <li>Community-driven development</li>
            </ul>
          </li>
          <li><strong>Recon-ng:</strong>
            <ul>
              <li>Modular reconnaissance framework</li>
              <li>Automated data collection modules</li>
              <li>Database integration and management</li>
              <li>Extensible plugin architecture</li>
            </ul>
          </li>
          <li><strong>theHarvester:</strong>
            <ul>
              <li>Email and subdomain harvesting</li>
              <li>Multiple search engine integration</li>
              <li>Passive information gathering</li>
              <li>Output formatting and reporting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Operational Security for OSINT</h3>
        <ul>
          <li><strong>Anonymity and Privacy:</strong>
            <ul>
              <li>VPN and proxy usage</li>
              <li>Tor browser and onion routing</li>
              <li>Burner accounts and personas</li>
              <li>Device and browser fingerprinting</li>
            </ul>
          </li>
          <li><strong>Data Protection:</strong>
            <ul>
              <li>Encrypted storage and communication</li>
              <li>Secure data handling procedures</li>
              <li>Information classification and marking</li>
              <li>Access control and audit trails</li>
            </ul>
          </li>
          <li><strong>Operational Discipline:</strong>
            <ul>
              <li>Compartmentalization of activities</li>
              <li>Regular security assessments</li>
              <li>Incident response procedures</li>
              <li>Continuous security awareness</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary characteristic that distinguishes OSINT from other intelligence disciplines?",
            options: [
              "It requires special authorization to access",
              "It uses only automated collection methods",
              "It relies on publicly available information",
              "It focuses only on technical data"
            ],
            correctAnswer: 2,
            explanation: "OSINT is distinguished by its reliance on publicly available information that can be accessed without special authorization, unlike other intelligence disciplines that may require classified access or special collection methods."
          },
          {
            question: "Which confidence level should be assigned to information corroborated by multiple reliable sources?",
            options: [
              "No confidence",
              "Low confidence", 
              "Medium confidence",
              "High confidence"
            ],
            correctAnswer: 3,
            explanation: "High confidence should be assigned to information that has been corroborated by multiple reliable sources, as this provides the strongest validation of accuracy and reliability."
          },
          {
            question: "What is the primary purpose of using operational security measures during OSINT collection?",
            options: [
              "To increase collection speed",
              "To protect analyst identity and organization",
              "To improve data quality",
              "To reduce collection costs"
            ],
            correctAnswer: 1,
            explanation: "Operational security measures are primarily used to protect the analyst's identity and organization from detection, which could compromise ongoing operations or put personnel at risk."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
