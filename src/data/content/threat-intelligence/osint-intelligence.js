/**
 * OSINT Intelligence Module
 */

export const osintIntelligenceContent = {
  title: 'OSINT Intelligence',
  description: 'Open Source Intelligence (OSINT) techniques for gathering, analyzing, and leveraging publicly available information.',
  concepts: [
    'OSINT fundamentals',
    'Data collection and validation',
    'Social media intelligence',
    'Geolocation and metadata analysis',
    'Threat actor profiling'
  ],
  labs: [
    {
      title: 'Social Media OSINT Lab',
      description: 'Gather and analyze intelligence from social media platforms',
      difficulty: 'Intermediate',
      duration: '1.5 hours',
      objectives: [
        'Identify target profiles',
        'Collect and analyze posts and connections',
        'Correlate findings with other sources',
        'Document intelligence reports'
      ],
      tools: ['Maltego', 'SpiderFoot', 'Social-Searcher'],
      prerequisites: ['Basic OSINT knowledge', 'Familiarity with social media platforms']
    },
    {
      title: 'Metadata and Geolocation Analysis',
      description: 'Extract and analyze metadata from files and images',
      difficulty: 'Advanced',
      duration: '2 hours',
      objectives: [
        'Extract metadata from various file types',
        'Analyze geolocation data',
        'Correlate with open source maps',
        'Report findings'
      ],
      tools: ['ExifTool', 'Google Earth', 'OpenStreetMap'],
      prerequisites: ['Metadata basics', 'Geolocation concepts']
    }
  ],
  useCases: [
    {
      title: 'Threat Actor Profiling',
      description: 'Profile threat actors using open source data',
      scenario: 'Gather and correlate information from multiple sources',
      mitreTactics: ['Reconnaissance', 'Resource Development'],
      tools: ['OSINT Tools', 'Social Media Analysis'],
      steps: [
        'Identify threat actor aliases',
        'Collect data from forums and social media',
        'Correlate with technical indicators',
        'Build threat profiles'
      ]
    },
    {
      title: 'Infrastructure Mapping',
      description: 'Map adversary infrastructure using OSINT',
      scenario: 'Identify domains, IPs, and hosting providers',
      mitreTactics: ['Reconnaissance'],
      tools: ['Domain Tools', 'IP Lookup', 'WHOIS'],
      steps: [
        'Collect domain and IP data',
        'Analyze hosting and registration details',
        'Correlate with threat intelligence feeds',
        'Document infrastructure maps'
      ]
    }
  ],
  mitreMapping: [
    {
      tactic: 'Reconnaissance',
      techniques: [
        {
          name: 'Gather Victim Identity Information',
          description: 'Collect information about targets from public sources',
          detection: 'Monitor for data collection activities on public platforms'
        },
        {
          name: 'Gather Victim Network Information',
          description: 'Identify network infrastructure using OSINT',
          detection: 'Track domain and IP lookups'
        }
      ]
    },
    {
      tactic: 'Resource Development',
      techniques: [
        {
          name: 'Establish Accounts',
          description: 'Create and manage accounts for intelligence gathering',
          detection: 'Monitor for new account creation and activity'
        }
      ]
    }
  ],
  tools: [
    {
      name: 'OSINT Frameworks',
      description: 'Comprehensive OSINT toolkits and frameworks',
      useCases: ['Data collection', 'Analysis', 'Reporting'],
      examples: ['Maltego', 'SpiderFoot', 'theHarvester']
    },
    {
      name: 'Metadata Analysis Tools',
      description: 'Tools for extracting and analyzing metadata',
      useCases: ['Geolocation', 'Attribution', 'File analysis'],
      examples: ['ExifTool', 'Google Earth', 'OpenStreetMap']
    }
  ],
  prerequisites: [
    'Understanding of OSINT concepts',
    'Familiarity with social media and public data',
    'Basic analysis skills',
    'Awareness of privacy and legal considerations'
  ],
  resources: [
    {
      type: 'Guide',
      title: 'OSINT Techniques and Tools',
      url: 'https://example.com/osint-guide'
    },
    {
      type: 'Toolkit',
      title: 'OSINT Frameworks and Resources',
      url: 'https://example.com/osint-toolkit'
    }
  ]
}; 