/**
 * TTP Analysis Module
 */

export const ttpAnalysisContent = {
  id: "ti-8",
  pathId: "threat-intelligence",
  title: "TTP Analysis",
  description: "Master the analysis of Tactics, Techniques, and Procedures (TTPs) used by threat actors to understand attack methodologies and develop effective countermeasures.",
  objectives: [
    "Understand TTP frameworks and classification systems",
    "Learn TTP extraction and mapping techniques",
    "Master behavioral analysis and pattern recognition",
    "Develop TTP-based threat hunting capabilities",
    "Learn defensive mapping and countermeasure development",
    "Create comprehensive TTP intelligence products"
  ],
  difficulty: "Intermediate",
  estimatedTime: 120,
  sections: [
    {
      title: "TTP Framework and Classification",
      content: `
        <h2>Tactics, Techniques, and Procedures (TTP) Analysis</h2>
        <p>TTPs represent the behavior patterns and methodologies used by threat actors, providing deeper insights into adversary capabilities and intentions than simple IOCs.</p>
        
        <h3>TTP Definition and Components</h3>
        <ul>
          <li><strong>Tactics:</strong>
            <ul>
              <li>High-level goals and objectives</li>
              <li>What the adversary is trying to achieve</li>
              <li>Strategic intent and mission</li>
              <li>Examples: Initial Access, Persistence, Privilege Escalation</li>
            </ul>
          </li>
          <li><strong>Techniques:</strong>
            <ul>
              <li>Specific methods to achieve tactical goals</li>
              <li>How the adversary accomplishes objectives</li>
              <li>Technical implementation approaches</li>
              <li>Examples: Spearphishing, DLL Hijacking, Credential Dumping</li>
            </ul>
          </li>
          <li><strong>Procedures:</strong>
            <ul>
              <li>Detailed implementation steps</li>
              <li>Specific tools and configurations used</li>
              <li>Operational patterns and preferences</li>
              <li>Examples: Specific malware variants, command sequences</li>
            </ul>
          </li>
        </ul>
        
        <h3>MITRE ATT&CK Framework</h3>
        <ul>
          <li><strong>Framework Structure:</strong>
            <ul>
              <li>14 tactical categories (Enterprise matrix)</li>
              <li>200+ techniques and sub-techniques</li>
              <li>Procedure examples and references</li>
              <li>Mitigation and detection guidance</li>
            </ul>
          </li>
          <li><strong>ATT&CK Tactics:</strong>
            <ul>
              <li>Initial Access - Entry point establishment</li>
              <li>Execution - Code execution on systems</li>
              <li>Persistence - Maintaining foothold</li>
              <li>Privilege Escalation - Higher-level permissions</li>
              <li>Defense Evasion - Avoiding detection</li>
              <li>Credential Access - Account and password theft</li>
              <li>Discovery - System and network reconnaissance</li>
              <li>Lateral Movement - Network expansion</li>
              <li>Collection - Data gathering</li>
              <li>Command and Control - Communication channels</li>
              <li>Exfiltration - Data theft</li>
              <li>Impact - Disruption and destruction</li>
            </ul>
          </li>
          <li><strong>Matrix Applications:</strong>
            <ul>
              <li>Threat actor profiling and comparison</li>
              <li>Campaign analysis and tracking</li>
              <li>Gap analysis and coverage assessment</li>
              <li>Training and education programs</li>
            </ul>
          </li>
        </ul>
        
        <h3>Alternative TTP Frameworks</h3>
        <ul>
          <li><strong>Cyber Kill Chain:</strong>
            <ul>
              <li>Linear attack progression model</li>
              <li>Seven-stage attack lifecycle</li>
              <li>Defensive strategy alignment</li>
              <li>Campaign phase identification</li>
            </ul>
          </li>
          <li><strong>Diamond Model:</strong>
            <ul>
              <li>Four-element relationship model</li>
              <li>Adversary, Infrastructure, Capability, Victim</li>
              <li>Meta-features and confidence assessment</li>
              <li>Activity threading and grouping</li>
            </ul>
          </li>
          <li><strong>VERIS Framework:</strong>
            <ul>
              <li>Vocabulary for Event Recording and Incident Sharing</li>
              <li>Structured incident classification</li>
              <li>Statistical analysis and metrics</li>
              <li>Industry benchmarking and comparison</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "TTP Extraction and Mapping",
      content: `
        <h2>TTP Extraction and Mapping Techniques</h2>
        <p>Extracting and mapping TTPs from various sources enables comprehensive understanding of threat actor behavior and attack patterns.</p>
        
        <h3>TTP Extraction Sources</h3>
        <ul>
          <li><strong>Incident Response Data:</strong>
            <ul>
              <li>Forensic investigation findings</li>
              <li>Attack timeline reconstruction</li>
              <li>Tool and technique identification</li>
              <li>Behavioral pattern analysis</li>
            </ul>
          </li>
          <li><strong>Malware Analysis:</strong>
            <ul>
              <li>Static and dynamic analysis results</li>
              <li>Capability and functionality mapping</li>
              <li>Behavioral characteristic identification</li>
              <li>Code similarity and family clustering</li>
            </ul>
          </li>
          <li><strong>Threat Research Reports:</strong>
            <ul>
              <li>Vendor and researcher publications</li>
              <li>Campaign analysis and attribution</li>
              <li>Technical deep-dive investigations</li>
              <li>Community knowledge sharing</li>
            </ul>
          </li>
          <li><strong>Open Source Intelligence:</strong>
            <ul>
              <li>Public disclosure and reporting</li>
              <li>Social media and forum discussions</li>
              <li>Academic and conference presentations</li>
              <li>Government and industry advisories</li>
            </ul>
          </li>
        </ul>
        
        <h3>TTP Mapping Methodologies</h3>
        <ul>
          <li><strong>Manual Mapping Process:</strong>
            <ul>
              <li>Expert analysis and interpretation</li>
              <li>Framework alignment and classification</li>
              <li>Context and nuance preservation</li>
              <li>Quality assurance and validation</li>
            </ul>
          </li>
          <li><strong>Automated Mapping Tools:</strong>
            <ul>
              <li>Natural language processing</li>
              <li>Machine learning classification</li>
              <li>Pattern recognition algorithms</li>
              <li>Confidence scoring and ranking</li>
            </ul>
          </li>
          <li><strong>Hybrid Approaches:</strong>
            <ul>
              <li>Automated initial mapping</li>
              <li>Human expert validation</li>
              <li>Iterative refinement process</li>
              <li>Continuous improvement feedback</li>
            </ul>
          </li>
        </ul>
        
        <h3>TTP Relationship Analysis</h3>
        <ul>
          <li><strong>Technique Clustering:</strong>
            <ul>
              <li>Similar technique grouping</li>
              <li>Functional relationship identification</li>
              <li>Alternative implementation analysis</li>
              <li>Substitution and variation mapping</li>
            </ul>
          </li>
          <li><strong>Sequential Analysis:</strong>
            <ul>
              <li>Attack chain reconstruction</li>
              <li>Temporal relationship mapping</li>
              <li>Dependency identification</li>
              <li>Critical path analysis</li>
            </ul>
          </li>
          <li><strong>Actor Profiling:</strong>
            <ul>
              <li>Preferred technique identification</li>
              <li>Capability assessment</li>
              <li>Behavioral signature development</li>
              <li>Evolution and adaptation tracking</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "TTP-Based Defense and Hunting",
      content: `
        <h2>TTP-Based Defense and Threat Hunting</h2>
        <p>Leveraging TTP intelligence for defensive operations and threat hunting provides more robust and sustainable security capabilities than IOC-based approaches.</p>
        
        <h3>Defensive Mapping and Coverage</h3>
        <ul>
          <li><strong>Control Mapping:</strong>
            <ul>
              <li>Security control effectiveness assessment</li>
              <li>Coverage gap identification</li>
              <li>Mitigation strategy development</li>
              <li>Defense-in-depth optimization</li>
            </ul>
          </li>
          <li><strong>Detection Engineering:</strong>
            <ul>
              <li>Behavioral detection rule development</li>
              <li>Analytics and use case creation</li>
              <li>False positive minimization</li>
              <li>Detection maturity assessment</li>
            </ul>
          </li>
          <li><strong>Purple Team Operations:</strong>
            <ul>
              <li>Adversary emulation planning</li>
              <li>Detection validation testing</li>
              <li>Control effectiveness evaluation</li>
              <li>Continuous improvement cycles</li>
            </ul>
          </li>
        </ul>
        
        <h3>TTP-Based Threat Hunting</h3>
        <ul>
          <li><strong>Hypothesis Development:</strong>
            <ul>
              <li>TTP-driven hunting hypotheses</li>
              <li>Behavioral pattern assumptions</li>
              <li>Environmental context consideration</li>
              <li>Threat landscape alignment</li>
            </ul>
          </li>
          <li><strong>Hunting Methodologies:</strong>
            <ul>
              <li>Technique-specific hunting approaches</li>
              <li>Multi-technique campaign hunting</li>
              <li>Anomaly detection and analysis</li>
              <li>Timeline and correlation analysis</li>
            </ul>
          </li>
          <li><strong>Hunt Analytics:</strong>
            <ul>
              <li>Behavioral analytics development</li>
              <li>Statistical analysis and modeling</li>
              <li>Machine learning applications</li>
              <li>Continuous monitoring integration</li>
            </ul>
          </li>
        </ul>
        
        <h3>TTP Intelligence Products</h3>
        <ul>
          <li><strong>Actor Profiles:</strong>
            <ul>
              <li>Comprehensive TTP documentation</li>
              <li>Capability and sophistication assessment</li>
              <li>Targeting and motivation analysis</li>
              <li>Evolution and adaptation tracking</li>
            </ul>
          </li>
          <li><strong>Campaign Analysis:</strong>
            <ul>
              <li>Multi-stage attack documentation</li>
              <li>TTP progression and evolution</li>
              <li>Tool and infrastructure correlation</li>
              <li>Impact and attribution assessment</li>
            </ul>
          </li>
          <li><strong>Defensive Recommendations:</strong>
            <ul>
              <li>Prioritized mitigation strategies</li>
              <li>Detection and monitoring guidance</li>
              <li>Control implementation recommendations</li>
              <li>Risk assessment and prioritization</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "In the TTP framework, what do 'Tactics' represent?",
            options: [
              "Specific tools used by attackers",
              "High-level goals and objectives",
              "Detailed implementation steps",
              "Network infrastructure used"
            ],
            correctAnswer: 1,
            explanation: "Tactics represent the high-level goals and objectives that adversaries are trying to achieve, such as Initial Access, Persistence, or Privilege Escalation."
          },
          {
            question: "Which MITRE ATT&CK tactic focuses on maintaining access to compromised systems?",
            options: [
              "Initial Access",
              "Execution",
              "Persistence",
              "Discovery"
            ],
            correctAnswer: 2,
            explanation: "Persistence is the MITRE ATT&CK tactic that focuses on maintaining access to compromised systems, ensuring continued presence even after reboots or credential changes."
          },
          {
            question: "What is the primary advantage of TTP-based threat hunting over IOC-based hunting?",
            options: [
              "Faster detection speed",
              "Lower computational requirements",
              "More sustainable and harder for attackers to evade",
              "Easier to implement"
            ],
            correctAnswer: 2,
            explanation: "TTP-based hunting is more sustainable because behavioral patterns and methodologies are harder for attackers to change than specific indicators, providing longer-lasting detection capabilities."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
