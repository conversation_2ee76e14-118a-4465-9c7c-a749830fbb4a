/**
 * IOC and Malware Analysis Module
 */

export const iocMalwareContent = {
  id: "ti-ioc-malware",
  title: "IOC and Malware Analysis",
  description: "Learn to analyze indicators of compromise and malware for threat intelligence.",
  difficulty: "Advanced",
  estimatedTime: 120,
  objectives: [
    "Understand IOC types and sources",
    "Learn malware analysis techniques",
    "Master attribution methods",
    "Develop analytical workflows"
  ],
  sections: [
    {
      title: "IOC Analysis Fundamentals",
      content: `
        <h2>Indicators of Compromise (IOC) Analysis</h2>
        <p>Learn how to identify, analyze, and leverage IOCs for threat intelligence operations.</p>
        <h3>IOC Types</h3>
        <ul>
          <li>File hashes (MD5, SHA-1, SHA-256)</li>
          <li>IP addresses and domains</li>
          <li>URLs and file paths</li>
          <li>Registry keys and values</li>
          <li>Network signatures</li>
        </ul>
      `,
      type: "text"
    }
  ]
}; 