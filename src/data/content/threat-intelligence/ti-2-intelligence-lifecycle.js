/**
 * Intelligence Lifecycle Module
 */

export const intelligenceLifecycleContent = {
  id: "ti-2",
  pathId: "threat-intelligence",
  title: "Intelligence Lifecycle",
  description: "Master the intelligence lifecycle process from planning and collection to analysis and dissemination.",
  objectives: [
    "Understand the intelligence lifecycle phases",
    "Learn planning and direction techniques",
    "Master collection methodologies",
    "Develop analysis and production skills",
    "Learn dissemination best practices",
    "Understand feedback and evaluation processes"
  ],
  difficulty: "Intermediate",
  estimatedTime: 100,
  sections: [
    {
      title: "Intelligence Lifecycle Overview",
      content: `
        <h2>Intelligence Lifecycle Overview</h2>
        <p>The intelligence lifecycle is a systematic process for producing actionable intelligence from raw data and information.</p>
        
        <h3>Lifecycle Phases</h3>
        <ul>
          <li><strong>Planning and Direction:</strong> Define intelligence requirements and priorities</li>
          <li><strong>Collection:</strong> Gather raw data from various sources</li>
          <li><strong>Processing:</strong> Convert raw data into usable format</li>
          <li><strong>Analysis and Production:</strong> Analyze data and produce intelligence products</li>
          <li><strong>Dissemination:</strong> Distribute intelligence to stakeholders</li>
          <li><strong>Feedback and Evaluation:</strong> Assess effectiveness and refine process</li>
        </ul>
        
        <h3>Key Principles</h3>
        <ul>
          <li>Customer-driven requirements</li>
          <li>Continuous and iterative process</li>
          <li>Quality over quantity</li>
          <li>Timely and actionable output</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Planning and Direction",
      content: `
        <h2>Planning and Direction</h2>
        <p>The first phase involves defining intelligence requirements and establishing collection priorities.</p>
        
        <h3>Intelligence Requirements</h3>
        <ul>
          <li><strong>Strategic Requirements:</strong> Long-term organizational needs</li>
          <li><strong>Tactical Requirements:</strong> Immediate operational needs</li>
          <li><strong>Technical Requirements:</strong> Specific technical indicators</li>
          <li><strong>Warning Requirements:</strong> Early warning indicators</li>
        </ul>
        
        <h3>Prioritization Framework</h3>
        <ul>
          <li>Criticality to mission</li>
          <li>Time sensitivity</li>
          <li>Resource availability</li>
          <li>Collection feasibility</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "Which phase of the intelligence lifecycle involves defining intelligence requirements?",
            options: [
              "Collection",
              "Planning and Direction",
              "Analysis and Production",
              "Dissemination"
            ],
            correctAnswer: 1,
            explanation: "Planning and Direction is the first phase where intelligence requirements are defined and collection priorities are established."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
