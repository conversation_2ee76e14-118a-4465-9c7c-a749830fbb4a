/**
 * Threat Intelligence Learning Path
 * Comprehensive cyber threat intelligence analysis and operations
 */

// Import all module content
import { threatIntelIntroContent } from './threat-intel-intro.js';
import { intelligenceLifecycleContent } from './ti-2-intelligence-lifecycle.js';
import { threatLandscapeContent } from './ti-3-threat-landscape.js';
import { osintFundamentalsContent } from './ti-4-osint-fundamentals.js';
import { dataCollectionContent } from './ti-5-data-collection.js';
import { analysisMethodsContent } from './ti-6-analysis-methods.js';
import { iocManagementContent } from './ti-7-ioc-management.js';
import { ttpAnalysisContent } from './ti-8-ttp-analysis.js';
import { attributionAnalysisContent } from './ti-9-attribution-analysis.js';
import { intelligenceSharingContent } from './ti-10-intelligence-sharing.js';
import { threatModelingContent } from './ti-11-threat-modeling.js';
import { automationToolsContent } from './ti-12-automation-tools.js';
import { strategicIntelligenceContent } from './ti-13-strategic-intelligence.js';
import { tacticalIntelligenceContent } from './ti-14-tactical-intelligence.js';
import { operationalIntelligenceContent } from './ti-15-operational-intelligence.js';
import { advancedAnalyticsContent } from './ti-16-advanced-analytics.js';
import { cyberThreatHuntingContent } from './ti-17-cyber-threat-hunting.js';
import { intelligenceRequirementsContent } from './ti-18-intelligence-requirements.js';
import { intelligenceDisseminationContent } from './ti-19-intelligence-dissemination.js';
import { performanceMetricsContent } from './ti-20-performance-metrics.js';
import { crisisIntelligenceContent } from './ti-21-crisis-intelligence.js';
import { legalEthicalContent } from './ti-22-legal-ethical.js';
import { programManagementContent } from './ti-23-program-management.js';
import { futureTrendsContent } from './ti-24-future-trends.js';
import { intelligenceCycleContent } from './intelligence-cycle.js';
import { threatActorsContent } from './threat-actors.js';
import { ttpsAnalysisContent } from './ttps-analysis.js';
import { iocMalwareContent } from './ioc-malware.js';
import { osintIntelligenceContent } from './osint-intelligence.js';
import { darkWebIntelligenceContent } from './dark-web-intelligence.js';
// Temporarily comment out missing imports until files are created
// import { technicalAnalysisContent } from './technical-analysis.js';
// import { strategicIntelligenceContent } from './strategic-intelligence.js';
// import { tacticalIntelligenceContent } from './tactical-intelligence.js';
// import { operationalIntelligenceContent } from './operational-intelligence.js';
// import { threatModelingContent } from './threat-modeling.js';
// import { attributionAnalysisContent } from './attribution-analysis.js';
// import { intelligenceSharingContent } from './intelligence-sharing.js';
// import { automationToolsContent } from './automation-tools.js';

export const threatIntelligenceLearningPath = {
  id: "threat-intelligence",
  title: "Threat Intelligence",
  description: "Master comprehensive threat intelligence operations, from collection and analysis to dissemination of actionable intelligence for proactive cybersecurity defense.",
  category: "Intelligence & Analysis",
  difficulty: "Intermediate to Expert",
  estimatedTime: "150+ hours",
  prerequisites: [
    "Cybersecurity fundamentals",
    "Understanding of attack methodologies",
    "Basic knowledge of malware and threat actors",
    "Analytical and research skills",
    "Familiarity with security tools and technologies"
  ],
  outcomes: [
    "Lead threat intelligence programs and operations",
    "Conduct advanced threat actor analysis and attribution",
    "Develop actionable intelligence products and briefings",
    "Master OSINT and dark web intelligence gathering",
    "Implement threat intelligence platforms and automation",
    "Provide strategic, tactical, and operational intelligence support"
  ],
  modules: [
    threatIntelIntroContent,
    intelligenceLifecycleContent,
    threatLandscapeContent,
    osintFundamentalsContent,
    dataCollectionContent,
    analysisMethodsContent,
    iocManagementContent,
    ttpAnalysisContent,
    attributionAnalysisContent,
    intelligenceSharingContent,
    threatModelingContent,
    automationToolsContent,
    strategicIntelligenceContent,
    tacticalIntelligenceContent,
    operationalIntelligenceContent,
    advancedAnalyticsContent,
    cyberThreatHuntingContent,
    intelligenceRequirementsContent,
    intelligenceDisseminationContent,
    performanceMetricsContent,
    crisisIntelligenceContent,
    legalEthicalContent,
    programManagementContent,
    futureTrendsContent,
    intelligenceCycleContent,
    threatActorsContent,
    ttpsAnalysisContent,
    iocMalwareContent,
    osintIntelligenceContent,
    darkWebIntelligenceContent
  ]
};

export const getAllThreatIntelligenceModules = () => {
  return threatIntelligenceLearningPath.modules;
};

export const getThreatIntelligenceModuleById = (id) => {
  return threatIntelligenceLearningPath.modules.find(module => module.id === id);
}; 