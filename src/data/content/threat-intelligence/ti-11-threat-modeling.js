/**
 * Threat Modeling for Intelligence Module
 */

export const threatModelingContent = {
  id: "ti-11",
  pathId: "threat-intelligence",
  title: "Threat Modeling for Intelligence",
  description: "Master threat modeling methodologies specifically designed for threat intelligence operations, including adversary modeling, attack scenario development, and intelligence-driven threat assessment.",
  objectives: [
    "Understand threat modeling fundamentals for intelligence",
    "Learn adversary modeling and profiling techniques",
    "Master attack scenario development and analysis",
    "Develop intelligence-driven threat assessment skills",
    "Learn threat model validation and testing",
    "Create actionable threat models for defensive planning"
  ],
  difficulty: "Advanced",
  estimatedTime: 125,
  sections: [
    {
      title: "Intelligence-Driven Threat Modeling",
      content: `
        <h2>Intelligence-Driven Threat Modeling Fundamentals</h2>
        <p>Threat modeling for intelligence operations focuses on understanding adversary capabilities, intentions, and likely attack paths to inform defensive strategies and intelligence collection priorities.</p>
        
        <h3>Threat Modeling in Intelligence Context</h3>
        <ul>
          <li><strong>Traditional vs. Intelligence-Driven Modeling:</strong>
            <ul>
              <li>Asset-centric vs. adversary-centric approaches</li>
              <li>Static vs. dynamic threat landscapes</li>
              <li>Generic vs. specific threat actor focus</li>
              <li>Defensive vs. intelligence collection orientation</li>
            </ul>
          </li>
          <li><strong>Intelligence Integration:</strong>
            <ul>
              <li>Current threat intelligence incorporation</li>
              <li>Historical attack pattern analysis</li>
              <li>Emerging threat trend consideration</li>
              <li>Geopolitical context integration</li>
            </ul>
          </li>
          <li><strong>Stakeholder Alignment:</strong>
            <ul>
              <li>Intelligence requirements mapping</li>
              <li>Operational security priorities</li>
              <li>Risk management objectives</li>
              <li>Resource allocation considerations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Threat Modeling Methodologies</h3>
        <ul>
          <li><strong>STRIDE-Based Intelligence Modeling:</strong>
            <ul>
              <li>Spoofing - Identity and authentication threats</li>
              <li>Tampering - Data and system integrity threats</li>
              <li>Repudiation - Non-repudiation and accountability</li>
              <li>Information Disclosure - Confidentiality threats</li>
              <li>Denial of Service - Availability threats</li>
              <li>Elevation of Privilege - Authorization threats</li>
            </ul>
          </li>
          <li><strong>PASTA (Process for Attack Simulation and Threat Analysis):</strong>
            <ul>
              <li>Business objective definition</li>
              <li>Technical scope identification</li>
              <li>Application decomposition</li>
              <li>Threat analysis and enumeration</li>
              <li>Vulnerability and weakness analysis</li>
              <li>Attack modeling and simulation</li>
              <li>Risk analysis and impact assessment</li>
            </ul>
          </li>
          <li><strong>OCTAVE (Operationally Critical Threat, Asset, and Vulnerability Evaluation):</strong>
            <ul>
              <li>Organizational risk assessment</li>
              <li>Asset identification and prioritization</li>
              <li>Threat scenario development</li>
              <li>Risk analysis and mitigation planning</li>
            </ul>
          </li>
        </ul>
        
        <h3>Intelligence-Specific Considerations</h3>
        <ul>
          <li><strong>Adversary Capability Assessment:</strong>
            <ul>
              <li>Technical sophistication levels</li>
              <li>Resource availability and constraints</li>
              <li>Operational security practices</li>
              <li>Innovation and adaptation capabilities</li>
            </ul>
          </li>
          <li><strong>Intent and Motivation Analysis:</strong>
            <ul>
              <li>Strategic objectives and goals</li>
              <li>Tactical priorities and preferences</li>
              <li>Risk tolerance and operational constraints</li>
              <li>Success criteria and metrics</li>
            </ul>
          </li>
          <li><strong>Opportunity Assessment:</strong>
            <ul>
              <li>Attack surface analysis</li>
              <li>Vulnerability landscape evaluation</li>
              <li>Timing and situational factors</li>
              <li>Environmental and contextual opportunities</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Adversary Modeling and Profiling",
      content: `
        <h2>Adversary Modeling and Profiling</h2>
        <p>Comprehensive adversary modeling creates detailed profiles of threat actors, their capabilities, motivations, and operational patterns to predict future behavior and attack vectors.</p>
        
        <h3>Adversary Profiling Framework</h3>
        <ul>
          <li><strong>Capability Profiling:</strong>
            <ul>
              <li>Technical skills and expertise levels</li>
              <li>Tool development and acquisition</li>
              <li>Infrastructure management capabilities</li>
              <li>Operational security sophistication</li>
            </ul>
          </li>
          <li><strong>Resource Assessment:</strong>
            <ul>
              <li>Financial resources and funding</li>
              <li>Human resources and team size</li>
              <li>Time availability and constraints</li>
              <li>Technology and infrastructure access</li>
            </ul>
          </li>
          <li><strong>Motivation Analysis:</strong>
            <ul>
              <li>Primary and secondary motivations</li>
              <li>Risk-reward calculations</li>
              <li>Success criteria and objectives</li>
              <li>Ideological and personal drivers</li>
            </ul>
          </li>
        </ul>
        
        <h3>Behavioral Pattern Analysis</h3>
        <ul>
          <li><strong>Operational Patterns:</strong>
            <ul>
              <li>Attack timing and frequency</li>
              <li>Target selection criteria</li>
              <li>Campaign duration and persistence</li>
              <li>Operational tempo and intensity</li>
            </ul>
          </li>
          <li><strong>Technical Patterns:</strong>
            <ul>
              <li>Preferred attack vectors and techniques</li>
              <li>Tool and malware preferences</li>
              <li>Infrastructure usage patterns</li>
              <li>Communication and coordination methods</li>
            </ul>
          </li>
          <li><strong>Adaptation and Evolution:</strong>
            <ul>
              <li>Response to defensive measures</li>
              <li>Technology adoption and innovation</li>
              <li>Operational security improvements</li>
              <li>Collaboration and knowledge sharing</li>
            </ul>
          </li>
        </ul>
        
        <h3>Adversary Modeling Techniques</h3>
        <ul>
          <li><strong>Persona Development:</strong>
            <ul>
              <li>Detailed adversary character profiles</li>
              <li>Skill sets and capability matrices</li>
              <li>Motivation and objective mapping</li>
              <li>Behavioral characteristic documentation</li>
            </ul>
          </li>
          <li><strong>Scenario-Based Modeling:</strong>
            <ul>
              <li>Attack scenario development</li>
              <li>Decision tree and pathway analysis</li>
              <li>Contingency and adaptation planning</li>
              <li>Success and failure condition modeling</li>
            </ul>
          </li>
          <li><strong>Comparative Analysis:</strong>
            <ul>
              <li>Cross-actor capability comparison</li>
              <li>Technique and tool overlap analysis</li>
              <li>Evolution and trend identification</li>
              <li>Clustering and classification</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Attack Scenario Development",
      content: `
        <h2>Attack Scenario Development and Analysis</h2>
        <p>Developing realistic attack scenarios based on threat intelligence enables organizations to test defenses, identify gaps, and prioritize security investments.</p>
        
        <h3>Scenario Development Process</h3>
        <ul>
          <li><strong>Intelligence Foundation:</strong>
            <ul>
              <li>Current threat intelligence integration</li>
              <li>Historical attack pattern analysis</li>
              <li>Adversary capability assessment</li>
              <li>Environmental context consideration</li>
            </ul>
          </li>
          <li><strong>Scenario Construction:</strong>
            <ul>
              <li>Attack vector identification</li>
              <li>Multi-stage attack chain development</li>
              <li>Decision point and branching analysis</li>
              <li>Success and failure condition definition</li>
            </ul>
          </li>
          <li><strong>Realism and Validation:</strong>
            <ul>
              <li>Technical feasibility assessment</li>
              <li>Resource requirement validation</li>
              <li>Timeline and duration estimation</li>
              <li>Expert review and validation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Scenario Types and Categories</h3>
        <ul>
          <li><strong>Tactical Scenarios:</strong>
            <ul>
              <li>Specific technique implementation</li>
              <li>Tool and exploit utilization</li>
              <li>Short-term operational objectives</li>
              <li>Immediate defensive implications</li>
            </ul>
          </li>
          <li><strong>Campaign Scenarios:</strong>
            <ul>
              <li>Multi-stage attack operations</li>
              <li>Long-term strategic objectives</li>
              <li>Resource allocation and planning</li>
              <li>Adaptive and evolutionary elements</li>
            </ul>
          </li>
          <li><strong>Strategic Scenarios:</strong>
            <ul>
              <li>Nation-state level operations</li>
              <li>Critical infrastructure targeting</li>
              <li>Geopolitical context and implications</li>
              <li>Long-term impact assessment</li>
            </ul>
          </li>
        </ul>
        
        <h3>Scenario Analysis and Application</h3>
        <ul>
          <li><strong>Defense Gap Analysis:</strong>
            <ul>
              <li>Control effectiveness assessment</li>
              <li>Detection capability evaluation</li>
              <li>Response readiness testing</li>
              <li>Mitigation strategy validation</li>
            </ul>
          </li>
          <li><strong>Risk Assessment Integration:</strong>
            <ul>
              <li>Likelihood and impact evaluation</li>
              <li>Risk prioritization and ranking</li>
              <li>Cost-benefit analysis</li>
              <li>Investment decision support</li>
            </ul>
          </li>
          <li><strong>Training and Exercise Development:</strong>
            <ul>
              <li>Tabletop exercise scenarios</li>
              <li>Red team operation planning</li>
              <li>Incident response training</li>
              <li>Awareness and education programs</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "What is the primary difference between traditional threat modeling and intelligence-driven threat modeling?",
            options: [
              "Traditional modeling is faster",
              "Intelligence-driven modeling focuses on specific adversaries rather than generic threats",
              "Traditional modeling is more accurate",
              "Intelligence-driven modeling requires fewer resources"
            ],
            correctAnswer: 1,
            explanation: "Intelligence-driven threat modeling focuses on specific adversaries and their known capabilities, motivations, and patterns, rather than generic threat categories, making it more targeted and actionable."
          },
          {
            question: "In the STRIDE methodology, what does the 'I' represent?",
            options: [
              "Identity",
              "Information Disclosure",
              "Integrity",
              "Infrastructure"
            ],
            correctAnswer: 1,
            explanation: "In STRIDE, the 'I' represents Information Disclosure, which refers to threats that compromise the confidentiality of data or information."
          },
          {
            question: "What is the primary purpose of developing attack scenarios based on threat intelligence?",
            options: [
              "To create new attack methods",
              "To test defenses and identify security gaps",
              "To train attackers",
              "To develop new malware"
            ],
            correctAnswer: 1,
            explanation: "Attack scenarios based on threat intelligence are primarily used to test existing defenses, identify security gaps, and validate the effectiveness of security controls against realistic threats."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
