/**
 * Introduction to Threat Intelligence Module
 */

export const threatIntelIntroContent = {
  id: "ti-1",
  pathId: "threat-intelligence",
  title: "Introduction to Threat Intelligence",
  description: "Master the fundamentals of cyber threat intelligence, from collection and analysis to dissemination of actionable intelligence for proactive cybersecurity defense.",
  objectives: [
    "Understand threat intelligence fundamentals and taxonomy",
    "Learn the intelligence cycle and analytical methodologies",
    "Explore different types of threat intelligence (strategic, tactical, operational)",
    "Master intelligence collection techniques and sources",
    "Understand threat actor analysis and attribution",
    "Learn intelligence sharing frameworks and standards"
  ],
  difficulty: "Beginner",
  estimatedTime: 110,
  sections: [
    {
      title: "Threat Intelligence Fundamentals",
      content: `
        <h2>Threat Intelligence Fundamentals</h2>
        <p>Threat intelligence is evidence-based knowledge about existing or emerging menaces or hazards to assets that can inform decisions regarding how to respond to threats.</p>
        
        <h3>What is Threat Intelligence?</h3>
        <ul>
          <li><strong>Definition:</strong> Actionable information about current and potential attacks that threaten an organization</li>
          <li><strong>Purpose:</strong> Enable informed decision-making for security and business operations</li>
          <li><strong>Scope:</strong> Covers tactics, techniques, procedures, and capabilities of threat actors</li>
          <li><strong>Value:</strong> Transforms raw data into actionable insights for proactive defense</li>
        </ul>
        
        <h3>Threat Intelligence vs Information vs Data</h3>
        <ul>
          <li><strong>Data:</strong> Raw facts and observations
            <ul>
              <li>IP addresses, domain names, file hashes</li>
              <li>Log entries and network traffic</li>
              <li>Vulnerability scan results</li>
            </ul>
          </li>
          <li><strong>Information:</strong> Processed data with context
            <ul>
              <li>Correlation of related indicators</li>
              <li>Pattern identification and analysis</li>
              <li>Structured reporting and documentation</li>
            </ul>
          </li>
          <li><strong>Intelligence:</strong> Analyzed information for decision-making
            <ul>
              <li>Actionable insights and recommendations</li>
              <li>Risk assessment and impact analysis</li>
              <li>Predictive analysis and forecasting</li>
            </ul>
          </li>
        </ul>
        
        <h3>Benefits of Threat Intelligence</h3>
        <ul>
          <li><strong>Proactive Defense:</strong>
            <ul>
              <li>Early warning of emerging threats</li>
              <li>Prevention rather than reactive response</li>
              <li>Strategic planning and resource allocation</li>
            </ul>
          </li>
          <li><strong>Improved Detection:</strong>
            <ul>
              <li>Enhanced security monitoring capabilities</li>
              <li>Reduced false positives through context</li>
              <li>Better understanding of attack patterns</li>
            </ul>
          </li>
          <li><strong>Informed Decision Making:</strong>
            <ul>
              <li>Risk-based prioritization of security efforts</li>
              <li>Budget justification and resource planning</li>
              <li>Vendor and technology selection guidance</li>
            </ul>
          </li>
          <li><strong>Incident Response Enhancement:</strong>
            <ul>
              <li>Faster incident classification and triage</li>
              <li>Attribution and campaign tracking</li>
              <li>Improved containment and remediation</li>
            </ul>
          </li>
        </ul>
        
        <h3>Threat Intelligence Stakeholders</h3>
        <ul>
          <li><strong>Executive Leadership:</strong> Strategic intelligence for business decisions</li>
          <li><strong>Security Operations:</strong> Tactical intelligence for detection and response</li>
          <li><strong>Incident Response Teams:</strong> Operational intelligence for investigation</li>
          <li><strong>Threat Hunters:</strong> Technical intelligence for proactive hunting</li>
          <li><strong>Risk Management:</strong> Intelligence for risk assessment and mitigation</li>
          <li><strong>Vulnerability Management:</strong> Intelligence for prioritization and patching</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Types of Threat Intelligence",
      content: `
        <h2>Types of Threat Intelligence</h2>
        <p>Threat intelligence operates at different levels to serve various organizational needs and stakeholder requirements.</p>
        
        <h3>Strategic Intelligence</h3>
        <ul>
          <li><strong>Purpose:</strong> High-level intelligence for executive decision-making</li>
          <li><strong>Audience:</strong> C-suite executives, board members, senior management</li>
          <li><strong>Characteristics:</strong>
            <ul>
              <li>Long-term trends and patterns</li>
              <li>Geopolitical analysis and nation-state activities</li>
              <li>Industry-wide threat landscapes</li>
              <li>Business impact and risk assessments</li>
            </ul>
          </li>
          <li><strong>Examples:</strong>
            <ul>
              <li>Annual threat landscape reports</li>
              <li>Emerging threat trend analysis</li>
              <li>Regulatory and compliance intelligence</li>
              <li>Investment and merger security considerations</li>
            </ul>
          </li>
          <li><strong>Format:</strong> Executive briefings, reports, presentations</li>
          <li><strong>Frequency:</strong> Quarterly or annually</li>
        </ul>
        
        <h3>Tactical Intelligence</h3>
        <ul>
          <li><strong>Purpose:</strong> Medium-term intelligence for security planning and operations</li>
          <li><strong>Audience:</strong> Security managers, architects, and operations teams</li>
          <li><strong>Characteristics:</strong>
            <ul>
              <li>Threat actor capabilities and intentions</li>
              <li>Campaign analysis and attribution</li>
              <li>Attack methodology documentation</li>
              <li>Defense strategy recommendations</li>
            </ul>
          </li>
          <li><strong>Examples:</strong>
            <ul>
              <li>Threat actor profiles and playbooks</li>
              <li>Malware family analysis reports</li>
              <li>Attack campaign documentation</li>
              <li>Defensive control recommendations</li>
            </ul>
          </li>
          <li><strong>Format:</strong> Detailed reports, threat briefings, playbooks</li>
          <li><strong>Frequency:</strong> Weekly to monthly</li>
        </ul>
        
        <h3>Operational Intelligence</h3>
        <ul>
          <li><strong>Purpose:</strong> Short-term intelligence for immediate security actions</li>
          <li><strong>Audience:</strong> SOC analysts, incident responders, threat hunters</li>
          <li><strong>Characteristics:</strong>
            <ul>
              <li>Specific threat details and context</li>
              <li>Incident-specific intelligence</li>
              <li>Attribution and campaign linking</li>
              <li>Actionable response guidance</li>
            </ul>
          </li>
          <li><strong>Examples:</strong>
            <ul>
              <li>Incident analysis and attribution</li>
              <li>Campaign tracking and evolution</li>
              <li>Threat hunting hypotheses</li>
              <li>Response playbook guidance</li>
            </ul>
          </li>
          <li><strong>Format:</strong> Incident reports, alerts, hunting guides</li>
          <li><strong>Frequency:</strong> Real-time to weekly</li>
        </ul>
        
        <h3>Technical Intelligence</h3>
        <ul>
          <li><strong>Purpose:</strong> Detailed technical information for defensive implementation</li>
          <li><strong>Audience:</strong> Security engineers, analysts, and technical specialists</li>
          <li><strong>Characteristics:</strong>
            <ul>
              <li>Indicators of Compromise (IOCs)</li>
              <li>Tactics, Techniques, and Procedures (TTPs)</li>
              <li>Malware analysis and signatures</li>
              <li>Network and host-based indicators</li>
            </ul>
          </li>
          <li><strong>Examples:</strong>
            <ul>
              <li>IOC feeds and threat feeds</li>
              <li>YARA rules and signatures</li>
              <li>MITRE ATT&CK technique mappings</li>
              <li>Detection and hunting queries</li>
            </ul>
          </li>
          <li><strong>Format:</strong> Machine-readable feeds, technical reports</li>
          <li><strong>Frequency:</strong> Real-time to daily</li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "The Intelligence Cycle",
      content: `
        <h2>The Intelligence Cycle</h2>
        <p>The intelligence cycle is a systematic process for producing actionable intelligence from raw information.</p>
        
        <h3>Phase 1: Planning and Direction</h3>
        <ul>
          <li><strong>Requirements Definition:</strong>
            <ul>
              <li>Identify intelligence needs and priorities</li>
              <li>Define collection objectives and scope</li>
              <li>Establish success criteria and metrics</li>
              <li>Allocate resources and assign responsibilities</li>
            </ul>
          </li>
          <li><strong>Intelligence Requirements:</strong>
            <ul>
              <li>Priority Intelligence Requirements (PIRs)</li>
              <li>Essential Elements of Information (EEIs)</li>
              <li>Specific Information Requirements (SIRs)</li>
              <li>Collection guidance and tasking</li>
            </ul>
          </li>
          <li><strong>Planning Considerations:</strong>
            <ul>
              <li>Stakeholder needs and expectations</li>
              <li>Available collection sources and methods</li>
              <li>Legal and ethical constraints</li>
              <li>Timeline and resource limitations</li>
            </ul>
          </li>
        </ul>
        
        <h3>Phase 2: Collection</h3>
        <ul>
          <li><strong>Collection Sources:</strong>
            <ul>
              <li>Open Source Intelligence (OSINT)</li>
              <li>Human Intelligence (HUMINT)</li>
              <li>Technical Intelligence (TECHINT)</li>
              <li>Commercial Intelligence (COMINT)</li>
            </ul>
          </li>
          <li><strong>Collection Methods:</strong>
            <ul>
              <li>Automated feeds and APIs</li>
              <li>Manual research and analysis</li>
              <li>Community sharing and partnerships</li>
              <li>Internal telemetry and logs</li>
            </ul>
          </li>
          <li><strong>Collection Management:</strong>
            <ul>
              <li>Source reliability assessment</li>
              <li>Data quality and validation</li>
              <li>Collection gap identification</li>
              <li>Source protection and OPSEC</li>
            </ul>
          </li>
        </ul>
        
        <h3>Phase 3: Processing and Exploitation</h3>
        <ul>
          <li><strong>Data Processing:</strong>
            <ul>
              <li>Data normalization and standardization</li>
              <li>Automated parsing and extraction</li>
              <li>Data enrichment and contextualization</li>
              <li>Quality control and validation</li>
            </ul>
          </li>
          <li><strong>Information Exploitation:</strong>
            <ul>
              <li>Pattern recognition and analysis</li>
              <li>Correlation and linking</li>
              <li>Timeline reconstruction</li>
              <li>Relationship mapping</li>
            </ul>
          </li>
        </ul>
        
        <h3>Phase 4: Analysis and Production</h3>
        <ul>
          <li><strong>Analytical Techniques:</strong>
            <ul>
              <li>Structured analytic techniques (SATs)</li>
              <li>Hypothesis generation and testing</li>
              <li>Alternative analysis and devil's advocacy</li>
              <li>Predictive analysis and forecasting</li>
            </ul>
          </li>
          <li><strong>Intelligence Production:</strong>
            <ul>
              <li>Written reports and assessments</li>
              <li>Visual analysis and presentations</li>
              <li>Briefings and oral presentations</li>
              <li>Machine-readable formats and feeds</li>
            </ul>
          </li>
        </ul>
        
        <h3>Phase 5: Dissemination and Integration</h3>
        <ul>
          <li><strong>Dissemination:</strong>
            <ul>
              <li>Targeted distribution to stakeholders</li>
              <li>Format adaptation for audience needs</li>
              <li>Classification and handling guidance</li>
              <li>Timeliness and relevance considerations</li>
            </ul>
          </li>
          <li><strong>Integration:</strong>
            <ul>
              <li>Integration into security operations</li>
              <li>Decision support and action guidance</li>
              <li>Feedback collection and evaluation</li>
              <li>Continuous improvement and refinement</li>
            </ul>
          </li>
        </ul>
        
        <h3>Phase 6: Feedback and Evaluation</h3>
        <ul>
          <li><strong>Effectiveness Assessment:</strong>
            <ul>
              <li>Stakeholder satisfaction and feedback</li>
              <li>Intelligence product utility and impact</li>
              <li>Process efficiency and effectiveness</li>
              <li>Resource utilization and ROI</li>
            </ul>
          </li>
          <li><strong>Continuous Improvement:</strong>
            <ul>
              <li>Process refinement and optimization</li>
              <li>Source evaluation and adjustment</li>
              <li>Analytical method enhancement</li>
              <li>Capability development and training</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Threat Intelligence Standards and Frameworks",
      content: `
        <h2>Threat Intelligence Standards and Frameworks</h2>
        <p>Industry standards and frameworks enable consistent threat intelligence sharing, analysis, and implementation.</p>
        
        <h3>STIX (Structured Threat Information eXpression)</h3>
        <ul>
          <li><strong>Purpose:</strong> Standardized language for cyber threat intelligence</li>
          <li><strong>Components:</strong>
            <ul>
              <li>Domain Objects: Indicators, malware, attack patterns, campaigns</li>
              <li>Relationship Objects: Links between domain objects</li>
              <li>Meta Objects: Marking definitions, language content</li>
            </ul>
          </li>
          <li><strong>Benefits:</strong>
            <ul>
              <li>Structured data representation</li>
              <li>Interoperability between platforms</li>
              <li>Rich context and relationships</li>
              <li>Machine-readable format</li>
            </ul>
          </li>
        </ul>
        
        <h3>TAXII (Trusted Automated eXchange of Indicator Information)</h3>
        <ul>
          <li><strong>Purpose:</strong> Standard for sharing cyber threat intelligence</li>
          <li><strong>Services:</strong>
            <ul>
              <li>Collection: Repository for threat intelligence</li>
              <li>Channel: Distribution mechanism for intelligence</li>
              <li>Discovery: Finding available intelligence sources</li>
            </ul>
          </li>
          <li><strong>Transport:</strong> RESTful API over HTTPS</li>
        </ul>
        
        <h3>MITRE ATT&CK Framework</h3>
        <ul>
          <li><strong>Matrix Structure:</strong>
            <ul>
              <li>Tactics: High-level adversary goals</li>
              <li>Techniques: Methods to achieve tactics</li>
              <li>Sub-techniques: Specific implementations</li>
            </ul>
          </li>
          <li><strong>Intelligence Applications:</strong>
            <ul>
              <li>Threat actor behavior mapping</li>
              <li>Campaign analysis and attribution</li>
              <li>Gap analysis and coverage assessment</li>
              <li>Detection and mitigation strategy</li>
            </ul>
          </li>
        </ul>
        
        <h3>Traffic Light Protocol (TLP)</h3>
        <ul>
          <li><strong>TLP:RED:</strong> Personal use only, no sharing</li>
          <li><strong>TLP:AMBER:</strong> Limited sharing within organization</li>
          <li><strong>TLP:GREEN:</strong> Community sharing allowed</li>
          <li><strong>TLP:WHITE:</strong> Public sharing and disclosure</li>
        </ul>
        
        <h3>Diamond Model of Intrusion Analysis</h3>
        <ul>
          <li><strong>Adversary:</strong> Actor or organization behind the attack</li>
          <li><strong>Infrastructure:</strong> Physical or logical communication structures</li>
          <li><strong>Capability:</strong> Tools, techniques, and procedures</li>
          <li><strong>Victim:</strong> Target of the adversary's attack</li>
        </ul>
        
        <h3>Lockheed Martin Cyber Kill Chain</h3>
        <ul>
          <li><strong>Reconnaissance:</strong> Research and target identification</li>
          <li><strong>Weaponization:</strong> Exploit and payload creation</li>
          <li><strong>Delivery:</strong> Payload transmission to target</li>
          <li><strong>Exploitation:</strong> Code execution on target</li>
          <li><strong>Installation:</strong> Malware installation and persistence</li>
          <li><strong>Command and Control:</strong> Remote access establishment</li>
          <li><strong>Actions on Objectives:</strong> Goal achievement and data exfiltration</li>
        </ul>
      `,
      type: "text"
    }
  ],
  practicalLab: {
    title: "Threat Intelligence Fundamentals Lab",
    description: "Hands-on exploration of threat intelligence concepts, standards, and basic analysis techniques.",
    tasks: [
      {
        category: "Intelligence Standards",
        commands: [
          {
            command: "Explore MITRE ATT&CK Enterprise Matrix",
            description: "Navigate and understand the ATT&CK framework structure",
            hint: "Focus on understanding tactic-technique relationships",
            expectedOutput: "Familiarity with ATT&CK navigation and technique details"
          }
        ]
      },
      {
        category: "Threat Actor Research",
        commands: [
          {
            command: "Research APT1 threat actor profile",
            description: "Analyze a well-documented threat actor using open sources",
            hint: "Look for TTPs, infrastructure, and campaign information",
            expectedOutput: "Comprehensive threat actor profile with attribution"
          }
        ]
      }
    ]
  },
  knowledgeCheck: [
    {
      question: "What is the primary difference between threat intelligence and threat information?",
      options: [
        "Intelligence is faster to produce than information",
        "Intelligence is analyzed information that supports decision-making",
        "Intelligence contains more technical details than information",
        "Intelligence is only available to government agencies"
      ],
      correct: 1,
      explanation: "Threat intelligence is analyzed information that has been processed and contextualized to support decision-making, while information is simply processed data with context."
    },
    {
      question: "Which type of threat intelligence is most appropriate for C-suite executives?",
      options: [
        "Technical intelligence with IOCs",
        "Operational intelligence for incident response",
        "Strategic intelligence with business impact analysis",
        "Tactical intelligence with detailed TTPs"
      ],
      correct: 2,
      explanation: "Strategic intelligence provides high-level analysis of trends, business impacts, and risk assessments that are most relevant for executive decision-making."
    },
    {
      question: "What is the purpose of the STIX standard?",
      options: [
        "To share threat intelligence data between organizations",
        "To provide a structured language for representing cyber threat intelligence",
        "To classify the sensitivity of threat intelligence",
        "To analyze the effectiveness of threat intelligence programs"
      ],
      correct: 1,
      explanation: "STIX (Structured Threat Information eXpression) provides a standardized language and format for representing cyber threat intelligence in a structured, machine-readable way."
    }
  ]
}; 