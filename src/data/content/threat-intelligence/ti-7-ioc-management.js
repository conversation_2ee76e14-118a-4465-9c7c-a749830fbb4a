/**
 * IOC Management and Analysis Module
 */

export const iocManagementContent = {
  id: "ti-7",
  pathId: "threat-intelligence",
  title: "IOC Management and Analysis",
  description: "Master Indicators of Compromise (IOC) identification, analysis, and management for effective threat detection and response operations.",
  objectives: [
    "Understand IOC types and classification systems",
    "Learn IOC extraction and validation techniques",
    "Master IOC lifecycle management processes",
    "Develop IOC quality assessment skills",
    "Learn IOC sharing and distribution methods",
    "Implement automated IOC management systems"
  ],
  difficulty: "Intermediate",
  estimatedTime: 110,
  sections: [
    {
      title: "IOC Fundamentals and Classification",
      content: `
        <h2>Indicators of Compromise (IOC) Fundamentals</h2>
        <p>Indicators of Compromise are forensic artifacts that suggest a system or network has been breached or compromised by malicious activity.</p>
        
        <h3>IOC Definition and Purpose</h3>
        <ul>
          <li><strong>What are IOCs:</strong>
            <ul>
              <li>Digital forensic artifacts indicating malicious activity</li>
              <li>Observable evidence of security incidents</li>
              <li>Actionable intelligence for detection and response</li>
              <li>Building blocks for threat hunting and monitoring</li>
            </ul>
          </li>
          <li><strong>IOC vs. TTPs:</strong>
            <ul>
              <li>IOCs - Specific technical indicators</li>
              <li>TTPs - Behavioral patterns and methodologies</li>
              <li>Complementary intelligence types</li>
              <li>Different detection and response applications</li>
            </ul>
          </li>
          <li><strong>IOC Lifecycle:</strong>
            <ul>
              <li>Discovery and extraction</li>
              <li>Validation and enrichment</li>
              <li>Distribution and implementation</li>
              <li>Monitoring and maintenance</li>
              <li>Retirement and archival</li>
            </ul>
          </li>
        </ul>
        
        <h3>IOC Classification Systems</h3>
        <ul>
          <li><strong>Technical IOC Categories:</strong>
            <ul>
              <li>Network indicators (IPs, domains, URLs)</li>
              <li>File-based indicators (hashes, names, paths)</li>
              <li>Registry indicators (keys, values)</li>
              <li>Process and service indicators</li>
              <li>Email and communication indicators</li>
            </ul>
          </li>
          <li><strong>Pyramid of Pain Classification:</strong>
            <ul>
              <li>Hash values - Easy to change, low pain</li>
              <li>IP addresses - Moderate effort to change</li>
              <li>Domain names - Higher cost to change</li>
              <li>Network/host artifacts - Significant effort</li>
              <li>Tools - Expensive to replace</li>
              <li>TTPs - Most difficult and costly to change</li>
            </ul>
          </li>
          <li><strong>Confidence and Fidelity Levels:</strong>
            <ul>
              <li>High fidelity - Low false positive rate</li>
              <li>Medium fidelity - Moderate false positives</li>
              <li>Low fidelity - High false positive potential</li>
              <li>Context-dependent reliability</li>
            </ul>
          </li>
        </ul>
        
        <h3>IOC Data Formats and Standards</h3>
        <ul>
          <li><strong>STIX (Structured Threat Information eXpression):</strong>
            <ul>
              <li>Standardized threat intelligence format</li>
              <li>Rich context and relationship modeling</li>
              <li>Version 2.x current standard</li>
              <li>JSON-based implementation</li>
            </ul>
          </li>
          <li><strong>OpenIOC:</strong>
            <ul>
              <li>XML-based IOC format</li>
              <li>Flexible indicator composition</li>
              <li>Boolean logic support</li>
              <li>Tool ecosystem integration</li>
            </ul>
          </li>
          <li><strong>YARA Rules:</strong>
            <ul>
              <li>Pattern matching rule language</li>
              <li>Malware identification and classification</li>
              <li>String and binary pattern matching</li>
              <li>Conditional logic and metadata</li>
            </ul>
          </li>
          <li><strong>Sigma Rules:</strong>
            <ul>
              <li>Log analysis rule format</li>
              <li>SIEM-agnostic detection rules</li>
              <li>Behavioral detection patterns</li>
              <li>Community-driven rule sharing</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "IOC Extraction and Validation",
      content: `
        <h2>IOC Extraction and Validation Techniques</h2>
        <p>Effective IOC extraction and validation ensures high-quality indicators that provide reliable detection capabilities with minimal false positives.</p>
        
        <h3>IOC Extraction Methods</h3>
        <ul>
          <li><strong>Automated Extraction:</strong>
            <ul>
              <li>Malware analysis sandbox outputs</li>
              <li>Network traffic analysis tools</li>
              <li>Log parsing and pattern recognition</li>
              <li>Machine learning-based extraction</li>
            </ul>
          </li>
          <li><strong>Manual Extraction:</strong>
            <ul>
              <li>Incident response investigations</li>
              <li>Forensic analysis findings</li>
              <li>Threat research and analysis</li>
              <li>Open source intelligence gathering</li>
            </ul>
          </li>
          <li><strong>Collaborative Extraction:</strong>
            <ul>
              <li>Threat intelligence sharing platforms</li>
              <li>Industry collaboration groups</li>
              <li>Government and law enforcement sharing</li>
              <li>Vendor and researcher contributions</li>
            </ul>
          </li>
        </ul>
        
        <h3>IOC Validation Processes</h3>
        <ul>
          <li><strong>Technical Validation:</strong>
            <ul>
              <li>Format and syntax verification</li>
              <li>Domain and IP address validation</li>
              <li>Hash algorithm verification</li>
              <li>URL and file path validation</li>
            </ul>
          </li>
          <li><strong>Contextual Validation:</strong>
            <ul>
              <li>Threat campaign association</li>
              <li>Temporal relevance assessment</li>
              <li>Geographic and sector relevance</li>
              <li>Attack vector correlation</li>
            </ul>
          </li>
          <li><strong>Quality Assessment:</strong>
            <ul>
              <li>False positive rate evaluation</li>
              <li>Detection efficacy testing</li>
              <li>Source credibility assessment</li>
              <li>Confidence level assignment</li>
            </ul>
          </li>
        </ul>
        
        <h3>IOC Enrichment and Enhancement</h3>
        <ul>
          <li><strong>Contextual Enrichment:</strong>
            <ul>
              <li>Threat actor attribution</li>
              <li>Campaign and malware family association</li>
              <li>Geographic and temporal context</li>
              <li>Target industry and sector information</li>
            </ul>
          </li>
          <li><strong>Technical Enrichment:</strong>
            <ul>
              <li>WHOIS and DNS information</li>
              <li>Geolocation and ASN data</li>
              <li>SSL certificate information</li>
              <li>Passive DNS and historical data</li>
            </ul>
          </li>
          <li><strong>Relationship Mapping:</strong>
            <ul>
              <li>IOC clustering and grouping</li>
              <li>Infrastructure relationship analysis</li>
              <li>Malware family connections</li>
              <li>Campaign timeline correlation</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "IOC Management and Distribution",
      content: `
        <h2>IOC Lifecycle Management and Distribution</h2>
        <p>Effective IOC management ensures indicators remain current, accurate, and actionable throughout their operational lifecycle.</p>
        
        <h3>IOC Lifecycle Management</h3>
        <ul>
          <li><strong>Creation and Ingestion:</strong>
            <ul>
              <li>Standardized IOC creation processes</li>
              <li>Quality control checkpoints</li>
              <li>Metadata and attribution requirements</li>
              <li>Initial confidence assessment</li>
            </ul>
          </li>
          <li><strong>Maintenance and Updates:</strong>
            <ul>
              <li>Regular validation and verification</li>
              <li>Confidence level adjustments</li>
              <li>Context and metadata updates</li>
              <li>Relationship and clustering updates</li>
            </ul>
          </li>
          <li><strong>Aging and Retirement:</strong>
            <ul>
              <li>Time-based aging policies</li>
              <li>Effectiveness-based retirement</li>
              <li>False positive rate monitoring</li>
              <li>Archival and historical preservation</li>
            </ul>
          </li>
        </ul>
        
        <h3>IOC Distribution and Sharing</h3>
        <ul>
          <li><strong>Internal Distribution:</strong>
            <ul>
              <li>SIEM and security tool integration</li>
              <li>Threat hunting team distribution</li>
              <li>Incident response team sharing</li>
              <li>SOC analyst briefings</li>
            </ul>
          </li>
          <li><strong>External Sharing:</strong>
            <ul>
              <li>Industry sharing groups</li>
              <li>Government and law enforcement</li>
              <li>Vendor and partner sharing</li>
              <li>Public threat intelligence platforms</li>
            </ul>
          </li>
          <li><strong>Automated Distribution:</strong>
            <ul>
              <li>TAXII (Trusted Automated eXchange)</li>
              <li>API-based distribution</li>
              <li>Feed-based sharing</li>
              <li>Real-time alerting systems</li>
            </ul>
          </li>
        </ul>
        
        <h3>IOC Management Platforms</h3>
        <ul>
          <li><strong>Threat Intelligence Platforms (TIPs):</strong>
            <ul>
              <li>Centralized IOC management</li>
              <li>Automated enrichment and correlation</li>
              <li>Integration with security tools</li>
              <li>Collaboration and sharing features</li>
            </ul>
          </li>
          <li><strong>SIEM Integration:</strong>
            <ul>
              <li>Real-time IOC matching</li>
              <li>Alert generation and prioritization</li>
              <li>Historical search capabilities</li>
              <li>Automated response actions</li>
            </ul>
          </li>
          <li><strong>Custom Management Systems:</strong>
            <ul>
              <li>Organization-specific requirements</li>
              <li>Custom workflow integration</li>
              <li>Specialized analysis capabilities</li>
              <li>Legacy system compatibility</li>
            </ul>
          </li>
        </ul>
      `,
      type: "text"
    },
    {
      title: "Knowledge Check",
      content: {
        questions: [
          {
            question: "According to the Pyramid of Pain, which type of indicator is most difficult and costly for attackers to change?",
            options: [
              "Hash values",
              "IP addresses",
              "Domain names",
              "TTPs (Tactics, Techniques, and Procedures)"
            ],
            correctAnswer: 3,
            explanation: "TTPs are at the top of the Pyramid of Pain because they represent fundamental attack methodologies that are most difficult and costly for attackers to change, making them the most valuable for long-term detection."
          },
          {
            question: "What is the primary advantage of using STIX format for IOC management?",
            options: [
              "Faster processing speed",
              "Smaller file sizes",
              "Standardized format with rich context and relationships",
              "Better encryption capabilities"
            ],
            correctAnswer: 2,
            explanation: "STIX provides a standardized format that includes rich context and relationship modeling, enabling better interoperability and more comprehensive threat intelligence sharing."
          },
          {
            question: "Which IOC validation process focuses on threat campaign association and temporal relevance?",
            options: [
              "Technical validation",
              "Contextual validation",
              "Format validation",
              "Syntax verification"
            ],
            correctAnswer: 1,
            explanation: "Contextual validation focuses on assessing the relevance of IOCs within the broader threat landscape, including campaign association, temporal relevance, and attack vector correlation."
          }
        ]
      },
      type: "quiz"
    }
  ]
};
