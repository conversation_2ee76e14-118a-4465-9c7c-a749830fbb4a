/**
 * TTPs Analysis Module
 */

export const ttpsAnalysisContent = {
  id: "ti-ttps-analysis",
  title: "TTPs Analysis",
  description: "Learn to analyze tactics, techniques, and procedures of threat actors.",
  difficulty: "Advanced",
  estimatedTime: 90,
  objectives: [
    "Understand TTPs framework",
    "Learn analysis techniques",
    "Master MITRE ATT&CK mapping",
    "Develop analytical capabilities"
  ],
  sections: [
    {
      title: "TTPs Analysis Fundamentals",
      content: `
        <h2>Tactics, Techniques, and Procedures Analysis</h2>
        <p>Learn how to analyze and understand threat actor TTPs for better threat intelligence.</p>
        <h3>TTP Components</h3>
        <ul>
          <li>Tactics - High-level goals</li>
          <li>Techniques - Methods to achieve tactics</li>
          <li>Procedures - Specific implementations</li>
          <li>MITRE ATT&CK mapping</li>
        </ul>
      `,
      type: "text"
    }
  ]
}; 