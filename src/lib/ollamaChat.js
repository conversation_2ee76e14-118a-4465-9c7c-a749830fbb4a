/**
 * Ollama Chat Integration
 * 
 * This module provides integration with the Ollama LLM through our middleware service
 * for the chat functionality in the CyberForce platform.
 */

import { supabase } from './supabase';

// Cache for storing recent responses to avoid duplicate API calls
const responseCache = new Map();
const CACHE_EXPIRY = 1000 * 60 * 60; // 1 hour

// Rate limiting
const rateLimits = {
  anonymous: {
    maxRequests: 10,
    timeWindow: 1000 * 60 * 60, // 1 hour
    requests: []
  },
  authenticated: {
    maxRequests: 50,
    timeWindow: 1000 * 60 * 60, // 1 hour
    requests: {}
  }
};

/**
 * Check if a request is within rate limits
 * @param {string|null} userId - User ID or null for anonymous users
 * @returns {boolean} - Whether the request is allowed
 */
function checkRateLimit(userId = null) {
  const now = Date.now();
  
  if (userId) {
    // Authenticated user
    if (!rateLimits.authenticated.requests[userId]) {
      rateLimits.authenticated.requests[userId] = [];
    }
    
    // Clean up old requests
    rateLimits.authenticated.requests[userId] = rateLimits.authenticated.requests[userId].filter(
      time => now - time < rateLimits.authenticated.timeWindow
    );
    
    // Check if under limit
    if (rateLimits.authenticated.requests[userId].length < rateLimits.authenticated.maxRequests) {
      rateLimits.authenticated.requests[userId].push(now);
      return true;
    }
    
    return false;
  } else {
    // Anonymous user
    // Clean up old requests
    rateLimits.anonymous.requests = rateLimits.anonymous.requests.filter(
      time => now - time < rateLimits.anonymous.timeWindow
    );
    
    // Check if under limit
    if (rateLimits.anonymous.requests.length < rateLimits.anonymous.maxRequests) {
      rateLimits.anonymous.requests.push(now);
      return true;
    }
    
    return false;
  }
}

/**
 * Get a cached response if available
 * @param {string} query - The user's query
 * @returns {object|null} - Cached response or null
 */
function getCachedResponse(query) {
  const normalizedQuery = query.toLowerCase().trim();
  
  if (responseCache.has(normalizedQuery)) {
    const { response, timestamp } = responseCache.get(normalizedQuery);
    
    // Check if cache is still valid
    if (Date.now() - timestamp < CACHE_EXPIRY) {
      return { ...response, cached: true };
    } else {
      // Remove expired cache entry
      responseCache.delete(normalizedQuery);
    }
  }
  
  return null;
}

/**
 * Cache a response for future use
 * @param {string} query - The user's query
 * @param {object} response - The response to cache
 */
function cacheResponse(query, response) {
  const normalizedQuery = query.toLowerCase().trim();
  responseCache.set(normalizedQuery, {
    response,
    timestamp: Date.now()
  });
}

/**
 * Generate a chat response using Ollama through our middleware
 * @param {string} query - The user's query
 * @param {string} language - The language to respond in
 * @param {string|null} userId - User ID for authenticated users
 * @returns {Promise<object>} - The generated response
 */
export async function generateChatResponse(query, language = 'english', userId = null) {
  try {
    console.log('Generating Ollama chat response for query:', query);
    
    // Check cache first
    const cached = getCachedResponse(query);
    if (cached) {
      console.log('Using cached response');
      return cached;
    }
    
    // Check rate limit
    if (!checkRateLimit(userId)) {
      console.warn('Rate limit exceeded for user:', userId || 'anonymous');
      return {
        content: `I'm currently handling a lot of requests. Please try again in a few minutes.`,
        category: 'error',
        language,
        cached: false
      };
    }
    
    // Call our middleware service
    const response = await fetch('/api/ai/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query,
        language,
        userId,
        model: 'mistral' // Default to mistral model
      })
    });
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Cache the response
    cacheResponse(query, data);
    
    return data;
  } catch (error) {
    console.error('Error in generateChatResponse:', error);
    
    // Try to get a fallback response from the database
    try {
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('ai_responses')
        .select('*')
        .ilike('keyword', `%${query.toLowerCase().split(' ')[0]}%`)
        .limit(1)
        .single();
      
      if (!fallbackError && fallbackData) {
        return {
          content: fallbackData.content,
          category: fallbackData.category || 'general',
          language,
          cached: false,
          source: 'fallback'
        };
      }
    } catch (fallbackDbError) {
      console.error('Error getting fallback response:', fallbackDbError);
    }
    
    throw error;
  }
}

/**
 * Submit feedback for a chat response
 * @param {string} query - The user's query
 * @param {object} response - The response object
 * @param {number} rating - Feedback rating (1: helpful, 0: neutral, -1: not helpful)
 * @param {string|null} comment - Optional comment
 * @param {string|null} userId - User ID for authenticated users
 * @returns {Promise<boolean>} - Whether the feedback was submitted successfully
 */
export async function submitFeedback(query, response, rating, comment = null, userId = null) {
  try {
    console.log('Submitting feedback for query:', query, 'rating:', rating);
    
    // Call our middleware service
    const apiResponse = await fetch('/api/ai/feedback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query,
        response,
        rating,
        comment,
        userId
      })
    });
    
    if (!apiResponse.ok) {
      throw new Error(`API error: ${apiResponse.status}`);
    }
    
    return true;
  } catch (error) {
    console.error('Error submitting feedback:', error);
    return false;
  }
}

export default {
  generateChatResponse,
  submitFeedback
};
