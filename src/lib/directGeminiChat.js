/**
 * Direct Gemini Chat Integration
 * 
 * This module provides direct integration with Google's Gemini API
 */

import { supabase } from './supabase';

// API key
const GEMINI_API_KEY = 'AIzaSyAkEun3_2XWuSxrJ745GQajn_6gscO12cU';

// Basic fallback responses
const fallbackResponses = {
  default: "Cybersecurity is about protecting systems, networks, and data from digital attacks. Key practices include using strong passwords, keeping software updated, being cautious with emails and downloads, using antivirus software, and backing up your data regularly."
};

/**
 * Generate a chat response using Gemini API
 * @param {string} query - The user's query
 * @param {string} language - The language to respond in
 * @param {string|null} userId - User ID for authenticated users
 * @returns {Promise<object>} - The generated response
 */
export async function generateChatResponse(query, language = 'english', userId = null) {
  try {
    console.log('Generating direct chat response for query:', query);

    // Make direct API call to Gemini
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              role: "user",
              parts: [{ 
                text: `You are CyberForce AI, an advanced cybersecurity assistant. Answer this question about cybersecurity in ${language}: ${query}` 
              }]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topP: 0.8,
            topK: 40,
            maxOutputTokens: 1024
          }
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Direct API call successful');

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Invalid response format from API');
    }

    const content = data.candidates[0].content.parts[0].text;
    console.log('Received content from API, length:', content.length);

    // Create response object
    const responseObj = {
      content: content,
      category: 'general',
      language,
      cached: false
    };

    return responseObj;
  } catch (error) {
    console.error('Error in generateChatResponse:', error);

    // Return a fallback response
    return {
      content: `I apologize, but I'm having trouble generating a response right now. Here's some basic information that might help:\n\n${fallbackResponses.default}\n\nPlease try again later with a more specific question.`,
      category: 'general',
      language,
      cached: false
    };
  }
}

/**
 * Submit feedback for a chat response
 * @param {string} query - The user's query
 * @param {object} response - The response object
 * @param {number} rating - Feedback rating (1: helpful, 0: neutral, -1: not helpful)
 * @param {string|null} comment - Optional comment
 * @param {string|null} userId - User ID for authenticated users
 * @returns {Promise<boolean>} - Whether the feedback was submitted successfully
 */
export async function submitFeedback(query, response, rating, comment = null, userId = null) {
  try {
    console.log('Submitting feedback for query:', query, 'rating:', rating);
    
    // Call our middleware service
    const apiResponse = await fetch('/api/ai/feedback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query,
        response,
        rating,
        comment,
        userId
      })
    });
    
    if (!apiResponse.ok) {
      throw new Error(`API error: ${apiResponse.status}`);
    }
    
    return true;
  } catch (error) {
    console.error('Error submitting feedback:', error);
    return false;
  }
}

export default {
  generateChatResponse,
  submitFeedback
};
