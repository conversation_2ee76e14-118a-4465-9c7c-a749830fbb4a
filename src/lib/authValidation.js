import { supabase } from './supabase';

// Production-ready authentication validation
export const validateCredentials = async (email, password) => {
  try {
    // Validate input
    if (!email || !password) {
      throw new Error('Email and password are required');
    }

    if (!isValidEmail(email)) {
      throw new Error('Please enter a valid email address');
    }

    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters long');
    }

    // Attempt authentication with Supabase
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email.toLowerCase().trim(),
      password: password
    });

    if (error) {
      // Handle specific Supabase errors
      switch (error.message) {
        case 'Invalid login credentials':
          throw new Error('Invalid email or password. Please check your credentials and try again.');
        case 'Email not confirmed':
          throw new Error('Please verify your email address before signing in.');
        case 'Too many requests':
          throw new Error('Too many login attempts. Please try again later.');
        default:
          throw new Error(error.message || 'Authentication failed');
      }
    }

    if (!data.session || !data.user) {
      throw new Error('Authentication failed. Please try again.');
    }

    // Verify user is active
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', data.user.id)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Profile fetch error:', profileError);
    }

    // Get user subscription
    const { data: subscription, error: subError } = await supabase
      .rpc('get_user_subscription', { p_user_id: data.user.id });

    if (subError) {
      console.error('Subscription fetch error:', subError);
    }

    return {
      success: true,
      session: data.session,
      user: data.user,
      profile: profile || null,
      subscription: subscription || { tier: 'free', active: true }
    };

  } catch (error) {
    console.error('Authentication validation error:', error);
    return {
      success: false,
      error: error.message || 'Authentication failed'
    };
  }
};

// Email validation helper
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Check if user session is valid
export const validateSession = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      throw error;
    }

    if (!session) {
      return { valid: false, session: null };
    }

    // Check if session is expired
    const now = Math.floor(Date.now() / 1000);
    if (session.expires_at && session.expires_at < now) {
      // Try to refresh the session
      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
      
      if (refreshError || !refreshData.session) {
        return { valid: false, session: null };
      }
      
      return { valid: true, session: refreshData.session };
    }

    return { valid: true, session };
  } catch (error) {
    console.error('Session validation error:', error);
    return { valid: false, session: null };
  }
};

// Secure logout
export const secureLogout = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.error('Logout error:', error);
    }

    // Clear all local storage
    localStorage.clear();
    sessionStorage.clear();

    return { success: true };
  } catch (error) {
    console.error('Secure logout error:', error);
    return { success: false, error: error.message };
  }
};

// Rate limiting for login attempts
const loginAttempts = new Map();

export const checkRateLimit = (email) => {
  const now = Date.now();
  const attempts = loginAttempts.get(email) || { count: 0, lastAttempt: 0 };
  
  // Reset counter if more than 15 minutes have passed
  if (now - attempts.lastAttempt > 15 * 60 * 1000) {
    attempts.count = 0;
  }
  
  // Check if rate limited (max 5 attempts per 15 minutes)
  if (attempts.count >= 5) {
    const timeLeft = Math.ceil((15 * 60 * 1000 - (now - attempts.lastAttempt)) / 1000 / 60);
    throw new Error(`Too many login attempts. Please try again in ${timeLeft} minutes.`);
  }
  
  // Increment attempt counter
  attempts.count++;
  attempts.lastAttempt = now;
  loginAttempts.set(email, attempts);
  
  return true;
};

// Clear rate limit on successful login
export const clearRateLimit = (email) => {
  loginAttempts.delete(email);
};
