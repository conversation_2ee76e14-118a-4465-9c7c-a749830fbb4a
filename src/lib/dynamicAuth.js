import { supabase } from './supabase';

// Enhanced auth service with dynamic user profiles
export class DynamicAuthService {
  constructor() {
    this.currentUser = null;
    this.currentProfile = null;
  }

  // Sign up with email verification
  async signUp(email, password, userData = {}) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.fullName || userData.full_name || email.split('@')[0],
            username: userData.username || email.split('@')[0],
            ...userData
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) throw error;

      return {
        user: data.user,
        session: data.session,
        needsVerification: !data.session // If no session, email verification is required
      };
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  // Sign in with profile loading
  async signIn(email, password) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;

      if (data.user) {
        // Load user profile
        const profile = await this.getUserProfile(data.user.id);
        this.currentUser = data.user;
        this.currentProfile = profile;

        // Update last login
        if (profile) {
          await this.updateUserProfile(data.user.id, { 
            last_login: new Date().toISOString() 
          });
        }
      }

      return {
        user: data.user,
        session: data.session,
        profile: this.currentProfile
      };
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  }

  // Sign out
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      this.currentUser = null;
      this.currentProfile = null;
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  // Get current user with profile
  async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;

      if (user && !this.currentProfile) {
        this.currentProfile = await this.getUserProfile(user.id);
      }

      this.currentUser = user;
      return {
        user,
        profile: this.currentProfile
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return { user: null, profile: null };
    }
  }

  // Get user profile
  async getUserProfile(userId) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Get user profile error:', error);
      return null;
    }
  }

  // Update user profile
  async updateUserProfile(userId, updates) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ 
          ...updates, 
          updated_at: new Date().toISOString() 
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      if (this.currentUser?.id === userId) {
        this.currentProfile = data;
      }

      return data;
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  }

  // Get user progress
  async getUserProgress(userId, learningPath = null) {
    try {
      let query = supabase
        .from('user_progress')
        .select('*')
        .eq('user_id', userId);

      if (learningPath) {
        query = query.eq('learning_path', learningPath);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Get user progress error:', error);
      return [];
    }
  }

  // Update module progress
  async updateModuleProgress(userId, learningPath, moduleId, progressData) {
    try {
      const { data, error } = await supabase
        .from('user_progress')
        .upsert({
          user_id: userId,
          learning_path: learningPath,
          module_id: moduleId,
          ...progressData,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Update module progress error:', error);
      throw error;
    }
  }

  // Get user enrollments
  async getUserEnrollments(userId) {
    try {
      const { data, error } = await supabase
        .from('user_enrollments')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Get user enrollments error:', error);
      return [];
    }
  }

  // Enroll in learning path
  async enrollInLearningPath(userId, learningPath) {
    try {
      const { data, error } = await supabase
        .from('user_enrollments')
        .upsert({
          user_id: userId,
          learning_path: learningPath,
          enrolled_at: new Date().toISOString(),
          status: 'active'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Enroll in learning path error:', error);
      throw error;
    }
  }

  // Get user achievements
  async getUserAchievements(userId) {
    try {
      const { data, error } = await supabase
        .from('user_achievements')
        .select('*')
        .eq('user_id', userId)
        .order('earned_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Get user achievements error:', error);
      return [];
    }
  }

  // Add achievement
  async addAchievement(userId, achievementType, achievementData = {}) {
    try {
      const { data, error } = await supabase
        .from('user_achievements')
        .insert({
          user_id: userId,
          achievement_type: achievementType,
          achievement_data: achievementData,
          earned_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Add achievement error:', error);
      throw error;
    }
  }

  // Reset password
  async resetPassword(email) {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  }

  // Resend confirmation email
  async resendConfirmation(email) {
    try {
      const { data, error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Resend confirmation error:', error);
      throw error;
    }
  }

  // Get session
  async getSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) throw error;
      return session;
    } catch (error) {
      console.error('Get session error:', error);
      return null;
    }
  }
}

// Create singleton instance
export const dynamicAuth = new DynamicAuthService();

// Export individual functions for backward compatibility
export const signUp = (email, password, userData) => dynamicAuth.signUp(email, password, userData);
export const signIn = (email, password) => dynamicAuth.signIn(email, password);
export const signOut = () => dynamicAuth.signOut();
export const getCurrentUser = () => dynamicAuth.getCurrentUser();
export const getUserProfile = (userId) => dynamicAuth.getUserProfile(userId);
export const updateUserProfile = (userId, updates) => dynamicAuth.updateUserProfile(userId, updates);
export const getUserProgress = (userId, learningPath) => dynamicAuth.getUserProgress(userId, learningPath);
export const updateModuleProgress = (userId, learningPath, moduleId, progressData) => 
  dynamicAuth.updateModuleProgress(userId, learningPath, moduleId, progressData);
export const getUserEnrollments = (userId) => dynamicAuth.getUserEnrollments(userId);
export const enrollInLearningPath = (userId, learningPath) => dynamicAuth.enrollInLearningPath(userId, learningPath);
export const getUserAchievements = (userId) => dynamicAuth.getUserAchievements(userId);
export const addAchievement = (userId, achievementType, achievementData) => 
  dynamicAuth.addAchievement(userId, achievementType, achievementData);
export const resetPassword = (email) => dynamicAuth.resetPassword(email);
export const resendConfirmation = (email) => dynamicAuth.resendConfirmation(email);
export const getSession = () => dynamicAuth.getSession();
