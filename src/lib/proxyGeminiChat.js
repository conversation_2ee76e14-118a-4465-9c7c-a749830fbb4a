/**
 * Proxy-based Gemini Chat Integration
 *
 * This module provides integration with Google's Gemini API through a server-side proxy
 * to avoid CORS issues and provide better error handling.
 */

import { supabase } from './supabase';

// Cache for storing recent responses to avoid duplicate API calls
const responseCache = new Map();
const CACHE_EXPIRY = 1000 * 60 * 60; // 1 hour

// Basic fallback responses for common cybersecurity topics
const fallbackResponses = {
  password: "Strong passwords are essential for cybersecurity. Use a combination of uppercase and lowercase letters, numbers, and special characters. Aim for at least 12 characters, and avoid using easily guessable information like birthdays or names.",
  phishing: "Phishing attacks try to trick you into revealing sensitive information. Always verify the sender's email address, be suspicious of unexpected attachments, and never click on suspicious links. When in doubt, contact the supposed sender through official channels.",
  malware: "Malware is malicious software designed to damage or gain unauthorized access to systems. Protect yourself by keeping software updated, using antivirus programs, avoiding suspicious downloads, and backing up your data regularly.",
  network: "Network security involves protecting your computer networks from intrusions and attacks. Use firewalls, secure your Wi-Fi with strong encryption (WPA3 if possible), regularly update router firmware, and consider using a VPN for additional privacy.",
  encryption: "Encryption converts data into a code to prevent unauthorized access. It's essential for protecting sensitive information. Always use HTTPS websites, encrypt your devices and sensitive files, and use end-to-end encrypted messaging apps.",
  firewall: "A firewall is a security system that monitors and controls incoming and outgoing network traffic. It acts as a barrier between a trusted network and untrusted networks. Enable your operating system's built-in firewall, and consider using application-level firewalls for additional protection.",
  hacking: "Ethical hacking involves authorized attempts to gain unauthorized access to systems to identify vulnerabilities. Always get proper authorization before testing security, follow responsible disclosure practices, and never use your skills for malicious purposes.",
  ransomware: "Ransomware is malware that encrypts your files and demands payment for the decryption key. Protect yourself by keeping regular backups, being cautious with email attachments, keeping software updated, and using security software.",
  vpn: "A Virtual Private Network (VPN) creates a secure, encrypted connection over a less secure network. Use a VPN to protect your privacy when using public Wi-Fi, access geo-restricted content, and hide your browsing activity from your ISP.",
  social: "Social engineering attacks manipulate people into breaking security procedures. Be skeptical of unsolicited contacts, verify identities through official channels, be wary of offers that seem too good to be true, and never share sensitive information in response to unsolicited requests.",
  default: "Cybersecurity is about protecting systems, networks, and data from digital attacks. Key practices include using strong passwords, keeping software updated, being cautious with emails and downloads, using antivirus software, and backing up your data regularly."
};

/**
 * Get a cached response if available
 * @param {string} query - The user's query
 * @returns {object|null} - Cached response or null
 */
function getCachedResponse(query) {
  const normalizedQuery = query.toLowerCase().trim();

  if (responseCache.has(normalizedQuery)) {
    const { response, timestamp } = responseCache.get(normalizedQuery);

    // Check if cache is still valid
    if (Date.now() - timestamp < CACHE_EXPIRY) {
      return { ...response, cached: true };
    } else {
      // Remove expired cache entry
      responseCache.delete(normalizedQuery);
    }
  }

  return null;
}

/**
 * Cache a response for future use
 * @param {string} query - The user's query
 * @param {object} response - The response to cache
 */
function cacheResponse(query, response) {
  const normalizedQuery = query.toLowerCase().trim();
  responseCache.set(normalizedQuery, {
    response,
    timestamp: Date.now()
  });
}

/**
 * Generate a chat response using Gemini API through proxy
 * @param {string} query - The user's query
 * @param {string} language - The language to respond in
 * @param {string|null} userId - User ID for authenticated users
 * @returns {Promise<object>} - The generated response
 */
export async function generateChatResponse(query, language = 'english', userId = null) {
  try {
    console.log('Generating chat response via proxy for query:', query);

    // Check cache first
    const cached = getCachedResponse(query);
    if (cached) {
      console.log('Using cached response');
      return cached;
    }

    // Try to get response from database first
    try {
      const { data: dbResponse, error } = await supabase
        .from('ai_responses')
        .select('*')
        .textSearch('keywords', query.toLowerCase())
        .limit(1)
        .single();

      if (!error && dbResponse) {
        console.log('Using database response');
        return {
          content: dbResponse.content,
          category: dbResponse.category || 'general',
          language,
          cached: false
        };
      }
    } catch (dbError) {
      console.log('No matching response in database');
    }

    // Make a direct API call to Gemini
    console.log('Making direct API call to Gemini');

    // API key
    const GEMINI_API_KEY = 'AIzaSyAkEun3_2XWuSxrJ745GQajn_6gscO12cU';

    // First try direct API call
    const apiResponse = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${GEMINI_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              role: "user",
              parts: [{
                text: `You are CyberForce AI, an advanced cybersecurity assistant. Answer this question about cybersecurity in ${language}: ${query}`
              }]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topP: 0.8,
            topK: 40,
            maxOutputTokens: 1024
          }
        }),
      }
    );

    if (!apiResponse.ok) {
      throw new Error(`API error: ${apiResponse.status}`);
    }

    const apiData = await apiResponse.json();
    console.log('Direct API call successful');

    if (!apiData.candidates || !apiData.candidates[0] || !apiData.candidates[0].content) {
      throw new Error('Invalid response format from API');
    }

    const content = apiData.candidates[0].content.parts[0].text;
    console.log('Received content from API, length:', content.length);

    // Create data object in the expected format
    const data = {
      content: content,
      category: 'general'
    };

    console.log('Received response from API:', data);

    // Create response object
    const responseObj = {
      content: data.content,
      category: data.category || 'general',
      language,
      cached: false
    };

    // Cache the response
    cacheResponse(query, responseObj);

    // Save to database if user is authenticated
    if (userId) {
      try {
        await supabase.from('ai_responses').insert({
          keyword: query.toLowerCase(),
          content: data.content,
          category: data.category || 'general',
          language,
          user_id: userId
        });
      } catch (saveError) {
        console.error('Error saving response to database:', saveError);
      }
    }

    return responseObj;
  } catch (error) {
    console.error('Error in generateChatResponse:', error);

    // Try to get a fallback response from the database
    try {
      console.log('Attempting to get fallback response from database');
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('ai_responses')
        .select('*')
        .ilike('keywords', `%${query.toLowerCase().split(' ')[0]}%`)
        .limit(1)
        .single();

      if (!fallbackError && fallbackData) {
        console.log('Found fallback response in database');
        return {
          content: fallbackData.content,
          category: fallbackData.category || 'general',
          language,
          cached: false
        };
      }
    } catch (fallbackDbError) {
      console.error('Error getting fallback response:', fallbackDbError);
    }

    // Use keyword-based fallback
    const queryLower = query.toLowerCase();
    let fallbackContent = fallbackResponses.default;
    let category = 'general';

    // Check for keywords in the query to provide a more relevant fallback
    if (queryLower.includes('password')) {
      fallbackContent = fallbackResponses.password;
      category = 'security';
    } else if (queryLower.includes('phish')) {
      fallbackContent = fallbackResponses.phishing;
      category = 'security';
    } else if (queryLower.includes('malware') || queryLower.includes('virus')) {
      fallbackContent = fallbackResponses.malware;
      category = 'security';
    } else if (queryLower.includes('network')) {
      fallbackContent = fallbackResponses.network;
      category = 'technical';
    } else if (queryLower.includes('encrypt')) {
      fallbackContent = fallbackResponses.encryption;
      category = 'technical';
    } else if (queryLower.includes('firewall')) {
      fallbackContent = fallbackResponses.firewall;
      category = 'technical';
    } else if (queryLower.includes('hack')) {
      fallbackContent = fallbackResponses.hacking;
      category = 'security';
    } else if (queryLower.includes('ransom')) {
      fallbackContent = fallbackResponses.ransomware;
      category = 'security';
    } else if (queryLower.includes('vpn')) {
      fallbackContent = fallbackResponses.vpn;
      category = 'technical';
    } else if (queryLower.includes('social') || queryLower.includes('engineer')) {
      fallbackContent = fallbackResponses.social;
      category = 'security';
    }

    return {
      content: `I apologize, but I'm having trouble generating a response right now. Here's some basic information that might help:\n\n${fallbackContent}\n\nPlease try again later with a more specific question.`,
      category,
      language,
      cached: false
    };
  }
}

/**
 * Submit feedback for a chat response
 * @param {string} query - The user's query
 * @param {object} response - The response object
 * @param {number} rating - Feedback rating (1: helpful, 0: neutral, -1: not helpful)
 * @param {string|null} comment - Optional comment
 * @param {string|null} userId - User ID for authenticated users
 * @returns {Promise<boolean>} - Whether the feedback was submitted successfully
 */
export async function submitFeedback(query, response, rating, comment = null, userId = null) {
  try {
    console.log('Submitting feedback for query:', query, 'rating:', rating);

    // Call our middleware service
    const apiResponse = await fetch('/api/ai/feedback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query,
        response,
        rating,
        comment,
        userId
      })
    });

    if (!apiResponse.ok) {
      throw new Error(`API error: ${apiResponse.status}`);
    }

    return true;
  } catch (error) {
    console.error('Error submitting feedback:', error);
    return false;
  }
}

export default {
  generateChatResponse,
  submitFeedback
};
