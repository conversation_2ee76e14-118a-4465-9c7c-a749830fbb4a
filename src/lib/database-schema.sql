-- =============================================
-- COMPLETE DATABASE SCHEMA FOR CYBERFORCE
-- =============================================
-- This schema includes all tables needed for learning paths, progress tracking, and features

-- =============================================
-- 1. USER PROFILES AND AUTHENTICATION
-- =============================================

-- User profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE,
    full_name VARCHAR(100),
    avatar_url TEXT,
    bio TEXT,
    total_coins INTEGER DEFAULT 0,
    total_points INTEGER DEFAULT 0,
    total_xp INTEGER DEFAULT 0,
    modules_completed INTEGER DEFAULT 0,
    challenges_completed INTEGER DEFAULT 0,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    subscription_tier VARCHAR(20) DEFAULT 'free', -- 'free', 'premium'
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 2. LEARNING PATHS SYSTEM
-- =============================================

-- Learning path metadata
CREATE TABLE IF NOT EXISTS learning_path_metadata (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    difficulty TEXT DEFAULT 'Intermediate' CHECK (difficulty IN ('Beginner', 'Intermediate', 'Advanced', 'Expert')),
    estimated_hours INTEGER DEFAULT 0,
    module_count INTEGER DEFAULT 0,
    prerequisites TEXT[], -- Array of prerequisite path IDs
    skills TEXT[], -- Array of skills learned
    icon TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning module metadata
CREATE TABLE IF NOT EXISTS learning_module_metadata (
    id TEXT PRIMARY KEY, -- Format: "path-id-module-id"
    path_id TEXT NOT NULL,
    module_order INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    difficulty TEXT DEFAULT 'Intermediate' CHECK (difficulty IN ('Beginner', 'Intermediate', 'Advanced', 'Expert')),
    estimated_time_minutes INTEGER DEFAULT 30,
    xp_reward INTEGER DEFAULT 100,
    objectives TEXT[], -- Array of learning objectives
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique order per path
    UNIQUE(path_id, module_order)
);

-- User learning path enrollments
CREATE TABLE IF NOT EXISTS learning_path_enrollments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    path_id TEXT NOT NULL,
    enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    current_module INTEGER DEFAULT 1,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique enrollment per user per path
    UNIQUE(user_id, path_id)
);

-- User learning module progress
CREATE TABLE IF NOT EXISTS learning_module_progress (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    module_id TEXT NOT NULL, -- Format: "path-id-module-id"
    path_id TEXT NOT NULL,
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'started', 'in_progress', 'completed')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    time_spent INTEGER DEFAULT 0, -- in seconds
    xp_earned INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique progress per user per module
    UNIQUE(user_id, module_id)
);

-- =============================================
-- 3. START HACK AND CHALLENGER FEATURES
-- =============================================

-- Start Hack Simulations
CREATE TABLE IF NOT EXISTS start_hack_simulations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE,
  description TEXT NOT NULL,
  simulation_type TEXT NOT NULL,
  points INTEGER NOT NULL DEFAULT 100,
  estimated_time INTEGER, -- in minutes
  is_premium BOOLEAN DEFAULT false,
  is_business BOOLEAN DEFAULT false,
  objectives JSONB,
  completion_criteria JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Start Hack Simulation Attempts
CREATE TABLE IF NOT EXISTS start_hack_simulation_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  simulation_id UUID REFERENCES start_hack_simulations(id) ON DELETE CASCADE,
  is_completed BOOLEAN DEFAULT false,
  completion_time INTEGER, -- in seconds
  approach_score INTEGER,
  total_score INTEGER,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, simulation_id)
);

-- Start Hack Simulation Progress
CREATE TABLE IF NOT EXISTS start_hack_simulation_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  simulation_id UUID REFERENCES start_hack_simulations(id) ON DELETE CASCADE,
  step_id TEXT NOT NULL,
  is_completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, simulation_id, step_id)
);

-- Challenger Challenges
CREATE TABLE IF NOT EXISTS challenger_challenges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE,
  description TEXT NOT NULL,
  challenge_type TEXT NOT NULL,
  points INTEGER NOT NULL DEFAULT 100,
  estimated_time INTEGER, -- in minutes
  is_premium BOOLEAN DEFAULT false,
  is_business BOOLEAN DEFAULT false,
  objectives JSONB,
  completion_criteria JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

-- Challenger Challenge Attempts
CREATE TABLE IF NOT EXISTS challenger_challenge_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  challenge_id UUID REFERENCES challenger_challenges(id) ON DELETE CASCADE,
  is_completed BOOLEAN DEFAULT false,
  completion_time INTEGER, -- in seconds
  approach_score INTEGER,
  total_score INTEGER,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(user_id, challenge_id)
);

-- =============================================
-- 4. DAILY CHALLENGES AND ACHIEVEMENTS
-- =============================================

-- Daily challenges
CREATE TABLE IF NOT EXISTS daily_challenges (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    challenge_date DATE NOT NULL DEFAULT CURRENT_DATE,
    difficulty TEXT DEFAULT 'Intermediate' CHECK (difficulty IN ('Beginner', 'Intermediate', 'Advanced')),
    xp_reward INTEGER DEFAULT 50,
    coin_reward INTEGER DEFAULT 10,
    challenge_type TEXT DEFAULT 'quiz' CHECK (challenge_type IN ('quiz', 'practical', 'reading')),
    content JSONB, -- Challenge content/questions
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique challenge per date
    UNIQUE(challenge_date)
);

-- User daily challenge progress
CREATE TABLE IF NOT EXISTS user_daily_challenges (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    challenge_id UUID NOT NULL REFERENCES daily_challenges(id) ON DELETE CASCADE,
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    xp_earned INTEGER DEFAULT 0,
    coins_earned INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique attempt per user per challenge
    UNIQUE(user_id, challenge_id)
);

-- Achievements
CREATE TABLE IF NOT EXISTS achievements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    icon TEXT,
    category TEXT NOT NULL, -- 'learning', 'streak', 'completion', 'special'
    criteria JSONB NOT NULL, -- Achievement criteria
    xp_reward INTEGER DEFAULT 100,
    coin_reward INTEGER DEFAULT 50,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User achievements
CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_id UUID NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    xp_earned INTEGER DEFAULT 0,
    coins_earned INTEGER DEFAULT 0,

    -- Ensure unique achievement per user
    UNIQUE(user_id, achievement_id)
);

-- =============================================
-- 5. ACTIVITY TRACKING
-- =============================================

-- Daily learning activity tracking
CREATE TABLE IF NOT EXISTS daily_learning_activity (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_date DATE NOT NULL DEFAULT CURRENT_DATE,
    modules_completed INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    xp_earned INTEGER DEFAULT 0,
    paths_worked_on TEXT[], -- Array of path IDs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique activity per user per day
    UNIQUE(user_id, activity_date)
);

-- User learning statistics (aggregated data)
CREATE TABLE IF NOT EXISTS user_learning_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    total_study_time_minutes INTEGER DEFAULT 0,
    total_modules_completed INTEGER DEFAULT 0,
    total_paths_completed INTEGER DEFAULT 0,
    current_learning_streak INTEGER DEFAULT 0,
    longest_learning_streak INTEGER DEFAULT 0,
    favorite_category TEXT,
    last_activity_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Leaderboards
CREATE TABLE IF NOT EXISTS leaderboards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  challenge_id UUID REFERENCES challenger_challenges(id) ON DELETE SET NULL,
  simulation_id UUID REFERENCES start_hack_simulations(id) ON DELETE SET NULL,
  completion_time INTEGER NOT NULL, -- in seconds
  approach_score INTEGER,
  total_score INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, challenge_id),
  UNIQUE(user_id, simulation_id),
  CHECK (
    (challenge_id IS NOT NULL AND simulation_id IS NULL) OR
    (challenge_id IS NULL AND simulation_id IS NOT NULL)
  )
);

-- =============================================
-- 6. SAMPLE DATA FOR LEARNING PATHS
-- =============================================

-- Insert learning path metadata
INSERT INTO learning_path_metadata (id, title, description, category, difficulty, estimated_hours, module_count, skills, icon) VALUES
('networking-fundamentals', 'Network Fundamentals', 'Master the core concepts of computer networking, protocols, and network security fundamentals.', 'IT', 'Beginner', 8, 5, ARRAY['TCP/IP', 'Network Security', 'Routing', 'Switching'], 'network'),
('red-teaming', 'Advanced Red Teaming', 'Advanced red team operations, adversary simulation, and persistent threat techniques.', 'Red Team', 'Advanced', 12, 5, ARRAY['Penetration Testing', 'Social Engineering', 'Post-Exploitation'], 'shield-alert'),
('blue-teaming', 'Blue Team Defense', 'Defensive cybersecurity techniques, incident response, and threat hunting methodologies.', 'Blue Team', 'Intermediate', 10, 5, ARRAY['Incident Response', 'Threat Hunting', 'SIEM'], 'shield-check')
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  description = EXCLUDED.description,
  category = EXCLUDED.category,
  difficulty = EXCLUDED.difficulty,
  estimated_hours = EXCLUDED.estimated_hours,
  module_count = EXCLUDED.module_count,
  skills = EXCLUDED.skills,
  icon = EXCLUDED.icon,
  updated_at = NOW();

-- Insert learning module metadata for networking fundamentals
INSERT INTO learning_module_metadata (id, path_id, module_order, title, description, difficulty, estimated_time_minutes, xp_reward, objectives) VALUES
('networking-fundamentals-module-1', 'networking-fundamentals', 1, 'Introduction to Computer Networks', 'Learn the fundamental concepts of computer networks and their importance in modern computing and cybersecurity.', 'Beginner', 60, 100, ARRAY['Define what a computer network is', 'Identify different network topologies', 'Distinguish between network types']),
('networking-fundamentals-module-2', 'networking-fundamentals', 2, 'TCP/IP Protocol Suite', 'Deep dive into the TCP/IP protocol stack and understand how data flows across networks.', 'Beginner', 75, 150, ARRAY['Understand TCP/IP layers', 'Learn about IP addressing', 'Explore routing concepts']),
('networking-fundamentals-module-3', 'networking-fundamentals', 3, 'Network Security Basics', 'Introduction to network security concepts and threat landscape.', 'Intermediate', 90, 200, ARRAY['Identify network threats', 'Understand security controls', 'Learn about firewalls']),
('networking-fundamentals-module-4', 'networking-fundamentals', 4, 'Routing and Switching', 'Learn routing protocols and switching concepts for network infrastructure.', 'Intermediate', 85, 175, ARRAY['Configure basic routing', 'Understand switching', 'Learn VLANs']),
('networking-fundamentals-module-5', 'networking-fundamentals', 5, 'Wireless Security', 'Explore wireless networking technologies and security considerations.', 'Intermediate', 70, 150, ARRAY['Understand Wi-Fi security', 'Learn about wireless attacks', 'Configure secure wireless'])
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  description = EXCLUDED.description,
  difficulty = EXCLUDED.difficulty,
  estimated_time_minutes = EXCLUDED.estimated_time_minutes,
  xp_reward = EXCLUDED.xp_reward,
  objectives = EXCLUDED.objectives,
  updated_at = NOW();

-- Insert sample achievements
INSERT INTO achievements (title, description, icon, category, criteria, xp_reward, coin_reward) VALUES
('First Steps', 'Complete your first learning module', 'trophy', 'learning', '{"type": "modules_completed", "count": 1}', 50, 25),
('Network Explorer', 'Complete the Network Fundamentals path', 'network', 'completion', '{"type": "path_completed", "path_id": "networking-fundamentals"}', 500, 100),
('Streak Master', 'Maintain a 7-day learning streak', 'flame', 'streak', '{"type": "learning_streak", "days": 7}', 200, 50),
('Knowledge Seeker', 'Complete 10 learning modules', 'book-open', 'learning', '{"type": "modules_completed", "count": 10}', 300, 75)
ON CONFLICT (title) DO NOTHING;

-- =============================================
-- 7. SAMPLE DATA FOR START HACK AND CHALLENGER
-- =============================================

-- Sample data for Start Hack simulations
INSERT INTO start_hack_simulations (title, slug, description, simulation_type, points, estimated_time, is_premium, is_business, objectives, completion_criteria)
VALUES
  ('Server Breach Simulation', 'server-breach-simulation', 'Simulate a server breach to understand attack vectors and defense mechanisms.', 'server_breach', 200, 30, false, false,
   '[{"id": 1, "description": "Identify vulnerabilities in the server"}, {"id": 2, "description": "Exploit the vulnerabilities to gain access"}, {"id": 3, "description": "Extract sensitive data"}]',
   '{"flag_format": "flag{...}", "time_limit": 1800}'
  ),
  ('Network Penetration Simulation', 'network-penetration-simulation', 'Perform a network penetration test to identify and exploit vulnerabilities.', 'network_penetration', 300, 45, true, false,
   '[{"id": 1, "description": "Perform network reconnaissance"}, {"id": 2, "description": "Identify vulnerable services"}, {"id": 3, "description": "Exploit vulnerabilities to gain access"}, {"id": 4, "description": "Escalate privileges"}]',
   '{"flag_format": "flag{...}", "time_limit": 2700}'
  ),
  ('Web Application Exploitation', 'web-application-exploitation', 'Exploit vulnerabilities in a web application to gain unauthorized access.', 'web_exploit', 250, 40, false, false,
   '[{"id": 1, "description": "Identify vulnerabilities in the web application"}, {"id": 2, "description": "Exploit the vulnerabilities to gain access"}, {"id": 3, "description": "Extract sensitive data"}]',
   '{"flag_format": "flag{...}", "time_limit": 2400}'
  )
ON CONFLICT (slug) DO NOTHING;

-- Sample data for Challenger challenges
INSERT INTO challenger_challenges (title, slug, description, challenge_type, points, estimated_time, is_premium, is_business, objectives, completion_criteria)
VALUES
  ('Web Application Exploitation Challenge', 'web-exploit-challenge', 'Exploit vulnerabilities in a web application to gain unauthorized access.', 'web_exploit', 250, 45, false, false,
   '[{"id": 1, "description": "Identify vulnerabilities in the web application"}, {"id": 2, "description": "Exploit the vulnerabilities to gain access"}, {"id": 3, "description": "Extract the secret flag"}]',
   '{"flag_format": "flag{...}", "time_limit": 3600}'
  ),
  ('Network Penetration Testing Challenge', 'network-penetration-challenge', 'Perform a penetration test on a simulated network to identify and exploit vulnerabilities.', 'network_penetration', 500, 60, true, false,
   '[{"id": 1, "description": "Perform network reconnaissance"}, {"id": 2, "description": "Identify vulnerable services"}, {"id": 3, "description": "Exploit vulnerabilities to gain access"}, {"id": 4, "description": "Escalate privileges"}, {"id": 5, "description": "Extract the secret flag"}]',
   '{"flag_format": "flag{...}", "time_limit": 7200}'
  )
ON CONFLICT (slug) DO NOTHING;

-- =============================================
-- 8. INDEXES FOR PERFORMANCE
-- =============================================

-- Learning path indexes
CREATE INDEX IF NOT EXISTS idx_learning_path_enrollments_user_id ON learning_path_enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_path_enrollments_path_id ON learning_path_enrollments(path_id);
CREATE INDEX IF NOT EXISTS idx_learning_module_progress_user_id ON learning_module_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_module_progress_module_id ON learning_module_progress(module_id);
CREATE INDEX IF NOT EXISTS idx_learning_module_progress_path_id ON learning_module_progress(path_id);

-- Activity tracking indexes
CREATE INDEX IF NOT EXISTS idx_daily_learning_activity_user_date ON daily_learning_activity(user_id, activity_date);
CREATE INDEX IF NOT EXISTS idx_user_daily_challenges_user_id ON user_daily_challenges(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id);

-- User profile indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);

-- =============================================
-- 9. ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on all user-specific tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_path_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_module_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_daily_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_learning_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_learning_statistics ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Learning progress policies
CREATE POLICY "Users can view own enrollments" ON learning_path_enrollments FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own enrollments" ON learning_path_enrollments FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own module progress" ON learning_module_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own module progress" ON learning_module_progress FOR ALL USING (auth.uid() = user_id);

-- Activity tracking policies
CREATE POLICY "Users can view own daily challenges" ON user_daily_challenges FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own daily challenges" ON user_daily_challenges FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own achievements" ON user_achievements FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own achievements" ON user_achievements FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own activity" ON daily_learning_activity FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own activity" ON daily_learning_activity FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own statistics" ON user_learning_statistics FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own statistics" ON user_learning_statistics FOR ALL USING (auth.uid() = user_id);

-- Public read access for metadata tables
ALTER TABLE learning_path_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_module_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_challenges ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view learning path metadata" ON learning_path_metadata FOR SELECT USING (is_active = true);
CREATE POLICY "Anyone can view learning module metadata" ON learning_module_metadata FOR SELECT USING (is_active = true);
CREATE POLICY "Anyone can view achievements" ON achievements FOR SELECT USING (is_active = true);
CREATE POLICY "Anyone can view daily challenges" ON daily_challenges FOR SELECT USING (is_active = true);
