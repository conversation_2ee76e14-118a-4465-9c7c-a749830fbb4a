import { supabase } from './supabase';
import fs from 'fs';
import path from 'path';

/**
 * Initialize the database with the required tables and sample data
 * This script should be run once to set up the database
 */
const initDatabase = async () => {
  try {
    console.log('Initializing database...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'database-schema.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql.split(';').filter(statement => statement.trim() !== '');
    
    // Execute each statement
    for (const statement of statements) {
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      if (error) {
        console.error('Error executing SQL statement:', error);
        console.error('Statement:', statement);
      }
    }
    
    console.log('Database initialization complete!');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
};

export default initDatabase;
