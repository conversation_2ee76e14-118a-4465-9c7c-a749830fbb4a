/**
 * Mock LLM Implementation
 * 
 * This module provides a reliable mock implementation of an LLM for development purposes.
 * It returns pre-defined responses based on keywords in the query.
 */

// Mock responses for different cybersecurity topics
const mockResponses = {
  password: {
    content: `# Password Security Best Practices

Strong passwords are essential for cybersecurity. Here are some key practices:

1. **Use complex passwords** with a mix of uppercase, lowercase, numbers, and special characters
2. **Make passwords at least 12 characters** long
3. **Don't reuse passwords** across different accounts
4. **Use a password manager** to generate and store strong passwords
5. **Enable two-factor authentication (2FA)** whenever possible
6. **Change passwords regularly**, especially for critical accounts
7. **Avoid using personal information** like birthdays or names

Remember, a password is only as secure as the practices around it!`,
    category: 'security'
  },
  
  phishing: {
    content: `# Recognizing and Avoiding Phishing Attacks

Phishing attacks try to trick you into revealing sensitive information. Here's how to stay safe:

1. **Check email sender addresses** carefully - look for misspellings or unusual domains
2. **Be suspicious of unexpected attachments** or requests for personal information
3. **Don't click on suspicious links** - hover over them to see the actual URL first
4. **Look for poor grammar or spelling** - many phishing attempts contain obvious errors
5. **Verify requests through official channels** if you're unsure
6. **Use anti-phishing tools** in your email and browser
7. **Report suspected phishing** to your IT department or email provider

Remember: legitimate organizations will never ask for sensitive information via email!`,
    category: 'security'
  },
  
  malware: {
    content: `# Protecting Against Malware

Malware is malicious software designed to damage systems or steal data. Here's how to protect yourself:

1. **Keep all software updated** with the latest security patches
2. **Use reputable antivirus/anti-malware software** and keep it updated
3. **Be careful what you download** - only use trusted sources
4. **Scan files before opening** them, especially email attachments
5. **Use a firewall** to monitor network traffic
6. **Back up your data regularly** to recover from ransomware attacks
7. **Be cautious with removable media** like USB drives

Prevention is always better than trying to remove malware after infection!`,
    category: 'security'
  },
  
  network: {
    content: `# Network Security Fundamentals

Network security involves protecting your computer networks from intrusions and attacks:

1. **Use strong encryption** for your Wi-Fi (WPA3 if possible)
2. **Change default router passwords** and update firmware regularly
3. **Enable and configure firewalls** on both network and device levels
4. **Segment your network** to limit access between different parts
5. **Use a VPN** when connecting to public Wi-Fi
6. **Implement access controls** to limit who can access your network
7. **Monitor network traffic** for unusual patterns that might indicate a breach

A secure network is your first line of defense against many cyber threats!`,
    category: 'technical'
  },
  
  encryption: {
    content: `# Understanding Encryption

Encryption converts data into a code to prevent unauthorized access:

1. **Always use HTTPS websites** - look for the padlock icon in your browser
2. **Encrypt sensitive files** on your devices
3. **Use end-to-end encrypted messaging apps** for sensitive communications
4. **Enable full-disk encryption** on your devices
5. **Use encrypted connections (SSH, SFTP)** instead of unencrypted ones
6. **Be aware of encryption limitations** - it protects data, not the endpoints
7. **Keep encryption keys secure** and separate from the encrypted data

Encryption is a powerful tool, but it must be implemented correctly to be effective!`,
    category: 'technical'
  },
  
  hacking: {
    content: `# Ethical Hacking Explained

Ethical hacking involves authorized attempts to gain unauthorized access to systems:

1. **Always get proper authorization** before testing security
2. **Document your scope and methodology** before starting
3. **Follow responsible disclosure practices** when finding vulnerabilities
4. **Never exceed your authorized scope** during testing
5. **Protect any sensitive data** you encounter during testing
6. **Provide clear documentation** of findings and remediation steps
7. **Stay updated on the latest techniques** and vulnerabilities

Ethical hacking helps organizations find and fix vulnerabilities before malicious hackers can exploit them!`,
    category: 'technical'
  },
  
  default: {
    content: `# Cybersecurity Fundamentals

Cybersecurity is about protecting systems, networks, and data from digital attacks:

1. **Use strong, unique passwords** for all accounts
2. **Keep all software and devices updated** with security patches
3. **Be cautious with emails and downloads** - verify before clicking
4. **Use reputable security software** and keep it updated
5. **Back up your data regularly** using the 3-2-1 rule (3 copies, 2 different media types, 1 offsite)
6. **Enable two-factor authentication** whenever possible
7. **Be careful what you share online** - once it's out there, it's hard to take back

The best security combines good technology with aware users and strong policies!`,
    category: 'general'
  }
};

/**
 * Generate a chat response using the mock LLM
 * @param {string} query - The user's query
 * @param {string} language - The language to respond in (ignored in mock)
 * @param {string|null} userId - User ID for authenticated users (ignored in mock)
 * @returns {Promise<object>} - The generated response
 */
export async function generateChatResponse(query, language = 'english', userId = null) {
  console.log('Generating mock response for query:', query);
  
  // Simulate network delay for realism
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Convert query to lowercase for keyword matching
  const queryLower = query.toLowerCase();
  
  // Find the most relevant response based on keywords
  let response;
  
  if (queryLower.includes('password')) {
    response = mockResponses.password;
  } else if (queryLower.includes('phish')) {
    response = mockResponses.phishing;
  } else if (queryLower.includes('malware') || queryLower.includes('virus')) {
    response = mockResponses.malware;
  } else if (queryLower.includes('network')) {
    response = mockResponses.network;
  } else if (queryLower.includes('encrypt')) {
    response = mockResponses.encryption;
  } else if (queryLower.includes('hack')) {
    response = mockResponses.hacking;
  } else {
    // If no specific keywords match, generate a response based on the query
    // This simulates a more dynamic response
    response = {
      content: `# Response to: "${query}"\n\n${mockResponses.default.content}\n\nFor more specific information about "${query}", please ask a more detailed question.`,
      category: 'general'
    };
  }
  
  // Return the response in the expected format
  return {
    content: response.content,
    category: response.category,
    language: language,
    cached: false
  };
}

/**
 * Submit feedback for a chat response (mock implementation)
 * @param {string} query - The user's query
 * @param {object} response - The response object
 * @param {number} rating - Feedback rating (1: helpful, 0: neutral, -1: not helpful)
 * @param {string|null} comment - Optional comment
 * @param {string|null} userId - User ID for authenticated users
 * @returns {Promise<boolean>} - Whether the feedback was submitted successfully
 */
export async function submitFeedback(query, response, rating, comment = null, userId = null) {
  console.log('Mock feedback submission:', { query, rating, comment, userId });
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Always return success
  return true;
}

export default {
  generateChatResponse,
  submitFeedback
};
