/* Module Content Visibility Fix for Dark Mode */

/* Fix for SVG text elements */
svg text {
  fill: #ffffff !important; /* Ensure SVG text is white */
}

/* Fix for SVG paths and lines */
svg path, svg line {
  stroke: #ffffff !important; /* Ensure SVG paths and lines are white */
}

/* Fix for SVG rectangles with light backgrounds */
svg rect[fill="#f0f0f0"] {
  fill: #2a3548 !important; /* Darker background for better contrast */
  stroke: #4a5568 !important; /* Darker stroke for better contrast */
}

/* Fix for SVG rectangles with light blue backgrounds */
svg rect[fill="#e6f7ff"] {
  fill: #1e3a5f !important; /* Darker blue background for better contrast */
  stroke: #3182ce !important; /* Darker blue stroke for better contrast */
}

/* Fix for module content text */
.learning-module-story p,
.learning-module-story li,
.interactive-diagram + p,
.interactive-diagram ~ p,
.interactive-diagram ~ ul li,
.interactive-diagram ~ ol li,
.interactive-diagram ~ h3,
.interactive-diagram ~ h4,
.feature-cards p,
.feature-card p,
.feature-card h3,
.troubleshooting-cards p,
.trouble-card p,
.trouble-card h4,
.trouble-card li,
.option-cards p,
.option-card p,
.option-card h4,
.exercise-container p,
.exercise-steps p,
.step p,
.step h4,
.step li,
.quiz-container p,
.quiz-question p,
.quiz-question h3,
.quiz-options label,
.quiz-feedback p {
  color: #ffffff !important; /* Ensure text is white */
}

/* Fix for headings */
h2, h3, h4, h5, h6 {
  color: #ffffff !important; /* Ensure headings are white */
}

/* Fix for paragraphs */
p {
  color: #e2e8f0 !important; /* Light gray for better readability */
}

/* Fix for list items */
li {
  color: #e2e8f0 !important; /* Light gray for better readability */
}

/* Fix for strong text */
strong {
  color: #ffffff !important; /* Ensure strong text is white */
  font-weight: bold;
}

/* Fix for code blocks */
code, pre {
  color: #a3e635 !important; /* Light green for code */
  background-color: #1a202c !important; /* Dark background for code */
}

/* Fix for tables */
table {
  border-color: #4a5568 !important; /* Darker border for tables */
}

table th {
  background-color: #2d3748 !important; /* Darker background for table headers */
  color: #ffffff !important; /* White text for table headers */
}

table td {
  color: #e2e8f0 !important; /* Light gray for table cells */
  border-color: #4a5568 !important; /* Darker border for table cells */
}

/* Fix for links */
a {
  color: #3182ce !important; /* Blue for links */
}

/* Fix for security tip boxes */
.security-tip {
  background-color: #2d3748 !important; /* Darker background for security tips */
  border: 1px solid #4a5568 !important; /* Darker border for security tips */
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.security-tip h4 {
  color: #38b2ac !important; /* Teal for security tip headers */
}

.security-tip p {
  color: #e2e8f0 !important; /* Light gray for security tip text */
}

/* Fix for learning module story sections */
.learning-module-story {
  background-color: #2d3748 !important; /* Darker background for story sections */
  border: 1px solid #4a5568 !important; /* Darker border for story sections */
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
  position: relative;
}

.learning-module-story::before {
  content: '"';
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  font-size: 3rem;
  color: #38b2ac !important; /* Teal for quotation mark */
  opacity: 0.3;
  line-height: 1;
}

.learning-module-story h3 {
  color: #38b2ac !important; /* Teal for story headers */
  margin-bottom: 1rem;
}

.learning-module-story p {
  color: #e2e8f0 !important; /* Light gray for story text */
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.learning-module-story p:last-child {
  margin-bottom: 0;
}

/* Fix for interactive exercises */
.interactive-exercise {
  background-color: #2d3748 !important; /* Darker background for exercises */
  border: 1px solid #4a5568 !important; /* Darker border for exercises */
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.interactive-exercise h3 {
  color: #38b2ac !important; /* Teal for exercise headers */
}

.interactive-exercise p {
  color: #e2e8f0 !important; /* Light gray for exercise text */
}

.exercise-container {
  background-color: #2d3748 !important; /* Darker background for exercise container */
  border: 1px solid #4a5568 !important; /* Darker border for exercise container */
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

.exercise-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.exercise-visualization {
  background-color: #1a202c !important; /* Darker background for exercise visualization */
  border: 1px solid #4a5568 !important; /* Darker border for exercise visualization */
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  min-height: 200px;
}

.exercise-steps {
  background-color: #2d3748 !important; /* Darker background for exercise steps */
  border: 1px solid #4a5568 !important; /* Darker border for exercise steps */
  border-radius: 0.5rem;
  padding: 1rem;
}

.step {
  margin-bottom: 1rem;
}

.step h4 {
  color: #38b2ac !important; /* Teal for step headers */
  margin-bottom: 0.5rem;
}

.step p {
  color: #e2e8f0 !important; /* Light gray for step text */
  margin-bottom: 0.5rem;
}

.step-button {
  background-color: #3182ce !important; /* Blue for step buttons */
  color: #ffffff !important; /* White text for step buttons */
  border: none !important;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
}

.step-button:hover {
  background-color: #2c5282 !important; /* Darker blue for step button hover */
}

/* Fix for quiz containers */
.quiz-container {
  background-color: #2d3748 !important; /* Darker background for quizzes */
  border: 1px solid #4a5568 !important; /* Darker border for quizzes */
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.quiz-question h3 {
  color: #38b2ac !important; /* Teal for question headers */
}

.quiz-question p {
  color: #e2e8f0 !important; /* Light gray for question text */
}

.quiz-options label {
  color: #e2e8f0 !important; /* Light gray for option text */
}

/* Fix for option cards */
.option-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.option-card {
  background-color: #2d3748 !important; /* Darker background for option cards */
  border: 1px solid #4a5568 !important; /* Darker border for option cards */
  border-radius: 0.5rem;
  padding: 1rem;
}

.option-card h4 {
  color: #38b2ac !important; /* Teal for option card headers */
  margin-bottom: 0.5rem;
}

.option-card p {
  color: #e2e8f0 !important; /* Light gray for option card text */
}

/* Fix for troubleshooting cards */
.troubleshooting-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.trouble-card {
  background-color: #2d3748 !important; /* Darker background for trouble cards */
  border: 1px solid #4a5568 !important; /* Darker border for trouble cards */
  border-radius: 0.5rem;
  padding: 1rem;
}

.trouble-card h4 {
  color: #38b2ac !important; /* Teal for trouble card headers */
  margin-bottom: 0.5rem;
}

.trouble-card p, .trouble-card li {
  color: #e2e8f0 !important; /* Light gray for trouble card text */
}

/* Fix for buttons */
button {
  background-color: #3182ce !important; /* Blue for buttons */
  color: #ffffff !important; /* White text for buttons */
  border: none !important;
}

button:hover {
  background-color: #2c5282 !important; /* Darker blue for button hover */
}

/* Fix for input fields */
input, select, textarea {
  background-color: #2d3748 !important; /* Darker background for inputs */
  color: #e2e8f0 !important; /* Light gray for input text */
  border: 1px solid #4a5568 !important; /* Darker border for inputs */
}
