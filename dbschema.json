[{"format_table_info": "\n=== COMPREHENSIVE DATABASE SCHEMA REPORT ===\n\n📋 ALL TABLES IN PUBLIC SCHEMA:\n  achievement_categories (BASE TABLE)\n  achievements (BASE TABLE)\n  ai_monitoring_logs (BASE TABLE)\n  certification_progress (BASE TABLE)\n  certification_tracks (BASE TABLE)\n  challenge_attempts (BASE TABLE)\n  challenge_categories (BASE TABLE)\n  challenge_completions (BASE TABLE)\n  challenge_content (BASE TABLE)\n  challenge_difficulty_levels (BASE TABLE)\n  challenge_hint_purchases (BASE TABLE)\n  challenge_hints (BASE TABLE)\n  challenge_ratings (BASE TABLE)\n  challenge_resources (BASE TABLE)\n  challenge_submissions (BASE TABLE)\n  challenge_types (BASE TABLE)\n  challenges (BASE TABLE)\n  chat_bot_conversations (BASE TABLE)\n  chat_bot_messages (BASE TABLE)\n  coin_transactions (BASE TABLE)\n  cookie_consent (BASE TABLE)\n  daily_challenge_attempts (BASE TABLE)\n  daily_challenge_completions (BASE TABLE)\n  daily_challenges (BASE TABLE)\n  daily_learning_activity (BASE TABLE)\n  difficulty_levels (BASE TABLE)\n  email_verification_requests (BASE TABLE)\n  error_logs (BASE TABLE)\n  leaderboard (BASE TABLE)\n  leaderboard_categories (BASE TABLE)\n  leaderboard_entries (BASE TABLE)\n  learning_module_categories (BASE TABLE)\n  learning_module_content (BASE TABLE)\n  learning_module_difficulty_levels (BASE TABLE)\n  learning_module_metadata (BASE TABLE)\n  learning_module_progress (BASE TABLE)\n  learning_module_types (BASE TABLE)\n  learning_modules (BASE TABLE)\n  learning_path_enrollments (BASE TABLE)\n  learning_path_metadata (BASE TABLE)\n  learning_path_modules (BASE TABLE)\n  learning_paths (BASE TABLE)\n  login_history (BASE TABLE)\n  module_progress (BASE TABLE)\n  notifications (BASE TABLE)\n  order_items (BASE TABLE)\n  orders (BASE TABLE)\n  practice_simulation_attempts (BASE TABLE)\n  practice_simulation_categories (BASE TABLE)\n  practice_simulation_difficulty_levels (BASE TABLE)\n  practice_simulations (BASE TABLE)\n  product_variants (BASE TABLE)\n  products (BASE TABLE)\n  profiles (VIEW)\n  security_posture (BASE TABLE)\n  security_posture_assessments (BASE TABLE)\n  security_recommendations (BASE TABLE)\n  simulations (BASE TABLE)\n  start_hack_simulation_attempts (BASE TABLE)\n  subscription_plans (BASE TABLE)\n  subscription_tracking (BASE TABLE)\n  subscriptions (BASE TABLE)\n  threat_data_cache (BASE TABLE)\n  threat_intelligence (BASE TABLE)\n  threat_intelligence_cache (BASE TABLE)\n  user_achievements (BASE TABLE)\n  user_activity_log (BASE TABLE)\n  user_avatars (BASE TABLE)\n  user_certification_progress (BASE TABLE)\n  user_challenge_progress (BASE TABLE)\n  user_coins (BASE TABLE)\n  user_cookies (BASE TABLE)\n  user_daily_challenges (BASE TABLE)\n  user_events (BASE TABLE)\n  user_learning_preferences (BASE TABLE)\n  user_learning_statistics (BASE TABLE)\n  user_module_progress (BASE TABLE)\n  user_profiles (BASE TABLE)\n  user_sessions (BASE TABLE)\n  user_settings (BASE TABLE)\n  user_skills (BASE TABLE)\n  user_streaks (BASE TABLE)\n  user_subscriptions (BASE TABLE)\n  username_change_requests (BASE TABLE)\n  users (BASE TABLE)\n\n\n🗂️  TABLE: achievement_categories (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - name: text NOT NULL\n     - description: text\n     - icon: text\n     - color: text DEFAULT '#88cc14'::text\n     - display_order: integer DEFAULT 0\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_53017_1_not_null (multiple columns)\n     - CHECK: 2200_53017_2_not_null (multiple columns)\n     - PRIMARY KEY: achievement_categories_pkey (id)\n     - UNIQUE: achievement_categories_name_key (name)\n\n🗂️  TABLE: achievements (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - title: text NOT NULL\n     - description: text\n     - icon: text\n     - category: text\n     - points_reward: integer DEFAULT 0\n     - coin_reward: integer DEFAULT 0\n     - requirements: jsonb\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - tier: text DEFAULT 'bronze'::text\n     - xp_reward: integer DEFAULT 0\n   CONSTRAINTS:\n     - CHECK: 2200_56078_1_not_null (multiple columns)\n     - CHECK: 2200_56078_2_not_null (multiple columns)\n     - PRIMARY KEY: achievements_pkey (id)\n\n🗂️  TABLE: ai_monitoring_logs (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - event_type: text NOT NULL\n     - event_data: jsonb NOT NULL DEFAULT '{}'::jsonb\n     - severity: text DEFAULT 'info'::text\n     - source: text NOT NULL\n     - ip_address: inet\n     - user_agent: text\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49562_1_not_null (multiple columns)\n     - CHECK: 2200_49562_3_not_null (multiple columns)\n     - CHECK: 2200_49562_4_not_null (multiple columns)\n     - CHECK: 2200_49562_6_not_null (multiple columns)\n     - CHECK: ai_monitoring_logs_severity_check (multiple columns)\n     - FOREIGN KEY: ai_monitoring_logs.user_id -> .\n     - PRIMARY KEY: ai_monitoring_logs_pkey (id)\n\n🗂️  TABLE: certification_progress (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - track_id: uuid\n     - progress_percentage: integer DEFAULT 0\n     - completed_domains: jsonb DEFAULT '[]'::jsonb\n     - started_at: timestamp with time zone DEFAULT now()\n     - completed_at: timestamp with time zone\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_53168_1_not_null (multiple columns)\n     - FOREIGN KEY: certification_progress.track_id -> certification_tracks.id\n     - FOREIGN KEY: certification_progress.user_id -> .\n     - PRIMARY KEY: certification_progress_pkey (id)\n     - UNIQUE: certification_progress_user_id_track_id_key (user_id)\n     - UNIQUE: certification_progress_user_id_track_id_key (track_id)\n     - UNIQUE: certification_progress_user_id_track_id_key (track_id)\n     - UNIQUE: certification_progress_user_id_track_id_key (user_id)\n\n🗂️  TABLE: certification_tracks (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - name: text NOT NULL\n     - description: text\n     - provider: text\n     - icon: text\n     - difficulty: text DEFAULT 'beginner'::text\n     - estimated_hours: integer DEFAULT 40\n     - domains: jsonb DEFAULT '[]'::jsonb\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_53154_1_not_null (multiple columns)\n     - CHECK: 2200_53154_2_not_null (multiple columns)\n     - CHECK: certification_tracks_difficulty_check (multiple columns)\n     - PRIMARY KEY: certification_tracks_pkey (id)\n\n🗂️  TABLE: challenge_attempts (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - user_id: uuid\n     - challenge_id: text\n     - solution: text\n     - is_correct: boolean DEFAULT false\n     - points_earned: integer DEFAULT 0\n     - coins_earned: integer DEFAULT 0\n     - attempt_time: integer DEFAULT 0\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_57283_1_not_null (multiple columns)\n     - FOREIGN KEY: challenge_attempts.challenge_id -> challenges.id\n     - FOREIGN KEY: challenge_attempts.user_id -> .\n     - PRIMARY KEY: challenge_attempts_pkey (id)\n\n🗂️  TABLE: challenge_categories (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - name: character varying(100) NOT NULL\n     - description: text\n     - icon: character varying(50)\n     - color: character varying(20)\n     - display_order: integer DEFAULT 0\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_57175_1_not_null (multiple columns)\n     - CHECK: 2200_57175_2_not_null (multiple columns)\n     - PRIMARY KEY: challenge_categories_pkey (id)\n     - UNIQUE: challenge_categories_name_key (name)\n\n🗂️  TABLE: challenge_completions (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - challenge_id: uuid\n     - completed_at: timestamp with time zone DEFAULT now()\n     - score: integer DEFAULT 0\n     - time_taken: integer\n     - attempts: integer DEFAULT 1\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_50422_1_not_null (multiple columns)\n     - FOREIGN KEY: challenge_completions.user_id -> .\n     - PRIMARY KEY: challenge_completions_pkey (id)\n\n🗂️  TABLE: challenge_content (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - challenge_id: text\n     - content: jsonb NOT NULL\n     - solution: jsonb\n     - validation_rules: jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_57250_1_not_null (multiple columns)\n     - CHECK: 2200_57250_3_not_null (multiple columns)\n     - FOREIGN KEY: challenge_content.challenge_id -> challenges.id\n     - PRIMARY KEY: challenge_content_pkey (id)\n\n🗂️  TABLE: challenge_difficulty_levels (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - name: character varying(50) NOT NULL\n     - description: text\n     - color: character varying(20)\n     - points_multiplier: numeric DEFAULT 1.0\n     - display_order: integer DEFAULT 0\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_57189_1_not_null (multiple columns)\n     - CHECK: 2200_57189_2_not_null (multiple columns)\n     - PRIMARY KEY: challenge_difficulty_levels_pkey (id)\n     - UNIQUE: challenge_difficulty_levels_name_key (name)\n\n🗂️  TABLE: challenge_hint_purchases (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - user_id: uuid\n     - hint_id: text\n     - coins_spent: integer DEFAULT 0\n     - purchased_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_57330_1_not_null (multiple columns)\n     - FOREIGN KEY: challenge_hint_purchases.hint_id -> challenge_hints.id\n     - FOREIGN KEY: challenge_hint_purchases.user_id -> .\n     - PRIMARY KEY: challenge_hint_purchases_pkey (id)\n     - UNIQUE: challenge_hint_purchases_user_id_hint_id_key (user_id)\n     - UNIQUE: challenge_hint_purchases_user_id_hint_id_key (user_id)\n     - UNIQUE: challenge_hint_purchases_user_id_hint_id_key (hint_id)\n     - UNIQUE: challenge_hint_purchases_user_id_hint_id_key (hint_id)\n\n🗂️  TABLE: challenge_hints (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - challenge_id: text\n     - hint: text NOT NULL\n     - coin_cost: integer DEFAULT 0\n     - display_order: integer DEFAULT 0\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_57265_1_not_null (multiple columns)\n     - CHECK: 2200_57265_3_not_null (multiple columns)\n     - FOREIGN KEY: challenge_hints.challenge_id -> challenges.id\n     - PRIMARY KEY: challenge_hints_pkey (id)\n\n🗂️  TABLE: challenge_ratings (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - user_id: uuid\n     - challenge_id: text\n     - rating: integer\n     - feedback: text\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_57352_1_not_null (multiple columns)\n     - CHECK: challenge_ratings_rating_check (multiple columns)\n     - FOREIGN KEY: challenge_ratings.challenge_id -> challenges.id\n     - FOREIGN KEY: challenge_ratings.user_id -> .\n     - PRIMARY KEY: challenge_ratings_pkey (id)\n     - UNIQUE: challenge_ratings_user_id_challenge_id_key (user_id)\n     - UNIQUE: challenge_ratings_user_id_challenge_id_key (user_id)\n     - UNIQUE: challenge_ratings_user_id_challenge_id_key (challenge_id)\n     - UNIQUE: challenge_ratings_user_id_challenge_id_key (challenge_id)\n\n🗂️  TABLE: challenge_resources (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - challenge_id: uuid\n     - title: text NOT NULL\n     - resource_type: text NOT NULL\n     - url: text\n     - description: text\n     - is_required: boolean DEFAULT false\n     - display_order: integer DEFAULT 0\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49357_1_not_null (multiple columns)\n     - CHECK: 2200_49357_3_not_null (multiple columns)\n     - CHECK: 2200_49357_4_not_null (multiple columns)\n     - CHECK: challenge_resources_resource_type_check (multiple columns)\n     - PRIMARY KEY: challenge_resources_pkey (id)\n\n🗂️  TABLE: challenge_submissions (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - user_id: uuid\n     - challenge_id: text\n     - points_earned: integer DEFAULT 0\n     - completion_time: timestamp with time zone DEFAULT now()\n     - status: character varying(20) DEFAULT 'completed'::character varying\n     - submission_data: jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n     - attempts: integer DEFAULT 1\n     - submitted_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_57306_1_not_null (multiple columns)\n     - FOREIGN KEY: challenge_submissions.challenge_id -> challenges.id\n     - FOREIGN KEY: challenge_submissions.user_id -> .\n     - PRIMARY KEY: challenge_submissions_pkey (id)\n     - UNIQUE: challenge_submissions_user_id_challenge_id_key (user_id)\n     - UNIQUE: challenge_submissions_user_id_challenge_id_key (user_id)\n     - UNIQUE: challenge_submissions_user_id_challenge_id_key (challenge_id)\n     - UNIQUE: challenge_submissions_user_id_challenge_id_key (challenge_id)\n\n🗂️  TABLE: challenge_types (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - name: character varying(100) NOT NULL\n     - description: text\n     - icon: character varying(50)\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_57204_1_not_null (multiple columns)\n     - CHECK: 2200_57204_2_not_null (multiple columns)\n     - PRIMARY KEY: challenge_types_pkey (id)\n     - UNIQUE: challenge_types_name_key (name)\n\n🗂️  TABLE: challenges (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL DEFAULT (gen_random_uuid())::text\n     - title: character varying(255) NOT NULL\n     - slug: character varying(255) NOT NULL\n     - description: text\n     - category_id: text\n     - difficulty_id: text\n     - type_id: text\n     - points: integer DEFAULT 0\n     - coin_reward: integer DEFAULT 0\n     - estimated_time: integer DEFAULT 60\n     - is_premium: boolean DEFAULT false\n     - is_business: boolean DEFAULT false\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n     - category: text\n     - difficulty: text DEFAULT 'beginner'::text\n   CONSTRAINTS:\n     - CHECK: 2200_57217_1_not_null (multiple columns)\n     - CHECK: 2200_57217_2_not_null (multiple columns)\n     - CHECK: 2200_57217_3_not_null (multiple columns)\n     - FOREIGN KEY: challenges.category_id -> challenge_categories.id\n     - FOREIGN KEY: challenges.difficulty_id -> challenge_difficulty_levels.id\n     - FOREIGN KEY: challenges.type_id -> challenge_types.id\n     - PRIMARY KEY: challenges_pkey (id)\n     - UNIQUE: challenges_slug_key (slug)\n\n🗂️  TABLE: chat_bot_conversations (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - title: text\n     - context_data: jsonb DEFAULT '{}'::jsonb\n     - is_active: boolean DEFAULT true\n     - started_at: timestamp with time zone DEFAULT now()\n     - last_message_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49629_1_not_null (multiple columns)\n     - FOREIGN KEY: chat_bot_conversations.user_id -> .\n     - PRIMARY KEY: chat_bot_conversations_pkey (id)\n\n🗂️  TABLE: chat_bot_messages (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - conversation_id: uuid NOT NULL\n     - user_id: uuid\n     - message: text NOT NULL\n     - sender: text NOT NULL\n     - message_type: text DEFAULT 'text'::text\n     - metadata: jsonb DEFAULT '{}'::jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49611_1_not_null (multiple columns)\n     - CHECK: 2200_49611_2_not_null (multiple columns)\n     - CHECK: 2200_49611_4_not_null (multiple columns)\n     - CHECK: 2200_49611_5_not_null (multiple columns)\n     - CHECK: chat_bot_messages_message_type_check (multiple columns)\n     - CHECK: chat_bot_messages_sender_check (multiple columns)\n     - FOREIGN KEY: chat_bot_messages.user_id -> .\n     - PRIMARY KEY: chat_bot_messages_pkey (id)\n\n🗂️  TABLE: coin_transactions (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - amount: integer NOT NULL\n     - transaction_type: text NOT NULL\n     - description: text NOT NULL\n     - reference_id: uuid\n     - reference_type: text\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49680_1_not_null (multiple columns)\n     - CHECK: 2200_49680_3_not_null (multiple columns)\n     - CHECK: 2200_49680_4_not_null (multiple columns)\n     - CHECK: 2200_49680_5_not_null (multiple columns)\n     - CHECK: coin_transactions_transaction_type_check (multiple columns)\n     - FOREIGN KEY: coin_transactions.user_id -> .\n     - PRIMARY KEY: coin_transactions_pkey (id)\n\n🗂️  TABLE: cookie_consent (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - session_id: uuid\n     - essential_cookies: boolean DEFAULT true\n     - functional_cookies: boolean DEFAULT false\n     - analytics_cookies: boolean DEFAULT false\n     - marketing_cookies: boolean DEFAULT false\n     - consent_version: text DEFAULT '1.0'::text\n     - consent_method: text DEFAULT 'banner'::text\n     - ip_address: inet\n     - user_agent: text\n     - given_at: timestamp with time zone DEFAULT now()\n     - expires_at: timestamp with time zone DEFAULT (now() + '1 year'::interval)\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_60935_1_not_null (multiple columns)\n     - CHECK: valid_consent_method (multiple columns)\n     - FOREIGN KEY: cookie_consent.session_id -> user_sessions.id\n     - FOREIGN KEY: cookie_consent.user_id -> .\n     - PRIMARY KEY: cookie_consent_pkey (id)\n\n🗂️  TABLE: daily_challenge_attempts (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - challenge_id: uuid\n     - status: text DEFAULT 'started'::text\n     - score: integer DEFAULT 0\n     - time_spent: integer DEFAULT 0\n     - submission: jsonb DEFAULT '{}'::jsonb\n     - completed_at: timestamp with time zone\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_53093_1_not_null (multiple columns)\n     - CHECK: daily_challenge_attempts_status_check (multiple columns)\n     - FOREIGN KEY: daily_challenge_attempts.user_id -> .\n     - PRIMARY KEY: daily_challenge_attempts_pkey (id)\n     - UNIQUE: daily_challenge_attempts_user_id_challenge_id_key (challenge_id)\n     - UNIQUE: daily_challenge_attempts_user_id_challenge_id_key (challenge_id)\n     - UNIQUE: daily_challenge_attempts_user_id_challenge_id_key (user_id)\n     - UNIQUE: daily_challenge_attempts_user_id_challenge_id_key (user_id)\n\n🗂️  TABLE: daily_challenge_completions (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - daily_challenge_id: uuid\n     - completed_at: timestamp with time zone DEFAULT now()\n     - points_earned: integer DEFAULT 0\n     - bonus_points_earned: integer DEFAULT 0\n   CONSTRAINTS:\n     - CHECK: 2200_49455_1_not_null (multiple columns)\n     - FOREIGN KEY: daily_challenge_completions.user_id -> .\n     - PRIMARY KEY: daily_challenge_completions_pkey (id)\n     - UNIQUE: daily_challenge_completions_user_id_daily_challenge_id_key (daily_challenge_id)\n     - UNIQUE: daily_challenge_completions_user_id_daily_challenge_id_key (daily_challenge_id)\n     - UNIQUE: daily_challenge_completions_user_id_daily_challenge_id_key (user_id)\n     - UNIQUE: daily_challenge_completions_user_id_daily_challenge_id_key (user_id)\n\n🗂️  TABLE: daily_challenges (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - challenge_id: text\n     - challenge_date: date NOT NULL\n     - points_reward: integer DEFAULT 50\n     - coin_reward: integer DEFAULT 10\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56040_1_not_null (multiple columns)\n     - CHECK: 2200_56040_3_not_null (multiple columns)\n     - PRIMARY KEY: daily_challenges_pkey (id)\n     - UNIQUE: daily_challenges_challenge_date_key (challenge_date)\n\n🗂️  TABLE: daily_learning_activity (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid NOT NULL\n     - activity_date: date NOT NULL DEFAULT CURRENT_DATE\n     - modules_completed: integer DEFAULT 0\n     - time_spent_minutes: integer DEFAULT 0\n     - xp_earned: integer DEFAULT 0\n     - paths_worked_on: ARRAY\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56379_1_not_null (multiple columns)\n     - CHECK: 2200_56379_2_not_null (multiple columns)\n     - CHECK: 2200_56379_3_not_null (multiple columns)\n     - FOREIGN KEY: daily_learning_activity.user_id -> .\n     - PRIMARY KEY: daily_learning_activity_pkey (id)\n     - UNIQUE: daily_learning_activity_user_id_activity_date_key (activity_date)\n     - UNIQUE: daily_learning_activity_user_id_activity_date_key (activity_date)\n     - UNIQUE: daily_learning_activity_user_id_activity_date_key (user_id)\n     - UNIQUE: daily_learning_activity_user_id_activity_date_key (user_id)\n\n🗂️  TABLE: difficulty_levels (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - name: text NOT NULL\n     - level: integer\n     - color: text DEFAULT '#gray'::text\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51150_1_not_null (multiple columns)\n     - CHECK: 2200_51150_2_not_null (multiple columns)\n     - PRIMARY KEY: difficulty_levels_pkey (id)\n     - UNIQUE: difficulty_levels_level_key (level)\n     - UNIQUE: difficulty_levels_name_key (name)\n\n🗂️  TABLE: email_verification_requests (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - current_email: text NOT NULL\n     - new_email: text NOT NULL\n     - verification_token: text NOT NULL\n     - status: text DEFAULT 'pending'::text\n     - verified_at: timestamp with time zone\n     - expires_at: timestamp with time zone DEFAULT (now() + '24:00:00'::interval)\n     - ip_address: inet\n     - user_agent: text\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_60888_1_not_null (multiple columns)\n     - CHECK: 2200_60888_3_not_null (multiple columns)\n     - CHECK: 2200_60888_4_not_null (multiple columns)\n     - CHECK: 2200_60888_5_not_null (multiple columns)\n     - CHECK: valid_emails (multiple columns)\n     - CHECK: valid_emails (multiple columns)\n     - CHECK: valid_status (multiple columns)\n     - FOREIGN KEY: email_verification_requests.user_id -> .\n     - PRIMARY KEY: email_verification_requests_pkey (id)\n     - UNIQUE: email_verification_requests_verification_token_key (verification_token)\n\n🗂️  TABLE: error_logs (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - error_type: text NOT NULL\n     - error_message: text NOT NULL\n     - error_stack: text\n     - component: text\n     - page_url: text\n     - additional_data: jsonb DEFAULT '{}'::jsonb\n     - ip_address: text\n     - user_agent: text\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51026_1_not_null (multiple columns)\n     - CHECK: 2200_51026_3_not_null (multiple columns)\n     - CHECK: 2200_51026_4_not_null (multiple columns)\n     - FOREIGN KEY: error_logs.user_id -> .\n     - PRIMARY KEY: error_logs_pkey (id)\n\n🗂️  TABLE: leaderboard (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - points: integer DEFAULT 0\n     - rank: integer\n     - challenges_completed: integer DEFAULT 0\n     - modules_completed: integer DEFAULT 0\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56140_1_not_null (multiple columns)\n     - FOREIGN KEY: leaderboard.user_id -> user_profiles.id\n     - PRIMARY KEY: leaderboard_pkey (id)\n\n🗂️  TABLE: leaderboard_categories (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - name: text NOT NULL\n     - description: text\n     - type: text DEFAULT 'xp'::text\n     - timeframe: text DEFAULT 'all_time'::text\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_53119_1_not_null (multiple columns)\n     - CHECK: 2200_53119_2_not_null (multiple columns)\n     - CHECK: leaderboard_categories_timeframe_check (multiple columns)\n     - CHECK: leaderboard_categories_type_check (multiple columns)\n     - PRIMARY KEY: leaderboard_categories_pkey (id)\n     - UNIQUE: leaderboard_categories_name_key (name)\n\n🗂️  TABLE: leaderboard_entries (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - category_id: uuid\n     - score: integer DEFAULT 0\n     - rank: integer\n     - period_start: date\n     - period_end: date\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_53135_1_not_null (multiple columns)\n     - FOREIGN KEY: leaderboard_entries.category_id -> leaderboard_categories.id\n     - FOREIGN KEY: leaderboard_entries.user_id -> .\n     - PRIMARY KEY: leaderboard_entries_pkey (id)\n\n🗂️  TABLE: learning_module_categories (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - name: text NOT NULL\n     - description: text\n     - icon: text\n     - color: text DEFAULT '#3B82F6'::text\n     - display_order: integer DEFAULT 0\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49091_1_not_null (multiple columns)\n     - CHECK: 2200_49091_2_not_null (multiple columns)\n     - PRIMARY KEY: learning_module_categories_pkey (id)\n     - UNIQUE: learning_module_categories_name_key (name)\n\n🗂️  TABLE: learning_module_content (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - module_id: uuid\n     - section_title: text NOT NULL\n     - content_type: text NOT NULL\n     - content: jsonb NOT NULL DEFAULT '{}'::jsonb\n     - display_order: integer DEFAULT 0\n     - is_required: boolean DEFAULT true\n     - estimated_time: integer DEFAULT 5\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49198_1_not_null (multiple columns)\n     - CHECK: 2200_49198_3_not_null (multiple columns)\n     - CHECK: 2200_49198_4_not_null (multiple columns)\n     - CHECK: 2200_49198_5_not_null (multiple columns)\n     - CHECK: learning_module_content_content_type_check (multiple columns)\n     - PRIMARY KEY: learning_module_content_pkey (id)\n\n🗂️  TABLE: learning_module_difficulty_levels (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - name: text NOT NULL\n     - description: text\n     - display_order: integer DEFAULT 0\n     - color: text DEFAULT '#3B82F6'::text\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49105_1_not_null (multiple columns)\n     - CHECK: 2200_49105_2_not_null (multiple columns)\n     - PRIMARY KEY: learning_module_difficulty_levels_pkey (id)\n     - UNIQUE: learning_module_difficulty_levels_name_key (name)\n\n🗂️  TABLE: learning_module_metadata (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL\n     - path_id: text NOT NULL\n     - module_order: integer NOT NULL\n     - title: text NOT NULL\n     - description: text\n     - difficulty: text DEFAULT 'Intermediate'::text\n     - estimated_time_minutes: integer DEFAULT 30\n     - xp_reward: integer DEFAULT 100\n     - objectives: ARRAY\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56297_1_not_null (multiple columns)\n     - CHECK: 2200_56297_2_not_null (multiple columns)\n     - CHECK: 2200_56297_3_not_null (multiple columns)\n     - CHECK: 2200_56297_4_not_null (multiple columns)\n     - CHECK: learning_module_metadata_difficulty_check (multiple columns)\n     - PRIMARY KEY: learning_module_metadata_pkey (id)\n     - UNIQUE: learning_module_metadata_path_id_module_order_key (module_order)\n     - UNIQUE: learning_module_metadata_path_id_module_order_key (module_order)\n     - UNIQUE: learning_module_metadata_path_id_module_order_key (path_id)\n     - UNIQUE: learning_module_metadata_path_id_module_order_key (path_id)\n\n🗂️  TABLE: learning_module_progress (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid NOT NULL\n     - module_id: text NOT NULL\n     - path_id: text NOT NULL\n     - status: text DEFAULT 'not_started'::text\n     - progress_percentage: integer DEFAULT 0\n     - started_at: timestamp with time zone\n     - completed_at: timestamp with time zone\n     - time_spent: integer DEFAULT 0\n     - xp_earned: integer DEFAULT 0\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56337_1_not_null (multiple columns)\n     - CHECK: 2200_56337_2_not_null (multiple columns)\n     - CHECK: 2200_56337_3_not_null (multiple columns)\n     - CHECK: 2200_56337_4_not_null (multiple columns)\n     - CHECK: learning_module_progress_progress_percentage_check (multiple columns)\n     - CHECK: learning_module_progress_status_check (multiple columns)\n     - FOREIGN KEY: learning_module_progress.user_id -> .\n     - PRIMARY KEY: learning_module_progress_pkey (id)\n     - UNIQUE: learning_module_progress_user_id_module_id_key (module_id)\n     - UNIQUE: learning_module_progress_user_id_module_id_key (module_id)\n     - UNIQUE: learning_module_progress_user_id_module_id_key (user_id)\n     - UNIQUE: learning_module_progress_user_id_module_id_key (user_id)\n\n🗂️  TABLE: learning_module_types (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - name: text NOT NULL\n     - description: text\n     - icon: text\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49119_1_not_null (multiple columns)\n     - CHECK: 2200_49119_2_not_null (multiple columns)\n     - PRIMARY KEY: learning_module_types_pkey (id)\n     - UNIQUE: learning_module_types_name_key (name)\n\n🗂️  TABLE: learning_modules (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL\n     - path_id: text\n     - title: text NOT NULL\n     - description: text\n     - slug: text\n     - content: jsonb\n     - category: text\n     - category_id: text\n     - difficulty: text DEFAULT 'Beginner'::text\n     - difficulty_id: text\n     - estimated_time: integer DEFAULT 60\n     - order_index: integer DEFAULT 1\n     - is_premium: boolean DEFAULT false\n     - is_business: boolean DEFAULT false\n     - is_published: boolean DEFAULT true\n     - is_active: boolean DEFAULT true\n     - author_id: uuid\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_55840_1_not_null (multiple columns)\n     - CHECK: 2200_55840_3_not_null (multiple columns)\n     - CHECK: learning_modules_difficulty_check (multiple columns)\n     - FOREIGN KEY: learning_modules.author_id -> user_profiles.id\n     - FOREIGN KEY: learning_modules.path_id -> learning_paths.id\n     - PRIMARY KEY: learning_modules_pkey (id)\n\n🗂️  TABLE: learning_path_enrollments (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid NOT NULL\n     - path_id: text NOT NULL\n     - enrolled_at: timestamp with time zone DEFAULT now()\n     - status: text DEFAULT 'active'::text\n     - progress_percentage: integer DEFAULT 0\n     - current_module: integer DEFAULT 1\n     - completed_at: timestamp with time zone\n     - last_accessed: timestamp with time zone DEFAULT now()\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56313_1_not_null (multiple columns)\n     - CHECK: 2200_56313_2_not_null (multiple columns)\n     - CHECK: 2200_56313_3_not_null (multiple columns)\n     - CHECK: learning_path_enrollments_progress_percentage_check (multiple columns)\n     - CHECK: learning_path_enrollments_status_check (multiple columns)\n     - FOREIGN KEY: learning_path_enrollments.user_id -> .\n     - PRIMARY KEY: learning_path_enrollments_pkey (id)\n     - UNIQUE: learning_path_enrollments_user_id_path_id_key (path_id)\n     - UNIQUE: learning_path_enrollments_user_id_path_id_key (path_id)\n     - UNIQUE: learning_path_enrollments_user_id_path_id_key (user_id)\n     - UNIQUE: learning_path_enrollments_user_id_path_id_key (user_id)\n\n🗂️  TABLE: learning_path_metadata (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL\n     - title: text NOT NULL\n     - description: text\n     - category: text NOT NULL\n     - difficulty: text DEFAULT 'Intermediate'::text\n     - estimated_hours: integer DEFAULT 0\n     - module_count: integer DEFAULT 0\n     - prerequisites: ARRAY\n     - skills: ARRAY\n     - icon: text\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n     - learning_path_id: uuid\n   CONSTRAINTS:\n     - CHECK: 2200_56283_1_not_null (multiple columns)\n     - CHECK: 2200_56283_2_not_null (multiple columns)\n     - CHECK: 2200_56283_4_not_null (multiple columns)\n     - CHECK: learning_path_metadata_difficulty_check (multiple columns)\n     - PRIMARY KEY: learning_path_metadata_pkey (id)\n\n🗂️  TABLE: learning_path_modules (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - learning_path_id: uuid\n     - module_id: uuid\n     - display_order: integer NOT NULL\n     - is_required: boolean DEFAULT true\n     - unlock_criteria: jsonb DEFAULT '{}'::jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49217_1_not_null (multiple columns)\n     - CHECK: 2200_49217_4_not_null (multiple columns)\n     - PRIMARY KEY: learning_path_modules_pkey (id)\n     - UNIQUE: learning_path_modules_learning_path_id_module_id_key (learning_path_id)\n     - UNIQUE: learning_path_modules_learning_path_id_module_id_key (module_id)\n     - UNIQUE: learning_path_modules_learning_path_id_module_id_key (learning_path_id)\n     - UNIQUE: learning_path_modules_learning_path_id_module_id_key (module_id)\n\n🗂️  TABLE: learning_paths (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL\n     - title: text NOT NULL\n     - description: text\n     - category: text\n     - category_id: text\n     - difficulty: text DEFAULT 'Beginner'::text\n     - estimated_hours: integer DEFAULT 10\n     - is_premium: boolean DEFAULT false\n     - is_business: boolean DEFAULT false\n     - is_published: boolean DEFAULT true\n     - is_active: boolean DEFAULT true\n     - image_url: text\n     - author_id: uuid\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_55819_1_not_null (multiple columns)\n     - CHECK: 2200_55819_2_not_null (multiple columns)\n     - CHECK: learning_paths_difficulty_check (multiple columns)\n     - FOREIGN KEY: learning_paths.author_id -> user_profiles.id\n     - PRIMARY KEY: learning_paths_pkey (id)\n\n🗂️  TABLE: login_history (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - session_id: uuid\n     - login_type: text NOT NULL\n     - login_method: text\n     - success: boolean NOT NULL\n     - failure_reason: text\n     - ip_address: inet\n     - user_agent: text\n     - device_fingerprint: text\n     - country: text\n     - city: text\n     - is_suspicious: boolean DEFAULT false\n     - risk_score: integer DEFAULT 0\n     - blocked: boolean DEFAULT false\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_60864_1_not_null (multiple columns)\n     - CHECK: 2200_60864_4_not_null (multiple columns)\n     - CHECK: 2200_60864_6_not_null (multiple columns)\n     - CHECK: valid_login_type (multiple columns)\n     - CHECK: valid_risk_score (multiple columns)\n     - FOREIGN KEY: login_history.session_id -> user_sessions.id\n     - FOREIGN KEY: login_history.user_id -> .\n     - PRIMARY KEY: login_history_pkey (id)\n\n🗂️  TABLE: module_progress (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - module_id: text NOT NULL\n     - progress: integer DEFAULT 0\n     - completed: boolean DEFAULT false\n     - completed_at: timestamp with time zone\n     - last_activity: timestamp with time zone DEFAULT now()\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_55917_1_not_null (multiple columns)\n     - CHECK: 2200_55917_3_not_null (multiple columns)\n     - FOREIGN KEY: module_progress.user_id -> user_profiles.id\n     - PRIMARY KEY: module_progress_pkey (id)\n     - UNIQUE: module_progress_user_id_module_id_key (module_id)\n     - UNIQUE: module_progress_user_id_module_id_key (module_id)\n     - UNIQUE: module_progress_user_id_module_id_key (user_id)\n     - UNIQUE: module_progress_user_id_module_id_key (user_id)\n\n🗂️  TABLE: notifications (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - title: text NOT NULL\n     - message: text\n     - type: text DEFAULT 'info'::text\n     - is_read: boolean DEFAULT false\n     - action_url: text\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56232_1_not_null (multiple columns)\n     - CHECK: 2200_56232_3_not_null (multiple columns)\n     - FOREIGN KEY: notifications.user_id -> user_profiles.id\n     - PRIMARY KEY: notifications_pkey (id)\n\n🗂️  TABLE: order_items (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - order_id: uuid\n     - product_id: uuid\n     - product_variant_id: uuid\n     - quantity: integer DEFAULT 1\n     - price_coins: integer DEFAULT 0\n     - price_usd: numeric DEFAULT 0\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49748_1_not_null (multiple columns)\n     - FOREIGN KEY: order_items.order_id -> orders.id\n     - FOREIGN KEY: order_items.product_id -> products.id\n     - FOREIGN KEY: order_items.product_variant_id -> product_variants.id\n     - PRIMARY KEY: order_items_pkey (id)\n\n🗂️  TABLE: orders (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - total_coins: integer DEFAULT 0\n     - total_usd: numeric DEFAULT 0\n     - status: text DEFAULT 'pending'::text\n     - payment_method: text\n     - payment_data: jsonb DEFAULT '{}'::jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49728_1_not_null (multiple columns)\n     - CHECK: orders_status_check (multiple columns)\n     - FOREIGN KEY: orders.user_id -> .\n     - PRIMARY KEY: orders_pkey (id)\n\n🗂️  TABLE: practice_simulation_attempts (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - simulation_id: uuid\n     - status: text DEFAULT 'started'::text\n     - score: integer DEFAULT 0\n     - time_spent: integer DEFAULT 0\n     - started_at: timestamp with time zone DEFAULT now()\n     - completed_at: timestamp with time zone\n     - submission: jsonb DEFAULT '{}'::jsonb\n     - feedback: jsonb DEFAULT '{}'::jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51302_1_not_null (multiple columns)\n     - CHECK: practice_simulation_attempts_status_check (multiple columns)\n     - FOREIGN KEY: practice_simulation_attempts.simulation_id -> practice_simulations.id\n     - FOREIGN KEY: practice_simulation_attempts.user_id -> .\n     - PRIMARY KEY: practice_simulation_attempts_pkey (id)\n     - UNIQUE: practice_simulation_attempts_user_id_simulation_id_key (user_id)\n     - UNIQUE: practice_simulation_attempts_user_id_simulation_id_key (simulation_id)\n     - UNIQUE: practice_simulation_attempts_user_id_simulation_id_key (simulation_id)\n     - UNIQUE: practice_simulation_attempts_user_id_simulation_id_key (user_id)\n\n🗂️  TABLE: practice_simulation_categories (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - name: text NOT NULL\n     - description: text\n     - icon: text\n     - color: text DEFAULT '#88cc14'::text\n     - display_order: integer DEFAULT 0\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51248_1_not_null (multiple columns)\n     - CHECK: 2200_51248_2_not_null (multiple columns)\n     - PRIMARY KEY: practice_simulation_categories_pkey (id)\n\n🗂️  TABLE: practice_simulation_difficulty_levels (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - name: text NOT NULL\n     - description: text\n     - color: text DEFAULT '#88cc14'::text\n     - display_order: integer DEFAULT 0\n     - xp_multiplier: numeric DEFAULT 1.0\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51261_1_not_null (multiple columns)\n     - CHECK: 2200_51261_2_not_null (multiple columns)\n     - PRIMARY KEY: practice_simulation_difficulty_levels_pkey (id)\n\n🗂️  TABLE: practice_simulations (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - title: text NOT NULL\n     - description: text\n     - short_description: text\n     - category_id: uuid\n     - difficulty_id: uuid\n     - estimated_time: integer DEFAULT 30\n     - xp_reward: integer DEFAULT 100\n     - coin_reward: integer DEFAULT 10\n     - content: jsonb DEFAULT '{}'::jsonb\n     - instructions: text\n     - solution: text\n     - hints: jsonb DEFAULT '[]'::jsonb\n     - tags: ARRAY DEFAULT '{}'::text[]\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51275_1_not_null (multiple columns)\n     - CHECK: 2200_51275_2_not_null (multiple columns)\n     - FOREIGN KEY: practice_simulations.category_id -> practice_simulation_categories.id\n     - FOREIGN KEY: practice_simulations.difficulty_id -> practice_simulation_difficulty_levels.id\n     - PRIMARY KEY: practice_simulations_pkey (id)\n\n🗂️  TABLE: product_variants (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - product_id: uuid\n     - name: text NOT NULL\n     - description: text\n     - price_coins: integer DEFAULT 0\n     - price_usd: numeric DEFAULT 0\n     - metadata: jsonb DEFAULT '{}'::jsonb\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49710_1_not_null (multiple columns)\n     - CHECK: 2200_49710_3_not_null (multiple columns)\n     - FOREIGN KEY: product_variants.product_id -> products.id\n     - PRIMARY KEY: product_variants_pkey (id)\n\n🗂️  TABLE: products (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - name: text NOT NULL\n     - description: text\n     - product_type: text NOT NULL\n     - price_coins: integer DEFAULT 0\n     - price_usd: numeric DEFAULT 0\n     - is_active: boolean DEFAULT true\n     - metadata: jsonb DEFAULT '{}'::jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49695_1_not_null (multiple columns)\n     - CHECK: 2200_49695_2_not_null (multiple columns)\n     - CHECK: 2200_49695_4_not_null (multiple columns)\n     - CHECK: products_product_type_check (multiple columns)\n     - PRIMARY KEY: products_pkey (id)\n\n🗂️  TABLE: profiles (VIEW)\n   COLUMNS:\n     - id: uuid\n     - user_id: uuid\n     - display_name: text\n     - username: text\n     - full_name: text\n     - avatar_url: text\n     - bio: text\n     - email: text\n     - subscription_tier: text\n     - subscription_status: text\n     - subscription_start_date: timestamp with time zone\n     - subscription_end_date: timestamp with time zone\n     - is_admin: boolean\n     - role: text\n     - total_points: integer\n     - points: integer\n     - total_coins: integer\n     - coins: integer\n     - current_streak: integer\n     - longest_streak: integer\n     - modules_completed: integer\n     - challenges_completed: integer\n     - achievements_earned: integer\n     - last_activity: timestamp with time zone\n     - learning_style: text\n     - created_at: timestamp with time zone\n     - updated_at: timestamp with time zone\n   CONSTRAINTS:\n\n🗂️  TABLE: security_posture (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - score: integer DEFAULT 0\n     - recommendations: jsonb\n     - last_assessment: timestamp with time zone DEFAULT now()\n     - created_at: timestamp with time zone DEFAULT now()\n     - overall_score: integer DEFAULT 0\n     - risk_level: text DEFAULT 'medium'::text\n     - last_scan_date: timestamp with time zone DEFAULT now()\n     - findings: jsonb DEFAULT '{}'::jsonb\n     - metrics: jsonb DEFAULT '{}'::jsonb\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56164_1_not_null (multiple columns)\n     - FOREIGN KEY: security_posture.user_id -> user_profiles.id\n     - PRIMARY KEY: security_posture_pkey (id)\n\n🗂️  TABLE: security_posture_assessments (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - overall_score: integer DEFAULT 0\n     - previous_score: integer DEFAULT 0\n     - score_change: integer DEFAULT 0\n     - categories: jsonb DEFAULT '{}'::jsonb\n     - strengths: ARRAY DEFAULT '{}'::text[]\n     - weaknesses: ARRAY DEFAULT '{}'::text[]\n     - recommendations: jsonb DEFAULT '[]'::jsonb\n     - assessment_date: timestamp with time zone DEFAULT now()\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51447_1_not_null (multiple columns)\n     - FOREIGN KEY: security_posture_assessments.user_id -> .\n     - PRIMARY KEY: security_posture_assessments_pkey (id)\n\n🗂️  TABLE: security_recommendations (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - title: text NOT NULL\n     - description: text NOT NULL\n     - recommendation_type: text NOT NULL\n     - priority: integer DEFAULT 1\n     - link: text\n     - is_completed: boolean DEFAULT false\n     - completed_at: timestamp with time zone\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49544_1_not_null (multiple columns)\n     - CHECK: 2200_49544_3_not_null (multiple columns)\n     - CHECK: 2200_49544_4_not_null (multiple columns)\n     - CHECK: 2200_49544_5_not_null (multiple columns)\n     - CHECK: security_recommendations_priority_check (multiple columns)\n     - FOREIGN KEY: security_recommendations.user_id -> .\n     - PRIMARY KEY: security_recommendations_pkey (id)\n\n🗂️  TABLE: simulations (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - title: text NOT NULL\n     - slug: text\n     - description: text\n     - points: integer DEFAULT 100\n     - difficulty_id: uuid\n     - content: jsonb DEFAULT '{}'::jsonb\n     - is_published: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51136_1_not_null (multiple columns)\n     - CHECK: 2200_51136_2_not_null (multiple columns)\n     - FOREIGN KEY: simulations.difficulty_id -> difficulty_levels.id\n     - PRIMARY KEY: simulations_pkey (id)\n     - UNIQUE: simulations_slug_key (slug)\n\n🗂️  TABLE: start_hack_simulation_attempts (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - simulation_id: uuid\n     - started_at: timestamp with time zone DEFAULT now()\n     - completed_at: timestamp with time zone\n     - score: integer DEFAULT 0\n     - status: text DEFAULT 'in_progress'::text\n     - time_taken: integer\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51062_1_not_null (multiple columns)\n     - FOREIGN KEY: start_hack_simulation_attempts.simulation_id -> simulations.id\n     - FOREIGN KEY: start_hack_simulation_attempts.user_id -> .\n     - PRIMARY KEY: start_hack_simulation_attempts_pkey (id)\n\n🗂️  TABLE: subscription_plans (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - name: text NOT NULL\n     - price: numeric NOT NULL\n     - currency: text DEFAULT 'USD'::text\n     - billing_interval: text DEFAULT 'monthly'::text\n     - features: jsonb\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56180_1_not_null (multiple columns)\n     - CHECK: 2200_56180_2_not_null (multiple columns)\n     - CHECK: 2200_56180_3_not_null (multiple columns)\n     - PRIMARY KEY: subscription_plans_pkey (id)\n     - UNIQUE: subscription_plans_name_key (name)\n\n🗂️  TABLE: subscription_tracking (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - status: text NOT NULL\n     - start_date: timestamp with time zone DEFAULT now()\n     - end_date: timestamp with time zone\n     - amount: numeric\n     - payment_id: text\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56217_1_not_null (multiple columns)\n     - CHECK: 2200_56217_3_not_null (multiple columns)\n     - FOREIGN KEY: subscription_tracking.user_id -> user_profiles.id\n     - PRIMARY KEY: subscription_tracking_pkey (id)\n\n🗂️  TABLE: subscriptions (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid NOT NULL\n     - plan_type: text NOT NULL\n     - status: text NOT NULL\n     - start_date: timestamp with time zone DEFAULT now()\n     - end_date: timestamp with time zone\n     - payment_id: text\n     - amount: numeric\n     - currency: text DEFAULT 'USD'::text\n     - auto_renew: boolean DEFAULT false\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56753_1_not_null (multiple columns)\n     - CHECK: 2200_56753_2_not_null (multiple columns)\n     - CHECK: 2200_56753_3_not_null (multiple columns)\n     - CHECK: 2200_56753_4_not_null (multiple columns)\n     - CHECK: subscriptions_plan_type_check (multiple columns)\n     - CHECK: subscriptions_status_check (multiple columns)\n     - FOREIGN KEY: subscriptions.user_id -> users.id\n     - PRIMARY KEY: subscriptions_pkey (id)\n\n🗂️  TABLE: threat_data_cache (BASE TABLE)\n   COLUMNS:\n     - id: text NOT NULL\n     - attacks: jsonb\n     - statistics: jsonb\n     - vulnerabilities: jsonb\n     - last_updated: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56156_1_not_null (multiple columns)\n     - PRIMARY KEY: threat_data_cache_pkey (id)\n\n🗂️  TABLE: threat_intelligence (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - threat_type: text NOT NULL\n     - title: text NOT NULL\n     - description: text\n     - severity: integer\n     - source: text NOT NULL\n     - indicators: jsonb\n     - ioc_type: ARRAY\n     - ioc_values: jsonb\n     - mitre_tactics: ARRAY\n     - mitre_techniques: ARRAY\n     - affected_systems: ARRAY\n     - recommended_actions: jsonb\n     - metadata: jsonb\n     - is_active: boolean DEFAULT true\n     - expiration_date: timestamp with time zone\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56899_1_not_null (multiple columns)\n     - CHECK: 2200_56899_2_not_null (multiple columns)\n     - CHECK: 2200_56899_3_not_null (multiple columns)\n     - CHECK: 2200_56899_6_not_null (multiple columns)\n     - CHECK: threat_intelligence_ioc_type_check (multiple columns)\n     - CHECK: threat_intelligence_severity_check (multiple columns)\n     - PRIMARY KEY: threat_intelligence_pkey (id)\n\n🗂️  TABLE: threat_intelligence_cache (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - threat_type: text NOT NULL\n     - severity: text DEFAULT 'medium'::text\n     - title: text NOT NULL\n     - description: text\n     - indicators: jsonb DEFAULT '{}'::jsonb\n     - mitigation: jsonb DEFAULT '{}'::jsonb\n     - source: text\n     - confidence_level: integer DEFAULT 50\n     - first_seen: timestamp with time zone\n     - last_seen: timestamp with time zone\n     - is_active: boolean DEFAULT true\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51469_1_not_null (multiple columns)\n     - CHECK: 2200_51469_2_not_null (multiple columns)\n     - CHECK: 2200_51469_4_not_null (multiple columns)\n     - CHECK: threat_intelligence_cache_severity_check (multiple columns)\n     - PRIMARY KEY: threat_intelligence_cache_pkey (id)\n\n🗂️  TABLE: user_achievements (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - achievement_id: uuid\n     - earned_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56090_1_not_null (multiple columns)\n     - FOREIGN KEY: user_achievements.achievement_id -> achievements.id\n     - FOREIGN KEY: user_achievements.user_id -> user_profiles.id\n     - PRIMARY KEY: user_achievements_pkey (id)\n     - UNIQUE: user_achievements_user_id_achievement_id_key (user_id)\n     - UNIQUE: user_achievements_user_id_achievement_id_key (user_id)\n     - UNIQUE: user_achievements_user_id_achievement_id_key (achievement_id)\n     - UNIQUE: user_achievements_user_id_achievement_id_key (achievement_id)\n\n🗂️  TABLE: user_activity_log (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - activity_type: text NOT NULL\n     - activity_description: text\n     - activity_data: jsonb\n     - ip_address: inet\n     - user_agent: text\n     - device_info: jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_60604_1_not_null (multiple columns)\n     - CHECK: 2200_60604_3_not_null (multiple columns)\n     - FOREIGN KEY: user_activity_log.user_id -> .\n     - PRIMARY KEY: user_activity_log_pkey (id)\n\n🗂️  TABLE: user_avatars (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - file_name: text NOT NULL\n     - file_path: text NOT NULL\n     - file_size: integer\n     - file_type: text\n     - width: integer\n     - height: integer\n     - is_current: boolean DEFAULT false\n     - storage_bucket: text DEFAULT 'avatars'::text\n     - storage_path: text\n     - public_url: text\n     - upload_status: text DEFAULT 'pending'::text\n     - uploaded_at: timestamp with time zone DEFAULT now()\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_60584_1_not_null (multiple columns)\n     - CHECK: 2200_60584_3_not_null (multiple columns)\n     - CHECK: 2200_60584_4_not_null (multiple columns)\n     - CHECK: user_avatars_upload_status_check (multiple columns)\n     - FOREIGN KEY: user_avatars.user_id -> .\n     - PRIMARY KEY: user_avatars_pkey (id)\n\n🗂️  TABLE: user_certification_progress (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - track_id: uuid\n     - progress_percentage: integer DEFAULT 0\n     - domains_completed: jsonb DEFAULT '{}'::jsonb\n     - study_time: integer DEFAULT 0\n     - last_studied: timestamp with time zone\n     - target_exam_date: date\n     - status: text DEFAULT 'not_started'::text\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_51420_1_not_null (multiple columns)\n     - CHECK: user_certification_progress_status_check (multiple columns)\n     - FOREIGN KEY: user_certification_progress.user_id -> .\n     - PRIMARY KEY: user_certification_progress_pkey (id)\n     - UNIQUE: user_certification_progress_user_id_track_id_key (user_id)\n     - UNIQUE: user_certification_progress_user_id_track_id_key (track_id)\n     - UNIQUE: user_certification_progress_user_id_track_id_key (track_id)\n     - UNIQUE: user_certification_progress_user_id_track_id_key (user_id)\n\n🗂️  TABLE: user_challenge_progress (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - challenge_id: text NOT NULL\n     - status: text DEFAULT 'not_started'::text\n     - progress: integer DEFAULT 0\n     - completed: boolean DEFAULT false\n     - points_earned: integer DEFAULT 0\n     - started_at: timestamp with time zone\n     - completed_at: timestamp with time zone\n     - last_attempt: timestamp with time zone DEFAULT now()\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_55976_1_not_null (multiple columns)\n     - CHECK: 2200_55976_3_not_null (multiple columns)\n     - FOREIGN KEY: user_challenge_progress.user_id -> user_profiles.id\n     - PRIMARY KEY: user_challenge_progress_pkey (id)\n     - UNIQUE: user_challenge_progress_user_id_challenge_id_key (challenge_id)\n     - UNIQUE: user_challenge_progress_user_id_challenge_id_key (challenge_id)\n     - UNIQUE: user_challenge_progress_user_id_challenge_id_key (user_id)\n     - UNIQUE: user_challenge_progress_user_id_challenge_id_key (user_id)\n\n🗂️  TABLE: user_coins (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - coins: integer DEFAULT 100\n     - total_earned: integer DEFAULT 100\n     - total_spent: integer DEFAULT 0\n     - last_updated: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49663_1_not_null (multiple columns)\n     - FOREIGN KEY: user_coins.user_id -> .\n     - PRIMARY KEY: user_coins_pkey (id)\n     - UNIQUE: user_coins_user_id_key (user_id)\n\n🗂️  TABLE: user_cookies (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - session_id: uuid\n     - cookie_name: text NOT NULL\n     - cookie_value: text NOT NULL\n     - cookie_domain: text\n     - cookie_path: text DEFAULT '/'::text\n     - is_secure: boolean DEFAULT true\n     - is_http_only: boolean DEFAULT true\n     - same_site: text DEFAULT 'strict'::text\n     - expires_at: timestamp with time zone\n     - is_persistent: boolean DEFAULT false\n     - is_essential: boolean DEFAULT true\n     - category: text DEFAULT 'essential'::text\n     - purpose: text\n     - consent_given: boolean DEFAULT false\n     - consent_date: timestamp with time zone\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_60834_1_not_null (multiple columns)\n     - CHECK: 2200_60834_4_not_null (multiple columns)\n     - CHECK: 2200_60834_5_not_null (multiple columns)\n     - CHECK: valid_category (multiple columns)\n     - CHECK: valid_same_site (multiple columns)\n     - FOREIGN KEY: user_cookies.session_id -> user_sessions.id\n     - FOREIGN KEY: user_cookies.user_id -> .\n     - PRIMARY KEY: user_cookies_pkey (id)\n\n🗂️  TABLE: user_daily_challenges (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - challenge_id: text NOT NULL\n     - challenge_date: date DEFAULT CURRENT_DATE\n     - completed: boolean DEFAULT false\n     - points_earned: integer DEFAULT 0\n     - completed_at: timestamp with time zone\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56059_1_not_null (multiple columns)\n     - CHECK: 2200_56059_3_not_null (multiple columns)\n     - FOREIGN KEY: user_daily_challenges.user_id -> user_profiles.id\n     - PRIMARY KEY: user_daily_challenges_pkey (id)\n     - UNIQUE: user_daily_challenges_user_id_challenge_id_challenge_date_key (challenge_id)\n     - UNIQUE: user_daily_challenges_user_id_challenge_id_challenge_date_key (challenge_id)\n     - UNIQUE: user_daily_challenges_user_id_challenge_id_challenge_date_key (challenge_id)\n     - UNIQUE: user_daily_challenges_user_id_challenge_id_challenge_date_key (challenge_date)\n     - UNIQUE: user_daily_challenges_user_id_challenge_id_challenge_date_key (challenge_date)\n     - UNIQUE: user_daily_challenges_user_id_challenge_id_challenge_date_key (challenge_date)\n     - UNIQUE: user_daily_challenges_user_id_challenge_id_challenge_date_key (user_id)\n     - UNIQUE: user_daily_challenges_user_id_challenge_id_challenge_date_key (user_id)\n     - UNIQUE: user_daily_challenges_user_id_challenge_id_challenge_date_key (user_id)\n\n🗂️  TABLE: user_events (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - event_type: text NOT NULL\n     - event_data: jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56109_1_not_null (multiple columns)\n     - CHECK: 2200_56109_3_not_null (multiple columns)\n     - FOREIGN KEY: user_events.user_id -> user_profiles.id\n     - PRIMARY KEY: user_events_pkey (id)\n\n🗂️  TABLE: user_learning_preferences (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - preferred_difficulty: text DEFAULT 'beginner'::text\n     - preferred_topics: ARRAY DEFAULT '{}'::text[]\n     - learning_pace: text DEFAULT 'normal'::text\n     - notifications_enabled: boolean DEFAULT true\n     - dark_mode: boolean DEFAULT false\n     - language: text DEFAULT 'en'::text\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_50399_1_not_null (multiple columns)\n     - FOREIGN KEY: user_learning_preferences.user_id -> .\n     - PRIMARY KEY: user_learning_preferences_pkey (id)\n     - UNIQUE: user_learning_preferences_user_id_key (user_id)\n\n🗂️  TABLE: user_learning_statistics (BASE TABLE)\n   COLUMNS:\n     - user_id: uuid NOT NULL\n     - total_paths_enrolled: integer DEFAULT 0\n     - total_paths_completed: integer DEFAULT 0\n     - total_modules_completed: integer DEFAULT 0\n     - total_learning_time_minutes: integer DEFAULT 0\n     - total_learning_xp: integer DEFAULT 0\n     - current_streak_days: integer DEFAULT 0\n     - longest_streak_days: integer DEFAULT 0\n     - last_activity_date: date\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56360_1_not_null (multiple columns)\n     - FOREIGN KEY: user_learning_statistics.user_id -> .\n     - PRIMARY KEY: user_learning_statistics_pkey (user_id)\n\n🗂️  TABLE: user_module_progress (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - module_id: text NOT NULL\n     - progress: integer DEFAULT 0\n     - completed: boolean DEFAULT false\n     - completed_at: timestamp with time zone\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_55936_1_not_null (multiple columns)\n     - CHECK: 2200_55936_3_not_null (multiple columns)\n     - FOREIGN KEY: user_module_progress.user_id -> user_profiles.id\n     - PRIMARY KEY: user_module_progress_pkey (id)\n     - UNIQUE: user_module_progress_user_id_module_id_key (module_id)\n     - UNIQUE: user_module_progress_user_id_module_id_key (module_id)\n     - UNIQUE: user_module_progress_user_id_module_id_key (user_id)\n     - UNIQUE: user_module_progress_user_id_module_id_key (user_id)\n\n🗂️  TABLE: user_profiles (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL\n     - user_id: uuid\n     - display_name: text\n     - username: text\n     - full_name: text\n     - avatar_url: text\n     - bio: text\n     - email: text\n     - subscription_tier: text DEFAULT 'free'::text\n     - subscription_status: text DEFAULT 'active'::text\n     - subscription_start_date: timestamp with time zone DEFAULT now()\n     - subscription_end_date: timestamp with time zone\n     - is_admin: boolean DEFAULT false\n     - role: text DEFAULT 'user'::text\n     - total_points: integer DEFAULT 0\n     - points: integer DEFAULT 0\n     - total_coins: integer DEFAULT 100\n     - coins: integer DEFAULT 100\n     - current_streak: integer DEFAULT 0\n     - longest_streak: integer DEFAULT 0\n     - modules_completed: integer DEFAULT 0\n     - challenges_completed: integer DEFAULT 0\n     - achievements_earned: integer DEFAULT 0\n     - last_activity: timestamp with time zone DEFAULT now()\n     - learning_style: text DEFAULT 'visual'::text\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n     - total_xp: integer DEFAULT 0\n     - current_level: integer DEFAULT 1\n     - linkedin_url: text\n     - github_url: text\n     - twitter_url: text\n     - instagram_url: text\n     - facebook_url: text\n     - phone_number: text\n     - country: text\n     - website: text\n     - profile_visibility: text DEFAULT 'public'::text\n     - location: text\n     - show_email: boolean DEFAULT false\n     - show_phone: boolean DEFAULT false\n     - show_location: boolean DEFAULT true\n     - show_social_links: boolean DEFAULT true\n     - email_notifications: boolean DEFAULT true\n     - push_notifications: boolean DEFAULT true\n     - marketing_emails: boolean DEFAULT false\n     - theme_preference: text DEFAULT 'system'::text\n     - preferred_language: text DEFAULT 'en'::text\n   CONSTRAINTS:\n     - CHECK: 2200_55776_1_not_null (multiple columns)\n     - CHECK: user_profiles_profile_visibility_check (multiple columns)\n     - CHECK: user_profiles_subscription_tier_check (multiple columns)\n     - CHECK: user_profiles_theme_preference_check (multiple columns)\n     - FOREIGN KEY: user_profiles.id -> .\n     - FOREIGN KEY: user_profiles.user_id -> .\n     - PRIMARY KEY: user_profiles_pkey (id)\n     - UNIQUE: user_profiles_username_key (username)\n\n🗂️  TABLE: user_sessions (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - session_token: text NOT NULL\n     - refresh_token: text\n     - device_id: text\n     - device_name: text\n     - device_type: text\n     - browser_name: text\n     - browser_version: text\n     - operating_system: text\n     - user_agent: text\n     - ip_address: inet\n     - country: text\n     - city: text\n     - timezone: text\n     - is_active: boolean DEFAULT true\n     - is_primary: boolean DEFAULT false\n     - last_activity: timestamp with time zone DEFAULT now()\n     - expires_at: timestamp with time zone\n     - login_method: text DEFAULT 'email'::text\n     - two_factor_verified: boolean DEFAULT false\n     - suspicious_activity: boolean DEFAULT false\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_60810_1_not_null (multiple columns)\n     - CHECK: 2200_60810_3_not_null (multiple columns)\n     - CHECK: valid_device_type (multiple columns)\n     - FOREIGN KEY: user_sessions.user_id -> .\n     - PRIMARY KEY: user_sessions_pkey (id)\n     - UNIQUE: user_sessions_session_token_key (session_token)\n\n🗂️  TABLE: user_settings (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - email_notifications: jsonb DEFAULT '{\"updates\": true, \"marketing\": false, \"challenges\": true, \"achievements\": true}'::jsonb\n     - push_notifications: jsonb DEFAULT '{\"reminders\": true, \"challenges\": true, \"achievements\": true}'::jsonb\n     - privacy_settings: jsonb DEFAULT '{\"show_progress\": true, \"show_achievements\": true, \"profile_visibility\": \"public\"}'::jsonb\n     - learning_preferences: jsonb DEFAULT '{\"pace\": \"normal\", \"topics\": [], \"difficulty\": \"intermediate\"}'::jsonb\n     - ui_preferences: jsonb DEFAULT '{\"theme\": \"system\", \"language\": \"en\", \"sidebar_collapsed\": false}'::jsonb\n     - security_settings: jsonb DEFAULT '{\"two_factor\": false, \"login_alerts\": true, \"session_timeout\": 30}'::jsonb\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_60561_1_not_null (multiple columns)\n     - FOREIGN KEY: user_settings.user_id -> .\n     - PRIMARY KEY: user_settings_pkey (id)\n     - UNIQUE: user_settings_user_id_key (user_id)\n\n🗂️  TABLE: user_skills (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - category: text NOT NULL\n     - skill_level: integer DEFAULT 0\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56123_1_not_null (multiple columns)\n     - CHECK: 2200_56123_3_not_null (multiple columns)\n     - FOREIGN KEY: user_skills.user_id -> user_profiles.id\n     - PRIMARY KEY: user_skills_pkey (id)\n     - UNIQUE: user_skills_user_id_category_key (category)\n     - UNIQUE: user_skills_user_id_category_key (category)\n     - UNIQUE: user_skills_user_id_category_key (user_id)\n     - UNIQUE: user_skills_user_id_category_key (user_id)\n\n🗂️  TABLE: user_streaks (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - streak_type: text NOT NULL\n     - current_count: integer DEFAULT 0\n     - longest_count: integer DEFAULT 0\n     - last_activity_date: date DEFAULT CURRENT_DATE\n     - started_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_49418_1_not_null (multiple columns)\n     - CHECK: 2200_49418_3_not_null (multiple columns)\n     - CHECK: user_streaks_streak_type_check (multiple columns)\n     - FOREIGN KEY: user_streaks.user_id -> .\n     - PRIMARY KEY: user_streaks_pkey (id)\n     - UNIQUE: user_streaks_user_id_streak_type_key (streak_type)\n     - UNIQUE: user_streaks_user_id_streak_type_key (streak_type)\n     - UNIQUE: user_streaks_user_id_streak_type_key (user_id)\n     - UNIQUE: user_streaks_user_id_streak_type_key (user_id)\n\n🗂️  TABLE: user_subscriptions (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT gen_random_uuid()\n     - user_id: uuid\n     - plan_id: uuid\n     - status: text DEFAULT 'active'::text\n     - start_date: timestamp with time zone DEFAULT now()\n     - end_date: timestamp with time zone\n     - payment_id: text\n     - created_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56194_1_not_null (multiple columns)\n     - FOREIGN KEY: user_subscriptions.plan_id -> subscription_plans.id\n     - FOREIGN KEY: user_subscriptions.user_id -> user_profiles.id\n     - PRIMARY KEY: user_subscriptions_pkey (id)\n     - UNIQUE: user_subscriptions_user_id_key (user_id)\n\n🗂️  TABLE: username_change_requests (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL DEFAULT uuid_generate_v4()\n     - user_id: uuid\n     - current_username: text\n     - new_username: text NOT NULL\n     - verification_token: text NOT NULL\n     - status: text DEFAULT 'pending'::text\n     - approved_at: timestamp with time zone\n     - approved_by: uuid\n     - rejection_reason: text\n     - expires_at: timestamp with time zone DEFAULT (now() + '7 days'::interval)\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_60909_1_not_null (multiple columns)\n     - CHECK: 2200_60909_4_not_null (multiple columns)\n     - CHECK: 2200_60909_5_not_null (multiple columns)\n     - CHECK: valid_username_status (multiple columns)\n     - CHECK: valid_usernames (multiple columns)\n     - CHECK: valid_usernames (multiple columns)\n     - FOREIGN KEY: username_change_requests.approved_by -> .\n     - FOREIGN KEY: username_change_requests.user_id -> .\n     - PRIMARY KEY: username_change_requests_pkey (id)\n     - UNIQUE: username_change_requests_verification_token_key (verification_token)\n\n🗂️  TABLE: users (BASE TABLE)\n   COLUMNS:\n     - id: uuid NOT NULL\n     - username: text NOT NULL\n     - full_name: text\n     - avatar_url: text\n     - email: text NOT NULL\n     - subscription_tier: text DEFAULT 'free'::text\n     - subscription_start_date: timestamp with time zone\n     - subscription_end_date: timestamp with time zone\n     - coins: integer DEFAULT 0\n     - created_at: timestamp with time zone DEFAULT now()\n     - updated_at: timestamp with time zone DEFAULT now()\n   CONSTRAINTS:\n     - CHECK: 2200_56732_1_not_null (multiple columns)\n     - CHECK: 2200_56732_2_not_null (multiple columns)\n     - CHECK: 2200_56732_5_not_null (multiple columns)\n     - CHECK: users_subscription_tier_check (multiple columns)\n     - FOREIGN KEY: users.id -> .\n     - PRIMARY KEY: users_pkey (id)\n     - UNIQUE: users_email_key (email)\n     - UNIQUE: users_username_key (username)\n\n=== APPLICATION REQUIREMENTS CHECK ===\n\n👤 PROFILE SYSTEM:\n  ✅ profiles (VIEW)\n  ✅ user_profiles\n  ❌ profile_completion_percentage column MISSING\n\n📚 LEARNING SYSTEM:\n  ✅ learning_paths\n  ✅ learning_modules\n  ✅ learning_path_enrollments\n\n🎯 CHALLENGE SYSTEM:\n  ✅ challenges\n  ✅ challenge_attempts\n\n⚙️ SYSTEM TABLES:\n  ✅ user_events\n  ✅ notifications\n\n=== END OF REPORT ===\n"}]