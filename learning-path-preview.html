<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberForce Learning Paths - Content Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .learning-paths {
            display: grid;
            gap: 30px;
        }

        .learning-path {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .learning-path:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }

        .path-header {
            border-bottom: 3px solid #88cc14;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .path-title {
            font-size: 2.5rem;
            color: #4A5CBA;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .path-icon {
            font-size: 2rem;
        }

        .path-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
        }

        .path-meta {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .meta-item {
            background: #f8f9fa;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #555;
            border: 2px solid #e9ecef;
        }

        .difficulty-beginner { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .difficulty-intermediate { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .difficulty-advanced { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .module-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .module-card:hover {
            border-color: #88cc14;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(136, 204, 20, 0.2);
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #88cc14, #4A5CBA);
        }

        .module-number {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #4A5CBA;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .module-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
            padding-right: 40px;
        }

        .module-description {
            color: #666;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .module-topics {
            margin-top: 15px;
        }

        .module-topics h4 {
            font-size: 0.9rem;
            color: #4A5CBA;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .topics-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .topic-tag {
            background: #88cc14;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .stats-bar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-around;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4A5CBA;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: white;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .path-title {
                font-size: 1.8rem;
            }
            
            .modules-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-bar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ CyberForce Learning Paths</h1>
            <p class="subtitle">Comprehensive Cybersecurity Education Platform</p>
        </div>

        <div id="loading" class="loading">
            <h2>🔄 Loading Learning Paths...</h2>
            <p>Fetching content from source files...</p>
        </div>

        <div id="content" style="display: none;">
            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-number" id="total-paths">0</div>
                    <div class="stat-label">Learning Paths</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-modules">0</div>
                    <div class="stat-label">Total Modules</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-hours">0</div>
                    <div class="stat-label">Study Hours</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-skills">0</div>
                    <div class="stat-label">Skills Covered</div>
                </div>
            </div>

            <div class="learning-paths" id="learning-paths">
                <!-- Learning paths will be populated here -->
            </div>
        </div>

        <div id="error" class="error" style="display: none;">
            <h3>❌ Error Loading Content</h3>
            <p>Unable to load learning path data. Please check the console for details.</p>
        </div>
    </div>

    <script>
        // Learning Paths Data (extracted from your source files)
        const learningPathsData = [
            {
                id: 'networking-fundamentals',
                title: 'Networking Fundamentals',
                icon: '🌐',
                description: 'Master the essential concepts of computer networking that form the foundation of cybersecurity. Learn about network protocols, architecture, and security principles.',
                category: 'fundamentals',
                difficulty: 'beginner',
                estimatedHours: 60,
                modules: [
                    {
                        id: 'nf-1',
                        title: 'Introduction to Computer Networks',
                        description: 'Learn the fundamental concepts of computer networks and their importance in modern computing and cybersecurity.',
                        topics: ['What is a Computer Network?', 'Network Topologies', 'Network Types', 'Security Implications']
                    },
                    {
                        id: 'nf-2',
                        title: 'The OSI Model',
                        description: 'Understand the seven layers of the OSI model and how they provide a framework for network communication.',
                        topics: ['OSI Model Layers', 'Data Encapsulation', 'Protocol Data Units', 'Layer Interactions']
                    },
                    {
                        id: 'nf-3',
                        title: 'TCP/IP Protocol Suite',
                        description: 'Explore the TCP/IP protocol stack, the foundation of internet communications.',
                        topics: ['TCP/IP Protocol Stack', 'IP Addressing', 'TCP vs UDP', 'Protocol Analysis']
                    },
                    {
                        id: 'nf-4',
                        title: 'IP Addressing and Subnetting',
                        description: 'Master IP addressing concepts, including IPv4, IPv6, and subnetting techniques.',
                        topics: ['IPv4 Addressing', 'Subnet Masks', 'CIDR Notation', 'IPv6 Addressing']
                    },
                    {
                        id: 'nf-5',
                        title: 'Network Devices and Infrastructure',
                        description: 'Learn about the hardware components that make up computer networks.',
                        topics: ['Network Interface Cards', 'Hubs, Bridges, and Switches', 'Routers', 'Wireless Access Points', 'Firewalls', 'Network Cabling']
                    },
                    {
                        id: 'nf-6',
                        title: 'IPv6 Fundamentals',
                        description: 'Understand the next generation of IP addressing and its implementation.',
                        topics: ['IPv6 Address Structure', 'IPv6 Transition Mechanisms', 'IPv6 Security Considerations']
                    },
                    {
                        id: 'nf-7',
                        title: 'Routing Fundamentals',
                        description: 'Learn how data is routed across networks and the internet.',
                        topics: ['Routing Concepts', 'Static vs Dynamic Routing', 'Routing Protocols']
                    },
                    {
                        id: 'nf-8',
                        title: 'DNS Fundamentals',
                        description: 'Understand the Domain Name System and its critical role in network operations.',
                        topics: ['DNS Resolution Process', 'DNS Records', 'DNS Security']
                    },
                    {
                        id: 'nf-9',
                        title: 'DHCP Fundamentals',
                        description: 'Learn how the Dynamic Host Configuration Protocol automates IP address assignment.',
                        topics: ['DHCP Process', 'DHCP Options', 'DHCP Security']
                    },
                    {
                        id: 'nf-10',
                        title: 'Switching Concepts',
                        description: 'Explore how switches operate and their role in local area networks.',
                        topics: ['Switch Operation', 'VLANs', 'Spanning Tree Protocol']
                    },
                    {
                        id: 'nf-11',
                        title: 'VLANs and Trunking',
                        description: 'Learn how to segment networks using Virtual LANs and trunking protocols.',
                        topics: ['VLAN Configuration', 'Trunking Protocols', 'Inter-VLAN Routing']
                    },
                    {
                        id: 'nf-12',
                        title: 'Network Address Translation',
                        description: 'Understand how NAT extends IPv4 addressing and provides a layer of security.',
                        topics: ['NAT Types', 'Port Forwarding', 'NAT Traversal']
                    },
                    {
                        id: 'nf-13',
                        title: 'Wireless Networking',
                        description: 'Learn about wireless network technologies, standards, and security.',
                        topics: ['Wi-Fi Standards', 'Wireless Security', 'Wireless Troubleshooting']
                    },
                    {
                        id: 'nf-14',
                        title: 'Network Security Fundamentals',
                        description: 'Explore the basic principles of securing computer networks.',
                        topics: ['Network Threats', 'Defense in Depth', 'Security Controls']
                    },
                    {
                        id: 'nf-15',
                        title: 'Firewalls and Access Control Lists',
                        description: 'Learn how firewalls and ACLs protect networks by controlling traffic flow.',
                        topics: ['Firewall Types', 'ACL Configuration', 'Next-Gen Firewalls']
                    },
                    {
                        id: 'nf-16',
                        title: 'Virtual Private Networks',
                        description: 'Understand how VPNs provide secure remote access and site-to-site connectivity.',
                        topics: ['VPN Technologies', 'VPN Protocols', 'VPN Implementation']
                    },
                    {
                        id: 'nf-17',
                        title: 'Network Monitoring and Management',
                        description: 'Learn tools and techniques for monitoring and managing network performance and security.',
                        topics: ['Monitoring Tools', 'SNMP', 'NetFlow', 'Syslog']
                    },
                    {
                        id: 'nf-18',
                        title: 'Network Troubleshooting',
                        description: 'Master the methodologies and tools for diagnosing and resolving network issues.',
                        topics: ['Troubleshooting Methodology', 'Diagnostic Tools', 'Common Issues']
                    },
                    {
                        id: 'nf-19',
                        title: 'Packet Analysis with Wireshark',
                        description: 'Learn to capture and analyze network traffic for troubleshooting and security analysis.',
                        topics: ['Packet Capture', 'Protocol Analysis', 'Security Analysis']
                    },
                    {
                        id: 'nf-20',
                        title: 'Network Protocols and Services',
                        description: 'Explore common network protocols and services and their security implications.',
                        topics: ['HTTP/HTTPS', 'FTP/SFTP', 'SSH', 'Telnet', 'SMTP/POP3/IMAP']
                    },
                    {
                        id: 'nf-21',
                        title: 'Network Performance and Optimization',
                        description: 'Learn techniques for optimizing network performance and reliability.',
                        topics: ['Bandwidth Management', 'QoS', 'Caching', 'Load Balancing']
                    },
                    {
                        id: 'nf-22',
                        title: 'Cloud Networking',
                        description: 'Understand networking concepts in cloud environments.',
                        topics: ['Cloud Network Architecture', 'Virtual Networks', 'Cloud Security']
                    },
                    {
                        id: 'nf-23',
                        title: 'Software-Defined Networking',
                        description: 'Explore the concepts and benefits of software-defined networking.',
                        topics: ['SDN Architecture', 'SDN Controllers', 'SDN Security']
                    },
                    {
                        id: 'nf-24',
                        title: 'Network Virtualization',
                        description: 'Learn about network virtualization technologies and their implementation.',
                        topics: ['Virtual Networks', 'Network Function Virtualization', 'Containers']
                    },
                    {
                        id: 'nf-25',
                        title: 'Network Security Assessment',
                        description: 'Learn methodologies and tools for assessing network security.',
                        topics: ['Vulnerability Assessment', 'Penetration Testing', 'Security Auditing']
                    }
                ]
            }
        ];

            },
            {
                id: 'operating-systems',
                title: 'Operating Systems for Cybersecurity',
                icon: '💻',
                description: 'Master the essential concepts of Linux and Windows operating systems from a cybersecurity perspective.',
                category: 'fundamentals',
                difficulty: 'beginner',
                estimatedHours: 50,
                modules: [
                    {
                        id: 'os-1',
                        title: 'Introduction to Operating Systems',
                        description: 'Understand the fundamental concepts of operating systems and their role in cybersecurity.',
                        topics: ['OS Architecture', 'Kernel vs User Space', 'System Calls', 'Security Models']
                    },
                    {
                        id: 'os-2',
                        title: 'Linux Fundamentals',
                        description: 'Learn the basics of Linux operating system and command line interface.',
                        topics: ['Linux Distribution', 'File System', 'Command Line', 'Package Management']
                    },
                    {
                        id: 'os-3',
                        title: 'Windows Fundamentals',
                        description: 'Explore Windows operating system architecture and security features.',
                        topics: ['Windows Architecture', 'Registry', 'Services', 'Security Features']
                    },
                    {
                        id: 'os-4',
                        title: 'File Systems and Permissions',
                        description: 'Master file system concepts and permission models in different operating systems.',
                        topics: ['File System Types', 'Permissions', 'Access Control', 'File Attributes']
                    },
                    {
                        id: 'os-5',
                        title: 'Process and Memory Management',
                        description: 'Understand how operating systems manage processes and memory.',
                        topics: ['Process Lifecycle', 'Memory Management', 'Virtual Memory', 'Process Security']
                    }
                ]
            },
            {
                id: 'ethical-hacking',
                title: 'Ethical Hacking Fundamentals',
                icon: '🔓',
                description: 'Master ethical hacking and penetration testing from fundamental concepts to advanced attack techniques.',
                category: 'offensive',
                difficulty: 'intermediate',
                estimatedHours: 120,
                modules: [
                    {
                        id: 'eh-1',
                        title: 'Introduction to Ethical Hacking',
                        description: 'Learn the fundamentals of ethical hacking and penetration testing.',
                        topics: ['Ethics and Legality', 'Hacking Methodology', 'Types of Hackers', 'Penetration Testing']
                    },
                    {
                        id: 'eh-2',
                        title: 'Reconnaissance and Information Gathering',
                        description: 'Master the art of gathering information about targets.',
                        topics: ['Passive Reconnaissance', 'Active Reconnaissance', 'OSINT', 'Footprinting']
                    },
                    {
                        id: 'eh-3',
                        title: 'Scanning and Enumeration',
                        description: 'Learn to identify live systems, open ports, and running services.',
                        topics: ['Network Scanning', 'Port Scanning', 'Service Enumeration', 'Vulnerability Scanning']
                    },
                    {
                        id: 'eh-4',
                        title: 'Vulnerability Assessment',
                        description: 'Identify and assess security vulnerabilities in systems and applications.',
                        topics: ['Vulnerability Types', 'Assessment Tools', 'Risk Analysis', 'Reporting']
                    },
                    {
                        id: 'eh-5',
                        title: 'System Hacking',
                        description: 'Learn techniques for gaining unauthorized access to systems.',
                        topics: ['Password Attacks', 'Privilege Escalation', 'Maintaining Access', 'Covering Tracks']
                    }
                ]
            },
            {
                id: 'blue-teaming',
                title: 'Blue Teaming',
                icon: '🛡️',
                description: 'Master defensive security operations, incident response, and threat hunting.',
                category: 'defensive',
                difficulty: 'intermediate',
                estimatedHours: 80,
                modules: [
                    {
                        id: 'bt-1',
                        title: 'Introduction to Blue Teaming',
                        description: 'Learn the fundamentals of defensive cybersecurity operations.',
                        topics: ['Blue Team Role', 'Defense Strategies', 'Security Operations', 'Team Structure']
                    },
                    {
                        id: 'bt-2',
                        title: 'Security Monitoring and SIEM',
                        description: 'Master security monitoring techniques and SIEM technologies.',
                        topics: ['Log Analysis', 'SIEM Platforms', 'Alert Management', 'Correlation Rules']
                    },
                    {
                        id: 'bt-3',
                        title: 'Incident Response',
                        description: 'Learn how to respond to and manage security incidents.',
                        topics: ['Incident Response Process', 'Containment', 'Eradication', 'Recovery']
                    },
                    {
                        id: 'bt-4',
                        title: 'Threat Hunting',
                        description: 'Develop skills in proactively hunting for threats in your environment.',
                        topics: ['Hunting Methodologies', 'Threat Intelligence', 'IOCs', 'Behavioral Analysis']
                    },
                    {
                        id: 'bt-5',
                        title: 'Digital Forensics',
                        description: 'Learn digital forensics techniques for incident investigation.',
                        topics: ['Forensic Process', 'Evidence Collection', 'Analysis Tools', 'Reporting']
                    }
                ]
            },
            {
                id: 'red-teaming',
                title: 'Red Teaming',
                icon: '⚔️',
                description: 'Master advanced offensive security techniques and red team operations.',
                category: 'offensive',
                difficulty: 'advanced',
                estimatedHours: 100,
                modules: [
                    {
                        id: 'rt-1',
                        title: 'Introduction to Red Teaming',
                        description: 'Learn the fundamentals of red team operations and advanced persistent threats.',
                        topics: ['Red Team Methodology', 'APT Simulation', 'Operational Security', 'Team Coordination']
                    },
                    {
                        id: 'rt-2',
                        title: 'Advanced Reconnaissance',
                        description: 'Master advanced information gathering and target profiling techniques.',
                        topics: ['Deep OSINT', 'Social Engineering', 'Physical Reconnaissance', 'Target Profiling']
                    },
                    {
                        id: 'rt-3',
                        title: 'Initial Access and Persistence',
                        description: 'Learn techniques for gaining initial access and maintaining persistence.',
                        topics: ['Phishing Campaigns', 'Exploit Development', 'Persistence Mechanisms', 'Evasion Techniques']
                    },
                    {
                        id: 'rt-4',
                        title: 'Lateral Movement and Privilege Escalation',
                        description: 'Master techniques for moving through networks and escalating privileges.',
                        topics: ['Network Pivoting', 'Credential Harvesting', 'Privilege Escalation', 'Domain Dominance']
                    },
                    {
                        id: 'rt-5',
                        title: 'Data Exfiltration and Impact',
                        description: 'Learn techniques for data exfiltration and demonstrating impact.',
                        topics: ['Data Identification', 'Exfiltration Methods', 'Impact Demonstration', 'Cleanup']
                    }
                ]
            }
        ];

        // Initialize the page
        function initializePage() {
            console.log('Initializing page...');
            try {
                renderLearningPaths();
                updateStats();
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
                console.log('Page initialized successfully');
            } catch (error) {
                console.error('Error initializing page:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').innerHTML = `
                    <h3>❌ Error Loading Content</h3>
                    <p>Error: ${error.message}</p>
                    <p>Please check the browser console for more details.</p>
                `;
            }
        }

        // Update statistics
        function updateStats() {
            const totalPaths = learningPathsData.length;
            const totalModules = learningPathsData.reduce((sum, path) => sum + path.modules.length, 0);
            const totalHours = learningPathsData.reduce((sum, path) => sum + path.estimatedHours, 0);
            const allTopics = learningPathsData.flatMap(path =>
                path.modules.flatMap(module => module.topics)
            );
            const uniqueSkills = [...new Set(allTopics)].length;

            document.getElementById('total-paths').textContent = totalPaths;
            document.getElementById('total-modules').textContent = totalModules;
            document.getElementById('total-hours').textContent = totalHours;
            document.getElementById('total-skills').textContent = uniqueSkills;
        }

        // Render learning paths
        function renderLearningPaths() {
            const container = document.getElementById('learning-paths');
            container.innerHTML = '';

            learningPathsData.forEach(path => {
                const pathElement = createLearningPathElement(path);
                container.appendChild(pathElement);
            });
        }

        // Create learning path element
        function createLearningPathElement(path) {
            const pathDiv = document.createElement('div');
            pathDiv.className = 'learning-path';

            pathDiv.innerHTML = `
                <div class="path-header">
                    <h2 class="path-title">
                        <span class="path-icon">${path.icon}</span>
                        ${path.title}
                    </h2>
                    <p class="path-description">${path.description}</p>
                    <div class="path-meta">
                        <span class="meta-item difficulty-${path.difficulty}">${path.difficulty.charAt(0).toUpperCase() + path.difficulty.slice(1)}</span>
                        <span class="meta-item">${path.estimatedHours} hours</span>
                        <span class="meta-item">${path.modules.length} modules</span>
                        <span class="meta-item">${path.category}</span>
                    </div>
                </div>
                <div class="modules-grid">
                    ${path.modules.map((module, index) => createModuleCard(module, index + 1)).join('')}
                </div>
            `;

            return pathDiv;
        }

        // Create module card
        function createModuleCard(module, number) {
            return `
                <div class="module-card">
                    <div class="module-number">${number}</div>
                    <h3 class="module-title">${module.title}</h3>
                    <p class="module-description">${module.description}</p>
                    <div class="module-topics">
                        <h4>Topics Covered:</h4>
                        <div class="topics-list">
                            ${module.topics.map(topic => `<span class="topic-tag">${topic}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        // Start the application - try multiple ways to ensure it runs
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePage);
        } else {
            initializePage();
        }

        // Fallback - run after a short delay regardless
        setTimeout(initializePage, 500);
    </script>
</body>
</html>
